version: '3.8'

services:
  promax-platform:
    image: ${IMAGE_NAME}
    container_name: ${CONTAINER_NAME:-promax-platform}
    restart: unless-stopped
    
    # GPU 支持 (Docker Compose 2.3+ 支持)
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    
    ports:
      - "3100:3100"  # 主应用端口
      - "3101:3101"  # API端口
      - "8000:8000"  # Python挖掘服务端口
    
    volumes:
      # 数据持久化
      - promax_logs:/app/logs
      - promax_uploads:/app/uploads
      - promax_reports:/app/reports
      - promax_result_data:/app/result-data
      - promax_supervisor_logs:/var/log/supervisor
      # 配置文件
      - ./config:/app/config:ro
    
    env_file:
      - promax.env
    
    environment:
      - NODE_ENV=production
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - PYTHONPATH=/app/python-mining-service
      # 数据库配置
      - DB_HOST=localhost
      - DB_PORT=3306
      - DB_USERNAME=promax
      - DB_PASSWORD=promax123
      - DB_DATABASE=promax_db
      # Redis配置
      - REDIS_HOST=localhost
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      # JWT配置
      - JWT_SECRET=your-jwt-secret-key
      - JWT_EXPIRES_IN=7d
      # 文件上传配置
      - UPLOAD_MAX_SIZE=100MB
      - UPLOAD_ALLOWED_TYPES=csv,xlsx,xls
      # Python服务配置
      - PYTHON_SERVICE_URL=http://localhost:8000
      # 其他配置
      - LOG_LEVEL=info
      - CORS_ORIGIN=*
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3100/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 网络配置
    networks:
      - promax_network

networks:
  promax_network:
    driver: bridge

volumes:
  promax_logs:
    driver: local
  promax_uploads:
    driver: local
  promax_reports:
    driver: local
  promax_result_data:
    driver: local
  promax_supervisor_logs:
    driver: local