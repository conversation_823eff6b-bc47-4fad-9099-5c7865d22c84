services:
  promax-platform:
    image: ${IMAGE_NAME}
    container_name: promax-platform
    restart: unless-stopped
    
    # GPU 支持 (Docker Compose 2.3+ 支持)
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    
    ports:
      - "3100:3100"  # 主应用端口
      - "3101:3101"  # API端口
      - "8000:8000"  # Python挖掘服务端口
    
    volumes:
      # 数据持久化
      - promax_logs:/app/logs
      - promax_uploads:/app/uploads
      - promax_reports:/app/reports
      - promax_result_data:/app/result-data
      - promax_supervisor_logs:/var/log/supervisor
      # 配置文件
      - ./config:/app/config:ro
    
    environment:
      SERVER_HOST: *************
      SERVER_USER: bpmax4090
      DEPLOY_SERVER_IP: *************
      DEPLOY_SERVER_HOST: bpmax4090@*************
      CONTAINER_NAME: promax-platform
      IMAGE_NAME: promax-platform
      IMAGE_TAG: latest
      REMOTE_DIR: /home/<USER>/promax-deployment
      REMOTE_DEPLOY_DIR: /home/<USER>/promax-deployment
      GIT_REPO_URL: http://gitlabbot:<EMAIL>/bpmax/promax.git
      GIT_CREDENTIALS_ID: f75a9928-9b05-4dd7-8fdf-57d80e312ddf
      REGISTRY_URL: registry.yitaiyitai.com
      NODE_ENV: production
      PYTHONPATH: /app/python-mining-service
      HOST: 0.0.0.0
      DEBUG: false
      NVIDIA_VISIBLE_DEVICES: all
      NVIDIA_DRIVER_CAPABILITIES: compute,utility
      DB_HOST: **************
      DB_PORT: 33068
      DB_USERNAME: root
      DB_PASSWORD: "J#fe9Yu{"
      DB_DATABASE: promax_db
      DB_TYPE: mysql
      MYSQL_HOST: **************
      MYSQL_PORT: 33068
      MYSQL_USER: root
      MYSQL_PASSWORD: "J#fe9Yu{"
      MYSQL_DATABASE: promax_db
      REDIS_HOST: **************
      REDIS_PORT: 63790
      REDIS_PASSWORD: ""
      REDIS_DB: 0
      REDIS_KEY_PREFIX: "promax_yitaiyitai_com:"
      REDIS_DEFAULT_EXPIRE: 600
      JWT_SECRET: promax-production-jwt-secret-2024
      JWT_EXPIRES_IN: 30d
      NESTJS_INTERNAL_PORT: 3003
      NESTJS_EXTERNAL_PORT: 3101
      API_PREFIX: api
      API_VERSION: v1
      CORS_ORIGIN: "http://*************,http://localhost:3000,http://localhost:3001"
      CORS_CREDENTIALS: true
      UPLOAD_MAX_SIZE: 100MB
      UPLOAD_ALLOWED_TYPES: "csv,xlsx,xls,json"
      UPLOAD_PATH: /app/uploads
      PYTHON_MINING_SERVICE_URL: http://localhost:8000
      PYTHON_SERVICE_INTERNAL_PORT: 8000
      PYTHON_SERVICE_PORT: 8000
      PYTHON_SERVICE_EXTERNAL_PORT: 3102
      MAX_WORKERS: 4
      ENABLE_GPU_ACCELERATION: true
      MAX_MEMORY_USAGE_PERCENT: 80.0
      GPU_MEMORY_FRACTION: 0.8
      BATCH_SIZE: 1000
      MIN_FREQUENCY: 2
      MIN_CONFIDENCE: 0.1
      MAX_PATTERN_LENGTH: 5
      LOG_LEVEL: INFO
      LOG_FILE: logs/mining_service.log
      ALLOWED_ORIGINS: "http://*************,http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000,http://127.0.0.1:3001"
      NUXT_HOST: 0.0.0.0
      NUXT_PORT: 3000
      NUXT_INTERNAL_PORT: 3000
      NUXT_PUBLIC_API_BASE: /
      NGINX_PORT: 3100
      DATA_ROOT: ~/promax-data
      LOGS_DIR: ~/promax-data/logs
      UPLOADS_DIR: ~/promax-data/uploads
      REPORTS_DIR: ~/promax-data/reports
      RESULT_DATA_DIR: ~/promax-data/result-data
      HEALTH_CHECK_TIMEOUT: 30
      STARTUP_WAIT_TIME: 20
      SERVICE_CHECK_INTERVAL: 10
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3100/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 网络配置
    networks:
      - promax_network

networks:
  promax_network:
    driver: bridge

volumes:
  promax_logs:
    driver: local
  promax_uploads:
    driver: local
  promax_reports:
    driver: local
  promax_result_data:
    driver: local
  promax_supervisor_logs:
    driver: local