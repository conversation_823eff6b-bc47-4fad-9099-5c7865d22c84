# ProMax 流程挖掘平台 Docker Compose 模板
# 参考jenkins模板的变量替换方式
version: '3.8'

services:
  promax-platform:
    image: ${IMAGE}
    container_name: promax-platform
    restart: unless-stopped
    
    # GPU 支持
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    
    ports:
      # 使用变量替换，与jenkins模板保持一致
      - "${PORT}:80"        # Nginx主入口
      - "${NESTJS_PORT}:3003"   # NestJS API
      - "${PYTHON_PORT}:8000"   # Python挖掘服务
    
    volumes:
      # 数据持久化 - 使用主机路径挂载
      - ${ROOTPATH}/logs:/app/logs
      - ${ROOTPATH}/uploads:/app/uploads
      - ${ROOTPATH}/reports:/app/reports
      - ${ROOTPATH}/result-data:/app/result-data
      - ${ROOTPATH}/supervisor-logs:/var/log/supervisor
      # 配置文件挂载
      - ${ROOTPATH}/config:/app/config:ro
    
    # 使用项目的环境配置文件
    env_file:
      - ${ROOTPATH}/config/production.env
    
    environment:
      # 容器运行时环境变量
      - NODE_ENV=production
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - PYTHONPATH=/app/python-mining-service
      # 构建时注入的变量
      - BUILD_NUMBER=${BUILD_NUMBER}
      - BUILD_TIMESTAMP=${BUILD_TIMESTAMP}
      - COMMIT_HASH=${COMMIT_HASH}
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 网络配置
    networks:
      - promax_network

networks:
  promax_network:
    driver: bridge
