services:
  promax-platform:
    image: ${IMAGE}
    container_name: promax-platform
    restart: unless-stopped
    
    # GPU 支持 (Docker Compose 2.3+ 支持)
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    
    ports:
      - "${PORT1}:3100"  # 主应用端口
      - "${PORT2}:3101"  # API端口
      - "${PORT3}:8000"  # Python挖掘服务端口
    
    volumes:
      # 数据持久化
      - promax_logs:/app/logs
      - promax_uploads:/app/uploads
      - promax_reports:/app/reports
      - promax_result_data:/app/result-data
      - promax_supervisor_logs:/var/log/supervisor
      # 配置文件
      - ./config:/app/config:ro
    
    environment:
      SERVER_HOST: ${SERVER_HOST}
      SERVER_USER: ${SERVER_USER}
      DEPLOY_SERVER_IP: ${DEPLOY_SERVER_IP}
      DEPLOY_SERVER_HOST: ${DEPLOY_SERVER_HOST}
      CONTAINER_NAME: promax-platform
      IMAGE_NAME: promax-platform
      IMAGE_TAG: latest
      REMOTE_DIR: ${REMOTE_DEPLOY_DIR}
      REMOTE_DEPLOY_DIR: ${REMOTE_DEPLOY_DIR}
      GIT_REPO_URL: ${GIT_REPO_URL}
      GIT_CREDENTIALS_ID: ${GIT_CREDENTIALS_ID}
      REGISTRY_URL: ${REGISTRY_URL}
      NODE_ENV: production
      PYTHONPATH: /app/python-mining-service
      HOST: 0.0.0.0
      DEBUG: false
      NVIDIA_VISIBLE_DEVICES: all
      NVIDIA_DRIVER_CAPABILITIES: compute,utility
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT}
      DB_USERNAME: ${DB_USERNAME}
      DB_PASSWORD: "${DB_PASSWORD}"
      DB_DATABASE: ${DB_DATABASE}
      DB_TYPE: mysql
      MYSQL_HOST: ${MYSQL_HOST}
      MYSQL_PORT: ${MYSQL_PORT}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: "${MYSQL_PASSWORD}"
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      REDIS_HOST: ${REDIS_HOST}
      REDIS_PORT: ${REDIS_PORT}
      REDIS_PASSWORD: "${REDIS_PASSWORD}"
      REDIS_DB: ${REDIS_DB}
      REDIS_KEY_PREFIX: "${REDIS_KEY_PREFIX}"
      REDIS_DEFAULT_EXPIRE: ${REDIS_DEFAULT_EXPIRE}
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN}
      NESTJS_INTERNAL_PORT: 3003
      NESTJS_EXTERNAL_PORT: ${PORT2}
      API_PREFIX: api
      API_VERSION: v1
      CORS_ORIGIN: "${CORS_ORIGIN}"
      CORS_CREDENTIALS: true
      UPLOAD_MAX_SIZE: 100MB
      UPLOAD_ALLOWED_TYPES: "csv,xlsx,xls,json"
      UPLOAD_PATH: /app/uploads
      PYTHON_MINING_SERVICE_URL: http://localhost:8000
      PYTHON_SERVICE_INTERNAL_PORT: 8000
      PYTHON_SERVICE_PORT: 8000
      PYTHON_SERVICE_EXTERNAL_PORT: ${PORT3}
      MAX_WORKERS: 4
      ENABLE_GPU_ACCELERATION: true
      MAX_MEMORY_USAGE_PERCENT: 80.0
      GPU_MEMORY_FRACTION: 0.8
      BATCH_SIZE: 1000
      MIN_FREQUENCY: 2
      MIN_CONFIDENCE: 0.1
      MAX_PATTERN_LENGTH: 5
      LOG_LEVEL: INFO
      LOG_FILE: logs/mining_service.log
      ALLOWED_ORIGINS: "${ALLOWED_ORIGINS}"
      NUXT_HOST: 0.0.0.0
      NUXT_PORT: 3000
      NUXT_INTERNAL_PORT: 3000
      NUXT_PUBLIC_API_BASE: /
      NGINX_PORT: ${PORT1}
      DATA_ROOT: ~/promax-data
      LOGS_DIR: ~/promax-data/logs
      UPLOADS_DIR: ~/promax-data/uploads
      REPORTS_DIR: ~/promax-data/reports
      RESULT_DATA_DIR: ~/promax-data/result-data
      HEALTH_CHECK_TIMEOUT: 30
      STARTUP_WAIT_TIME: 20
      SERVICE_CHECK_INTERVAL: 10
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3100/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 网络配置
    networks:
      - promax_network

networks:
  promax_network:
    driver: bridge

volumes:
  promax_logs:
    driver: local
  promax_uploads:
    driver: local
  promax_reports:
    driver: local
  promax_result_data:
    driver: local
  promax_supervisor_logs:
    driver: local