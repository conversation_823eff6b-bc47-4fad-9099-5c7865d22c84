#!/usr/bin/env ts-node

/**
 * 子流程发现功能演示脚本
 * 
 * 该脚本演示如何使用子流程发现功能分析制造业质量管理流程
 * 包括数据上传、子流程发现、结果分析等完整流程
 */

import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';

// 配置
const API_BASE_URL = 'http://localhost:3000/api/v1';
const TEST_DATA_PATH = path.join(__dirname, '../test-data/manufacturing_quality_management_process.csv');

// 演示用户凭据
const DEMO_USER = {
  username: 'demo_user',
  email: '<EMAIL>',
  password: 'demo123456'
};

class SubprocessDiscoveryDemo {
  private authToken: string = '';
  private processId: number = 0;

  async run() {
    console.log('🚀 ProMined 子流程发现功能演示');
    console.log('=====================================\n');

    try {
      // 1. 用户认证
      await this.authenticate();
      
      // 2. 上传测试数据
      await this.uploadTestData();
      
      // 3. 基础流程发现
      await this.basicProcessDiscovery();
      
      // 4. 子流程发现
      await this.subprocessDiscovery();
      
      // 5. 层次化DFG生成
      await this.hierarchicalDFGGeneration();
      
      // 6. 结果分析
      await this.analyzeResults();
      
      console.log('\n✅ 演示完成！');
      console.log('您可以访问前端界面查看可视化结果：');
      console.log(`http://localhost:3001/analysis/${this.processId}/subprocess`);
      
    } catch (error) {
      console.error('❌ 演示过程中发生错误:', error.message);
      process.exit(1);
    }
  }

  private async authenticate() {
    console.log('1. 用户认证...');
    
    try {
      // 尝试注册用户（如果已存在会失败，但不影响后续登录）
      await axios.post(`${API_BASE_URL}/auth/register`, DEMO_USER);
      console.log('   ✓ 用户注册成功');
    } catch (error) {
      console.log('   ℹ 用户可能已存在，继续登录...');
    }

    try {
      const response = await axios.post(`${API_BASE_URL}/auth/login`, {
        username: DEMO_USER.username,
        password: DEMO_USER.password
      });
      
      this.authToken = response.data.access_token;
      console.log('   ✓ 登录成功\n');
    } catch (error) {
      throw new Error(`登录失败: ${error.response?.data?.message || error.message}`);
    }
  }

  private async uploadTestData() {
    console.log('2. 上传制造业质量管理流程数据...');
    
    if (!fs.existsSync(TEST_DATA_PATH)) {
      throw new Error(`测试数据文件不存在: ${TEST_DATA_PATH}`);
    }

    const FormData = require('form-data');
    const form = new FormData();
    form.append('file', fs.createReadStream(TEST_DATA_PATH));
    form.append('processName', '制造业质量管理流程');
    form.append('description', '包含采购、生产、检验、发货等完整质量管理流程');
    form.append('caseIdColumn', 'case_id');
    form.append('activityColumn', 'activity');
    form.append('timestampColumn', 'timestamp');
    form.append('resourceColumn', 'resource');

    try {
      const response = await axios.post(`${API_BASE_URL}/analysis/upload`, form, {
        headers: {
          ...form.getHeaders(),
          'Authorization': `Bearer ${this.authToken}`
        }
      });

      if (response.data.success) {
        console.log(`   ✓ 数据上传成功，保存了 ${response.data.savedRecords} 条记录`);
        
        // 获取流程ID（这里简化处理，实际应该从上传响应中获取）
        const processesResponse = await axios.get(`${API_BASE_URL}/processes`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });
        
        this.processId = processesResponse.data[0]?.id;
        if (!this.processId) {
          throw new Error('无法获取流程ID');
        }
        
        console.log(`   ✓ 流程ID: ${this.processId}\n`);
      } else {
        throw new Error('数据上传失败');
      }
    } catch (error) {
      throw new Error(`数据上传失败: ${error.response?.data?.message || error.message}`);
    }
  }

  private async basicProcessDiscovery() {
    console.log('3. 基础流程发现...');
    
    try {
      const response = await axios.post(
        `${API_BASE_URL}/analysis/discover/${this.processId}`,
        {},
        {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        }
      );

      const result = response.data;
      console.log(`   ✓ 发现了 ${result.nodes?.length || 0} 个活动节点`);
      console.log(`   ✓ 发现了 ${result.edges?.length || 0} 个连接关系`);
      console.log(`   ✓ 总案例数: ${result.statistics?.totalCases || 0}`);
      console.log(`   ✓ 平均案例持续时间: ${this.formatDuration(result.statistics?.avgCaseDuration)}\n`);
    } catch (error) {
      throw new Error(`基础流程发现失败: ${error.response?.data?.message || error.message}`);
    }
  }

  private async subprocessDiscovery() {
    console.log('4. 子流程自动发现...');
    
    const options = {
      minFrequency: 2,
      minLength: 2,
      maxLength: 8,
      confidenceThreshold: 0.6,
      enableParallelDetection: true,
      enableLoopDetection: true,
      groupByDepartment: false,
      groupByResource: false
    };

    try {
      const response = await axios.post(
        `${API_BASE_URL}/analysis/subprocess-discovery/${this.processId}`,
        options,
        {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        }
      );

      const result = response.data;
      console.log(`   ✓ 发现了 ${result.subprocesses?.length || 0} 个子流程模式`);
      console.log(`   ✓ 压缩率: ${Math.round((result.statistics?.compressionRatio || 0) * 100)}%`);
      console.log(`   ✓ 原始活动数: ${result.statistics?.originalActivities || 0}`);
      console.log(`   ✓ 压缩后节点数: ${result.statistics?.compressedActivities || 0}`);
      
      // 显示发现的子流程模式
      if (result.subprocesses && result.subprocesses.length > 0) {
        console.log('\n   发现的子流程模式:');
        result.subprocesses.slice(0, 5).forEach((pattern: any, index: number) => {
          console.log(`   ${index + 1}. ${pattern.name}`);
          console.log(`      类型: ${this.getPatternTypeLabel(pattern.type)}`);
          console.log(`      频率: ${pattern.frequency}`);
          console.log(`      置信度: ${Math.round(pattern.confidence * 100)}%`);
          console.log(`      活动: ${pattern.activities.join(' → ')}`);
          console.log('');
        });
      }
      
      console.log('');
    } catch (error) {
      throw new Error(`子流程发现失败: ${error.response?.data?.message || error.message}`);
    }
  }

  private async hierarchicalDFGGeneration() {
    console.log('5. 层次化DFG生成...');
    
    try {
      const response = await axios.post(
        `${API_BASE_URL}/analysis/hierarchical-dfg/${this.processId}`,
        {
          minFrequency: 2,
          minLength: 2,
          maxLength: 8
        },
        {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        }
      );

      const result = response.data;
      console.log(`   ✓ 层次化DFG节点数: ${result.hierarchicalDFG?.nodes?.length || 0}`);
      console.log(`   ✓ 层次化DFG边数: ${result.hierarchicalDFG?.edges?.length || 0}`);
      
      // 统计不同类型的节点
      if (result.hierarchicalDFG?.nodes) {
        const nodeTypes = result.hierarchicalDFG.nodes.reduce((acc: any, node: any) => {
          acc[node.type] = (acc[node.type] || 0) + 1;
          return acc;
        }, {});
        
        console.log('   节点类型分布:');
        Object.entries(nodeTypes).forEach(([type, count]) => {
          console.log(`     ${type === 'subprocess' ? '子流程' : '原子活动'}: ${count}`);
        });
      }
      
      console.log('');
    } catch (error) {
      throw new Error(`层次化DFG生成失败: ${error.response?.data?.message || error.message}`);
    }
  }

  private async analyzeResults() {
    console.log('6. 结果分析...');
    
    try {
      const response = await axios.get(
        `${API_BASE_URL}/analysis/subprocess-patterns/${this.processId}`,
        {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        }
      );

      const result = response.data;
      console.log(`   ✓ 总模式数: ${result.statistics?.totalPatterns || 0}`);
      
      if (result.statistics?.byType) {
        console.log('   模式类型分布:');
        Object.entries(result.statistics.byType).forEach(([type, count]) => {
          console.log(`     ${this.getPatternTypeLabel(type)}: ${count}`);
        });
      }
      
      // 分析最频繁的子流程
      if (result.patterns && result.patterns.length > 0) {
        const mostFrequent = result.patterns.reduce((max: any, pattern: any) => 
          pattern.frequency > max.frequency ? pattern : max
        );
        
        console.log(`\n   最频繁的子流程: ${mostFrequent.name}`);
        console.log(`     出现频率: ${mostFrequent.frequency}`);
        console.log(`     平均耗时: ${this.formatDuration(mostFrequent.avgDuration)}`);
        console.log(`     涉及案例: ${mostFrequent.cases?.length || 0} 个`);
      }
      
      console.log('');
    } catch (error) {
      throw new Error(`结果分析失败: ${error.response?.data?.message || error.message}`);
    }
  }

  private getPatternTypeLabel(type: string): string {
    const labels: Record<string, string> = {
      sequential: '顺序',
      parallel: '并行',
      loop: '循环',
      choice: '选择'
    };
    return labels[type] || type;
  }

  private formatDuration(milliseconds: number): string {
    if (!milliseconds) return '0ms';
    
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }
}

// 运行演示
if (require.main === module) {
  const demo = new SubprocessDiscoveryDemo();
  demo.run().catch(console.error);
}

export { SubprocessDiscoveryDemo };
