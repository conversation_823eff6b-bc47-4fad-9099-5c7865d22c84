#!/bin/bash

# ProMax 配置管理脚本
# 用于管理和验证环境配置

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
ENV_FILE="$PROJECT_ROOT/config/production.env"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

show_help() {
    echo "ProMax 配置管理脚本"
    echo "=================="
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  show        显示当前配置"
    echo "  validate    验证配置完整性"
    echo "  edit        编辑配置文件"
    echo "  backup      备份配置文件"
    echo "  restore     恢复配置文件"
    echo "  template    生成配置模板"
    echo "  diff        比较配置差异"
    echo "  help        显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 show      # 显示当前配置"
    echo "  $0 validate  # 验证配置"
    echo "  $0 edit      # 编辑配置文件"
}

show_config() {
    echo -e "${BLUE}📋 当前配置信息${NC}"
    echo "===================="
    
    if [ ! -f "$ENV_FILE" ]; then
        echo -e "${RED}❌ 配置文件不存在: $ENV_FILE${NC}"
        return 1
    fi
    
    # 加载配置
    source "$ENV_FILE"
    
    echo ""
    echo -e "${GREEN}🖥️  服务器配置${NC}"
    echo "服务器地址: ${SERVER_HOST}"
    echo "用户名: ${SERVER_USER}"
    echo "容器名称: ${CONTAINER_NAME}"
    echo "镜像名称: ${IMAGE_NAME}:${IMAGE_TAG}"
    echo "部署目录: ${REMOTE_DIR}"
    
    echo ""
    echo -e "${GREEN}🗄️  数据库配置${NC}"
    echo "MySQL主机: ${DB_HOST}:${DB_PORT}"
    echo "数据库名: ${DB_DATABASE}"
    echo "用户名: ${DB_USERNAME}"
    echo "密码: ${DB_PASSWORD:0:3}***"
    
    echo ""
    echo -e "${GREEN}📦 Redis配置${NC}"
    echo "Redis主机: ${REDIS_HOST}:${REDIS_PORT}"
    echo "数据库: ${REDIS_DB}"
    echo "前缀: ${REDIS_KEY_PREFIX}"
    echo "过期时间: ${REDIS_DEFAULT_EXPIRE}秒"
    
    echo ""
    echo -e "${GREEN}🚀 服务配置${NC}"
    echo "NestJS端口: ${PORT}"
    echo "Python服务端口: ${PYTHON_SERVICE_PORT}"
    echo "Nginx端口: ${NGINX_PORT}"
    echo "工作进程数: ${MAX_WORKERS}"
    echo "GPU加速: ${ENABLE_GPU_ACCELERATION}"
    
    echo ""
    echo -e "${GREEN}📁 数据目录${NC}"
    echo "数据根目录: ${DATA_ROOT}"
    echo "日志目录: ${LOGS_DIR}"
    echo "上传目录: ${UPLOADS_DIR}"
    echo "报告目录: ${REPORTS_DIR}"
    echo "结果目录: ${RESULT_DATA_DIR}"
}

validate_config() {
    echo -e "${BLUE}🔍 验证配置完整性${NC}"
    echo "===================="
    
    if [ ! -f "$ENV_FILE" ]; then
        echo -e "${RED}❌ 配置文件不存在: $ENV_FILE${NC}"
        return 1
    fi
    
    # 加载配置
    source "$ENV_FILE"
    
    local errors=0
    
    # 验证必需的配置项
    local required_vars=(
        "SERVER_HOST:服务器地址"
        "SERVER_USER:服务器用户"
        "CONTAINER_NAME:容器名称"
        "IMAGE_NAME:镜像名称"
        "DB_HOST:数据库主机"
        "DB_PORT:数据库端口"
        "DB_USERNAME:数据库用户"
        "DB_PASSWORD:数据库密码"
        "DB_DATABASE:数据库名"
        "REDIS_HOST:Redis主机"
        "REDIS_PORT:Redis端口"
        "PORT:NestJS端口"
        "JWT_SECRET:JWT密钥"
    )
    
    echo ""
    echo "检查必需配置项..."
    
    for item in "${required_vars[@]}"; do
        local var_name="${item%%:*}"
        local var_desc="${item##*:}"
        local var_value="${!var_name}"
        
        if [ -z "$var_value" ]; then
            echo -e "${RED}❌ ${var_desc} (${var_name}): 未设置${NC}"
            ((errors++))
        else
            echo -e "${GREEN}✅ ${var_desc} (${var_name}): 已设置${NC}"
        fi
    done
    
    echo ""
    echo "检查网络连接..."
    
    # 测试数据库连接
    if command -v nc >/dev/null 2>&1; then
        if nc -z "$DB_HOST" "$DB_PORT" 2>/dev/null; then
            echo -e "${GREEN}✅ MySQL连接: ${DB_HOST}:${DB_PORT}${NC}"
        else
            echo -e "${RED}❌ MySQL连接失败: ${DB_HOST}:${DB_PORT}${NC}"
            ((errors++))
        fi
        
        if nc -z "$REDIS_HOST" "$REDIS_PORT" 2>/dev/null; then
            echo -e "${GREEN}✅ Redis连接: ${REDIS_HOST}:${REDIS_PORT}${NC}"
        else
            echo -e "${RED}❌ Redis连接失败: ${REDIS_HOST}:${REDIS_PORT}${NC}"
            ((errors++))
        fi
    else
        echo -e "${YELLOW}⚠️  nc命令不可用，跳过网络连接测试${NC}"
    fi
    
    echo ""
    if [ $errors -eq 0 ]; then
        echo -e "${GREEN}🎉 配置验证通过！${NC}"
        return 0
    else
        echo -e "${RED}❌ 发现 $errors 个配置问题${NC}"
        return 1
    fi
}

edit_config() {
    echo -e "${BLUE}📝 编辑配置文件${NC}"
    echo "===================="
    
    if [ ! -f "$ENV_FILE" ]; then
        echo -e "${YELLOW}⚠️  配置文件不存在，将创建新文件${NC}"
        mkdir -p "$(dirname "$ENV_FILE")"
        touch "$ENV_FILE"
    fi
    
    # 使用默认编辑器打开配置文件
    ${EDITOR:-nano} "$ENV_FILE"
    
    echo ""
    echo -e "${GREEN}✅ 配置文件已保存${NC}"
    echo "建议运行验证: $0 validate"
}

backup_config() {
    echo -e "${BLUE}💾 备份配置文件${NC}"
    echo "===================="
    
    if [ ! -f "$ENV_FILE" ]; then
        echo -e "${RED}❌ 配置文件不存在: $ENV_FILE${NC}"
        return 1
    fi
    
    local backup_dir="$PROJECT_ROOT/config/backups"
    local backup_file="$backup_dir/production.env.$(date +%Y%m%d_%H%M%S)"
    
    mkdir -p "$backup_dir"
    cp "$ENV_FILE" "$backup_file"
    
    echo -e "${GREEN}✅ 配置已备份到: $backup_file${NC}"
}

restore_config() {
    echo -e "${BLUE}🔄 恢复配置文件${NC}"
    echo "===================="
    
    local backup_dir="$PROJECT_ROOT/config/backups"
    
    if [ ! -d "$backup_dir" ]; then
        echo -e "${RED}❌ 备份目录不存在: $backup_dir${NC}"
        return 1
    fi
    
    echo "可用的备份文件:"
    ls -la "$backup_dir"/*.env.* 2>/dev/null || {
        echo -e "${RED}❌ 没有找到备份文件${NC}"
        return 1
    }
    
    echo ""
    read -p "请输入要恢复的备份文件名: " backup_name
    
    local backup_file="$backup_dir/$backup_name"
    
    if [ ! -f "$backup_file" ]; then
        echo -e "${RED}❌ 备份文件不存在: $backup_file${NC}"
        return 1
    fi
    
    cp "$backup_file" "$ENV_FILE"
    echo -e "${GREEN}✅ 配置已从备份恢复${NC}"
}

generate_template() {
    echo -e "${BLUE}📄 生成配置模板${NC}"
    echo "===================="
    
    local template_file="$PROJECT_ROOT/config/production.env.template"
    
    cat > "$template_file" << 'EOF'
# ProMax 流程挖掘平台生产环境配置模板
# 请根据实际环境修改以下配置

# 服务器信息
SERVER_HOST=your-server-host
SERVER_USER=your-username
CONTAINER_NAME=promax-platform
IMAGE_NAME=promax-platform
IMAGE_TAG=latest
REMOTE_DIR=/home/<USER>/promax-deployment

# 数据库配置
DB_HOST=your-db-host
DB_PORT=3306
DB_USERNAME=your-db-user
DB_PASSWORD=your-db-password
DB_DATABASE=your-db-name

# Redis配置
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_KEY_PREFIX=promax:
REDIS_DEFAULT_EXPIRE=600
REDIS_DB=0

# 应用配置
NODE_ENV=production
PORT=3003
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=30d

# 其他配置...
EOF
    
    echo -e "${GREEN}✅ 配置模板已生成: $template_file${NC}"
    echo "请复制模板并修改为实际配置:"
    echo "cp $template_file $ENV_FILE"
}

# 主函数
main() {
    case "${1:-help}" in
        show)
            show_config
            ;;
        validate)
            validate_config
            ;;
        edit)
            edit_config
            ;;
        backup)
            backup_config
            ;;
        restore)
            restore_config
            ;;
        template)
            generate_template
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo -e "${RED}❌ 未知命令: $1${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

main "$@"
