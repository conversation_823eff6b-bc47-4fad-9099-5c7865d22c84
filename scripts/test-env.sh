#!/bin/bash

# 测试环境变量加载脚本

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "🧪 测试环境变量加载"
echo "===================="

# 加载环境配置
echo "📋 加载环境配置..."
source "$SCRIPT_DIR/load-env.sh"

echo ""
echo "🔍 测试Docker参数生成..."

echo ""
echo "端口映射参数:"
generate_docker_port_args

echo ""
echo "数据卷参数:"
generate_docker_volume_args

echo ""
echo "环境变量参数 (前10个):"
generate_docker_env_args | head -10

echo ""
echo "📊 统计信息:"
echo "端口映射数量: $(generate_docker_port_args | wc -l)"
echo "数据卷数量: $(generate_docker_volume_args | wc -l)"
echo "环境变量数量: $(generate_docker_env_args | wc -l)"

echo ""
echo "✅ 环境变量加载测试完成"
