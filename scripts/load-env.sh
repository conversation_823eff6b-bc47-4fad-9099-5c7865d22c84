#!/bin/bash

# ProMax 环境变量加载脚本
# 从配置文件加载环境变量

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# 环境配置文件路径
ENV_FILE="$PROJECT_ROOT/config/production.env"

# 检查配置文件是否存在
if [ ! -f "$ENV_FILE" ]; then
    echo "❌ 环境配置文件不存在: $ENV_FILE"
    exit 1
fi

# 加载环境变量
echo "📋 加载环境配置: $ENV_FILE"

# 读取配置文件并导出环境变量
set -a  # 自动导出所有变量
source "$ENV_FILE"
set +a  # 关闭自动导出

# 验证关键环境变量
validate_required_vars() {
    local required_vars=(
        "SERVER_HOST"
        "SERVER_USER"
        "CONTAINER_NAME"
        "IMAGE_NAME"
        "DB_HOST"
        "DB_PORT"
        "DB_USERNAME"
        "DB_PASSWORD"
        "DB_DATABASE"
        "REDIS_HOST"
        "REDIS_PORT"
    )
    
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        echo "❌ 缺少必需的环境变量:"
        printf "   - %s\n" "${missing_vars[@]}"
        return 1
    fi
    
    return 0
}

# 显示加载的关键配置
show_config_summary() {
    echo ""
    echo "✅ 环境配置加载成功"
    echo "===================="
    echo "服务器: ${SERVER_USER}@${SERVER_HOST}"
    echo "容器: ${CONTAINER_NAME}"
    echo "镜像: ${IMAGE_NAME}:${IMAGE_TAG}"
    echo "数据库: ${DB_HOST}:${DB_PORT}/${DB_DATABASE}"
    echo "Redis: ${REDIS_HOST}:${REDIS_PORT}"
    echo "端口映射: ${NGINX_PORT}:80, ${NESTJS_EXTERNAL_PORT}:3003, ${PYTHON_SERVICE_EXTERNAL_PORT}:8000"
    echo ""
}

# 生成Docker环境变量参数
generate_docker_env_args() {
    cat << EOF
-e NODE_ENV=${NODE_ENV}
-e PYTHONPATH=${PYTHONPATH}
-e NESTJS_INTERNAL_PORT=${NESTJS_INTERNAL_PORT}
-e DB_HOST=${DB_HOST}
-e DB_PORT=${DB_PORT}
-e DB_USERNAME=${DB_USERNAME}
-e DB_PASSWORD=${DB_PASSWORD}
-e DB_DATABASE=${DB_DATABASE}
-e REDIS_HOST=${REDIS_HOST}
-e REDIS_PORT=${REDIS_PORT}
-e REDIS_KEY_PREFIX=${REDIS_KEY_PREFIX}
-e REDIS_PASSWORD=${REDIS_PASSWORD}
-e REDIS_DEFAULT_EXPIRE=${REDIS_DEFAULT_EXPIRE}
-e JWT_SECRET=${JWT_SECRET}
-e JWT_EXPIRES_IN=${JWT_EXPIRES_IN}
-e PYTHON_MINING_SERVICE_URL=${PYTHON_MINING_SERVICE_URL}
-e HOST=${HOST}
-e DEBUG=${DEBUG}
-e MYSQL_HOST=${MYSQL_HOST}
-e MYSQL_PORT=${MYSQL_PORT}
-e MYSQL_USER=${MYSQL_USER}
-e MYSQL_PASSWORD=${MYSQL_PASSWORD}
-e MYSQL_DATABASE=${MYSQL_DATABASE}
-e REDIS_DB=${REDIS_DB}
-e MAX_WORKERS=${MAX_WORKERS}
-e ENABLE_GPU_ACCELERATION=${ENABLE_GPU_ACCELERATION}
-e MAX_MEMORY_USAGE_PERCENT=${MAX_MEMORY_USAGE_PERCENT}
-e BATCH_SIZE=${BATCH_SIZE}
-e MIN_FREQUENCY=${MIN_FREQUENCY}
-e MIN_CONFIDENCE=${MIN_CONFIDENCE}
-e MAX_PATTERN_LENGTH=${MAX_PATTERN_LENGTH}
-e LOG_LEVEL=${LOG_LEVEL}
-e ALLOWED_ORIGINS=${ALLOWED_ORIGINS}
-e NUXT_HOST=${NUXT_HOST}
-e NUXT_PORT=${NUXT_PORT}
-e NUXT_PUBLIC_API_BASE=${NUXT_PUBLIC_API_BASE}
-e PYTHON_SERVICE_PORT=${PYTHON_SERVICE_INTERNAL_PORT}
EOF
}

# 生成Docker卷挂载参数
generate_docker_volume_args() {
    cat << EOF
-v ${LOGS_DIR}:/app/logs
-v ${UPLOADS_DIR}:/app/uploads
-v ${REPORTS_DIR}:/app/reports
-v ${RESULT_DATA_DIR}:/app/result-data
EOF
}

# 生成Docker端口映射参数
generate_docker_port_args() {
    cat << EOF
-p ${NGINX_PORT}:80
-p ${NESTJS_EXTERNAL_PORT}:3003
-p ${PYTHON_SERVICE_EXTERNAL_PORT}:8000
EOF
}

# 主函数
main() {
    # 验证必需变量
    if ! validate_required_vars; then
        exit 1
    fi
    
    # 显示配置摘要
    if [ "${1:-}" != "--quiet" ]; then
        show_config_summary
    fi
}

# 如果直接执行此脚本，则运行主函数
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
