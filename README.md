# ProMax - 专业流程挖掘平台

ProMax 是一个基于现代技术栈构建的企业级流程挖掘和业务流程分析平台，专为大规模数据处理和高性能分析而设计，帮助企业发现、分析和优化复杂业务流程。

## 🚀 快速开始

### 生产环境部署
```bash
# 1. 克隆项目
git clone <repository-url> && cd bpmax-pro-mined

# 2. 配置环境变量
vim config/production.env

# 3. 挂载式部署
chmod +x deploy-volume-mount.sh && ./deploy-volume-mount.sh

# 4. 访问应用
# http://*************:3100
```

### 开发环境搭建
```bash
# 1. 安装依赖
cd client && yarn install
cd ../server && yarn install
cd ../python-mining-service && ./setup_env.sh

# 2. 启动服务
cd server && yarn start:dev          # 后端 :3003
cd client && yarn dev                # 前端 :3000
cd python-mining-service && python start_service.py  # Python :8000
```

📖 **详细说明**: [开发环境搭建](#🛠️-快速开始) | [Docker部署](#🚀-docker-部署) | [部署文档](./DEPLOYMENT.md)

## ✨ 核心特性

- **🔍 智能流程发现**: 从事件日志中自动发现业务流程，支持多层级嵌套流程挖掘
- **📊 深度流程分析**: 性能瓶颈识别、一致性检查、流程对比分析
- **🎨 高级可视化**: 基于 GoJS 的交互式 DFG 图表，支持 ECharts 统计图表
- **⚡ GPU 加速**: 支持 Apple M 芯片 MPS 和 NVIDIA CUDA GPU 加速
- **🔄 实时处理**: 异步任务处理，支持大规模数据集
- **📈 智能报告**: 自动生成专业流程挖掘分析报告
- **🌐 多数据源**: 支持 CSV、Excel、飞书多维表格等多种数据源

## 🏗️ 技术架构

### 部署架构
ProMax 采用**挂载式 Docker 部署架构**，实现高效的开发和运维：

```
┌─────────────────────────────────────────┐
│              Docker Container           │
├─────────────────────────────────────────┤
│  Nginx (端口 80 → 外部端口 3100)        │
│  ├── / → Nuxt3 Frontend (端口 3000)     │
│  ├── /api/ → NestJS Backend (端口 3003) │
│  └── /mining/ → Python Service (端口 8000) │
├─────────────────────────────────────────┤
│  Supervisor 进程管理                     │
│  ├── nginx                             │
│  ├── nestjs-server                     │
│  ├── python-mining-service             │
│  └── nuxt-frontend                     │
├─────────────────────────────────────────┤
│  挂载卷                                 │
│  ├── /app/code ← 源代码目录              │
│  ├── /app/logs ← 日志文件               │
│  ├── /app/uploads ← 上传文件            │
│  ├── /app/reports ← 分析报告            │
│  └── /app/result-data ← 挖掘结果        │
└─────────────────────────────────────────┘
```

### 前端 (client)
- **Nuxt 3** - 现代化的 Vue.js 全栈框架
- **Element Plus** - 企业级 Vue 3 组件库
- **TypeScript** - 类型安全的 JavaScript
- **Pinia** - Vue 3 状态管理
- **GoJS** - 专业图表可视化库
- **ECharts** - 数据可视化图表库
- **Sass/SCSS** - CSS 预处理器

### 后端 (server)
- **NestJS** - 企业级 Node.js 框架
- **TypeORM** - TypeScript ORM 框架
- **MySQL** - 关系型数据库
- **Redis** - 高性能缓存和会话存储
- **Swagger** - API 文档自动生成

### Python 挖掘服务 (python-mining-service)
- **FastAPI** - 现代化 Python Web 框架
- **PyTorch** - 深度学习框架，支持 GPU 加速
- **PM4Py** - 专业流程挖掘算法库
- **Pandas/NumPy** - 高性能数据处理
- **Conda** - Python 环境管理

### 容器化技术
- **Docker** - 容器化部署
- **Nginx** - 反向代理和负载均衡
- **Supervisor** - 进程管理和监控
- **挂载卷** - 代码和数据持久化

## 📁 项目结构

```
bpmax-pro-mined/
├── client/                    # Nuxt3 前端应用
│   ├── components/           # Vue 组件库
│   ├── pages/               # 页面路由
│   ├── stores/              # Pinia 状态管理
│   ├── assets/              # 静态资源
│   ├── layouts/             # 布局组件
│   ├── middleware/          # 路由中间件
│   ├── plugins/             # Vue 插件
│   ├── workers/             # Web Workers
│   └── tests/               # 前端测试
├── server/                   # NestJS 后端应用
│   ├── src/                 # 源代码
│   │   ├── modules/         # 业务模块
│   │   ├── database/        # 数据库配置
│   │   ├── common/          # 公共模块
│   │   └── main.ts          # 应用入口
│   ├── test/                # 测试文件
│   ├── uploads/             # 文件上传目录
│   └── test-data/           # 测试数据
├── python-mining-service/    # Python 挖掘服务
│   ├── app/                 # FastAPI 应用
│   ├── services/            # 挖掘算法服务
│   ├── models/              # 数据模型
│   ├── config/              # 配置管理
│   ├── utils/               # 工具函数
│   └── tests/               # Python 测试
├── dev-docs/                # 开发文档
│   ├── api/                 # API 文档
│   ├── database/            # 数据库设计
│   ├── deployment/          # 部署文档
│   ├── development/         # 开发指南
│   └── architecture/        # 架构设计
├── reports/                 # 分析报告输出
├── result-data/             # 挖掘结果数据
├── test-data/               # 测试数据集
├── config/                  # 环境配置
│   └── production.env      # 生产环境配置
├── docker/                  # Docker 配置
│   ├── Dockerfile.runtime   # 运行时基础镜像
│   ├── nginx.conf          # Nginx 配置
│   ├── supervisord.conf    # Supervisor 配置
│   ├── start-mount.sh      # 容器启动脚本
│   └── validate-env.sh     # 环境变量验证
├── scripts/                 # 部署和管理脚本
│   ├── load-env.sh         # 环境变量加载
│   └── config-manager.sh   # 配置管理
├── deploy-volume-mount.sh   # 挂载式部署脚本
├── rebuild-runtime.sh       # 运行时镜像重建脚本
├── DEPLOYMENT.md            # 部署指南
└── README.md                # 项目说明
```

## 🛠️ 快速开始

### 环境要求
- **Node.js** >= 22.0.0
- **Yarn** >= 1.22.0 (包管理器)
- **Python** >= 3.12 (用于挖掘服务)
- **Conda** (Python 环境管理)
- **MySQL** >= 8.0
- **Redis** >= 6.0
- **Docker** (可选，用于容器化部署)

### 开发环境搭建

#### 1. 克隆项目
```bash
git clone <repository-url>
cd bpmax-pro-mined
```

#### 2. 安装依赖
```bash
# 前端依赖
cd client
yarn install

# 后端依赖
cd ../server
yarn install

# Python 挖掘服务依赖
cd ../python-mining-service
chmod +x setup_env.sh
./setup_env.sh
```

#### 3. 配置数据库
```bash
# 创建 MySQL 数据库
mysql -u root -p
CREATE DATABASE promined_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 运行数据库迁移
cd server
npm run migration:run
```

#### 4. 配置环境变量
```bash
# 查看和配置环境变量
source scripts/load-env.sh

# 编辑配置文件
vim config/production.env
```

#### 5. 启动开发服务器
```bash
# 启动后端服务 (端口 3003)
cd server
yarn start:dev

# 启动前端服务 (端口 3000)
cd ../client
yarn dev

# 启动 Python 挖掘服务 (端口 8000)
cd ../python-mining-service
python start_service.py
```

### 访问应用
- **前端应用**: http://localhost:3000
- **后端 API**: http://localhost:3003
- **API 文档**: http://localhost:3003/api/docs
- **Python 服务文档**: http://localhost:8000/docs

## 🚀 Docker 部署

### 挂载式部署（推荐）

ProMax 采用**挂载式部署架构**，通过代码挂载实现快速更新和调试：

```bash
# 给脚本执行权限
chmod +x deploy-volume-mount.sh rebuild-runtime.sh scripts/*.sh

# 挂载式部署（推荐方式）
./deploy-volume-mount.sh

# 重建运行时镜像（可选）
./rebuild-runtime.sh
```

### 部署特点
- **快速更新**: 代码修改后无需重新构建镜像
- **便于调试**: 可直接在服务器上修改代码进行调试
- **分离关注点**: 运行时环境与业务代码分离
- **高效开发**: 支持热更新和快速迭代

### 管理命令

```bash
# 查看容器状态
ssh bpmax4090@************* 'sudo docker ps --filter name=promax-platform'

# 查看容器日志
ssh bpmax4090@************* 'sudo docker logs -f promax-platform'

# 重启容器
ssh bpmax4090@************* 'sudo docker restart promax-platform'

# 查看服务状态
ssh bpmax4090@************* 'sudo docker exec promax-platform supervisorctl status'

# 进入容器
ssh bpmax4090@************* 'sudo docker exec -it promax-platform bash'
```

### 访问地址（生产环境）
- **主页**: http://*************:3100
- **API 文档**: http://*************:3100/api/docs
- **挖掘服务文档**: http://*************:3100/mining/docs
- **直接API访问**: http://*************:3101/api/docs
- **直接挖掘服务访问**: http://*************:3102/docs

### 更新流程

挂载式部署的更新非常简单：

```bash
# 1. 修改本地代码
# 2. 运行部署脚本
./deploy-volume-mount.sh

# 系统会自动：
# - 同步代码到远程服务器
# - 在Docker容器中重新构建
# - 重启服务
```

详细部署说明请查看 [DEPLOYMENT.md](./DEPLOYMENT.md)

## 🎯 主要功能

### 流程发现
- **单层流程挖掘**: 传统的流程发现算法
- **多层级嵌套流程挖掘**: 支持复杂的层级业务流程
- **子流程自动识别**: 智能识别和提取子流程模式
- **GPU 加速处理**: 支持大规模数据集的高性能处理

### 流程分析
- **性能分析**: 识别流程瓶颈和等待时间
- **一致性检查**: 检查实际流程与标准流程的偏差
- **流程对比**: 多个流程版本的对比分析
- **统计分析**: 详细的流程执行统计信息

### 可视化
- **交互式 DFG 图**: 基于 GoJS 的专业流程图
- **统计图表**: 基于 ECharts 的多维度数据可视化
- **实时过滤**: 动态过滤和高亮显示
- **导出功能**: 支持图片和 PDF 导出

### 数据源支持
- **CSV/Excel 文件**: 标准事件日志格式
- **飞书多维表格**: 直接连接飞书数据源
- **数据库连接**: 支持多种数据库直连
- **API 接口**: RESTful API 数据接入

## 📚 文档

详细的开发文档请查看相关目录：

- **[开发指南](./dev-docs/development/README.md)** - 开发环境搭建和开发规范
- **[API 文档](./dev-docs/api/README.md)** - 后端 API 接口文档
- **[数据库设计](./dev-docs/database/README.md)** - 数据库结构和设计
- **[架构设计](./dev-docs/architecture/README.md)** - 系统架构和技术选型
- **[部署文档](./DEPLOYMENT.md)** - 生产环境部署指南
- **[Python 服务文档](./python-mining-service/README.md)** - Python 挖掘服务详细说明

## 🧪 测试

### 运行测试
```bash
# 后端单元测试
cd server
yarn test

# 后端测试覆盖率
yarn test:cov

# 端到端测试
yarn test:e2e

# Python 服务测试
cd ../python-mining-service
pytest tests/
```

### 测试数据
项目提供了多种测试数据集：
- **供应链流程数据**: 复杂的多层级业务流程
- **软件开发流程数据**: 包含分支和循环的流程
- **制造质量管理流程**: 质量检查和返工流程

## 🔧 配置管理

### 环境配置
```bash
# 加载环境配置
source scripts/load-env.sh

# 静默加载（不显示配置摘要）
source scripts/load-env.sh --quiet

# 编辑配置文件
vim config/production.env
```

### 主要配置项
所有配置统一管理在 `config/production.env` 文件中：

- **服务器配置**: 目标服务器地址和用户信息
- **数据库配置**: MySQL 连接信息（**************:33068）
- **Redis 配置**: 缓存和会话存储（**************:63790）
- **端口配置**: 各服务的内部和外部端口映射
- **GPU 配置**: GPU 加速相关设置
- **性能配置**: 工作进程数、内存限制等

## 🤝 贡献指南

1. Fork 项目到您的 GitHub 账户
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 开发规范
- 遵循 ESLint 和 Prettier 代码规范
- 编写单元测试，保持 80%+ 测试覆盖率
- 使用 TypeScript 进行类型安全开发
- 遵循 Git 提交信息规范

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 技术支持

### 故障排查
```bash
# 查看容器状态
ssh bpmax4090@************* 'sudo docker ps --filter name=promax-platform'

# 查看服务日志
ssh bpmax4090@************* 'sudo docker logs -f promax-platform'

# 查看服务状态
ssh bpmax4090@************* 'sudo docker exec promax-platform supervisorctl status'

# 健康检查
curl http://*************:3100/health
```

### 支持资源
- **项目文档**: 查看 dev-docs 目录下的详细文档
- **部署指南**: [DEPLOYMENT.md](./DEPLOYMENT.md) 包含完整的部署和故障排查信息
- **Python服务文档**: [python-mining-service/README.md](./python-mining-service/README.md)
- **GPU部署指南**: [python-mining-service/GPU_DEPLOYMENT_GUIDE.md](./python-mining-service/GPU_DEPLOYMENT_GUIDE.md)
- **问题反馈**: 通过 GitHub Issues 提交问题
- **技术交流**: 欢迎提交 Pull Request 和建议

---

⭐ **如果这个项目对您有帮助，请给我们一个 Star！**

🚀 **ProMax - 让流程挖掘更简单、更高效、更智能！**
