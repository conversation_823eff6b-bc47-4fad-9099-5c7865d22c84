# ProMax Production Environment Configuration
# Generated based on load-env.sh requirements and config/production.env values

# ===========================================
# 服务器配置 (Server Configuration)
# ===========================================
SERVER_HOST=*************
SERVER_USER=promax
CONTAINER_NAME=promax-container
IMAGE_NAME=r.yitaiyitai.com/promax
IMAGE_TAG=latest

# ===========================================
# 应用通用配置 (Application General)
# ===========================================
NODE_ENV=production
PYTHONPATH=/app/python-mining-service
HOST=0.0.0.0
DEBUG=false

# ===========================================
# 数据库配置 (Database Configuration)
# ===========================================
DB_HOST=*************
DB_PORT=3306
DB_USERNAME=promax_user
DB_PASSWORD=ProMax2024!@#
DB_DATABASE=promax_production

# MySQL 配置 (Python Service)
MYSQL_HOST=*************
MYSQL_PORT=3306
MYSQL_USER=promax_user
MYSQL_PASSWORD=ProMax2024!@#
MYSQL_DATABASE=promax_production

# ===========================================
# Redis 配置 (Redis Configuration)
# ===========================================
REDIS_HOST=*************
REDIS_PORT=6379
REDIS_PASSWORD=Redis2024!@#
REDIS_DB=0
REDIS_KEY_PREFIX=promax:
REDIS_DEFAULT_EXPIRE=3600

# ===========================================
# JWT 配置 (JWT Configuration)
# ===========================================
JWT_SECRET=ProMax_JWT_Secret_Key_2024_Production_Environment
JWT_EXPIRES_IN=24h

# ===========================================
# NestJS 服务器配置 (NestJS Server)
# ===========================================
NESTJS_INTERNAL_PORT=3003
NESTJS_EXTERNAL_PORT=3000
API_PREFIX=api/v1
CORS_ENABLED=true
CORS_ORIGINS=http://localhost:3000,https://promax.yitaiyitai.com
FILE_UPLOAD_MAX_SIZE=100MB
FILE_UPLOAD_ALLOWED_TYPES=.csv,.xlsx,.xls,.json,.xml

# ===========================================
# Python 挖掘服务配置 (Python Mining Service)
# ===========================================
PYTHON_MINING_SERVICE_URL=http://localhost:8000
PYTHON_SERVICE_INTERNAL_PORT=8000
PYTHON_SERVICE_EXTERNAL_PORT=8000

# 性能配置 (Performance)
MAX_WORKERS=4
ENABLE_GPU_ACCELERATION=true
MAX_MEMORY_USAGE_PERCENT=80

# 算法配置 (Algorithm)
BATCH_SIZE=1000
MIN_FREQUENCY=0.1
MIN_CONFIDENCE=0.8
MAX_PATTERN_LENGTH=10

# 日志配置 (Logging)
LOG_LEVEL=INFO

# CORS 配置 (Python Service)
ALLOWED_ORIGINS=http://localhost:3000,https://promax.yitaiyitai.com

# ===========================================
# Nuxt3 前端配置 (Nuxt3 Frontend)
# ===========================================
NUXT_HOST=0.0.0.0
NUXT_PORT=3000
NUXT_PUBLIC_API_BASE=http://localhost:3000/api/v1

# ===========================================
# Docker 端口映射 (Docker Port Mapping)
# ===========================================
NGINX_PORT=80

# ===========================================
# 数据卷挂载路径 (Data Volume Mount Paths)
# ===========================================
LOGS_DIR=/opt/promax/data/logs
UPLOADS_DIR=/opt/promax/data/uploads
REPORTS_DIR=/opt/promax/data/reports
RESULT_DATA_DIR=/opt/promax/data/result-data

# ===========================================
# 健康检查配置 (Health Check)
# ===========================================
HEALTH_CHECK_ENDPOINT=/api/v1/health
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3