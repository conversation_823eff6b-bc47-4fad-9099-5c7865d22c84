pipeline {
    agent any
    
    parameters {
        booleanParam(
            name: 'SKIP_TESTS',
            defaultValue: false,
            description: '跳过测试阶段'
        )
        booleanParam(
            name: 'FORCE_REBUILD_RUNTIME',
            defaultValue: false,
            description: '强制重建基础镜像 promax-runtime'
        )
        booleanParam(
            name: 'CLEAN_DEPLOY',
            defaultValue: false,
            description: '清理部署（停止现有容器并重新部署）'
        )
        string(
            name: 'DEPLOY_BRANCH',
            defaultValue: 'main',
            description: '部署分支'
        )
        string(
            name: 'IMAGE_TAG',
            defaultValue: '',
            description: '镜像标签（留空则使用BUILD_NUMBER）'
        )
    }
    
    environment {
        // 部署服务器配置
        DEPLOY_SERVER_IP = '*************'
        DEPLOY_SERVER_USER = 'promax'
        DEPLOY_SERVER_HOST = "${DEPLOY_SERVER_USER}@${DEPLOY_SERVER_IP}"
        
        // Docker镜像仓库配置
        REGISTRY_URL = 'r.yitaiyitai.com'
        IMAGE_NAME = 'promax'
        RUNTIME_IMAGE_NAME = 'promax-runtime'
        RUNTIME_IMAGE_TAG = 'latest'
        
        // 项目配置
        PROJECT_NAME = 'promax'
        CONTAINER_NAME = 'promax-platform'
        REMOTE_DEPLOY_DIR = '/home/<USER>/promax-deploy'
        
        // 构建信息
        BUILD_TIMESTAMP = sh(script: "date '+%Y%m%d_%H%M%S'", returnStdout: true).trim()
        COMMIT_HASH = sh(script: 'git rev-parse --short HEAD', returnStdout: true).trim()
        IMAGE_TAG_FINAL = "${params.IMAGE_TAG ?: BUILD_NUMBER}"
        FULL_IMAGE_NAME = "${REGISTRY_URL}/${IMAGE_NAME}:${IMAGE_TAG_FINAL}"
    }
    
    stages {
        stage('准备阶段') {
            steps {
                script {
                    echo "🚀 开始 ProMax 部署流程"
                    echo "================================"
                    echo "构建编号: ${BUILD_NUMBER}"
                    echo "构建时间: ${BUILD_TIMESTAMP}"
                    echo "提交哈希: ${COMMIT_HASH}"
                    echo "部署分支: ${params.DEPLOY_BRANCH}"
                    echo "镜像名称: ${FULL_IMAGE_NAME}"
                    echo "目标服务器: ${DEPLOY_SERVER_HOST}"
                    echo "================================"
                    
                    // 创建临时目录
                    sh "mkdir -p ./tmpdata/${BUILD_NUMBER}"
                    
                    // 保存构建信息
                    writeFile file: "./tmpdata/${BUILD_NUMBER}/build_info.txt", text: """
BUILD_NUMBER=${BUILD_NUMBER}
BUILD_TIMESTAMP=${BUILD_TIMESTAMP}
COMMIT_HASH=${COMMIT_HASH}
DEPLOY_BRANCH=${params.DEPLOY_BRANCH}
IMAGE_TAG=${IMAGE_TAG_FINAL}
FULL_IMAGE_NAME=${FULL_IMAGE_NAME}
DEPLOY_SERVER=${DEPLOY_SERVER_HOST}
CONTAINER_NAME=${CONTAINER_NAME}
"""
                }
            }
        }
        
        stage('代码检出') {
            steps {
                script {
                    echo "📥 检出代码分支: ${params.DEPLOY_BRANCH}"
                    
                    // 检出指定分支
                    checkout([
                        $class: 'GitSCM',
                        branches: [[name: "*/${params.DEPLOY_BRANCH}"]],
                        userRemoteConfigs: scm.userRemoteConfigs
                    ])
                    
                    // 验证关键文件存在
                    sh '''
                        echo "验证项目结构..."
                        [ -f "docker/Dockerfile.production" ] || { echo "❌ Dockerfile.production 不存在"; exit 1; }
                        [ -f "docker/Dockerfile.runtime" ] || { echo "❌ Dockerfile.runtime 不存在"; exit 1; }
                        [ -d "client" ] || { echo "❌ client 目录不存在"; exit 1; }
                        [ -d "server" ] || { echo "❌ server 目录不存在"; exit 1; }
                        [ -d "python-mining-service" ] || { echo "❌ python-mining-service 目录不存在"; exit 1; }
                        echo "✅ 项目结构验证通过"
                    '''
                }
            }
        }
        
        stage('构建Docker镜像') {
            steps {
                script {
                    echo "🔨 构建应用Docker镜像: ${FULL_IMAGE_NAME}"
                    
                    // 构建应用镜像
                    sh """
                        docker build -f docker/Dockerfile.production -t ${FULL_IMAGE_NAME} .
                    """
                    
                    // 推送镜像到仓库
                    sh """
                        echo "📤 推送镜像到仓库..."
                        docker push ${FULL_IMAGE_NAME}
                    """
                    
                    echo "✅ Docker镜像构建并推送完成"
                }
            }
        }
        
        stage('准备部署文件') {
            steps {
                script {
                    echo "📤 准备部署文件..."
                    
                    // 创建远程部署目录
                    sh """
                        ssh -o StrictHostKeyChecking=no ${DEPLOY_SERVER_HOST} \
                        "mkdir -p ${REMOTE_DEPLOY_DIR}"
                    """
                    
                    // 同步部署相关文件
                    sh """
                        rsync -avz \
                            docker-compose.yml \
                            config/ \
                            scripts/load-env.sh \
                            ${DEPLOY_SERVER_HOST}:${REMOTE_DEPLOY_DIR}/
                    """
                    
                    echo "✅ 部署文件准备完成"
                }
            }
        }
        
        stage('运行测试') {
            when {
                not { params.SKIP_TESTS }
            }
            steps {
                script {
                    echo "🧪 运行测试..."
                    
                    // 在本地运行测试
                    sh """
                        echo "🧪 运行后端测试..."
                        cd server
                        yarn install --frozen-lockfile
                        yarn test || echo "⚠️ 测试失败，但继续部署"
                    """
                }
            }
        }
        
        stage('部署服务') {
            steps {
                script {
                    echo "🚀 开始部署服务..."
                    
                    // 创建环境变量文件
                    sh """
                        echo "📝 创建环境变量文件..."
                        ssh -o StrictHostKeyChecking=no ${DEPLOY_SERVER_HOST} \
                        "cd ${REMOTE_DEPLOY_DIR} && \
                         echo 'IMAGE_NAME=${FULL_IMAGE_NAME}' > .env && \
                         echo 'CONTAINER_NAME=${CONTAINER_NAME}' >> .env && \
                         echo 'BUILD_NUMBER=${BUILD_NUMBER}' >> .env && \
                         echo 'BUILD_TIMESTAMP=${BUILD_TIMESTAMP}' >> .env && \
                         echo 'COMMIT_HASH=${COMMIT_HASH}' >> .env"
                    """
                    
                    // 拉取最新镜像
                    sh """
                        echo "📥 拉取最新镜像..."
                        ssh -o StrictHostKeyChecking=no ${DEPLOY_SERVER_HOST} \
                        "docker pull ${FULL_IMAGE_NAME}"
                    """
                    
                    // 停止现有服务（如果存在）
                    if (params.CLEAN_DEPLOY) {
                        sh """
                            echo "🛑 停止现有服务..."
                            ssh -o StrictHostKeyChecking=no ${DEPLOY_SERVER_HOST} \
                            "cd ${REMOTE_DEPLOY_DIR} && docker-compose down || true"
                        """
                    }
                    
                    // 启动服务
                    sh """
                        echo "🚀 启动服务..."
                        ssh -o StrictHostKeyChecking=no ${DEPLOY_SERVER_HOST} \
                        "cd ${REMOTE_DEPLOY_DIR} && docker-compose up -d"
                    """
                }
            }
        }
        
        stage('健康检查') {
            steps {
                script {
                    echo "🔍 执行健康检查..."
                    
                    // 等待服务启动
                    sleep(time: 30, unit: 'SECONDS')
                    
                    // 检查docker-compose服务状态
                    sh """
                        echo "检查服务状态..."
                        ssh -o StrictHostKeyChecking=no ${DEPLOY_SERVER_HOST} \
                        "cd ${REMOTE_DEPLOY_DIR} && docker-compose ps"
                    """
                    
                    // 检查容器日志
                    sh """
                        echo "检查容器日志..."
                        ssh -o StrictHostKeyChecking=no ${DEPLOY_SERVER_HOST} \
                        "cd ${REMOTE_DEPLOY_DIR} && docker-compose logs --tail=20 ${CONTAINER_NAME} || echo '⚠️ 日志检查失败'"
                    """
                    
                    // 测试API端点
                    sh """
                        echo "测试API端点..."
                        ssh -o StrictHostKeyChecking=no ${DEPLOY_SERVER_HOST} \
                        "curl -f http://localhost:3100/api/health 2>/dev/null || echo '⚠️ API健康检查失败'"
                    """
                }
            }
        }
    }
    
    post {
        always {
            script {
                // 生成部署报告
                def deployReport = """
🎉 ProMax 部署完成报告
================================
构建编号: ${BUILD_NUMBER}
构建时间: ${BUILD_TIMESTAMP}
提交哈希: ${COMMIT_HASH}
部署分支: ${params.DEPLOY_BRANCH}
镜像名称: ${FULL_IMAGE_NAME}
目标服务器: ${DEPLOY_SERVER_HOST}
容器名称: ${CONTAINER_NAME}

访问地址:
- 主应用: http://${DEPLOY_SERVER_IP}:3100
- API文档: http://${DEPLOY_SERVER_IP}:3100/api/docs
- 挖掘服务文档: http://${DEPLOY_SERVER_IP}:3100/mining/docs

管理命令:
- 查看服务状态: ssh ${DEPLOY_SERVER_HOST} 'cd ${REMOTE_DEPLOY_DIR} && docker-compose ps'
- 查看服务日志: ssh ${DEPLOY_SERVER_HOST} 'cd ${REMOTE_DEPLOY_DIR} && docker-compose logs -f'
- 重启服务: ssh ${DEPLOY_SERVER_HOST} 'cd ${REMOTE_DEPLOY_DIR} && docker-compose restart'
- 停止服务: ssh ${DEPLOY_SERVER_HOST} 'cd ${REMOTE_DEPLOY_DIR} && docker-compose down'
- 启动服务: ssh ${DEPLOY_SERVER_HOST} 'cd ${REMOTE_DEPLOY_DIR} && docker-compose up -d'
- 进入容器: ssh ${DEPLOY_SERVER_HOST} 'cd ${REMOTE_DEPLOY_DIR} && docker-compose exec ${CONTAINER_NAME} bash'

部署目录: ${REMOTE_DEPLOY_DIR}
================================
"""
                
                writeFile file: "./tmpdata/${BUILD_NUMBER}/deploy-report.txt", text: deployReport
                echo deployReport
                
                // 清理临时文件
                sh "rm -rf ./tmpdata/${BUILD_NUMBER} || true"
            }
        }
        
        success {
            echo "✅ ProMax 部署成功完成！"
        }
        
        failure {
            script {
                echo "❌ ProMax 部署失败！"
                
                // 收集错误日志
                sh """
                    echo "收集错误信息..."
                    ssh -o StrictHostKeyChecking=no ${DEPLOY_SERVER_HOST} \
                    "cd ${REMOTE_DEPLOY_DIR} && docker-compose logs --tail=50 2>&1" || echo "无法获取服务日志"
                """
            }
        }
    }
}