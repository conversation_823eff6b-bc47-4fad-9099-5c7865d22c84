#!/bin/bash

# ProMax 运行时镜像重建脚本
# 用于强制重建包含所有必要工具的运行时基础镜像

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 加载环境配置
source "$SCRIPT_DIR/scripts/load-env.sh" --quiet

echo "🔄 ProMax 运行时镜像重建"
echo "========================"
echo "目标服务器: ${SERVER_HOST}"
echo ""

# 定义镜像信息
RUNTIME_IMAGE_NAME="promax-runtime"
RUNTIME_IMAGE_TAG="latest"
REMOTE_CODE_DIR="/home/<USER>/promax-code"

# 1. 同步Dockerfile到远程服务器
echo "📤 同步Dockerfile到远程服务器..."
ssh ${SERVER_USER}@${SERVER_HOST} "mkdir -p ${REMOTE_CODE_DIR}/docker"

scp docker/Dockerfile.runtime ${SERVER_USER}@${SERVER_HOST}:${REMOTE_CODE_DIR}/docker/
scp docker/nginx.conf ${SERVER_USER}@${SERVER_HOST}:${REMOTE_CODE_DIR}/docker/
scp docker/supervisord.conf ${SERVER_USER}@${SERVER_HOST}:${REMOTE_CODE_DIR}/docker/
scp docker/start-mount.sh ${SERVER_USER}@${SERVER_HOST}:${REMOTE_CODE_DIR}/docker/

echo "✅ Dockerfile同步成功"

# 2. 创建远程重建脚本
cat > /tmp/rebuild-runtime-remote.sh << EOF
#!/bin/bash

set -e

RUNTIME_IMAGE_NAME="promax-runtime"
RUNTIME_IMAGE_TAG="latest"
REMOTE_CODE_DIR="/home/<USER>/promax-code"

echo "🔄 开始重建运行时镜像..."
echo "当前用户: \$(whoami)"

cd \${REMOTE_CODE_DIR}

# 删除现有镜像
echo "🗑️  删除现有运行时镜像..."
if docker images --format 'table {{.Repository}}:{{.Tag}}' | grep -q "^\${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG}\$"; then
    docker rmi \${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG} || true
    echo "✅ 现有镜像已删除"
fi

# 构建新的运行时镜像
echo "📦 构建新的运行时镜像..."
docker build -f docker/Dockerfile.runtime -t \${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG} .

if [ \$? -ne 0 ]; then
    echo "❌ 运行时镜像构建失败"
    exit 1
fi

echo "✅ 运行时镜像构建成功"

# 验证镜像
echo "🔍 验证运行时镜像..."
echo "Node.js版本: \$(docker run --rm \${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG} node --version)"
echo "npm版本: \$(docker run --rm \${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG} npm --version)"

if docker run --rm \${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG} which yarn > /dev/null 2>&1; then
    echo "yarn版本: \$(docker run --rm \${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG} yarn --version)"
    echo "✅ yarn可用"
else
    echo "❌ yarn不可用"
    exit 1
fi

echo "Python版本: \$(docker run --rm \${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG} python --version)"
echo "pip版本: \$(docker run --rm \${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG} pip --version)"

echo ""
echo "🎉 运行时镜像重建完成！"
echo "========================"
echo "镜像名称: \${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG}"
echo "包含工具: Node.js 22, yarn, Python 3.12, nginx, supervisor"

EOF

# 3. 上传并执行重建脚本
echo "📤 上传重建脚本..."
scp /tmp/rebuild-runtime-remote.sh ${SERVER_USER}@${SERVER_HOST}:~/rebuild-runtime-remote.sh

echo "🔄 在服务器上执行重建..."
ssh -t ${SERVER_USER}@${SERVER_HOST} "chmod +x ~/rebuild-runtime-remote.sh && sudo ~/rebuild-runtime-remote.sh"

if [ $? -ne 0 ]; then
    echo "❌ 运行时镜像重建失败"
    exit 1
fi

# 清理临时文件
rm -f /tmp/rebuild-runtime-remote.sh

echo ""
echo "🎉 运行时镜像重建完成！"
echo "======================"
echo "现在可以重新运行部署脚本："
echo "./deploy-volume-mount.sh"
echo ""
