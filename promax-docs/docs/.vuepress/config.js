import { defaultTheme } from '@vuepress/theme-default'
import { defineUserConfig } from 'vuepress'
import { viteBundler } from '@vuepress/bundler-vite'
import { slimsearchPlugin } from '@vuepress/plugin-slimsearch'
import { markdownChartPlugin } from '@vuepress/plugin-markdown-chart'


export default defineUserConfig({
  lang: 'zh-CN',
  description: 'ProMAX 专业流程挖掘平台用户操作指南',

  head: [
    ['link', { rel: 'icon', href: '/images/promax.svg' }],
    ['meta', { name: 'viewport', content: 'width=device-width,initial-scale=1' }]
  ],

  theme: defaultTheme({
    logo: '/images/promax.svg',

    navbar: [
      {
        text: '首页',
        link: '/'
      },
      {
        text: '快速入门',
        children: [
          {
            text: '介绍',
            link: '/guide/about'
          },
          {
            text: '主要页面',
            link: '/guide/overview'
          },


        ]
      },
      {
        text: '操作指南',
        children: [
          {
            text: '流程管理',
            link: '/guide/process-management'
          },
          {
            text: '数据管理',
            link: '/guide/data-management'
          },
          {
            text: '流程发现',
            link: '/guide/process-discovery'
          },
          {
            text: '性能分析',
            link: '/guide/performance-analysis'
          },
          {
            text: '符合性检查',
            link: '/guide/conformance-checking'
          }
        ]
      },

    ],

    sidebar: {
      // 快速入门部分 - 统一侧边栏
      '/guide/about': [
        {
          text: '快速入门',
          children: [
            '/guide/about',
            '/guide/overview'
          ]
        }
      ],

      '/guide/overview': [
        {
          text: '快速入门',
          children: [
            '/guide/about',
            '/guide/overview'
          ]
        }
      ],

      // 流程管理
      '/guide/process-management': [
        {
          text: '流程管理',
          children: [
            '/guide/process-management'
          ]
        }
      ],

      // 导入数据
      '/guide/data-management': [
        {
          text: '导入数据',
          children: [
            '/guide/data-management'
          ]
        }
      ],

      // 流程发现
      '/guide/process-discovery': [
        {
          text: '流程发现',
          children: [
            '/guide/process-discovery'
          ]
        }
      ],

      // 性能分析
      '/guide/performance-analysis': [
        {
          text: '性能分析',
          children: [
            '/guide/performance-analysis'
          ]
        }
      ],

      // 符合性检查
      '/guide/conformance-checking': [
        {
          text: '符合性检查',
          children: [
            '/guide/conformance-checking'
          ]
        }
      ]
    },

    // 页面配置
    editLink: false,
    lastUpdated: true,
    lastUpdatedText: '最后更新',
    contributors: false,

    // 主题色配置
    colorMode: 'auto',
    colorModeSwitch: true
  }),

  bundler: viteBundler(),

  plugins: [
    slimsearchPlugin({
      // 索引全部内容
      indexContent: true,
      // 自定义字段
      customFields: [
        {
          getter: (page) => page.title,
          formatter: "标题：$content",
        },
        {
          getter: (page) => page.frontmatter.description,
          formatter: "描述：$content",
        },
      ],
      // 搜索热键
      hotKeys: [
        { key: 'k', ctrl: true },
        { key: '/', ctrl: true },
      ],
      // 搜索结果数量
      resultNum: 10,
      // 搜索延迟
      searchDelay: 150,
      // 建议词数量
      suggestionsNum: 5,
      // 本地化
      locales: {
        '/': {
          placeholder: '搜索文档',
        },
      },
    }),
    markdownChartPlugin({
      // 启用 Mermaid 图表
      mermaid: true,
      // 启用 Chart.js
      chart: true,
      // 启用 ECharts
      echarts: true,
      // 启用 Flowchart
      flowchart: true,
    }),
  ],
})
