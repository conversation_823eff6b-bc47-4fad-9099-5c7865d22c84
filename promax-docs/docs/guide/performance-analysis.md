# 性能分析

ProMAX 提供全面的性能分析工具，帮助您深入了解流程执行效率、识别瓶颈并制定优化策略。

您可以通过点击**开始性能分析**按钮来启动性能分析。
![性能分析按钮](./images/performance-analysis/start-btn.png)

## 静态分析结果

静态分析结果提供了流程执行的基本性能指标和统计信息，您可以根据瓶颈识别和改进建议数据来优化流程。

### 关键性能指标

您可以在静态分析结果中查看以下关键性能指标：
- **平均案例持续时间**：每个案例的平均执行时间。
- **最短案例时间**：流程中执行的最短案例的持续时间。
- **最长案例时间**：流程中执行的最长案例的持续时间。
- **分析案例总数**：流程中参与分析的案例总数。

![关键性能指标](./images/performance-analysis/key-metrics.png)

### 案例持续分布时间

案例持续分布时间图表展示了不同持续时间的案例数量分布，帮助您识别执行时间较长的案例。

![案例持续分布时间](./images/performance-analysis/case-duration-distribution.png)

### 活动性能分析

活动性能分析提供了每个活动的执行时间和案例数量，帮助您识别执行效率较低的活动。

![活动性能分析](./images/performance-analysis/activity-performance.png)

### 瓶颈识别

瓶颈识别部分列出了流程中执行时间较长的活动和案例，帮助您快速定位性能问题。

![瓶颈识别](./images/performance-analysis/bottleneck-identification.png)

### 改进建议

改进建议部分提供了针对识别出的瓶颈和低效活动的优化建议，帮助您提升流程性能。
![改进建议](./images/performance-analysis/improvement-suggestions.png)

## 性能趋势分析

您可以点击趋势对比上的任意点来加载当前周期与上一周期的详细分析结果。
![趋势对比分析](./images/performance-analysis/trend-compare.png)

### 趋势对比

趋势对比中展示不同时间周期内各项性能指标的变化趋势：
- **平均持续时间**：展示每个时间周期内的平均案例持续时间。
- **平均等待时间**：展示每个时间周期内的平均资源等待时间。
- **案例数量**：展示每个时间周期内的案例数量。

您可以点击趋势对比上的任意点来加载当前周期与上一周期的详细分析结果。
![趋势对比分析](./images/performance-analysis/trend-compare.png)

### 性能排行榜

性能排行榜展示耗时变化最显著的活动或资源, 点击项目可查看具体耗时数据
![性能排行榜](./images/performance-analysis/performance-ranking.png)

### 活动耗时占比

活动耗时占比图表展示了各个活动在总执行时间中的占比，帮助您识别耗时较长的活动。
![活动耗时占比](./images/performance-analysis/activity-time-share.png)

### 资源等待时间堆叠图

资源等待时间堆叠图展示了不同资源在各个时间周期内的等待时间分布，帮助您识别资源瓶颈。
![资源等待时间堆叠图](./images/performance-analysis/resource-waiting-time-stacked.png)
