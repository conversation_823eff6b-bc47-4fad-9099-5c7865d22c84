# 流程发现

导入事件日志数据后，您可以使用流程发现中查看生成的流程模型和路径。

## 基础流程发现

单击**开始流程发现**按钮后，系统将自动分析事件日志数据，并生成直接跟随图（DFG）和相关统计信息。

![流程发现分析结果](./images/discovery-main.png)

#### 配置发现参数

单击**流程发现配置**可以自定义参数：

- **必需活动**：选择必须包含的活动节点
- **强制刷新**：忽略缓存，重新计算结果

## 子流程发现

子流程发现用于从完整流程中自动识别频繁出现的“局部序列/片段”（如常见审批链、标准作业链），便于复用与优化。

![子流程发现界面示意](./images/discovery-subprocess.png)

- 在流程发现页面右上角，点击“开始子流程发现”
- 系统会基于事件日志自动计算，并在右侧列出“发现的子流程模式”

> 首次运行在数据量较大时可能需要数十秒，请耐心等待。再次运行可利用缓存或按需强制刷新。

### 结果页结构
- 左侧主画布：渲染当前选中的子流程路径
  - 起止节点使用绿色（开始/结束），核心活动以蓝色方块串联
  - 支持放大、缩小、居中/适屏、重置视图
- 右侧“发现的子流程模式”列表：每条卡片包含
  - 模式名称（自动生成）与活动序列预览
  - 统计信息：出现次数、覆盖的案例占比（%）等
  - 操作按钮：
    - “在图中定位” 将该模式渲染/高亮到主画布
- 右下“分析统计”卡片：展示已发现模式数、平均长度、覆盖率等关键指标

### 交互与操作
- 选择模式
  - 在右侧列表点击“在图中定位”，左侧画布即显示该子流程的端到端路径
  - 可重复点击对不同模式进行对比
- 画布浏览
  - 鼠标滚轮缩放、按住空白处拖拽平移
  - 右上角工具按钮支持“放大/缩小/重置/适应屏幕”

## 流程比对分析

您可以通过对比分析功能，深入了解不同时间段内流程的变化和趋势。

选择**时间范围**后，单击**开始比对分析**按钮。

![流程对比分析](./images/compare.png)
