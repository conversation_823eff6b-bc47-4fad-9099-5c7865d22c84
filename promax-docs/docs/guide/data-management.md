# 数据管理

创建流程项目后，您现在需要导入数据或与数据源建立连接。

有关数据连接类型的详细信息，请参阅：[数据连接类型](#数据连接类型)。

您可以通过单击流程中的**添加数据**来上传与业务相关的数据文件。

![数据上传界面](./images/data-management/upload-btn.png)

## 数据连接类型

在 ProMAX 平台上配置和管理数据连接时，您可以使用以下连接类型：

### BPMAX

[BPMAX](https://www.bpmx.cn) 提供组织数字孪生解决方案，能够帮助企业管理快速从线下走向线上，从信息化走向数字化、自动化、智能化.

### 飞书多维表格
### 飞书审批
### SAP
### Salesforce
### Jira
### 泛微
### 蓝凌
### 本地数据源

您可以通过上传文件将数据添加到 ProMAX 平台，而无需建立与数据源的连接。

#### 支持的文件类型

将数据与 ProMAX 平台集成时，可以上传以下文件类型：
- **CSV**：逗号分隔值文件
- **Excel**：.xlsx 或 .xls 格式的电子表格文件

#### 文件大小限制

上传的文件大小不得超过 100MB。

#### 上传数据文件

1. 在流程页面单击**添加数据**。
2. 单击**上传文件**选项。
   ![上传文件](./images/data-management/upload-file.png)
3. 将文件拖放到上传区域，或单击上传区域选择文件。
   上传后，ProMAX 将自动识别并处理时间字段，您可根据需要进行调整。
   ![文件上传成功](./images/data-management/upload-success.png)
4. 单击**下一步：配置字段映射**继续数据处理。
   您可以在此步骤中配置数据字段映射，将上传的文件中的列映射到 ProMAX 的标准字段。其中必填字段包括：
   - **案例ID (caseId)**：流程实例的唯一标识
   - **活动名称 (activity)**：业务步骤的描述
   - **时间戳 (timestamp)**：活动发生的时间
   ![配置字段映射](./images/data-management/field-mapping.png)
5. 完成字段映射后，单击**下一步**，系统将自动进行数据验证和处理。

#### 数据库连接

要创建新的数据库连接，请单击**数据库连接**
![数据库连接](./images/data-management/database-connection.png)

然后，你可以在多种数据库之间进行选择和配置，支持 MySQL、PostgreSQL、SQL Server 和 Oracle 等主流数据库。

#### API接口


