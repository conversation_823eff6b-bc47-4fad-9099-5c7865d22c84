# 流程管理

在 ProMAX 平台中，流程项目是数据分析的基础容器，用于组织和管理特定业务流程的所有相关数据和分析结果。

## 创建流程

1. 单击**仪表板 - 创建新流程**或**流程管理 - 创建流程**。
   ![创建流程](./images/process-management/processes-create1.png)
   ![创建流程](./images/process-management/processes-create2.png)

2. 填写流程名称、描述、业务领域和初始状态信息。
   <img src="./images/process-management/processes-create-form.png" width="400">

## 流程主要功能

查看流程时，可以使用以下主要功能：

### 数据统计

在此部分，您可以查看与流程相关的各种统计信息，例如事件总数、案例数量、活动类型和资源数量等。

![数据统计](./images/process-management/processes-statistics.png)

### 添加数据

您可以上传与业务相关的数据文件。

有关数据管理的更多信息，请参阅 [数据管理](./data-management.md)。

### 流程发现

查看自动生成的流程模型和路径。

有关流程发现的更多信息，请参阅 [流程发现](./process-discovery.md)。

### 性能分析

您可以查看流程的性能分析结果，了解流程的性能和瓶颈。

有关性能分析的更多信息，请参阅 [性能分析](./performance-analysis.md)。

### 符合性检查

您可以对流程进行符合性检查，验证实际执行与预期模型之间的一致性。

有关符合性检查的更多信息，请参阅 [符合性检查](./conformance-checking.md)。

## 管理现有流程

创建流程后，您可以在流程管理页面单击选项进行管理。
![管理流程](./images/process-management/processes-manage.png)

您有以下可用选项：
- **重命名**：更改流程的名称，现有设置和配置不受影响。
- **修改流程描述**：更新流程的描述信息，帮助团队成员更好地理解流程的目的与范围。
- **修改业务领域**：更改流程所属的业务领域，适用于流程跨部门或跨业务线的情况。
- **修改状态**：更新流程的初始状态，适用于流程状态变更或重新激活的场景。
- **删除**：删除流程项目，此操作不可恢复，请谨慎使用。
