# 符合性检查

执行流程发现或添加 BPMN 模型后，您可以使用符合性检查功能来验证实际执行与标准模型之间的符合性。

## BPMN 模型管理

在 ProMAX 中，BPMN 模型是符合性检查的基础。您可以创建、编辑和管理 BPMN 模型，以便在符合性检查中使用。

### 添加 BPMN 模型

1. 在符合性检查页面单击**创建 BPMN 模型**。
   ![创建 BPMN 模型](./images/conformance/create-model.png)
2. 填写模型的基本信息并导入 BPMN 文件。
   ![模型信息](./images/conformance/model-info.png)

### 管理现有模型

创建模型后，您可以在 BPMN 模型管理页面单击选项管理现有模型。

![管理 BPMN 模型](./images/conformance/manage-models.png)

您有以下可用选项：
- **验证模型**：检查模型的语法和语义是否符合BPMN标准
- **归档模型**：将模型移至归档状态
- **符合性检查**：使用该模型进行符合性检查
- **下载 BPMN**：下载模型的 BPMN 文件
- **删除模型**：删除不再需要的模型

## 执行符合性检查

1. 单击符合性检查页面上的**执行符合性检查**按钮。
   ![执行符合性检查](./images/conformance/conformance-check.png)
2. 填写符合性检查配置表单，包括选择流程、BPMN模型、时间范围和算法等。
   当选择"从流程发现结果生成"选项时，系统会自动填充当前流程的DFG（直接跟随图）作为模型。
   ![符合性检查配置](./images/conformance/configuration.png)
3. 单击**开始符合性检查**，系统将开始执行符合性检查。
4. 如果选择了"从流程发现结果生成"选项，系统会自动跳转到流程发现结果编辑页面。
   ![流程发现结果生成模型示例](./images/discovery-edit.png)
5. 编辑完成后，单击**保存模型**，系统将保存生成的BPMN模型并自动开始符合性检查。


## 符合性分析结果

### 偏差分析

![符合性分析结果](./images/deviation.png)

#### 偏差类型

ProMAX 识别以下类型的偏差：

1. **缺失活动**（Missing Activity）
   - 标准模型中要求但实际未执行的活动
   - 在BPMN图上以虚线框显示
   - 影响：流程不完整，可能影响质量

2. **额外活动**（Extra Activity）
   - 实际执行但标准模型中不存在的活动
   - 在偏差列表中单独显示
   - 影响：增加成本，可能降低效率

3. **错误顺序**（Wrong Order）
   - 活动执行顺序与标准模型不符
   - 在BPMN图上以红色连接线标注
   - 影响：可能影响流程逻辑和结果

4. **跳过活动**（Skipped Activity）
   - 应该执行但被跳过的活动
   - 与缺失活动类似，但原因不同
   - 影响：流程不完整，可能存在风险

5. **重复活动**（Repeated Activity）
   - 不应重复但被重复执行的活动
   - 在活动节点上显示重复标记
   - 影响：浪费资源，增加成本

6. **时间违规**（Timing Violation）
   - 不符合时间约束的执行
   - 需要启用时间分析功能
   - 影响：可能影响服务质量和客户满意度

### 变体分析

![变体分析结果](./images/variant.png)

#### 根据变体修改标准模型

1. 点击模型中虚拟的节点或连线选择将其添加到标准模型中
   ![根据变体修改标准模型](./images/variant-add.png)
2. 选择完成后，单击**添加到标准模型**，系统将保存修改后的标准模型。