#!/usr/bin/env ts-node

import { ConformanceTestDataGenerator } from '../src/test-utils/conformance-test-data-generator';

async function main() {
  console.log('🚀 开始生成符合性检查测试数据...\n');

  try {
    const generator = new ConformanceTestDataGenerator();
    await generator.generateAllTestData();
    
    console.log('\n✅ 测试数据生成完成！');
    console.log('\n生成的文件包括：');
    console.log('📄 BPMN模型文件:');
    console.log('  - simple-process.bpmn (简单顺序流程)');
    console.log('  - complex-process.bpmn (包含网关的复杂流程)');
    console.log('  - parallel-process.bpmn (并行流程)');
    console.log('\n📊 测试数据CSV文件:');
    console.log('  - excellent-conformance.csv (完全符合，预期得分: 1.0)');
    console.log('  - good-conformance.csv (良好符合，预期得分: 0.8)');
    console.log('  - fair-conformance.csv (一般符合，预期得分: 0.6)');
    console.log('  - poor-conformance.csv (较差符合，预期得分: 0.3)');
    console.log('\n📋 场景描述文件:');
    console.log('  - *.json (每个场景的详细描述和统计信息)');
    
    console.log('\n💡 使用说明：');
    console.log('1. 将BPMN文件上传到系统中作为参考模型');
    console.log('2. 将对应的CSV文件上传作为事件日志');
    console.log('3. 执行符合性检查并验证结果是否符合预期');
    
  } catch (error) {
    console.error('❌ 生成测试数据时发生错误:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
