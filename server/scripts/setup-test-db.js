const mysql = require('mysql2/promise');
const dotenv = require('dotenv');
const path = require('path');
const { execSync } = require('child_process');

// 加载测试环境变量
dotenv.config({ path: path.join(__dirname, '../.env.test') });

async function setupTestDatabase() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
  });

  try {
    // 删除并重新创建测试数据库
    await connection.execute(`DROP DATABASE IF EXISTS \`${process.env.DB_DATABASE}\``);
    await connection.execute(`CREATE DATABASE \`${process.env.DB_DATABASE}\``);
    console.log(`✅ 测试数据库 ${process.env.DB_DATABASE} 已创建`);

    // 运行数据库迁移
    console.log('🔄 正在运行数据库迁移...');
    execSync('npm run migration:run', {
      stdio: 'inherit',
      env: { ...process.env, NODE_ENV: 'test' }
    });
    console.log(`✅ 测试数据库 ${process.env.DB_DATABASE} 已准备就绪`);
  } catch (error) {
    console.error('❌ 创建测试数据库失败:', error.message);
    process.exit(1);
  } finally {
    await connection.end();
  }
}

async function cleanupTestDatabase() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
  });

  try {
    // 删除测试数据库
    await connection.execute(`DROP DATABASE IF EXISTS \`${process.env.DB_DATABASE}\``);
    console.log(`🗑️ 测试数据库 ${process.env.DB_DATABASE} 已清理`);
  } catch (error) {
    console.error('❌ 清理测试数据库失败:', error.message);
  } finally {
    await connection.end();
  }
}

// 根据命令行参数执行相应操作
const action = process.argv[2];

if (action === 'setup') {
  setupTestDatabase();
} else if (action === 'cleanup') {
  cleanupTestDatabase();
} else {
  console.log('用法: node setup-test-db.js [setup|cleanup]');
  process.exit(1);
}
