#!/usr/bin/env ts-node

import { ConformanceAlgorithmService } from '../src/conformance/conformance-algorithm.service';
import { BpmnModel } from '../src/entities/bpmn-model.entity';
import { EventLog } from '../src/entities/event-log.entity';

// 辅助函数：创建完整的 EventLog 对象
function createEventLog(
  id: number,
  caseId: string,
  activity: string,
  timestamp: Date,
  resource: string,
  processId: number,
): EventLog {
  return {
    id,
    caseId,
    activity,
    timestamp,
    resource,
    processId,
    createdAt: new Date(),
  } as EventLog;
}

async function testConformanceAlgorithm() {
  console.log('🧪 测试符合性检查算法...\n');

  const algorithmService = new ConformanceAlgorithmService();

  // 创建测试BPMN模型
  const testBpmnModel: Partial<BpmnModel> = {
    id: 1,
    name: '测试流程',
    activities: ['申请提交', '初审', '复审', '批准'],
    paths: [
      { from: '开始', to: '申请提交' },
      { from: '申请提交', to: '初审' },
      { from: '初审', to: '复审' },
      { from: '复审', to: '批准' },
      { from: '批准', to: '结束' },
    ],
  } as BpmnModel;

  // 创建测试事件日志
  const testEventLogs: EventLog[] = [
    // 案例1：完全符合
    createEventLog(
      1,
      'CASE_001',
      '申请提交',
      new Date('2024-01-01T10:00:00Z'),
      'User1',
      1,
    ),
    createEventLog(
      2,
      'CASE_001',
      '初审',
      new Date('2024-01-01T11:00:00Z'),
      'User2',
      1,
    ),
    createEventLog(
      3,
      'CASE_001',
      '复审',
      new Date('2024-01-01T12:00:00Z'),
      'User3',
      1,
    ),
    createEventLog(
      4,
      'CASE_001',
      '批准',
      new Date('2024-01-01T13:00:00Z'),
      'User4',
      1,
    ),

    // 案例2：跳过复审
    createEventLog(
      5,
      'CASE_002',
      '申请提交',
      new Date('2024-01-02T10:00:00Z'),
      'User1',
      1,
    ),
    createEventLog(
      6,
      'CASE_002',
      '初审',
      new Date('2024-01-02T11:00:00Z'),
      'User2',
      1,
    ),
    createEventLog(
      7,
      'CASE_002',
      '批准',
      new Date('2024-01-02T12:00:00Z'),
      'User4',
      1,
    ),

    // 案例3：额外活动
    createEventLog(
      8,
      'CASE_003',
      '申请提交',
      new Date('2024-01-03T10:00:00Z'),
      'User1',
      1,
    ),
    createEventLog(
      9,
      'CASE_003',
      '补充材料',
      new Date('2024-01-03T10:30:00Z'),
      'User1',
      1,
    ),
    createEventLog(
      10,
      'CASE_003',
      '初审',
      new Date('2024-01-03T11:00:00Z'),
      'User2',
      1,
    ),
    createEventLog(
      11,
      'CASE_003',
      '复审',
      new Date('2024-01-03T12:00:00Z'),
      'User3',
      1,
    ),
    createEventLog(
      12,
      'CASE_003',
      '批准',
      new Date('2024-01-03T13:00:00Z'),
      'User4',
      1,
    ),
  ];

  try {
    console.log('📊 执行符合性检查...');
    const result = await algorithmService.performAlignment(
      testEventLogs,
      testBpmnModel as BpmnModel,
      {
        alignmentAlgorithm: 'heuristic',
        includeCaseAnalysis: true,
        includeActivityAnalysis: true,
      },
    );

    console.log('\n✅ 符合性检查结果:');
    console.log(
      `适应性得分 (Fitness): ${(result.fitnessScore * 100).toFixed(2)}%`,
    );
    console.log(
      `精确性得分 (Precision): ${(result.precisionScore * 100).toFixed(2)}%`,
    );
    console.log(
      `泛化性得分 (Generalization): ${(result.generalizationScore * 100).toFixed(2)}%`,
    );
    console.log(
      `简洁性得分 (Simplicity): ${(result.simplicityScore * 100).toFixed(2)}%`,
    );

    console.log(`\n📈 案例统计:`);
    console.log(`总案例数: ${result.caseResults.length}`);
    console.log(
      `符合案例数: ${result.caseResults.filter((c) => c.isConforming).length}`,
    );
    console.log(
      `偏差案例数: ${result.caseResults.filter((c) => !c.isConforming).length}`,
    );

    console.log(`\n🔍 偏差详情:`);
    result.deviations.forEach((deviation, index) => {
      console.log(
        `${index + 1}. [${deviation.caseId}] ${deviation.type}: ${deviation.description}`,
      );
    });

    console.log(`\n📋 案例分析:`);
    result.caseResults.forEach((caseResult, index) => {
      console.log(`案例 ${index + 1} (${caseResult.caseId}):`);
      console.log(`  - 符合性: ${caseResult.isConforming ? '✅' : '❌'}`);
      console.log(
        `  - 得分: ${(caseResult.conformanceScore * 100).toFixed(2)}%`,
      );
      console.log(`  - 轨迹: ${caseResult.trace.join(' → ')}`);
      console.log(`  - 偏差数: ${caseResult.deviationCount}`);
    });
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error(error.stack);
  }
}

if (require.main === module) {
  testConformanceAlgorithm();
}
