#!/bin/bash

# ProMax 数据库设置脚本
# 此脚本将帮助您设置 MySQL 数据库和 Redis

set -e

echo "🚀 ProMax 数据库设置脚本"
echo "================================"

# 检查是否安装了 MySQL
if ! command -v mysql &> /dev/null; then
    echo "❌ MySQL 未安装。请先安装 MySQL 8.0+"
    echo "   macOS: brew install mysql"
    echo "   Ubuntu: sudo apt-get install mysql-server"
    echo "   CentOS: sudo yum install mysql-server"
    exit 1
fi

# 检查是否安装了 Redis
if ! command -v redis-cli &> /dev/null; then
    echo "❌ Redis 未安装。请先安装 Redis"
    echo "   macOS: brew install redis"
    echo "   Ubuntu: sudo apt-get install redis-server"
    echo "   CentOS: sudo yum install redis"
    exit 1
fi

# 获取数据库配置
echo ""
echo "📝 请输入数据库配置信息："
read -p "MySQL 主机 (默认: localhost): " DB_HOST
DB_HOST=${DB_HOST:-localhost}

read -p "MySQL 端口 (默认: 3306): " DB_PORT
DB_PORT=${DB_PORT:-3306}

read -p "MySQL 用户名 (默认: root): " DB_USERNAME
DB_USERNAME=${DB_USERNAME:-root}

read -s -p "MySQL 密码: " DB_PASSWORD
echo ""

read -p "数据库名称 (默认: promined): " DB_DATABASE
DB_DATABASE=${DB_DATABASE:-promined}

# 测试 MySQL 连接
echo ""
echo "🔍 测试 MySQL 连接..."
if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "SELECT 1;" &> /dev/null; then
    echo "✅ MySQL 连接成功"
else
    echo "❌ MySQL 连接失败，请检查配置"
    exit 1
fi

# 创建数据库
echo ""
echo "🗄️  创建数据库..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS \`$DB_DATABASE\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
echo "✅ 数据库 '$DB_DATABASE' 创建成功"

# 获取 Redis 配置
echo ""
echo "📝 请输入 Redis 配置信息："
read -p "Redis 主机 (默认: localhost): " REDIS_HOST
REDIS_HOST=${REDIS_HOST:-localhost}

read -p "Redis 端口 (默认: 6379): " REDIS_PORT
REDIS_PORT=${REDIS_PORT:-6379}

read -p "Redis 密码 (可选): " REDIS_PASSWORD

# 测试 Redis 连接
echo ""
echo "🔍 测试 Redis 连接..."
if [ -n "$REDIS_PASSWORD" ]; then
    if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" ping &> /dev/null; then
        echo "✅ Redis 连接成功"
    else
        echo "❌ Redis 连接失败，请检查配置"
        exit 1
    fi
else
    if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping &> /dev/null; then
        echo "✅ Redis 连接成功"
    else
        echo "❌ Redis 连接失败，请检查配置"
        exit 1
    fi
fi

# 创建 .env 文件
echo ""
echo "📄 创建环境配置文件..."
cat > .env << EOF
# 应用配置
NODE_ENV=development
PORT=3003

# 数据库配置 (MySQL)
DB_HOST=$DB_HOST
DB_PORT=$DB_PORT
DB_USERNAME=$DB_USERNAME
DB_PASSWORD=$DB_PASSWORD
DB_DATABASE=$DB_DATABASE

# Redis配置
REDIS_HOST=$REDIS_HOST
REDIS_PORT=$REDIS_PORT
REDIS_PASSWORD=$REDIS_PASSWORD
REDIS_KEY_PREFIX=promined:
REDIS_DEFAULT_EXPIRE=600

# JWT配置
JWT_SECRET=$(openssl rand -base64 32)
JWT_EXPIRES_IN=7d

# 文件上传配置
UPLOAD_MAX_SIZE=100MB
UPLOAD_ALLOWED_TYPES=csv,xlsx,xls,json

# API配置
API_PREFIX=api
API_VERSION=v1

# CORS配置
CORS_ORIGIN=http://localhost:3001
CORS_CREDENTIALS=true
EOF

echo "✅ 环境配置文件 .env 创建成功"

# 安装依赖
echo ""
echo "📦 安装项目依赖..."
if command -v yarn &> /dev/null; then
    yarn install
else
    npm install
fi

# 运行数据库迁移
echo ""
echo "🔄 运行数据库迁移..."
if command -v yarn &> /dev/null; then
    yarn migration:run
else
    npm run migration:run
fi

echo ""
echo "🎉 数据库设置完成！"
echo ""
echo "📋 配置摘要："
echo "   数据库: $DB_DATABASE@$DB_HOST:$DB_PORT"
echo "   Redis: $REDIS_HOST:$REDIS_PORT"
echo ""
echo "🚀 现在可以启动服务器："
echo "   yarn start:dev  或  npm run start:dev"
echo ""
echo "👤 默认管理员账户："
echo "   用户名: admin"
echo "   密码: adminadmin"
echo "   邮箱: <EMAIL>"
echo ""
echo "⚠️  请在生产环境中修改默认密码！"
