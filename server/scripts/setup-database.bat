@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 ProMax 数据库设置脚本
echo ================================
echo.

REM 检查是否安装了 MySQL
mysql --version >nul 2>&1
if errorlevel 1 (
    echo ❌ MySQL 未安装。请先安装 MySQL 8.0+
    echo    下载地址: https://dev.mysql.com/downloads/mysql/
    pause
    exit /b 1
)

REM 检查是否安装了 Redis
redis-cli --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Redis 未安装。请先安装 Redis
    echo    Windows 下载地址: https://github.com/microsoftarchive/redis/releases
    pause
    exit /b 1
)

echo 📝 请输入数据库配置信息：
set /p DB_HOST="MySQL 主机 (默认: localhost): "
if "%DB_HOST%"=="" set DB_HOST=localhost

set /p DB_PORT="MySQL 端口 (默认: 3306): "
if "%DB_PORT%"=="" set DB_PORT=3306

set /p DB_USERNAME="MySQL 用户名 (默认: root): "
if "%DB_USERNAME%"=="" set DB_USERNAME=root

set /p DB_PASSWORD="MySQL 密码: "

set /p DB_DATABASE="数据库名称 (默认: promined): "
if "%DB_DATABASE%"=="" set DB_DATABASE=promined

echo.
echo 🔍 测试 MySQL 连接...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USERNAME% -p%DB_PASSWORD% -e "SELECT 1;" >nul 2>&1
if errorlevel 1 (
    echo ❌ MySQL 连接失败，请检查配置
    pause
    exit /b 1
) else (
    echo ✅ MySQL 连接成功
)

echo.
echo 🗄️  创建数据库...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USERNAME% -p%DB_PASSWORD% -e "CREATE DATABASE IF NOT EXISTS `%DB_DATABASE%` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" >nul 2>&1
echo ✅ 数据库 '%DB_DATABASE%' 创建成功

echo.
echo 📝 请输入 Redis 配置信息：
set /p REDIS_HOST="Redis 主机 (默认: localhost): "
if "%REDIS_HOST%"=="" set REDIS_HOST=localhost

set /p REDIS_PORT="Redis 端口 (默认: 6379): "
if "%REDIS_PORT%"=="" set REDIS_PORT=6379

set /p REDIS_PASSWORD="Redis 密码 (可选): "

echo.
echo 🔍 测试 Redis 连接...
if "%REDIS_PASSWORD%"=="" (
    redis-cli -h %REDIS_HOST% -p %REDIS_PORT% ping >nul 2>&1
) else (
    redis-cli -h %REDIS_HOST% -p %REDIS_PORT% -a %REDIS_PASSWORD% ping >nul 2>&1
)

if errorlevel 1 (
    echo ❌ Redis 连接失败，请检查配置
    pause
    exit /b 1
) else (
    echo ✅ Redis 连接成功
)

echo.
echo 📄 创建环境配置文件...

REM 生成随机 JWT Secret
set JWT_SECRET=
for /l %%i in (1,1,32) do (
    set /a "rand=!random! %% 62"
    if !rand! lss 10 (
        set "char=!rand!"
    ) else if !rand! lss 36 (
        set /a "char=!rand! - 10 + 65"
        for /f %%j in ('cmd /c "echo !char!"') do (
            for /f %%k in ('powershell -command "[char]%%j"') do set "char=%%k"
        )
    ) else (
        set /a "char=!rand! - 36 + 97"
        for /f %%j in ('cmd /c "echo !char!"') do (
            for /f %%k in ('powershell -command "[char]%%j"') do set "char=%%k"
        )
    )
    set "JWT_SECRET=!JWT_SECRET!!char!"
)

(
echo # 应用配置
echo NODE_ENV=development
echo PORT=3003
echo.
echo # 数据库配置 ^(MySQL^)
echo DB_HOST=%DB_HOST%
echo DB_PORT=%DB_PORT%
echo DB_USERNAME=%DB_USERNAME%
echo DB_PASSWORD=%DB_PASSWORD%
echo DB_DATABASE=%DB_DATABASE%
echo.
echo # Redis配置
echo REDIS_HOST=%REDIS_HOST%
echo REDIS_PORT=%REDIS_PORT%
echo REDIS_PASSWORD=%REDIS_PASSWORD%
echo REDIS_KEY_PREFIX=promined:
echo REDIS_DEFAULT_EXPIRE=600
echo.
echo # JWT配置
echo JWT_SECRET=%JWT_SECRET%
echo JWT_EXPIRES_IN=7d
echo.
echo # 文件上传配置
echo UPLOAD_MAX_SIZE=100MB
echo UPLOAD_ALLOWED_TYPES=csv,xlsx,xls,json
echo.
echo # API配置
echo API_PREFIX=api
echo API_VERSION=v1
echo.
echo # CORS配置
echo CORS_ORIGIN=http://localhost:3001
echo CORS_CREDENTIALS=true
) > .env

echo ✅ 环境配置文件 .env 创建成功

echo.
echo 📦 安装项目依赖...
where yarn >nul 2>&1
if errorlevel 1 (
    npm install
) else (
    yarn install
)

echo.
echo 🔄 运行数据库迁移...
where yarn >nul 2>&1
if errorlevel 1 (
    npm run migration:run
) else (
    yarn migration:run
)

echo.
echo 🎉 数据库设置完成！
echo.
echo 📋 配置摘要：
echo    数据库: %DB_DATABASE%@%DB_HOST%:%DB_PORT%
echo    Redis: %REDIS_HOST%:%REDIS_PORT%
echo.
echo 🚀 现在可以启动服务器：
echo    yarn start:dev  或  npm run start:dev
echo.
echo 👤 默认管理员账户：
echo    用户名: admin
echo    密码: adminadmin
echo    邮箱: <EMAIL>
echo.
echo ⚠️  请在生产环境中修改默认密码！
echo.
pause
