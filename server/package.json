{"name": "server", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node --max-old-space-size=8192 --expose-gc dist/main", "start:dev": "node --max-old-space-size=8192 --expose-gc ./node_modules/.bin/nest start --watch", "start:dev-win": "node --max-old-space-size=8192 --expose-gc node_modules/@nestjs/cli/bin/nest.js start --watch", "start:debug": "node --max-old-space-size=8192 --expose-gc ./node_modules/.bin/nest start --debug --watch", "start:prod": "node --max-old-space-size=8192 --expose-gc dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "node scripts/setup-test-db.js setup && jest --config ./test/jest-e2e.json && node scripts/setup-test-db.js cleanup", "test:e2e:setup": "node scripts/setup-test-db.js setup", "test:e2e:cleanup": "node scripts/setup-test-db.js cleanup", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js -d ormconfig.ts", "migration:create": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:create src/database/migrations/$npm_config_name", "migration:generate": "npm run typeorm -- migration:generate src/database/migrations/$npm_config_name", "migration:run": "npm run typeorm -- migration:run", "migration:revert": "npm run typeorm -- migration:revert"}, "dependencies": {"@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cache-manager": "^7.0.0", "cache-manager-ioredis": "^2.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "csv-parser": "^3.2.0", "dotenv": "^16.5.0", "ioredis": "^5.6.1", "moment": "^2.30.1", "multer": "^2.0.1", "mysql2": "^3.14.1", "papaparse": "^5.5.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.25", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcryptjs": "^3.0.0", "@types/body-parser": "^1.19.6", "@types/express": "^5.0.0", "@types/ioredis": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^1.4.13", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^15.14.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "sqlite3": "^5.1.7", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}