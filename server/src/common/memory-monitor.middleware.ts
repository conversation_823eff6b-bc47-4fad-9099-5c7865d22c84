import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class MemoryMonitorMiddleware implements NestMiddleware {
  private readonly logger = new Logger(MemoryMonitorMiddleware.name);
  private readonly memoryThreshold = 6000; // 6GB in MB

  use(req: Request, res: Response, next: NextFunction) {
    const startTime = Date.now();
    const startMemory = process.memoryUsage();

    // 检查请求前的内存使用
    const memoryUsedMB = Math.round(startMemory.heapUsed / 1024 / 1024);

    if (memoryUsedMB > this.memoryThreshold) {
      this.logger.warn(
        `High memory usage detected: ${memoryUsedMB}MB before request ${req.method} ${req.url}`,
      );

      // 强制垃圾回收
      if (global.gc) {
        global.gc();
        const afterGcMemory = process.memoryUsage();
        const afterGcMB = Math.round(afterGcMemory.heapUsed / 1024 / 1024);
        this.logger.log(`Memory after GC: ${afterGcMB}MB`);
      }
    }

    // 监控特定的内存密集型路由
    const isMemoryIntensiveRoute =
      req.url.includes('/subprocess') ||
      req.url.includes('/discovery') ||
      req.url.includes('/analysis');

    if (isMemoryIntensiveRoute) {
      this.logger.log(
        `Starting memory-intensive operation: ${req.method} ${req.url}, Memory: ${memoryUsedMB}MB`,
      );
    }

    // 响应完成后的内存监控
    res.on('finish', () => {
      const endTime = Date.now();
      const endMemory = process.memoryUsage();
      const endMemoryMB = Math.round(endMemory.heapUsed / 1024 / 1024);
      const duration = endTime - startTime;

      if (isMemoryIntensiveRoute || duration > 5000) {
        this.logger.log(
          `Request completed: ${req.method} ${req.url}, ` +
            `Duration: ${duration}ms, ` +
            `Memory: ${Math.round(startMemory.heapUsed / 1024 / 1024)}MB -> ${endMemoryMB}MB, ` +
            `Delta: ${endMemoryMB - Math.round(startMemory.heapUsed / 1024 / 1024)}MB`,
        );
      }

      // 检查内存泄漏
      const memoryIncrease =
        endMemoryMB - Math.round(startMemory.heapUsed / 1024 / 1024);
      if (memoryIncrease > 500) {
        // 500MB增长
        this.logger.warn(
          `Potential memory leak detected: ${memoryIncrease}MB increase during request`,
        );
      }

      // 高内存使用警告
      if (endMemoryMB > this.memoryThreshold) {
        this.logger.error(
          `Critical memory usage: ${endMemoryMB}MB after request ${req.method} ${req.url}`,
        );
      }
    });

    next();
  }

  /**
   * 获取内存使用统计
   */
  static getMemoryStats() {
    const usage = process.memoryUsage();
    return {
      heapUsed: Math.round(usage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(usage.heapTotal / 1024 / 1024),
      external: Math.round(usage.external / 1024 / 1024),
      rss: Math.round(usage.rss / 1024 / 1024),
    };
  }

  /**
   * 强制垃圾回收
   */
  static forceGarbageCollection(): boolean {
    if (global.gc) {
      global.gc();
      return true;
    }
    return false;
  }
}
