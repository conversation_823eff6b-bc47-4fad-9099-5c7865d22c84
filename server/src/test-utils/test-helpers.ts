import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { CacheModule } from '@nestjs/cache-manager';

/**
 * 测试数据库配置
 */
export const getTestDatabaseConfig = () => ({
  type: 'sqlite' as const,
  database: ':memory:',
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  synchronize: true,
  logging: false,
});

/**
 * 测试模块配置
 */
export const getTestModuleConfig = () => ({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env.test',
    }),
    TypeOrmModule.forRoot(getTestDatabaseConfig()),
    PassportModule,
    JwtModule.register({
      secret: 'test-secret',
      signOptions: { expiresIn: '1h' },
    }),
    CacheModule.register({
      isGlobal: true,
    }),
  ],
});

/**
 * 创建测试应用
 */
export async function createTestApp(
  moduleMetadata: any,
): Promise<INestApplication> {
  const moduleFixture: TestingModule =
    await Test.createTestingModule(moduleMetadata).compile();

  const app = moduleFixture.createNestApplication();
  app.setGlobalPrefix('api/v1');

  await app.init();
  return app;
}

/**
 * 清理测试应用
 */
export async function cleanupTestApp(app: INestApplication): Promise<void> {
  if (app) {
    await app.close();
  }
}

/**
 * 生成JWT Token用于测试
 */
export function generateTestToken(
  payload: any = { id: 1, username: 'testuser' },
): string {
  const jwt = require('jsonwebtoken');
  return jwt.sign(payload, 'test-secret', { expiresIn: '1h' });
}

/**
 * 创建认证头部
 */
export function createAuthHeader(token?: string): { Authorization: string } {
  const testToken = token || generateTestToken();
  return {
    Authorization: `Bearer ${testToken}`,
  };
}

/**
 * 延迟函数，用于测试异步操作
 */
export function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * 生成随机字符串
 */
export function generateRandomString(length: number = 10): string {
  const chars =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 生成随机邮箱
 */
export function generateRandomEmail(): string {
  return `test${generateRandomString(8)}@example.com`;
}
