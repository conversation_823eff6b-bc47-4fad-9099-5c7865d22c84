import { User } from '../entities/user.entity';
import { Process, ProcessStatus } from '../entities/process.entity';
import { EventLog } from '../entities/event-log.entity';
import {
  AnalysisResult,
  AnalysisType,
} from '../entities/analysis-result.entity';
import { CreateUserDto, UpdateUserDto } from '../users/dto';
import { CreateProcessDto, UpdateProcessDto } from '../processes/dto';
import { LoginDto, RegisterDto } from '../auth/dto';
import { UploadDataDto } from '../analysis/dto';

/**
 * Mock用户数据
 */
export const mockUsers: Partial<User>[] = [
  {
    id: 1,
    username: 'testuser1',
    email: '<EMAIL>',
    fullName: '测试用户1',
    avatar: 'avatar1.jpg',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: 2,
    username: 'testuser2',
    email: '<EMAIL>',
    fullName: '测试用户2',
    avatar: 'avatar2.jpg',
    isActive: true,
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
  },
];

/**
 * Mock流程数据
 */
export const mockProcesses: Partial<Process>[] = [
  {
    id: 1,
    name: '测试流程1',
    description: '这是一个测试流程',
    status: ProcessStatus.ACTIVE,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    userId: 1,
  },
  {
    id: 2,
    name: '测试流程2',
    description: '这是另一个测试流程',
    status: ProcessStatus.DRAFT,
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
    userId: 2,
  },
];

/**
 * Mock事件日志数据
 */
export const mockEventLogs: Partial<EventLog>[] = [
  {
    id: 1,
    caseId: 'case001',
    activity: '开始',
    timestamp: new Date('2024-01-01T09:00:00'),
    resource: 'user1',
    processId: 1,
  },
  {
    id: 2,
    caseId: 'case001',
    activity: '审核',
    timestamp: new Date('2024-01-01T10:00:00'),
    resource: 'user2',
    processId: 1,
  },
  {
    id: 3,
    caseId: 'case001',
    activity: '完成',
    timestamp: new Date('2024-01-01T11:00:00'),
    resource: 'user1',
    processId: 1,
  },
];

/**
 * Mock分析结果数据
 */
export const mockAnalysisResults: Partial<AnalysisResult>[] = [
  {
    id: 1,
    processId: 1,
    analysisType: AnalysisType.PROCESS_DISCOVERY,
    resultData: {
      nodes: ['开始', '审核', '完成'],
      edges: [
        { from: '开始', to: '审核', frequency: 10 },
        { from: '审核', to: '完成', frequency: 8 },
      ],
    },
    createdAt: new Date('2024-01-01'),
  },
];

/**
 * Mock DTO数据
 */
export const mockCreateUserDto: CreateUserDto = {
  username: 'newuser',
  email: '<EMAIL>',
  password: 'password123',
  fullName: '新用户',
};

export const mockUpdateUserDto: UpdateUserDto = {
  fullName: '更新的用户名',
  avatar: 'new-avatar.jpg',
};

export const mockCreateProcessDto: CreateProcessDto = {
  name: '新流程',
  description: '新流程描述',
};

export const mockUpdateProcessDto: UpdateProcessDto = {
  name: '更新的流程',
  description: '更新的流程描述',
  status: ProcessStatus.ACTIVE,
};

export const mockLoginDto: LoginDto = {
  usernameOrEmail: 'testuser1',
  password: 'password123',
};

export const mockUploadDataDto: UploadDataDto = {
  processId: 1,
  caseIdField: 'case_id',
  activityField: 'activity',
  timestampField: 'timestamp',
  resourceField: 'resource',
};

export const mockRegisterDto: RegisterDto = {
  username: 'newuser',
  email: '<EMAIL>',
  password: 'password123',
  fullName: '新注册用户',
};

/**
 * Mock响应数据
 */
export const mockAuthResponse = {
  access_token: 'mock-jwt-token',
  user: {
    id: 1,
    username: 'testuser1',
    email: '<EMAIL>',
    fullName: '测试用户1',
    avatar: 'avatar1.jpg',
  },
};

export const mockProcessStatistics = {
  totalCases: 100,
  totalActivities: 50,
  avgCaseDuration: 3600000, // 1小时（毫秒）
  mostFrequentActivity: '审核',
  processVariants: 5,
};

export const mockDFGResult = {
  nodes: [
    {
      id: '开始',
      label: '开始',
      frequency: 100,
      avgDuration: 300000, // 5分钟
      minDuration: 60000, // 1分钟
      maxDuration: 900000, // 15分钟
    },
    {
      id: '审核',
      label: '审核',
      frequency: 95,
      avgDuration: 1800000, // 30分钟
      minDuration: 600000, // 10分钟
      maxDuration: 3600000, // 60分钟
    },
    {
      id: '完成',
      label: '完成',
      frequency: 90,
      avgDuration: 180000, // 3分钟
      minDuration: 60000, // 1分钟
      maxDuration: 600000, // 10分钟
    },
  ],
  edges: [
    {
      from: '开始',
      to: '审核',
      frequency: 95,
      avgDuration: 120000, // 2分钟
      minDuration: 30000, // 30秒
      maxDuration: 600000, // 10分钟
    },
    {
      from: '审核',
      to: '完成',
      frequency: 90,
      avgDuration: 60000, // 1分钟
      minDuration: 15000, // 15秒
      maxDuration: 300000, // 5分钟
    },
  ],
};

/**
 * 生成Mock数据的工厂函数
 */
export class MockDataFactory {
  static createUser(overrides: Partial<User> = {}): Partial<User> {
    return {
      id: Math.floor(Math.random() * 1000),
      username: `user${Math.floor(Math.random() * 1000)}`,
      email: `user${Math.floor(Math.random() * 1000)}@example.com`,
      fullName: '测试用户',
      avatar: 'default-avatar.jpg',
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  static createProcess(overrides: Partial<Process> = {}): Partial<Process> {
    return {
      id: Math.floor(Math.random() * 1000),
      name: `流程${Math.floor(Math.random() * 1000)}`,
      description: '测试流程描述',
      status: ProcessStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
      userId: 1,
      ...overrides,
    };
  }

  static createEventLog(overrides: Partial<EventLog> = {}): Partial<EventLog> {
    return {
      id: Math.floor(Math.random() * 1000),
      caseId: `case${Math.floor(Math.random() * 100)}`,
      activity: '测试活动',
      timestamp: new Date(),
      resource: 'testuser',
      processId: 1,
      ...overrides,
    };
  }
}
