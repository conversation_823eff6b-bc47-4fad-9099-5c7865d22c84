import * as fs from 'fs';
import * as path from 'path';

export interface TestEventLog {
  caseId: string;
  activity: string;
  timestamp: Date;
  resource?: string;
  cost?: number;
}

export interface TestScenario {
  name: string;
  description: string;
  conformanceLevel: 'excellent' | 'good' | 'fair' | 'poor';
  expectedScore: number;
  eventLogs: TestEventLog[];
}

export class ConformanceTestDataGenerator {
  private readonly outputDir = path.join(
    __dirname,
    '../../test-data/conformance',
  );

  constructor() {
    // 确保输出目录存在
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }
  }

  /**
   * 生成所有测试数据
   */
  async generateAllTestData(): Promise<void> {
    console.log('开始生成符合性检查测试数据...');

    // 生成BPMN模型文件
    await this.generateBpmnModels();

    // 生成测试场景
    const scenarios = this.generateTestScenarios();

    // 为每个场景生成CSV文件
    for (const scenario of scenarios) {
      await this.generateCsvFile(scenario);
    }

    console.log(`测试数据生成完成，输出目录: ${this.outputDir}`);
  }

  /**
   * 生成BPMN模型文件
   */
  private async generateBpmnModels(): Promise<void> {
    const models = [
      {
        name: 'simple-process.bpmn',
        content: this.generateSimpleProcessBpmn(),
      },
      {
        name: 'complex-process.bpmn',
        content: this.generateComplexProcessBpmn(),
      },
      {
        name: 'parallel-process.bpmn',
        content: this.generateParallelProcessBpmn(),
      },
    ];

    for (const model of models) {
      const filePath = path.join(this.outputDir, model.name);
      fs.writeFileSync(filePath, model.content, 'utf-8');
      console.log(`生成BPMN模型: ${model.name}`);
    }
  }

  /**
   * 生成测试场景
   */
  private generateTestScenarios(): TestScenario[] {
    return [
      this.generateExcellentConformanceScenario(),
      this.generateGoodConformanceScenario(),
      this.generateFairConformanceScenario(),
      this.generatePoorConformanceScenario(),
    ];
  }

  /**
   * 生成完全符合的测试场景
   */
  private generateExcellentConformanceScenario(): TestScenario {
    const eventLogs: TestEventLog[] = [];
    const activities = ['开始', '申请提交', '初审', '复审', '批准', '结束'];

    // 生成100个完全符合的案例
    for (let caseId = 1; caseId <= 100; caseId++) {
      let timestamp = new Date('2024-01-01');

      activities.forEach((activity, index) => {
        timestamp = new Date(
          timestamp.getTime() + Math.random() * 24 * 60 * 60 * 1000,
        ); // 随机间隔

        eventLogs.push({
          caseId: `CASE_${caseId.toString().padStart(3, '0')}`,
          activity,
          timestamp: new Date(timestamp),
          resource: `User_${Math.floor(Math.random() * 5) + 1}`,
          cost: Math.round(Math.random() * 1000 + 100),
        });
      });
    }

    return {
      name: 'excellent-conformance',
      description: '完全符合标准流程的测试数据',
      conformanceLevel: 'excellent',
      expectedScore: 1.0,
      eventLogs,
    };
  }

  /**
   * 生成良好符合的测试场景
   */
  private generateGoodConformanceScenario(): TestScenario {
    const eventLogs: TestEventLog[] = [];
    const activities = ['开始', '申请提交', '初审', '复审', '批准', '结束'];

    // 生成100个案例，其中80%符合，20%有轻微偏差
    for (let caseId = 1; caseId <= 100; caseId++) {
      let timestamp = new Date('2024-01-01');
      const caseActivities = [...activities];

      // 20%的案例有偏差
      if (Math.random() < 0.2) {
        // 随机跳过一个非关键活动
        const skipIndex = Math.floor(Math.random() * 2) + 2; // 跳过初审或复审
        caseActivities.splice(skipIndex, 1);
      }

      caseActivities.forEach((activity, index) => {
        timestamp = new Date(
          timestamp.getTime() + Math.random() * 24 * 60 * 60 * 1000,
        );

        eventLogs.push({
          caseId: `CASE_${caseId.toString().padStart(3, '0')}`,
          activity,
          timestamp: new Date(timestamp),
          resource: `User_${Math.floor(Math.random() * 5) + 1}`,
          cost: Math.round(Math.random() * 1000 + 100),
        });
      });
    }

    return {
      name: 'good-conformance',
      description: '大部分符合标准流程，有少量偏差的测试数据',
      conformanceLevel: 'good',
      expectedScore: 0.8,
      eventLogs,
    };
  }

  /**
   * 生成一般符合的测试场景
   */
  private generateFairConformanceScenario(): TestScenario {
    const eventLogs: TestEventLog[] = [];
    const activities = ['开始', '申请提交', '初审', '复审', '批准', '结束'];
    const extraActivities = ['补充材料', '重新审核', '主管确认'];

    // 生成100个案例，其中50%符合，50%有各种偏差
    for (let caseId = 1; caseId <= 100; caseId++) {
      let timestamp = new Date('2024-01-01');
      const caseActivities = [...activities];

      // 50%的案例有偏差
      if (Math.random() < 0.5) {
        // 随机添加额外活动
        if (Math.random() < 0.3) {
          const extraActivity =
            extraActivities[Math.floor(Math.random() * extraActivities.length)];
          const insertIndex =
            Math.floor(Math.random() * (caseActivities.length - 1)) + 1;
          caseActivities.splice(insertIndex, 0, extraActivity);
        }

        // 随机改变顺序
        if (Math.random() < 0.2) {
          const index1 =
            Math.floor(Math.random() * (caseActivities.length - 2)) + 1;
          const index2 =
            Math.floor(Math.random() * (caseActivities.length - 2)) + 1;
          [caseActivities[index1], caseActivities[index2]] = [
            caseActivities[index2],
            caseActivities[index1],
          ];
        }
      }

      caseActivities.forEach((activity, index) => {
        timestamp = new Date(
          timestamp.getTime() + Math.random() * 24 * 60 * 60 * 1000,
        );

        eventLogs.push({
          caseId: `CASE_${caseId.toString().padStart(3, '0')}`,
          activity,
          timestamp: new Date(timestamp),
          resource: `User_${Math.floor(Math.random() * 5) + 1}`,
          cost: Math.round(Math.random() * 1000 + 100),
        });
      });
    }

    return {
      name: 'fair-conformance',
      description: '中等符合度，有较多偏差的测试数据',
      conformanceLevel: 'fair',
      expectedScore: 0.6,
      eventLogs,
    };
  }

  /**
   * 生成较差符合的测试场景
   */
  private generatePoorConformanceScenario(): TestScenario {
    const eventLogs: TestEventLog[] = [];
    const activities = ['开始', '申请提交', '初审', '复审', '批准', '结束'];
    const extraActivities = [
      '补充材料',
      '重新审核',
      '主管确认',
      '特殊处理',
      '异常处理',
    ];

    // 生成100个案例，大部分都有严重偏差
    for (let caseId = 1; caseId <= 100; caseId++) {
      let timestamp = new Date('2024-01-01');
      let caseActivities = [...activities];

      // 80%的案例有严重偏差
      if (Math.random() < 0.8) {
        // 添加多个额外活动
        const numExtra = Math.floor(Math.random() * 3) + 1;
        for (let i = 0; i < numExtra; i++) {
          const extraActivity =
            extraActivities[Math.floor(Math.random() * extraActivities.length)];
          const insertIndex = Math.floor(Math.random() * caseActivities.length);
          caseActivities.splice(insertIndex, 0, extraActivity);
        }

        // 跳过关键活动
        if (Math.random() < 0.3) {
          const skipIndex = Math.floor(Math.random() * 3) + 2; // 跳过关键活动
          caseActivities.splice(skipIndex, 1);
        }

        // 严重打乱顺序
        if (Math.random() < 0.4) {
          const middlePart = caseActivities.slice(1, -1);
          for (let i = middlePart.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [middlePart[i], middlePart[j]] = [middlePart[j], middlePart[i]];
          }
          caseActivities = [
            caseActivities[0],
            ...middlePart,
            caseActivities[caseActivities.length - 1],
          ];
        }
      }

      caseActivities.forEach((activity, index) => {
        timestamp = new Date(
          timestamp.getTime() + Math.random() * 24 * 60 * 60 * 1000,
        );

        eventLogs.push({
          caseId: `CASE_${caseId.toString().padStart(3, '0')}`,
          activity,
          timestamp: new Date(timestamp),
          resource: `User_${Math.floor(Math.random() * 5) + 1}`,
          cost: Math.round(Math.random() * 1000 + 100),
        });
      });
    }

    return {
      name: 'poor-conformance',
      description: '低符合度，有大量偏差的测试数据',
      conformanceLevel: 'poor',
      expectedScore: 0.3,
      eventLogs,
    };
  }

  /**
   * 生成CSV文件
   */
  private async generateCsvFile(scenario: TestScenario): Promise<void> {
    const csvHeader = 'caseId,activity,timestamp,resource,cost\n';
    const csvRows = scenario.eventLogs
      .map(
        (log) =>
          `${log.caseId},${log.activity},${log.timestamp.toISOString()},${log.resource || ''},${log.cost || ''}`,
      )
      .join('\n');

    const csvContent = csvHeader + csvRows;
    const fileName = `${scenario.name}.csv`;
    const filePath = path.join(this.outputDir, fileName);

    fs.writeFileSync(filePath, csvContent, 'utf-8');
    console.log(
      `生成CSV文件: ${fileName} (${scenario.eventLogs.length} 条记录)`,
    );

    // 同时生成场景描述文件
    const descriptionFile = `${scenario.name}.json`;
    const descriptionPath = path.join(this.outputDir, descriptionFile);
    const description = {
      name: scenario.name,
      description: scenario.description,
      conformanceLevel: scenario.conformanceLevel,
      expectedScore: scenario.expectedScore,
      totalEvents: scenario.eventLogs.length,
      uniqueCases: new Set(scenario.eventLogs.map((log) => log.caseId)).size,
      activities: [...new Set(scenario.eventLogs.map((log) => log.activity))],
      timeRange: {
        start: Math.min(
          ...scenario.eventLogs.map((log) => log.timestamp.getTime()),
        ),
        end: Math.max(
          ...scenario.eventLogs.map((log) => log.timestamp.getTime()),
        ),
      },
    };

    fs.writeFileSync(
      descriptionPath,
      JSON.stringify(description, null, 2),
      'utf-8',
    );
  }

  /**
   * 生成简单流程BPMN模型
   */
  private generateSimpleProcessBpmn(): string {
    return `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                  id="Definitions_1"
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="SimpleProcess" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:task id="Task_1" name="申请提交">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Task_2" name="初审">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Task_3" name="复审">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Task_4" name="批准">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:task>
    <bpmn:endEvent id="EndEvent_1" name="结束">
      <bpmn:incoming>Flow_5</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="Task_1" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_1" targetRef="Task_2" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_2" targetRef="Task_3" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_3" targetRef="Task_4" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_4" targetRef="EndEvent_1" />
  </bpmn:process>
</bpmn:definitions>`;
  }

  /**
   * 生成复杂流程BPMN模型
   */
  private generateComplexProcessBpmn(): string {
    return `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                  id="Definitions_1"
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="ComplexProcess" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:task id="Task_1" name="申请提交">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:task>
    <bpmn:exclusiveGateway id="Gateway_1" name="审核分支">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:task id="Task_2" name="初审">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Task_3" name="快速审核">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:task>
    <bpmn:exclusiveGateway id="Gateway_2" name="合并">
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:task id="Task_4" name="复审">
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:outgoing>Flow_8</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Task_5" name="批准">
      <bpmn:incoming>Flow_8</bpmn:incoming>
      <bpmn:outgoing>Flow_9</bpmn:outgoing>
    </bpmn:task>
    <bpmn:endEvent id="EndEvent_1" name="结束">
      <bpmn:incoming>Flow_9</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="Task_1" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_1" targetRef="Gateway_1" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Gateway_1" targetRef="Task_2" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Gateway_1" targetRef="Task_3" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_2" targetRef="Gateway_2" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_3" targetRef="Gateway_2" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Gateway_2" targetRef="Task_4" />
    <bpmn:sequenceFlow id="Flow_8" sourceRef="Task_4" targetRef="Task_5" />
    <bpmn:sequenceFlow id="Flow_9" sourceRef="Task_5" targetRef="EndEvent_1" />
  </bpmn:process>
</bpmn:definitions>`;
  }

  /**
   * 生成并行流程BPMN模型
   */
  private generateParallelProcessBpmn(): string {
    return `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                  id="Definitions_1"
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="ParallelProcess" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:task id="Task_1" name="申请提交">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:task>
    <bpmn:parallelGateway id="Gateway_1" name="并行分支">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:task id="Task_2" name="技术审核">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Task_3" name="财务审核">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:task>
    <bpmn:parallelGateway id="Gateway_2" name="并行合并">
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_7</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:task id="Task_4" name="最终批准">
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:outgoing>Flow_8</bpmn:outgoing>
    </bpmn:task>
    <bpmn:endEvent id="EndEvent_1" name="结束">
      <bpmn:incoming>Flow_8</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="Task_1" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_1" targetRef="Gateway_1" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Gateway_1" targetRef="Task_2" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Gateway_1" targetRef="Task_3" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_2" targetRef="Gateway_2" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_3" targetRef="Gateway_2" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Gateway_2" targetRef="Task_4" />
    <bpmn:sequenceFlow id="Flow_8" sourceRef="Task_4" targetRef="EndEvent_1" />
  </bpmn:process>
</bpmn:definitions>`;
  }
}
