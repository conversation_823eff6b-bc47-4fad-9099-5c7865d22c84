import { <PERSON>, Get, Post } from '@nestjs/common';
import { AppService } from './app.service';
import { MemoryMonitorMiddleware } from './common/memory-monitor.middleware';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('system/memory')
  getMemoryStats() {
    return {
      memory: MemoryMonitorMiddleware.getMemoryStats(),
      timestamp: new Date().toISOString(),
    };
  }

  @Post('system/gc')
  forceGarbageCollection() {
    const beforeGc = MemoryMonitorMiddleware.getMemoryStats();
    const gcExecuted = MemoryMonitorMiddleware.forceGarbageCollection();
    const afterGc = MemoryMonitorMiddleware.getMemoryStats();

    return {
      gcExecuted,
      before: beforeGc,
      after: afterGc,
      freed: gcExecuted ? beforeGc.heapUsed - afterGc.heapUsed : 0,
      timestamp: new Date().toISOString(),
    };
  }
}
