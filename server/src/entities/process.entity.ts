import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';
import { EventLog } from './event-log.entity';
import { AnalysisResult } from './analysis-result.entity';
import { BpmnModel } from './bpmn-model.entity';
import { ConformanceResult } from './conformance-result.entity';

export enum ProcessStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  ARCHIVED = 'archived',
}

@Entity('processes')
export class Process {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 100 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: ProcessStatus,
    default: ProcessStatus.DRAFT,
  })
  status: ProcessStatus;

  @Column({ length: 50, nullable: true })
  businessDomain: string;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => User, (user) => user.processes, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  userId: number;

  @OneToMany(() => EventLog, (eventLog) => eventLog.process)
  eventLogs: EventLog[];

  @OneToMany(() => AnalysisResult, (analysisResult) => analysisResult.process)
  analysisResults: AnalysisResult[];

  @OneToMany(() => BpmnModel, (bpmnModel) => bpmnModel.process)
  bpmnModels: BpmnModel[];

  @OneToMany(
    () => ConformanceResult,
    (conformanceResult) => conformanceResult.process,
  )
  conformanceResults: ConformanceResult[];

  // 事件日志计数
  eventLogCount?: number;
}
