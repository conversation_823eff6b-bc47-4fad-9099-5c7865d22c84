import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Process } from './process.entity';

@Entity('event_logs')
@Index(['processId', 'caseId'])
@Index(['processId', 'timestamp'])
export class EventLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 100 })
  caseId: string;

  @Column({ length: 100 })
  activity: string;

  @Column({ type: 'timestamp' })
  timestamp: Date;

  @Column({ length: 100, nullable: true })
  resource: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  cost: number;

  @Column({ length: 100, nullable: true })
  activityId: string;

  @Column({ length: 100, nullable: true })
  previousActivity: string;

  @Column({ type: 'timestamp', nullable: true })
  endTimestamp: Date;

  @Column({ type: 'json', nullable: true })
  attributes: Record<string, any>;

  @Column({ length: 100, nullable: true })
  parentCaseId: string;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => Process, (process) => process.eventLogs, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'processId' })
  process: Process;

  @Column()
  processId: number;
}
