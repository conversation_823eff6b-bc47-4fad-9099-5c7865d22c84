import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Exclude, Expose } from 'class-transformer';
import { User } from './user.entity';

export enum DatabaseType {
  MYSQL = 'mysql',
  POSTGRESQL = 'postgresql',
  MSSQL = 'mssql',
  ORACLE = 'oracle',
}

export enum ConnectionStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error',
}

@Entity('database_connections')
@Index(['userId', 'name'])
@Index(['userId', 'status'])
export class DatabaseConnection {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 100 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: DatabaseType,
    default: DatabaseType.MYSQL,
  })
  type: DatabaseType;

  @Column({ length: 255 })
  host: string;

  @Column({ type: 'int', default: 3306 })
  port: number;

  @Column({ length: 100 })
  database: string;

  @Column({ length: 100 })
  username: string;

  @Column({ length: 500 })
  @Exclude()
  encryptedPassword: string;

  @Column({
    type: 'enum',
    enum: ConnectionStatus,
    default: ConnectionStatus.INACTIVE,
  })
  status: ConnectionStatus;

  @Column({ type: 'json', nullable: true })
  options: Record<string, any>;

  @Column({ type: 'timestamp', nullable: true })
  lastTestedAt: Date;

  @Column({ type: 'text', nullable: true })
  lastError: string | null;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  userId: number;

  // 计算属性：连接字符串（不包含密码）
  @Expose()
  get connectionString(): string {
    return `${this.type}://${this.username}@${this.host}:${this.port}/${this.database}`;
  }

  // 计算属性：是否可用
  @Expose()
  get isAvailable(): boolean {
    return this.status === ConnectionStatus.ACTIVE;
  }

  // 计算属性：最后测试时间描述
  @Expose()
  get lastTestedDescription(): string {
    if (!this.lastTestedAt) {
      return '从未测试';
    }

    const now = new Date();
    const diff = now.getTime() - this.lastTestedAt.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}天前`;
    } else if (hours > 0) {
      return `${hours}小时前`;
    } else if (minutes > 0) {
      return `${minutes}分钟前`;
    } else {
      return '刚刚';
    }
  }
}
