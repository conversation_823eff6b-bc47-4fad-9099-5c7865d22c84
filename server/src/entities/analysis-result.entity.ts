import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Process } from './process.entity';

export enum AnalysisType {
  PROCESS_DISCOVERY = 'process_discovery',
  PERFORMANCE_ANALYSIS = 'performance_analysis',
  CONFORMANCE_CHECK = 'conformance_check',
  VARIANT_ANALYSIS = 'variant_analysis',
  SOCIAL_NETWORK = 'social_network',
}

export enum AnalysisStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CACHED = 'cached',
}

@Entity('analysis_results')
@Index(['processId', 'analysisType'])
@Index(['processId', 'analysisType', 'version'])
@Index(['processId', 'analysisType', 'createdAt'])
@Index(['cacheKey'])
export class AnalysisResult {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: AnalysisType,
  })
  analysisType: AnalysisType;

  @Column({
    type: 'enum',
    enum: AnalysisStatus,
    default: AnalysisStatus.PENDING,
  })
  status: AnalysisStatus;

  @Column({ type: 'json' })
  resultData: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  parameters: Record<string, any>;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'int', default: 1 })
  version: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  cacheKey: string | null;

  @Column({ type: 'timestamp', nullable: true })
  expiresAt: Date | null;

  @Column({ type: 'int', default: 3600 })
  cacheTtl: number; // 缓存TTL，单位秒

  @Column({ type: 'varchar', length: 64, nullable: true })
  dataSourceHash: string | null; // 数据源哈希值，用于检测数据变更

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>; // 额外的元数据

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => Process, (process) => process.analysisResults, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'processId' })
  process: Process;

  @Column()
  processId: number;

  // 计算属性：是否已过期
  get isExpired(): boolean {
    if (!this.expiresAt) return false;
    return new Date() > this.expiresAt;
  }

  // 计算属性：是否需要刷新
  get needsRefresh(): boolean {
    return this.isExpired || this.status === AnalysisStatus.FAILED;
  }

  // 生成缓存键
  generateCacheKey(): string {
    return `promined:analysis:${this.processId}:${this.analysisType}:v${this.version}`;
  }

  // 设置过期时间
  setExpiresAt(ttlSeconds?: number): void {
    const ttl = ttlSeconds || this.cacheTtl;
    this.expiresAt = new Date(Date.now() + ttl * 1000);
  }
}
