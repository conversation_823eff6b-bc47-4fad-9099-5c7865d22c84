import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Process } from './process.entity';
import { BpmnModel } from './bpmn-model.entity';

export enum ConformanceStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export enum DeviationType {
  MISSING_ACTIVITY = 'missing_activity', // 缺失活动
  EXTRA_ACTIVITY = 'extra_activity', // 额外活动
  WRONG_ORDER = 'wrong_order', // 错误顺序
  SKIPPED_ACTIVITY = 'skipped_activity', // 跳过活动
  REPEATED_ACTIVITY = 'repeated_activity', // 重复活动
  TIMING_VIOLATION = 'timing_violation', // 时间违规
}

@Entity('conformance_results')
@Index(['processId', 'bpmnModelId'])
@Index(['processId', 'status'])
@Index(['conformanceScore'])
export class ConformanceResult {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: ConformanceStatus,
    default: ConformanceStatus.PENDING,
  })
  status: ConformanceStatus;

  @Column({ type: 'decimal', precision: 5, scale: 4 })
  conformanceScore: number; // 符合性得分 (0-1)

  @Column({ type: 'decimal', precision: 5, scale: 4 })
  fitnessScore: number; // 适应性得分 (0-1)

  @Column({ type: 'decimal', precision: 5, scale: 4 })
  precisionScore: number; // 精确性得分 (0-1)

  @Column({ type: 'decimal', precision: 5, scale: 4 })
  generalizationScore: number; // 泛化性得分 (0-1)

  @Column({ type: 'decimal', precision: 5, scale: 4 })
  simplicityScore: number; // 简洁性得分 (0-1)

  @Column({ type: 'int' })
  totalCases: number; // 总案例数

  @Column({ type: 'int' })
  conformingCases: number; // 符合的案例数

  @Column({ type: 'int' })
  deviatingCases: number; // 偏差的案例数

  @Column({ type: 'json' })
  deviations: Array<{
    caseId: string;
    type: DeviationType;
    description: string;
    activity?: string;
    expectedActivity?: string;
    timestamp?: Date;
    severity: 'low' | 'medium' | 'high';
  }>; // 偏差详情

  @Column({ type: 'json' })
  alignmentResult: Record<string, any>; // 对齐结果

  @Column({ type: 'json', nullable: true })
  caseAnalysis: Array<{
    caseId: string;
    isConforming: boolean;
    deviationCount: number;
    conformanceScore: number;
    trace: string[];
    alignedTrace: string[];
  }>; // 案例级别分析

  @Column({ type: 'json', nullable: true })
  activityAnalysis: Array<{
    activity: string;
    frequency: number;
    conformanceRate: number;
    commonDeviations: string[];
  }>; // 活动级别分析

  @Column({ type: 'json', nullable: true })
  parameters: Record<string, any>; // 分析参数

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  cacheKey: string | null;

  @Column({ type: 'timestamp', nullable: true })
  expiresAt: Date | null;

  @Column({ type: 'int', default: 3600 })
  cacheTtl: number; // 缓存TTL，单位秒

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>; // 额外的元数据

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => Process, (process) => process.conformanceResults, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'processId' })
  process: Process;

  @Column()
  processId: number;

  @ManyToOne(() => BpmnModel, (bpmnModel) => bpmnModel.conformanceResults, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'bpmnModelId' })
  bpmnModel: BpmnModel;

  @Column()
  bpmnModelId: number;

  // 计算属性：是否已过期
  get isExpired(): boolean {
    if (!this.expiresAt) return false;
    return new Date() > this.expiresAt;
  }

  // 计算属性：是否需要刷新
  get needsRefresh(): boolean {
    return this.isExpired || this.status === ConformanceStatus.FAILED;
  }

  // 计算属性：符合性等级
  get conformanceLevel(): 'excellent' | 'good' | 'fair' | 'poor' {
    if (this.conformanceScore >= 0.9) return 'excellent';
    if (this.conformanceScore >= 0.7) return 'good';
    if (this.conformanceScore >= 0.5) return 'fair';
    return 'poor';
  }

  // 计算属性：主要偏差类型
  get majorDeviationTypes(): DeviationType[] {
    const deviationCounts = new Map<DeviationType, number>();

    this.deviations.forEach((deviation) => {
      const count = deviationCounts.get(deviation.type) || 0;
      deviationCounts.set(deviation.type, count + 1);
    });

    return Array.from(deviationCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([type]) => type);
  }

  // 生成缓存键
  generateCacheKey(): string {
    return `promined:conformance:${this.processId}:${this.bpmnModelId}`;
  }

  // 设置过期时间
  setExpiresAt(ttlSeconds?: number): void {
    const ttl = ttlSeconds || this.cacheTtl;
    this.expiresAt = new Date(Date.now() + ttl * 1000);
  }
}
