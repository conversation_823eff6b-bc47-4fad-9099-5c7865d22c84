import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { Expose } from 'class-transformer';
import { Process } from './process.entity';
import { ConformanceResult } from './conformance-result.entity';

export enum BpmnModelType {
  REFERENCE = 'reference', // 参考模型
  DISCOVERED = 'discovered', // 发现的模型
  NORMATIVE = 'normative', // 规范模型
}

export enum BpmnModelStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  ARCHIVED = 'archived',
}

@Entity('bpmn_models')
@Index(['processId', 'modelType'])
@Index(['processId', 'status'])
export class BpmnModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 100 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: BpmnModelType,
    default: BpmnModelType.REFERENCE,
  })
  modelType: BpmnModelType;

  @Column({
    type: 'enum',
    enum: BpmnModelStatus,
    default: BpmnModelStatus.DRAFT,
  })
  status: BpmnModelStatus;

  @Column({ type: 'longtext' })
  bpmnXml: string; // BPMN 2.0 XML内容

  @Column({ type: 'json', nullable: true })
  modelData: Record<string, any>; // 解析后的模型数据

  @Column({ type: 'varchar', length: 64, nullable: true })
  modelHash: string; // 模型内容哈希值

  @Column({ type: 'varchar', length: 100, nullable: true })
  version: string; // 模型版本

  @Column({ type: 'varchar', length: 100, nullable: true })
  author: string; // 模型作者

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>; // 额外的元数据

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => Process, (process) => process.bpmnModels, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'processId' })
  process: Process;

  @Column()
  processId: number;

  @OneToMany(
    () => ConformanceResult,
    (conformanceResult) => conformanceResult.bpmnModel,
  )
  conformanceResults: ConformanceResult[];

  // 计算属性：获取模型活动列表
  @Expose()
  get activities(): string[] {
    if (!this.modelData || !this.modelData.activities) {
      return [];
    }
    return this.modelData.activities;
  }

  // 计算属性：获取模型流程路径
  @Expose()
  get paths(): Array<{ from: string; to: string }> {
    if (!this.modelData || !this.modelData.paths) {
      return [];
    }
    return this.modelData.paths;
  }

  // 计算属性：获取模型验证状态
  @Expose()
  get isValid(): boolean {
    return this.validateBpmnXml();
  }

  // 计算属性：获取验证错误
  @Expose()
  get validationErrors(): string[] {
    if (!this.modelData || !this.modelData.validationErrors) {
      return [];
    }
    return this.modelData.validationErrors;
  }

  // 计算属性：获取验证警告
  @Expose()
  get validationWarnings(): string[] {
    if (!this.modelData || !this.modelData.validationWarnings) {
      return [];
    }
    return this.modelData.validationWarnings;
  }

  // 生成模型哈希值
  generateModelHash(): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(this.bpmnXml).digest('hex');
  }

  // 验证BPMN XML格式
  validateBpmnXml(): boolean {
    try {
      // 简单的XML格式验证
      return (
        this.bpmnXml.includes('<bpmn:') || this.bpmnXml.includes('<bpmn2:')
      );
    } catch {
      return false;
    }
  }
}
