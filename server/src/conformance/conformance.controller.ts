import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  UploadedFile,
  UseInterceptors,
  BadRequestException,
  NotFoundException,
  ParseIntPipe,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiResponse,
  ApiConsumes,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { diskStorage } from 'multer';
import { extname } from 'path';
import * as fs from 'fs';
import { ConformanceService } from './conformance.service';
import { BpmnModelService } from './bpmn-model.service';
import { ConformanceCacheService } from './conformance-cache.service';
import { ProcessMiningService } from '../analysis/process-mining.service';
import {
  ConformanceCheckDto,
  ConformanceCheckResponseDto,
  CreateBpmnModelDto,
  UpdateBpmnModelDto,
  ConformanceResultDto,
} from './dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('符合性检查')
@Controller('conformance')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ConformanceController {
  constructor(
    private readonly conformanceService: ConformanceService,
    private readonly bpmnModelService: BpmnModelService,
    private readonly conformanceCacheService: ConformanceCacheService,
    private readonly processMiningService: ProcessMiningService,
  ) {}

  // BPMN模型管理接口

  @Post('models')
  @ApiOperation({ summary: '创建BPMN模型' })
  @ApiResponse({ status: 201, description: 'BPMN模型创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误或BPMN格式无效' })
  async createBpmnModel(@Body() createDto: CreateBpmnModelDto) {
    return this.bpmnModelService.create(createDto);
  }

  @Post('models/upload')
  @ApiOperation({ summary: '上传BPMN模型文件' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, description: 'BPMN模型上传成功' })
  @ApiResponse({ status: 400, description: '文件格式不支持或BPMN格式无效' })
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads/bpmn',
        filename: (req, file, cb) => {
          const randomName = Array(32)
            .fill(null)
            .map(() => Math.round(Math.random() * 16).toString(16))
            .join('');
          cb(null, `${randomName}${extname(file.originalname)}`);
        },
      }),
      fileFilter: (req, file, cb) => {
        if (
          file.mimetype === 'application/xml' ||
          file.mimetype === 'text/xml' ||
          file.originalname.endsWith('.bpmn') ||
          file.originalname.endsWith('.xml')
        ) {
          cb(null, true);
        } else {
          cb(new BadRequestException('只支持 BPMN/XML 文件'), false);
        }
      },
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
    }),
  )
  async uploadBpmnModel(
    @UploadedFile() file: Express.Multer.File,
    @Body()
    body: {
      name: string;
      description?: string;
      processId: string;
      version?: string;
      author?: string;
    },
  ) {
    if (!file) {
      throw new BadRequestException('请选择要上传的BPMN文件');
    }

    try {
      // 读取文件内容
      const bpmnXml = fs.readFileSync(file.path, 'utf-8');

      // 创建BPMN模型
      const createDto: CreateBpmnModelDto = {
        name: body.name,
        description: body.description,
        processId: parseInt(body.processId),
        bpmnXml,
        version: body.version,
        author: body.author,
        modelType: 'reference' as any,
      };

      const result = await this.bpmnModelService.create(createDto);

      // 删除临时文件
      fs.unlinkSync(file.path);

      return {
        success: true,
        message: 'BPMN模型上传成功',
        model: result,
      };
    } catch (error) {
      // 删除临时文件
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
      throw new BadRequestException(`BPMN模型上传失败: ${error.message}`);
    }
  }

  @Get('models')
  @ApiOperation({ summary: '获取BPMN模型列表' })
  @ApiQuery({
    name: 'processId',
    required: false,
    type: Number,
    description: '流程ID',
  })
  @ApiResponse({ status: 200, description: '获取BPMN模型列表成功' })
  async getBpmnModels(@Query('processId') processId?: number) {
    return this.bpmnModelService.findAll(processId);
  }

  @Get('models/:id')
  @ApiOperation({ summary: '获取BPMN模型详情' })
  @ApiParam({ name: 'id', description: 'BPMN模型ID' })
  @ApiResponse({ status: 200, description: '获取BPMN模型详情成功' })
  @ApiResponse({ status: 404, description: 'BPMN模型不存在' })
  async getBpmnModel(@Param('id', ParseIntPipe) id: number) {
    return this.bpmnModelService.findOne(id);
  }

  @Put('models/:id')
  @ApiOperation({ summary: '更新BPMN模型' })
  @ApiParam({ name: 'id', description: 'BPMN模型ID' })
  @ApiResponse({ status: 200, description: 'BPMN模型更新成功' })
  @ApiResponse({ status: 404, description: 'BPMN模型不存在' })
  async updateBpmnModel(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateBpmnModelDto,
  ) {
    return this.bpmnModelService.update(id, updateDto);
  }

  @Delete('models/:id')
  @ApiOperation({ summary: '删除BPMN模型' })
  @ApiParam({ name: 'id', description: 'BPMN模型ID' })
  @ApiResponse({ status: 200, description: 'BPMN模型删除成功' })
  @ApiResponse({ status: 404, description: 'BPMN模型不存在' })
  async deleteBpmnModel(@Param('id', ParseIntPipe) id: number) {
    await this.bpmnModelService.remove(id);
    return { message: 'BPMN模型删除成功' };
  }

  @Post('models/:id/activate')
  @ApiOperation({ summary: '激活BPMN模型' })
  @ApiParam({ name: 'id', description: 'BPMN模型ID' })
  @ApiResponse({ status: 200, description: 'BPMN模型激活成功' })
  async activateBpmnModel(@Param('id', ParseIntPipe) id: number) {
    return this.bpmnModelService.activate(id);
  }

  @Post('models/:id/archive')
  @ApiOperation({ summary: '归档BPMN模型' })
  @ApiParam({ name: 'id', description: 'BPMN模型ID' })
  @ApiResponse({ status: 200, description: 'BPMN模型归档成功' })
  async archiveBpmnModel(@Param('id', ParseIntPipe) id: number) {
    return this.bpmnModelService.archive(id);
  }

  @Post('models/from-discovery/:processId')
  @ApiOperation({ summary: '从流程发现结果创建BPMN模型' })
  @ApiParam({ name: 'processId', description: '流程ID' })
  @ApiResponse({ status: 201, description: 'BPMN模型创建成功' })
  @ApiResponse({ status: 404, description: '流程不存在或无流程发现结果' })
  async createBpmnModelFromDiscovery(
    @Param('processId', ParseIntPipe) processId: number,
    @Body() body?: { name?: string; description?: string },
  ) {
    try {
      // 获取流程发现结果
      const dfgResult =
        await this.processMiningService.discoverProcess(processId);

      if (!dfgResult) {
        throw new NotFoundException('该流程暂无流程发现结果');
      }

      // 从流程发现结果创建BPMN模型
      const bpmnModel = await this.bpmnModelService.createFromDiscovery(
        processId,
        dfgResult,
        body?.name,
        body?.description,
      );

      return {
        success: true,
        message: '从流程发现结果创建BPMN模型成功',
        model: bpmnModel,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('创建BPMN模型失败: ' + error.message);
    }
  }

  // 符合性检查接口
  @Post('check')
  @ApiOperation({ summary: '执行符合性检查' })
  @ApiResponse({
    status: 201,
    description: '符合性检查已启动',
    type: ConformanceCheckResponseDto,
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 404, description: '流程或BPMN模型不存在' })
  async performConformanceCheck(
    @Body() checkDto: ConformanceCheckDto,
  ): Promise<ConformanceCheckResponseDto> {
    const result =
      await this.conformanceService.performConformanceCheck(checkDto);

    return {
      id: result.id,
      status: result.status,
      conformanceScore: result.conformanceScore,
      fitnessScore: result.fitnessScore,
      precisionScore: result.precisionScore,
      generalizationScore: result.generalizationScore,
      simplicityScore: result.simplicityScore,
      totalCases: result.totalCases,
      conformingCases: result.conformingCases,
      deviatingCases: result.deviatingCases,
      conformanceLevel: result.conformanceLevel,
      majorDeviationTypes: result.majorDeviationTypes,
      deviations: checkDto.parameters?.includeAlignment
        ? result.deviations
        : undefined,
      caseAnalysis: result.caseAnalysis,
      activityAnalysis: result.activityAnalysis,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      fromCache: false,
      expiresAt: result.expiresAt || undefined,
    };
  }

  @Post('reanalyze/:id')
  @ApiOperation({ summary: '重新分析特定的符合性检查结果' })
  @ApiParam({ name: 'id', description: '符合性检查结果ID' })
  @ApiResponse({
    status: 200,
    description: '重新分析已启动',
    type: ConformanceCheckResponseDto,
  })
  @ApiResponse({ status: 404, description: '符合性检查结果不存在' })
  async reanalyzeConformanceResult(
    @Param('id', ParseIntPipe) id: number,
    @Body() reanalyzeDto?: { parameters?: Record<string, any> },
  ): Promise<ConformanceCheckResponseDto> {
    const result = await this.conformanceService.reanalyzeConformanceResult(
      id,
      reanalyzeDto?.parameters,
    );

    return {
      id: result.id,
      status: result.status,
      conformanceScore: result.conformanceScore,
      fitnessScore: result.fitnessScore,
      precisionScore: result.precisionScore,
      generalizationScore: result.generalizationScore,
      simplicityScore: result.simplicityScore,
      totalCases: result.totalCases,
      conformingCases: result.conformingCases,
      deviatingCases: result.deviatingCases,
      conformanceLevel: result.conformanceLevel,
      majorDeviationTypes: result.majorDeviationTypes,
      deviations: reanalyzeDto?.parameters?.includeAlignment
        ? result.deviations
        : undefined,
      caseAnalysis: result.caseAnalysis,
      activityAnalysis: result.activityAnalysis,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      fromCache: false,
      expiresAt: result.expiresAt || undefined,
    };
  }

  @Get('results/:processId/:bpmnModelId')
  @ApiOperation({ summary: '获取符合性检查结果' })
  @ApiParam({ name: 'processId', description: '流程ID' })
  @ApiParam({ name: 'bpmnModelId', description: 'BPMN模型ID' })
  @ApiResponse({
    status: 200,
    description: '获取符合性检查结果成功',
    type: ConformanceResultDto,
  })
  @ApiResponse({ status: 404, description: '符合性检查结果不存在' })
  async getConformanceResult(
    @Param('processId', ParseIntPipe) processId: number,
    @Param('bpmnModelId', ParseIntPipe) bpmnModelId: number,
  ) {
    const result = await this.conformanceService.getConformanceResult(
      processId,
      bpmnModelId,
    );

    if (!result) {
      return null;
    }

    return {
      ...result,
      conformanceLevel: result.conformanceLevel,
      majorDeviationTypes: result.majorDeviationTypes,
      fromCache: !!result.cacheKey,
    };
  }

  @Get('results')
  @ApiOperation({ summary: '分页获取符合性检查结果' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: '页码（默认1）',
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    type: Number,
    description: '每页数量（默认20）',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: '搜索关键词（描述/模型名/流程名/ID）',
  })
  @ApiQuery({
    name: 'processId',
    required: false,
    type: Number,
    description: '流程ID',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: ['pending', 'processing', 'completed', 'failed'],
    description: '检查状态',
  })
  @ApiQuery({
    name: 'level',
    required: false,
    enum: ['excellent', 'good', 'fair', 'poor'],
    description: '符合性等级',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: ['createdAt', 'conformanceScore'],
    description: '排序字段',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['asc', 'desc'],
    description: '排序方向',
  })
  @ApiResponse({ status: 200, description: '获取符合性检查结果成功' })
  async getAllConformanceResults(
    @Query('page') page?: string,
    @Query('pageSize') pageSize?: string,
    @Query('search') search?: string,
    @Query('processId') processId?: string,
    @Query('status') status?: string,
    @Query('level') level?: string,
    @Query('sortBy') sortBy?: 'createdAt' | 'conformanceScore',
    @Query('sortOrder') sortOrder?: 'asc' | 'desc',
  ) {
    const pageNum = page ? parseInt(page, 10) : 1;
    const pageSizeNum = pageSize ? parseInt(pageSize, 10) : 20;
    const processIdNum = processId ? parseInt(processId, 10) : undefined;

    const { data, total } =
      await this.conformanceService.getAllConformanceResultsPaged({
        page: pageNum,
        pageSize: pageSizeNum,
        search,
        processId: processIdNum,
        status: status as any,
        level: level as any,
        sortBy,
        sortOrder,
      });

    return {
      data: data.map((result) => ({
        id: result.id,
        processId: result.processId,
        bpmnModelId: result.bpmnModelId,
        status: result.status,
        conformanceScore: result.conformanceScore,
        fitnessScore: result.fitnessScore,
        precisionScore: result.precisionScore,
        generalizationScore: result.generalizationScore,
        simplicityScore: result.simplicityScore,
        totalCases: result.totalCases,
        conformingCases: result.conformingCases,
        deviatingCases: result.deviatingCases,
        conformanceLevel: result.conformanceLevel,
        majorDeviationTypes: result.majorDeviationTypes,
        description: result.description,
        createdAt: result.createdAt,
        updatedAt: result.updatedAt,
        bpmnModel: result.bpmnModel
          ? { id: result.bpmnModel.id, name: result.bpmnModel.name }
          : null,
        process: result.process
          ? { id: result.process.id, name: result.process.name }
          : null,
      })),
      meta: {
        page: pageNum,
        limit: pageSizeNum,
        total,
        totalPages: Math.ceil(total / pageSizeNum),
      },
    };
  }

  @Get('results_detail/:id')
  @ApiOperation({ summary: '根据ID获取符合性检查结果详情' })
  @ApiParam({ name: 'id', description: '符合性检查结果ID' })
  @ApiResponse({ status: 200, description: '获取符合性检查结果详情成功' })
  @ApiResponse({ status: 404, description: '符合性检查结果不存在' })
  async getConformanceResultById(@Param('id', ParseIntPipe) id: number) {
    const numericId = id;
    if (isNaN(numericId)) {
      throw new BadRequestException('无效的ID格式');
    }
    const result =
      await this.conformanceService.getConformanceResultById(numericId);

    if (!result) {
      throw new NotFoundException('符合性检查结果不存在');
    }

    return {
      ...result,
      conformanceLevel: result.conformanceLevel,
      majorDeviationTypes: result.majorDeviationTypes,
      fromCache: !!result.cacheKey,
    };
  }

  @Get('results/:processId')
  @ApiOperation({ summary: '获取流程的所有符合性检查结果' })
  @ApiParam({ name: 'processId', description: '流程ID' })
  @ApiResponse({ status: 200, description: '获取符合性检查结果列表成功' })
  async getConformanceResults(
    @Param('processId', ParseIntPipe) processId: number,
  ) {
    return this.conformanceService.getConformanceResults(processId);
  }
  @Delete('results/:id')
  @ApiOperation({ summary: '删除符合性检查结果' })
  @ApiParam({ name: 'id', description: '符合性检查结果ID' })
  @ApiResponse({ status: 200, description: '符合性检查结果删除成功' })
  async deleteConformanceResult(@Param('id', ParseIntPipe) id: number) {
    await this.conformanceService.deleteConformanceResult(id);
    return { message: '符合性检查结果删除成功' };
  }

  // 缓存管理接口

  @Get('cache/stats/:processId')
  @ApiOperation({ summary: '获取符合性检查缓存统计' })
  @ApiParam({ name: 'processId', description: '流程ID' })
  @ApiResponse({ status: 200, description: '获取缓存统计成功' })
  async getCacheStats(@Param('processId', ParseIntPipe) processId: number) {
    return this.conformanceCacheService.getCacheStats(processId);
  }

  @Delete('cache/:processId')
  @ApiOperation({ summary: '清除流程的符合性检查缓存' })
  @ApiParam({ name: 'processId', description: '流程ID' })
  @ApiResponse({ status: 200, description: '缓存清除成功' })
  async clearProcessCache(@Param('processId', ParseIntPipe) processId: number) {
    await this.conformanceCacheService.clearProcessCache(processId);
    return { message: '符合性检查缓存已清除' };
  }

  @Delete('cache/:processId/:bpmnModelId')
  @ApiOperation({ summary: '清除特定模型的符合性检查缓存' })
  @ApiParam({ name: 'processId', description: '流程ID' })
  @ApiParam({ name: 'bpmnModelId', description: 'BPMN模型ID' })
  @ApiResponse({ status: 200, description: '缓存清除成功' })
  async clearModelCache(
    @Param('processId', ParseIntPipe) processId: number,
    @Param('bpmnModelId', ParseIntPipe) bpmnModelId: number,
  ) {
    await this.conformanceCacheService.deleteCachedResult(
      processId,
      bpmnModelId,
    );
    return { message: '符合性检查缓存已清除' };
  }

  // 分析和统计接口

  @Get('statistics/global')
  @ApiOperation({ summary: '获取全局符合性检查统计' })
  @ApiResponse({ status: 200, description: '获取全局统计成功' })
  async getGlobalConformanceStatistics() {
    // 获取所有BPMN模型数量
    const totalModels = await this.bpmnModelService.getTotalCount();

    const summary =
      await this.conformanceService.getAllConformanceResultsSummary();

    return {
      totalModels,
      totalChecks: summary.totalChecks,
      averageScore: Number(summary.averageScore.toFixed(4)),
      recentChecks: summary.recentChecks,
    };
  }

  @Get('results_recent')
  @ApiOperation({ summary: '获取最近的符合性检查结果' })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: '返回结果数量限制，默认5',
  })
  @ApiResponse({ status: 200, description: '获取最近结果成功' })
  async getRecentConformanceResults(@Query('limit') limit?: string) {
    const limitNumber = limit ? parseInt(limit, 10) : 5;
    const results =
      await this.conformanceService.getRecentConformanceResults(limitNumber);

    return results.map((result) => ({
      id: result.id,
      processId: result.processId,
      bpmnModelId: result.bpmnModelId,
      conformanceScore: result.conformanceScore,
      conformanceLevel: result.conformanceLevel,
      status: result.status,
      createdAt: result.createdAt,
      totalCases: result.totalCases,
      // 添加模型和流程信息
      bpmnModel: result.bpmnModel
        ? {
            id: result.bpmnModel.id,
            name: result.bpmnModel.name,
          }
        : null,
      process: result.process
        ? {
            id: result.process.id,
            name: result.process.name,
          }
        : null,
    }));
  }

  @Get('analysis/summary/:processId')
  @ApiOperation({ summary: '获取流程符合性分析摘要' })
  @ApiParam({ name: 'processId', description: '流程ID' })
  @ApiResponse({ status: 200, description: '获取分析摘要成功' })
  async getConformanceAnalysisSummary(
    @Param('processId', ParseIntPipe) processId: number,
  ) {
    const results =
      await this.conformanceService.getConformanceResults(processId);
    const models = await this.bpmnModelService.findAll(processId);

    if (results.length === 0) {
      return {
        processId,
        totalModels: models.length,
        totalAnalyses: 0,
        averageConformanceScore: 0,
        bestConformanceScore: 0,
        worstConformanceScore: 0,
        commonDeviationTypes: [],
        analysisHistory: [],
      };
    }

    const conformanceScores = results.map((r) => r.conformanceScore);
    const averageScore =
      conformanceScores.reduce((sum, score) => sum + score, 0) /
      conformanceScores.length;
    const bestScore = Math.max(...conformanceScores);
    const worstScore = Math.min(...conformanceScores);

    // 统计常见偏差类型
    const deviationTypeCount = new Map<string, number>();
    results.forEach((result) => {
      result.majorDeviationTypes.forEach((type) => {
        deviationTypeCount.set(type, (deviationTypeCount.get(type) || 0) + 1);
      });
    });

    const commonDeviationTypes = Array.from(deviationTypeCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([type, count]) => ({ type, count }));

    return {
      processId,
      totalModels: models.length,
      totalAnalyses: results.length,
      averageConformanceScore: Number(averageScore.toFixed(4)),
      bestConformanceScore: Number(bestScore.toFixed(4)),
      worstConformanceScore: Number(worstScore.toFixed(4)),
      commonDeviationTypes,
      analysisHistory: results.slice(0, 10).map((r) => ({
        id: r.id,
        bpmnModelId: r.bpmnModelId,
        conformanceScore: r.conformanceScore,
        status: r.status,
        createdAt: r.createdAt,
      })),
    };
  }

  @Get('analysis/trends/:processId')
  @ApiOperation({ summary: '获取符合性趋势分析' })
  @ApiParam({ name: 'processId', description: '流程ID' })
  @ApiQuery({
    name: 'days',
    required: false,
    type: Number,
    description: '分析天数，默认30天',
  })
  @ApiResponse({ status: 200, description: '获取趋势分析成功' })
  async getConformanceTrends(
    @Param('processId', ParseIntPipe) processId: number,
    @Query('days') days?: string,
  ) {
    const daysNumber = days ? parseInt(days, 10) : 30;
    const results =
      await this.conformanceService.getConformanceResults(processId);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysNumber);

    const recentResults = results.filter((r) => r.createdAt >= cutoffDate);

    if (recentResults.length === 0) {
      return {
        processId,
        period: `${daysNumber}天`,
        dataPoints: [],
        trend: 'stable',
        improvement: 0,
      };
    }

    // 按日期分组
    const dailyScores = new Map<string, number[]>();
    recentResults.forEach((result) => {
      const dateKey = result.createdAt.toISOString().split('T')[0];
      if (!dailyScores.has(dateKey)) {
        dailyScores.set(dateKey, []);
      }
      dailyScores.get(dateKey)!.push(result.conformanceScore);
    });

    // 计算每日平均分
    const dataPoints = Array.from(dailyScores.entries())
      .map(([date, scores]) => ({
        date,
        averageScore:
          scores.reduce((sum, score) => sum + score, 0) / scores.length,
        analysisCount: scores.length,
      }))
      .sort((a, b) => a.date.localeCompare(b.date));

    // 计算趋势
    let trend = 'stable';
    let improvement = 0;
    if (dataPoints.length >= 2) {
      const firstScore = dataPoints[0].averageScore;
      const lastScore = dataPoints[dataPoints.length - 1].averageScore;
      improvement = ((lastScore - firstScore) / firstScore) * 100;

      if (improvement > 5) trend = 'improving';
      else if (improvement < -5) trend = 'declining';
    }

    return {
      processId,
      period: `${daysNumber}天`,
      dataPoints,
      trend,
      improvement: Number(improvement.toFixed(2)),
    };
  }

  @Get('models/:id/validation')
  @ApiOperation({ summary: '验证BPMN模型' })
  @ApiParam({ name: 'id', description: 'BPMN模型ID' })
  @ApiResponse({ status: 200, description: 'BPMN模型验证结果' })
  async validateBpmnModel(@Param('id', ParseIntPipe) id: number) {
    const model = await this.bpmnModelService.findOne(id);

    const validation = {
      isValid: true,
      errors: [] as string[],
      warnings: [] as string[],
      modelInfo: {
        activities: model.activities.length,
        paths: model.paths.length,
        complexity: model.modelData?.complexity || 0,
      },
    };

    // 基本验证
    if (!model.bpmnXml || model.bpmnXml.trim().length === 0) {
      validation.isValid = false;
      validation.errors.push('BPMN XML内容为空');
    }

    if (!model.validateBpmnXml()) {
      validation.isValid = false;
      validation.errors.push('BPMN XML格式无效');
    }

    if (model.activities.length === 0) {
      validation.warnings.push('模型中没有找到任何活动');
    }

    if (model.activities.length > 50) {
      validation.warnings.push('模型复杂度较高，可能影响符合性检查性能');
    }

    // 检查是否有开始和结束事件
    const hasStart = model.activities.some(
      (activity) =>
        activity.toLowerCase().includes('start') ||
        activity.toLowerCase().includes('开始'),
    );
    const hasEnd = model.activities.some(
      (activity) =>
        activity.toLowerCase().includes('end') ||
        activity.toLowerCase().includes('结束'),
    );

    if (!hasStart) {
      validation.warnings.push('建议添加明确的开始事件');
    }
    if (!hasEnd) {
      validation.warnings.push('建议添加明确的结束事件');
    }

    return validation;
  }

  @Post('batch-check')
  @ApiOperation({ summary: '批量执行符合性检查' })
  @ApiResponse({ status: 201, description: '批量符合性检查已启动' })
  async batchConformanceCheck(
    @Body()
    batchDto: {
      processId: number;
      bpmnModelIds: number[];
      parameters?: any;
      forceRefresh?: boolean;
    },
  ) {
    const results: Array<{
      bpmnModelId: number;
      resultId?: number;
      status: string;
      error?: string;
    }> = [];

    for (const bpmnModelId of batchDto.bpmnModelIds) {
      try {
        const checkDto: ConformanceCheckDto = {
          processId: batchDto.processId,
          bpmnModelId,
          parameters: batchDto.parameters,
          forceRefresh: batchDto.forceRefresh,
        };

        const result =
          await this.conformanceService.performConformanceCheck(checkDto);
        results.push({
          bpmnModelId,
          resultId: result.id,
          status: 'started',
        });
      } catch (error) {
        results.push({
          bpmnModelId,
          status: 'failed',
          error: error.message,
        });
      }
    }

    return {
      message: '批量符合性检查已启动',
      results,
      totalRequests: batchDto.bpmnModelIds.length,
      successfulRequests: results.filter((r) => r.status === 'started').length,
    };
  }
}
