import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  BpmnModel,
  BpmnModelStatus,
  BpmnModelType,
} from '../entities/bpmn-model.entity';
import { Process } from '../entities/process.entity';
import { CreateBpmnModelDto, UpdateBpmnModelDto } from './dto/bpmn-model.dto';
import { DFGResult } from '../analysis/process-mining.service';
import * as crypto from 'crypto';

@Injectable()
export class BpmnModelService {
  private readonly logger = new Logger(BpmnModelService.name);

  constructor(
    @InjectRepository(BpmnModel)
    private bpmnModelRepository: Repository<BpmnModel>,
    @InjectRepository(Process)
    private processRepository: Repository<Process>,
  ) {}

  /**
   * 创建BPMN模型
   */
  async create(createDto: CreateBpmnModelDto): Promise<BpmnModel> {
    this.logger.log(`创建BPMN模型: ${createDto.name}`);

    // 验证流程是否存在
    const process = await this.processRepository.findOne({
      where: { id: createDto.processId },
    });
    if (!process) {
      throw new NotFoundException('流程不存在');
    }

    // 验证BPMN XML格式
    if (!this.validateBpmnXml(createDto.bpmnXml)) {
      throw new BadRequestException('无效的BPMN XML格式');
    }

    // 解析BPMN模型数据
    const modelData = await this.parseBpmnXml(createDto.bpmnXml);

    // 生成模型哈希值
    const modelHash = this.generateModelHash(createDto.bpmnXml);

    // 创建BPMN模型实体
    const bpmnModel = this.bpmnModelRepository.create({
      ...createDto,
      modelData,
      modelHash,
      status: createDto.status || BpmnModelStatus.DRAFT,
    });

    return this.bpmnModelRepository.save(bpmnModel);
  }

  /**
   * 获取BPMN模型列表
   */
  async findAll(processId?: number): Promise<BpmnModel[]> {
    const where = processId ? { processId } : {};
    return this.bpmnModelRepository.find({
      where,
      order: { createdAt: 'DESC' },
      relations: ['process'],
    });
  }

  /**
   * 获取BPMN模型总数
   */
  async getTotalCount(): Promise<number> {
    return this.bpmnModelRepository.count();
  }

  /**
   * 根据ID获取BPMN模型
   */
  async findOne(id: number): Promise<BpmnModel> {
    const bpmnModel = await this.bpmnModelRepository.findOne({
      where: { id },
      relations: ['process', 'conformanceResults'],
    });

    if (!bpmnModel) {
      throw new NotFoundException('BPMN模型不存在');
    }

    return bpmnModel;
  }

  /**
   * 更新BPMN模型
   */
  async update(id: number, updateDto: UpdateBpmnModelDto): Promise<BpmnModel> {
    const bpmnModel = await this.findOne(id);

    // 如果更新了BPMN XML，需要重新解析和生成哈希
    if (updateDto.bpmnXml) {
      if (!this.validateBpmnXml(updateDto.bpmnXml)) {
        throw new BadRequestException('无效的BPMN XML格式');
      }

      const modelData = await this.parseBpmnXml(updateDto.bpmnXml);
      const modelHash = this.generateModelHash(updateDto.bpmnXml);

      Object.assign(bpmnModel, updateDto, { modelData, modelHash });
    } else {
      Object.assign(bpmnModel, updateDto);
    }

    return this.bpmnModelRepository.save(bpmnModel);
  }

  /**
   * 删除BPMN模型
   */
  async remove(id: number): Promise<void> {
    const bpmnModel = await this.findOne(id);
    await this.bpmnModelRepository.remove(bpmnModel);
  }

  /**
   * 从流程发现结果创建BPMN模型
   */
  async createFromDiscovery(
    processId: number,
    dfgResult: DFGResult,
    name?: string,
    description?: string,
  ): Promise<BpmnModel> {
    this.logger.log(`从流程发现结果创建BPMN模型: processId=${processId}`);

    // 验证流程是否存在
    const process = await this.processRepository.findOne({
      where: { id: processId },
    });
    if (!process) {
      throw new NotFoundException('流程不存在');
    }

    // 将DFG结果转换为BPMN XML
    const bpmnXml = this.convertDfgToBpmnXml(dfgResult);

    // 解析BPMN模型数据
    const modelData = await this.parseBpmnXml(bpmnXml);

    // 生成模型哈希值
    const modelHash = this.generateModelHash(bpmnXml);

    // 创建BPMN模型实体
    const bpmnModel = this.bpmnModelRepository.create({
      name:
        name ||
        `流程发现模型_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}`,
      description: description || '从流程发现结果自动生成的BPMN模型',
      bpmnXml,
      processId,
      modelType: BpmnModelType.DISCOVERED,
      modelData,
      modelHash,
      status: BpmnModelStatus.ACTIVE,
      author: 'System',
      version: '1.0',
      metadata: {
        source: 'process_discovery',
        generatedAt: new Date().toISOString(),
        dfgStatistics: dfgResult.statistics,
      },
    });

    return this.bpmnModelRepository.save(bpmnModel);
  }

  /**
   * 将DFG结果转换为BPMN XML
   */
  private convertDfgToBpmnXml(dfgResult: DFGResult): string {
    this.logger.log('开始转换DFG到BPMN XML');
    this.logger.log(
      `DFG节点数: ${dfgResult.nodes.length}, 边数: ${dfgResult.edges.length}`,
    );
    this.logger.log(
      'DFG节点:',
      dfgResult.nodes.map((n) => `${n.id}(${n.label})`),
    );
    this.logger.log(
      'DFG边:',
      dfgResult.edges.map((e) => `${e.source}->${e.target}(${e.frequency})`),
    );

    const processId = `Process_${Date.now()}`;
    const startEventId = 'StartEvent_1';
    const endEventId = 'EndEvent_1';

    // 创建节点ID映射
    const nodeIdMap = new Map<string, string>();
    const activityNodes = dfgResult.nodes.filter(
      (node) => !node.isStartNode && !node.isEndNode,
    );

    // 为活动节点创建ID映射
    activityNodes.forEach((node, index) => {
      nodeIdMap.set(node.id, `Task_${index + 1}`);
    });

    // 过滤掉自循环边和无效边
    const validEdges = dfgResult.edges.filter((edge) => {
      // 排除自循环边
      if (edge.source === edge.target) {
        this.logger.log(`跳过自循环边: ${edge.source} -> ${edge.target}`);
        return false;
      }
      // 排除频率为0的边
      if (edge.frequency <= 0) {
        this.logger.log(`跳过零频率边: ${edge.source} -> ${edge.target}`);
        return false;
      }
      return true;
    });

    this.logger.log(`过滤后的有效边数: ${validEdges.length}`);

    // 生成节点XML
    const nodeElements: string[] = [];
    const flowElements: string[] = [];

    // 添加开始事件
    const startOutgoingFlows: string[] = [];
    validEdges.forEach((edge, index) => {
      if (
        edge.source === '开始' ||
        dfgResult.nodes.find((n) => n.id === edge.source)?.isStartNode
      ) {
        startOutgoingFlows.push(`Flow_start_${index}`);
      }
    });

    nodeElements.push(`    <bpmn:startEvent id="${startEventId}" name="开始">
${startOutgoingFlows.map((flow) => `      <bpmn:outgoing>${flow}</bpmn:outgoing>`).join('\n')}
    </bpmn:startEvent>`);

    // 添加结束事件
    const endIncomingFlows: string[] = [];
    validEdges.forEach((edge, index) => {
      if (
        edge.target === '结束' ||
        dfgResult.nodes.find((n) => n.id === edge.target)?.isEndNode
      ) {
        endIncomingFlows.push(`Flow_end_${index}`);
      }
    });

    nodeElements.push(`    <bpmn:endEvent id="${endEventId}" name="结束">
${endIncomingFlows.map((flow) => `      <bpmn:incoming>${flow}</bpmn:incoming>`).join('\n')}
    </bpmn:endEvent>`);

    // 为每个活动节点生成任务元素
    activityNodes.forEach((node) => {
      const taskId = nodeIdMap.get(node.id);
      const incomingFlows: string[] = [];
      const outgoingFlows: string[] = [];

      // 查找输入和输出流
      validEdges.forEach((edge, index) => {
        if (edge.target === node.id) {
          incomingFlows.push(`Flow_${index}`);
        }
        if (edge.source === node.id) {
          outgoingFlows.push(`Flow_${index}`);
        }
      });

      const incomingXml =
        incomingFlows.length > 0
          ? incomingFlows
              .map((flow) => `      <bpmn:incoming>${flow}</bpmn:incoming>`)
              .join('\n')
          : '';
      const outgoingXml =
        outgoingFlows.length > 0
          ? outgoingFlows
              .map((flow) => `      <bpmn:outgoing>${flow}</bpmn:outgoing>`)
              .join('\n')
          : '';

      nodeElements.push(`    <bpmn:userTask id="${taskId}" name="${node.label}">
${incomingXml}
${outgoingXml}
    </bpmn:userTask>`);
    });

    // 生成序列流XML - 只处理有效边
    validEdges.forEach((edge, index) => {
      let flowId = `Flow_${index}`;
      let sourceId: string;
      let targetId: string;

      // 处理开始节点
      if (
        edge.source === '开始' ||
        dfgResult.nodes.find((n) => n.id === edge.source)?.isStartNode
      ) {
        sourceId = startEventId;
        flowId = `Flow_start_${index}`;
      } else {
        sourceId = nodeIdMap.get(edge.source) || edge.source;
      }

      // 处理结束节点
      if (
        edge.target === '结束' ||
        dfgResult.nodes.find((n) => n.id === edge.target)?.isEndNode
      ) {
        targetId = endEventId;
        flowId = `Flow_end_${index}`;
      } else {
        targetId = nodeIdMap.get(edge.target) || edge.target;
      }

      // 只添加有效的流
      if (sourceId && targetId && sourceId !== targetId) {
        flowElements.push(
          `    <bpmn:sequenceFlow id="${flowId}" sourceRef="${sourceId}" targetRef="${targetId}" />`,
        );
      }
    });

    // 构建完整的BPMN XML
    const bpmnXml = `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                  id="Definitions_1"
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="${processId}" isExecutable="true">
${nodeElements.join('\n')}
${flowElements.join('\n')}
  </bpmn:process>
</bpmn:definitions>`;

    return bpmnXml;
  }

  /**
   * 激活BPMN模型
   */
  async activate(id: number): Promise<BpmnModel> {
    const bpmnModel = await this.findOne(id);
    bpmnModel.status = BpmnModelStatus.ACTIVE;
    return this.bpmnModelRepository.save(bpmnModel);
  }

  /**
   * 归档BPMN模型
   */
  async archive(id: number): Promise<BpmnModel> {
    const bpmnModel = await this.findOne(id);
    bpmnModel.status = BpmnModelStatus.ARCHIVED;
    return this.bpmnModelRepository.save(bpmnModel);
  }

  /**
   * 获取流程的活跃BPMN模型
   */
  async getActiveModels(processId: number): Promise<BpmnModel[]> {
    return this.bpmnModelRepository.find({
      where: { processId, status: BpmnModelStatus.ACTIVE },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 验证BPMN XML格式
   */
  private validateBpmnXml(bpmnXml: string): boolean {
    try {
      // 基本的XML格式验证
      if (!bpmnXml || typeof bpmnXml !== 'string') {
        return false;
      }

      // 检查是否包含BPMN命名空间
      const hasBpmnNamespace =
        bpmnXml.includes('xmlns:bpmn') ||
        bpmnXml.includes('xmlns:bpmn2') ||
        bpmnXml.includes('<bpmn:') ||
        bpmnXml.includes('<bpmn2:');

      if (!hasBpmnNamespace) {
        return false;
      }

      // 检查是否包含基本的BPMN元素
      const hasProcess =
        bpmnXml.includes('<bpmn:process') ||
        bpmnXml.includes('<bpmn2:process') ||
        bpmnXml.includes('<process');

      return hasProcess;
    } catch (error) {
      this.logger.error(`BPMN XML验证失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 解析BPMN XML并提取模型数据
   */
  private async parseBpmnXml(bpmnXml: string): Promise<Record<string, any>> {
    try {
      // 这里应该使用专业的BPMN解析库，如bpmn-js或bpmn-moddle
      // 为了简化，这里提供一个基本的解析实现

      const activities = this.extractActivities(bpmnXml);
      const paths = this.extractPaths(bpmnXml);
      const gateways = this.extractGateways(bpmnXml);
      const events = this.extractEvents(bpmnXml);

      return {
        activities,
        paths,
        gateways,
        events,
        elementCount: activities.length + gateways.length + events.length,
        complexity: this.calculateComplexity(activities, paths, gateways),
      };
    } catch (error) {
      this.logger.error(`BPMN XML解析失败: ${error.message}`);
      return {
        activities: [],
        paths: [],
        gateways: [],
        events: [],
        elementCount: 0,
        complexity: 0,
      };
    }
  }

  /**
   * 提取活动列表
   */
  private extractActivities(bpmnXml: string): string[] {
    const activities: string[] = [];

    // 匹配任务元素
    const taskRegex =
      /<bpmn[2]?:(?:task|userTask|serviceTask|scriptTask|businessRuleTask|sendTask|receiveTask|manualTask)[^>]*name="([^"]+)"/g;
    let match: RegExpExecArray | null;

    while ((match = taskRegex.exec(bpmnXml)) !== null) {
      if (match[1] && !activities.includes(match[1])) {
        activities.push(match[1]);
      }
    }

    return activities;
  }

  /**
   * 提取路径关系
   */
  private extractPaths(bpmnXml: string): Array<{ from: string; to: string }> {
    const paths: Array<{ from: string; to: string }> = [];

    // 解析sequenceFlow元素
    const flowRegex =
      /<bpmn[2]?:sequenceFlow[^>]*sourceRef="([^"]+)"[^>]*targetRef="([^"]+)"/g;
    let match: RegExpExecArray | null;

    // 创建ID到名称的映射
    const idToNameMap = new Map<string, string>();

    // 提取所有元素的ID和名称映射
    const elementRegex =
      /<bpmn[2]?:(?:startEvent|endEvent|task|userTask|serviceTask|scriptTask|businessRuleTask|sendTask|receiveTask|manualTask|exclusiveGateway|parallelGateway|inclusiveGateway)[^>]*id="([^"]+)"[^>]*(?:name="([^"]+)")?/g;
    let elementMatch: RegExpExecArray | null;

    while ((elementMatch = elementRegex.exec(bpmnXml)) !== null) {
      const id = elementMatch[1];
      const name = elementMatch[2] || id; // 如果没有名称，使用ID
      idToNameMap.set(id, name);
    }

    // 解析流程连接
    while ((match = flowRegex.exec(bpmnXml)) !== null) {
      const sourceId = match[1];
      const targetId = match[2];

      const sourceName = idToNameMap.get(sourceId) || sourceId;
      const targetName = idToNameMap.get(targetId) || targetId;

      paths.push({
        from: sourceName,
        to: targetName,
      });
    }

    return paths;
  }

  /**
   * 提取网关
   */
  private extractGateways(bpmnXml: string): string[] {
    const gateways: string[] = [];

    const gatewayRegex =
      /<bpmn[2]?:(?:exclusiveGateway|parallelGateway|inclusiveGateway|eventBasedGateway)[^>]*name="([^"]+)"/g;
    let match: RegExpExecArray | null;

    while ((match = gatewayRegex.exec(bpmnXml)) !== null) {
      if (match[1] && !gateways.includes(match[1])) {
        gateways.push(match[1]);
      }
    }

    return gateways;
  }

  /**
   * 提取事件
   */
  private extractEvents(bpmnXml: string): string[] {
    const events: string[] = [];

    const eventRegex =
      /<bpmn[2]?:(?:startEvent|endEvent|intermediateThrowEvent|intermediateCatchEvent|boundaryEvent)[^>]*name="([^"]+)"/g;
    let match: RegExpExecArray | null;

    while ((match = eventRegex.exec(bpmnXml)) !== null) {
      if (match[1] && !events.includes(match[1])) {
        events.push(match[1]);
      }
    }

    return events;
  }

  /**
   * 计算模型复杂度
   */
  private calculateComplexity(
    activities: string[],
    paths: Array<{ from: string; to: string }>,
    gateways: string[],
  ): number {
    // 简单的复杂度计算：活动数 + 路径数 + 网关数 * 2
    return activities.length + paths.length + gateways.length * 2;
  }

  /**
   * 生成模型哈希值
   */
  private generateModelHash(bpmnXml: string): string {
    return crypto.createHash('sha256').update(bpmnXml).digest('hex');
  }
}
