import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConformanceController } from './conformance.controller';
import { ConformanceService } from './conformance.service';
import { BpmnModelService } from './bpmn-model.service';
import { ConformanceAlgorithmService } from './conformance-algorithm.service';
import { ConformanceCacheService } from './conformance-cache.service';
import { ConformanceResult } from '../entities/conformance-result.entity';
import { BpmnModel } from '../entities/bpmn-model.entity';
import { EventLog } from '../entities/event-log.entity';
import { Process } from '../entities/process.entity';
import { AnalysisModule } from '../analysis/analysis.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ConformanceResult, BpmnModel, EventLog, Process]),
    AnalysisModule,
  ],
  controllers: [ConformanceController],
  providers: [
    ConformanceService,
    BpmnModelService,
    ConformanceAlgorithmService,
    ConformanceCacheService,
  ],
  exports: [
    ConformanceService,
    BpmnModelService,
    ConformanceAlgorithmService,
    ConformanceCacheService,
  ],
})
export class ConformanceModule {}
