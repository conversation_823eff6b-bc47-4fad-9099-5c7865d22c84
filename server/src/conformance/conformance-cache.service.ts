import { Injectable, Logger, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { ConformanceResult } from '../entities/conformance-result.entity';
import { EventLog } from '../entities/event-log.entity';
import * as crypto from 'crypto';

@Injectable()
export class ConformanceCacheService {
  private readonly logger = new Logger(ConformanceCacheService.name);

  constructor(
    @InjectRepository(ConformanceResult)
    private conformanceResultRepository: Repository<ConformanceResult>,
    @InjectRepository(EventLog)
    private eventLogRepository: Repository<EventLog>,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
  ) {}

  /**
   * 获取缓存的符合性检查结果
   */
  async getCachedResult(
    processId: number,
    bpmnModelId: number,
  ): Promise<ConformanceResult | null> {
    try {
      // 生成缓存键
      const cacheKey = this.generateCacheKey(processId, bpmnModelId);

      // 首先检查Redis缓存
      const cachedData = await this.cacheManager.get<any>(cacheKey);
      if (cachedData) {
        this.logger.log(`从Redis缓存获取符合性检查结果: ${cacheKey}`);
        return this.deserializeResult(cachedData);
      }

      // 检查数据库中的缓存结果
      const dbResult = await this.conformanceResultRepository.findOne({
        where: { processId, bpmnModelId },
        order: { createdAt: 'DESC' },
      });

      if (dbResult && !dbResult.needsRefresh) {
        // 检查数据源是否有变更
        const currentDataHash = await this.getDataSourceHash(processId);
        if (dbResult.metadata?.dataSourceHash === currentDataHash) {
          this.logger.log(`从数据库获取有效的符合性检查结果: ${dbResult.id}`);

          // 将结果缓存到Redis
          await this.cacheToRedis(cacheKey, dbResult);

          return dbResult;
        } else {
          this.logger.log(`数据源已变更，缓存结果无效: ${dbResult.id}`);
        }
      }

      return null;
    } catch (error) {
      this.logger.error(`获取缓存结果失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 缓存符合性检查结果
   */
  async cacheResult(result: ConformanceResult): Promise<void> {
    try {
      const cacheKey = this.generateCacheKey(
        result.processId,
        result.bpmnModelId,
      );

      // 获取数据源哈希值
      const dataSourceHash = await this.getDataSourceHash(result.processId);

      // 更新结果的元数据
      result.metadata = {
        ...result.metadata,
        dataSourceHash,
        cachedAt: new Date(),
      };

      // 设置缓存键和过期时间
      result.cacheKey = cacheKey;
      result.setExpiresAt();

      // 保存到数据库
      await this.conformanceResultRepository.save(result);

      // 缓存到Redis
      await this.cacheToRedis(cacheKey, result);

      this.logger.log(`符合性检查结果已缓存: ${cacheKey}`);
    } catch (error) {
      this.logger.error(`缓存结果失败: ${error.message}`);
    }
  }

  /**
   * 删除缓存的符合性检查结果
   */
  async deleteCachedResult(
    processId: number,
    bpmnModelId: number,
  ): Promise<void> {
    try {
      const cacheKey = this.generateCacheKey(processId, bpmnModelId);

      // 从Redis删除
      await this.cacheManager.del(cacheKey);

      // 从数据库删除
      await this.conformanceResultRepository.delete({
        processId,
        bpmnModelId,
      });

      this.logger.log(`符合性检查缓存已删除: ${cacheKey}`);
    } catch (error) {
      this.logger.error(`删除缓存失败: ${error.message}`);
    }
  }

  /**
   * 清除流程的所有符合性检查缓存
   */
  async clearProcessCache(processId: number): Promise<void> {
    try {
      // 获取所有相关的缓存键
      const results = await this.conformanceResultRepository.find({
        where: { processId },
        select: ['cacheKey'],
      });

      // 从Redis删除
      const cacheKeys = results
        .map((r) => r.cacheKey)
        .filter((key) => key !== null);

      if (cacheKeys.length > 0) {
        await Promise.all(cacheKeys.map((key) => this.cacheManager.del(key)));
      }

      // 从数据库删除
      await this.conformanceResultRepository.delete({ processId });

      this.logger.log(`流程 ${processId} 的所有符合性检查缓存已清除`);
    } catch (error) {
      this.logger.error(`清除流程缓存失败: ${error.message}`);
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats(processId: number): Promise<{
    totalResults: number;
    cachedResults: number;
    expiredResults: number;
    cacheHitRate: number;
  }> {
    try {
      const results = await this.conformanceResultRepository.find({
        where: { processId },
      });

      const totalResults = results.length;
      const cachedResults = results.filter(
        (r) => r.cacheKey && !r.isExpired,
      ).length;
      const expiredResults = results.filter((r) => r.isExpired).length;
      const cacheHitRate = totalResults > 0 ? cachedResults / totalResults : 0;

      return {
        totalResults,
        cachedResults,
        expiredResults,
        cacheHitRate,
      };
    } catch (error) {
      this.logger.error(`获取缓存统计失败: ${error.message}`);
      return {
        totalResults: 0,
        cachedResults: 0,
        expiredResults: 0,
        cacheHitRate: 0,
      };
    }
  }

  /**
   * 获取数据源哈希值
   */
  async getDataSourceHash(processId: number): Promise<string> {
    try {
      // 获取最新的事件日志数据
      const latestLog = await this.eventLogRepository.findOne({
        where: { processId },
        order: { createdAt: 'DESC' },
      });

      if (!latestLog) {
        return '';
      }

      // 获取事件日志总数和最后更新时间
      const logCount = await this.eventLogRepository.count({
        where: { processId },
      });
      const hashInput = `${processId}-${logCount}-${latestLog.createdAt.getTime()}`;

      return crypto.createHash('md5').update(hashInput).digest('hex');
    } catch (error) {
      this.logger.error(`获取数据源哈希失败: ${error.message}`);
      return '';
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(processId: number, bpmnModelId: number): string {
    return `promined:conformance:${processId}:${bpmnModelId}`;
  }

  /**
   * 缓存到Redis
   */
  private async cacheToRedis(
    cacheKey: string,
    result: ConformanceResult,
  ): Promise<void> {
    try {
      const serializedData = this.serializeResult(result);
      const ttl = result.cacheTtl || 3600; // 默认1小时

      await this.cacheManager.set(cacheKey, serializedData, ttl * 1000);
    } catch (error) {
      this.logger.error(`Redis缓存失败: ${error.message}`);
    }
  }

  /**
   * 序列化结果数据
   */
  private serializeResult(result: ConformanceResult): any {
    return {
      id: result.id,
      status: result.status,
      conformanceScore: result.conformanceScore,
      fitnessScore: result.fitnessScore,
      precisionScore: result.precisionScore,
      generalizationScore: result.generalizationScore,
      simplicityScore: result.simplicityScore,
      totalCases: result.totalCases,
      conformingCases: result.conformingCases,
      deviatingCases: result.deviatingCases,
      deviations: result.deviations,
      alignmentResult: result.alignmentResult,
      caseAnalysis: result.caseAnalysis,
      activityAnalysis: result.activityAnalysis,
      processId: result.processId,
      bpmnModelId: result.bpmnModelId,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      fromCache: true,
    };
  }

  /**
   * 反序列化结果数据
   */
  private deserializeResult(data: any): ConformanceResult {
    const result = new ConformanceResult();
    Object.assign(result, data);
    return result;
  }

  /**
   * 预热缓存 - 为常用的符合性检查结果预加载缓存
   */
  async warmupCache(processId: number): Promise<void> {
    try {
      this.logger.log(`开始预热流程 ${processId} 的符合性检查缓存`);

      // 获取所有已完成的符合性检查结果
      const results = await this.conformanceResultRepository.find({
        where: {
          processId,
          status: 'completed' as any,
        },
        order: { createdAt: 'DESC' },
        take: 10, // 只预热最近的10个结果
      });

      // 将结果缓存到Redis
      const cachePromises = results.map(async (result) => {
        const cacheKey = this.generateCacheKey(
          result.processId,
          result.bpmnModelId,
        );
        await this.cacheToRedis(cacheKey, result);
      });

      await Promise.all(cachePromises);
      this.logger.log(
        `缓存预热完成: 预热了 ${results.length} 个符合性检查结果`,
      );
    } catch (error) {
      this.logger.error(`缓存预热失败: ${error.message}`);
    }
  }

  /**
   * 清理过期缓存
   */
  async cleanupExpiredCache(): Promise<void> {
    try {
      this.logger.log('开始清理过期的符合性检查缓存');

      // 查找所有过期的结果
      const expiredResults = await this.conformanceResultRepository.find({
        where: {
          expiresAt: { $lt: new Date() } as any,
        },
      });

      // 从Redis删除过期的缓存
      const deletePromises = expiredResults.map(async (result) => {
        if (result.cacheKey) {
          await this.cacheManager.del(result.cacheKey);
        }
      });

      await Promise.all(deletePromises);

      // 更新数据库记录，清除过期的缓存键
      if (expiredResults.length > 0) {
        await this.conformanceResultRepository.update(
          { id: { $in: expiredResults.map((r) => r.id) } as any },
          { cacheKey: null, expiresAt: null },
        );
      }

      this.logger.log(
        `过期缓存清理完成: 清理了 ${expiredResults.length} 个过期缓存`,
      );
    } catch (error) {
      this.logger.error(`清理过期缓存失败: ${error.message}`);
    }
  }

  /**
   * 获取缓存使用情况统计
   */
  async getCacheUsageStats(): Promise<{
    totalCachedResults: number;
    activeCacheKeys: number;
    expiredCacheKeys: number;
    cacheHitRate: number;
    averageCacheAge: number;
    topProcessesByCache: Array<{ processId: number; cacheCount: number }>;
  }> {
    try {
      // 获取所有缓存结果
      const allResults = await this.conformanceResultRepository.find({
        where: { cacheKey: { $ne: null } as any },
      });

      const now = new Date();
      const activeCacheKeys = allResults.filter((r) => !r.isExpired).length;
      const expiredCacheKeys = allResults.filter((r) => r.isExpired).length;

      // 计算平均缓存年龄
      const cacheAges = allResults
        .filter((r) => r.metadata?.cachedAt)
        .map((r) => now.getTime() - new Date(r.metadata.cachedAt).getTime());

      const averageCacheAge =
        cacheAges.length > 0
          ? cacheAges.reduce((sum, age) => sum + age, 0) /
            cacheAges.length /
            1000 /
            60 // 转换为分钟
          : 0;

      // 统计各流程的缓存数量
      const processCache = new Map<number, number>();
      allResults.forEach((result) => {
        processCache.set(
          result.processId,
          (processCache.get(result.processId) || 0) + 1,
        );
      });

      const topProcessesByCache = Array.from(processCache.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10)
        .map(([processId, cacheCount]) => ({ processId, cacheCount }));

      // 简化的缓存命中率计算（这里需要实际的访问统计）
      const cacheHitRate =
        activeCacheKeys / (activeCacheKeys + expiredCacheKeys) || 0;

      return {
        totalCachedResults: allResults.length,
        activeCacheKeys,
        expiredCacheKeys,
        cacheHitRate: Number(cacheHitRate.toFixed(4)),
        averageCacheAge: Number(averageCacheAge.toFixed(2)),
        topProcessesByCache,
      };
    } catch (error) {
      this.logger.error(`获取缓存使用统计失败: ${error.message}`);
      return {
        totalCachedResults: 0,
        activeCacheKeys: 0,
        expiredCacheKeys: 0,
        cacheHitRate: 0,
        averageCacheAge: 0,
        topProcessesByCache: [],
      };
    }
  }

  /**
   * 智能缓存策略 - 根据访问频率调整TTL
   */
  async optimizeCacheTtl(
    processId: number,
    bpmnModelId: number,
  ): Promise<number> {
    try {
      // 获取历史访问记录（这里简化实现）
      const results = await this.conformanceResultRepository.find({
        where: { processId, bpmnModelId },
        order: { createdAt: 'DESC' },
        take: 5,
      });

      if (results.length === 0) {
        return 3600; // 默认1小时
      }

      // 计算平均访问间隔
      const intervals: number[] = [];
      for (let i = 1; i < results.length; i++) {
        const interval =
          results[i - 1].createdAt.getTime() - results[i].createdAt.getTime();
        intervals.push(interval);
      }

      if (intervals.length === 0) {
        return 3600;
      }

      const averageInterval =
        intervals.reduce((sum, interval) => sum + interval, 0) /
        intervals.length;
      const averageIntervalSeconds = averageInterval / 1000;

      // 根据访问频率调整TTL
      let optimizedTtl: number;
      if (averageIntervalSeconds < 300) {
        // 5分钟内频繁访问
        optimizedTtl = 7200; // 2小时
      } else if (averageIntervalSeconds < 3600) {
        // 1小时内访问
        optimizedTtl = 3600; // 1小时
      } else if (averageIntervalSeconds < 86400) {
        // 1天内访问
        optimizedTtl = 1800; // 30分钟
      } else {
        optimizedTtl = 900; // 15分钟
      }

      this.logger.log(
        `优化缓存TTL: processId=${processId}, bpmnModelId=${bpmnModelId}, ttl=${optimizedTtl}秒`,
      );
      return optimizedTtl;
    } catch (error) {
      this.logger.error(`优化缓存TTL失败: ${error.message}`);
      return 3600; // 默认值
    }
  }

  /**
   * 批量预加载缓存
   */
  async batchPreloadCache(
    requests: Array<{ processId: number; bpmnModelId: number }>,
  ): Promise<{ preloaded: number; failed: number }> {
    let preloaded = 0;
    let failed = 0;

    const preloadPromises = requests.map(async (request) => {
      try {
        const result = await this.conformanceResultRepository.findOne({
          where: {
            processId: request.processId,
            bpmnModelId: request.bpmnModelId,
            status: 'completed' as any,
          },
          order: { createdAt: 'DESC' },
        });

        if (result && !result.isExpired) {
          const cacheKey = this.generateCacheKey(
            request.processId,
            request.bpmnModelId,
          );
          await this.cacheToRedis(cacheKey, result);
          preloaded++;
        }
      } catch (error) {
        this.logger.error(`预加载缓存失败: ${error.message}`);
        failed++;
      }
    });

    await Promise.all(preloadPromises);

    this.logger.log(`批量预加载缓存完成: 成功=${preloaded}, 失败=${failed}`);
    return { preloaded, failed };
  }
}
