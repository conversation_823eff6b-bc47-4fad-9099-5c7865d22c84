import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  ConformanceResult,
  ConformanceStatus,
  DeviationType,
} from '../entities/conformance-result.entity';
import { BpmnModel } from '../entities/bpmn-model.entity';
import { EventLog } from '../entities/event-log.entity';
import { Process } from '../entities/process.entity';
import { ConformanceCheckDto } from './dto/conformance-check.dto';
import { ConformanceAlgorithmService } from './conformance-algorithm.service';
import { ConformanceCacheService } from './conformance-cache.service';

@Injectable()
export class ConformanceService {
  private readonly logger = new Logger(ConformanceService.name);

  constructor(
    @InjectRepository(ConformanceResult)
    private conformanceResultRepository: Repository<ConformanceResult>,
    @InjectRepository(BpmnModel)
    private bpmnModelRepository: Repository<BpmnModel>,
    @InjectRepository(EventLog)
    private eventLogRepository: Repository<EventLog>,
    @InjectRepository(Process)
    private processRepository: Repository<Process>,
    private conformanceAlgorithmService: ConformanceAlgorithmService,
    private conformanceCacheService: ConformanceCacheService,
  ) {}

  /**
   * 执行符合性检查
   */
  async performConformanceCheck(
    dto: ConformanceCheckDto,
  ): Promise<ConformanceResult> {
    this.logger.log(
      `开始符合性检查: processId=${dto.processId}, bpmnModelId=${dto.bpmnModelId}`,
    );

    // 验证流程和模型是否存在
    await this.validateInputs(dto.processId, dto.bpmnModelId);

    // 检查缓存
    if (!dto.forceRefresh) {
      const cachedResult = await this.conformanceCacheService.getCachedResult(
        dto.processId,
        dto.bpmnModelId,
      );
      if (cachedResult) {
        this.logger.log(`返回缓存的符合性检查结果: ${cachedResult.id}`);
        return cachedResult;
      }
    }

    // 创建新的符合性检查结果记录
    const conformanceResult = this.conformanceResultRepository.create({
      processId: dto.processId,
      bpmnModelId: dto.bpmnModelId,
      status: ConformanceStatus.PROCESSING,
      description: dto.description,
      parameters: dto.parameters,
      conformanceScore: 0,
      fitnessScore: 0,
      precisionScore: 0,
      generalizationScore: 0,
      simplicityScore: 0,
      totalCases: 0,
      conformingCases: 0,
      deviatingCases: 0,
      deviations: [],
      alignmentResult: {},
    });

    const savedResult =
      await this.conformanceResultRepository.save(conformanceResult);

    // 异步执行符合性检查算法
    this.executeConformanceCheckAsync(savedResult.id, dto);

    return savedResult;
  }

  /**
   * 重新分析特定的符合性检查结果
   */
  async reanalyzeConformanceResult(
    resultId: number,
    parameters?: Record<string, any>,
  ): Promise<ConformanceResult> {
    this.logger.log(`开始重新分析符合性检查结果: resultId=${resultId}`);

    // 查找现有结果
    const existingResult = await this.conformanceResultRepository.findOne({
      where: { id: resultId },
    });

    if (!existingResult) {
      throw new Error(`符合性检查结果不存在: ${resultId}`);
    }

    // 重置结果状态和数据
    await this.conformanceResultRepository.update(resultId, {
      status: ConformanceStatus.PROCESSING,
      conformanceScore: 0,
      fitnessScore: 0,
      precisionScore: 0,
      generalizationScore: 0,
      simplicityScore: 0,
      totalCases: 0,
      conformingCases: 0,
      deviatingCases: 0,
      deviations: [],
      alignmentResult: {},
      updatedAt: new Date(),
    });

    // 更新参数
    if (parameters) {
      existingResult.parameters = parameters;
      await this.conformanceResultRepository.save(existingResult);
    }

    // 获取更新后的结果
    const updatedResult = await this.conformanceResultRepository.findOne({
      where: { id: resultId },
    });

    if (!updatedResult) {
      throw new Error('更新后无法找到符合性检查结果');
    }

    // 异步执行符合性检查算法
    const dto = {
      processId: updatedResult.processId,
      bpmnModelId: updatedResult.bpmnModelId,
      parameters: updatedResult.parameters || {},
      description: updatedResult.description,
      forceRefresh: true,
    };

    this.executeConformanceCheckAsync(resultId, dto);

    return updatedResult;
  }

  /**
   * 异步执行符合性检查算法
   */
  private async executeConformanceCheckAsync(
    resultId: number,
    dto: ConformanceCheckDto,
  ): Promise<void> {
    try {
      this.logger.log(`开始异步执行符合性检查算法: resultId=${resultId}`);

      // 获取事件日志数据
      const eventLogs = await this.getEventLogs(
        dto.processId,
        dto.parameters?.caseFilter,
      );

      // 获取BPMN模型
      const bpmnModel = await this.bpmnModelRepository.findOne({
        where: { id: dto.bpmnModelId },
      });

      if (!bpmnModel) {
        throw new NotFoundException('BPMN模型不存在');
      }

      // 执行符合性检查算法
      const algorithmResult =
        await this.conformanceAlgorithmService.performAlignment(
          eventLogs,
          bpmnModel,
          dto.parameters,
        );

      // 计算符合性指标
      const metrics = this.calculateConformanceMetrics(algorithmResult);

      // 更新结果
      await this.conformanceResultRepository.update(resultId, {
        status: ConformanceStatus.COMPLETED,
        ...metrics,
        alignmentResult: algorithmResult.alignmentResult,
        caseAnalysis: dto.parameters?.includeCaseAnalysis
          ? algorithmResult.caseAnalysis
          : undefined,
        activityAnalysis: dto.parameters?.includeActivityAnalysis
          ? algorithmResult.activityAnalysis
          : undefined,
      });

      // 缓存结果
      const updatedResult = await this.conformanceResultRepository.findOne({
        where: { id: resultId },
      });

      if (updatedResult) {
        await this.conformanceCacheService.cacheResult(updatedResult);
      }

      this.logger.log(
        `符合性检查完成: resultId=${resultId}, score=${metrics.conformanceScore}`,
      );
    } catch (error) {
      this.logger.error(
        `符合性检查失败: resultId=${resultId}, error=${error.message}`,
      );

      await this.conformanceResultRepository.update(resultId, {
        status: ConformanceStatus.FAILED,
        description: `检查失败: ${error.message}`,
      });
    }
  }

  /**
   * 获取符合性检查结果
   */
  async getConformanceResult(
    processId: number,
    bpmnModelId: number,
  ): Promise<ConformanceResult | null> {
    return this.conformanceResultRepository.findOne({
      where: { processId, bpmnModelId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 根据ID获取符合性检查结果
   */
  async getConformanceResultById(
    id: number,
  ): Promise<ConformanceResult | null> {
    return this.conformanceResultRepository.findOne({
      where: { id },
      relations: ['bpmnModel', 'process'],
    });
  }

  /**
   * 获取符合性检查结果列表
   */
  async getConformanceResults(processId: number): Promise<ConformanceResult[]> {
    return this.conformanceResultRepository.find({
      where: { processId },
      order: { createdAt: 'DESC' },
      relations: ['bpmnModel'],
    });
  }

  /**
   * 获取符合性检查结果
   */
  async getAllConformanceResultsPaged(params: {
    page?: number;
    pageSize?: number;
    search?: string;
    processId?: number;
    status?: ConformanceStatus;
    level?: 'excellent' | 'good' | 'fair' | 'poor';
    sortBy?: 'createdAt' | 'conformanceScore';
    sortOrder?: 'asc' | 'desc';
  }): Promise<{ data: ConformanceResult[]; total: number }> {
    const {
      page = 1,
      pageSize = 20,
      search,
      processId,
      status,
      level,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = params || {};

    const qb = this.conformanceResultRepository
      .createQueryBuilder('result')
      .leftJoinAndSelect('result.bpmnModel', 'bpmnModel')
      .leftJoinAndSelect('result.process', 'process');

    // 条件筛选
    if (processId) {
      qb.andWhere('result.processId = :processId', { processId });
    }
    if (status) {
      qb.andWhere('result.status = :status', { status });
    }
    if (level) {
      if (level === 'excellent') {
        qb.andWhere('result.conformanceScore >= :min', { min: 0.9 });
      } else if (level === 'good') {
        qb.andWhere(
          'result.conformanceScore >= :min AND result.conformanceScore < :max',
          { min: 0.7, max: 0.9 },
        );
      } else if (level === 'fair') {
        qb.andWhere(
          'result.conformanceScore >= :min AND result.conformanceScore < :max',
          { min: 0.5, max: 0.7 },
        );
      } else if (level === 'poor') {
        qb.andWhere('result.conformanceScore < :max', { max: 0.5 });
      }
    }

    if (search && search.trim() !== '') {
      const q = `%${search.trim()}%`;
      qb.andWhere(
        '(result.description LIKE :q OR bpmnModel.name LIKE :q OR process.name LIKE :q OR CAST(result.id AS CHAR) LIKE :q)',
        { q },
      );
    }

    // 排序
    const sortMap: Record<string, string> = {
      createdAt: 'result.createdAt',
      conformanceScore: 'result.conformanceScore',
    };
    const sortColumn = sortMap[sortBy] || 'result.createdAt';
    qb.orderBy(
      sortColumn,
      (sortOrder || 'desc').toUpperCase() as 'ASC' | 'DESC',
    );

    // 分页
    const skip = (page - 1) * pageSize;
    qb.skip(skip).take(pageSize);

    const [data, total] = await qb.getManyAndCount();
    return { data, total };
  }

  /**
   * 全局统计汇总：总次数、平均分、本月次数
   */
  async getAllConformanceResultsSummary(): Promise<{
    totalChecks: number;
    averageScore: number;
    recentChecks: number;
  }> {
    const totalChecks = await this.conformanceResultRepository.count();

    const avgRaw = await this.conformanceResultRepository
      .createQueryBuilder('r')
      .select('AVG(r.conformanceScore)', 'avg')
      .getRawOne<{ avg: string | number | null }>();

    const averageScore = avgRaw && avgRaw.avg != null ? Number(avgRaw.avg) : 0;

    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const recentChecks = await this.conformanceResultRepository
      .createQueryBuilder('r')
      .where('r.createdAt >= :start', { start: startOfMonth })
      .getCount();

    return { totalChecks, averageScore, recentChecks };
  }

  /**
   * 获取最近的符合性检查结果
   */
  async getRecentConformanceResults(
    limit: number = 5,
  ): Promise<ConformanceResult[]> {
    return this.conformanceResultRepository.find({
      order: { createdAt: 'DESC' },
      take: limit,
      relations: ['bpmnModel', 'process'],
    });
  }

  /**
   * 删除符合性检查结果
   */
  async deleteConformanceResult(id: number): Promise<void> {
    const result = await this.conformanceResultRepository.findOne({
      where: { id },
    });
    if (!result) {
      throw new NotFoundException('符合性检查结果不存在');
    }

    await this.conformanceCacheService.deleteCachedResult(
      result.processId,
      result.bpmnModelId,
    );
    await this.conformanceResultRepository.delete(id);
  }

  /**
   * 验证输入参数
   */
  private async validateInputs(
    processId: number,
    bpmnModelId: number,
  ): Promise<void> {
    const process = await this.processRepository.findOne({
      where: { id: processId },
    });
    if (!process) {
      throw new NotFoundException('流程不存在');
    }

    const bpmnModel = await this.bpmnModelRepository.findOne({
      where: { id: bpmnModelId },
    });
    if (!bpmnModel) {
      throw new NotFoundException('BPMN模型不存在');
    }

    if (bpmnModel.processId !== processId) {
      throw new BadRequestException('BPMN模型不属于指定的流程');
    }
  }

  /**
   * 获取事件日志数据
   */
  private async getEventLogs(
    processId: number,
    caseFilter?: any,
  ): Promise<EventLog[]> {
    const queryBuilder = this.eventLogRepository
      .createQueryBuilder('eventLog')
      .where('eventLog.processId = :processId', { processId })
      .orderBy('eventLog.caseId, eventLog.timestamp');

    // 过滤掉开始节点和结束节点，只保留实际的业务活动
    queryBuilder.andWhere('eventLog.activity NOT IN (:...excludedActivities)', {
      excludedActivities: ['开始', '结束'],
    });

    // 应用过滤条件
    if (caseFilter) {
      if (caseFilter.startDate) {
        queryBuilder.andWhere('eventLog.timestamp >= :startDate', {
          startDate: caseFilter.startDate,
        });
      }
      if (caseFilter.endDate) {
        queryBuilder.andWhere('eventLog.timestamp <= :endDate', {
          endDate: caseFilter.endDate,
        });
      }
      if (caseFilter.includeActivities?.length > 0) {
        queryBuilder.andWhere('eventLog.activity IN (:...includeActivities)', {
          includeActivities: caseFilter.includeActivities,
        });
      }
      if (caseFilter.excludeActivities?.length > 0) {
        queryBuilder.andWhere(
          'eventLog.activity NOT IN (:...excludeActivities)',
          {
            excludeActivities: caseFilter.excludeActivities,
          },
        );
      }
    }

    const eventLogs = await queryBuilder.getMany();

    // 添加详细的调试日志
    const uniqueActivities = [...new Set(eventLogs.map((log) => log.activity))];
    this.logger.log(
      `获取事件日志数据完成: processId=${processId}, 总数=${eventLogs.length} (已过滤开始/结束节点)`,
    );
    this.logger.log(`唯一活动列表: ${JSON.stringify(uniqueActivities)}`);

    return eventLogs;
  }

  /**
   * 计算符合性指标
   */
  private calculateConformanceMetrics(algorithmResult: any): any {
    const totalCases = algorithmResult.caseResults.length;
    const conformingCases = algorithmResult.caseResults.filter(
      (c: any) => c.isConforming,
    ).length;
    const deviatingCases = totalCases - conformingCases;

    const conformanceScore = totalCases > 0 ? conformingCases / totalCases : 0;
    const fitnessScore = algorithmResult.fitnessScore || conformanceScore;
    const precisionScore = algorithmResult.precisionScore || 0.8; // 默认值
    const generalizationScore = algorithmResult.generalizationScore || 0.7; // 默认值
    const simplicityScore = algorithmResult.simplicityScore || 0.9; // 默认值

    return {
      conformanceScore,
      fitnessScore,
      precisionScore,
      generalizationScore,
      simplicityScore,
      totalCases,
      conformingCases,
      deviatingCases,
      deviations: algorithmResult.deviations || [],
    };
  }
}
