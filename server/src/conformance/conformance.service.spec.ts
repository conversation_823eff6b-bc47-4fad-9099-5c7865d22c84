import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConformanceService } from './conformance.service';
import { ConformanceAlgorithmService } from './conformance-algorithm.service';
import { ConformanceCacheService } from './conformance-cache.service';
import { EventLog } from '../entities/event-log.entity';
import { ConformanceResult } from '../entities/conformance-result.entity';
import { BpmnModel } from '../entities/bpmn-model.entity';
import { Process } from '../entities/process.entity';

describe('ConformanceService', () => {
  let service: ConformanceService;
  let eventLogRepository: Repository<EventLog>;
  let conformanceResultRepository: Repository<ConformanceResult>;
  let bpmnModelRepository: Repository<BpmnModel>;
  let processRepository: Repository<Process>;
  let conformanceAlgorithmService: ConformanceAlgorithmService;
  let conformanceCacheService: ConformanceCacheService;

  const mockEventLogRepository = {
    find: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
    })),
  };

  const mockConformanceResultRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  const mockBpmnModelRepository = {
    findOne: jest.fn(),
  };

  const mockProcessRepository = {
    findOne: jest.fn(),
  };

  const mockConformanceAlgorithmService = {
    performAlignment: jest.fn(),
  };

  const mockConformanceCacheService = {
    getCachedResult: jest.fn(),
    cacheResult: jest.fn(),
    deleteCachedResult: jest.fn(),
  };

  const mockEventLogs = [
    {
      id: 1,
      processId: 1,
      caseId: 'case1',
      activity: 'Start',
      timestamp: new Date('2024-01-01T10:00:00Z'),
      resource: 'User1',
    },
    {
      id: 2,
      processId: 1,
      caseId: 'case1',
      activity: 'Task A',
      timestamp: new Date('2024-01-01T10:30:00Z'),
      resource: 'User1',
    },
    {
      id: 3,
      processId: 1,
      caseId: 'case1',
      activity: 'End',
      timestamp: new Date('2024-01-01T11:00:00Z'),
      resource: 'User1',
    },
  ];

  const mockBpmnModel = {
    id: 1,
    processId: 1,
    name: 'Test Model',
    bpmnXml: `<?xml version="1.0" encoding="UTF-8"?>
      <definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL">
        <process id="process1">
          <startEvent id="start" />
          <task id="taskA" name="Task A" />
          <endEvent id="end" />
          <sequenceFlow id="flow1" sourceRef="start" targetRef="taskA" />
          <sequenceFlow id="flow2" sourceRef="taskA" targetRef="end" />
        </process>
      </definitions>`,
    version: 1,
    createdAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ConformanceService,
        {
          provide: getRepositoryToken(EventLog),
          useValue: mockEventLogRepository,
        },
        {
          provide: getRepositoryToken(ConformanceResult),
          useValue: mockConformanceResultRepository,
        },
        {
          provide: getRepositoryToken(BpmnModel),
          useValue: mockBpmnModelRepository,
        },
        {
          provide: getRepositoryToken(Process),
          useValue: mockProcessRepository,
        },
        {
          provide: ConformanceAlgorithmService,
          useValue: mockConformanceAlgorithmService,
        },
        {
          provide: ConformanceCacheService,
          useValue: mockConformanceCacheService,
        },
      ],
    }).compile();

    service = module.get<ConformanceService>(ConformanceService);
    eventLogRepository = module.get<Repository<EventLog>>(
      getRepositoryToken(EventLog),
    );
    conformanceResultRepository = module.get<Repository<ConformanceResult>>(
      getRepositoryToken(ConformanceResult),
    );
    bpmnModelRepository = module.get<Repository<BpmnModel>>(
      getRepositoryToken(BpmnModel),
    );
    processRepository = module.get<Repository<Process>>(
      getRepositoryToken(Process),
    );
    conformanceAlgorithmService = module.get<ConformanceAlgorithmService>(
      ConformanceAlgorithmService,
    );
    conformanceCacheService = module.get<ConformanceCacheService>(
      ConformanceCacheService,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('performConformanceCheck', () => {
    it('should perform conformance check successfully', async () => {
      const dto = {
        processId: 1,
        bpmnModelId: 1,
        description: '测试符合性检查',
        forceRefresh: false,
      };

      const mockResult = {
        id: 1,
        processId: 1,
        bpmnModelId: 1,
        status: 'processing',
        conformanceScore: 0,
        fitnessScore: 0,
        precisionScore: 0,
        generalizationScore: 0,
        simplicityScore: 0,
        totalCases: 0,
        conformingCases: 0,
        deviatingCases: 0,
        deviations: [],
        alignmentResult: {},
      };

      mockProcessRepository.findOne.mockResolvedValue({ id: 1 });
      mockBpmnModelRepository.findOne.mockResolvedValue(mockBpmnModel);
      mockConformanceCacheService.getCachedResult.mockResolvedValue(null);
      mockConformanceResultRepository.create.mockReturnValue(mockResult);
      mockConformanceResultRepository.save.mockResolvedValue(mockResult);

      const result = await service.performConformanceCheck(dto);

      expect(result).toEqual(mockResult);
      expect(mockConformanceResultRepository.save).toHaveBeenCalled();
    });

    it('should return cached result when available', async () => {
      const dto = {
        processId: 1,
        bpmnModelId: 1,
        description: '测试符合性检查',
        forceRefresh: false,
      };

      const cachedResult = {
        id: 1,
        processId: 1,
        bpmnModelId: 1,
        status: 'completed',
        conformanceScore: 0.95,
        fitnessScore: 0.95,
        precisionScore: 0.9,
        generalizationScore: 0.8,
        simplicityScore: 0.9,
        totalCases: 100,
        conformingCases: 95,
        deviatingCases: 5,
        deviations: [],
        alignmentResult: {},
      };

      mockProcessRepository.findOne.mockResolvedValue({ id: 1 });
      mockBpmnModelRepository.findOne.mockResolvedValue(mockBpmnModel);
      mockConformanceCacheService.getCachedResult.mockResolvedValue(
        cachedResult,
      );

      const result = await service.performConformanceCheck(dto);

      expect(result).toEqual(cachedResult);
      expect(mockConformanceResultRepository.save).not.toHaveBeenCalled();
    });

    it('should throw error if process not found', async () => {
      const dto = {
        processId: 999,
        bpmnModelId: 1,
        description: '测试符合性检查',
        forceRefresh: false,
      };

      mockProcessRepository.findOne.mockResolvedValue(null);

      await expect(service.performConformanceCheck(dto)).rejects.toThrow(
        '流程不存在',
      );
    });

    it('should throw error if BPMN model not found', async () => {
      const dto = {
        processId: 1,
        bpmnModelId: 999,
        description: '测试符合性检查',
        forceRefresh: false,
      };

      mockProcessRepository.findOne.mockResolvedValue({ id: 1 });
      mockBpmnModelRepository.findOne.mockResolvedValue(null);

      await expect(service.performConformanceCheck(dto)).rejects.toThrow(
        'BPMN模型不存在',
      );
    });
  });

  describe('getConformanceResult', () => {
    it('should get conformance result by processId and bpmnModelId', async () => {
      const processId = 1;
      const bpmnModelId = 1;
      const mockResult = {
        id: 1,
        processId: 1,
        bpmnModelId: 1,
        conformanceScore: 0.95,
        fitnessScore: 0.95,
        precisionScore: 0.9,
        generalizationScore: 0.8,
        simplicityScore: 0.9,
        totalCases: 100,
        conformingCases: 95,
        deviatingCases: 5,
        deviations: [],
        alignmentResult: {},
        createdAt: new Date(),
      };

      mockConformanceResultRepository.findOne.mockResolvedValue(mockResult);

      const result = await service.getConformanceResult(processId, bpmnModelId);

      expect(result).toEqual(mockResult);
      expect(mockConformanceResultRepository.findOne).toHaveBeenCalledWith({
        where: { processId, bpmnModelId },
        order: { createdAt: 'DESC' },
      });
    });

    it('should return null if result not found', async () => {
      const processId = 999;
      const bpmnModelId = 999;

      mockConformanceResultRepository.findOne.mockResolvedValue(null);

      const result = await service.getConformanceResult(processId, bpmnModelId);

      expect(result).toBeNull();
    });
  });

  describe('getConformanceResults', () => {
    it('should get conformance results for process', async () => {
      const processId = 1;
      const mockResults = [
        {
          id: 1,
          processId,
          bpmnModelId: 1,
          conformanceScore: 0.95,
          fitnessScore: 0.95,
          precisionScore: 0.9,
          generalizationScore: 0.8,
          simplicityScore: 0.9,
          totalCases: 100,
          conformingCases: 95,
          deviatingCases: 5,
          deviations: [],
          alignmentResult: {},
          createdAt: new Date(),
        },
        {
          id: 2,
          processId,
          bpmnModelId: 1,
          conformanceScore: 0.9,
          fitnessScore: 0.9,
          precisionScore: 0.85,
          generalizationScore: 0.75,
          simplicityScore: 0.85,
          totalCases: 100,
          conformingCases: 90,
          deviatingCases: 10,
          deviations: [],
          alignmentResult: {},
          createdAt: new Date(),
        },
      ];

      mockConformanceResultRepository.find.mockResolvedValue(mockResults);

      const result = await service.getConformanceResults(processId);

      expect(result).toEqual(mockResults);
      expect(mockConformanceResultRepository.find).toHaveBeenCalledWith({
        where: { processId },
        order: { createdAt: 'DESC' },
        relations: ['bpmnModel'],
      });
    });
  });

  describe('deleteConformanceResult', () => {
    it('should delete conformance result successfully', async () => {
      const resultId = 1;
      const mockResult = {
        id: 1,
        processId: 1,
        bpmnModelId: 1,
        conformanceScore: 0.95,
        fitnessScore: 0.95,
        precisionScore: 0.9,
        generalizationScore: 0.8,
        simplicityScore: 0.9,
        totalCases: 100,
        conformingCases: 95,
        deviatingCases: 5,
        deviations: [],
        alignmentResult: {},
      };

      mockConformanceResultRepository.findOne.mockResolvedValue(mockResult);
      mockConformanceCacheService.deleteCachedResult.mockResolvedValue(
        undefined,
      );
      mockConformanceResultRepository.delete.mockResolvedValue({ affected: 1 });

      await service.deleteConformanceResult(resultId);

      expect(mockConformanceResultRepository.findOne).toHaveBeenCalledWith({
        where: { id: resultId },
      });
      expect(
        mockConformanceCacheService.deleteCachedResult,
      ).toHaveBeenCalledWith(mockResult.processId, mockResult.bpmnModelId);
      expect(mockConformanceResultRepository.delete).toHaveBeenCalledWith(
        resultId,
      );
    });

    it('should throw error if result not found', async () => {
      const resultId = 999;

      mockConformanceResultRepository.findOne.mockResolvedValue(null);

      await expect(service.deleteConformanceResult(resultId)).rejects.toThrow(
        '符合性检查结果不存在',
      );
    });
  });

  describe('getEventLogs', () => {
    it('should filter out start and end nodes from event logs', async () => {
      const processId = 1;
      const mockEventLogs = [
        { id: 1, activity: '开始', caseId: 'case1', timestamp: new Date() },
        { id: 2, activity: '申请提交', caseId: 'case1', timestamp: new Date() },
        { id: 3, activity: '初审', caseId: 'case1', timestamp: new Date() },
        { id: 4, activity: '结束', caseId: 'case1', timestamp: new Date() },
        { id: 5, activity: '开始', caseId: 'case2', timestamp: new Date() },
        { id: 6, activity: '复审', caseId: 'case2', timestamp: new Date() },
        { id: 7, activity: '结束', caseId: 'case2', timestamp: new Date() },
      ];

      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getMany: jest
          .fn()
          .mockResolvedValue(
            mockEventLogs.filter(
              (log) => !['开始', '结束'].includes(log.activity),
            ),
          ),
      };

      mockEventLogRepository.createQueryBuilder.mockReturnValue(
        mockQueryBuilder,
      );

      // 使用反射来访问私有方法
      const getEventLogs = (service as any).getEventLogs.bind(service);
      const result = await getEventLogs(processId);

      // 验证查询构建器被正确调用
      expect(mockEventLogRepository.createQueryBuilder).toHaveBeenCalledWith(
        'eventLog',
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'eventLog.processId = :processId',
        { processId },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'eventLog.activity NOT IN (:...excludedActivities)',
        { excludedActivities: ['开始', '结束'] },
      );
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        'eventLog.caseId, eventLog.timestamp',
      );

      // 验证结果只包含业务活动，不包含开始和结束节点
      expect(result).toHaveLength(3);
      expect(result.map((log) => log.activity)).toEqual([
        '申请提交',
        '初审',
        '复审',
      ]);
      expect(result.some((log) => log.activity === '开始')).toBe(false);
      expect(result.some((log) => log.activity === '结束')).toBe(false);
    });

    it('should apply additional filters while still filtering start/end nodes', async () => {
      const processId = 1;
      const caseFilter = {
        includeActivities: ['申请提交', '初审'],
      };

      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([
          {
            id: 2,
            activity: '申请提交',
            caseId: 'case1',
            timestamp: new Date(),
          },
          { id: 3, activity: '初审', caseId: 'case1', timestamp: new Date() },
        ]),
      };

      mockEventLogRepository.createQueryBuilder.mockReturnValue(
        mockQueryBuilder,
      );

      // 使用反射来访问私有方法
      const getEventLogs = (service as any).getEventLogs.bind(service);
      const result = await getEventLogs(processId, caseFilter);

      // 验证开始/结束节点过滤器被应用
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'eventLog.activity NOT IN (:...excludedActivities)',
        { excludedActivities: ['开始', '结束'] },
      );

      // 验证额外的过滤条件也被应用
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'eventLog.activity IN (:...includeActivities)',
        { includeActivities: ['申请提交', '初审'] },
      );

      expect(result).toHaveLength(2);
    });
  });
});
