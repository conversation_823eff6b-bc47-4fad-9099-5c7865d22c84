import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BpmnModelService } from './bpmn-model.service';
import { BpmnModel } from '../entities/bpmn-model.entity';
import { CreateBpmnModelDto, UpdateBpmnModelDto } from './dto';

describe('BpmnModelService', () => {
  let service: BpmnModelService;
  let repository: Repository<BpmnModel>;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getManyAndCount: jest.fn(),
      getMany: jest.fn(),
    })),
  };

  const mockBpmnModel = {
    id: 1,
    processId: 1,
    name: 'Test Model',
    description: 'Test Description',
    bpmnXml: `<?xml version="1.0" encoding="UTF-8"?>
      <definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL">
        <process id="process1">
          <startEvent id="start" />
          <task id="taskA" name="Task A" />
          <endEvent id="end" />
        </process>
      </definitions>`,
    version: 1,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BpmnModelService,
        {
          provide: getRepositoryToken(BpmnModel),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<BpmnModelService>(BpmnModelService);
    repository = module.get<Repository<BpmnModel>>(
      getRepositoryToken(BpmnModel),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new BPMN model', async () => {
      const createDto: CreateBpmnModelDto = {
        processId: 1,
        name: 'New Model',
        description: 'New Description',
        bpmnXml: '<definitions></definitions>',
      };

      const newModel = { ...mockBpmnModel, ...createDto };
      mockRepository.create.mockReturnValue(newModel);
      mockRepository.save.mockResolvedValue(newModel);

      const result = await service.create(createDto);

      expect(mockRepository.create).toHaveBeenCalledWith(createDto);
      expect(mockRepository.save).toHaveBeenCalledWith(newModel);
      expect(result).toEqual(newModel);
    });

    it('should validate BPMN XML format', async () => {
      const createDto: CreateBpmnModelDto = {
        processId: 1,
        name: 'Invalid Model',
        bpmnXml: 'invalid xml',
      };

      await expect(service.create(createDto)).rejects.toThrow(
        'BPMN XML格式无效',
      );
    });
  });

  describe('findAll', () => {
    it('should return paginated BPMN models for process', async () => {
      const processId = 1;
      const page = 1;
      const limit = 10;
      const models = [mockBpmnModel];
      const total = 1;

      mockRepository
        .createQueryBuilder()
        .getManyAndCount.mockResolvedValue([models, total]);

      const result = await service.findAll(processId, page, limit);

      expect(result).toEqual({
        data: models,
        total,
        page,
        limit,
        totalPages: 1,
      });
    });

    it('should filter by active status', async () => {
      const processId = 1;
      const page = 1;
      const limit = 10;
      const isActive = true;
      const models = [mockBpmnModel];

      mockRepository
        .createQueryBuilder()
        .getManyAndCount.mockResolvedValue([models, 1]);

      const result = await service.findAll(processId, page, limit, isActive);

      expect(result.data).toEqual(models);
    });
  });

  describe('findOne', () => {
    it('should return a BPMN model by id', async () => {
      const id = 1;

      mockRepository.findOne.mockResolvedValue(mockBpmnModel);

      const result = await service.findOne(id);

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id },
        relations: ['process'],
      });
      expect(result).toEqual(mockBpmnModel);
    });

    it('should return null if model not found', async () => {
      const id = 999;

      mockRepository.findOne.mockResolvedValue(null);

      const result = await service.findOne(id);

      expect(result).toBeNull();
    });
  });

  describe('update', () => {
    it('should update a BPMN model', async () => {
      const id = 1;
      const updateDto: UpdateBpmnModelDto = {
        name: 'Updated Model',
        description: 'Updated Description',
      };

      const updatedModel = { ...mockBpmnModel, ...updateDto };
      mockRepository.findOne.mockResolvedValue(mockBpmnModel);
      mockRepository.save.mockResolvedValue(updatedModel);

      const result = await service.update(id, updateDto);

      expect(mockRepository.findOne).toHaveBeenCalledWith({ where: { id } });
      expect(mockRepository.save).toHaveBeenCalled();
      expect(result).toEqual(updatedModel);
    });

    it('should throw error if model not found', async () => {
      const id = 999;
      const updateDto: UpdateBpmnModelDto = {
        name: 'Updated Model',
      };

      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.update(id, updateDto)).rejects.toThrow(
        'BPMN模型不存在',
      );
    });

    it('should validate BPMN XML when updating', async () => {
      const id = 1;
      const updateDto: UpdateBpmnModelDto = {
        bpmnXml: 'invalid xml',
      };

      mockRepository.findOne.mockResolvedValue(mockBpmnModel);

      await expect(service.update(id, updateDto)).rejects.toThrow(
        'BPMN XML格式无效',
      );
    });
  });

  describe('remove', () => {
    it('should remove a BPMN model', async () => {
      const id = 1;

      mockRepository.findOne.mockResolvedValue(mockBpmnModel);
      mockRepository.delete.mockResolvedValue({ affected: 1 });

      await service.remove(id);

      expect(mockRepository.findOne).toHaveBeenCalledWith({ where: { id } });
      expect(mockRepository.delete).toHaveBeenCalledWith(id);
    });

    it('should throw error if model not found', async () => {
      const id = 999;

      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.remove(id)).rejects.toThrow('BPMN模型不存在');
    });
  });

  describe('setActive', () => {
    it('should set model as active and deactivate others', async () => {
      const id = 1;
      const processId = 1;

      mockRepository.findOne.mockResolvedValue(mockBpmnModel);
      mockRepository.update.mockResolvedValue({ affected: 1 });
      mockRepository.save.mockResolvedValue({
        ...mockBpmnModel,
        isActive: true,
      });

      const result = await service.setActive(id);

      expect(mockRepository.update).toHaveBeenCalledWith(
        { processId, isActive: true },
        { isActive: false },
      );
      expect(mockRepository.save).toHaveBeenCalled();
      expect(result.isActive).toBe(true);
    });
  });

  describe('getActiveModel', () => {
    it('should return active model for process', async () => {
      const processId = 1;
      const activeModel = { ...mockBpmnModel, isActive: true };

      mockRepository.findOne.mockResolvedValue(activeModel);

      const result = await service.getActiveModel(processId);

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { processId, isActive: true },
      });
      expect(result).toEqual(activeModel);
    });

    it('should return null if no active model found', async () => {
      const processId = 999;

      mockRepository.findOne.mockResolvedValue(null);

      const result = await service.getActiveModel(processId);

      expect(result).toBeNull();
    });
  });

  describe('validateBpmnXml', () => {
    it('should validate correct BPMN XML', () => {
      const validXml = `<?xml version="1.0" encoding="UTF-8"?>
        <definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL">
          <process id="process1">
            <startEvent id="start" />
            <endEvent id="end" />
          </process>
        </definitions>`;

      expect(() => service.validateBpmnXml(validXml)).not.toThrow();
    });

    it('should throw error for invalid XML', () => {
      const invalidXml = 'not xml';

      expect(() => service.validateBpmnXml(invalidXml)).toThrow(
        'BPMN XML格式无效',
      );
    });

    it('should throw error for XML without BPMN namespace', () => {
      const xmlWithoutBpmn = `<?xml version="1.0" encoding="UTF-8"?>
        <definitions>
          <process id="process1" />
        </definitions>`;

      expect(() => service.validateBpmnXml(xmlWithoutBpmn)).toThrow(
        'BPMN XML格式无效',
      );
    });
  });

  describe('extractModelInfo', () => {
    it('should extract model information from BPMN XML', () => {
      const bpmnXml = `<?xml version="1.0" encoding="UTF-8"?>
        <definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL">
          <process id="process1">
            <startEvent id="start" />
            <task id="task1" name="Task 1" />
            <task id="task2" name="Task 2" />
            <endEvent id="end" />
          </process>
        </definitions>`;

      const result = service.extractModelInfo(bpmnXml);

      expect(result).toHaveProperty('activities');
      expect(result).toHaveProperty('gateways');
      expect(result).toHaveProperty('events');
      expect(result.activities).toContain('Task 1');
      expect(result.activities).toContain('Task 2');
    });
  });
});
