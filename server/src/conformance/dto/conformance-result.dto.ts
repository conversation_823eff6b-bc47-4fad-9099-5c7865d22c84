import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  ConformanceStatus,
  DeviationType,
} from '../../entities/conformance-result.entity';

export class ConformanceDeviationDto {
  @ApiProperty({ description: '案例ID' })
  caseId: string;

  @ApiProperty({ description: '偏差类型', enum: DeviationType })
  type: DeviationType;

  @ApiProperty({ description: '偏差描述' })
  description: string;

  @ApiPropertyOptional({ description: '相关活动' })
  activity?: string;

  @ApiPropertyOptional({ description: '期望的活动' })
  expectedActivity?: string;

  @ApiPropertyOptional({ description: '时间戳' })
  timestamp?: Date;

  @ApiProperty({ description: '严重程度', enum: ['low', 'medium', 'high'] })
  severity: 'low' | 'medium' | 'high';
}

export class ConformanceResultDto {
  @ApiProperty({ description: '符合性检查结果ID' })
  id: number;

  @ApiProperty({ description: '分析状态', enum: ConformanceStatus })
  status: ConformanceStatus;

  @ApiProperty({ description: '符合性得分 (0-1)' })
  conformanceScore: number;

  @ApiProperty({ description: '适应性得分 (0-1)' })
  fitnessScore: number;

  @ApiProperty({ description: '精确性得分 (0-1)' })
  precisionScore: number;

  @ApiProperty({ description: '泛化性得分 (0-1)' })
  generalizationScore: number;

  @ApiProperty({ description: '简洁性得分 (0-1)' })
  simplicityScore: number;

  @ApiProperty({ description: '总案例数' })
  totalCases: number;

  @ApiProperty({ description: '符合的案例数' })
  conformingCases: number;

  @ApiProperty({ description: '偏差的案例数' })
  deviatingCases: number;

  @ApiProperty({ description: '符合性等级' })
  conformanceLevel: 'excellent' | 'good' | 'fair' | 'poor';

  @ApiProperty({ description: '主要偏差类型', type: [String] })
  majorDeviationTypes: DeviationType[];

  @ApiProperty({ description: '偏差详情', type: [ConformanceDeviationDto] })
  deviations: ConformanceDeviationDto[];

  @ApiProperty({ description: '流程ID' })
  processId: number;

  @ApiProperty({ description: 'BPMN模型ID' })
  bpmnModelId: number;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @ApiPropertyOptional({ description: '分析描述' })
  description?: string;

  @ApiPropertyOptional({ description: '是否来自缓存' })
  fromCache?: boolean;

  @ApiPropertyOptional({ description: '缓存过期时间' })
  expiresAt?: Date;
}
