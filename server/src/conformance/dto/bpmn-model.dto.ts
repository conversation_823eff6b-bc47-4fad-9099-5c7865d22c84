import {
  IsString,
  IsOptional,
  IsEnum,
  IsObject,
  IsN<PERSON>ber,
  MaxLength,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  BpmnModelType,
  BpmnModelStatus,
} from '../../entities/bpmn-model.entity';

export class CreateBpmnModelDto {
  @ApiProperty({ description: '模型名称', maxLength: 100 })
  @IsString()
  @MaxLength(100)
  name: string;

  @ApiPropertyOptional({ description: '模型描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: '模型类型',
    enum: BpmnModelType,
    default: BpmnModelType.REFERENCE,
  })
  @IsEnum(BpmnModelType)
  modelType: BpmnModelType;

  @ApiPropertyOptional({ description: '模型状态', enum: BpmnModelStatus })
  @IsOptional()
  @IsEnum(BpmnModelStatus)
  status?: BpmnModelStatus;

  @ApiProperty({ description: 'BPMN 2.0 XML内容' })
  @IsString()
  bpmnXml: string;

  @ApiProperty({ description: '流程ID' })
  @IsNumber()
  processId: number;

  @ApiPropertyOptional({ description: '模型版本' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  version?: string;

  @ApiPropertyOptional({ description: '模型作者' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  author?: string;

  @ApiPropertyOptional({ description: '额外的元数据' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class UpdateBpmnModelDto extends PartialType(CreateBpmnModelDto) {
  @ApiPropertyOptional({
    description: '模型状态',
    enum: BpmnModelStatus,
  })
  @IsOptional()
  @IsEnum(BpmnModelStatus)
  status?: BpmnModelStatus;
}
