import {
  IsN<PERSON>ber,
  IsOptional,
  IsObject,
  IsBoolean,
  IsArray,
  IsString,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  ConformanceStatus,
  DeviationType,
} from '../../entities/conformance-result.entity';

export class ConformanceCheckDto {
  @ApiProperty({ description: '流程ID' })
  @IsNumber()
  processId: number;

  @ApiProperty({ description: 'BPMN模型ID' })
  @IsNumber()
  bpmnModelId: number;

  @ApiPropertyOptional({ description: '分析参数' })
  @IsOptional()
  @IsObject()
  parameters?: {
    // 对齐算法参数
    alignmentAlgorithm?: 'optimal' | 'heuristic' | 'genetic';

    // 成本参数
    moveCosts?: {
      modelMove: number;
      logMove: number;
      synchronousMove: number;
    };

    // 过滤参数
    caseFilter?: {
      minActivities?: number;
      maxActivities?: number;
      startDate?: Date;
      endDate?: Date;
      includeActivities?: string[];
      excludeActivities?: string[];
    };

    // 性能参数
    maxExecutionTime?: number; // 最大执行时间（秒）
    maxMemoryUsage?: number; // 最大内存使用（MB）

    // 输出参数
    includeAlignment?: boolean; // 是否包含详细对齐结果
    includeCaseAnalysis?: boolean; // 是否包含案例级别分析
    includeActivityAnalysis?: boolean; // 是否包含活动级别分析
  };

  @ApiPropertyOptional({ description: '是否强制刷新缓存', default: false })
  @IsOptional()
  @IsBoolean()
  forceRefresh?: boolean;

  @ApiPropertyOptional({ description: '分析描述' })
  @IsOptional()
  @IsString()
  description?: string;
}

export class ConformanceCheckResponseDto {
  @ApiProperty({ description: '符合性检查结果ID' })
  id: number;

  @ApiProperty({ description: '分析状态', enum: ConformanceStatus })
  status: ConformanceStatus;

  @ApiProperty({ description: '符合性得分 (0-1)' })
  conformanceScore: number;

  @ApiProperty({ description: '适应性得分 (0-1)' })
  fitnessScore: number;

  @ApiProperty({ description: '精确性得分 (0-1)' })
  precisionScore: number;

  @ApiProperty({ description: '泛化性得分 (0-1)' })
  generalizationScore: number;

  @ApiProperty({ description: '简洁性得分 (0-1)' })
  simplicityScore: number;

  @ApiProperty({ description: '总案例数' })
  totalCases: number;

  @ApiProperty({ description: '符合的案例数' })
  conformingCases: number;

  @ApiProperty({ description: '偏差的案例数' })
  deviatingCases: number;

  @ApiProperty({ description: '符合性等级' })
  conformanceLevel: 'excellent' | 'good' | 'fair' | 'poor';

  @ApiProperty({ description: '主要偏差类型', type: [String] })
  majorDeviationTypes: DeviationType[];

  @ApiPropertyOptional({ description: '偏差详情' })
  deviations?: Array<{
    caseId: string;
    type: DeviationType;
    description: string;
    activity?: string;
    expectedActivity?: string;
    timestamp?: Date;
    severity: 'low' | 'medium' | 'high';
  }>;

  @ApiPropertyOptional({ description: '案例级别分析' })
  caseAnalysis?: Array<{
    caseId: string;
    isConforming: boolean;
    deviationCount: number;
    conformanceScore: number;
    trace: string[];
    alignedTrace: string[];
  }>;

  @ApiPropertyOptional({ description: '活动级别分析' })
  activityAnalysis?: Array<{
    activity: string;
    frequency: number;
    conformanceRate: number;
    commonDeviations: string[];
  }>;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @ApiPropertyOptional({ description: '是否来自缓存' })
  fromCache?: boolean;

  @ApiPropertyOptional({ description: '缓存过期时间' })
  expiresAt?: Date;
}
