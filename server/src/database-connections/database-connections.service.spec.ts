import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { Repository } from 'typeorm';
import { DatabaseConnectionsService } from './database-connections.service';
import {
  DatabaseConnection,
  DatabaseType,
  ConnectionStatus,
} from '../entities/database-connection.entity';
import { CreateDatabaseConnectionDto } from './dto/create-database-connection.dto';
import { BadRequestException, NotFoundException } from '@nestjs/common';

describe('DatabaseConnectionsService', () => {
  let service: DatabaseConnectionsService;
  let repository: Repository<DatabaseConnection>;
  let configService: ConfigService;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    remove: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn().mockReturnValue('test-secret'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DatabaseConnectionsService,
        {
          provide: getRepositoryToken(DatabaseConnection),
          useValue: mockRepository,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<DatabaseConnectionsService>(
      DatabaseConnectionsService,
    );
    repository = module.get<Repository<DatabaseConnection>>(
      getRepositoryToken(DatabaseConnection),
    );
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new database connection', async () => {
      const userId = 1;
      const createDto: CreateDatabaseConnectionDto = {
        name: 'Test Connection',
        description: 'Test Description',
        type: DatabaseType.MYSQL,
        host: 'localhost',
        port: 3306,
        database: 'test_db',
        username: 'test_user',
        password: 'test_password',
      };

      const mockConnection = {
        id: 1,
        ...createDto,
        userId,
        status: ConnectionStatus.INACTIVE,
        encryptedPassword: 'encrypted_password',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockRepository.findOne.mockResolvedValue(null); // No existing connection
      mockRepository.create.mockReturnValue(mockConnection);
      mockRepository.save.mockResolvedValue(mockConnection);

      const result = await service.create(userId, createDto);

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { userId, name: createDto.name },
      });
      expect(mockRepository.create).toHaveBeenCalled();
      expect(mockRepository.save).toHaveBeenCalled();
      expect(result).toEqual(mockConnection);
    });

    it('should throw BadRequestException if connection name already exists', async () => {
      const userId = 1;
      const createDto: CreateDatabaseConnectionDto = {
        name: 'Existing Connection',
        type: DatabaseType.MYSQL,
        host: 'localhost',
        port: 3306,
        database: 'test_db',
        username: 'test_user',
        password: 'test_password',
      };

      const existingConnection = { id: 1, name: 'Existing Connection' };
      mockRepository.findOne.mockResolvedValue(existingConnection);

      await expect(service.create(userId, createDto)).rejects.toThrow(
        BadRequestException,
      );
      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { userId, name: createDto.name },
      });
    });
  });

  describe('findAll', () => {
    it('should return all connections for a user', async () => {
      const userId = 1;
      const mockConnections = [
        { id: 1, name: 'Connection 1', userId },
        { id: 2, name: 'Connection 2', userId },
      ];

      mockRepository.find.mockResolvedValue(mockConnections);

      const result = await service.findAll(userId);

      expect(mockRepository.find).toHaveBeenCalledWith({
        where: { userId },
        order: { createdAt: 'DESC' },
      });
      expect(result).toEqual(mockConnections);
    });
  });

  describe('findOne', () => {
    it('should return a connection by id', async () => {
      const userId = 1;
      const connectionId = 1;
      const mockConnection = {
        id: connectionId,
        name: 'Test Connection',
        userId,
      };

      mockRepository.findOne.mockResolvedValue(mockConnection);

      const result = await service.findOne(userId, connectionId);

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id: connectionId, userId },
      });
      expect(result).toEqual(mockConnection);
    });

    it('should throw NotFoundException if connection not found', async () => {
      const userId = 1;
      const connectionId = 999;

      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne(userId, connectionId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('remove', () => {
    it('should remove a connection', async () => {
      const userId = 1;
      const connectionId = 1;
      const mockConnection = {
        id: connectionId,
        name: 'Test Connection',
        userId,
      };

      mockRepository.findOne.mockResolvedValue(mockConnection);
      mockRepository.remove.mockResolvedValue(mockConnection);

      await service.remove(userId, connectionId);

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id: connectionId, userId },
      });
      expect(mockRepository.remove).toHaveBeenCalledWith(mockConnection);
    });
  });

  describe('testConnection', () => {
    it('should return success for valid MySQL connection parameters', async () => {
      // Note: This test would require mocking mysql2 connection
      // For now, we'll skip the actual implementation test
      expect(service).toBeDefined();
    });
  });
});
