import {
  IsString,
  <PERSON>NotEmpty,
  <PERSON>Int,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ExecuteQueryDto {
  @ApiProperty({ description: '数据库连接ID' })
  @IsInt()
  connectionId: number;

  @ApiProperty({
    description: 'SQL查询语句',
    example: 'SELECT * FROM users LIMIT 100',
  })
  @IsString()
  @IsNotEmpty()
  query: string;

  @ApiPropertyOptional({
    description: '查询限制行数',
    example: 1000,
    default: 1000,
  })
  @IsInt()
  @Min(1)
  @Max(10000)
  @IsOptional()
  limit?: number = 1000;
}

export class QueryResultDto {
  @ApiProperty({ description: '查询是否成功' })
  success: boolean;

  @ApiProperty({ description: '列名列表' })
  columns: string[];

  @ApiProperty({ description: '数据行' })
  data: any[];

  @ApiProperty({ description: '总行数' })
  totalRows: number;

  @ApiProperty({ description: '执行时间（毫秒）' })
  executionTime: number;

  @ApiPropertyOptional({ description: '错误信息' })
  error?: string;

  @ApiPropertyOptional({ description: '字段类型信息' })
  fieldTypes?: Record<string, string>;
}
