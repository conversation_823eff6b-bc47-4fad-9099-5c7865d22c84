import {
  IsString,
  <PERSON><PERSON>otEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  IsObject,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { DatabaseType } from '../../entities/database-connection.entity';

export class TestConnectionDto {
  @ApiProperty({
    description: '数据库类型',
    enum: DatabaseType,
    example: DatabaseType.MYSQL,
  })
  @IsEnum(DatabaseType)
  type: DatabaseType;

  @ApiProperty({ description: '主机地址', example: 'localhost' })
  @IsString()
  @IsNotEmpty()
  host: string;

  @ApiProperty({ description: '端口号', example: 3306 })
  @IsInt()
  @Min(1)
  @Max(65535)
  port: number;

  @ApiProperty({ description: '数据库名', example: 'business_db' })
  @IsString()
  @IsNotEmpty()
  database: string;

  @ApiProperty({ description: '用户名', example: 'admin' })
  @IsString()
  @IsNotEmpty()
  username: string;

  @ApiProperty({ description: '密码', example: 'password123' })
  @IsString()
  @IsNotEmpty()
  password: string;

  @ApiPropertyOptional({
    description: '连接选项',
    example: { charset: 'utf8mb4', timezone: '+08:00' },
  })
  @IsObject()
  @IsOptional()
  options?: Record<string, any>;
}

export class TestConnectionResponseDto {
  @ApiProperty({ description: '连接是否成功' })
  success: boolean;

  @ApiProperty({ description: '响应消息' })
  message: string;

  @ApiPropertyOptional({ description: '连接时间（毫秒）' })
  connectionTime?: number;

  @ApiPropertyOptional({ description: '数据库版本信息' })
  version?: string;

  @ApiPropertyOptional({ description: '错误详情' })
  error?: string;
}
