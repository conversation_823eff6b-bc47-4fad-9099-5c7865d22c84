import { PartialType, OmitType, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { CreateDatabaseConnectionDto } from './create-database-connection.dto';

export class UpdateDatabaseConnectionDto extends PartialType(
  OmitType(CreateDatabaseConnectionDto, ['password'] as const),
) {
  // 密码字段单独处理，因为更新时密码是可选的
  @ApiPropertyOptional({ description: '密码', example: 'password123' })
  @IsOptional()
  @IsString()
  password?: string;
}
