import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DatabaseConnectionsService } from './database-connections.service';
import { DatabaseConnectionsController } from './database-connections.controller';
import { DatabaseConnection } from '../entities/database-connection.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DatabaseConnection])],
  controllers: [DatabaseConnectionsController],
  providers: [DatabaseConnectionsService],
  exports: [DatabaseConnectionsService],
})
export class DatabaseConnectionsModule {}
