import { Test, TestingModule } from '@nestjs/testing';
import { DatabaseConnectionsController } from './database-connections.controller';
import { DatabaseConnectionsService } from './database-connections.service';
import { CreateDatabaseConnectionDto } from './dto/create-database-connection.dto';
import { UpdateDatabaseConnectionDto } from './dto/update-database-connection.dto';
import { TestConnectionDto } from './dto/test-connection.dto';
import { ExecuteQueryDto } from './dto/execute-query.dto';
import {
  DatabaseType,
  ConnectionStatus,
} from '../entities/database-connection.entity';

describe('DatabaseConnectionsController', () => {
  let controller: DatabaseConnectionsController;
  let service: DatabaseConnectionsService;

  const mockService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    testConnection: jest.fn(),
    testSavedConnection: jest.fn(),
    executeQuery: jest.fn(),
    getTables: jest.fn(),
    getTableStructure: jest.fn(),
  };

  const mockRequest = {
    user: { id: 1, username: 'testuser' },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DatabaseConnectionsController],
      providers: [
        {
          provide: DatabaseConnectionsService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<DatabaseConnectionsController>(
      DatabaseConnectionsController,
    );
    service = module.get<DatabaseConnectionsService>(
      DatabaseConnectionsService,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new database connection', async () => {
      const createDto: CreateDatabaseConnectionDto = {
        name: 'Test Connection',
        type: DatabaseType.MYSQL,
        host: 'localhost',
        port: 3306,
        database: 'test_db',
        username: 'test_user',
        password: 'test_password',
      };

      const expectedResult = {
        id: 1,
        ...createDto,
        userId: 1,
        status: ConnectionStatus.INACTIVE,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockService.create.mockResolvedValue(expectedResult);

      const result = await controller.create(mockRequest, createDto);

      expect(service.create).toHaveBeenCalledWith(1, createDto);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findAll', () => {
    it('should return all connections for the user', async () => {
      const expectedConnections = [
        { id: 1, name: 'Connection 1', userId: 1 },
        { id: 2, name: 'Connection 2', userId: 1 },
      ];

      mockService.findAll.mockResolvedValue(expectedConnections);

      const result = await controller.findAll(mockRequest);

      expect(service.findAll).toHaveBeenCalledWith(1);
      expect(result).toEqual(expectedConnections);
    });
  });

  describe('findOne', () => {
    it('should return a specific connection', async () => {
      const connectionId = 1;
      const expectedConnection = {
        id: connectionId,
        name: 'Test Connection',
        userId: 1,
      };

      mockService.findOne.mockResolvedValue(expectedConnection);

      const result = await controller.findOne(mockRequest, connectionId);

      expect(service.findOne).toHaveBeenCalledWith(1, connectionId);
      expect(result).toEqual(expectedConnection);
    });
  });

  describe('update', () => {
    it('should update a connection', async () => {
      const connectionId = 1;
      const updateDto: UpdateDatabaseConnectionDto = {
        name: 'Updated Connection',
        description: 'Updated description',
      };

      const expectedResult = {
        id: connectionId,
        ...updateDto,
        userId: 1,
      };

      mockService.update.mockResolvedValue(expectedResult);

      const result = await controller.update(
        mockRequest,
        connectionId,
        updateDto,
      );

      expect(service.update).toHaveBeenCalledWith(1, connectionId, updateDto);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('remove', () => {
    it('should remove a connection', async () => {
      const connectionId = 1;

      mockService.remove.mockResolvedValue(undefined);

      const result = await controller.remove(mockRequest, connectionId);

      expect(service.remove).toHaveBeenCalledWith(1, connectionId);
      expect(result).toEqual({ message: '连接删除成功' });
    });
  });

  describe('testConnection', () => {
    it('should test a database connection', async () => {
      const testDto: TestConnectionDto = {
        type: DatabaseType.MYSQL,
        host: 'localhost',
        port: 3306,
        database: 'test_db',
        username: 'test_user',
        password: 'test_password',
      };

      const expectedResult = {
        success: true,
        message: '连接成功',
        connectionTime: 150,
      };

      mockService.testConnection.mockResolvedValue(expectedResult);

      const result = await controller.testConnection(testDto);

      expect(service.testConnection).toHaveBeenCalledWith(testDto);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('testSavedConnection', () => {
    it('should test a saved connection', async () => {
      const connectionId = 1;
      const expectedResult = {
        success: true,
        message: '连接成功',
        connectionTime: 120,
      };

      mockService.testSavedConnection.mockResolvedValue(expectedResult);

      const result = await controller.testSavedConnection(
        mockRequest,
        connectionId,
      );

      expect(service.testSavedConnection).toHaveBeenCalledWith(1, connectionId);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('executeQuery', () => {
    it('should execute a SQL query', async () => {
      const executeDto: ExecuteQueryDto = {
        connectionId: 1,
        query: 'SELECT * FROM users LIMIT 10',
        limit: 10,
      };

      const expectedResult = {
        success: true,
        columns: ['id', 'name', 'email'],
        data: [
          { id: 1, name: 'John', email: '<EMAIL>' },
          { id: 2, name: 'Jane', email: '<EMAIL>' },
        ],
        totalRows: 2,
        executionTime: 45,
      };

      mockService.executeQuery.mockResolvedValue(expectedResult);

      const result = await controller.executeQuery(mockRequest, executeDto);

      expect(service.executeQuery).toHaveBeenCalledWith(1, executeDto);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('getTables', () => {
    it('should return list of tables', async () => {
      const connectionId = 1;
      const expectedTables = ['users', 'orders', 'products'];

      mockService.getTables.mockResolvedValue(expectedTables);

      const result = await controller.getTables(mockRequest, connectionId);

      expect(service.getTables).toHaveBeenCalledWith(1, connectionId);
      expect(result).toEqual(expectedTables);
    });
  });

  describe('getTableStructure', () => {
    it('should return table structure', async () => {
      const connectionId = 1;
      const tableName = 'users';
      const expectedStructure = [
        { Field: 'id', Type: 'int(11)', Null: 'NO', Key: 'PRI' },
        { Field: 'name', Type: 'varchar(255)', Null: 'YES', Key: '' },
      ];

      mockService.getTableStructure.mockResolvedValue(expectedStructure);

      const result = await controller.getTableStructure(
        mockRequest,
        connectionId,
        tableName,
      );

      expect(service.getTableStructure).toHaveBeenCalledWith(
        1,
        connectionId,
        tableName,
      );
      expect(result).toEqual(expectedStructure);
    });
  });
});
