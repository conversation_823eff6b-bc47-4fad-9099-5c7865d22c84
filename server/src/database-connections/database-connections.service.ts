import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import * as mysql from 'mysql2/promise';
import {
  DatabaseConnection,
  DatabaseType,
  ConnectionStatus,
} from '../entities/database-connection.entity';
import { CreateDatabaseConnectionDto } from './dto/create-database-connection.dto';
import { UpdateDatabaseConnectionDto } from './dto/update-database-connection.dto';
import {
  TestConnectionDto,
  TestConnectionResponseDto,
} from './dto/test-connection.dto';
import { ExecuteQueryDto, QueryResultDto } from './dto/execute-query.dto';

@Injectable()
export class DatabaseConnectionsService {
  private readonly encryptionKey: string;

  constructor(
    @InjectRepository(DatabaseConnection)
    private readonly connectionRepository: Repository<DatabaseConnection>,
    private readonly configService: ConfigService,
  ) {
    // 使用JWT密钥作为加密密钥的基础
    const jwtSecret = this.configService.get<string>(
      'JWT_SECRET',
      'default-secret',
    );
    this.encryptionKey = crypto
      .createHash('sha256')
      .update(jwtSecret)
      .digest('hex')
      .slice(0, 32);
  }

  /**
   * 加密密码
   */
  private encryptPassword(password: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv('aes-256-cbc', this.encryptionKey, iv);
    let encrypted = cipher.update(password, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return iv.toString('hex') + ':' + encrypted;
  }

  /**
   * 解密密码
   */
  private decryptPassword(encryptedPassword: string): string {
    try {
      const [ivHex, encrypted] = encryptedPassword.split(':');
      const iv = Buffer.from(ivHex, 'hex');
      const decipher = crypto.createDecipheriv(
        'aes-256-cbc',
        this.encryptionKey,
        iv,
      );
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } catch (error) {
      throw new InternalServerErrorException('密码解密失败');
    }
  }

  /**
   * 创建数据库连接配置
   */
  async create(
    userId: number,
    createDto: CreateDatabaseConnectionDto,
  ): Promise<DatabaseConnection> {
    // 检查连接名称是否重复
    const existingConnection = await this.connectionRepository.findOne({
      where: { userId, name: createDto.name },
    });

    if (existingConnection) {
      throw new BadRequestException('连接名称已存在');
    }

    // 加密密码
    const encryptedPassword = this.encryptPassword(createDto.password);

    const connection = this.connectionRepository.create({
      ...createDto,
      userId,
      encryptedPassword,
      status: ConnectionStatus.INACTIVE,
    });

    return await this.connectionRepository.save(connection);
  }

  /**
   * 获取用户的所有数据库连接
   */
  async findAll(userId: number): Promise<DatabaseConnection[]> {
    return await this.connectionRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 根据ID获取数据库连接
   */
  async findOne(userId: number, id: number): Promise<DatabaseConnection> {
    const connection = await this.connectionRepository.findOne({
      where: { id, userId },
    });

    if (!connection) {
      throw new NotFoundException('数据库连接不存在');
    }

    return connection;
  }

  /**
   * 更新数据库连接配置
   */
  async update(
    userId: number,
    id: number,
    updateDto: UpdateDatabaseConnectionDto,
  ): Promise<DatabaseConnection> {
    const connection = await this.findOne(userId, id);

    // 如果提供了新密码，则加密
    if (updateDto.password) {
      updateDto['encryptedPassword'] = this.encryptPassword(updateDto.password);
      delete updateDto.password;
    }

    // 如果更新了连接信息，重置状态
    if (
      updateDto.host ||
      updateDto.port ||
      updateDto.database ||
      updateDto.username ||
      updateDto.password
    ) {
      updateDto['status'] = ConnectionStatus.INACTIVE;
      updateDto['lastTestedAt'] = null;
      updateDto['lastError'] = null;
    }

    Object.assign(connection, updateDto);
    return await this.connectionRepository.save(connection);
  }

  /**
   * 删除数据库连接
   */
  async remove(userId: number, id: number): Promise<void> {
    const connection = await this.findOne(userId, id);
    await this.connectionRepository.remove(connection);
  }

  /**
   * 测试数据库连接
   */
  async testConnection(
    testDto: TestConnectionDto,
  ): Promise<TestConnectionResponseDto> {
    const startTime = Date.now();

    try {
      let connection: any;
      let version: string = '';

      switch (testDto.type) {
        case DatabaseType.MYSQL:
          connection = await mysql.createConnection({
            host: testDto.host,
            port: testDto.port,
            user: testDto.username,
            password: testDto.password,
            database: testDto.database,
            connectTimeout: 10000,
            ...testDto.options,
          });

          const [rows] = await connection.execute(
            'SELECT VERSION() as version',
          );
          version = rows[0]?.version || '';
          await connection.end();
          break;

        default:
          throw new BadRequestException(`暂不支持 ${testDto.type} 数据库类型`);
      }

      const connectionTime = Date.now() - startTime;

      return {
        success: true,
        message: '连接成功',
        connectionTime,
        version,
      };
    } catch (error) {
      const connectionTime = Date.now() - startTime;

      return {
        success: false,
        message: '连接失败',
        connectionTime,
        error: error.message,
      };
    }
  }

  /**
   * 测试已保存的数据库连接
   */
  async testSavedConnection(
    userId: number,
    id: number,
  ): Promise<TestConnectionResponseDto> {
    const connection = await this.findOne(userId, id);
    const password = this.decryptPassword(connection.encryptedPassword);

    const testDto: TestConnectionDto = {
      type: connection.type,
      host: connection.host,
      port: connection.port,
      database: connection.database,
      username: connection.username,
      password,
      options: connection.options,
    };

    const result = await this.testConnection(testDto);

    // 更新连接状态
    connection.lastTestedAt = new Date();
    if (result.success) {
      connection.status = ConnectionStatus.ACTIVE;
      connection.lastError = null;
    } else {
      connection.status = ConnectionStatus.ERROR;
      connection.lastError = result.error || '连接失败';
    }

    await this.connectionRepository.save(connection);

    return result;
  }

  /**
   * 执行SQL查询
   */
  async executeQuery(
    userId: number,
    executeDto: ExecuteQueryDto,
  ): Promise<QueryResultDto> {
    const connection = await this.findOne(userId, executeDto.connectionId);

    if (connection.status !== ConnectionStatus.ACTIVE) {
      throw new BadRequestException('数据库连接未激活，请先测试连接');
    }

    const password = this.decryptPassword(connection.encryptedPassword);
    const startTime = Date.now();

    try {
      let dbConnection: any;
      let results: any;
      let fields: any;

      switch (connection.type) {
        case DatabaseType.MYSQL:
          dbConnection = await mysql.createConnection({
            host: connection.host,
            port: connection.port,
            user: connection.username,
            password,
            database: connection.database,
            connectTimeout: 10000,
            ...connection.options,
          });

          // 添加LIMIT限制
          let query = executeDto.query.trim();

          // 移除末尾的分号（如果存在）
          if (query.endsWith(';')) {
            query = query.slice(0, -1).trim();
          }

          // 检查是否已经包含LIMIT子句（不区分大小写）
          const lowerQuery = query.toLowerCase();
          if (!lowerQuery.includes('limit')) {
            query += ` LIMIT ${executeDto.limit || 1000}`;
          }

          [results, fields] = await dbConnection.execute(query);
          await dbConnection.end();
          break;

        default:
          throw new BadRequestException(
            `暂不支持 ${connection.type} 数据库类型`,
          );
      }

      const executionTime = Date.now() - startTime;
      const columns = fields ? fields.map((field: any) => field.name) : [];
      const data = Array.isArray(results) ? results : [];

      // 分析字段类型
      const fieldTypes: Record<string, string> = {};
      if (fields && Array.isArray(fields)) {
        fields.forEach((field: any) => {
          try {
            // 安全地获取字段名和类型信息
            const fieldName = field.name || field.Field || 'unknown_field';
            const fieldType = field.type || field.Type;
            const columnType = field.columnType || field.Type || '';

            fieldTypes[fieldName] = this.mapFieldType(fieldType, columnType);
          } catch (error) {
            console.warn('字段类型分析失败:', field, error);
            // 如果分析失败，默认为字符串类型
            const fieldName = field.name || field.Field || 'unknown_field';
            fieldTypes[fieldName] = 'string';
          }
        });
      }

      return {
        success: true,
        columns,
        data,
        totalRows: data.length,
        executionTime,
        fieldTypes,
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;

      return {
        success: false,
        columns: [],
        data: [],
        totalRows: 0,
        executionTime,
        error: error.message,
      };
    }
  }

  /**
   * 映射数据库字段类型到通用类型
   */
  private mapFieldType(mysqlType: any, columnType: any): string {
    // 安全处理 columnType，防止 undefined 或 null
    const typeStr = String(columnType || '').toLowerCase();

    if (
      typeStr.includes('int') ||
      typeStr.includes('bigint') ||
      typeStr.includes('smallint') ||
      typeStr.includes('tinyint')
    ) {
      return 'number';
    }

    if (
      typeStr.includes('decimal') ||
      typeStr.includes('float') ||
      typeStr.includes('double') ||
      typeStr.includes('numeric')
    ) {
      return 'number';
    }

    if (
      typeStr.includes('date') ||
      typeStr.includes('time') ||
      typeStr.includes('timestamp') ||
      typeStr.includes('datetime')
    ) {
      return 'datetime';
    }

    if (
      typeStr.includes('text') ||
      typeStr.includes('varchar') ||
      typeStr.includes('char') ||
      typeStr.includes('longtext') ||
      typeStr.includes('mediumtext')
    ) {
      return 'string';
    }

    if (typeStr.includes('json')) {
      return 'json';
    }

    if (typeStr.includes('blob') || typeStr.includes('binary')) {
      return 'binary';
    }

    if (typeStr.includes('bool') || typeStr.includes('bit')) {
      return 'boolean';
    }

    // 如果无法识别类型或 columnType 为空，默认为字符串类型
    return 'string';
  }

  /**
   * 获取数据库表列表
   */
  async getTables(userId: number, connectionId: number): Promise<string[]> {
    const connection = await this.findOne(userId, connectionId);

    if (connection.status !== ConnectionStatus.ACTIVE) {
      throw new BadRequestException('数据库连接未激活，请先测试连接');
    }

    const password = this.decryptPassword(connection.encryptedPassword);

    try {
      let dbConnection: any;
      let results: any;

      switch (connection.type) {
        case DatabaseType.MYSQL:
          dbConnection = await mysql.createConnection({
            host: connection.host,
            port: connection.port,
            user: connection.username,
            password,
            database: connection.database,
            connectTimeout: 10000,
            ...connection.options,
          });

          [results] = await dbConnection.execute('SHOW TABLES');
          await dbConnection.end();

          return results.map((row: any) => Object.values(row)[0] as string);

        default:
          throw new BadRequestException(
            `暂不支持 ${connection.type} 数据库类型`,
          );
      }
    } catch (error) {
      throw new InternalServerErrorException(
        `获取表列表失败: ${error.message}`,
      );
    }
  }

  /**
   * 获取表结构信息
   */
  async getTableStructure(
    userId: number,
    connectionId: number,
    tableName: string,
  ): Promise<any[]> {
    const connection = await this.findOne(userId, connectionId);

    if (connection.status !== ConnectionStatus.ACTIVE) {
      throw new BadRequestException('数据库连接未激活，请先测试连接');
    }

    const password = this.decryptPassword(connection.encryptedPassword);

    try {
      let dbConnection: any;
      let results: any;

      switch (connection.type) {
        case DatabaseType.MYSQL:
          dbConnection = await mysql.createConnection({
            host: connection.host,
            port: connection.port,
            user: connection.username,
            password,
            database: connection.database,
            connectTimeout: 10000,
            ...connection.options,
          });

          [results] = await dbConnection.execute(`DESCRIBE ${tableName}`);
          await dbConnection.end();

          return results;

        default:
          throw new BadRequestException(
            `暂不支持 ${connection.type} 数据库类型`,
          );
      }
    } catch (error) {
      throw new InternalServerErrorException(
        `获取表结构失败: ${error.message}`,
      );
    }
  }
}
