import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  ParseIntPipe,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { DatabaseConnectionsService } from './database-connections.service';
import { CreateDatabaseConnectionDto } from './dto/create-database-connection.dto';
import { UpdateDatabaseConnectionDto } from './dto/update-database-connection.dto';
import {
  TestConnectionDto,
  TestConnectionResponseDto,
} from './dto/test-connection.dto';
import { ExecuteQueryDto, QueryResultDto } from './dto/execute-query.dto';
import { DatabaseConnection } from '../entities/database-connection.entity';

@ApiTags('数据库连接管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('database-connections')
export class DatabaseConnectionsController {
  constructor(
    private readonly databaseConnectionsService: DatabaseConnectionsService,
  ) {}

  @Post()
  @ApiOperation({ summary: '创建数据库连接配置' })
  @ApiResponse({
    status: 201,
    description: '连接配置创建成功',
    type: DatabaseConnection,
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 409, description: '连接名称已存在' })
  async create(
    @Request() req: any,
    @Body() createDto: CreateDatabaseConnectionDto,
  ): Promise<DatabaseConnection> {
    return await this.databaseConnectionsService.create(req.user.id, createDto);
  }

  @Get()
  @ApiOperation({ summary: '获取用户的所有数据库连接' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: [DatabaseConnection],
  })
  async findAll(@Request() req: any): Promise<DatabaseConnection[]> {
    return await this.databaseConnectionsService.findAll(req.user.id);
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取数据库连接' })
  @ApiParam({ name: 'id', description: '连接ID' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: DatabaseConnection,
  })
  @ApiResponse({ status: 404, description: '连接不存在' })
  async findOne(
    @Request() req: any,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<DatabaseConnection> {
    return await this.databaseConnectionsService.findOne(req.user.id, id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新数据库连接配置' })
  @ApiParam({ name: 'id', description: '连接ID' })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: DatabaseConnection,
  })
  @ApiResponse({ status: 404, description: '连接不存在' })
  async update(
    @Request() req: any,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateDatabaseConnectionDto,
  ): Promise<DatabaseConnection> {
    return await this.databaseConnectionsService.update(
      req.user.id,
      id,
      updateDto,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除数据库连接' })
  @ApiParam({ name: 'id', description: '连接ID' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '连接不存在' })
  async remove(
    @Request() req: any,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<{ message: string }> {
    await this.databaseConnectionsService.remove(req.user.id, id);
    return { message: '连接删除成功' };
  }

  @Post('test')
  @ApiOperation({ summary: '测试数据库连接' })
  @ApiResponse({
    status: 200,
    description: '测试完成',
    type: TestConnectionResponseDto,
  })
  async testConnection(
    @Body() testDto: TestConnectionDto,
  ): Promise<TestConnectionResponseDto> {
    return await this.databaseConnectionsService.testConnection(testDto);
  }

  @Post(':id/test')
  @ApiOperation({ summary: '测试已保存的数据库连接' })
  @ApiParam({ name: 'id', description: '连接ID' })
  @ApiResponse({
    status: 200,
    description: '测试完成',
    type: TestConnectionResponseDto,
  })
  @ApiResponse({ status: 404, description: '连接不存在' })
  async testSavedConnection(
    @Request() req: any,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<TestConnectionResponseDto> {
    return await this.databaseConnectionsService.testSavedConnection(
      req.user.id,
      id,
    );
  }

  @Post('execute-query')
  @ApiOperation({ summary: '执行SQL查询' })
  @ApiResponse({
    status: 200,
    description: '查询执行完成',
    type: QueryResultDto,
  })
  @ApiResponse({ status: 400, description: '连接未激活或查询错误' })
  async executeQuery(
    @Request() req: any,
    @Body() executeDto: ExecuteQueryDto,
  ): Promise<QueryResultDto> {
    return await this.databaseConnectionsService.executeQuery(
      req.user.id,
      executeDto,
    );
  }

  @Get(':id/tables')
  @ApiOperation({ summary: '获取数据库表列表' })
  @ApiParam({ name: 'id', description: '连接ID' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: [String],
  })
  @ApiResponse({ status: 400, description: '连接未激活' })
  @ApiResponse({ status: 404, description: '连接不存在' })
  async getTables(
    @Request() req: any,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<string[]> {
    return await this.databaseConnectionsService.getTables(req.user.id, id);
  }

  @Get(':id/tables/:tableName/structure')
  @ApiOperation({ summary: '获取表结构信息' })
  @ApiParam({ name: 'id', description: '连接ID' })
  @ApiParam({ name: 'tableName', description: '表名' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: Object,
  })
  @ApiResponse({ status: 400, description: '连接未激活' })
  @ApiResponse({ status: 404, description: '连接不存在' })
  async getTableStructure(
    @Request() req: any,
    @Param('id', ParseIntPipe) id: number,
    @Param('tableName') tableName: string,
  ): Promise<any[]> {
    return await this.databaseConnectionsService.getTableStructure(
      req.user.id,
      id,
      tableName,
    );
  }
}
