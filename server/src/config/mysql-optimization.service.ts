import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { DataSource } from 'typeorm';

@Injectable()
export class MySQLOptimizationService implements OnModuleInit {
  private readonly logger = new Logger(MySQLOptimizationService.name);

  constructor(private dataSource: DataSource) {}

  async onModuleInit() {
    // 在模块初始化时设置 MySQL 会话变量
    await this.optimizeMySQLSession();
  }

  private async optimizeMySQLSession() {
    try {
      this.logger.log('🔧 开始优化 MySQL 会话变量...');

      // 获取原始连接
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();

      // 设置会话变量以优化排序性能
      const optimizationQueries = [
        'SET SESSION sort_buffer_size = 80388608', // 80MB
        'SET SESSION read_buffer_size = 2097152', // 2MB
        'SET SESSION read_rnd_buffer_size = 4194304', // 4MB
        'SET SESSION tmp_table_size = 134217728', // 128MB
        'SET SESSION max_heap_table_size = 134217728', // 128MB
        'SET SESSION max_length_for_sort_data = 8192', // 8KB
      ];

      for (const query of optimizationQueries) {
        await queryRunner.query(query);
      }

      // 验证设置是否生效
      const variables = [
        'sort_buffer_size',
        'read_buffer_size',
        'read_rnd_buffer_size',
        'tmp_table_size',
        'max_heap_table_size',
        'max_length_for_sort_data',
      ];

      this.logger.log('📊 当前会话变量值:');
      for (const variable of variables) {
        const result = await queryRunner.query(
          `SHOW SESSION VARIABLES LIKE '${variable}'`
        );
        if (result.length > 0) {
          const value = parseInt(result[0].Value);
          const valueMB = (value / 1024 / 1024).toFixed(2);
          this.logger.log(`${variable}: ${value} (${valueMB} MB)`);
        }
      }

      await queryRunner.release();
      this.logger.log('✅ MySQL 会话变量优化完成');
    } catch (error) {
      this.logger.error('❌ MySQL 会话变量优化失败:', error.message);
    }
  }

  /**
   * 为新连接设置优化参数
   */
  async optimizeConnection(queryRunner: any) {
    try {
      const optimizationQueries = [
        'SET SESSION sort_buffer_size = 8388608', // 8MB
        'SET SESSION read_buffer_size = 2097152', // 2MB
        'SET SESSION read_rnd_buffer_size = 4194304', // 4MB
        'SET SESSION tmp_table_size = 134217728', // 128MB
        'SET SESSION max_heap_table_size = 134217728', // 128MB
        'SET SESSION max_length_for_sort_data = 8192', // 8KB
      ];

      for (const query of optimizationQueries) {
        await queryRunner.query(query);
      }
    } catch (error) {
      this.logger.warn('⚠️ 连接优化失败:', error.message);
    }
  }
}
