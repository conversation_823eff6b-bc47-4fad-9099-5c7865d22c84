import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixDataSourceHashType1750670463601 implements MigrationInterface {
  name = 'FixDataSourceHashType1750670463601';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 修改dataSourceHash字段类型从bigint到varchar(64)
    await queryRunner.query(`
            ALTER TABLE \`analysis_results\`
            MODIFY COLUMN \`dataSourceHash\` varchar(64) NULL
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 回滚到bigint类型
    await queryRunner.query(`
            ALTER TABLE \`analysis_results\`
            MODIFY COLUMN \`dataSourceHash\` bigint NULL
        `);
  }
}
