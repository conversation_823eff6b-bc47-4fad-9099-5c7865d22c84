import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitialMigrationMySQL1750602780460 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建用户表
    await queryRunner.query(`
            CREATE TABLE \`users\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`username\` varchar(50) NOT NULL,
                \`email\` varchar(100) NOT NULL,
                \`passwordHash\` varchar(255) NOT NULL,
                \`fullName\` varchar(100) NULL,
                \`avatar\` text NULL,
                \`isActive\` tinyint NOT NULL DEFAULT 1,
                \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                UNIQUE INDEX \`UQ_users_username\` (\`username\`),
                UNIQUE INDEX \`UQ_users_email\` (\`email\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB
        `);

    // 创建流程表
    await queryRunner.query(`
            CREATE TABLE \`processes\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(100) NOT NULL,
                \`description\` text NULL,
                \`status\` enum('draft', 'active', 'completed', 'archived') NOT NULL DEFAULT 'draft',
                \`businessDomain\` varchar(50) NULL,
                \`metadata\` json NULL,
                \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                \`userId\` int NOT NULL,
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB
        `);

    // 创建事件日志表
    await queryRunner.query(`
            CREATE TABLE \`event_logs\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`caseId\` varchar(100) NOT NULL,
                \`activity\` varchar(100) NOT NULL,
                \`timestamp\` timestamp NOT NULL,
                \`resource\` varchar(100) NULL,
                \`cost\` decimal(10,2) NULL,
                \`attributes\` json NULL,
                \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                \`processId\` int NOT NULL,
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB
        `);

    // 创建分析结果表
    await queryRunner.query(`
            CREATE TABLE \`analysis_results\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`analysisType\` enum('process_discovery', 'performance_analysis', 'conformance_check', 'variant_analysis', 'social_network') NOT NULL,
                \`resultData\` json NOT NULL,
                \`parameters\` json NULL,
                \`description\` text NULL,
                \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                \`processId\` int NOT NULL,
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB
        `);

    // 添加外键约束
    await queryRunner.query(`
            ALTER TABLE \`processes\` ADD CONSTRAINT \`FK_processes_userId\`
            FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE CASCADE
        `);

    await queryRunner.query(`
            ALTER TABLE \`event_logs\` ADD CONSTRAINT \`FK_event_logs_processId\`
            FOREIGN KEY (\`processId\`) REFERENCES \`processes\`(\`id\`) ON DELETE CASCADE
        `);

    await queryRunner.query(`
            ALTER TABLE \`analysis_results\` ADD CONSTRAINT \`FK_analysis_results_processId\`
            FOREIGN KEY (\`processId\`) REFERENCES \`processes\`(\`id\`) ON DELETE CASCADE
        `);

    // 创建索引
    await queryRunner.query(
      `CREATE INDEX \`IDX_event_logs_processId_caseId\` ON \`event_logs\` (\`processId\`, \`caseId\`)`,
    );
    await queryRunner.query(
      `CREATE INDEX \`IDX_event_logs_processId_timestamp\` ON \`event_logs\` (\`processId\`, \`timestamp\`)`,
    );
    await queryRunner.query(
      `CREATE INDEX \`IDX_analysis_results_processId_analysisType\` ON \`analysis_results\` (\`processId\`, \`analysisType\`)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除索引
    await queryRunner.query(
      `DROP INDEX \`IDX_analysis_results_processId_analysisType\` ON \`analysis_results\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_event_logs_processId_timestamp\` ON \`event_logs\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_event_logs_processId_caseId\` ON \`event_logs\``,
    );

    // 删除外键约束
    await queryRunner.query(
      `ALTER TABLE \`analysis_results\` DROP FOREIGN KEY \`FK_analysis_results_processId\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`event_logs\` DROP FOREIGN KEY \`FK_event_logs_processId\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`processes\` DROP FOREIGN KEY \`FK_processes_userId\``,
    );

    // 删除表
    await queryRunner.query(`DROP TABLE \`analysis_results\``);
    await queryRunner.query(`DROP TABLE \`event_logs\``);
    await queryRunner.query(`DROP TABLE \`processes\``);
    await queryRunner.query(`DROP TABLE \`users\``);
  }
}
