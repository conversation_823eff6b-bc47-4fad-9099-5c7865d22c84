import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddEventLogExtendedFields1750831802270
  implements MigrationInterface
{
  name = 'AddEventLogExtendedFields1750831802270';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE \`event_logs\`
            ADD COLUMN \`activityId\` varchar(100) NULL,
            ADD COLUMN \`previousActivity\` varchar(100) NULL,
            ADD COLUMN \`endTimestamp\` timestamp NULL
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE \`event_logs\`
            DROP COLUMN \`endTimestamp\`,
            DROP COLUMN \`previousActivity\`,
            DROP COLUMN \`activityId\`
        `);
  }
}
