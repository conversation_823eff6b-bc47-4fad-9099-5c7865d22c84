import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddConformanceCheckTables1750700000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建BPMN模型表
    await queryRunner.query(`
      CREATE TABLE \`bpmn_models\` (
        \`id\` int NOT NULL AUTO_INCREMENT,
        \`name\` varchar(100) NOT NULL,
        \`description\` text NULL,
        \`modelType\` enum('reference', 'discovered', 'normative') NOT NULL DEFAULT 'reference',
        \`status\` enum('draft', 'active', 'archived') NOT NULL DEFAULT 'draft',
        \`bpmnXml\` longtext NOT NULL,
        \`modelData\` json NULL,
        \`modelHash\` varchar(64) NULL,
        \`version\` varchar(100) NULL,
        \`author\` varchar(100) NULL,
        \`metadata\` json NULL,
        \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
        \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
        \`processId\` int NOT NULL,
        PRIMARY KEY (\`id\`)
      ) ENGINE=InnoDB
    `);

    // 创建符合性检查结果表
    await queryRunner.query(`
      CREATE TABLE \`conformance_results\` (
        \`id\` int NOT NULL AUTO_INCREMENT,
        \`status\` enum('pending', 'processing', 'completed', 'failed') NOT NULL DEFAULT 'pending',
        \`conformanceScore\` decimal(5,4) NOT NULL,
        \`fitnessScore\` decimal(5,4) NOT NULL,
        \`precisionScore\` decimal(5,4) NOT NULL,
        \`generalizationScore\` decimal(5,4) NOT NULL,
        \`simplicityScore\` decimal(5,4) NOT NULL,
        \`totalCases\` int NOT NULL,
        \`conformingCases\` int NOT NULL,
        \`deviatingCases\` int NOT NULL,
        \`deviations\` json NOT NULL,
        \`alignmentResult\` json NOT NULL,
        \`caseAnalysis\` json NULL,
        \`activityAnalysis\` json NULL,
        \`parameters\` json NULL,
        \`description\` text NULL,
        \`cacheKey\` varchar(255) NULL,
        \`expiresAt\` timestamp NULL,
        \`cacheTtl\` int NOT NULL DEFAULT 3600,
        \`metadata\` json NULL,
        \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
        \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
        \`processId\` int NOT NULL,
        \`bpmnModelId\` int NOT NULL,
        PRIMARY KEY (\`id\`)
      ) ENGINE=InnoDB
    `);

    // 添加外键约束
    await queryRunner.query(`
      ALTER TABLE \`bpmn_models\` ADD CONSTRAINT \`FK_bpmn_models_processId\`
      FOREIGN KEY (\`processId\`) REFERENCES \`processes\`(\`id\`) ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE \`conformance_results\` ADD CONSTRAINT \`FK_conformance_results_processId\`
      FOREIGN KEY (\`processId\`) REFERENCES \`processes\`(\`id\`) ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE \`conformance_results\` ADD CONSTRAINT \`FK_conformance_results_bpmnModelId\`
      FOREIGN KEY (\`bpmnModelId\`) REFERENCES \`bpmn_models\`(\`id\`) ON DELETE CASCADE
    `);

    // 创建索引
    await queryRunner.query(
      `CREATE INDEX \`IDX_bpmn_models_processId_modelType\` ON \`bpmn_models\` (\`processId\`, \`modelType\`)`,
    );
    await queryRunner.query(
      `CREATE INDEX \`IDX_bpmn_models_processId_status\` ON \`bpmn_models\` (\`processId\`, \`status\`)`,
    );
    await queryRunner.query(
      `CREATE INDEX \`IDX_conformance_results_processId_bpmnModelId\` ON \`conformance_results\` (\`processId\`, \`bpmnModelId\`)`,
    );
    await queryRunner.query(
      `CREATE INDEX \`IDX_conformance_results_processId_status\` ON \`conformance_results\` (\`processId\`, \`status\`)`,
    );
    await queryRunner.query(
      `CREATE INDEX \`IDX_conformance_results_conformanceScore\` ON \`conformance_results\` (\`conformanceScore\`)`,
    );
    await queryRunner.query(
      `CREATE INDEX \`IDX_conformance_results_cacheKey\` ON \`conformance_results\` (\`cacheKey\`)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除索引
    await queryRunner.query(
      `DROP INDEX \`IDX_conformance_results_cacheKey\` ON \`conformance_results\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_conformance_results_conformanceScore\` ON \`conformance_results\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_conformance_results_processId_status\` ON \`conformance_results\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_conformance_results_processId_bpmnModelId\` ON \`conformance_results\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_bpmn_models_processId_status\` ON \`bpmn_models\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_bpmn_models_processId_modelType\` ON \`bpmn_models\``,
    );

    // 删除外键约束
    await queryRunner.query(
      `ALTER TABLE \`conformance_results\` DROP FOREIGN KEY \`FK_conformance_results_bpmnModelId\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`conformance_results\` DROP FOREIGN KEY \`FK_conformance_results_processId\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`bpmn_models\` DROP FOREIGN KEY \`FK_bpmn_models_processId\``,
    );

    // 删除表
    await queryRunner.query(`DROP TABLE \`conformance_results\``);
    await queryRunner.query(`DROP TABLE \`bpmn_models\``);
  }
}
