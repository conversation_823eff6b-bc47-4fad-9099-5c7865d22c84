import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateDatabaseConnectionsTable1751344139454
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`database_connections\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(100) NOT NULL,
                \`description\` text NULL,
                \`type\` enum('mysql', 'postgresql', 'mssql', 'oracle') NOT NULL DEFAULT 'mysql',
                \`host\` varchar(255) NOT NULL,
                \`port\` int NOT NULL DEFAULT 3306,
                \`database\` varchar(100) NOT NULL,
                \`username\` varchar(100) NOT NULL,
                \`encryptedPassword\` varchar(500) NOT NULL,
                \`status\` enum('active', 'inactive', 'error') NOT NULL DEFAULT 'inactive',
                \`options\` json NULL,
                \`lastTestedAt\` timestamp NULL,
                \`lastError\` text NULL,
                \`userId\` int NOT NULL,
                \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                INDEX \`IDX_database_connections_userId_name\` (\`userId\`, \`name\`),
                INDEX \`IDX_database_connections_userId_status\` (\`userId\`, \`status\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB
        `);

    // 添加外键约束
    await queryRunner.query(`
            ALTER TABLE \`database_connections\`
            ADD CONSTRAINT \`FK_database_connections_userId\`
            FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE CASCADE
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE \`database_connections\``);
  }
}
