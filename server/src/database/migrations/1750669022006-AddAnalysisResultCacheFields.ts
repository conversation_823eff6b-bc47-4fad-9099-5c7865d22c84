import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAnalysisResultCacheFields1750669022006
  implements MigrationInterface
{
  name = 'AddAnalysisResultCacheFields1750669022006';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 添加状态字段
    await queryRunner.query(`
            ALTER TABLE \`analysis_results\`
            ADD COLUMN \`status\` enum('pending', 'processing', 'completed', 'failed', 'cached')
            NOT NULL DEFAULT 'pending'
        `);

    // 添加版本字段
    await queryRunner.query(`
            ALTER TABLE \`analysis_results\`
            ADD COLUMN \`version\` int NOT NULL DEFAULT 1
        `);

    // 添加缓存键字段
    await queryRunner.query(`
            ALTER TABLE \`analysis_results\`
            ADD COLUMN \`cacheKey\` varchar(255) NULL
        `);

    // 添加过期时间字段
    await queryRunner.query(`
            ALTER TABLE \`analysis_results\`
            ADD COLUMN \`expiresAt\` timestamp NULL
        `);

    // 添加缓存TTL字段
    await queryRunner.query(`
            ALTER TABLE \`analysis_results\`
            ADD COLUMN \`cacheTtl\` int NOT NULL DEFAULT 3600
        `);

    // 添加数据源哈希字段
    await queryRunner.query(`
            ALTER TABLE \`analysis_results\`
            ADD COLUMN \`dataSourceHash\` varchar(64) NULL
        `);

    // 添加元数据字段
    await queryRunner.query(`
            ALTER TABLE \`analysis_results\`
            ADD COLUMN \`metadata\` json NULL
        `);

    // 添加更新时间字段
    await queryRunner.query(`
            ALTER TABLE \`analysis_results\`
            ADD COLUMN \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
        `);

    // 创建新的索引
    await queryRunner.query(`
            CREATE INDEX \`IDX_analysis_results_processId_analysisType_version\`
            ON \`analysis_results\` (\`processId\`, \`analysisType\`, \`version\`)
        `);

    await queryRunner.query(`
            CREATE INDEX \`IDX_analysis_results_cacheKey\`
            ON \`analysis_results\` (\`cacheKey\`)
        `);

    // 更新现有记录的状态为completed
    await queryRunner.query(`
            UPDATE \`analysis_results\`
            SET \`status\` = 'completed'
            WHERE \`resultData\` IS NOT NULL
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除索引
    await queryRunner.query(
      `DROP INDEX \`IDX_analysis_results_cacheKey\` ON \`analysis_results\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_analysis_results_processId_analysisType_version\` ON \`analysis_results\``,
    );

    // 删除新添加的字段
    await queryRunner.query(
      `ALTER TABLE \`analysis_results\` DROP COLUMN \`updatedAt\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`analysis_results\` DROP COLUMN \`metadata\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`analysis_results\` DROP COLUMN \`dataSourceHash\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`analysis_results\` DROP COLUMN \`cacheTtl\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`analysis_results\` DROP COLUMN \`expiresAt\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`analysis_results\` DROP COLUMN \`cacheKey\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`analysis_results\` DROP COLUMN \`version\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`analysis_results\` DROP COLUMN \`status\``,
    );
  }
}
