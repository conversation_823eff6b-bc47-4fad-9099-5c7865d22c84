import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAnalysisResultCreatedAtIndex1750777296435
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 添加复合索引以优化按processId、analysisType过滤并按createdAt排序的查询
    await queryRunner.query(`
            CREATE INDEX \`IDX_analysis_results_processId_analysisType_createdAt\`
            ON \`analysis_results\` (\`processId\`, \`analysisType\`, \`createdAt\` DESC)
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除索引
    await queryRunner.query(`
            DROP INDEX \`IDX_analysis_results_processId_analysisType_createdAt\`
            ON \`analysis_results\`
        `);
  }
}
