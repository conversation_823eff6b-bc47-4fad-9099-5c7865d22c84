import { MigrationInterface, QueryRunner } from 'typeorm';
import * as bcrypt from 'bcryptjs';

export class AddTestUser1750602780461 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 生成密码哈希
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash('adminadmin', saltRounds);

    // 插入测试用户
    await queryRunner.query(`
      INSERT INTO \`users\` (
        \`username\`,
        \`email\`,
        \`passwordHash\`,
        \`fullName\`,
        \`isActive\`,
        \`createdAt\`,
        \`updatedAt\`
      ) VALUES (
        'admin',
        '<EMAIL>',
        '${passwordHash}',
        '系统管理员',
        1,
        NOW(),
        NOW()
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除测试用户
    await queryRunner.query(`
      DELETE FROM \`users\` WHERE \`username\` = 'admin'
    `);
  }
}
