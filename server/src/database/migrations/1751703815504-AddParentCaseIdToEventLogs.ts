import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddParentCaseIdToEventLogs1751703815504
  implements MigrationInterface
{
  name = 'AddParentCaseIdToEventLogs1751703815504';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE \`event_logs\`
            ADD COLUMN \`parentCaseId\` varchar(100) NULL
        `);

    // 添加索引以优化层次结构查询
    await queryRunner.query(`
            CREATE INDEX \`IDX_event_logs_parentCaseId\`
            ON \`event_logs\` (\`parentCaseId\`)
        `);

    // 添加复合索引以优化按流程和父案例查询
    await queryRunner.query(`
            CREATE INDEX \`IDX_event_logs_processId_parentCaseId\`
            ON \`event_logs\` (\`processId\`, \`parentCaseId\`)
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除索引
    await queryRunner.query(`
            DROP INDEX \`IDX_event_logs_processId_parentCaseId\`
            ON \`event_logs\`
        `);

    await queryRunner.query(`
            DROP INDEX \`IDX_event_logs_parentCaseId\`
            ON \`event_logs\`
        `);

    // 删除字段
    await queryRunner.query(`
            ALTER TABLE \`event_logs\`
            DROP COLUMN \`parentCaseId\`
        `);
  }
}
