import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Process } from '../entities/process.entity';
import { EventLog } from '../entities/event-log.entity';
import { CreateProcessDto, UpdateProcessDto } from './dto';

@Injectable()
export class ProcessesService {
  constructor(
    @InjectRepository(Process)
    private readonly processRepository: Repository<Process>,
    @InjectRepository(EventLog)
    private readonly eventLogRepository: Repository<EventLog>,
  ) {}

  async create(
    createProcessDto: CreateProcessDto,
    userId: number,
  ): Promise<Process> {
    const process = this.processRepository.create({
      ...createProcessDto,
      userId,
    });

    return this.processRepository.save(process);
  }

  async findAll(userId?: number): Promise<Process[]> {
    const queryBuilder = this.processRepository
      .createQueryBuilder('process')
      .leftJoinAndSelect('process.user', 'user')
      .loadRelationCountAndMap('process.eventLogCount', 'process.eventLogs')
      .select([
        'process.id',
        'process.name',
        'process.description',
        'process.status',
        'process.businessDomain',
        'process.metadata',
        'process.createdAt',
        'process.updatedAt',
        'user.id',
        'user.username',
        'user.fullName',
      ]);

    if (userId) {
      queryBuilder.where('process.userId = :userId', { userId });
    }

    return queryBuilder.getMany();
  }

  async findOne(id: number, userId?: number): Promise<Process> {
    const queryBuilder = this.processRepository
      .createQueryBuilder('process')
      .leftJoinAndSelect('process.user', 'user')
      .leftJoinAndSelect('process.analysisResults', 'analysisResults')
      .where('process.id = :id', { id });

    if (userId) {
      queryBuilder.andWhere('process.userId = :userId', { userId });
    }

    const process = await queryBuilder.getOne();

    if (!process) {
      throw new NotFoundException('流程不存在');
    }

    return process;
  }

  async findOneWithStats(
    id: number,
    userId?: number,
  ): Promise<Process & { eventLogStats?: any }> {
    const process = await this.findOne(id, userId);

    // 获取事件日志统计信息而不是加载所有数据
    const eventLogStats = await this.eventLogRepository
      .createQueryBuilder('eventLog')
      .select([
        'COUNT(*) as totalEvents',
        'COUNT(DISTINCT eventLog.caseId) as uniqueCases',
        'COUNT(DISTINCT eventLog.activity) as uniqueActivities',
        'COUNT(DISTINCT eventLog.resource) as uniqueResources',
        'MIN(eventLog.timestamp) as minTimestamp',
        'MAX(eventLog.timestamp) as maxTimestamp',
      ])
      .where('eventLog.processId = :processId', { processId: id })
      .getRawOne();

    return {
      ...process,
      eventLogStats: {
        totalEvents: parseInt(eventLogStats.totalEvents) || 0,
        uniqueCases: parseInt(eventLogStats.uniqueCases) || 0,
        uniqueActivities: parseInt(eventLogStats.uniqueActivities) || 0,
        uniqueResources: parseInt(eventLogStats.uniqueResources) || 0,
        minTimestamp: eventLogStats.minTimestamp,
        maxTimestamp: eventLogStats.maxTimestamp,
      },
    };
  }

  async update(
    id: number,
    updateProcessDto: UpdateProcessDto,
    userId: number,
  ): Promise<Process> {
    const process = await this.findOne(id);

    // 检查权限
    if (process.userId !== userId) {
      throw new ForbiddenException('无权限修改此流程');
    }

    Object.assign(process, updateProcessDto);

    return this.processRepository.save(process);
  }

  async remove(id: number, userId: number): Promise<{ message: string }> {
    const process = await this.findOne(id);

    // 检查权限
    if (process.userId !== userId) {
      throw new ForbiddenException('无权限删除此流程');
    }

    await this.processRepository.remove(process);
    return { message: '流程删除成功' };
  }

  async getProcessStatistics(userId?: number): Promise<any> {
    const queryBuilder = this.processRepository.createQueryBuilder('process');

    if (userId) {
      queryBuilder.where('process.userId = :userId', { userId });
    }

    const [total, draft, active, completed, archived] = await Promise.all([
      queryBuilder.getCount(),
      queryBuilder
        .clone()
        .andWhere('process.status = :status', { status: 'draft' })
        .getCount(),
      queryBuilder
        .clone()
        .andWhere('process.status = :status', { status: 'active' })
        .getCount(),
      queryBuilder
        .clone()
        .andWhere('process.status = :status', { status: 'completed' })
        .getCount(),
      queryBuilder
        .clone()
        .andWhere('process.status = :status', { status: 'archived' })
        .getCount(),
    ]);

    return {
      total,
      byStatus: {
        draft,
        active,
        completed,
        archived,
      },
    };
  }
}
