import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum, IsObject } from 'class-validator';
import { ProcessStatus } from '../../entities/process.entity';

export class UpdateProcessDto {
  @ApiProperty({ description: '流程名称', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: '流程描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '业务领域', required: false })
  @IsOptional()
  @IsString()
  businessDomain?: string;

  @ApiProperty({
    description: '流程状态',
    enum: ProcessStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProcessStatus)
  status?: ProcessStatus;

  @ApiProperty({ description: '元数据', required: false })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
