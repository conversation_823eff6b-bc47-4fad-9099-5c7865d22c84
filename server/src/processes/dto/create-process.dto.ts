import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsEnum,
  IsObject,
} from 'class-validator';
import { ProcessStatus } from '../../entities/process.entity';

export class CreateProcessDto {
  @ApiProperty({ description: '流程名称', example: '订单处理流程' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: '流程描述',
    example: '从订单创建到完成的完整流程',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '业务领域', example: '电商', required: false })
  @IsOptional()
  @IsString()
  businessDomain?: string;

  @ApiProperty({
    description: '流程状态',
    enum: ProcessStatus,
    example: ProcessStatus.DRAFT,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProcessStatus)
  status?: ProcessStatus;

  @ApiProperty({
    description: '元数据',
    example: { category: 'business' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
