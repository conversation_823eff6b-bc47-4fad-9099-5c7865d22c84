import { Test, TestingModule } from '@nestjs/testing';
import { ProcessesController } from './processes.controller';
import { ProcessesService } from './processes.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
  mockProcesses,
  mockCreateProcessDto,
  mockUpdateProcessDto,
  mockProcessStatistics,
  MockDataFactory,
} from '../test-utils/mock-data';

describe('ProcessesController', () => {
  let controller: ProcessesController;
  let processesService: ProcessesService;

  const mockProcessesService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    getProcessStatistics: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProcessesController],
      providers: [
        {
          provide: ProcessesService,
          useValue: mockProcessesService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<ProcessesController>(ProcessesController);
    processesService = module.get<ProcessesService>(ProcessesService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new process successfully', async () => {
      const mockRequest = { user: { id: 1 } };
      const newProcess = MockDataFactory.createProcess();
      mockProcessesService.create.mockResolvedValue(newProcess);

      const result = await controller.create(mockCreateProcessDto, mockRequest);

      expect(processesService.create).toHaveBeenCalledWith(
        mockCreateProcessDto,
        1,
      );
      expect(result).toEqual(newProcess);
    });

    it('should handle creation errors', async () => {
      const mockRequest = { user: { id: 1 } };
      const error = new Error('流程名称已存在');
      mockProcessesService.create.mockRejectedValue(error);

      await expect(
        controller.create(mockCreateProcessDto, mockRequest),
      ).rejects.toThrow(error);
    });
  });

  describe('findAll', () => {
    it('should return user processes by default', async () => {
      const mockRequest = { user: { id: 1 } };
      mockProcessesService.findAll.mockResolvedValue(mockProcesses);

      const result = await controller.findAll(mockRequest);

      expect(processesService.findAll).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockProcesses);
    });

    it('should return all processes when all=true', async () => {
      const mockRequest = { user: { id: 1 } };
      mockProcessesService.findAll.mockResolvedValue(mockProcesses);

      const result = await controller.findAll(mockRequest, 'true');

      expect(processesService.findAll).toHaveBeenCalledWith(undefined);
      expect(result).toEqual(mockProcesses);
    });
  });

  describe('getStatistics', () => {
    it('should return process statistics', async () => {
      const mockRequest = { user: { id: 1 } };
      mockProcessesService.getProcessStatistics.mockResolvedValue(
        mockProcessStatistics,
      );

      const result = await controller.getStatistics(mockRequest);

      expect(processesService.getProcessStatistics).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockProcessStatistics);
    });
  });

  describe('findOne', () => {
    it('should return a process by id', async () => {
      const mockRequest = { user: { id: 1 } };
      const mockProcess = mockProcesses[0];
      mockProcessesService.findOne.mockResolvedValue(mockProcess);

      const result = await controller.findOne('1', mockRequest);

      expect(processesService.findOne).toHaveBeenCalledWith(1, 1);
      expect(result).toEqual(mockProcess);
    });

    it('should handle process not found', async () => {
      const mockRequest = { user: { id: 1 } };
      mockProcessesService.findOne.mockResolvedValue(null);

      const result = await controller.findOne('999', mockRequest);

      expect(processesService.findOne).toHaveBeenCalledWith(999, 1);
      expect(result).toBeNull();
    });
  });

  describe('update', () => {
    it('should update a process successfully', async () => {
      const mockRequest = { user: { id: 1 } };
      const updatedProcess = { ...mockProcesses[0], ...mockUpdateProcessDto };
      mockProcessesService.update.mockResolvedValue(updatedProcess);

      const result = await controller.update(
        '1',
        mockUpdateProcessDto,
        mockRequest,
      );

      expect(processesService.update).toHaveBeenCalledWith(
        1,
        mockUpdateProcessDto,
        1,
      );
      expect(result).toEqual(updatedProcess);
    });

    it('should handle update errors', async () => {
      const mockRequest = { user: { id: 1 } };
      const error = new Error('无权限修改此流程');
      mockProcessesService.update.mockRejectedValue(error);

      await expect(
        controller.update('1', mockUpdateProcessDto, mockRequest),
      ).rejects.toThrow(error);
    });
  });

  describe('remove', () => {
    it('should remove a process successfully', async () => {
      const mockRequest = { user: { id: 1 } };
      mockProcessesService.remove.mockResolvedValue({ affected: 1 });

      const result = await controller.remove('1', mockRequest);

      expect(processesService.remove).toHaveBeenCalledWith(1, 1);
      expect(result).toEqual({ affected: 1 });
    });

    it('should handle removal errors', async () => {
      const mockRequest = { user: { id: 1 } };
      const error = new Error('无权限删除此流程');
      mockProcessesService.remove.mockRejectedValue(error);

      await expect(controller.remove('1', mockRequest)).rejects.toThrow(error);
    });
  });
});
