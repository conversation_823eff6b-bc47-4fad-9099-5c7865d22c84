import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProcessesService } from './processes.service';
import { Process } from '../entities/process.entity';
import { CreateProcessDto, UpdateProcessDto } from './dto';

describe('ProcessesService', () => {
  let service: ProcessesService;
  let repository: Repository<Process>;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    findOneBy: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getManyAndCount: jest.fn(),
      getMany: jest.fn(),
      getCount: jest.fn(),
    })),
  };

  const mockProcess = {
    id: 1,
    name: 'Test Process',
    description: 'Test Description',
    status: 'draft',
    businessDomain: 'test',
    userId: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProcessesService,
        {
          provide: getRepositoryToken(Process),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<ProcessesService>(ProcessesService);
    repository = module.get<Repository<Process>>(getRepositoryToken(Process));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new process', async () => {
      const createProcessDto: CreateProcessDto = {
        name: 'New Process',
        description: 'New Description',
        businessDomain: 'finance',
      };
      const userId = 1;

      const newProcess = { ...mockProcess, ...createProcessDto, userId };
      mockRepository.create.mockReturnValue(newProcess);
      mockRepository.save.mockResolvedValue(newProcess);

      const result = await service.create(createProcessDto, userId);

      expect(mockRepository.create).toHaveBeenCalledWith({
        ...createProcessDto,
        userId,
      });
      expect(mockRepository.save).toHaveBeenCalledWith(newProcess);
      expect(result).toEqual(newProcess);
    });
  });

  describe('findAll', () => {
    it('should return all processes for user', async () => {
      const userId = 1;
      const processes = [mockProcess];

      mockRepository.createQueryBuilder().getMany.mockResolvedValue(processes);

      const result = await service.findAll(userId);

      expect(result).toEqual(processes);
    });

    it('should return all processes when no userId provided', async () => {
      const processes = [mockProcess];

      mockRepository.createQueryBuilder().getMany.mockResolvedValue(processes);

      const result = await service.findAll();

      expect(result).toEqual(processes);
    });
  });

  describe('findOne', () => {
    it('should return a process by id and userId', async () => {
      const id = 1;
      const userId = 1;

      mockRepository.createQueryBuilder().getOne.mockResolvedValue(mockProcess);

      const result = await service.findOne(id, userId);

      expect(result).toEqual(mockProcess);
    });

    it('should throw NotFoundException if process not found', async () => {
      const id = 999;
      const userId = 1;

      mockRepository.createQueryBuilder().getOne.mockResolvedValue(null);

      await expect(service.findOne(id, userId)).rejects.toThrow('流程不存在');
    });
  });

  describe('update', () => {
    it('should update a process', async () => {
      const id = 1;
      const userId = 1;
      const updateProcessDto: UpdateProcessDto = {
        name: 'Updated Process',
        description: 'Updated Description',
      };

      const updatedProcess = { ...mockProcess, ...updateProcessDto };
      mockRepository.createQueryBuilder().getOne.mockResolvedValue(mockProcess);
      mockRepository.save.mockResolvedValue(updatedProcess);

      const result = await service.update(id, updateProcessDto, userId);

      expect(mockRepository.createQueryBuilder).toHaveBeenCalled();
      expect(mockRepository.save).toHaveBeenCalled();
      expect(result).toEqual(updatedProcess);
    });

    it('should throw error if process not found', async () => {
      const id = 999;
      const userId = 1;
      const updateProcessDto: UpdateProcessDto = {
        name: 'Updated Process',
      };

      mockRepository.createQueryBuilder().getOne.mockResolvedValue(null);

      await expect(
        service.update(id, updateProcessDto, userId),
      ).rejects.toThrow('流程不存在');
    });
  });

  describe('remove', () => {
    it('should remove a process', async () => {
      const id = 1;
      const userId = 1;

      mockRepository.createQueryBuilder().getOne.mockResolvedValue(mockProcess);
      mockRepository.remove.mockResolvedValue(mockProcess);

      await service.remove(id, userId);

      expect(mockRepository.createQueryBuilder).toHaveBeenCalled();
      expect(mockRepository.remove).toHaveBeenCalledWith(mockProcess);
    });

    it('should throw error if process not found', async () => {
      const id = 999;
      const userId = 1;

      mockRepository.createQueryBuilder().getOne.mockResolvedValue(null);

      await expect(service.remove(id, userId)).rejects.toThrow('流程不存在');
    });
  });

  describe('getProcessStatistics', () => {
    it('should return user process statistics', async () => {
      const userId = 1;

      mockRepository
        .createQueryBuilder()
        .getCount.mockResolvedValueOnce(10) // total
        .mockResolvedValueOnce(3) // draft
        .mockResolvedValueOnce(5) // active
        .mockResolvedValueOnce(2) // completed
        .mockResolvedValueOnce(0); // archived

      const result = await service.getProcessStatistics(userId);

      expect(result).toEqual({
        total: 10,
        byStatus: {
          draft: 3,
          active: 5,
          completed: 2,
          archived: 0,
        },
      });
    });
  });
});
