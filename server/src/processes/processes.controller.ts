import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiResponse,
  ApiQuery,
} from '@nestjs/swagger';
import { ProcessesService } from './processes.service';
import { CreateProcessDto, UpdateProcessDto } from './dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('流程管理')
@Controller('processes')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ProcessesController {
  constructor(private readonly processesService: ProcessesService) {}

  @Post()
  @ApiOperation({ summary: '创建流程' })
  @ApiResponse({ status: 201, description: '流程创建成功' })
  create(@Body() createProcessDto: CreateProcessDto, @Request() req) {
    return this.processesService.create(createProcessDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: '获取流程列表' })
  @ApiQuery({
    name: 'all',
    required: false,
    description: '是否获取所有用户的流程',
  })
  @ApiResponse({ status: 200, description: '获取流程列表成功' })
  findAll(@Request() req, @Query('all') all?: string) {
    const userId = all === 'true' ? undefined : req.user.id;
    return this.processesService.findAll(userId);
  }

  @Get('statistics')
  @ApiOperation({ summary: '获取流程统计信息' })
  @ApiResponse({ status: 200, description: '获取统计信息成功' })
  getStatistics(@Request() req) {
    return this.processesService.getProcessStatistics(req.user.id);
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取流程详情' })
  @ApiResponse({ status: 200, description: '获取流程详情成功' })
  @ApiResponse({ status: 404, description: '流程不存在' })
  @ApiQuery({
    name: 'withStats',
    required: false,
    description: '是否包含事件日志统计信息',
  })
  findOne(
    @Param('id') id: string,
    @Request() req,
    @Query('withStats') withStats?: string,
  ) {
    if (withStats === 'true') {
      return this.processesService.findOneWithStats(+id, req.user.id);
    }
    return this.processesService.findOne(+id, req.user.id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新流程' })
  @ApiResponse({ status: 200, description: '流程更新成功' })
  @ApiResponse({ status: 404, description: '流程不存在' })
  @ApiResponse({ status: 403, description: '无权限修改此流程' })
  update(
    @Param('id') id: string,
    @Body() updateProcessDto: UpdateProcessDto,
    @Request() req,
  ) {
    return this.processesService.update(+id, updateProcessDto, req.user.id);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除流程' })
  @ApiResponse({ status: 200, description: '流程删除成功' })
  @ApiResponse({ status: 404, description: '流程不存在' })
  @ApiResponse({ status: 403, description: '无权限删除此流程' })
  remove(@Param('id') id: string, @Request() req) {
    return this.processesService.remove(+id, req.user.id);
  }
}
