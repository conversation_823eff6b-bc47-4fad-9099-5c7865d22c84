import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProcessesService } from './processes.service';
import { ProcessesController } from './processes.controller';
import { Process } from '../entities/process.entity';
import { EventLog } from '../entities/event-log.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Process, EventLog])],
  controllers: [ProcessesController],
  providers: [ProcessesService],
  exports: [ProcessesService],
})
export class ProcessesModule {}
