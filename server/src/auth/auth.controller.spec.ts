import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import {
  mockLoginDto,
  mockRegisterDto,
  mockAuthResponse,
} from '../test-utils/mock-data';

describe('AuthController', () => {
  let controller: AuthController;
  let authService: AuthService;

  const mockAuthService = {
    register: jest.fn(),
    login: jest.fn(),
    validateUser: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('register', () => {
    it('should register a new user successfully', async () => {
      mockAuthService.register.mockResolvedValue(mockAuthResponse);

      const result = await controller.register(mockRegisterDto);

      expect(authService.register).toHaveBeenCalledWith(mockRegisterDto);
      expect(result).toEqual(mockAuthResponse);
    });

    it('should handle registration errors', async () => {
      const error = new Error('用户名已存在');
      mockAuthService.register.mockRejectedValue(error);

      await expect(controller.register(mockRegisterDto)).rejects.toThrow(error);
      expect(authService.register).toHaveBeenCalledWith(mockRegisterDto);
    });
  });

  describe('login', () => {
    it('should login user successfully', async () => {
      mockAuthService.login.mockResolvedValue(mockAuthResponse);

      const result = await controller.login(mockLoginDto);

      expect(authService.login).toHaveBeenCalledWith(mockLoginDto);
      expect(result).toEqual(mockAuthResponse);
    });

    it('should handle login errors', async () => {
      const error = new Error('用户名或密码错误');
      mockAuthService.login.mockRejectedValue(error);

      await expect(controller.login(mockLoginDto)).rejects.toThrow(error);
      expect(authService.login).toHaveBeenCalledWith(mockLoginDto);
    });
  });

  describe('getProfile', () => {
    it('should get user profile successfully', async () => {
      const mockUser = { id: 1, username: 'testuser' };
      const mockRequest = { user: { id: 1 } };
      mockAuthService.validateUser.mockResolvedValue(mockUser);

      const result = await controller.getProfile(mockRequest);

      expect(authService.validateUser).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockUser);
    });

    it('should handle profile retrieval errors', async () => {
      const mockRequest = { user: { id: 1 } };
      const error = new Error('用户不存在');
      mockAuthService.validateUser.mockRejectedValue(error);

      await expect(controller.getProfile(mockRequest)).rejects.toThrow(error);
      expect(authService.validateUser).toHaveBeenCalledWith(1);
    });
  });
});
