import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { ConflictException, UnauthorizedException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import {
  mockUsers,
  mockLoginDto,
  mockRegisterDto,
} from '../test-utils/mock-data';
import * as bcrypt from 'bcryptjs';

// Mock bcrypt
jest.mock('bcryptjs', () => ({
  hash: jest.fn().mockResolvedValue('hashedPassword123'),
  compare: jest.fn().mockResolvedValue(true),
}));
const mockedBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

describe('AuthService', () => {
  let service: AuthService;
  let usersService: UsersService;
  let jwtService: JwtService;

  const mockUsersService = {
    findByUsername: jest.fn(),
    findByEmail: jest.fn(),
    create: jest.fn(),
    findOne: jest.fn(),
    validatePassword: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    usersService = module.get<UsersService>(UsersService);
    jwtService = module.get<JwtService>(JwtService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('register', () => {
    it('should register a new user successfully', async () => {
      const newUser = { ...mockUsers[0], ...mockRegisterDto };
      const token = 'jwt-token';

      mockUsersService.create.mockResolvedValue(newUser);
      mockJwtService.sign.mockReturnValue(token);

      const result = await service.register(mockRegisterDto);

      expect(usersService.create).toHaveBeenCalledWith(mockRegisterDto);
      expect(jwtService.sign).toHaveBeenCalledWith({
        sub: newUser.id,
        username: newUser.username,
        email: newUser.email,
      });
      expect(result).toEqual({
        access_token: token,
        user: {
          id: newUser.id,
          username: newUser.username,
          email: newUser.email,
          fullName: newUser.fullName,
          avatar: newUser.avatar,
        },
      });
    });

    it('should throw ConflictException when username exists', async () => {
      const error = new ConflictException('用户名已存在');
      mockUsersService.create.mockRejectedValue(error);

      await expect(service.register(mockRegisterDto)).rejects.toThrow(
        ConflictException,
      );
      expect(usersService.create).toHaveBeenCalledWith(mockRegisterDto);
    });

    it('should throw ConflictException when email exists', async () => {
      const error = new ConflictException('邮箱已存在');
      mockUsersService.create.mockRejectedValue(error);

      await expect(service.register(mockRegisterDto)).rejects.toThrow(
        ConflictException,
      );
      expect(usersService.create).toHaveBeenCalledWith(mockRegisterDto);
    });
  });

  describe('login', () => {
    it('should login with username successfully', async () => {
      const user = { ...mockUsers[0], passwordHash: 'hashedPassword' };
      const token = 'jwt-token';

      mockUsersService.findByUsername.mockResolvedValue(user);
      mockUsersService.validatePassword.mockResolvedValue(true);
      mockJwtService.sign.mockReturnValue(token);

      const result = await service.login(mockLoginDto);

      expect(usersService.findByUsername).toHaveBeenCalledWith(
        mockLoginDto.usernameOrEmail,
      );
      expect(usersService.validatePassword).toHaveBeenCalledWith(
        user,
        mockLoginDto.password,
      );
      expect(jwtService.sign).toHaveBeenCalledWith({
        sub: user.id,
        username: user.username,
        email: user.email,
      });
      expect(result).toEqual({
        access_token: token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.fullName,
          avatar: user.avatar,
        },
      });
    });

    it('should login with email successfully', async () => {
      const user = { ...mockUsers[0], passwordHash: 'hashedPassword' };
      const token = 'jwt-token';
      const emailLoginDto = {
        usernameOrEmail: '<EMAIL>',
        password: 'password123',
      };

      mockUsersService.findByUsername.mockResolvedValue(null);
      mockUsersService.findByEmail.mockResolvedValue(user);
      mockUsersService.validatePassword.mockResolvedValue(true);
      mockJwtService.sign.mockReturnValue(token);

      const result = await service.login(emailLoginDto);

      expect(usersService.findByUsername).toHaveBeenCalledWith(
        emailLoginDto.usernameOrEmail,
      );
      expect(usersService.findByEmail).toHaveBeenCalledWith(
        emailLoginDto.usernameOrEmail,
      );
      expect(usersService.validatePassword).toHaveBeenCalledWith(
        user,
        emailLoginDto.password,
      );
      expect(result.access_token).toBe(token);
    });

    it('should throw UnauthorizedException when user not found', async () => {
      mockUsersService.findByUsername.mockResolvedValue(null);
      mockUsersService.findByEmail.mockResolvedValue(null);

      await expect(service.login(mockLoginDto)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it('should throw UnauthorizedException when password is incorrect', async () => {
      const user = { ...mockUsers[0], passwordHash: 'hashedPassword' };
      mockUsersService.findByUsername.mockResolvedValue(user);
      mockUsersService.validatePassword.mockResolvedValue(false);

      await expect(service.login(mockLoginDto)).rejects.toThrow(
        UnauthorizedException,
      );
    });
  });

  describe('validateUser', () => {
    it('should validate user successfully', async () => {
      const user = mockUsers[0];
      mockUsersService.findOne.mockResolvedValue(user);

      const result = await service.validateUser(1);

      expect(usersService.findOne).toHaveBeenCalledWith(1);
      expect(result).toEqual(user);
    });

    it('should return null when user not found', async () => {
      mockUsersService.findOne.mockResolvedValue(null);

      const result = await service.validateUser(999);

      expect(usersService.findOne).toHaveBeenCalledWith(999);
      expect(result).toBeNull();
    });
  });
});
