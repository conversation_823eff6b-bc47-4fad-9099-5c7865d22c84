import { Test, TestingModule } from '@nestjs/testing';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
  mockUsers,
  mockCreateUserDto,
  mockUpdateUserDto,
  MockDataFactory,
} from '../test-utils/mock-data';

describe('UsersController', () => {
  let controller: UsersController;
  let usersService: UsersService;

  const mockUsersService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<UsersController>(UsersController);
    usersService = module.get<UsersService>(UsersService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new user successfully', async () => {
      const newUser = MockDataFactory.createUser();
      mockUsersService.create.mockResolvedValue(newUser);

      const result = await controller.create(mockCreateUserDto);

      expect(usersService.create).toHaveBeenCalledWith(mockCreateUserDto);
      expect(result).toEqual(newUser);
    });

    it('should handle creation errors', async () => {
      const error = new Error('用户名已存在');
      mockUsersService.create.mockRejectedValue(error);

      await expect(controller.create(mockCreateUserDto)).rejects.toThrow(error);
    });
  });

  describe('findAll', () => {
    it('should return all users', async () => {
      mockUsersService.findAll.mockResolvedValue(mockUsers);

      const result = await controller.findAll();

      expect(usersService.findAll).toHaveBeenCalled();
      expect(result).toEqual(mockUsers);
    });
  });

  describe('getProfile', () => {
    it('should return current user profile', async () => {
      const mockRequest = { user: { id: 1 } };
      const mockUser = mockUsers[0];
      mockUsersService.findOne.mockResolvedValue(mockUser);

      const result = await controller.getProfile(mockRequest);

      expect(usersService.findOne).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockUser);
    });
  });

  describe('findOne', () => {
    it('should return a user by id', async () => {
      const mockUser = mockUsers[0];
      mockUsersService.findOne.mockResolvedValue(mockUser);

      const result = await controller.findOne('1');

      expect(usersService.findOne).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockUser);
    });

    it('should handle user not found', async () => {
      mockUsersService.findOne.mockResolvedValue(null);

      const result = await controller.findOne('999');

      expect(usersService.findOne).toHaveBeenCalledWith(999);
      expect(result).toBeNull();
    });
  });

  describe('update', () => {
    it('should update a user successfully', async () => {
      const updatedUser = { ...mockUsers[0], ...mockUpdateUserDto };
      mockUsersService.update.mockResolvedValue(updatedUser);

      const result = await controller.update('1', mockUpdateUserDto);

      expect(usersService.update).toHaveBeenCalledWith(1, mockUpdateUserDto);
      expect(result).toEqual(updatedUser);
    });

    it('should handle update errors', async () => {
      const error = new Error('用户不存在');
      mockUsersService.update.mockRejectedValue(error);

      await expect(controller.update('999', mockUpdateUserDto)).rejects.toThrow(
        error,
      );
    });
  });

  describe('remove', () => {
    it('should remove a user successfully', async () => {
      mockUsersService.remove.mockResolvedValue({ affected: 1 });

      const result = await controller.remove('1');

      expect(usersService.remove).toHaveBeenCalledWith(1);
      expect(result).toEqual({ affected: 1 });
    });

    it('should handle removal errors', async () => {
      const error = new Error('用户不存在');
      mockUsersService.remove.mockRejectedValue(error);

      await expect(controller.remove('999')).rejects.toThrow(error);
    });
  });
});
