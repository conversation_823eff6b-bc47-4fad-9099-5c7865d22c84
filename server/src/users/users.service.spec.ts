import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConflictException, NotFoundException } from '@nestjs/common';
import { UsersService } from './users.service';
import { User } from '../entities/user.entity';
import {
  mockUsers,
  mockCreateUserDto,
  mockUpdateUserDto,
} from '../test-utils/mock-data';

describe('UsersService', () => {
  let service: UsersService;
  let repository: Repository<User>;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    findOneBy: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getRepositoryToken(User),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    repository = module.get<Repository<User>>(getRepositoryToken(User));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new user successfully', async () => {
      const newUser = { ...mockUsers[0], ...mockCreateUserDto };

      mockRepository.findOne.mockResolvedValue(null); // no existing user
      mockRepository.create.mockReturnValue(newUser);
      mockRepository.save.mockResolvedValue(newUser);

      const result = await service.create(mockCreateUserDto);

      expect(repository.findOne).toHaveBeenCalledWith({
        where: [
          { username: mockCreateUserDto.username },
          { email: mockCreateUserDto.email },
        ],
      });
      expect(repository.create).toHaveBeenCalledWith({
        username: mockCreateUserDto.username,
        email: mockCreateUserDto.email,
        passwordHash: expect.any(String),
        fullName: mockCreateUserDto.fullName,
      });
      expect(repository.save).toHaveBeenCalledWith(newUser);
      expect(result).toEqual(newUser);
    });

    it('should throw ConflictException when username exists', async () => {
      const existingUser = {
        ...mockUsers[0],
        username: mockCreateUserDto.username,
      };
      mockRepository.findOne.mockResolvedValue(existingUser);

      await expect(service.create(mockCreateUserDto)).rejects.toThrow(
        ConflictException,
      );
      expect(repository.findOne).toHaveBeenCalledWith({
        where: [
          { username: mockCreateUserDto.username },
          { email: mockCreateUserDto.email },
        ],
      });
    });

    it('should throw ConflictException when email exists', async () => {
      const existingUser = { ...mockUsers[0], email: mockCreateUserDto.email };
      mockRepository.findOne.mockResolvedValue(existingUser);

      await expect(service.create(mockCreateUserDto)).rejects.toThrow(
        ConflictException,
      );
      expect(repository.findOne).toHaveBeenCalledWith({
        where: [
          { username: mockCreateUserDto.username },
          { email: mockCreateUserDto.email },
        ],
      });
    });
  });

  describe('findAll', () => {
    it('should return all users', async () => {
      mockRepository.find.mockResolvedValue(mockUsers);

      const result = await service.findAll();

      expect(repository.find).toHaveBeenCalledWith({
        select: [
          'id',
          'username',
          'email',
          'fullName',
          'avatar',
          'isActive',
          'createdAt',
          'updatedAt',
        ],
      });
      expect(result).toEqual(mockUsers);
    });
  });

  describe('findOne', () => {
    it('should return a user by id', async () => {
      const user = mockUsers[0];
      mockRepository.findOne.mockResolvedValue(user);

      const result = await service.findOne(1);

      expect(repository.findOne).toHaveBeenCalledWith({
        where: { id: 1 },
        select: [
          'id',
          'username',
          'email',
          'fullName',
          'avatar',
          'isActive',
          'createdAt',
          'updatedAt',
        ],
      });
      expect(result).toEqual(user);
    });

    it('should throw NotFoundException when user not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne(999)).rejects.toThrow(NotFoundException);
    });
  });

  describe('findByUsername', () => {
    it('should return a user by username', async () => {
      const user = mockUsers[0];
      mockRepository.findOne.mockResolvedValue(user);

      const result = await service.findByUsername('testuser1');

      expect(repository.findOne).toHaveBeenCalledWith({
        where: { username: 'testuser1' },
      });
      expect(result).toEqual(user);
    });
  });

  describe('findByEmail', () => {
    it('should return a user by email', async () => {
      const user = mockUsers[0];
      mockRepository.findOne.mockResolvedValue(user);

      const result = await service.findByEmail('<EMAIL>');

      expect(repository.findOne).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });
      expect(result).toEqual(user);
    });
  });

  describe('update', () => {
    it('should update a user successfully', async () => {
      const existingUser = mockUsers[0];
      const updatedUser = { ...existingUser, ...mockUpdateUserDto };

      mockRepository.findOne.mockResolvedValue(existingUser);
      mockRepository.save.mockResolvedValue(updatedUser);

      const result = await service.update(1, mockUpdateUserDto);

      expect(repository.findOne).toHaveBeenCalledWith({
        where: { id: 1 },
        select: [
          'id',
          'username',
          'email',
          'fullName',
          'avatar',
          'isActive',
          'createdAt',
          'updatedAt',
        ],
      });
      expect(repository.save).toHaveBeenCalledWith(updatedUser);
      expect(result).toEqual(updatedUser);
    });

    it('should throw NotFoundException when user not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.update(999, mockUpdateUserDto)).rejects.toThrow(
        NotFoundException,
      );
      expect(repository.findOne).toHaveBeenCalledWith({
        where: { id: 999 },
        select: [
          'id',
          'username',
          'email',
          'fullName',
          'avatar',
          'isActive',
          'createdAt',
          'updatedAt',
        ],
      });
    });
  });

  describe('remove', () => {
    it('should remove a user successfully', async () => {
      const existingUser = mockUsers[0];
      mockRepository.findOne.mockResolvedValue(existingUser);
      mockRepository.remove.mockResolvedValue(existingUser);

      await service.remove(1);

      expect(repository.findOne).toHaveBeenCalledWith({
        where: { id: 1 },
        select: [
          'id',
          'username',
          'email',
          'fullName',
          'avatar',
          'isActive',
          'createdAt',
          'updatedAt',
        ],
      });
      expect(repository.remove).toHaveBeenCalledWith(existingUser);
    });

    it('should throw NotFoundException when user not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.remove(999)).rejects.toThrow(NotFoundException);
    });
  });
});
