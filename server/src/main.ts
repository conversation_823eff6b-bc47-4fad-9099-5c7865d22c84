import { NestFactory, Reflector } from '@nestjs/core';
import { ValidationPipe, ClassSerializerInterceptor } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import * as bodyParser from 'body-parser';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 配置请求体大小限制
  app.use(bodyParser.json({ limit: '50mb' }));
  app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

  // 设置全局API前缀
  app.setGlobalPrefix('api/v1');

  // 启用CORS
  const corsOrigins = process.env.CORS_ORIGIN
    ? process.env.CORS_ORIGIN.split(',').map((origin) => origin.trim())
    : [
        'http://localhost:3000',
        'http://localhost:3001',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:3001',
      ];

  app.enableCors({
    origin: corsOrigins,
    credentials: process.env.CORS_CREDENTIALS === 'true' || true,
  });

  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // 全局序列化拦截器
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)));

  // Swagger API 文档
  const config = new DocumentBuilder()
    .setTitle('ProMax API')
    .setDescription('ProMax 流程挖掘平台 API 文档')
    .setVersion('1.0')
    .addBearerAuth()
    .addServer('http://localhost:3000', '开发环境')
    .addServer('https://api.promined.com', '生产环境')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api-docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });

  const port = process.env.NESTJS_INTERNAL_PORT || process.env.PORT || 3003;
  await app.listen(port);

  console.log(`🚀 ProMax API 服务已启动: http://localhost:${port}`);
  console.log(`📚 API 文档地址: http://localhost:${port}/api-docs`);
  console.log(`🔗 API 基础路径: http://localhost:${port}/api/v1`);
}
bootstrap();
