import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  Request,
  BadRequestException,
  SetMetadata,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiResponse,
  ApiConsumes,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { diskStorage } from 'multer';
import { extname } from 'path';
import * as fs from 'fs';
import { DataProcessingService } from './data-processing.service';
import { ProcessMiningService } from './process-mining.service';
import { DiscoveryStatusService } from './discovery-status.service';
import { DiscoveryExecutionService } from './discovery-execution.service';
import { CacheService } from './cache.service';
import {
  SubprocessDiscoveryService,
  SubprocessDiscoveryOptions,
} from './subprocess-discovery.service';
import {
  UploadDataDto,
  SubprocessDiscoveryOptionsDto,
  ProcessDiscoveryOptionsDto,
  PerformanceAnalysisOptionsDto,
} from './dto';
import {
  ProcessCompareOptionsDto,
  ProcessCompareResultDto,
} from './dto/process-compare-options.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AnalysisType } from '../entities/analysis-result.entity';

@ApiTags('数据分析')
@Controller('analysis')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AnalysisController {
  constructor(
    private readonly dataProcessingService: DataProcessingService,
    private readonly processMiningService: ProcessMiningService,
    private readonly discoveryStatusService: DiscoveryStatusService,
    private readonly discoveryExecutionService: DiscoveryExecutionService,
    private readonly cacheService: CacheService,
    private readonly subprocessDiscoveryService: SubprocessDiscoveryService,
  ) {}

  @Post('preview')
  @SetMetadata('isPublic', true)
  @ApiOperation({ summary: '获取文件预览数据' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 200, description: '文件预览获取成功' })
  @ApiResponse({ status: 400, description: '文件格式不支持' })
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads',
        filename: (req, file, cb) => {
          const randomName = Array(32)
            .fill(null)
            .map(() => Math.round(Math.random() * 16).toString(16))
            .join('');
          cb(null, `${randomName}${extname(file.originalname)}`);
        },
      }),
      fileFilter: (req, file, cb) => {
        if (
          file.mimetype.match(
            /\/(csv|vnd\.ms-excel|vnd\.openxmlformats-officedocument\.spreadsheetml\.sheet)$/,
          )
        ) {
          cb(null, true);
        } else {
          cb(new BadRequestException('只支持 CSV 和 Excel 文件'), false);
        }
      },
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
  )
  async getFilePreview(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('请选择要预览的文件');
    }

    try {
      // 解析文件
      const fileType = extname(file.originalname).substring(1).toLowerCase();
      const data = await this.dataProcessingService.parseFile(
        file.path,
        fileType,
      );

      // 删除临时文件
      fs.unlinkSync(file.path);

      // 提取列名
      const columns = data.length > 0 ? Object.keys(data[0]) : [];

      return {
        columns,
        preview: data.slice(0, 10), // 只返回前10行作为预览
        totalRows: data.length,
      };
    } catch (error) {
      // 删除临时文件
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
      throw new BadRequestException(`文件预览失败: ${error.message}`);
    }
  }

  @Get('discovery-status/:discoveryId')
  @ApiOperation({ summary: '获取流程发现状态' })
  @ApiResponse({ status: 200, description: '获取状态成功' })
  @ApiResponse({ status: 404, description: '发现任务不存在' })
  async getDiscoveryStatus(@Param('discoveryId') discoveryId: string) {
    // 验证discoveryId格式
    if (!discoveryId.startsWith('discovery_')) {
      throw new BadRequestException('无效的发现任务ID格式');
    }

    const status = await this.discoveryStatusService.getStatus(discoveryId);

    if (!status) {
      throw new BadRequestException('发现任务不存在或已过期');
    }

    return {
      discoveryId: status.discoveryId,
      status: status.status,
      progress: status.progress,
      message: status.message,
      currentTask: status.message,
      startTime: status.startTime?.toISOString(),
      endTime: status.endTime?.toISOString(),
      error: status.error,
      currentStepId: status.currentStepId,
      failedStepId: status.failedStepId,
      phase: status.phase,
      phaseError: status.phaseError,
      validationErrors: status.validationErrors,
      result: status.result,
    };
  }

  @Delete('cancel-discovery/:discoveryId')
  @ApiOperation({ summary: '取消流程发现' })
  @ApiResponse({ status: 200, description: '取消成功' })
  @ApiResponse({ status: 404, description: '发现任务不存在' })
  async cancelDiscovery(@Param('discoveryId') discoveryId: string) {
    if (!discoveryId.startsWith('discovery_')) {
      throw new BadRequestException('无效的发现ID');
    }

    const success =
      await this.discoveryStatusService.cancelDiscovery(discoveryId);

    if (success) {
      return {
        success: true,
        message: '流程发现已取消',
        discoveryId,
      };
    } else {
      throw new BadRequestException('取消失败');
    }
  }

  @Post('discovery-wizard/:processId')
  @ApiOperation({ summary: '启动流程发现向导' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, description: '流程发现向导启动成功' })
  @ApiResponse({ status: 400, description: '文件格式不支持或配置无效' })
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads',
        filename: (req, file, cb) => {
          const randomName = Array(32)
            .fill(null)
            .map(() => Math.round(Math.random() * 16).toString(16))
            .join('');
          cb(null, `${randomName}${extname(file.originalname)}`);
        },
      }),
      fileFilter: (req, file, cb) => {
        if (
          file.mimetype.match(
            /\/(csv|vnd\.ms-excel|vnd\.openxmlformats-officedocument\.spreadsheetml\.sheet)$/,
          )
        ) {
          cb(null, true);
        } else {
          cb(new BadRequestException('只支持 CSV 和 Excel 文件'), false);
        }
      },
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
  )
  async startDiscoveryWizard(
    @Param('processId') processId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDto: UploadDataDto,
  ) {
    if (!file) {
      throw new BadRequestException('请选择要上传的文件');
    }

    const discoveryId =
      await this.discoveryStatusService.createDiscovery(+processId);

    // 处理文件和流程发现
    this.processFileDiscovery(+processId, discoveryId, file.path, uploadDto);

    return {
      discoveryId,
      message: '流程发现已启动',
    };
  }

  private processFileDiscovery(
    processId: number,
    discoveryId: string,
    filePath: string,
    uploadDto: UploadDataDto,
  ): void {
    setImmediate(() => {
      this.discoveryExecutionService
        .executeFileDiscovery(processId, discoveryId, filePath, uploadDto)
        .catch((error) => {
          console.error(`File discovery failed for ${discoveryId}:`, error);
        });
    });
  }

  @Post('discovery-wizard/:processId/database')
  @ApiOperation({ summary: '启动流程发现向导（数据库查询模式）' })
  @ApiResponse({ status: 201, description: '流程发现向导启动成功' })
  @ApiResponse({ status: 400, description: '数据验证失败或配置无效' })
  async startDiscoveryWizardWithDatabase(
    @Param('processId') processId: string,
    @Body()
    requestBody: {
      queryData: { columns: string[]; data: any[]; totalRows: number };
      config: UploadDataDto;
    },
  ) {
    const { queryData, config } = requestBody;

    if (!queryData || !queryData.data || queryData.data.length === 0) {
      throw new BadRequestException('查询数据不能为空');
    }

    const discoveryId =
      await this.discoveryStatusService.createDiscovery(+processId);

    // 处理数据库数据和流程发现
    this.processDatabaseDiscovery(+processId, discoveryId, queryData, config);

    return {
      discoveryId,
      message: '流程发现已启动',
    };
  }

  private processDatabaseDiscovery(
    processId: number,
    discoveryId: string,
    queryData: { columns: string[]; data: any[]; totalRows: number },
    config: UploadDataDto,
  ): void {
    setImmediate(() => {
      this.discoveryExecutionService
        .executeDatabaseDiscovery(processId, discoveryId, queryData, config)
        .catch((error) => {
          console.error(`Database discovery failed for ${discoveryId}:`, error);
        });
    });
  }

  @Post('upload')
  @ApiOperation({ summary: '上传数据文件' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, description: '文件上传成功' })
  @ApiResponse({ status: 400, description: '文件格式不支持或数据验证失败' })
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads',
        filename: (req, file, cb) => {
          const randomName = Array(32)
            .fill(null)
            .map(() => Math.round(Math.random() * 16).toString(16))
            .join('');
          cb(null, `${randomName}${extname(file.originalname)}`);
        },
      }),
      fileFilter: (req, file, cb) => {
        if (
          file.mimetype.match(
            /\/(csv|vnd\.ms-excel|vnd\.openxmlformats-officedocument\.spreadsheetml\.sheet)$/,
          )
        ) {
          cb(null, true);
        } else {
          cb(new BadRequestException('只支持 CSV 和 Excel 文件'), false);
        }
      },
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
  )
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDto: UploadDataDto,
    @Request() req,
  ) {
    if (!file) {
      throw new BadRequestException('请选择要上传的文件');
    }

    try {
      // 解析文件
      const fileType = extname(file.originalname).substring(1).toLowerCase();
      const data = await this.dataProcessingService.parseFile(
        file.path,
        fileType,
      );

      // 验证数据
      const validation = this.dataProcessingService.validateData(
        data,
        uploadDto,
      );

      if (!validation.isValid) {
        // 删除临时文件
        fs.unlinkSync(file.path);
        return {
          success: false,
          validation,
        };
      }

      // 保存事件日志
      const eventLogs = await this.dataProcessingService.saveEventLogs(
        data,
        uploadDto,
      );

      // 删除临时文件
      fs.unlinkSync(file.path);

      return {
        success: true,
        message: '数据上传成功',
        validation,
        savedRecords: eventLogs.length,
      };
    } catch (error) {
      // 删除临时文件
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
      throw new BadRequestException(`数据处理失败: ${error.message}`);
    }
  }

  @Post('validate')
  @ApiOperation({ summary: '验证数据文件' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 200, description: '数据验证完成' })
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads',
        filename: (req, file, cb) => {
          const randomName = Array(32)
            .fill(null)
            .map(() => Math.round(Math.random() * 16).toString(16))
            .join('');
          cb(null, `${randomName}${extname(file.originalname)}`);
        },
      }),
      fileFilter: (req, file, cb) => {
        if (
          file.mimetype.match(
            /\/(csv|vnd\.ms-excel|vnd\.openxmlformats-officedocument\.spreadsheetml\.sheet)$/,
          )
        ) {
          cb(null, true);
        } else {
          cb(new BadRequestException('只支持 CSV 和 Excel 文件'), false);
        }
      },
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
  )
  async validateFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDto: UploadDataDto,
  ) {
    if (!file) {
      throw new BadRequestException('请选择要验证的文件');
    }

    try {
      // 解析文件
      const fileType = extname(file.originalname).substring(1).toLowerCase();
      const data = await this.dataProcessingService.parseFile(
        file.path,
        fileType,
      );

      // 验证数据
      const validation = this.dataProcessingService.validateData(
        data,
        uploadDto,
      );

      // 删除临时文件
      fs.unlinkSync(file.path);

      return {
        validation,
        fileName: file.originalname,
        fileSize: file.size,
      };
    } catch (error) {
      // 删除临时文件
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
      throw new BadRequestException(`文件验证失败: ${error.message}`);
    }
  }

  @Post('upload-large')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads',
        filename: (req, file, cb) => {
          const randomName = Array(32)
            .fill(null)
            .map(() => Math.round(Math.random() * 16).toString(16))
            .join('');
          cb(null, `${randomName}${extname(file.originalname)}`);
        },
      }),
      fileFilter: (req, file, cb) => {
        if (file.mimetype.match(/\/csv$/)) {
          cb(null, true);
        } else {
          cb(new BadRequestException('大文件上传只支持 CSV 文件'), false);
        }
      },
      limits: {
        fileSize: 500 * 1024 * 1024, // 500MB
      },
    }),
  )
  @ApiOperation({ summary: '上传大型数据文件（流式处理）' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: '大型数据文件上传',
    type: 'multipart/form-data',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        processId: { type: 'number' },
        caseIdField: { type: 'string' },
        activityField: { type: 'string' },
        timestampField: { type: 'string' },
        resourceField: { type: 'string' },
        costField: { type: 'string' },
      },
    },
  })
  async uploadLargeFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDto: UploadDataDto,
  ) {
    if (!file) {
      throw new BadRequestException('请选择要上传的文件');
    }

    try {
      const result = await this.dataProcessingService.uploadLargeFile(
        file.path,
        uploadDto,
      );

      // 清理临时文件
      fs.unlinkSync(file.path);

      return result;
    } catch (error) {
      // 清理临时文件
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
      throw error;
    }
  }

  @Get('statistics/:processId')
  @ApiOperation({ summary: '获取流程数据统计' })
  @ApiResponse({ status: 200, description: '获取统计信息成功' })
  async getStatistics(@Param('processId') processId: string) {
    return this.dataProcessingService.getDataStatistics(+processId);
  }

  @Post('fix-parent-case-ids/:processId')
  @ApiOperation({ summary: '修复父案例ID数据' })
  @ApiResponse({ status: 200, description: '修复成功' })
  @ApiResponse({ status: 400, description: '修复失败' })
  @ApiQuery({
    name: 'sourceField',
    required: false,
    description: '源字段名称',
    example: 'parent_case_id',
  })
  async fixParentCaseIds(
    @Param('processId') processId: string,
    @Query('sourceField') sourceField?: string,
  ) {
    return this.dataProcessingService.fixParentCaseIds(
      +processId,
      sourceField || 'parent_case_id',
    );
  }

  @Get('test-hierarchy/:processId')
  @ApiOperation({ summary: '测试层次结构分析' })
  @ApiResponse({ status: 200, description: '测试成功' })
  async testHierarchy(@Param('processId') processId: string) {
    return this.processMiningService.testHierarchicalAnalysis(+processId);
  }

  @Post('discover/:processId')
  @ApiOperation({ summary: '流程发现 - 生成DFG' })
  @ApiResponse({ status: 200, description: '流程发现成功' })
  @ApiQuery({
    name: 'forceRefresh',
    required: false,
    type: Boolean,
    description: '是否强制刷新缓存（兼容旧版本）',
  })
  async discoverProcess(
    @Param('processId') processId: string,
    @Query('forceRefresh') forceRefresh?: boolean,
    @Body() options?: ProcessDiscoveryOptionsDto,
  ) {
    try {
      // 合并查询参数和请求体参数，请求体优先
      const finalOptions = {
        forceRefresh: options?.forceRefresh ?? forceRefresh ?? false,
        requiredActivities: options?.requiredActivities ?? [],
      };

      return await this.processMiningService.discoverProcess(
        +processId,
        finalOptions,
      );
    } catch (error) {
      if (error instanceof Error) {
        throw new BadRequestException(error.message);
      }
      throw error;
    }
  }

  @Post('variants/:processId')
  @ApiOperation({ summary: '变体分析' })
  @ApiResponse({ status: 200, description: '变体分析成功' })
  async analyzeVariants(@Param('processId') processId: string) {
    try {
      return await this.processMiningService.analyzeVariants(+processId);
    } catch (error) {
      if (error instanceof Error) {
        throw new BadRequestException(error.message);
      }
      throw error;
    }
  }

  @Post('performance/:processId')
  @ApiOperation({ summary: '性能分析' })
  @ApiResponse({ status: 200, description: '性能分析成功' })
  @ApiQuery({
    name: 'forceRefresh',
    required: false,
    type: Boolean,
    description: '是否强制刷新缓存（兼容旧版本）',
  })
  async analyzePerformance(
    @Param('processId') processId: string,
    @Query('forceRefresh') forceRefresh?: string,
    @Body() options?: PerformanceAnalysisOptionsDto,
  ) {
    try {
      // 合并查询参数和请求体参数，请求体优先
      const finalOptions = {
        forceRefresh: options?.forceRefresh ?? forceRefresh === 'true',
        requiredActivities: options?.requiredActivities ?? [],
      };

      return await this.processMiningService.analyzePerformance(
        +processId,
        finalOptions,
      );
    } catch (error) {
      if (error instanceof Error) {
        throw new BadRequestException(error.message);
      }
      throw error;
    }
  }

  @Post('compare/:processId')
  @ApiOperation({ summary: '流程比对分析' })
  @ApiResponse({
    status: 200,
    description: '流程比对分析成功',
    type: ProcessCompareResultDto,
  })
  @ApiBody({
    type: ProcessCompareOptionsDto,
    description: '比对分析配置参数',
  })
  async compareProcess(
    @Param('processId') processId: string,
    @Body() options: ProcessCompareOptionsDto,
  ): Promise<ProcessCompareResultDto> {
    return this.processMiningService.compareProcess(+processId, options);
  }

  @Get('results/:processId/:analysisType')
  @ApiOperation({ summary: '获取分析结果' })
  @ApiResponse({ status: 200, description: '获取分析结果成功' })
  async getAnalysisResult(
    @Param('processId') processId: string,
    @Param('analysisType') analysisType: AnalysisType,
  ) {
    const result = await this.processMiningService.getAnalysisResult(
      +processId,
      analysisType,
    );

    // 如果找到结果，返回resultData字段；否则返回null
    return result ? result.resultData : null;
  }

  @Get('data-count/:processId')
  @ApiOperation({ summary: '获取指定时间范围内的数据计数' })
  @ApiResponse({ status: 200, description: '获取数据计数成功' })
  async getDataCountByTimeRange(
    @Param('processId') processId: string,
    @Query('startTime') startTime: string,
    @Query('endTime') endTime: string,
  ) {
    return this.dataProcessingService.getDataCountByTimeRange(
      +processId,
      startTime,
      endTime,
    );
  }

  @Post('period-details/:processId')
  @ApiOperation({ summary: '获取指定周期的详细数据' })
  @ApiResponse({ status: 200, description: '获取周期详细数据成功' })
  @ApiQuery({
    name: 'period',
    required: true,
    type: String,
  })
  @ApiQuery({
    name: 'granularity',
    required: false,
    type: String,
    description: '时间粒度：day, week, month, year',
  })
  @ApiBody({
    type: PerformanceAnalysisOptionsDto,
    description: '性能分析配置参数（可选）',
    required: false,
  })
  async getPeriodDetails(
    @Param('processId') processId: string,
    @Query('period') period: string,
    @Query('granularity') granularity: string = 'week',
    @Body() options?: PerformanceAnalysisOptionsDto,
  ) {
    return this.processMiningService.getPeriodDetails(
      +processId,
      period,
      granularity,
      options?.requiredActivities,
    );
  }

  @Get('node-detail/:processId/:nodeId')
  @ApiOperation({ summary: '获取节点详细信息' })
  @ApiResponse({ status: 200, description: '获取节点详细信息成功' })
  async getNodeDetailInfo(
    @Param('processId') processId: string,
    @Param('nodeId') nodeId: string,
  ) {
    return this.processMiningService.getNodeDetailInfo(+processId, nodeId);
  }

  @Get('edge-detail/:processId/:sourceId/:targetId')
  @ApiOperation({ summary: '获取连接详细信息' })
  @ApiResponse({ status: 200, description: '获取连接详细信息成功' })
  async getEdgeDetailInfo(
    @Param('processId') processId: string,
    @Param('sourceId') sourceId: string,
    @Param('targetId') targetId: string,
  ) {
    return this.processMiningService.getEdgeDetailInfo(
      +processId,
      sourceId,
      targetId,
    );
  }

  @Get('cache/status/:processId')
  @ApiOperation({ summary: '获取缓存状态' })
  @ApiResponse({ status: 200, description: '获取缓存状态成功' })
  async getCacheStatus(@Param('processId') processId: string) {
    return this.cacheService.getCacheStats(+processId);
  }

  @Delete('cache/:processId')
  @ApiOperation({ summary: '清除流程缓存' })
  @ApiResponse({ status: 200, description: '缓存清除成功' })
  async clearProcessCache(@Param('processId') processId: string) {
    await this.cacheService.clearProcessCache(+processId);
    return { message: '缓存已清除' };
  }

  @Delete('cache/:processId/:analysisType')
  @ApiOperation({ summary: '清除特定分析类型的缓存' })
  @ApiResponse({ status: 200, description: '缓存清除成功' })
  async clearAnalysisCache(
    @Param('processId') processId: string,
    @Param('analysisType') analysisType: AnalysisType,
  ) {
    await this.cacheService.deleteAnalysisResult(+processId, analysisType);
    return { message: '缓存已清除' };
  }

  @Get('cache/hash/:processId')
  @ApiOperation({ summary: '获取数据源哈希值' })
  @ApiResponse({ status: 200, description: '获取哈希值成功' })
  async getDataSourceHash(@Param('processId') processId: string) {
    const hash = await this.cacheService.getDataSourceHash(+processId);
    return { processId: +processId, hash };
  }

  @Post('subprocess-discovery/:processId')
  @ApiOperation({ summary: '子流程自动发掘' })
  @ApiResponse({ status: 200, description: '子流程发掘成功' })
  async discoverSubprocesses(
    @Param('processId') processId: string,
    @Body() options?: SubprocessDiscoveryOptionsDto,
  ) {
    return this.subprocessDiscoveryService.discoverSubprocesses(
      +processId,
      options || {},
    );
  }

  @Post('hierarchical-dfg/:processId')
  @ApiOperation({ summary: '生成层次化DFG' })
  @ApiResponse({ status: 200, description: '层次化DFG生成成功' })
  async generateHierarchicalDFG(
    @Param('processId') processId: string,
    @Body() options?: SubprocessDiscoveryOptionsDto,
  ) {
    const result = await this.subprocessDiscoveryService.discoverSubprocesses(
      +processId,
      options || {},
    );
    return {
      hierarchicalDFG: result.hierarchicalDFG,
      statistics: result.statistics,
    };
  }

  @Get('subprocess-patterns/:processId')
  @ApiOperation({ summary: '获取子流程模式列表' })
  @ApiResponse({ status: 200, description: '获取子流程模式成功' })
  async getSubprocessPatterns(@Param('processId') processId: string) {
    const result =
      await this.subprocessDiscoveryService.discoverSubprocesses(+processId);
    return {
      patterns: result.subprocesses,
      statistics: {
        totalPatterns: result.subprocesses.length,
        byType: result.subprocesses.reduce(
          (acc, pattern) => {
            acc[pattern.type] = (acc[pattern.type] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>,
        ),
      },
    };
  }

  @Get('test-hierarchical-inference/:processId')
  @ApiOperation({ summary: '测试多层级嵌套流程挖掘的前置活动推断逻辑' })
  @ApiResponse({
    status: 200,
    description: '测试完成',
    schema: {
      type: 'object',
      properties: {
        totalEvents: { type: 'number', description: '总事件数' },
        eventsNeedingInference: {
          type: 'number',
          description: '需要推断前置活动的事件数',
        },
        successfulInferences: {
          type: 'number',
          description: '成功推断的事件数',
        },
        failedInferences: { type: 'number', description: '推断失败的事件数' },
        successRate: { type: 'number', description: '成功率（百分比）' },
        totalRelations: { type: 'number', description: '构建的关系总数' },
        topRelations: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              relation: { type: 'string', description: '关系描述' },
              frequency: { type: 'number', description: '频率' },
            },
          },
          description: '频率最高的10个关系',
        },
      },
    },
  })
  async testHierarchicalInference(@Param('processId') processId: string) {
    const result =
      await this.processMiningService.testHierarchicalPreviousActivityInference(
        +processId,
      );
    return {
      message: '多层级前置活动推断测试完成',
      data: result,
    };
  }

  @Get('test-cross-level-optimization/:processId')
  @ApiOperation({ summary: '测试跨层级连接优化效果' })
  @ApiResponse({
    status: 200,
    description: '测试完成',
    schema: {
      type: 'object',
      properties: {
        totalEvents: { type: 'number', description: '总事件数' },
        totalRelations: { type: 'number', description: '构建的关系总数' },
        crossLevelConnections: {
          type: 'number',
          description: '跨层级连接总数',
        },
        startToMainProcess: {
          type: 'number',
          description: '开始节点到主流程的连接数',
        },
        startToSubprocess: {
          type: 'number',
          description: '开始节点到子流程的连接数',
        },
        subprocessEndConnections: {
          type: 'number',
          description: '子流程结束到主流程的连接数',
        },
        hasRedundantConnections: {
          type: 'boolean',
          description: '是否存在冗余连接',
        },
        connectionDetails: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              relation: { type: 'string', description: '关系描述' },
              frequency: { type: 'number', description: '频率' },
              isSubprocess: {
                type: 'boolean',
                description: '是否为子流程连接',
              },
            },
          },
          description: '连接详情',
        },
        subprocessEndDetails: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              relation: { type: 'string', description: '关系描述' },
              frequency: { type: 'number', description: '频率' },
              type: { type: 'string', description: '连接类型' },
            },
          },
          description: '子流程结束连接详情',
        },
      },
    },
  })
  async testCrossLevelOptimization(@Param('processId') processId: string) {
    const result =
      await this.processMiningService.testCrossLevelConnectionOptimization(
        +processId,
      );
    return {
      message: '跨层级连接优化测试完成',
      data: result,
    };
  }
}
