import { Injectable, Inject, Logger } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

export interface DiscoveryStatus {
  discoveryId: string;
  processId: number;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  message: string;
  startTime: Date;
  endTime?: Date;
  error?: string;
  currentStepId?: string;
  failedStepId?: string;
  phase: 'parse' | 'validate' | 'save' | 'discover';
  phaseError?: string;
  validationErrors?: string[];
  result?: any;
}

@Injectable()
export class DiscoveryStatusService {
  private readonly logger = new Logger(DiscoveryStatusService.name);
  private readonly statusKeyPrefix = 'discovery:status';
  private readonly cancelKeyPrefix = 'discovery:cancel';
  private readonly abortControllers = new Map<string, AbortController>();

  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  /**
   * 创建新的发现任务
   */
  async createDiscovery(processId: number): Promise<string> {
    const discoveryId = `discovery_${processId}_${Date.now()}`;

    const status: DiscoveryStatus = {
      discoveryId,
      processId,
      status: 'processing',
      progress: 0,
      message: '开始流程发现',
      startTime: new Date(),
      phase: 'parse',
      currentStepId: 'parse',
    };

    await this.setStatus(discoveryId, status);
    this.logger.log(`Created discovery: ${discoveryId}`);

    return discoveryId;
  }

  /**
   * 开始处理
   */
  async startProcessing(
    discoveryId: string,
    abortController?: AbortController,
  ): Promise<void> {
    const status = await this.getStatus(discoveryId);
    if (!status) {
      throw new Error(`Discovery not found: ${discoveryId}`);
    }

    status.status = 'processing';
    status.message = '正在进行流程发现';
    status.progress = 0;

    await this.setStatus(discoveryId, status);

    // 存储 AbortController
    if (abortController) {
      this.abortControllers.set(discoveryId, abortController);
    }

    this.logger.log(`Started processing: ${discoveryId}`);
  }

  /**
   * 更新进度
   */
  async updateProgress(
    discoveryId: string,
    progress: number,
    message?: string,
  ): Promise<void> {
    const status = await this.getStatus(discoveryId);
    if (!status || status.status !== 'processing') {
      return;
    }

    status.progress = Math.min(100, Math.max(0, progress));
    if (message) {
      status.message = message;
    }

    await this.setStatus(discoveryId, status);
    this.logger.debug(`Updated progress for ${discoveryId}: ${progress}%`);
  }

  /**
   * 完成处理
   */
  async completeDiscovery(discoveryId: string, result: any): Promise<void> {
    const status = await this.getStatus(discoveryId);
    if (!status) {
      return;
    }

    status.status = 'completed';
    status.progress = 100;
    status.message = '流程发现完成';
    status.endTime = new Date();
    status.result = result;

    await this.setStatus(discoveryId, status);
    this.abortControllers.delete(discoveryId);

    this.logger.log(`Completed discovery: ${discoveryId}`);
  }

  /**
   * 更新处理阶段
   */
  async updatePhase(
    discoveryId: string,
    phase: 'parse' | 'validate' | 'save' | 'discover',
    progress: number,
    message?: string,
  ): Promise<void> {
    const status = await this.getStatus(discoveryId);
    if (!status) {
      return;
    }

    status.phase = phase;
    status.currentStepId = phase;
    status.progress = progress;

    if (message) {
      status.message = message;
    } else {
      const phaseMessages = {
        parse: '正在解析文件',
        validate: '正在验证数据',
        save: '正在保存数据',
        discover: '正在进行流程发现',
      };
      status.message = phaseMessages[phase];
    }

    await this.setStatus(discoveryId, status);
    this.logger.log(`Updated phase: ${discoveryId} - ${phase} (${progress}%)`);
  }

  /**
   * 失败处理
   */
  async failDiscovery(
    discoveryId: string,
    error: string,
    phase: 'parse' | 'validate' | 'save' | 'discover',
    validationErrors?: string[],
  ): Promise<void> {
    const status = await this.getStatus(discoveryId);
    if (!status) {
      return;
    }

    status.status = 'failed';
    status.endTime = new Date();
    status.error = error;
    status.phase = phase;
    status.failedStepId = phase;
    status.phaseError = error;
    status.validationErrors = validationErrors;

    const phaseMessages = {
      parse: '文件解析失败',
      validate: '数据验证失败',
      save: '数据保存失败',
      discover: '流程发现失败',
    };
    status.message = phaseMessages[phase];

    await this.setStatus(discoveryId, status);
    this.abortControllers.delete(discoveryId);

    this.logger.log(
      `Failed discovery: ${discoveryId} - ${error} (phase: ${phase})`,
    );
  }

  /**
   * 合并更新结果字段（用于处理中阶段追加统计信息等）
   */
  async mergeResult(discoveryId: string, patch: any): Promise<void> {
    const status = await this.getStatus(discoveryId);
    if (!status) return;
    const prevStats = (status.result && status.result.statistics) || {};
    const nextStats = (patch && patch.statistics) || {};
    status.result = {
      ...(status.result || {}),
      ...patch,
      statistics: { ...prevStats, ...nextStats },
    };
    await this.setStatus(discoveryId, status);
  }

  /**
   * 取消处理
   */
  async cancelDiscovery(discoveryId: string): Promise<boolean> {
    try {
      // 设置取消标记
      const cancelKey = `${this.cancelKeyPrefix}:${discoveryId}`;
      await this.cacheManager.set(cancelKey, true, 3600);

      // 更新状态
      const status = await this.getStatus(discoveryId);
      if (status) {
        status.status = 'cancelled';
        status.message = '任务已被取消';
        status.endTime = new Date();
        await this.setStatus(discoveryId, status);
      }

      // 中断请求
      const abortController = this.abortControllers.get(discoveryId);
      if (abortController) {
        abortController.abort();
        this.abortControllers.delete(discoveryId);
        this.logger.log(`Aborted request for discovery: ${discoveryId}`);
      }

      this.logger.log(`Cancelled discovery: ${discoveryId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to cancel discovery ${discoveryId}:`, error);
      return false;
    }
  }

  /**
   * 检查是否被取消
   */
  async isCancelled(discoveryId: string): Promise<boolean> {
    const cancelKey = `${this.cancelKeyPrefix}:${discoveryId}`;
    const cancelled = await this.cacheManager.get(cancelKey);
    return !!cancelled;
  }

  /**
   * 获取状态
   */
  async getStatus(discoveryId: string): Promise<DiscoveryStatus | null> {
    try {
      const statusKey = `${this.statusKeyPrefix}:${discoveryId}`;
      this.logger.debug(`Getting status for key: ${statusKey}`);

      const status = await this.cacheManager.get<DiscoveryStatus>(statusKey);

      if (status) {
        this.logger.debug(
          `Status found for ${discoveryId}: ${status.status} (${status.progress}%)`,
        );
        return status;
      } else {
        this.logger.warn(
          `No status found for discovery: ${discoveryId} (key: ${statusKey})`,
        );
        return null;
      }
    } catch (error) {
      this.logger.error(`Failed to get status for ${discoveryId}:`, error);
      return null;
    }
  }

  /**
   * 设置状态
   */
  private async setStatus(
    discoveryId: string,
    status: DiscoveryStatus,
  ): Promise<void> {
    try {
      const statusKey = `${this.statusKeyPrefix}:${discoveryId}`;
      // 根据状态设置不同的TTL
      let ttl = 3600; // 默认1小时
      if (
        status.status === 'completed' ||
        status.status === 'failed' ||
        status.status === 'cancelled'
      ) {
        ttl = 7200; // 完成/失败/取消状态保留2小时
      }

      this.logger.debug(
        `Setting status for ${discoveryId}: ${status.status} (${status.progress}%) with TTL ${ttl}s`,
      );

      await this.cacheManager.set(statusKey, status, ttl * 1000); // cache-manager expects milliseconds

      this.logger.debug(`Status successfully set for ${discoveryId}`);
    } catch (error) {
      this.logger.error(`Failed to set status for ${discoveryId}:`, error);
      throw error; // 重新抛出错误，让调用者知道设置失败
    }
  }

  /**
   * 清理发现任务
   */
  async cleanupDiscovery(discoveryId: string): Promise<void> {
    const statusKey = `${this.statusKeyPrefix}:${discoveryId}`;
    const cancelKey = `${this.cancelKeyPrefix}:${discoveryId}`;

    await this.cacheManager.del(statusKey);
    await this.cacheManager.del(cancelKey);
    this.abortControllers.delete(discoveryId);

    this.logger.log(`Cleaned up discovery: ${discoveryId}`);
  }

  /**
   * 检查发现是否存在
   */
  async exists(discoveryId: string): Promise<boolean> {
    const status = await this.getStatus(discoveryId);
    return !!status;
  }
}
