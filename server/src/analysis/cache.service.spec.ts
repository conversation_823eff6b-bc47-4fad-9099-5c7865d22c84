import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CacheService } from './cache.service';
import {
  AnalysisResult,
  AnalysisType,
} from '../entities/analysis-result.entity';
import { EventLog } from '../entities/event-log.entity';
import * as crypto from 'crypto';

describe('CacheService', () => {
  let service: CacheService;
  let analysisResultRepository: Repository<AnalysisResult>;
  let eventLogRepository: Repository<EventLog>;

  const mockAnalysisResultRepository = {
    findOne: jest.fn(),
    save: jest.fn(),
    delete: jest.fn(),
    find: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
      delete: jest.fn().mockReturnThis(),
      execute: jest.fn(),
    })),
  };

  const mockEventLogRepository = {
    find: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      getRawOne: jest.fn(),
    })),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CacheService,
        {
          provide: getRepositoryToken(AnalysisResult),
          useValue: mockAnalysisResultRepository,
        },
        {
          provide: getRepositoryToken(EventLog),
          useValue: mockEventLogRepository,
        },
      ],
    }).compile();

    service = module.get<CacheService>(CacheService);
    analysisResultRepository = module.get<Repository<AnalysisResult>>(
      getRepositoryToken(AnalysisResult),
    );
    eventLogRepository = module.get<Repository<EventLog>>(
      getRepositoryToken(EventLog),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getAnalysisResult', () => {
    it('should get cached analysis result', async () => {
      const processId = 1;
      const analysisType = AnalysisType.PROCESS_DISCOVERY;
      const mockResult = {
        id: 1,
        processId,
        analysisType,
        resultData: { nodes: [], edges: [] },
        status: 'completed',
        expiresAt: new Date(Date.now() + 3600000), // 1 hour from now
      };

      mockAnalysisResultRepository.findOne.mockResolvedValue(mockResult);

      const result = await service.getAnalysisResult(processId, analysisType);

      expect(result).toEqual(mockResult);
      expect(mockAnalysisResultRepository.findOne).toHaveBeenCalledWith({
        where: {
          processId,
          analysisType,
          status: 'completed',
        },
        order: { version: 'DESC' },
      });
    });

    it('should return null for expired cache', async () => {
      const processId = 1;
      const analysisType = AnalysisType.PROCESS_DISCOVERY;
      const expiredResult = {
        id: 1,
        processId,
        analysisType,
        resultData: { nodes: [], edges: [] },
        status: 'completed',
        expiresAt: new Date(Date.now() - 3600000), // 1 hour ago
      };

      mockAnalysisResultRepository.findOne.mockResolvedValue(expiredResult);

      const result = await service.getAnalysisResult(processId, analysisType);

      expect(result).toBeNull();
    });

    it('should return null when no cache found', async () => {
      const processId = 999;
      const analysisType = AnalysisType.PROCESS_DISCOVERY;

      mockAnalysisResultRepository.findOne.mockResolvedValue(null);

      const result = await service.getAnalysisResult(processId, analysisType);

      expect(result).toBeNull();
    });
  });

  describe('setAnalysisResult', () => {
    it('should save analysis result to cache', async () => {
      const processId = 1;
      const analysisType = AnalysisType.PROCESS_DISCOVERY;
      const data = {
        resultData: { nodes: [], edges: [] },
        version: 1,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
        dataSourceHash: 'hash123',
        metadata: { test: true },
      };

      const mockSavedResult = { id: 1, ...data };
      mockAnalysisResultRepository.save.mockResolvedValue(mockSavedResult);

      const result = await service.setAnalysisResult(
        processId,
        analysisType,
        data,
      );

      expect(result).toEqual(mockSavedResult);
      expect(mockAnalysisResultRepository.save).toHaveBeenCalled();
    });
  });

  describe('deleteAnalysisResult', () => {
    it('should delete analysis result from cache', async () => {
      const processId = 1;
      const analysisType = AnalysisType.PROCESS_DISCOVERY;

      mockAnalysisResultRepository
        .createQueryBuilder()
        .execute.mockResolvedValue({ affected: 1 });

      await service.deleteAnalysisResult(processId, analysisType);

      expect(
        mockAnalysisResultRepository.createQueryBuilder,
      ).toHaveBeenCalled();
    });
  });

  describe('clearProcessCache', () => {
    it('should clear all cache for a process', async () => {
      const processId = 1;

      mockAnalysisResultRepository
        .createQueryBuilder()
        .execute.mockResolvedValue({ affected: 3 });

      await service.clearProcessCache(processId);

      expect(
        mockAnalysisResultRepository.createQueryBuilder,
      ).toHaveBeenCalled();
    });
  });

  describe('getCacheStats', () => {
    it('should return cache statistics', async () => {
      const processId = 1;
      const mockResults = [
        { analysisType: AnalysisType.PROCESS_DISCOVERY, status: 'completed' },
        { analysisType: AnalysisType.VARIANT_ANALYSIS, status: 'completed' },
      ];

      mockAnalysisResultRepository.find.mockResolvedValue(mockResults);

      const result = await service.getCacheStats(processId);

      expect(result).toHaveProperty('totalCached');
      expect(result).toHaveProperty('byType');
      expect(result.totalCached).toBe(2);
      expect(result.byType.process_discovery).toBe(true);
      expect(result.byType.variant_analysis).toBe(true);
    });

    it('should return empty stats for process with no cache', async () => {
      const processId = 999;

      mockAnalysisResultRepository.find.mockResolvedValue([]);

      const result = await service.getCacheStats(processId);

      expect(result.totalCached).toBe(0);
      expect(result.byType.process_discovery).toBe(false);
    });
  });

  describe('getDataSourceHash', () => {
    it('should generate data source hash', async () => {
      const processId = 1;
      const mockEventLogs = [
        { id: 1, caseId: 'case1', activity: 'Start', timestamp: new Date() },
        { id: 2, caseId: 'case1', activity: 'End', timestamp: new Date() },
      ];

      mockEventLogRepository.find.mockResolvedValue(mockEventLogs);

      const result = await service.getDataSourceHash(processId);

      expect(typeof result).toBe('string');
      expect(result.length).toBe(64); // SHA-256 hash length
    });

    it('should return consistent hash for same data', async () => {
      const processId = 1;
      const mockEventLogs = [
        {
          id: 1,
          caseId: 'case1',
          activity: 'Start',
          timestamp: new Date('2024-01-01'),
        },
      ];

      mockEventLogRepository.find.mockResolvedValue(mockEventLogs);

      const hash1 = await service.getDataSourceHash(processId);
      const hash2 = await service.getDataSourceHash(processId);

      expect(hash1).toBe(hash2);
    });
  });

  describe('isDataSourceChanged', () => {
    it('should detect data source changes', async () => {
      const processId = 1;
      const oldHash = 'old-hash';
      const newHash = 'new-hash';

      mockEventLogRepository.find.mockResolvedValue([
        { id: 1, caseId: 'case1', activity: 'Start', timestamp: new Date() },
      ]);

      // Mock crypto.createHash to return predictable hash
      jest.spyOn(crypto, 'createHash').mockReturnValue({
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValue(newHash),
      } as any);

      const result = await service.isDataSourceChanged(processId, oldHash);

      expect(result).toBe(true);
    });

    it('should return false when data source unchanged', async () => {
      const processId = 1;
      const sameHash = 'same-hash';

      mockEventLogRepository.find.mockResolvedValue([
        { id: 1, caseId: 'case1', activity: 'Start', timestamp: new Date() },
      ]);

      // Mock crypto.createHash to return same hash
      jest.spyOn(crypto, 'createHash').mockReturnValue({
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValue(sameHash),
      } as any);

      const result = await service.isDataSourceChanged(processId, sameHash);

      expect(result).toBe(false);
    });
  });
});
