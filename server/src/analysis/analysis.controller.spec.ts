import { Test, TestingModule } from '@nestjs/testing';
import { AnalysisController } from './analysis.controller';
import { DataProcessingService } from './data-processing.service';
import { ProcessMiningService } from './process-mining.service';
import { CacheService } from './cache.service';
import { SubprocessDiscoveryService } from './subprocess-discovery.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AnalysisType } from '../entities/analysis-result.entity';
import {
  mockProcessStatistics,
  mockDFGResult,
  mockAnalysisResults,
  mockUploadDataDto,
} from '../test-utils/mock-data';

describe('AnalysisController', () => {
  let controller: AnalysisController;
  let dataProcessingService: DataProcessingService;
  let processMiningService: ProcessMiningService;
  let cacheService: CacheService;
  let subprocessDiscoveryService: SubprocessDiscoveryService;

  const mockDataProcessingService = {
    parseFile: jest.fn(),
    validateData: jest.fn(),
    saveEventLogs: jest.fn(),
    getDataStatistics: jest.fn(),
  };

  const mockProcessMiningService = {
    discoverProcess: jest.fn(),
    analyzeVariants: jest.fn(),
    analyzePerformance: jest.fn(),
    getAnalysisResult: jest.fn(),
  };

  const mockCacheService = {
    getCacheStats: jest.fn(),
    clearProcessCache: jest.fn(),
    deleteAnalysisResult: jest.fn(),
    getDataSourceHash: jest.fn(),
  };

  const mockSubprocessDiscoveryService = {
    discoverSubprocesses: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AnalysisController],
      providers: [
        {
          provide: DataProcessingService,
          useValue: mockDataProcessingService,
        },
        {
          provide: ProcessMiningService,
          useValue: mockProcessMiningService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
        {
          provide: SubprocessDiscoveryService,
          useValue: mockSubprocessDiscoveryService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<AnalysisController>(AnalysisController);
    dataProcessingService = module.get<DataProcessingService>(
      DataProcessingService,
    );
    processMiningService =
      module.get<ProcessMiningService>(ProcessMiningService);
    cacheService = module.get<CacheService>(CacheService);
    subprocessDiscoveryService = module.get<SubprocessDiscoveryService>(
      SubprocessDiscoveryService,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('uploadFile', () => {
    it('should upload and process file successfully', async () => {
      const mockFile = {
        originalname: 'test.csv',
        path: '/tmp/test.csv',
        buffer: Buffer.from('test data'),
        size: 100,
      } as Express.Multer.File;

      const mockUploadDto = mockUploadDataDto;
      const mockRequest = { user: { id: 1 } };

      const mockData = [
        { case_id: '1', activity: 'Start', timestamp: '2024-01-01' },
        { case_id: '1', activity: 'End', timestamp: '2024-01-02' },
      ];

      const mockValidation = {
        isValid: true,
        errors: [],
        warnings: [],
        totalRows: 2,
        validRows: 2,
        preview: mockData,
      };

      const mockEventLogs = [{ id: 1 }, { id: 2 }];

      mockDataProcessingService.parseFile.mockResolvedValue(mockData);
      mockDataProcessingService.validateData.mockReturnValue(mockValidation);
      mockDataProcessingService.saveEventLogs.mockResolvedValue(mockEventLogs);

      // Mock fs.unlinkSync
      const fs = require('fs');
      jest.spyOn(fs, 'unlinkSync').mockImplementation(() => {});

      const result = await controller.uploadFile(
        mockFile,
        mockUploadDto,
        mockRequest,
      );

      expect(dataProcessingService.parseFile).toHaveBeenCalledWith(
        '/tmp/test.csv',
        'csv',
      );
      expect(dataProcessingService.validateData).toHaveBeenCalledWith(
        mockData,
        mockUploadDto,
      );
      expect(dataProcessingService.saveEventLogs).toHaveBeenCalledWith(
        mockData,
        mockUploadDto,
      );
      expect(result).toEqual({
        success: true,
        message: '数据上传成功',
        validation: mockValidation,
        savedRecords: 2,
      });
    });

    it('should handle validation errors', async () => {
      const mockFile = {
        originalname: 'test.csv',
        path: '/tmp/test.csv',
        buffer: Buffer.from('test data'),
        size: 100,
      } as Express.Multer.File;

      const mockUploadDto = mockUploadDataDto;
      const mockRequest = { user: { id: 1 } };

      const mockData = [{ invalid: 'data' }];
      const mockValidation = {
        isValid: false,
        errors: ['缺少必需字段: case_id'],
        warnings: [],
        totalRows: 1,
        validRows: 0,
        preview: mockData,
      };

      mockDataProcessingService.parseFile.mockResolvedValue(mockData);
      mockDataProcessingService.validateData.mockReturnValue(mockValidation);

      // Mock fs.unlinkSync
      const fs = require('fs');
      jest.spyOn(fs, 'unlinkSync').mockImplementation(() => {});

      const result = await controller.uploadFile(
        mockFile,
        mockUploadDto,
        mockRequest,
      );

      expect(result).toEqual({
        success: false,
        validation: mockValidation,
      });
    });
  });

  describe('validateFile', () => {
    it('should validate file successfully', async () => {
      const mockFile = {
        originalname: 'test.csv',
        path: '/tmp/test.csv',
        buffer: Buffer.from('test data'),
        size: 100,
      } as Express.Multer.File;

      const mockUploadDto = mockUploadDataDto;

      const mockData = [
        { case_id: '1', activity: 'Start', timestamp: '2024-01-01' },
      ];

      const mockValidation = {
        isValid: true,
        errors: [],
        warnings: [],
        totalRows: 1,
        validRows: 1,
        preview: mockData,
      };

      mockDataProcessingService.parseFile.mockResolvedValue(mockData);
      mockDataProcessingService.validateData.mockReturnValue(mockValidation);

      // Mock fs.unlinkSync
      const fs = require('fs');
      jest.spyOn(fs, 'unlinkSync').mockImplementation(() => {});

      const result = await controller.validateFile(mockFile, mockUploadDto);

      expect(dataProcessingService.parseFile).toHaveBeenCalledWith(
        '/tmp/test.csv',
        'csv',
      );
      expect(dataProcessingService.validateData).toHaveBeenCalledWith(
        mockData,
        mockUploadDto,
      );
      expect(result).toEqual({
        validation: mockValidation,
        fileName: 'test.csv',
        fileSize: 100,
      });
    });
  });

  describe('getStatistics', () => {
    it('should return process statistics', async () => {
      mockDataProcessingService.getDataStatistics.mockResolvedValue(
        mockProcessStatistics,
      );

      const result = await controller.getStatistics('1');

      expect(dataProcessingService.getDataStatistics).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockProcessStatistics);
    });
  });

  describe('discoverProcess', () => {
    it('should discover process successfully', async () => {
      mockProcessMiningService.discoverProcess.mockResolvedValue(mockDFGResult);

      const result = await controller.discoverProcess('1');

      expect(processMiningService.discoverProcess).toHaveBeenCalledWith(
        1,
        false,
      );
      expect(result).toEqual(mockDFGResult);
    });
  });

  describe('analyzeVariants', () => {
    it('should analyze variants successfully', async () => {
      const mockVariantResult = {
        variants: [
          { path: ['开始', '审核', '完成'], frequency: 80, percentage: 80 },
          { path: ['开始', '完成'], frequency: 20, percentage: 20 },
        ],
        totalCases: 100,
      };

      mockProcessMiningService.analyzeVariants.mockResolvedValue(
        mockVariantResult,
      );

      const result = await controller.analyzeVariants('1');

      expect(processMiningService.analyzeVariants).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockVariantResult);
    });
  });

  describe('analyzePerformance', () => {
    it('should analyze performance successfully', async () => {
      const mockPerformanceResult = {
        avgCaseDuration: 3600000,
        avgActivityDuration: {
          开始: 300000,
          审核: 1800000,
          完成: 300000,
        },
        bottlenecks: ['审核'],
      };

      mockProcessMiningService.analyzePerformance.mockResolvedValue(
        mockPerformanceResult,
      );

      const result = await controller.analyzePerformance('1');

      expect(processMiningService.analyzePerformance).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockPerformanceResult);
    });
  });

  describe('getAnalysisResult', () => {
    it('should get analysis result successfully', async () => {
      const mockResult = mockAnalysisResults[0];
      mockProcessMiningService.getAnalysisResult.mockResolvedValue(mockResult);

      const result = await controller.getAnalysisResult(
        '1',
        AnalysisType.PROCESS_DISCOVERY,
      );

      expect(processMiningService.getAnalysisResult).toHaveBeenCalledWith(
        1,
        AnalysisType.PROCESS_DISCOVERY,
      );
      expect(result).toEqual(mockResult.resultData);
    });

    it('should return null when no result found', async () => {
      mockProcessMiningService.getAnalysisResult.mockResolvedValue(null);

      const result = await controller.getAnalysisResult(
        '999',
        AnalysisType.PROCESS_DISCOVERY,
      );

      expect(processMiningService.getAnalysisResult).toHaveBeenCalledWith(
        999,
        AnalysisType.PROCESS_DISCOVERY,
      );
      expect(result).toBeNull();
    });
  });
});
