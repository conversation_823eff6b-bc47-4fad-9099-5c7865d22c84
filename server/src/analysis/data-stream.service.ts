import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as csv from 'csv-parser';
import * as fs from 'fs';
import * as moment from 'moment';
import { EventLog } from '../entities/event-log.entity';
import { UploadDataDto } from './dto';

export interface StreamProcessingResult {
  totalProcessed: number;
  validRows: number;
  errors: string[];
  warnings: string[];
  preview: any[];
}

@Injectable()
export class DataStreamService {
  constructor(
    @InjectRepository(EventLog)
    private readonly eventLogRepository: Repository<EventLog>,
  ) {}

  /**
   * 流式处理大型CSV文件
   */
  async processLargeCSV(
    filePath: string,
    uploadDto: UploadDataDto,
  ): Promise<StreamProcessingResult> {
    const {
      processId,
      caseIdField,
      activityField,
      timestampField,
      resourceField,
      costField,
      activityIdField,
      previousActivityField,
      endTimestampField,
      clearExistingData,
    } = uploadDto;

    // 如果需要清空已存在的数据
    if (clearExistingData) {
      await this.eventLogRepository.delete({ processId });
      console.log(`已清空流程 ${processId} 下的所有事件日志数据`);
    }

    return new Promise((resolve, reject) => {
      const result: StreamProcessingResult = {
        totalProcessed: 0,
        validRows: 0,
        errors: [],
        warnings: [],
        preview: [],
      };

      const batchSize = 1000; // 批处理大小
      const maxRows = 100000; // 最大行数限制
      const maxErrors = 100; // 最大错误数限制

      let batch: any[] = [];
      let rowCount = 0;
      const isFirstRow = true;
      let headers: string[] = [];

      const stream = fs
        .createReadStream(filePath)
        .pipe(csv())
        .on('headers', (headerList) => {
          headers = headerList;

          // 验证必需字段
          if (!headers.includes(caseIdField)) {
            return reject(
              new BadRequestException(`缺少必需字段: ${caseIdField}`),
            );
          }
          if (!headers.includes(activityField)) {
            return reject(
              new BadRequestException(`缺少必需字段: ${activityField}`),
            );
          }
          if (!headers.includes(timestampField)) {
            return reject(
              new BadRequestException(`缺少必需字段: ${timestampField}`),
            );
          }
        })
        .on('data', async (data) => {
          rowCount++;
          result.totalProcessed++;

          // 检查行数限制
          if (rowCount > maxRows) {
            stream.destroy();
            return reject(
              new BadRequestException(`文件行数超过限制 (${maxRows} 行)`),
            );
          }

          // 保存前几行作为预览
          if (result.preview.length < 5) {
            result.preview.push(data);
          }

          // 验证行数据
          const validationError = this.validateRow(data, uploadDto, rowCount);
          if (validationError) {
            result.errors.push(validationError);

            // 如果错误太多，停止处理
            if (result.errors.length > maxErrors) {
              stream.destroy();
              return reject(
                new BadRequestException('错误数量过多，请检查数据格式'),
              );
            }
            return;
          }

          // 转换为EventLog实体
          const eventLog = this.createEventLog(data, uploadDto);
          batch.push(eventLog);
          result.validRows++;

          // 批量保存
          if (batch.length >= batchSize) {
            stream.pause(); // 暂停流

            try {
              await this.eventLogRepository.save(batch);
              batch = [];

              // 强制垃圾回收
              if (global.gc) {
                global.gc();
              }

              stream.resume(); // 恢复流
            } catch (error) {
              stream.destroy();
              return reject(
                new BadRequestException(`数据保存失败: ${error.message}`),
              );
            }
          }
        })
        .on('end', async () => {
          try {
            // 保存剩余的批次
            if (batch.length > 0) {
              await this.eventLogRepository.save(batch);
            }

            resolve(result);
          } catch (error) {
            reject(
              new BadRequestException(`最终数据保存失败: ${error.message}`),
            );
          }
        })
        .on('error', (error) => {
          reject(new BadRequestException(`文件处理失败: ${error.message}`));
        });
    });
  }

  /**
   * 验证单行数据
   */
  private validateRow(
    row: any,
    uploadDto: UploadDataDto,
    rowIndex: number,
  ): string | null {
    const {
      caseIdField,
      activityField,
      timestampField,
      costField,
      endTimestampField,
    } = uploadDto;

    // 检查必需字段
    if (!row[caseIdField] || row[caseIdField].toString().trim() === '') {
      return `第${rowIndex}行: ${caseIdField} 不能为空`;
    }
    if (!row[activityField] || row[activityField].toString().trim() === '') {
      return `第${rowIndex}行: ${activityField} 不能为空`;
    }
    if (!row[timestampField] || row[timestampField].toString().trim() === '') {
      return `第${rowIndex}行: ${timestampField} 不能为空`;
    }

    // 验证时间戳格式
    if (!this.isValidTimestamp(row[timestampField])) {
      return `第${rowIndex}行: ${timestampField} 时间格式无效`;
    }

    // 验证成本字段（如果存在）
    if (costField && row[costField] && isNaN(Number(row[costField]))) {
      return `第${rowIndex}行: ${costField} 不是有效数字`;
    }

    // 验证活动结束时间字段（如果存在）
    if (endTimestampField && row[endTimestampField]) {
      if (!this.isValidTimestamp(row[endTimestampField])) {
        return `第${rowIndex}行: ${endTimestampField} 时间格式无效`;
      }
    }

    return null;
  }

  /**
   * 验证时间戳格式是否有效
   */
  private isValidTimestamp(value: any): boolean {
    if (!value) return false;

    // 如果是数字，可能是Excel日期序列号或时间戳
    const numValue = Number(value);
    if (!isNaN(numValue)) {
      // Excel日期序列号
      if (numValue > 1 && numValue < 2958466) {
        try {
          const excelDate = new Date((numValue - 25569) * 86400 * 1000);
          const momentDate = moment(excelDate);
          return (
            momentDate.isValid() &&
            momentDate.year() >= 1900 &&
            momentDate.year() <= 2100
          );
        } catch {
          return false;
        }
      }

      // Unix时间戳
      if (numValue > 946684800 && numValue < 4102444800) {
        try {
          let momentDate: moment.Moment;
          if (numValue < 10000000000) {
            // 秒时间戳
            momentDate = moment.unix(numValue);
          } else {
            // 毫秒时间戳
            momentDate = moment(numValue);
          }
          return (
            momentDate.isValid() &&
            momentDate.year() >= 1970 &&
            momentDate.year() <= 2100
          );
        } catch {
          return false;
        }
      }
    }

    // 如果是字符串，使用 moment.js 尝试解析各种时间格式
    if (typeof value === 'string' && value.length > 0) {
      // 定义支持的时间格式
      const supportedFormats = [
        'YYYY-MM-DD HH:mm:ss',
        'YYYY/MM/DD HH:mm:ss',
        'MM/DD/YYYY HH:mm:ss',
        'DD/MM/YYYY HH:mm:ss',
        'YYYY-MM-DD',
        'YYYY/MM/DD',
        'MM/DD/YYYY',
        'DD/MM/YYYY',
        'YYYY-MM-DDTHH:mm:ss',
        'YYYY-MM-DDTHH:mm:ssZ',
        'YYYY-MM-DDTHH:mm:ss.SSSZ',
      ];

      // 尝试使用指定格式解析
      for (const format of supportedFormats) {
        const momentDate = moment(value, format, true); // strict parsing
        if (
          momentDate.isValid() &&
          momentDate.year() >= 1900 &&
          momentDate.year() <= 2100
        ) {
          return true;
        }
      }

      // 如果指定格式都失败，尝试 moment.js 的自动解析
      const momentDate = moment(value);
      return (
        momentDate.isValid() &&
        momentDate.year() >= 1900 &&
        momentDate.year() <= 2100
      );
    }

    return false;
  }

  /**
   * 将时间值转换为有效的Date对象
   */
  private convertToDate(value: any): Date | null {
    if (!value) return null;

    // 如果是数字，可能是Excel日期序列号或时间戳
    const numValue = Number(value);
    if (!isNaN(numValue)) {
      // Excel日期序列号
      if (numValue > 1 && numValue < 2958466) {
        try {
          const excelDate = new Date((numValue - 25569) * 86400 * 1000);
          const momentDate = moment(excelDate);
          if (
            momentDate.isValid() &&
            momentDate.year() >= 1900 &&
            momentDate.year() <= 2100
          ) {
            return momentDate.toDate();
          }
        } catch {
          // 继续尝试其他方法
        }
      }

      // Unix时间戳
      if (numValue > 946684800 && numValue < 4102444800) {
        try {
          let momentDate: moment.Moment;
          if (numValue < 10000000000) {
            // 秒时间戳
            momentDate = moment.unix(numValue);
          } else {
            // 毫秒时间戳
            momentDate = moment(numValue);
          }
          if (
            momentDate.isValid() &&
            momentDate.year() >= 1970 &&
            momentDate.year() <= 2100
          ) {
            return momentDate.toDate();
          }
        } catch {
          // 继续尝试其他方法
        }
      }
    }

    // 如果是字符串，使用 moment.js 尝试解析各种时间格式
    if (typeof value === 'string' && value.length > 0) {
      // 定义支持的时间格式
      const supportedFormats = [
        'YYYY-MM-DD HH:mm:ss',
        'YYYY/MM/DD HH:mm:ss',
        'MM/DD/YYYY HH:mm:ss',
        'DD/MM/YYYY HH:mm:ss',
        'YYYY-MM-DD',
        'YYYY/MM/DD',
        'MM/DD/YYYY',
        'DD/MM/YYYY',
        'YYYY-MM-DDTHH:mm:ss',
        'YYYY-MM-DDTHH:mm:ssZ',
        'YYYY-MM-DDTHH:mm:ss.SSSZ',
      ];

      // 尝试使用指定格式解析
      for (const format of supportedFormats) {
        const momentDate = moment(value, format, true); // strict parsing
        if (
          momentDate.isValid() &&
          momentDate.year() >= 1900 &&
          momentDate.year() <= 2100
        ) {
          return momentDate.toDate();
        }
      }

      // 如果指定格式都失败，尝试 moment.js 的自动解析
      const momentDate = moment(value);
      if (
        momentDate.isValid() &&
        momentDate.year() >= 1900 &&
        momentDate.year() <= 2100
      ) {
        return momentDate.toDate();
      }
    }

    return null;
  }

  /**
   * 创建EventLog实体
   */
  private createEventLog(row: any, uploadDto: UploadDataDto): EventLog {
    const {
      processId,
      caseIdField,
      activityField,
      timestampField,
      resourceField,
      costField,
      activityIdField,
      previousActivityField,
      endTimestampField,
      parentCaseIdField,
    } = uploadDto;

    const eventLog = new EventLog();
    eventLog.processId = processId;
    eventLog.caseId = row[caseIdField].toString().trim();
    eventLog.activity = row[activityField].toString().trim();

    // 使用容错的时间转换方法
    const timestamp = this.convertToDate(row[timestampField]);
    if (!timestamp) {
      throw new BadRequestException(`无法解析时间戳: ${row[timestampField]}`);
    }
    eventLog.timestamp = timestamp;

    if (resourceField && row[resourceField]) {
      eventLog.resource = row[resourceField].toString().trim();
    }

    if (costField && row[costField] && !isNaN(Number(row[costField]))) {
      eventLog.cost = Number(row[costField]);
    }

    if (activityIdField && row[activityIdField]) {
      eventLog.activityId = row[activityIdField].toString().trim();
    }

    if (previousActivityField && row[previousActivityField]) {
      eventLog.previousActivity = row[previousActivityField].toString().trim();
    }

    if (endTimestampField && row[endTimestampField]) {
      const endTimestamp = this.convertToDate(row[endTimestampField]);
      if (endTimestamp) {
        eventLog.endTimestamp = endTimestamp;
      }
    }

    if (parentCaseIdField && row[parentCaseIdField]) {
      eventLog.parentCaseId = row[parentCaseIdField].toString().trim();
    }

    // 保存其他属性到attributes字段
    const attributes: Record<string, any> = {};
    Object.keys(row).forEach((key) => {
      if (
        ![
          caseIdField,
          activityField,
          timestampField,
          resourceField,
          costField,
          activityIdField,
          previousActivityField,
          endTimestampField,
          parentCaseIdField,
        ].includes(key)
      ) {
        attributes[key] = row[key];
      }
    });

    if (Object.keys(attributes).length > 0) {
      eventLog.attributes = attributes;
    }

    return eventLog;
  }

  /**
   * 检查文件大小和估算行数
   */
  async estimateFileSize(filePath: string): Promise<{
    fileSizeBytes: number;
    estimatedRows: number;
    shouldUseStreaming: boolean;
  }> {
    const stats = fs.statSync(filePath);
    const fileSizeBytes = stats.size;

    // 估算行数（假设每行平均100字节）
    const estimatedRows = Math.floor(fileSizeBytes / 100);

    // 如果文件大于10MB或估算行数超过50000，使用流式处理
    const shouldUseStreaming =
      fileSizeBytes > 10 * 1024 * 1024 || estimatedRows > 50000;

    return {
      fileSizeBytes,
      estimatedRows,
      shouldUseStreaming,
    };
  }
}
