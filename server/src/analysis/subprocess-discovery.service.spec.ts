import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SubprocessDiscoveryService } from './subprocess-discovery.service';
import { EventLog } from '../entities/event-log.entity';
import { AnalysisResult } from '../entities/analysis-result.entity';
import { CacheService } from './cache.service';

describe('SubprocessDiscoveryService', () => {
  let service: SubprocessDiscoveryService;
  let eventLogRepository: Repository<EventLog>;
  let analysisResultRepository: Repository<AnalysisResult>;
  let cacheService: CacheService;

  const mockEventLogs = [
    // QM_001 - 正常流程
    {
      id: 1,
      processId: 1,
      caseId: 'QM_001',
      activity: '原材料采购申请',
      timestamp: new Date('2024-01-15 08:00:00'),
      resource: '采购经理_张明',
    },
    {
      id: 2,
      processId: 1,
      caseId: 'QM_001',
      activity: '供应商资质审核',
      timestamp: new Date('2024-01-15 09:30:00'),
      resource: '质量工程师_李华',
    },
    {
      id: 3,
      processId: 1,
      caseId: 'QM_001',
      activity: '采购合同签署',
      timestamp: new Date('2024-01-15 11:00:00'),
      resource: '采购主管_王强',
    },
    {
      id: 4,
      processId: 1,
      caseId: 'QM_001',
      activity: '原材料入库检验',
      timestamp: new Date('2024-01-16 09:00:00'),
      resource: '检验员_赵敏',
    },
    {
      id: 5,
      processId: 1,
      caseId: 'QM_001',
      activity: '入库确认',
      timestamp: new Date('2024-01-16 10:30:00'),
      resource: '仓库管理员_钱伟',
    },
    {
      id: 6,
      processId: 1,
      caseId: 'QM_001',
      activity: '生产计划制定',
      timestamp: new Date('2024-01-16 14:00:00'),
      resource: '生产计划员_孙丽',
    },
    {
      id: 7,
      processId: 1,
      caseId: 'QM_001',
      activity: '生产准备',
      timestamp: new Date('2024-01-17 08:00:00'),
      resource: '生产主管_周杰',
    },
    {
      id: 8,
      processId: 1,
      caseId: 'QM_001',
      activity: '首件检验',
      timestamp: new Date('2024-01-17 09:30:00'),
      resource: '质量检验员_吴琳',
    },
    {
      id: 9,
      processId: 1,
      caseId: 'QM_001',
      activity: '批量生产',
      timestamp: new Date('2024-01-17 10:00:00'),
      resource: '生产操作员_郑涛',
    },
    {
      id: 10,
      processId: 1,
      caseId: 'QM_001',
      activity: '过程检验',
      timestamp: new Date('2024-01-17 14:00:00'),
      resource: '质量检验员_冯雪',
    },
    {
      id: 11,
      processId: 1,
      caseId: 'QM_001',
      activity: '成品检验',
      timestamp: new Date('2024-01-18 09:00:00'),
      resource: '质量工程师_陈阳',
    },
    {
      id: 12,
      processId: 1,
      caseId: 'QM_001',
      activity: '包装',
      timestamp: new Date('2024-01-18 11:00:00'),
      resource: '包装工_褚明',
    },
    {
      id: 13,
      processId: 1,
      caseId: 'QM_001',
      activity: '出库检验',
      timestamp: new Date('2024-01-18 14:00:00'),
      resource: '出库检验员_卫强',
    },
    {
      id: 14,
      processId: 1,
      caseId: 'QM_001',
      activity: '发货',
      timestamp: new Date('2024-01-18 16:00:00'),
      resource: '物流专员_蒋丽',
    },
    {
      id: 15,
      processId: 1,
      caseId: 'QM_001',
      activity: '客户验收',
      timestamp: new Date('2024-01-19 10:00:00'),
      resource: '客户代表_沈杰',
    },

    // QM_002 - 包含不合格品处理的流程
    {
      id: 16,
      processId: 1,
      caseId: 'QM_002',
      activity: '原材料采购申请',
      timestamp: new Date('2024-01-15 10:00:00'),
      resource: '采购经理_张明',
    },
    {
      id: 17,
      processId: 1,
      caseId: 'QM_002',
      activity: '供应商资质审核',
      timestamp: new Date('2024-01-15 11:30:00'),
      resource: '质量工程师_李华',
    },
    {
      id: 18,
      processId: 1,
      caseId: 'QM_002',
      activity: '采购合同签署',
      timestamp: new Date('2024-01-15 13:00:00'),
      resource: '采购主管_王强',
    },
    {
      id: 19,
      processId: 1,
      caseId: 'QM_002',
      activity: '原材料入库检验',
      timestamp: new Date('2024-01-16 11:00:00'),
      resource: '检验员_赵敏',
    },
    {
      id: 20,
      processId: 1,
      caseId: 'QM_002',
      activity: '不合格品处理',
      timestamp: new Date('2024-01-16 13:00:00'),
      resource: '质量工程师_李华',
    },
    {
      id: 21,
      processId: 1,
      caseId: 'QM_002',
      activity: '供应商整改通知',
      timestamp: new Date('2024-01-16 15:00:00'),
      resource: '采购主管_王强',
    },
    {
      id: 22,
      processId: 1,
      caseId: 'QM_002',
      activity: '重新检验',
      timestamp: new Date('2024-01-17 09:00:00'),
      resource: '检验员_赵敏',
    },
    {
      id: 23,
      processId: 1,
      caseId: 'QM_002',
      activity: '入库确认',
      timestamp: new Date('2024-01-17 10:30:00'),
      resource: '仓库管理员_钱伟',
    },
    {
      id: 24,
      processId: 1,
      caseId: 'QM_002',
      activity: '生产计划制定',
      timestamp: new Date('2024-01-17 14:00:00'),
      resource: '生产计划员_孙丽',
    },
    {
      id: 25,
      processId: 1,
      caseId: 'QM_002',
      activity: '生产准备',
      timestamp: new Date('2024-01-18 08:00:00'),
      resource: '生产主管_周杰',
    },
    {
      id: 26,
      processId: 1,
      caseId: 'QM_002',
      activity: '首件检验',
      timestamp: new Date('2024-01-18 09:30:00'),
      resource: '质量检验员_吴琳',
    },
    {
      id: 27,
      processId: 1,
      caseId: 'QM_002',
      activity: '批量生产',
      timestamp: new Date('2024-01-18 10:00:00'),
      resource: '生产操作员_郑涛',
    },
    {
      id: 28,
      processId: 1,
      caseId: 'QM_002',
      activity: '过程检验',
      timestamp: new Date('2024-01-18 14:00:00'),
      resource: '质量检验员_冯雪',
    },
    {
      id: 29,
      processId: 1,
      caseId: 'QM_002',
      activity: '返工处理',
      timestamp: new Date('2024-01-18 16:00:00'),
      resource: '生产操作员_郑涛',
    },
    {
      id: 30,
      processId: 1,
      caseId: 'QM_002',
      activity: '重新检验',
      timestamp: new Date('2024-01-19 09:00:00'),
      resource: '质量检验员_冯雪',
    },
    {
      id: 31,
      processId: 1,
      caseId: 'QM_002',
      activity: '成品检验',
      timestamp: new Date('2024-01-19 11:00:00'),
      resource: '质量工程师_陈阳',
    },
    {
      id: 32,
      processId: 1,
      caseId: 'QM_002',
      activity: '包装',
      timestamp: new Date('2024-01-19 13:00:00'),
      resource: '包装工_褚明',
    },
    {
      id: 33,
      processId: 1,
      caseId: 'QM_002',
      activity: '出库检验',
      timestamp: new Date('2024-01-19 15:00:00'),
      resource: '出库检验员_卫强',
    },
    {
      id: 34,
      processId: 1,
      caseId: 'QM_002',
      activity: '发货',
      timestamp: new Date('2024-01-19 17:00:00'),
      resource: '物流专员_蒋丽',
    },
    {
      id: 35,
      processId: 1,
      caseId: 'QM_002',
      activity: '客户验收',
      timestamp: new Date('2024-01-20 10:00:00'),
      resource: '客户代表_沈杰',
    },

    // QM_003 - 另一个正常流程实例
    {
      id: 36,
      processId: 1,
      caseId: 'QM_003',
      activity: '原材料采购申请',
      timestamp: new Date('2024-01-16 08:00:00'),
      resource: '采购经理_张明',
    },
    {
      id: 37,
      processId: 1,
      caseId: 'QM_003',
      activity: '供应商资质审核',
      timestamp: new Date('2024-01-16 09:30:00'),
      resource: '质量工程师_李华',
    },
    {
      id: 38,
      processId: 1,
      caseId: 'QM_003',
      activity: '采购合同签署',
      timestamp: new Date('2024-01-16 13:00:00'),
      resource: '采购主管_王强',
    },
    {
      id: 39,
      processId: 1,
      caseId: 'QM_003',
      activity: '原材料入库检验',
      timestamp: new Date('2024-01-17 09:00:00'),
      resource: '检验员_赵敏',
    },
    {
      id: 40,
      processId: 1,
      caseId: 'QM_003',
      activity: '入库确认',
      timestamp: new Date('2024-01-17 14:00:00'),
      resource: '仓库管理员_钱伟',
    },
    {
      id: 41,
      processId: 1,
      caseId: 'QM_003',
      activity: '生产计划制定',
      timestamp: new Date('2024-01-17 16:00:00'),
      resource: '生产计划员_孙丽',
    },
    {
      id: 42,
      processId: 1,
      caseId: 'QM_003',
      activity: '生产准备',
      timestamp: new Date('2024-01-18 09:30:00'),
      resource: '生产主管_周杰',
    },
    {
      id: 43,
      processId: 1,
      caseId: 'QM_003',
      activity: '首件检验',
      timestamp: new Date('2024-01-18 11:00:00'),
      resource: '质量检验员_吴琳',
    },
    {
      id: 44,
      processId: 1,
      caseId: 'QM_003',
      activity: '批量生产',
      timestamp: new Date('2024-01-18 11:30:00'),
      resource: '生产操作员_郑涛',
    },
    {
      id: 45,
      processId: 1,
      caseId: 'QM_003',
      activity: '过程检验',
      timestamp: new Date('2024-01-18 15:30:00'),
      resource: '质量检验员_冯雪',
    },
    {
      id: 46,
      processId: 1,
      caseId: 'QM_003',
      activity: '成品检验',
      timestamp: new Date('2024-01-19 09:00:00'),
      resource: '质量工程师_陈阳',
    },
    {
      id: 47,
      processId: 1,
      caseId: 'QM_003',
      activity: '包装',
      timestamp: new Date('2024-01-19 11:00:00'),
      resource: '包装工_褚明',
    },
    {
      id: 48,
      processId: 1,
      caseId: 'QM_003',
      activity: '出库检验',
      timestamp: new Date('2024-01-19 13:00:00'),
      resource: '出库检验员_卫强',
    },
    {
      id: 49,
      processId: 1,
      caseId: 'QM_003',
      activity: '发货',
      timestamp: new Date('2024-01-19 16:00:00'),
      resource: '物流专员_蒋丽',
    },
    {
      id: 50,
      processId: 1,
      caseId: 'QM_003',
      activity: '客户验收',
      timestamp: new Date('2024-01-20 10:00:00'),
      resource: '客户代表_沈杰',
    },
  ];

  const mockEventLogRepository = {
    find: jest.fn().mockResolvedValue(mockEventLogs),
  };

  const mockAnalysisResultRepository = {
    save: jest.fn().mockResolvedValue({}),
  };

  const mockCacheService = {
    // 添加必要的缓存服务方法
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SubprocessDiscoveryService,
        {
          provide: getRepositoryToken(EventLog),
          useValue: mockEventLogRepository,
        },
        {
          provide: getRepositoryToken(AnalysisResult),
          useValue: mockAnalysisResultRepository,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
      ],
    }).compile();

    service = module.get<SubprocessDiscoveryService>(
      SubprocessDiscoveryService,
    );
    eventLogRepository = module.get<Repository<EventLog>>(
      getRepositoryToken(EventLog),
    );
    analysisResultRepository = module.get<Repository<AnalysisResult>>(
      getRepositoryToken(AnalysisResult),
    );
    cacheService = module.get<CacheService>(CacheService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('discoverSubprocesses', () => {
    it('should discover sequential subprocess patterns', async () => {
      const result = await service.discoverSubprocesses(1, {
        minFrequency: 2,
        minLength: 2,
        maxLength: 5,
        confidenceThreshold: 0.6,
      });

      expect(result).toBeDefined();
      expect(result.subprocesses).toBeDefined();
      expect(result.hierarchicalDFG).toBeDefined();
      expect(result.statistics).toBeDefined();

      // 验证发现了采购子流程模式
      const purchasePattern = result.subprocesses.find(
        (sp) =>
          sp.activities.includes('原材料采购申请') &&
          sp.activities.includes('供应商资质审核'),
      );
      expect(purchasePattern).toBeDefined();
      expect(purchasePattern?.type).toBe('sequential');
      expect(purchasePattern?.frequency).toBeGreaterThanOrEqual(2);

      // 验证发现了生产子流程模式
      const productionPattern = result.subprocesses.find(
        (sp) =>
          sp.activities.includes('生产准备') &&
          sp.activities.includes('首件检验'),
      );
      expect(productionPattern).toBeDefined();

      // 验证统计信息
      expect(result.statistics.totalSubprocesses).toBeGreaterThan(0);
      expect(result.statistics.compressionRatio).toBeGreaterThanOrEqual(-2); // 压缩比可能为负值
      expect(result.statistics.originalActivities).toBeGreaterThan(0);
      expect(result.statistics.compressedActivities).toBeGreaterThan(0);
    });

    it('should handle empty event logs', async () => {
      mockEventLogRepository.find.mockResolvedValueOnce([]);

      await expect(service.discoverSubprocesses(1)).rejects.toThrow(
        '没有找到事件日志数据',
      );
    });

    it('should respect minimum frequency threshold', async () => {
      const result = await service.discoverSubprocesses(1, {
        minFrequency: 5, // 设置较高的频率阈值
        minLength: 2,
        maxLength: 5,
        confidenceThreshold: 0.6,
      });

      // 由于频率阈值较高，应该发现较少的子流程
      expect(result.subprocesses.length).toBeLessThan(10);
    });

    it('should respect confidence threshold', async () => {
      const result = await service.discoverSubprocesses(1, {
        minFrequency: 1,
        minLength: 2,
        maxLength: 5,
        confidenceThreshold: 0.9, // 设置较高的置信度阈值
      });

      // 所有发现的子流程都应该满足置信度要求
      result.subprocesses.forEach((sp) => {
        expect(sp.confidence).toBeGreaterThanOrEqual(0.9);
      });
    });
  });
});
