import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventLog } from '../entities/event-log.entity';
import {
  AnalysisResult,
  AnalysisType,
} from '../entities/analysis-result.entity';
import { CacheService } from './cache.service';
import { ConfigService } from '@nestjs/config';

export interface SubprocessNode {
  id: string;
  label: string;
  frequency: number;
  activities: string[];
  avgDuration: number;
  type: 'subprocess' | 'activity';
}

export interface SubprocessEdge {
  source: string;
  target: string;
  frequency: number;
  avgDuration: number;
}

export interface SubprocessPattern {
  id: string;
  name: string;
  activities: string[];
  frequency: number;
  avgDuration: number;
  cases: string[];
  type: 'sequential' | 'parallel' | 'loop' | 'choice';
  confidence: number;
}

export interface SubprocessDiscoveryResult {
  subprocesses: SubprocessPattern[];
  hierarchicalDFG: {
    nodes: SubprocessNode[];
    edges: SubprocessEdge[];
  };
  statistics: {
    totalSubprocesses: number;
    avgSubprocessLength: number;
    compressionRatio: number;
    originalActivities: number;
    compressedActivities: number;
  };
}

export interface SubprocessDiscoveryOptions {
  minFrequency: number;
  minLength: number;
  maxLength: number;
  confidenceThreshold: number;
  enableParallelDetection: boolean;
  enableLoopDetection: boolean;
  groupByDepartment: boolean;
  groupByResource: boolean;
}

@Injectable()
export class SubprocessDiscoveryService {
  private readonly logger = new Logger(SubprocessDiscoveryService.name);

  constructor(
    @InjectRepository(EventLog)
    private readonly eventLogRepository: Repository<EventLog>,
    @InjectRepository(AnalysisResult)
    private readonly analysisResultRepository: Repository<AnalysisResult>,
    private readonly cacheService: CacheService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 自动发掘子流程
   */
  async discoverSubprocesses(
    processId: number,
    options: Partial<SubprocessDiscoveryOptions> = {},
  ): Promise<SubprocessDiscoveryResult> {
    this.logger.log(
      `Starting subprocess discovery for processId: ${processId}`,
    );

    const defaultOptions: SubprocessDiscoveryOptions = {
      minFrequency: 1, // 降低最小频率，发现更多模式
      minLength: 2,
      maxLength: 40, // 增加最大长度，发现更长的子流程
      confidenceThreshold: 0.05, // 大幅降低置信度阈值
      enableParallelDetection: false,
      enableLoopDetection: false,
      groupByDepartment: false,
      groupByResource: false,
      ...options,
    };

    // 检查数据量
    const totalCount = await this.eventLogRepository.count({
      where: { processId },
    });

    this.logger.log(`Total event logs: ${totalCount}`);

    this.logger.log(
      `Large dataset (${totalCount} events), using Python GPU service`,
    );
    return await this.callPythonMiningService(processId, defaultOptions);
  }
  /**
   * 调用Python GPU加速挖掘服务
   */
  private async callPythonMiningService(
    processId: number,
    options: SubprocessDiscoveryOptions,
  ): Promise<SubprocessDiscoveryResult> {
    const pythonServiceUrl = this.configService.get(
      'PYTHON_MINING_SERVICE_URL',
      'http://localhost:8001',
    );

    // 获取事件日志数据
    const eventLogs = await this.eventLogRepository.find({
      where: { processId },
      order: { caseId: 'ASC', timestamp: 'ASC' },
    });

    if (eventLogs.length === 0) {
      throw new BadRequestException('没有找到事件日志数据');
    }

    this.logger.log(`Calling Python service with ${eventLogs.length} events`);

    // 转换数据格式
    const requestData = {
      process_id: processId,
      event_logs: eventLogs.map((log) => ({
        id: log.id,
        case_id: log.caseId,
        activity: log.activity,
        timestamp: log.timestamp.toISOString(),
        resource: log.resource,
        cost: log.cost,
        attributes: log.attributes,
      })),
      options: {
        min_frequency: options.minFrequency,
        min_confidence: options.confidenceThreshold,
        max_pattern_length: options.maxLength,
        enable_parallel_detection: options.enableParallelDetection,
        enable_loop_detection: options.enableLoopDetection,
        use_gpu_acceleration: true,
      },
    };

    try {
      // 创建AbortController用于超时控制
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 300000); // 5分钟超时

      const response = await fetch(`${pythonServiceUrl}/subprocess-discovery`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(
          `Python service error: ${response.status} ${response.statusText}`,
        );
      }

      const pythonResult = await response.json();

      this.logger.log(
        `Python service completed, found ${pythonResult.subprocesses.length} subprocesses`,
      );
      this.logger.log(
        `Performance: ${JSON.stringify(pythonResult.performance_metrics)}`,
      );

      // 转换Python结果为Node.js格式
      const result: SubprocessDiscoveryResult = {
        subprocesses: pythonResult.subprocesses.map((sp) => ({
          id: sp.id,
          name: sp.name,
          activities: sp.activities,
          frequency: sp.frequency,
          avgDuration: sp.avg_duration,
          cases: sp.cases,
          type: sp.pattern_type,
          confidence: sp.confidence,
        })),
        hierarchicalDFG: {
          nodes: pythonResult.hierarchical_dfg.nodes.map((node) => ({
            id: node.id,
            label: node.label,
            frequency: node.frequency,
            activities: [],
            avgDuration: 0,
            type: node.node_type === 'activity' ? 'activity' : 'subprocess',
          })),
          edges: pythonResult.hierarchical_dfg.edges.map((edge) => ({
            source: edge.source,
            target: edge.target,
            frequency: edge.frequency,
            avgDuration: edge.avg_duration,
          })),
        },
        statistics: {
          totalSubprocesses: pythonResult.statistics.total_subprocesses,
          avgSubprocessLength: pythonResult.statistics.avg_frequency || 0,
          compressionRatio: pythonResult.statistics.avg_confidence || 0,
          originalActivities: pythonResult.subprocesses.length,
          compressedActivities: pythonResult.hierarchical_dfg.nodes.length,
        },
      };

      // 保存结果
      await this.saveAnalysisResult(
        processId,
        AnalysisType.PROCESS_DISCOVERY,
        result,
      );

      return result;
    } catch (error) {
      this.logger.error(`Python service call failed: ${error.message}`);
      throw new BadRequestException(`Python挖掘服务调用失败: ${error.message}`);
    }
  }

  /**
   * 保存分析结果
   */
  private async saveAnalysisResult(
    processId: number,
    analysisType: AnalysisType,
    resultData: any,
  ): Promise<void> {
    const analysisResult = new AnalysisResult();
    analysisResult.processId = processId;
    analysisResult.analysisType = analysisType;
    analysisResult.resultData = resultData;

    await this.analysisResultRepository.save(analysisResult);
  }
}
