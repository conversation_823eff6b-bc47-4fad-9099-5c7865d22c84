import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnalysisController } from './analysis.controller';
import { DataProcessingService } from './data-processing.service';
import { DataStreamService } from './data-stream.service';
import { ProcessMiningService } from './process-mining.service';
import { CacheService } from './cache.service';
import { SubprocessDiscoveryService } from './subprocess-discovery.service';
import { DiscoveryStatusService } from './discovery-status.service';
import { DiscoveryExecutionService } from './discovery-execution.service';
import { EventLog } from '../entities/event-log.entity';
import { AnalysisResult } from '../entities/analysis-result.entity';

@Module({
  imports: [TypeOrmModule.forFeature([EventLog, AnalysisResult])],
  controllers: [AnalysisController],
  providers: [
    DataProcessingService,
    DataStreamService,
    ProcessMiningService,
    CacheService,
    SubprocessDiscoveryService,
    DiscoveryStatusService,
    DiscoveryExecutionService,
  ],
  exports: [
    DataProcessingService,
    DataStreamService,
    ProcessMiningService,
    CacheService,
    SubprocessDiscoveryService,
    DiscoveryStatusService,
    DiscoveryExecutionService,
  ],
})
export class AnalysisModule {}
