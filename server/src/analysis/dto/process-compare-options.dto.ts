import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsArray,
  IsString,
  IsDateString,
  IsNumber,
  Min,
  Max,
  IsBoolean,
  IsEnum,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

// 筛选条件数据类型
class FilterConditionDataDto {
  @ApiProperty({ required: false })
  @IsOptional()
  resources?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  min?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  max?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  unit?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  field?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  operator?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  value?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  values?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  rangeMin?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  rangeMax?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  activity?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  type?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  frequency?: [number, number];
}

// 筛选条件
class FilterConditionDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty({ enum: ['resource', 'duration', 'businessField', 'pathway'] })
  @IsEnum(['resource', 'duration', 'businessField', 'pathway'])
  type: 'resource' | 'duration' | 'businessField' | 'pathway';

  @ApiProperty()
  @IsBoolean()
  enabled: boolean;

  @ApiProperty({ type: FilterConditionDataDto })
  @ValidateNested()
  @Type(() => FilterConditionDataDto)
  data: FilterConditionDataDto;
}

// 筛选器组
class FilterGroupDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty({ enum: ['resource', 'duration', 'businessField', 'pathway'] })
  @IsEnum(['resource', 'duration', 'businessField', 'pathway'])
  type: 'resource' | 'duration' | 'businessField' | 'pathway';

  @ApiProperty({ enum: ['AND', 'OR'] })
  @IsEnum(['AND', 'OR'])
  logic: 'AND' | 'OR';

  @ApiProperty({ type: [FilterConditionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FilterConditionDto)
  conditions: FilterConditionDto[];
}

// 筛选器状态
class FilterStateDto {
  @ApiProperty({ enum: ['AND', 'OR'] })
  @IsEnum(['AND', 'OR'])
  globalLogic: 'AND' | 'OR';

  @ApiProperty({ type: [FilterGroupDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FilterGroupDto)
  groups: FilterGroupDto[];
}

export class ProcessCompareOptionsDto {
  @ApiProperty({
    description: '基准时间段开始时间',
    example: '2024-01-01T00:00:00.000Z',
    required: true,
  })
  @IsDateString()
  baseStartTime: string;

  @ApiProperty({
    description: '基准时间段结束时间',
    example: '2024-01-07T23:59:59.999Z',
    required: true,
  })
  @IsDateString()
  baseEndTime: string;

  @ApiProperty({
    description: '偏移天数（向前偏移）',
    example: 7,
    required: true,
    minimum: 1,
    maximum: 365,
  })
  @IsNumber()
  @Min(1)
  @Max(365)
  offsetDays: number;

  @ApiProperty({
    description: '必须包含的活动节点列表（多个的话就是包含任一）',
    example: ['活动A', '活动B'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  requiredActivities?: string[];

  @ApiProperty({
    description: '完整的筛选器状态',
    required: false,
    type: FilterStateDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => FilterStateDto)
  filters?: FilterStateDto;

  @ApiProperty({
    description: '是否强制刷新缓存',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  forceRefresh?: boolean;

  @ApiProperty({
    description: '展示维度',
    example: 'frequency',
    enum: ['frequency', 'duration'],
    required: false,
  })
  @IsOptional()
  @IsEnum(['frequency', 'duration'])
  dimension?: 'frequency' | 'duration';

  @ApiProperty({
    description: '展示方式',
    example: 'absolute',
    enum: ['absolute', 'difference', 'percentage'],
    required: false,
  })
  @IsOptional()
  @IsEnum(['absolute', 'difference', 'percentage'])
  displayMode?: 'absolute' | 'difference' | 'percentage';
}

export class ProcessCompareResultDto {
  @ApiProperty({
    description: '基准时间段的DFG数据',
  })
  baseDfg: any;

  @ApiProperty({
    description: '对比时间段的DFG数据',
  })
  compareDfg: any;

  @ApiProperty({
    description: '比对分析统计信息',
  })
  compareStatistics: {
    baseTimeRange: {
      start: string;
      end: string;
      duration: number;
    };
    compareTimeRange: {
      start: string;
      end: string;
      duration: number;
    };
    offsetDays: number;
    baseCaseCount: number;
    compareCaseCount: number;
    baseActivityCount: number;
    compareActivityCount: number;
    commonActivities: string[];
    uniqueBaseActivities: string[];
    uniqueCompareActivities: string[];
    changeMetrics: {
      caseCountChange: number;
      caseCountChangePercent: number;
      activityCountChange: number;
      activityCountChangePercent: number;
      avgDurationChange?: number;
      avgDurationChangePercent?: number;
    };
  };

  @ApiProperty({
    description: '分析结果时间戳',
  })
  timestamp: string;

  @ApiProperty({
    description: '是否来自缓存',
  })
  fromCache: boolean;
}
