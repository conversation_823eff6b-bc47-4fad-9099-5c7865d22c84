import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

export class UploadDataDto {
  @ApiProperty({ description: '流程ID' })
  @IsNotEmpty()
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  processId: number;

  @ApiProperty({ description: '案例ID字段名', example: 'case_id' })
  @IsNotEmpty()
  @IsString()
  caseIdField: string;

  @ApiProperty({ description: '活动字段名', example: 'activity' })
  @IsNotEmpty()
  @IsString()
  activityField: string;

  @ApiProperty({ description: '时间戳字段名', example: 'timestamp' })
  @IsNotEmpty()
  @IsString()
  timestampField: string;

  @ApiProperty({
    description: '资源字段名',
    example: 'resource',
    required: false,
  })
  @IsOptional()
  @IsString()
  resourceField?: string;

  @ApiProperty({ description: '成本字段名', example: 'cost', required: false })
  @IsOptional()
  @IsString()
  costField?: string;

  @ApiProperty({
    description: '活动ID字段名',
    example: 'activity_id',
    required: false,
  })
  @IsOptional()
  @IsString()
  activityIdField?: string;

  @ApiProperty({
    description: '上一个活动字段名',
    example: 'previous_activity',
    required: false,
  })
  @IsOptional()
  @IsString()
  previousActivityField?: string;

  @ApiProperty({
    description: '活动结束时间字段名',
    example: 'end_timestamp',
    required: false,
  })
  @IsOptional()
  @IsString()
  endTimestampField?: string;

  @ApiProperty({
    description: '结束时间字段名',
    example: 'end_time',
    required: false,
  })
  @IsOptional()
  @IsString()
  endTimeField?: string;

  @ApiProperty({
    description: '父案例ID字段名（用于多层次嵌套流程）',
    example: 'parent_case_id',
    required: false,
  })
  @IsOptional()
  @IsString()
  parentCaseIdField?: string;

  @ApiProperty({
    description: '是否清空该流程下的所有已存在事件日志',
    example: false,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  clearExistingData?: boolean;
}
