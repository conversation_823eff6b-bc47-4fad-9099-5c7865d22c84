import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsArray, IsString } from 'class-validator';

export class ProcessDiscoveryOptionsDto {
  @ApiProperty({
    description: '必须包含的活动节点列表（多个的话就是包含任一）',
    example: ['活动A', '活动B'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  requiredActivities?: string[];

  @ApiProperty({
    description: '是否强制刷新缓存',
    example: false,
    required: false,
  })
  @IsOptional()
  forceRefresh?: boolean;
}
