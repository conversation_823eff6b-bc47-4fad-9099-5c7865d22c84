import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsBoolean, Min, Max } from 'class-validator';

export class SubprocessDiscoveryOptionsDto {
  @ApiProperty({
    description: '最小频率阈值',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  minFrequency?: number;

  @ApiProperty({
    description: '子流程最小长度',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(2)
  minLength?: number;

  @ApiProperty({
    description: '子流程最大长度',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Max(20)
  maxLength?: number;

  @ApiProperty({
    description: '置信度阈值',
    example: 0.7,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  confidenceThreshold?: number;

  @ApiProperty({
    description: '是否启用并行检测',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  enableParallelDetection?: boolean;

  @ApiProperty({
    description: '是否启用循环检测',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  enableLoopDetection?: boolean;

  @ApiProperty({
    description: '是否按部门分组',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  groupByDepartment?: boolean;

  @ApiProperty({
    description: '是否按资源分组',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  groupByResource?: boolean;
}
