import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProcessMiningService } from './process-mining.service';
import { CacheService } from './cache.service';
import { EventLog } from '../entities/event-log.entity';
import { AnalysisResult } from '../entities/analysis-result.entity';

describe('Activity Duration Consistency', () => {
  let service: ProcessMiningService;

  const mockEventLogRepository = {
    find: jest.fn(),
    count: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      where: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
    })),
  };

  const mockAnalysisResultRepository = {
    save: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(),
  };

  const mockCacheService = {
    getAnalysisResult: jest.fn(),
    setAnalysisResult: jest.fn(),
    getDataSourceHash: jest.fn(),
    setDataSourceHash: jest.fn(),
    entityToCacheData: jest.fn((entity) => entity),
  };

  // 测试数据：包含endTimestamp的事件日志
  const testEventLogs = [
    {
      id: 1,
      processId: 1,
      caseId: 'case1',
      activity: '副总裁',
      timestamp: new Date('2024-01-01T10:00:00Z'),
      endTimestamp: new Date('2024-01-01T10:05:00Z'), // 5分钟持续时间
      resource: 'VP1',
    },
    {
      id: 2,
      processId: 1,
      caseId: 'case1',
      activity: '经理审批',
      timestamp: new Date('2024-01-01T10:30:00Z'),
      endTimestamp: new Date('2024-01-01T10:54:00Z'), // 24分钟持续时间
      resource: 'Manager1',
    },
    {
      id: 3,
      processId: 1,
      caseId: 'case2',
      activity: '副总裁',
      timestamp: new Date('2024-01-01T14:00:00Z'),
      endTimestamp: new Date('2024-01-01T14:03:00Z'), // 3分钟持续时间
      resource: 'VP2',
    },
    {
      id: 4,
      processId: 1,
      caseId: 'case2',
      activity: '经理审批',
      timestamp: new Date('2024-01-01T14:30:00Z'),
      endTimestamp: new Date('2024-01-01T15:06:00Z'), // 36分钟持续时间
      resource: 'Manager2',
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProcessMiningService,
        {
          provide: getRepositoryToken(EventLog),
          useValue: mockEventLogRepository,
        },
        {
          provide: getRepositoryToken(AnalysisResult),
          useValue: mockAnalysisResultRepository,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
      ],
    }).compile();

    service = module.get<ProcessMiningService>(ProcessMiningService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should calculate consistent activity durations between process discovery and performance analysis', async () => {
    const processId = 1;

    // 设置 mock 返回值
    mockEventLogRepository.find.mockResolvedValue(testEventLogs);
    mockEventLogRepository.count.mockResolvedValue(testEventLogs.length);
    mockCacheService.getAnalysisResult.mockResolvedValue(null);
    mockCacheService.getDataSourceHash.mockResolvedValue('hash123');
    mockAnalysisResultRepository.save.mockResolvedValue({});

    // 执行流程发现
    const discoveryResult = await service.discoverProcess(processId);

    // 执行性能分析
    const performanceResult = await service.analyzePerformance(processId);

    // 验证副总裁活动的持续时间一致性
    const vpNodeInDiscovery = discoveryResult.nodes.find(
      (node: any) => node.id === '副总裁',
    );
    const vpActivityInPerformance = performanceResult.activityStatistics.find(
      (stat: any) => stat.activity === '副总裁',
    );

    expect(vpNodeInDiscovery).toBeDefined();
    expect(vpActivityInPerformance).toBeDefined();

    if (vpNodeInDiscovery && vpActivityInPerformance) {
      // 副总裁活动的平均持续时间应该是 (5分钟 + 3分钟) / 2 = 4分钟 = 240000毫秒
      expect(vpNodeInDiscovery.avgDuration).toBe(240000);
      expect(vpActivityInPerformance.avgDuration).toBe(240000);

      // 两个结果应该完全一致
      expect(vpNodeInDiscovery.avgDuration).toBe(
        vpActivityInPerformance.avgDuration,
      );
    }

    // 验证经理审批活动的持续时间一致性
    const managerNodeInDiscovery = discoveryResult.nodes.find(
      (node: any) => node.id === '经理审批',
    );
    const managerActivityInPerformance =
      performanceResult.activityStatistics.find(
        (stat: any) => stat.activity === '经理审批',
      );

    expect(managerNodeInDiscovery).toBeDefined();
    expect(managerActivityInPerformance).toBeDefined();

    if (managerNodeInDiscovery && managerActivityInPerformance) {
      // 经理审批活动的平均持续时间应该是 (24分钟 + 36分钟) / 2 = 30分钟 = 1800000毫秒
      expect(managerNodeInDiscovery.avgDuration).toBe(1800000);
      expect(managerActivityInPerformance.avgDuration).toBe(1800000);

      // 两个结果应该完全一致
      expect(managerNodeInDiscovery.avgDuration).toBe(
        managerActivityInPerformance.avgDuration,
      );
    }
  });

  it('should prioritize endTimestamp over next activity timestamp', async () => {
    const processId = 1;

    // 创建测试数据：有endTimestamp但下一个活动时间间隔不同
    const testData = [
      {
        id: 1,
        processId: 1,
        caseId: 'case1',
        activity: '测试活动',
        timestamp: new Date('2024-01-01T10:00:00Z'),
        endTimestamp: new Date('2024-01-01T10:10:00Z'), // 实际持续10分钟
        resource: 'User1',
      },
      {
        id: 2,
        processId: 1,
        caseId: 'case1',
        activity: '下一个活动',
        timestamp: new Date('2024-01-01T10:30:00Z'), // 30分钟后开始（如果用这个计算会是20分钟）
        resource: 'User1',
      },
    ];

    mockEventLogRepository.find.mockResolvedValue(testData);
    mockEventLogRepository.count.mockResolvedValue(testData.length);
    mockCacheService.getAnalysisResult.mockResolvedValue(null);
    mockCacheService.getDataSourceHash.mockResolvedValue('hash456');
    mockAnalysisResultRepository.save.mockResolvedValue({});

    // 执行流程发现和性能分析
    const discoveryResult = await service.discoverProcess(processId);
    const performanceResult = await service.analyzePerformance(processId);

    // 验证测试活动的持续时间
    const testNodeInDiscovery = discoveryResult.nodes.find(
      (node: any) => node.id === '测试活动',
    );
    const testActivityInPerformance = performanceResult.activityStatistics.find(
      (stat: any) => stat.activity === '测试活动',
    );

    expect(testNodeInDiscovery).toBeDefined();
    expect(testActivityInPerformance).toBeDefined();

    if (testNodeInDiscovery && testActivityInPerformance) {
      // 应该使用endTimestamp计算的10分钟（600000毫秒），而不是到下一个活动的20分钟
      expect(testNodeInDiscovery.avgDuration).toBe(600000); // 10分钟
      expect(testActivityInPerformance.avgDuration).toBe(600000); // 10分钟

      // 确保不是使用到下一个活动的时间间隔（20分钟）
      expect(testNodeInDiscovery.avgDuration).not.toBe(1200000); // 不是20分钟
      expect(testActivityInPerformance.avgDuration).not.toBe(1200000); // 不是20分钟
    }
  });
});
