import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { ProcessMiningService } from './process-mining.service';
import { CacheService } from './cache.service';
import { EventLog } from '../entities/event-log.entity';
import {
  AnalysisResult,
  AnalysisType,
  AnalysisStatus,
} from '../entities/analysis-result.entity';

describe('ProcessMiningService', () => {
  let service: ProcessMiningService;
  let eventLogRepository: Repository<EventLog>;
  let analysisResultRepository: Repository<AnalysisResult>;
  let cacheService: CacheService;

  const mockEventLogRepository = {
    find: jest.fn(),
    count: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      where: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
    })),
  };

  const mockAnalysisResultRepository = {
    save: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(),
  };

  const mockCacheService = {
    getAnalysisResult: jest.fn(),
    setAnalysisResult: jest.fn(),
    getDataSourceHash: jest.fn(),
    setDataSourceHash: jest.fn(),
    entityToCacheData: jest.fn((entity) => entity),
  };

  const mockEventLogs = [
    {
      id: 1,
      processId: 1,
      caseId: 'case1',
      activity: 'Start',
      timestamp: new Date('2024-01-01T10:00:00Z'),
      resource: 'User1',
      previousActivity: null,
    },
    {
      id: 2,
      processId: 1,
      caseId: 'case1',
      activity: 'Task A',
      timestamp: new Date('2024-01-01T10:30:00Z'),
      resource: 'User1',
    },
    {
      id: 3,
      processId: 1,
      caseId: 'case1',
      activity: 'End',
      timestamp: new Date('2024-01-01T11:00:00Z'),
      resource: 'User1',
    },
    {
      id: 4,
      processId: 1,
      caseId: 'case2',
      activity: 'Start',
      timestamp: new Date('2024-01-01T14:00:00Z'),
      resource: 'User2',
    },
    {
      id: 5,
      processId: 1,
      caseId: 'case2',
      activity: 'Task B',
      timestamp: new Date('2024-01-01T14:30:00Z'),
      resource: 'User2',
    },
    {
      id: 6,
      processId: 1,
      caseId: 'case2',
      activity: 'End',
      timestamp: new Date('2024-01-01T15:00:00Z'),
      resource: 'User2',
    },
  ];

  // 包含endTimestamp字段的测试数据
  const mockEventLogsWithEndTime = [
    {
      id: 1,
      processId: 1,
      caseId: 'case1',
      activity: '副总裁',
      timestamp: new Date('2024-01-01T10:00:00Z'),
      endTimestamp: new Date('2024-01-01T10:05:00Z'), // 5分钟持续时间
      resource: 'VP1',
    },
    {
      id: 2,
      processId: 1,
      caseId: 'case1',
      activity: '经理审批',
      timestamp: new Date('2024-01-01T10:30:00Z'),
      endTimestamp: new Date('2024-01-01T10:54:00Z'), // 24分钟持续时间
      resource: 'Manager1',
    },
    {
      id: 3,
      processId: 1,
      caseId: 'case1',
      activity: '结束',
      timestamp: new Date('2024-01-01T11:00:00Z'),
      endTimestamp: new Date('2024-01-01T11:01:00Z'), // 1分钟持续时间
      resource: 'System',
    },
    {
      id: 4,
      processId: 1,
      caseId: 'case2',
      activity: '副总裁',
      timestamp: new Date('2024-01-01T14:00:00Z'),
      endTimestamp: new Date('2024-01-01T14:03:00Z'), // 3分钟持续时间
      resource: 'VP2',
    },
    {
      id: 5,
      processId: 1,
      caseId: 'case2',
      activity: '经理审批',
      timestamp: new Date('2024-01-01T14:30:00Z'),
      endTimestamp: new Date('2024-01-01T15:06:00Z'), // 36分钟持续时间
      resource: 'Manager2',
    },
    {
      id: 6,
      processId: 1,
      caseId: 'case2',
      activity: '结束',
      timestamp: new Date('2024-01-01T15:30:00Z'),
      endTimestamp: new Date('2024-01-01T15:31:00Z'), // 1分钟持续时间
      resource: 'System',
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProcessMiningService,
        {
          provide: getRepositoryToken(EventLog),
          useValue: mockEventLogRepository,
        },
        {
          provide: getRepositoryToken(AnalysisResult),
          useValue: mockAnalysisResultRepository,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
      ],
    }).compile();

    service = module.get<ProcessMiningService>(ProcessMiningService);
    eventLogRepository = module.get<Repository<EventLog>>(
      getRepositoryToken(EventLog),
    );
    analysisResultRepository = module.get<Repository<AnalysisResult>>(
      getRepositoryToken(AnalysisResult),
    );
    cacheService = module.get<CacheService>(CacheService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('discoverProcess', () => {
    it('should discover process from event logs', async () => {
      const processId = 1;

      mockEventLogRepository.find.mockResolvedValue(mockEventLogs);
      mockCacheService.getAnalysisResult.mockResolvedValue(null);
      mockCacheService.getDataSourceHash.mockResolvedValue('hash123');
      mockAnalysisResultRepository.save.mockResolvedValue({});

      const result = await service.discoverProcess(processId, {});

      expect(result).toHaveProperty('nodes');
      expect(result).toHaveProperty('edges');
      expect(result).toHaveProperty('statistics');

      // 检查节点是否包含预期的活动
      const nodeIds = result.nodes.map((node: any) => node.id);
      expect(nodeIds).toContain('Start');
      expect(nodeIds).toContain('End');
      expect(result.edges.length).toBeGreaterThan(0);
    });

    it('should return cached result when available', async () => {
      const processId = 1;
      const cachedResult = {
        resultData: {
          nodes: ['Start', 'End'],
          edges: [{ from: 'Start', to: 'End', frequency: 2 }],
          statistics: { totalCases: 2 },
        },
      };

      mockCacheService.getAnalysisResult.mockResolvedValue(cachedResult);

      const result = await service.discoverProcess(processId, {});

      expect(result).toEqual(cachedResult.resultData);
      expect(mockEventLogRepository.find).not.toHaveBeenCalled();
    });

    it('should force refresh when requested', async () => {
      const processId = 1;

      mockEventLogRepository.find.mockResolvedValue(mockEventLogs);
      mockCacheService.getDataSourceHash.mockResolvedValue('hash123');
      mockAnalysisResultRepository.save.mockResolvedValue({});

      const result = await service.discoverProcess(processId, {
        forceRefresh: true,
      });

      expect(result).toHaveProperty('nodes');
      expect(result).toHaveProperty('edges');
      expect(mockCacheService.getAnalysisResult).not.toHaveBeenCalled();
    });

    it('should filter event logs by required activities', async () => {
      const processId = 1;
      const requiredActivities = ['活动A'];

      mockEventLogRepository.find.mockResolvedValue(mockEventLogs);
      mockCacheService.getDataSourceHash.mockResolvedValue('hash123');
      mockAnalysisResultRepository.save.mockResolvedValue({});

      const result = await service.discoverProcess(processId, {
        requiredActivities,
      });

      expect(result).toHaveProperty('nodes');
      expect(result).toHaveProperty('edges');
      expect(result).toHaveProperty('statistics');

      // 验证过滤逻辑被调用
      expect(mockEventLogRepository.find).toHaveBeenCalledWith({
        where: { processId },
        order: { caseId: 'ASC', timestamp: 'ASC' },
      });
    });

    it('should correctly calculate activity durations in DFG using endTimestamp', async () => {
      const processId = 1;

      mockEventLogRepository.find.mockResolvedValue(mockEventLogsWithEndTime);
      mockCacheService.getAnalysisResult.mockResolvedValue(null);
      mockCacheService.getDataSourceHash.mockResolvedValue('hash123');
      mockAnalysisResultRepository.save.mockResolvedValue({});

      const result = await service.discoverProcess(processId, {});

      expect(result).toHaveProperty('nodes');
      expect(result).toHaveProperty('edges');

      // 检查节点是否包含持续时间信息
      const vpNode = result.nodes.find((node: any) => node.id === '副总裁');
      expect(vpNode).toBeDefined();

      if (vpNode) {
        // 副总裁活动的平均持续时间应该是 (5分钟 + 3分钟) / 2 = 4分钟 = 240000毫秒
        expect(vpNode.avgDuration).toBe(240000);
        expect(vpNode.frequency).toBe(2);
      }

      // 检查经理审批节点
      const managerNode = result.nodes.find(
        (node: any) => node.id === '经理审批',
      );
      expect(managerNode).toBeDefined();

      if (managerNode) {
        // 经理审批活动的平均持续时间应该是 (24分钟 + 36分钟) / 2 = 30分钟 = 1800000毫秒
        expect(managerNode.avgDuration).toBe(1800000);
        expect(managerNode.frequency).toBe(2);
      }
    });
  });

  describe('analyzeVariants', () => {
    it('should analyze process variants', async () => {
      const processId = 1;

      mockEventLogRepository.find.mockResolvedValue(mockEventLogs);

      const result = await service.analyzeVariants(processId);

      expect(result).toHaveProperty('variants');
      expect(result).toHaveProperty('statistics');
      expect(Array.isArray(result.variants)).toBe(true);
      expect(result.statistics).toHaveProperty('totalVariants');
    });

    it('should handle empty event logs', async () => {
      const processId = 1;

      mockEventLogRepository.count.mockResolvedValue(0);
      mockEventLogRepository.find.mockResolvedValue([]);

      const result = await service.analyzeVariants(processId);

      expect(result).toHaveProperty('variants');
      expect(result).toHaveProperty('statistics');
      expect(result.variants).toEqual([]);
      expect(result.statistics.totalVariants).toBe(0);
    });
  });

  describe('analyzePerformance', () => {
    it('should analyze process performance', async () => {
      const processId = 1;

      mockEventLogRepository.count.mockResolvedValue(mockEventLogs.length);
      mockEventLogRepository.find.mockResolvedValue(mockEventLogs);

      const result = await service.analyzePerformance(processId);

      expect(result).toHaveProperty('caseStatistics');
      expect(result).toHaveProperty('activityStatistics');
      expect(result).toHaveProperty('bottlenecks');
      expect(result.caseStatistics).toHaveProperty('avgDuration');
      expect(result.caseStatistics).toHaveProperty('minDuration');
      expect(result.caseStatistics).toHaveProperty('maxDuration');
      expect(result.caseStatistics).toHaveProperty('medianDuration');
    });

    it('should analyze performance with required activities filter', async () => {
      const processId = 1;
      const options = {
        requiredActivities: ['活动A'],
        forceRefresh: false,
      };

      mockEventLogRepository.count.mockResolvedValue(mockEventLogs.length);
      mockEventLogRepository.find.mockResolvedValue(mockEventLogs);

      const result = await service.analyzePerformance(processId, options);

      expect(result).toHaveProperty('caseStatistics');
      expect(result).toHaveProperty('activityStatistics');
      expect(result).toHaveProperty('bottlenecks');
    });

    it('should force refresh when specified', async () => {
      const processId = 1;
      const options = {
        forceRefresh: true,
      };

      mockEventLogRepository.count.mockResolvedValue(mockEventLogs.length);
      mockEventLogRepository.find.mockResolvedValue(mockEventLogs);

      const result = await service.analyzePerformance(processId, options);

      expect(result).toHaveProperty('caseStatistics');
      expect(result).toHaveProperty('activityStatistics');
      expect(result).toHaveProperty('bottlenecks');
    });

    it('should correctly calculate activity durations using endTimestamp', async () => {
      const processId = 1;

      mockEventLogRepository.find.mockResolvedValue(mockEventLogsWithEndTime);
      mockEventLogRepository.count.mockResolvedValue(
        mockEventLogsWithEndTime.length,
      );

      const result = await service.analyzePerformance(processId);

      expect(result).toHaveProperty('activityStatistics');

      // 检查副总裁活动的持续时间统计
      const vpActivity = result.activityStatistics.find(
        (stat: any) => stat.activity === '副总裁',
      );
      expect(vpActivity).toBeDefined();

      if (vpActivity) {
        // 副总裁活动的平均持续时间应该是 (5分钟 + 3分钟) / 2 = 4分钟 = 240000毫秒
        expect(vpActivity.avgDuration).toBe(240000);
        expect(vpActivity.frequency).toBe(2); // 两个案例中都有
      }

      // 检查经理审批活动的持续时间统计
      const managerActivity = result.activityStatistics.find(
        (stat: any) => stat.activity === '经理审批',
      );
      expect(managerActivity).toBeDefined();

      if (managerActivity) {
        // 经理审批活动的平均持续时间应该是 (24分钟 + 36分钟) / 2 = 30分钟 = 1800000毫秒
        expect(managerActivity.avgDuration).toBe(1800000);
        expect(managerActivity.frequency).toBe(2); // 两个案例中都有
      }
    });

    it('should fallback to next activity timestamp when endTimestamp is missing', async () => {
      const processId = 1;

      // 创建没有endTimestamp的测试数据
      const eventLogsWithoutEndTime = [
        {
          id: 1,
          processId: 1,
          caseId: 'case1',
          activity: '开始',
          timestamp: new Date('2024-01-01T10:00:00Z'),
          resource: 'User1',
        },
        {
          id: 2,
          processId: 1,
          caseId: 'case1',
          activity: '处理',
          timestamp: new Date('2024-01-01T10:30:00Z'), // 30分钟后
          resource: 'User1',
        },
        {
          id: 3,
          processId: 1,
          caseId: 'case1',
          activity: '结束',
          timestamp: new Date('2024-01-01T11:00:00Z'), // 再30分钟后
          resource: 'User1',
        },
      ];

      mockEventLogRepository.find.mockResolvedValue(eventLogsWithoutEndTime);
      mockEventLogRepository.count.mockResolvedValue(
        eventLogsWithoutEndTime.length,
      );

      const result = await service.analyzePerformance(processId);

      expect(result).toHaveProperty('activityStatistics');

      // 检查开始活动的持续时间（应该使用到下一个活动的时间间隔）
      const startActivity = result.activityStatistics.find(
        (stat: any) => stat.activity === '开始',
      );
      expect(startActivity).toBeDefined();

      if (startActivity) {
        expect(startActivity.avgDuration).toBe(1800000); // 30分钟 = 1800000毫秒
      }

      // 检查处理活动的持续时间
      const processActivity = result.activityStatistics.find(
        (stat: any) => stat.activity === '处理',
      );
      expect(processActivity).toBeDefined();

      if (processActivity) {
        expect(processActivity.avgDuration).toBe(1800000); // 30分钟 = 1800000毫秒
      }
    });

    it('应该正确计算等待时间（优先使用活动结束时间）', async () => {
      const processId = 1;

      // 测试数据：包含endTimestamp的事件日志
      const testEventLogs = [
        {
          id: 1,
          processId: 1,
          caseId: 'case1',
          activity: '活动A',
          timestamp: new Date('2024-01-01T10:00:00Z'),
          endTimestamp: new Date('2024-01-01T10:05:00Z'), // 5分钟持续时间
          resource: 'User1',
        },
        {
          id: 2,
          processId: 1,
          caseId: 'case1',
          activity: '活动B',
          timestamp: new Date('2024-01-01T10:30:00Z'), // 从活动A结束后等待25分钟
          endTimestamp: new Date('2024-01-01T10:45:00Z'), // 15分钟持续时间
          resource: 'User2',
        },
        {
          id: 3,
          processId: 1,
          caseId: 'case1',
          activity: '活动C',
          timestamp: new Date('2024-01-01T11:00:00Z'), // 从活动B结束后等待15分钟
          resource: 'User3',
        },
      ];

      mockEventLogRepository.find.mockResolvedValue(testEventLogs);
      mockEventLogRepository.count.mockResolvedValue(testEventLogs.length);

      const result = await service.analyzePerformance(processId);

      expect(result).toHaveProperty('activityStatistics');

      // 检查活动B的等待时间（应该是从活动A结束时间到活动B开始时间的间隔）
      const activityB = result.activityStatistics.find(
        (stat: any) => stat.activity === '活动B',
      );
      expect(activityB).toBeDefined();

      if (activityB) {
        // 等待时间 = 10:30:00 - 10:05:00 = 25分钟 = 1500000毫秒
        expect(activityB.avgWaitingTime).toBe(1500000);
      }

      // 检查活动C的等待时间（应该是从活动B结束时间到活动C开始时间的间隔）
      const activityC = result.activityStatistics.find(
        (stat: any) => stat.activity === '活动C',
      );
      expect(activityC).toBeDefined();

      if (activityC) {
        // 等待时间 = 11:00:00 - 10:45:00 = 15分钟 = 900000毫秒
        expect(activityC.avgWaitingTime).toBe(900000);
      }
    });

    it('应该正确处理没有endTimestamp的等待时间计算', async () => {
      const processId = 1;

      // 测试数据：不包含endTimestamp的事件日志
      const testEventLogs = [
        {
          id: 1,
          processId: 1,
          caseId: 'case1',
          activity: '活动A',
          timestamp: new Date('2024-01-01T10:00:00Z'),
          resource: 'User1',
        },
        {
          id: 2,
          processId: 1,
          caseId: 'case1',
          activity: '活动B',
          timestamp: new Date('2024-01-01T10:30:00Z'),
          resource: 'User2',
        },
      ];

      mockEventLogRepository.find.mockResolvedValue(testEventLogs);
      mockEventLogRepository.count.mockResolvedValue(testEventLogs.length);

      const result = await service.analyzePerformance(processId);

      expect(result).toHaveProperty('activityStatistics');

      // 检查活动B的等待时间（应该使用活动A的开始时间计算）
      const activityB = result.activityStatistics.find(
        (stat: any) => stat.activity === '活动B',
      );
      expect(activityB).toBeDefined();

      if (activityB) {
        // 等待时间 = 10:30:00 - 10:00:00 = 30分钟 = 1800000毫秒
        expect(activityB.avgWaitingTime).toBe(1800000);
      }
    });

    it('应该正确识别瓶颈（包含等待时间字段）', async () => {
      const processId = 1;

      // 测试数据：包含不同等待时间的活动
      const testEventLogs = [
        {
          id: 1,
          processId: 1,
          caseId: 'case1',
          activity: '正常活动',
          timestamp: new Date('2024-01-01T10:00:00Z'),
          endTimestamp: new Date('2024-01-01T10:05:00Z'),
          resource: 'User1',
        },
        {
          id: 2,
          processId: 1,
          caseId: 'case1',
          activity: '瓶颈活动',
          timestamp: new Date('2024-01-01T12:00:00Z'), // 等待2小时
          endTimestamp: new Date('2024-01-01T12:30:00Z'),
          resource: 'User2',
        },
        {
          id: 3,
          processId: 1,
          caseId: 'case2',
          activity: '正常活动',
          timestamp: new Date('2024-01-01T14:00:00Z'),
          endTimestamp: new Date('2024-01-01T14:05:00Z'),
          resource: 'User1',
        },
        {
          id: 4,
          processId: 1,
          caseId: 'case2',
          activity: '瓶颈活动',
          timestamp: new Date('2024-01-01T16:00:00Z'), // 等待2小时
          endTimestamp: new Date('2024-01-01T16:30:00Z'),
          resource: 'User2',
        },
      ];

      mockEventLogRepository.find.mockResolvedValue(testEventLogs);
      mockEventLogRepository.count.mockResolvedValue(testEventLogs.length);

      const result = await service.analyzePerformance(processId);

      expect(result).toHaveProperty('bottlenecks');
      expect(result.bottlenecks).toHaveLength(1); // 只有瓶颈活动应该被识别

      const bottleneck = result.bottlenecks[0];
      expect(bottleneck.activity).toBe('瓶颈活动');
      expect(bottleneck).toHaveProperty('avgWaitingTime');
      expect(bottleneck.avgWaitingTime).toBe(7200000); // 2小时 = 7200000毫秒
      expect(bottleneck.frequency).toBe(2);
      expect(bottleneck).toHaveProperty('bottleneckType');
      expect(bottleneck).toHaveProperty('reason');
      expect(bottleneck.bottleneckType).toBe('waiting'); // 主要是等待瓶颈
    });

    it('应该正确识别执行瓶颈', async () => {
      const processId = 1;

      // 测试数据：包含执行时间很长的活动
      const testEventLogs = [
        {
          id: 1,
          processId: 1,
          caseId: 'case1',
          activity: '快速活动',
          timestamp: new Date('2024-01-01T10:00:00Z'),
          endTimestamp: new Date('2024-01-01T10:05:00Z'), // 5分钟
          resource: 'User1',
        },
        {
          id: 2,
          processId: 1,
          caseId: 'case1',
          activity: '慢速活动',
          timestamp: new Date('2024-01-01T10:05:00Z'), // 无等待时间
          endTimestamp: new Date('2024-01-01T14:05:00Z'), // 4小时执行时间
          resource: 'User2',
        },
        {
          id: 3,
          processId: 1,
          caseId: 'case2',
          activity: '快速活动',
          timestamp: new Date('2024-01-01T15:00:00Z'),
          endTimestamp: new Date('2024-01-01T15:05:00Z'), // 5分钟
          resource: 'User1',
        },
        {
          id: 4,
          processId: 1,
          caseId: 'case2',
          activity: '慢速活动',
          timestamp: new Date('2024-01-01T15:05:00Z'), // 无等待时间
          endTimestamp: new Date('2024-01-01T19:05:00Z'), // 4小时执行时间
          resource: 'User2',
        },
      ];

      mockEventLogRepository.find.mockResolvedValue(testEventLogs);
      mockEventLogRepository.count.mockResolvedValue(testEventLogs.length);

      const result = await service.analyzePerformance(processId);

      expect(result).toHaveProperty('bottlenecks');
      expect(result.bottlenecks.length).toBeGreaterThan(0);

      const executionBottleneck = result.bottlenecks.find(
        (b) => b.activity === '慢速活动',
      );
      expect(executionBottleneck).toBeDefined();
      expect(executionBottleneck?.bottleneckType).toBe('execution');
      expect(executionBottleneck?.avgDuration).toBe(14400000); // 4小时 = 14400000毫秒
      expect(executionBottleneck?.avgWaitingTime).toBe(0); // 无等待时间
    });

    it('应该使用缓存的性能分析结果', async () => {
      const processId = 1;
      const cachedResult = {
        resultData: {
          caseStatistics: {
            avgDuration: 3600000,
            minDuration: 1800000,
            maxDuration: 7200000,
            medianDuration: 3600000,
          },
          activityStatistics: [
            {
              activity: '测试活动',
              avgDuration: 1800000,
              frequency: 5,
              avgWaitingTime: 600000,
            },
          ],
          bottlenecks: [],
        },
      };

      mockCacheService.getAnalysisResult.mockResolvedValue(cachedResult);

      const result = await service.analyzePerformance(processId);

      expect(result).toEqual(cachedResult.resultData);
      expect(mockEventLogRepository.find).not.toHaveBeenCalled();
      expect(mockCacheService.getAnalysisResult).toHaveBeenCalledWith(
        processId,
        AnalysisType.PERFORMANCE_ANALYSIS,
      );
    });

    it('应该在强制刷新时跳过缓存', async () => {
      const processId = 1;
      const testEventLogs = [
        {
          id: 1,
          processId: 1,
          caseId: 'case1',
          activity: '活动1',
          timestamp: new Date('2024-01-01T10:00:00Z'),
          endTimestamp: new Date('2024-01-01T11:00:00Z'),
          resource: 'User1',
        },
      ];

      mockEventLogRepository.count.mockResolvedValue(1);
      mockEventLogRepository.find.mockResolvedValue(testEventLogs);
      mockCacheService.entityToCacheData.mockReturnValue({
        resultData: {},
        version: 1,
        createdAt: new Date(),
        expiresAt: new Date(),
      });

      const result = await service.analyzePerformance(processId, {
        forceRefresh: true,
      });

      expect(result).toHaveProperty('caseStatistics');
      expect(result).toHaveProperty('activityStatistics');
      expect(result).toHaveProperty('bottlenecks');
      expect(mockCacheService.getAnalysisResult).not.toHaveBeenCalled();
      expect(mockEventLogRepository.find).toHaveBeenCalled();
    });

    it('应该正确识别资源瓶颈', async () => {
      const processId = 1;

      // 测试数据：资源配置不足的活动
      const testEventLogs = [
        {
          id: 1,
          processId: 1,
          caseId: 'case1',
          activity: '资源紧张活动',
          timestamp: new Date('2024-01-01T10:00:00Z'),
          endTimestamp: new Date('2024-01-01T10:30:00Z'), // 30分钟执行
          resource: 'Resource1',
        },
        {
          id: 2,
          processId: 1,
          caseId: 'case1',
          activity: '正常活动',
          timestamp: new Date('2024-01-01T12:00:00Z'), // 1.5小时等待
          endTimestamp: new Date('2024-01-01T12:30:00Z'), // 30分钟执行
          resource: 'Resource2',
        },
        {
          id: 3,
          processId: 1,
          caseId: 'case2',
          activity: '资源紧张活动',
          timestamp: new Date('2024-01-01T14:00:00Z'),
          endTimestamp: new Date('2024-01-01T14:30:00Z'), // 30分钟执行
          resource: 'Resource1', // 同一个资源
        },
        {
          id: 4,
          processId: 1,
          caseId: 'case2',
          activity: '正常活动',
          timestamp: new Date('2024-01-01T16:00:00Z'), // 1.5小时等待
          endTimestamp: new Date('2024-01-01T16:30:00Z'), // 30分钟执行
          resource: 'Resource3', // 不同资源
        },
      ];

      mockEventLogRepository.count.mockResolvedValue(testEventLogs.length);
      mockEventLogRepository.find
        .mockResolvedValueOnce(testEventLogs) // 第一次调用（主要分析）
        .mockResolvedValueOnce(testEventLogs); // 第二次调用（资源分析）
      mockCacheService.entityToCacheData.mockReturnValue({
        resultData: {},
        version: 1,
        createdAt: new Date(),
        expiresAt: new Date(),
      });

      const result = await service.analyzePerformance(processId);

      expect(result).toHaveProperty('bottlenecks');

      // 检查是否正确识别了资源瓶颈
      const resourceBottleneck = result.bottlenecks.find(
        (b) => b.bottleneckType === 'resource',
      );
      if (resourceBottleneck) {
        expect(resourceBottleneck.activity).toBe('资源紧张活动');
        expect(resourceBottleneck.resourceAnalysis).toBeDefined();
        expect(resourceBottleneck.resourceAnalysis?.isResourceConstrained).toBe(
          true,
        );
        expect(resourceBottleneck.reason).toContain('资源配置不足');
      }
    });

    it('应该区分资源瓶颈和流程设计问题', async () => {
      const processId = 1;

      // 测试数据：资源充足但等待时间长的活动
      const testEventLogs = [
        {
          id: 1,
          processId: 1,
          caseId: 'case1',
          activity: '流程设计问题活动',
          timestamp: new Date('2024-01-01T10:00:00Z'),
          endTimestamp: new Date('2024-01-01T10:30:00Z'),
          resource: 'Resource1',
        },
        {
          id: 2,
          processId: 1,
          caseId: 'case1',
          activity: '下一个活动',
          timestamp: new Date('2024-01-01T14:00:00Z'), // 3.5小时等待
          endTimestamp: new Date('2024-01-01T14:30:00Z'),
          resource: 'Resource2',
        },
        {
          id: 3,
          processId: 1,
          caseId: 'case2',
          activity: '流程设计问题活动',
          timestamp: new Date('2024-01-01T15:00:00Z'),
          endTimestamp: new Date('2024-01-01T15:30:00Z'),
          resource: 'Resource3', // 不同资源，说明资源充足
        },
        {
          id: 4,
          processId: 1,
          caseId: 'case2',
          activity: '下一个活动',
          timestamp: new Date('2024-01-01T19:00:00Z'), // 3.5小时等待
          endTimestamp: new Date('2024-01-01T19:30:00Z'),
          resource: 'Resource4', // 不同资源
        },
      ];

      mockEventLogRepository.count.mockResolvedValue(testEventLogs.length);
      mockEventLogRepository.find
        .mockResolvedValueOnce(testEventLogs)
        .mockResolvedValueOnce(testEventLogs);
      mockCacheService.entityToCacheData.mockReturnValue({
        resultData: {},
        version: 1,
        createdAt: new Date(),
        expiresAt: new Date(),
      });

      const result = await service.analyzePerformance(processId);

      expect(result).toHaveProperty('bottlenecks');

      // 检查是否正确识别为等待瓶颈而非资源瓶颈
      const waitingBottleneck = result.bottlenecks.find(
        (b) => b.activity === '下一个活动' && b.bottleneckType === 'waiting',
      );
      if (waitingBottleneck) {
        expect(waitingBottleneck.reason).toContain('流程设计');
        expect(waitingBottleneck.resourceAnalysis?.isResourceConstrained).toBe(
          false,
        );
      }
    });
  });

  describe('getAnalysisResult', () => {
    it('should get analysis result by type', async () => {
      const processId = 1;
      const analysisType = AnalysisType.PROCESS_DISCOVERY;
      const mockResult = {
        id: 1,
        processId,
        analysisType,
        resultData: { nodes: [], edges: [] },
      };

      mockAnalysisResultRepository.findOne.mockResolvedValue(mockResult);

      const result = await service.getAnalysisResult(processId, analysisType);

      expect(result).toEqual(mockResult);
      expect(mockAnalysisResultRepository.findOne).toHaveBeenCalledWith({
        where: {
          processId,
          analysisType,
          status: expect.any(Object), // In([AnalysisStatus.COMPLETED, AnalysisStatus.CACHED])
        },
        order: { createdAt: 'DESC' },
        select: expect.any(Array),
      });
    });

    it('should return null when no result found', async () => {
      const processId = 999;
      const analysisType = AnalysisType.PROCESS_DISCOVERY;

      mockAnalysisResultRepository.findOne.mockResolvedValue(null);

      const result = await service.getAnalysisResult(processId, analysisType);

      expect(result).toBeNull();
    });
  });

  describe('previousActivity field priority', () => {
    it('should prioritize previousActivity field over timestamp order', async () => {
      const processId = 1;

      // 创建测试数据：时间顺序和前置活动字段不一致的情况
      const mockEventLogsWithPreviousActivity = [
        {
          id: 1,
          processId: 1,
          caseId: 'case1',
          activity: 'A',
          timestamp: new Date('2024-01-01T10:00:00Z'),
          previousActivity: null, // 开始活动
        },
        {
          id: 2,
          processId: 1,
          caseId: 'case1',
          activity: 'B',
          timestamp: new Date('2024-01-01T10:10:00Z'),
          previousActivity: 'A', // 明确指定前置活动
        },
        {
          id: 3,
          processId: 1,
          caseId: 'case1',
          activity: 'C',
          timestamp: new Date('2024-01-01T10:05:00Z'), // 时间戳比B早，但前置活动是B
          previousActivity: 'B', // 明确指定前置活动
        },
        {
          id: 4,
          processId: 1,
          caseId: 'case1',
          activity: 'D',
          timestamp: new Date('2024-01-01T10:20:00Z'),
          previousActivity: null, // 没有前置活动信息，应该使用时间顺序
        },
      ];

      mockEventLogRepository.find.mockResolvedValue(
        mockEventLogsWithPreviousActivity,
      );
      mockCacheService.getAnalysisResult.mockResolvedValue(null);
      mockAnalysisResultRepository.save.mockResolvedValue({
        id: 1,
        processId,
        analysisType: AnalysisType.PROCESS_DISCOVERY,
        status: AnalysisStatus.COMPLETED,
        resultData: {},
      });

      const result = await service.discoverProcess(processId, {
        forceRefresh: true,
      });

      expect(result).toHaveProperty('edges');

      // 验证基于前置活动字段的关系
      const edges = result.edges;
      const edgeMap = new Map(
        edges.map((edge) => [`${edge.source} -> ${edge.target}`, edge]),
      );

      // 应该有 A -> B 的关系（基于前置活动字段）
      expect(edgeMap.has('A -> B')).toBe(true);

      // 应该有 B -> C 的关系（基于前置活动字段，而不是时间顺序）
      expect(edgeMap.has('B -> C')).toBe(true);

      // 应该有 C -> D 的关系（基于时间顺序，因为D没有前置活动信息）
      expect(edgeMap.has('C -> D')).toBe(true);

      // 不应该有 A -> C 的关系（虽然时间顺序上C在B之后，但前置活动字段明确指定了B）
      expect(edgeMap.has('A -> C')).toBe(false);
    });

    it('should use timestamp order when previousActivity is empty', async () => {
      const processId = 1;

      // 创建测试数据：没有前置活动字段的情况
      const mockEventLogsWithoutPreviousActivity = [
        {
          id: 1,
          processId: 1,
          caseId: 'case1',
          activity: 'Start',
          timestamp: new Date('2024-01-01T10:00:00Z'),
          previousActivity: null,
        },
        {
          id: 2,
          processId: 1,
          caseId: 'case1',
          activity: 'Middle',
          timestamp: new Date('2024-01-01T10:10:00Z'),
          previousActivity: null, // 没有前置活动信息
        },
        {
          id: 3,
          processId: 1,
          caseId: 'case1',
          activity: 'End',
          timestamp: new Date('2024-01-01T10:20:00Z'),
          previousActivity: null, // 没有前置活动信息
        },
      ];

      mockEventLogRepository.find.mockResolvedValue(
        mockEventLogsWithoutPreviousActivity,
      );
      mockCacheService.getAnalysisResult.mockResolvedValue(null);
      mockAnalysisResultRepository.save.mockResolvedValue({
        id: 1,
        processId,
        analysisType: AnalysisType.PROCESS_DISCOVERY,
        status: AnalysisStatus.COMPLETED,
        resultData: {},
      });

      const result = await service.discoverProcess(processId, {
        forceRefresh: true,
      });

      expect(result).toHaveProperty('edges');

      // 验证基于时间顺序的关系
      const edges = result.edges;
      const edgeMap = new Map(
        edges.map((edge) => [`${edge.source} -> ${edge.target}`, edge]),
      );

      // 应该有基于时间顺序的关系
      expect(edgeMap.has('Start -> Middle')).toBe(true);
      expect(edgeMap.has('Middle -> End')).toBe(true);
    });
  });

  describe('多层级嵌套流程挖掘的前置活动推断', () => {
    it('应该能够推断具有parent_case_id但缺少previousActivity的活动的前置节点', async () => {
      const processId = 1;

      // 创建多层级测试数据
      const hierarchicalEventLogs = [
        // 主流程（父流程）
        {
          id: 1,
          processId: 1,
          caseId: 'parent_case_1',
          activity: '主流程活动A',
          timestamp: new Date('2024-01-01T10:00:00Z'),
          resource: 'User1',
          parentCaseId: null,
          previousActivity: null,
          endTimestamp: new Date('2024-01-01T10:30:00Z'),
        },
        {
          id: 2,
          processId: 1,
          caseId: 'parent_case_1',
          activity: '主流程活动B',
          timestamp: new Date('2024-01-01T10:45:00Z'),
          resource: 'User1',
          parentCaseId: null,
          previousActivity: '主流程活动A',
          endTimestamp: new Date('2024-01-01T11:15:00Z'),
        },
        // 子流程（在主流程活动B执行期间启动）
        {
          id: 3,
          processId: 1,
          caseId: 'child_case_1',
          activity: '子流程活动X',
          timestamp: new Date('2024-01-01T11:00:00Z'), // 在主流程活动B执行期间
          resource: 'User2',
          parentCaseId: 'parent_case_1',
          previousActivity: null, // 缺少前置活动信息，需要推断
          endTimestamp: new Date('2024-01-01T11:10:00Z'),
        },
        {
          id: 4,
          processId: 1,
          caseId: 'child_case_1',
          activity: '子流程活动Y',
          timestamp: new Date('2024-01-01T11:12:00Z'),
          resource: 'User2',
          parentCaseId: 'parent_case_1',
          previousActivity: '子流程活动X', // 有明确的前置活动
          endTimestamp: new Date('2024-01-01T11:20:00Z'),
        },
      ];

      mockEventLogRepository.find.mockResolvedValue(hierarchicalEventLogs);
      mockEventLogRepository.count.mockResolvedValue(
        hierarchicalEventLogs.length,
      );

      // 测试前置活动推断
      const result =
        await service.testHierarchicalPreviousActivityInference(processId);

      expect(result).toHaveProperty('totalEvents', 4);
      expect(result).toHaveProperty('eventsNeedingInference', 1); // 只有子流程活动X需要推断
      expect(result).toHaveProperty('successfulInferences', 1); // 应该成功推断出前置活动
      expect(result).toHaveProperty('failedInferences', 0);
      expect(result).toHaveProperty('successRate', 100);
      expect(result).toHaveProperty('totalRelations');
      expect(result).toHaveProperty('topRelations');
    });

    it('应该能够处理边界情况：父流程没有正在执行的活动', async () => {
      const processId = 1;

      // 创建边界情况测试数据
      const boundaryEventLogs = [
        // 主流程（父流程）
        {
          id: 1,
          processId: 1,
          caseId: 'parent_case_1',
          activity: '主流程活动A',
          timestamp: new Date('2024-01-01T10:00:00Z'),
          resource: 'User1',
          parentCaseId: null,
          previousActivity: null,
          endTimestamp: new Date('2024-01-01T10:30:00Z'), // 已经结束
        },
        // 子流程（在主流程活动A结束后启动）
        {
          id: 2,
          processId: 1,
          caseId: 'child_case_1',
          activity: '子流程活动X',
          timestamp: new Date('2024-01-01T10:45:00Z'), // 在主流程活动A结束后
          resource: 'User2',
          parentCaseId: 'parent_case_1',
          previousActivity: null, // 缺少前置活动信息，需要推断
          endTimestamp: new Date('2024-01-01T10:50:00Z'),
        },
      ];

      mockEventLogRepository.find.mockResolvedValue(boundaryEventLogs);
      mockEventLogRepository.count.mockResolvedValue(boundaryEventLogs.length);

      // 测试边界情况处理
      const result =
        await service.testHierarchicalPreviousActivityInference(processId);

      expect(result).toHaveProperty('totalEvents', 2);
      expect(result).toHaveProperty('eventsNeedingInference', 1);
      // 边界情况应该能够找到最近完成的活动作为前置活动
      expect(result.successfulInferences).toBeGreaterThan(0);
    });

    it('应该能够处理多层级嵌套（三层或更多层级）', async () => {
      const processId = 1;

      // 创建三层嵌套测试数据
      const multiLevelEventLogs = [
        // 第一层：主流程
        {
          id: 1,
          processId: 1,
          caseId: 'level1_case',
          activity: '一级活动A',
          timestamp: new Date('2024-01-01T10:00:00Z'),
          resource: 'User1',
          parentCaseId: null,
          previousActivity: null,
          endTimestamp: new Date('2024-01-01T11:00:00Z'),
        },
        // 第二层：子流程
        {
          id: 2,
          processId: 1,
          caseId: 'level2_case',
          activity: '二级活动B',
          timestamp: new Date('2024-01-01T10:15:00Z'),
          resource: 'User2',
          parentCaseId: 'level1_case',
          previousActivity: null, // 需要推断：应该连接到一级活动A
          endTimestamp: new Date('2024-01-01T10:45:00Z'),
        },
        // 第三层：子子流程
        {
          id: 3,
          processId: 1,
          caseId: 'level3_case',
          activity: '三级活动C',
          timestamp: new Date('2024-01-01T10:30:00Z'),
          resource: 'User3',
          parentCaseId: 'level2_case',
          previousActivity: null, // 需要推断：应该连接到二级活动B
          endTimestamp: new Date('2024-01-01T10:35:00Z'),
        },
      ];

      mockEventLogRepository.find.mockResolvedValue(multiLevelEventLogs);
      mockEventLogRepository.count.mockResolvedValue(
        multiLevelEventLogs.length,
      );

      // 测试多层级嵌套
      const result =
        await service.testHierarchicalPreviousActivityInference(processId);

      expect(result).toHaveProperty('totalEvents', 3);
      expect(result).toHaveProperty('eventsNeedingInference', 2); // 二级和三级活动都需要推断
      expect(result.successfulInferences).toBe(2); // 应该都能成功推断
      expect(result).toHaveProperty('successRate', 100);
    });
  });

  describe('跨层级连接优化', () => {
    it('应该避免从主流程开始节点直接连接到子流程节点', async () => {
      const processId = 1;

      // 创建包含主流程和子流程的测试数据
      const crossLevelEventLogs = [
        // 主流程
        {
          id: 1,
          processId: 1,
          caseId: 'main_case',
          activity: '主流程活动A',
          timestamp: new Date('2024-01-01T10:00:00Z'),
          resource: 'User1',
          parentCaseId: null,
          previousActivity: null,
          endTimestamp: new Date('2024-01-01T10:30:00Z'),
        },
        {
          id: 2,
          processId: 1,
          caseId: 'main_case',
          activity: '主流程活动B',
          timestamp: new Date('2024-01-01T10:35:00Z'),
          resource: 'User1',
          parentCaseId: null,
          previousActivity: '主流程活动A',
          endTimestamp: new Date('2024-01-01T11:00:00Z'),
        },
        // 子流程（在主流程活动A执行期间启动）
        {
          id: 3,
          processId: 1,
          caseId: 'sub_case',
          activity: '子流程活动X',
          timestamp: new Date('2024-01-01T10:15:00Z'),
          resource: 'User2',
          parentCaseId: 'main_case',
          previousActivity: null, // 应该推断为主流程活动A，而不是开始节点
          endTimestamp: new Date('2024-01-01T10:25:00Z'),
        },
      ];

      mockEventLogRepository.find.mockResolvedValue(crossLevelEventLogs);
      mockEventLogRepository.count.mockResolvedValue(
        crossLevelEventLogs.length,
      );

      // 测试跨层级连接优化
      const result =
        await service.testCrossLevelConnectionOptimization(processId);

      expect(result).toHaveProperty('totalEvents', 3);
      expect(result).toHaveProperty('crossLevelConnections');
      expect(result).toHaveProperty('startToMainProcess');
      expect(result).toHaveProperty('startToSubprocess');
      expect(result).toHaveProperty('hasRedundantConnections');

      // 验证优化效果：不应该有冗余的跨层级连接
      // 如果同时存在到主流程和子流程的连接，说明存在冗余
      if (result.startToMainProcess > 0 && result.startToSubprocess > 0) {
        console.warn('检测到冗余连接，需要进一步优化');
      }

      // 验证连接详情
      expect(result.connectionDetails).toBeDefined();
      expect(Array.isArray(result.connectionDetails)).toBe(true);
    });

    it('应该确保子流程节点通过父流程业务活动间接连接', async () => {
      const processId = 1;

      // 创建测试数据，确保子流程应该连接到父流程的业务活动
      const indirectConnectionEventLogs = [
        // 主流程
        {
          id: 1,
          processId: 1,
          caseId: 'parent_case',
          activity: '父流程业务活动',
          timestamp: new Date('2024-01-01T10:00:00Z'),
          resource: 'User1',
          parentCaseId: null,
          previousActivity: null,
          endTimestamp: new Date('2024-01-01T10:45:00Z'),
        },
        // 子流程（在父流程业务活动执行期间启动）
        {
          id: 2,
          processId: 1,
          caseId: 'child_case',
          activity: '子流程业务活动',
          timestamp: new Date('2024-01-01T10:20:00Z'),
          resource: 'User2',
          parentCaseId: 'parent_case',
          previousActivity: null, // 应该推断为父流程业务活动
          endTimestamp: new Date('2024-01-01T10:30:00Z'),
        },
      ];

      mockEventLogRepository.find.mockResolvedValue(
        indirectConnectionEventLogs,
      );
      mockEventLogRepository.count.mockResolvedValue(
        indirectConnectionEventLogs.length,
      );

      // 测试前置活动推断
      const inferenceResult =
        await service.testHierarchicalPreviousActivityInference(processId);

      // 验证推断成功
      expect(inferenceResult.successfulInferences).toBe(1);
      expect(inferenceResult.successRate).toBe(100);

      // 测试跨层级连接优化
      const optimizationResult =
        await service.testCrossLevelConnectionOptimization(processId);

      // 验证连接逻辑
      expect(optimizationResult.totalRelations).toBeGreaterThan(0);

      // 检查是否有从开始节点到子流程的直接连接
      const directSubprocessConnections =
        optimizationResult.connectionDetails.filter(
          (detail) => detail.isSubprocess,
        );

      // 理想情况下，不应该有直接从开始节点到子流程的连接
      console.log('直接子流程连接数:', directSubprocessConnections.length);
    });

    it('应该正确识别和连接子流程的结束节点', async () => {
      const processId = 1;

      // 创建包含子流程结束节点的测试数据
      const subprocessEndEventLogs = [
        // 主流程
        {
          id: 1,
          processId: 1,
          caseId: 'main_case',
          activity: '主流程活动A',
          timestamp: new Date('2024-01-01T10:00:00Z'),
          resource: 'User1',
          parentCaseId: null,
          previousActivity: null,
          endTimestamp: new Date('2024-01-01T10:30:00Z'),
        },
        {
          id: 2,
          processId: 1,
          caseId: 'main_case',
          activity: '主流程活动B',
          timestamp: new Date('2024-01-01T10:35:00Z'),
          resource: 'User1',
          parentCaseId: null,
          previousActivity: '主流程活动A',
          endTimestamp: new Date('2024-01-01T11:00:00Z'),
        },
        {
          id: 3,
          processId: 1,
          caseId: 'main_case',
          activity: '主流程活动C',
          timestamp: new Date('2024-01-01T11:05:00Z'),
          resource: 'User1',
          parentCaseId: null,
          previousActivity: '主流程活动B',
          endTimestamp: new Date('2024-01-01T11:30:00Z'),
        },
        // 子流程（在主流程活动A执行期间启动）
        {
          id: 4,
          processId: 1,
          caseId: 'sub_case',
          activity: '子流程活动X',
          timestamp: new Date('2024-01-01T10:10:00Z'),
          resource: 'User2',
          parentCaseId: 'main_case',
          previousActivity: null, // 应该推断为主流程活动A
          endTimestamp: new Date('2024-01-01T10:20:00Z'),
        },
        {
          id: 5,
          processId: 1,
          caseId: 'sub_case',
          activity: '子流程活动Y',
          timestamp: new Date('2024-01-01T10:25:00Z'),
          resource: 'User2',
          parentCaseId: 'main_case',
          previousActivity: '子流程活动X',
          endTimestamp: new Date('2024-01-01T10:40:00Z'), // 在主流程活动B执行期间结束
        },
      ];

      mockEventLogRepository.find.mockResolvedValue(subprocessEndEventLogs);
      mockEventLogRepository.count.mockResolvedValue(
        subprocessEndEventLogs.length,
      );

      // 测试跨层级连接优化
      const result =
        await service.testCrossLevelConnectionOptimization(processId);

      expect(result).toHaveProperty('totalEvents', 5);
      expect(result).toHaveProperty('subprocessEndConnections');
      expect(result).toHaveProperty('subprocessEndDetails');

      // 验证子流程结束连接
      expect(result.subprocessEndConnections).toBeGreaterThanOrEqual(0);
      expect(Array.isArray(result.subprocessEndDetails)).toBe(true);

      // 如果有子流程结束连接，验证其逻辑正确性
      if (result.subprocessEndConnections > 0) {
        const endConnections = result.subprocessEndDetails;
        expect(endConnections.length).toBe(result.subprocessEndConnections);

        // 验证连接类型
        endConnections.forEach((connection) => {
          expect(connection).toHaveProperty('relation');
          expect(connection).toHaveProperty('frequency');
          expect(connection).toHaveProperty('type', 'subprocess_end_to_parent');

          // 验证连接格式：子流程活动 -> 主流程活动
          expect(connection.relation).toMatch(/^.+ -> .+$/);
        });

        console.log('子流程结束连接详情:', endConnections);
      }
    });

    it('应该处理复杂的多子流程结束节点场景', async () => {
      const processId = 1;

      // 创建包含多个子流程的复杂测试数据
      const complexSubprocessEventLogs = [
        // 主流程
        {
          id: 1,
          processId: 1,
          caseId: 'main_case',
          activity: '主流程开始',
          timestamp: new Date('2024-01-01T10:00:00Z'),
          resource: 'User1',
          parentCaseId: null,
          previousActivity: null,
          endTimestamp: new Date('2024-01-01T10:05:00Z'),
        },
        {
          id: 2,
          processId: 1,
          caseId: 'main_case',
          activity: '主流程中间活动',
          timestamp: new Date('2024-01-01T10:30:00Z'),
          resource: 'User1',
          parentCaseId: null,
          previousActivity: '主流程开始',
          endTimestamp: new Date('2024-01-01T11:00:00Z'),
        },
        {
          id: 3,
          processId: 1,
          caseId: 'main_case',
          activity: '主流程结束',
          timestamp: new Date('2024-01-01T11:30:00Z'),
          resource: 'User1',
          parentCaseId: null,
          previousActivity: '主流程中间活动',
          endTimestamp: new Date('2024-01-01T11:35:00Z'),
        },
        // 第一个子流程
        {
          id: 4,
          processId: 1,
          caseId: 'sub_case_1',
          activity: '子流程1活动A',
          timestamp: new Date('2024-01-01T10:10:00Z'),
          resource: 'User2',
          parentCaseId: 'main_case',
          previousActivity: null,
          endTimestamp: new Date('2024-01-01T10:20:00Z'),
        },
        {
          id: 5,
          processId: 1,
          caseId: 'sub_case_1',
          activity: '子流程1活动B',
          timestamp: new Date('2024-01-01T10:25:00Z'),
          resource: 'User2',
          parentCaseId: 'main_case',
          previousActivity: '子流程1活动A',
          endTimestamp: new Date('2024-01-01T10:35:00Z'), // 结束节点
        },
        // 第二个子流程
        {
          id: 6,
          processId: 1,
          caseId: 'sub_case_2',
          activity: '子流程2活动X',
          timestamp: new Date('2024-01-01T10:40:00Z'),
          resource: 'User3',
          parentCaseId: 'main_case',
          previousActivity: null,
          endTimestamp: new Date('2024-01-01T10:50:00Z'),
        },
        {
          id: 7,
          processId: 1,
          caseId: 'sub_case_2',
          activity: '子流程2活动Y',
          timestamp: new Date('2024-01-01T10:55:00Z'),
          resource: 'User3',
          parentCaseId: 'main_case',
          previousActivity: '子流程2活动X',
          endTimestamp: new Date('2024-01-01T11:10:00Z'), // 结束节点
        },
      ];

      mockEventLogRepository.find.mockResolvedValue(complexSubprocessEventLogs);
      mockEventLogRepository.count.mockResolvedValue(
        complexSubprocessEventLogs.length,
      );

      // 测试复杂场景的跨层级连接优化
      const result =
        await service.testCrossLevelConnectionOptimization(processId);

      expect(result).toHaveProperty('totalEvents', 7);
      expect(result).toHaveProperty('subprocessEndConnections');

      // 验证多个子流程的结束连接
      // 应该有两个子流程结束连接（每个子流程一个）
      expect(result.subprocessEndConnections).toBeGreaterThanOrEqual(0);

      // 验证连接的合理性
      if (result.subprocessEndConnections > 0) {
        console.log(
          '复杂场景子流程结束连接数:',
          result.subprocessEndConnections,
        );
        console.log('连接详情:', result.subprocessEndDetails);

        // 验证每个连接都有正确的格式
        result.subprocessEndDetails.forEach((connection) => {
          expect(connection.relation).toMatch(
            /^子流程\d+活动[A-Z] -> 主流程.+$/,
          );
          expect(connection.frequency).toBeGreaterThan(0);
        });
      }
    });
  });

  describe('parallel activities handling', () => {
    it('should not create sequential relationships for activities with same start time', async () => {
      const sameTime = new Date('2024-01-01T10:00:00Z');
      const parallelEventLogs = [
        {
          id: 1,
          processId: 1,
          caseId: 'case1',
          activity: '开始',
          timestamp: new Date('2024-01-01T09:00:00Z'),
          resource: 'system',
        },
        {
          id: 2,
          processId: 1,
          caseId: 'case1',
          activity: '并行活动A',
          timestamp: sameTime, // 同时开始
          resource: 'user1',
          previousActivity: '开始', // 明确指定前置活动
        },
        {
          id: 3,
          processId: 1,
          caseId: 'case1',
          activity: '并行活动B',
          timestamp: sameTime, // 同时开始
          resource: 'user2',
          previousActivity: '开始', // 明确指定前置活动
        },
        {
          id: 4,
          processId: 1,
          caseId: 'case1',
          activity: '结束',
          timestamp: new Date('2024-01-01T11:00:00Z'),
          resource: 'system',
        },
      ] as EventLog[];

      mockEventLogRepository.find.mockResolvedValue(parallelEventLogs);
      mockCacheService.getDataSourceHash.mockResolvedValue('hash123');
      mockAnalysisResultRepository.save.mockResolvedValue({});

      const result = await service.discoverProcess(1, {});

      // 验证不会创建并行活动之间的直接连接
      const parallelEdge = result.edges.find(
        (edge: any) =>
          (edge.source === '并行活动A' && edge.target === '并行活动B') ||
          (edge.source === '并行活动B' && edge.target === '并行活动A'),
      );

      expect(parallelEdge).toBeUndefined();

      // 验证开始节点到并行活动的连接存在
      const startToParallelA = result.edges.find(
        (edge: any) => edge.source === '开始' && edge.target === '并行活动A',
      );
      const startToParallelB = result.edges.find(
        (edge: any) => edge.source === '开始' && edge.target === '并行活动B',
      );

      expect(startToParallelA).toBeDefined();
      expect(startToParallelB).toBeDefined();
    });

    it('should handle multiple parallel activities in different cases', async () => {
      const sameTime1 = new Date('2024-01-01T10:00:00Z');
      const sameTime2 = new Date('2024-01-02T10:00:00Z');

      const multiCaseParallelLogs = [
        // Case 1 - 并行活动
        {
          id: 1,
          processId: 1,
          caseId: 'case1',
          activity: '开始',
          timestamp: new Date('2024-01-01T09:00:00Z'),
          resource: 'system',
        },
        {
          id: 2,
          processId: 1,
          caseId: 'case1',
          activity: '审核A',
          timestamp: sameTime1,
          resource: 'user1',
          previousActivity: '开始', // 明确指定前置活动
        },
        {
          id: 3,
          processId: 1,
          caseId: 'case1',
          activity: '审核B',
          timestamp: sameTime1,
          resource: 'user2',
          previousActivity: '开始', // 明确指定前置活动
        },
        // Case 2 - 另一组并行活动
        {
          id: 4,
          processId: 1,
          caseId: 'case2',
          activity: '开始',
          timestamp: new Date('2024-01-02T09:00:00Z'),
          resource: 'system',
        },
        {
          id: 5,
          processId: 1,
          caseId: 'case2',
          activity: '审核A',
          timestamp: sameTime2,
          resource: 'user3',
          previousActivity: '开始', // 明确指定前置活动
        },
        {
          id: 6,
          processId: 1,
          caseId: 'case2',
          activity: '审核B',
          timestamp: sameTime2,
          resource: 'user4',
          previousActivity: '开始', // 明确指定前置活动
        },
      ] as EventLog[];

      mockEventLogRepository.find.mockResolvedValue(multiCaseParallelLogs);
      mockCacheService.getDataSourceHash.mockResolvedValue('hash123');
      mockAnalysisResultRepository.save.mockResolvedValue({});

      const result = await service.discoverProcess(1, {});

      // 验证不会创建并行活动之间的直接连接
      const parallelEdges = result.edges.filter(
        (edge: any) =>
          (edge.source === '审核A' && edge.target === '审核B') ||
          (edge.source === '审核B' && edge.target === '审核A'),
      );

      expect(parallelEdges.length).toBe(0);

      // 验证开始节点到并行活动的连接存在
      const startToAuditA = result.edges.find(
        (edge: any) => edge.source === '开始' && edge.target === '审核A',
      );
      const startToAuditB = result.edges.find(
        (edge: any) => edge.source === '开始' && edge.target === '审核B',
      );

      expect(startToAuditA).toBeDefined();
      expect(startToAuditB).toBeDefined();

      // 验证频次正确（两个案例都有这些连接）
      if (startToAuditA) {
        expect(startToAuditA.frequency).toBe(2);
      }
      if (startToAuditB) {
        expect(startToAuditB.frequency).toBe(2);
      }
    });
  });
});
