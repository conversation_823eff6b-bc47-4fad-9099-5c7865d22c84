import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, Between } from 'typeorm';
import { EventLog } from '../entities/event-log.entity';
import {
  AnalysisResult,
  AnalysisType,
  AnalysisStatus,
} from '../entities/analysis-result.entity';
import { CacheService } from './cache.service';
import * as crypto from 'crypto';

export interface DFGNode {
  id: string;
  label: string;
  frequency: number;
  avgDuration?: number;
  minDuration?: number;
  maxDuration?: number;
  // 添加业务字段信息
  businessFields?: Record<string, any>;
  // 添加资源信息
  resources?: Array<{
    resource: string;
    frequency: number;
    percentage: number;
  }>;
  // 添加主要资源
  resource?: string;
  // 层次结构相关字段
  parentCaseId?: string;
  isSubprocessNode?: boolean;
  subprocessLevel?: number;
  groupId?: string; // 用于GoJS分组
  // 节点类型标识
  isStartNode?: boolean; // 标识开始节点（包括子流程开始节点）
  isEndNode?: boolean; // 标识结束节点（包括子流程结束节点）
}

export interface DFGEdge {
  source: string;
  target: string;
  frequency: number;
  avgDuration?: number;
  minDuration?: number;
  maxDuration?: number;
  // 层次结构相关字段
  isSubprocessEdge?: boolean;
  subprocessLevel?: number;
  groupId?: string; // 用于GoJS分组
}

// 层次结构分析结果接口
export interface HierarchicalDFGResult {
  mainProcess: {
    nodes: DFGNode[];
    edges: DFGEdge[];
  };
  subprocesses: Array<{
    parentCaseId: string;
    level: number;
    nodes: DFGNode[];
    edges: DFGEdge[];
    statistics: {
      totalCases: number;
      totalActivities: number;
      avgCaseDuration: number;
    };
  }>;
  hierarchyMap: Map<string, string[]>; // parentCaseId -> childCaseIds
}

// 扩展的节点详细信息接口
export interface NodeDetailInfo {
  id: string;
  label: string;
  basicStats: {
    frequency: number;
    avgDuration: number;
    minDuration: number;
    maxDuration: number;
    medianDuration: number;
    stdDeviation: number;
  };
  timeDistribution: {
    intervals: Array<{
      range: string;
      count: number;
      percentage: number;
    }>;
    quartiles: {
      q1: number;
      q2: number;
      q3: number;
    };
  };
  resourceAnalysis: {
    resources: Array<{
      resource: string;
      frequency: number;
      avgDuration: number;
      efficiency: number;
    }>;
  };
  pathAnalysis: {
    predecessors: Array<{
      activity: string;
      frequency: number;
      percentage: number;
    }>;
    successors: Array<{
      activity: string;
      frequency: number;
      percentage: number;
    }>;
  };
  timePatterns: {
    hourlyDistribution: Array<{
      hour: number;
      count: number;
    }>;
    weeklyDistribution: Array<{
      dayOfWeek: number;
      count: number;
    }>;
  };
  anomalies: Array<{
    caseId: string;
    duration: number;
    reason: string;
    severity: 'low' | 'medium' | 'high';
  }>;
}

// 扩展的连接详细信息接口
export interface EdgeDetailInfo {
  source: string;
  target: string;
  basicStats: {
    frequency: number;
    avgDuration: number;
    minDuration: number;
    maxDuration: number;
    medianDuration: number;
    percentage: number;
  };
  timeDistribution: {
    intervals: Array<{
      range: string;
      count: number;
      percentage: number;
    }>;
    quartiles: {
      q1: number;
      q2: number;
      q3: number;
    };
  };
  trendAnalysis: {
    timeSeriesData: Array<{
      period: string;
      frequency: number;
      avgDuration: number;
    }>;
    trend: 'increasing' | 'decreasing' | 'stable';
  };
  efficiencyAnalysis: {
    comparedToOtherPaths: Array<{
      alternativePath: string;
      frequencyDiff: number;
      durationDiff: number;
    }>;
  };
  contextAnalysis: {
    commonPredecessors: Array<{
      activity: string;
      frequency: number;
    }>;
    commonSuccessors: Array<{
      activity: string;
      frequency: number;
    }>;
  };
  caseDetails: Array<{
    caseId: string;
    sourceTime: Date;
    targetTime: Date;
    duration: number;
  }>;
}

export interface DFGResult {
  nodes: DFGNode[];
  edges: DFGEdge[];
  statistics: {
    totalCases: number;
    totalActivities: number;
    avgCaseDuration: number;
    startActivities: string[];
    endActivities: string[];
  };
  hierarchicalInfo?: {
    hasHierarchy: boolean;
    maxLevel: number;
    subprocessCount: number;
    hierarchyMap: Record<string, string[]>; // parentCaseId -> childCaseIds
  };
}

export interface VariantResult {
  variants: Array<{
    id: string;
    path: string[];
    frequency: number;
    percentage: number;
    avgDuration: number;
    cases: string[];
  }>;
  statistics: {
    totalVariants: number;
    mostFrequentVariant: string;
    variantCoverage: number;
  };
}

export interface PerformanceResult {
  caseStatistics: {
    avgDuration: number;
    minDuration: number;
    maxDuration: number;
    medianDuration: number;
  };
  activityStatistics: Array<{
    activity: string;
    avgDuration: number;
    frequency: number;
    avgWaitingTime: number;
  }>;
  bottlenecks: Array<{
    activity: string;
    avgDuration: number;
    avgWaitingTime: number;
    frequency: number;
    impact: number;
    bottleneckType: 'execution' | 'waiting' | 'combined' | 'resource';
    reason: string;
    resourceAnalysis?: {
      resourceCount: number;
      resourceUtilization: number;
      isResourceConstrained: boolean;
    };
  }>;
  trends?: {
    granularities: {
      [granularity: string]: {
        overall: {
          avgDurationChange: number;
          avgWaitTimeChange: number;
          totalCases: number;
        };
        timeSeries: Array<{
          period: string;
          start: Date;
          end: Date;
          avgDuration: number;
          avgWaitTime: number;
          caseCount: number;
        }>;
        rankings: {
          activity: {
            improvements: Array<{
              name: string;
              description: string;
              change: number;
              currentValue: number;
              previousValue: number;
            }>;
            declines: Array<{
              name: string;
              description: string;
              change: number;
              currentValue: number;
              previousValue: number;
            }>;
          };
          resource: {
            improvements: Array<{
              name: string;
              description: string;
              change: number;
              currentValue: number;
              previousValue: number;
            }>;
            declines: Array<{
              name: string;
              description: string;
              change: number;
              currentValue: number;
              previousValue: number;
            }>;
          };
        };
      };
    };
    activityDurations: {
      current: Array<{
        name: string;
        value: number;
        percentage: number;
      }>;
    };
    resourceWaitTimes: {
      current: {
        legend: string[];
        data: number[];
        periods: string[];
      };
    };
    metadata?: {
      totalEvents: number;
      totalCases: number;
      timeRange: { start: Date; end: Date };
      computedAt: Date;
    };
  };
  options?: {
    forceRefresh?: boolean;
    requiredActivities?: string[];
  };
}

@Injectable()
export class ProcessMiningService {
  private readonly logger = new Logger(ProcessMiningService.name);

  constructor(
    @InjectRepository(EventLog)
    private readonly eventLogRepository: Repository<EventLog>,
    @InjectRepository(AnalysisResult)
    private readonly analysisResultRepository: Repository<AnalysisResult>,
    private readonly cacheService: CacheService,
  ) {}

  async discoverProcess(
    processId: number,
    options: { forceRefresh?: boolean; requiredActivities?: string[] } = {},
  ): Promise<DFGResult> {
    const { forceRefresh = false, requiredActivities = [] } = options;
    this.logger.log(
      `Starting process discovery for processId: ${processId}, forceRefresh: ${forceRefresh}, requiredActivities: ${requiredActivities.length > 0 ? requiredActivities.join(', ') : 'none'}`,
    );

    // 如果不是强制刷新，先检查缓存
    if (!forceRefresh) {
      const cachedResult = await this.getCachedAnalysisResult(
        processId,
        AnalysisType.PROCESS_DISCOVERY,
      );
      if (cachedResult) {
        this.logger.log(
          `Returning cached result for process discovery: ${processId}`,
        );
        return cachedResult;
      }
    }

    // 检查数据量
    const eventLogCount = await this.eventLogRepository.count({
      where: { processId },
    });

    if (eventLogCount === 0) {
      throw new Error('没有找到事件日志数据');
    }

    // 如果数据量过大，抛出错误或使用分页处理
    const MAX_EVENTS = 50000; // 最大事件数限制
    if (eventLogCount > MAX_EVENTS) {
      throw new Error(
        `数据量过大 (${eventLogCount} 条记录)，请联系管理员进行优化处理`,
      );
    }

    // 获取事件日志数据
    let eventLogs = await this.eventLogRepository.find({
      where: { processId },
      order: { caseId: 'ASC', timestamp: 'ASC' },
    });

    // 如果配置了必须包含的活动节点，进行过滤
    if (requiredActivities && requiredActivities.length > 0) {
      eventLogs = this.filterEventLogsByRequiredActivities(
        eventLogs,
        requiredActivities,
      );
      this.logger.log(
        `Filtered event logs by required activities: ${requiredActivities.join(', ')}. Remaining events: ${eventLogs.length}`,
      );
    }

    // 计算数据源哈希值
    const dataSourceHash = this.calculateDataSourceHash(eventLogs);

    // 按案例分组
    const caseGroups = this.groupByCaseId(eventLogs);

    // 分析层次结构
    const hierarchicalAnalysis = this.analyzeHierarchicalStructure(eventLogs);

    // 如果存在层次结构，进行分层处理
    let processedCaseGroups = caseGroups;
    if (hierarchicalAnalysis.hasHierarchy) {
      processedCaseGroups = this.processHierarchicalCases(
        caseGroups,
        hierarchicalAnalysis,
      );
    }

    // 计算活动频率
    const activityFrequency = this.calculateActivityFrequency(eventLogs);

    // 计算直接跟随关系
    const followRelations = this.calculateFollowRelations(
      caseGroups,
      eventLogs,
    );

    // 计算活动间转换的耗时统计
    const transitionDurations = this.calculateTransitionDurations(
      caseGroups,
      eventLogs,
    );

    // 计算活动的耗时统计
    const activityDurations = this.calculateActivityDurations(caseGroups);

    // 计算开始和结束活动
    const startActivities = this.getStartActivities(caseGroups);
    const endActivities = this.getEndActivities(caseGroups);

    // 计算案例持续时间
    const caseDurations = this.calculateCaseDurations(caseGroups);
    const avgCaseDuration =
      caseDurations.reduce((sum, duration) => sum + duration, 0) /
      caseDurations.length;

    // 提取业务字段信息
    const businessFieldsMap = this.extractBusinessFields(eventLogs);
    const resourcesMap = this.extractResources(eventLogs);

    // 构建DFG节点（包含耗时信息、业务字段和层次结构信息）
    const nodes: DFGNode[] = Object.entries(activityFrequency).map(
      ([activity, frequency]) => {
        const durationStats = activityDurations[activity];
        const businessFields = businessFieldsMap.get(activity) || {};
        const resources = resourcesMap.get(activity) || [];

        // 分析该活动的层次结构信息
        const activityEvents = eventLogs.filter(
          (event) => event.activity === activity,
        );
        const subprocessLevels = activityEvents
          .map((event) => hierarchicalAnalysis.levelMap.get(event.caseId) || 0)
          .filter((level, index, arr) => arr.indexOf(level) === index);

        const isSubprocessNode = subprocessLevels.some((level) => level > 0);
        const subprocessLevel = Math.max(...subprocessLevels);

        console.log(
          `🔍 活动 "${activity}": 层级 ${subprocessLevel}, 是子流程节点: ${isSubprocessNode}`,
        );

        // 为子流程节点生成分组ID
        let groupId: string | undefined;
        if (isSubprocessNode && subprocessLevel > 0) {
          // 找到该活动所属的父案例ID
          const parentCaseIds = activityEvents
            .map((event) => event.parentCaseId)
            .filter(Boolean)
            .filter((id, index, arr) => arr.indexOf(id) === index);
          if (parentCaseIds.length > 0) {
            groupId = `subprocess_${parentCaseIds[0]}_level_${subprocessLevel}`;
          }
        }

        return {
          id: activity,
          label: activity,
          frequency,
          avgDuration: durationStats?.avgDuration || 0,
          minDuration: durationStats?.minDuration || 0,
          maxDuration: durationStats?.maxDuration || 0,
          // 添加业务字段信息
          businessFields,
          // 添加资源信息
          resources,
          // 添加最常用的资源作为主要资源
          resource: resources.length > 0 ? resources[0].resource : undefined,
          // 添加层次结构信息
          isSubprocessNode,
          subprocessLevel,
          groupId,
        };
      },
    );

    // 添加开始和结束节点（包括子流程的开始和结束节点）
    const START_NODE_ID = '开始';
    const END_NODE_ID = '结束';

    // 主流程的开始和结束节点
    const startNode: DFGNode = {
      id: START_NODE_ID,
      label: START_NODE_ID,
      frequency: Object.keys(caseGroups).length, // 案例总数
      avgDuration: 0,
      minDuration: 0,
      maxDuration: 0,
      isSubprocessNode: false,
      subprocessLevel: 0,
      isStartNode: true, // 标记为开始节点
    };

    const endNode: DFGNode = {
      id: END_NODE_ID,
      label: END_NODE_ID,
      frequency: Object.keys(caseGroups).length, // 案例总数
      avgDuration: 0,
      minDuration: 0,
      maxDuration: 0,
      isSubprocessNode: false,
      subprocessLevel: 0,
      isEndNode: true, // 标记为结束节点
    };

    // 为每个子流程创建独立的开始和结束节点
    const subprocessStartEndNodes: DFGNode[] = [];
    if (hierarchicalAnalysis.hasHierarchy) {
      hierarchicalAnalysis.hierarchyMap.forEach(
        (childCaseIds, parentCaseId) => {
          const level = hierarchicalAnalysis.levelMap.get(childCaseIds[0]) || 1;
          const groupId = `subprocess_${parentCaseId}_level_${level}`;

          // 子流程开始节点
          const subprocessStartNode: DFGNode = {
            id: `${START_NODE_ID}_${parentCaseId}`,
            label: `${START_NODE_ID}`,
            frequency: childCaseIds.length,
            avgDuration: 0,
            minDuration: 0,
            maxDuration: 0,
            isSubprocessNode: true,
            subprocessLevel: level,
            groupId,
            parentCaseId,
            isStartNode: true, // 标记为开始节点
          };

          // 子流程结束节点
          const subprocessEndNode: DFGNode = {
            id: `${END_NODE_ID}_${parentCaseId}`,
            label: `${END_NODE_ID}`,
            frequency: childCaseIds.length,
            avgDuration: 0,
            minDuration: 0,
            maxDuration: 0,
            isSubprocessNode: true,
            subprocessLevel: level,
            groupId,
            parentCaseId,
            isEndNode: true, // 标记为结束节点
          };

          subprocessStartEndNodes.push(subprocessStartNode, subprocessEndNode);
        },
      );
    }

    // 构建DFG边（包含耗时信息和层次结构信息）
    const edges: DFGEdge[] = Object.entries(followRelations).map(
      ([relation, frequency]) => {
        const [source, target] = relation.split(' -> ');
        const durationStats = transitionDurations[relation];

        // 分析边的层次结构信息
        const relationEvents = eventLogs.filter((event) => {
          // 找到包含这个转换关系的事件
          const caseEvents = processedCaseGroups[event.caseId] || [];
          const eventIndex = caseEvents.findIndex(
            (e) => e.activity === event.activity,
          );
          if (eventIndex > 0) {
            const prevEvent = caseEvents[eventIndex - 1];
            return prevEvent.activity === source && event.activity === target;
          }
          return false;
        });

        const subprocessLevels = relationEvents
          .map((event) => hierarchicalAnalysis.levelMap.get(event.caseId) || 0)
          .filter((level, index, arr) => arr.indexOf(level) === index);

        const isSubprocessEdge = subprocessLevels.some((level) => level > 0);
        const subprocessLevel = Math.max(...subprocessLevels, 0);

        // 为子流程边生成分组ID
        let groupId: string | undefined;
        if (isSubprocessEdge && subprocessLevel > 0) {
          const parentCaseIds = relationEvents
            .map((event) => event.parentCaseId)
            .filter(Boolean)
            .filter((id, index, arr) => arr.indexOf(id) === index);
          if (parentCaseIds.length > 0) {
            groupId = `subprocess_${parentCaseIds[0]}_level_${subprocessLevel}`;
          }
        }

        return {
          source,
          target,
          frequency,
          avgDuration: durationStats?.avgDuration || 0,
          minDuration: durationStats?.minDuration || 0,
          maxDuration: durationStats?.maxDuration || 0,
          isSubprocessEdge,
          subprocessLevel,
          groupId,
        };
      },
    );

    // 添加从开始节点到开始活动的边
    startActivities.forEach((activity) => {
      const frequency = Object.values(caseGroups).filter(
        (events) => events.length > 0 && events[0].activity === activity,
      ).length;

      edges.push({
        source: START_NODE_ID,
        target: activity,
        frequency,
        avgDuration: 0, // 开始节点到第一个活动没有等待时间
        minDuration: 0,
        maxDuration: 0,
      });
    });

    // 添加从结束活动到结束节点的边
    endActivities.forEach((activity) => {
      const frequency = Object.values(caseGroups).filter(
        (events) =>
          events.length > 0 && events[events.length - 1].activity === activity,
      ).length;

      edges.push({
        source: activity,
        target: END_NODE_ID,
        frequency,
        avgDuration: 0, // 最后一个活动到结束节点没有等待时间
        minDuration: 0,
        maxDuration: 0,
      });
    });

    // 为子流程添加开始和结束边
    if (hierarchicalAnalysis.hasHierarchy) {
      hierarchicalAnalysis.hierarchyMap.forEach(
        (childCaseIds, parentCaseId) => {
          const level = hierarchicalAnalysis.levelMap.get(childCaseIds[0]) || 1;
          const groupId = `subprocess_${parentCaseId}_level_${level}`;
          const subprocessStartNodeId = `${START_NODE_ID}_${parentCaseId}`;
          const subprocessEndNodeId = `${END_NODE_ID}_${parentCaseId}`;

          // 获取该子流程的案例组
          const subprocessCaseGroups = Object.fromEntries(
            Object.entries(caseGroups).filter(([caseId]) =>
              childCaseIds.includes(caseId),
            ),
          );

          // 计算子流程的开始和结束活动
          const subprocessStartActivities =
            this.getStartActivities(subprocessCaseGroups);
          const subprocessEndActivities =
            this.getEndActivities(subprocessCaseGroups);

          // 添加从子流程开始节点到开始活动的边
          subprocessStartActivities.forEach((activity) => {
            const frequency = Object.values(subprocessCaseGroups).filter(
              (events) => events.length > 0 && events[0].activity === activity,
            ).length;

            edges.push({
              source: subprocessStartNodeId,
              target: activity,
              frequency,
              avgDuration: 0,
              minDuration: 0,
              maxDuration: 0,
              isSubprocessEdge: true,
              subprocessLevel: level,
              groupId,
            });
          });

          // 添加从结束活动到子流程结束节点的边
          subprocessEndActivities.forEach((activity) => {
            const frequency = Object.values(subprocessCaseGroups).filter(
              (events) =>
                events.length > 0 &&
                events[events.length - 1].activity === activity,
            ).length;

            edges.push({
              source: activity,
              target: subprocessEndNodeId,
              frequency,
              avgDuration: 0,
              minDuration: 0,
              maxDuration: 0,
              isSubprocessEdge: true,
              subprocessLevel: level,
              groupId,
            });
          });
        },
      );
    }

    // 过滤无效子流程（只包含开始和结束节点的子流程）
    const filteredResult = this.filterEmptySubprocesses(
      [startNode, ...nodes, endNode, ...subprocessStartEndNodes],
      edges,
      hierarchicalAnalysis,
    );

    const result: DFGResult = {
      nodes: filteredResult.nodes,
      edges: filteredResult.edges,
      statistics: {
        totalCases: Object.keys(caseGroups).length,
        totalActivities: nodes.length, // 不包含开始和结束节点
        avgCaseDuration,
        startActivities,
        endActivities,
      },
      // 添加层次结构信息
      hierarchicalInfo: hierarchicalAnalysis.hasHierarchy
        ? {
            hasHierarchy: true,
            maxLevel: hierarchicalAnalysis.maxLevel,
            subprocessCount: filteredResult.validSubprocessCount,
            hierarchyMap: Object.fromEntries(hierarchicalAnalysis.hierarchyMap),
          }
        : undefined,
    };

    // 保存分析结果到数据库和缓存
    await this.saveAnalysisResultWithCache(
      processId,
      AnalysisType.PROCESS_DISCOVERY,
      result,
      dataSourceHash,
    );

    this.logger.log(`Process discovery completed for processId: ${processId}`);
    return result;
  }

  async analyzeVariants(processId: number): Promise<VariantResult> {
    // 检查数据量
    const eventLogCount = await this.eventLogRepository.count({
      where: { processId },
    });

    const MAX_EVENTS = 50000;
    if (eventLogCount > MAX_EVENTS) {
      throw new Error(
        `数据量过大 (${eventLogCount} 条记录)，请联系管理员进行优化处理`,
      );
    }

    const eventLogs = await this.eventLogRepository.find({
      where: { processId },
      order: { caseId: 'ASC', timestamp: 'ASC' },
    });

    const caseGroups = this.groupByCaseId(eventLogs);
    const variants = new Map<
      string,
      { cases: string[]; durations: number[] }
    >();

    // 计算每个案例的变体路径
    Object.entries(caseGroups).forEach(([caseId, events]) => {
      const path = events.map((event) => event.activity);
      const pathString = path.join(' -> ');

      const duration = this.calculateCaseDuration(events);

      if (!variants.has(pathString)) {
        variants.set(pathString, { cases: [], durations: [] });
      }

      variants.get(pathString)!.cases.push(caseId);
      variants.get(pathString)!.durations.push(duration);
    });

    const totalCases = Object.keys(caseGroups).length;
    const variantResults = Array.from(variants.entries())
      .map(([path, data], index) => {
        const avgDuration =
          data.durations.reduce((sum, d) => sum + d, 0) / data.durations.length;
        return {
          id: `variant_${index + 1}`,
          path: path.split(' -> '),
          frequency: data.cases.length,
          percentage: (data.cases.length / totalCases) * 100,
          avgDuration,
          cases: data.cases,
        };
      })
      .sort((a, b) => b.frequency - a.frequency);

    const result: VariantResult = {
      variants: variantResults,
      statistics: {
        totalVariants: variantResults.length,
        mostFrequentVariant: variantResults[0]?.id || '',
        variantCoverage: variantResults
          .slice(0, 10)
          .reduce((sum, v) => sum + v.percentage, 0),
      },
    };

    await this.saveAnalysisResult(
      processId,
      AnalysisType.VARIANT_ANALYSIS,
      result,
    );
    return result;
  }

  async analyzePerformance(
    processId: number,
    options: { forceRefresh?: boolean; requiredActivities?: string[] } = {},
  ): Promise<PerformanceResult> {
    const { forceRefresh = false, requiredActivities } = options;
    this.logger.log(
      `Starting performance analysis for processId: ${processId}, forceRefresh: ${forceRefresh}`,
    );

    // 如果不是强制刷新，先检查缓存
    if (!forceRefresh) {
      const cachedResult = await this.getCachedAnalysisResult(
        processId,
        AnalysisType.PERFORMANCE_ANALYSIS,
      );
      if (cachedResult) {
        this.logger.log(
          `Returning cached result for performance analysis: ${processId}`,
        );
        return {
          ...cachedResult,
          options: {
            forceRefresh: false,
            requiredActivities: options.requiredActivities || [],
          },
        };
      }
    }

    // 检查数据量
    const eventLogCount = await this.eventLogRepository.count({
      where: { processId },
    });

    if (eventLogCount === 0) {
      throw new Error('没有找到事件日志数据');
    }

    const MAX_EVENTS = 50000;
    if (eventLogCount > MAX_EVENTS) {
      throw new Error(
        `数据量过大 (${eventLogCount} 条记录)，请联系管理员进行优化处理`,
      );
    }

    let eventLogs = await this.eventLogRepository.find({
      where: { processId },
      order: { caseId: 'ASC', timestamp: 'ASC' },
    });

    // 如果配置了必须包含的活动节点，进行过滤
    if (requiredActivities && requiredActivities.length > 0) {
      eventLogs = this.filterEventLogsByRequiredActivities(
        eventLogs,
        requiredActivities,
      );
      this.logger.log(
        `Filtered event logs by required activities: ${requiredActivities.join(', ')}. Remaining events: ${eventLogs.length}`,
      );
    }

    const caseGroups = this.groupByCaseId(eventLogs);

    // 计算案例统计
    const caseDurations = Object.values(caseGroups).map((events) =>
      this.calculateCaseDuration(events),
    );
    caseDurations.sort((a, b) => a - b);

    const caseStatistics = {
      avgDuration:
        caseDurations.reduce((sum, d) => sum + d, 0) / caseDurations.length,
      minDuration: Math.min(...caseDurations),
      maxDuration: Math.max(...caseDurations),
      medianDuration: caseDurations[Math.floor(caseDurations.length / 2)],
    };

    // 计算活动统计
    const activityStats = new Map<
      string,
      { durations: number[]; frequency: number; waitingTimes: number[] }
    >();

    Object.values(caseGroups).forEach((events) => {
      events.forEach((event, index) => {
        if (!activityStats.has(event.activity)) {
          activityStats.set(event.activity, {
            durations: [],
            frequency: 0,
            waitingTimes: [],
          });
        }

        const stats = activityStats.get(event.activity)!;
        stats.frequency++;

        // 计算等待时间（与前一个活动的间隔）
        // 注意：在只有单一时间戳的情况下，这实际上是"转换时间"，包含前一个活动的执行时间和真正的等待时间
        if (index > 0) {
          const previousEvent = events[index - 1];

          let previousEndTime: number;
          if (previousEvent.endTimestamp) {
            // 如果有结束时间戳，使用结束时间计算真正的等待时间
            previousEndTime = previousEvent.endTimestamp.getTime();
          } else {
            // 如果只有开始时间戳，按行业标准计算转换时间
            // 转换时间 = 当前活动开始时间 - 前一个活动开始时间
            // 这包含了前一个活动的执行时间 + 真正的等待时间
            previousEndTime = previousEvent.timestamp.getTime();
          }

          const waitingTime = event.timestamp.getTime() - previousEndTime;

          // 只添加非负的等待时间
          if (waitingTime >= 0) {
            stats.waitingTimes.push(waitingTime); // 保持毫秒单位
          }
        }

        // 计算活动持续时间
        let duration = 0;

        // 优先使用endTimestamp计算活动持续时间
        if (event.endTimestamp) {
          duration = event.endTimestamp.getTime() - event.timestamp.getTime();
        } else if (index < events.length - 1) {
          // 如果没有endTimestamp，使用到下一个活动开始时间的间隔
          const nextEvent = events[index + 1];
          duration = nextEvent.timestamp.getTime() - event.timestamp.getTime();
        } else {
          // 对于最后一个活动且没有endTimestamp，尝试估算一个合理的持续时间
          // 使用该活动在其他案例中的平均持续时间，如果没有则使用1小时
          const existingDurations = stats.durations.filter((d) => d > 0);
          if (existingDurations.length > 0) {
            duration =
              existingDurations.reduce((sum, d) => sum + d, 0) /
              existingDurations.length;
          } else {
            duration = 1000 * 60 * 60; // 1小时的毫秒数
          }
        }

        // 只添加有效的持续时间（大于0）
        if (duration > 0) {
          stats.durations.push(duration);
        }
      });
    });

    const activityStatistics = Array.from(activityStats.entries()).map(
      ([activity, stats]) => {
        // 计算活动的平均持续时间
        const avgDuration =
          stats.durations.length > 0
            ? stats.durations.reduce((sum, d) => sum + d, 0) /
              stats.durations.length
            : 0;

        return {
          activity,
          avgDuration,
          frequency: stats.frequency,
          // 注意：在只有单一时间戳时，这实际上是"平均转换时间"（包含前一个活动的执行时间 + 等待时间）
          avgWaitingTime:
            stats.waitingTimes.length > 0
              ? stats.waitingTimes.reduce((sum, t) => sum + t, 0) /
                stats.waitingTimes.length
              : 0,
        };
      },
    );

    // 识别瓶颈（综合考虑执行时间、等待时间和资源配置）
    const bottlenecks = await this.identifyBottlenecks(
      processId,
      activityStatistics,
    );

    // 计算性能趋势数据
    this.logger.log(`Computing performance trends for process ${processId}`);
    const trends = await this.computePerformanceTrends(eventLogs);

    const result: PerformanceResult = {
      caseStatistics,
      activityStatistics,
      bottlenecks,
      trends,
      options: {
        forceRefresh,
        requiredActivities: options.requiredActivities || [],
      },
    };

    // 计算数据源哈希值用于缓存失效检测
    const dataSourceHash = this.calculateDataSourceHash(eventLogs);

    // 保存到数据库和缓存，设置较长的TTL（2小时）因为性能分析计算较重
    await this.saveAnalysisResultWithCache(
      processId,
      AnalysisType.PERFORMANCE_ANALYSIS,
      result,
      dataSourceHash,
      7200, // 2小时TTL
    );

    this.logger.log(
      `Performance analysis completed for processId: ${processId}`,
    );
    return result;
  }

  /**
   * 计算性能趋势数据
   */
  private async computePerformanceTrends(eventLogs: EventLog[]) {
    if (eventLogs.length === 0) {
      return undefined;
    }

    try {
      // 计算数据的实际时间范围
      const timestamps = eventLogs.map((event) => event.timestamp.getTime());
      const start = new Date(Math.min(...timestamps));
      const end = new Date(Math.max(...timestamps));

      // 按案例分组
      const caseGroups = this.groupByCaseId(eventLogs);
      const totalCases = Object.keys(caseGroups).length;

      // 预计算不同粒度的数据
      const granularities = ['day', 'week', 'month', 'year'];
      const granularityData: Record<string, any> = {};

      for (const granularity of granularities) {
        // 生成时间序列数据
        const timeSeries = this.generateTimeSeriesData(
          caseGroups,
          granularity,
          start,
          end,
        );

        // 计算整体趋势
        const overall = this.calculateOverallTrends(timeSeries);

        // 计算排行榜数据
        const rankings = this.calculateRankings(caseGroups, timeSeries);

        granularityData[granularity] = {
          overall: {
            ...overall,
            totalCases,
          },
          timeSeries,
          rankings,
        };
      }

      // 计算活动耗时占比和资源等待时间
      const activityDurations = this.calculateActivityDurationChart(caseGroups);
      const resourceWaitTimes = this.calculateResourceWaitTimes(caseGroups);

      return {
        granularities: granularityData,
        activityDurations,
        resourceWaitTimes,
        metadata: {
          totalEvents: eventLogs.length,
          totalCases,
          timeRange: { start, end },
          computedAt: new Date(),
        },
      };
    } catch (error) {
      this.logger.error('Failed to compute performance trends:', error);
      return undefined;
    }
  }

  /**
   * 识别瓶颈活动（综合考虑执行时间、等待时间和资源配置）
   */
  private async identifyBottlenecks(
    processId: number,
    activityStatistics: Array<{
      activity: string;
      avgDuration: number;
      frequency: number;
      avgWaitingTime: number;
    }>,
  ): Promise<
    Array<{
      activity: string;
      avgDuration: number;
      avgWaitingTime: number;
      frequency: number;
      impact: number;
      bottleneckType: 'execution' | 'waiting' | 'combined' | 'resource';
      reason: string;
      resourceAnalysis?: {
        resourceCount: number;
        resourceUtilization: number;
        isResourceConstrained: boolean;
      };
    }>
  > {
    if (activityStatistics.length === 0) return [];

    // 获取资源分析数据
    const resourceAnalysisMap =
      await this.getActivityResourceAnalysis(processId);

    // 计算各项指标的统计信息
    const durations = activityStatistics
      .map((stat) => stat.avgDuration)
      .filter((d) => d > 0);
    const waitingTimes = activityStatistics
      .map((stat) => stat.avgWaitingTime)
      .filter((w) => w > 0);

    // 计算阈值（根据数据量动态调整）
    let durationThreshold = 0;
    let waitingTimeThreshold = 0;

    if (durations.length > 0) {
      if (durations.length <= 3) {
        // 数据量少时，使用平均值作为阈值
        durationThreshold =
          durations.reduce((sum, d) => sum + d, 0) / durations.length;
      } else {
        // 数据量多时，使用75分位数
        durationThreshold = this.calculatePercentile(durations, 0.75);
      }
    }

    if (waitingTimes.length > 0) {
      if (waitingTimes.length <= 3) {
        // 数据量少时，使用平均值作为阈值
        waitingTimeThreshold =
          waitingTimes.reduce((sum, w) => sum + w, 0) / waitingTimes.length;
      } else {
        // 数据量多时，使用75分位数
        waitingTimeThreshold = this.calculatePercentile(waitingTimes, 0.75);
      }
    }

    // 识别潜在瓶颈
    const potentialBottlenecks = activityStatistics
      .map((stat) => {
        const isExecutionBottleneck = stat.avgDuration > durationThreshold;
        // 注意：avgWaitingTime 在只有单一时间戳时实际上是"转换时间"（包含前一个活动的执行时间 + 等待时间）
        const isWaitingBottleneck = stat.avgWaitingTime > waitingTimeThreshold;

        // 获取资源分析数据
        const resourceAnalysis = resourceAnalysisMap.get(stat.activity);

        if (!isExecutionBottleneck && !isWaitingBottleneck) {
          return null; // 不是瓶颈
        }

        // 计算综合影响分数
        let impact = 0;
        let bottleneckType: 'execution' | 'waiting' | 'combined' | 'resource';
        let reason = '';

        if (isExecutionBottleneck && isWaitingBottleneck) {
          // 既有执行瓶颈又有等待瓶颈
          impact = stat.frequency * (stat.avgDuration + stat.avgWaitingTime);
          bottleneckType = 'combined';
          reason = `执行时间长(${this.formatDurationForReason(stat.avgDuration)})且等待时间长(${this.formatDurationForReason(stat.avgWaitingTime)})`;
        } else if (isExecutionBottleneck) {
          // 主要是执行瓶颈
          impact = stat.frequency * stat.avgDuration * 1.2; // 执行瓶颈权重稍高
          bottleneckType = 'execution';
          reason = `执行时间较长(${this.formatDurationForReason(stat.avgDuration)})，可能存在处理效率问题`;
        } else {
          // 等待时间长，需要进一步分析是否为资源瓶颈
          if (resourceAnalysis && resourceAnalysis.isResourceConstrained) {
            // 确实是资源约束导致的瓶颈
            impact = stat.frequency * stat.avgWaitingTime * 1.1; // 资源瓶颈权重稍高
            bottleneckType = 'resource';
            reason = `资源配置不足(${resourceAnalysis.resourceCount}个资源，利用率${(resourceAnalysis.resourceUtilization * 100).toFixed(1)}%)，导致等待时间长(${this.formatDurationForReason(stat.avgWaitingTime)})`;
          } else {
            // 等待时间长但不是资源约束，可能是流程设计问题
            impact = stat.frequency * stat.avgWaitingTime;
            bottleneckType = 'waiting';
            if (resourceAnalysis && resourceAnalysis.resourceCount > 0) {
              reason = `等待时间较长(${this.formatDurationForReason(stat.avgWaitingTime)})，但资源配置充足(${resourceAnalysis.resourceCount}个资源)，可能存在流程设计或依赖关系问题`;
            } else {
              reason = `等待时间较长(${this.formatDurationForReason(stat.avgWaitingTime)})，可能存在流程设计问题`;
            }
          }
        }

        return {
          activity: stat.activity,
          avgDuration: stat.avgDuration,
          avgWaitingTime: stat.avgWaitingTime,
          frequency: stat.frequency,
          impact,
          bottleneckType,
          reason,
          resourceAnalysis: resourceAnalysis
            ? {
                resourceCount: resourceAnalysis.resourceCount,
                resourceUtilization: resourceAnalysis.resourceUtilization,
                isResourceConstrained: resourceAnalysis.isResourceConstrained,
              }
            : undefined,
        };
      })
      .filter((item): item is NonNullable<typeof item> => item !== null);

    // 按影响程度排序，取前5个
    return potentialBottlenecks.sort((a, b) => b.impact - a.impact).slice(0, 5);
  }

  /**
   * 计算百分位数
   */
  private calculatePercentile(values: number[], percentile: number): number {
    if (values.length === 0) return 0;

    const sorted = [...values].sort((a, b) => a - b);
    const index = Math.ceil(sorted.length * percentile) - 1;
    return sorted[Math.max(0, index)];
  }

  /**
   * 格式化持续时间用于原因描述
   */
  private formatDurationForReason(milliseconds: number): string {
    if (milliseconds < 60000) {
      return `${Math.round(milliseconds / 1000)}秒`;
    } else if (milliseconds < 3600000) {
      return `${Math.round(milliseconds / 60000)}分钟`;
    } else if (milliseconds < 86400000) {
      return `${(milliseconds / 3600000).toFixed(1)}小时`;
    } else {
      return `${(milliseconds / 86400000).toFixed(1)}天`;
    }
  }

  /**
   * 获取活动的资源分析数据
   */
  private async getActivityResourceAnalysis(processId: number): Promise<
    Map<
      string,
      {
        resourceCount: number;
        resourceUtilization: number;
        isResourceConstrained: boolean;
        resources: Array<{
          resource: string;
          frequency: number;
          avgDuration: number;
        }>;
      }
    >
  > {
    const eventLogs = await this.eventLogRepository.find({
      where: { processId },
      select: ['activity', 'resource', 'timestamp', 'endTimestamp'],
    });

    const activityResourceMap = new Map<
      string,
      {
        resourceCount: number;
        resourceUtilization: number;
        isResourceConstrained: boolean;
        resources: Array<{
          resource: string;
          frequency: number;
          avgDuration: number;
        }>;
      }
    >();

    // 按活动分组
    const activityGroups = new Map<string, EventLog[]>();
    eventLogs.forEach((log) => {
      if (!activityGroups.has(log.activity)) {
        activityGroups.set(log.activity, []);
      }
      activityGroups.get(log.activity)!.push(log);
    });

    // 分析每个活动的资源情况
    activityGroups.forEach((logs, activity) => {
      const resourceStats = new Map<
        string,
        { frequency: number; durations: number[] }
      >();

      logs.forEach((log) => {
        if (log.resource) {
          if (!resourceStats.has(log.resource)) {
            resourceStats.set(log.resource, { frequency: 0, durations: [] });
          }
          const stats = resourceStats.get(log.resource)!;
          stats.frequency++;

          // 计算持续时间
          if (log.endTimestamp) {
            const duration =
              log.endTimestamp.getTime() - log.timestamp.getTime();
            stats.durations.push(duration);
          }
        }
      });

      const resourceCount = resourceStats.size;
      const totalExecutions = logs.length;
      const resourcesWithData = logs.filter((log) => log.resource).length;

      // 计算资源利用率（有资源信息的执行次数 / 总执行次数）
      const resourceUtilization =
        resourcesWithData > 0 ? resourcesWithData / totalExecutions : 0;

      // 判断是否为资源约束（资源种类少且利用率高）
      const isResourceConstrained =
        resourceCount > 0 && resourceCount <= 2 && resourceUtilization > 0.8;

      const resources = Array.from(resourceStats.entries()).map(
        ([resource, stats]) => ({
          resource,
          frequency: stats.frequency,
          avgDuration:
            stats.durations.length > 0
              ? stats.durations.reduce((sum, d) => sum + d, 0) /
                stats.durations.length
              : 0,
        }),
      );

      activityResourceMap.set(activity, {
        resourceCount,
        resourceUtilization,
        isResourceConstrained,
        resources,
      });
    });

    return activityResourceMap;
  }

  private groupByCaseId(eventLogs: EventLog[]): Record<string, EventLog[]> {
    return eventLogs.reduce(
      (groups, event) => {
        if (!groups[event.caseId]) {
          groups[event.caseId] = [];
        }
        groups[event.caseId].push(event);
        return groups;
      },
      {} as Record<string, EventLog[]>,
    );
  }

  private calculateActivityFrequency(
    eventLogs: EventLog[],
  ): Record<string, number> {
    return eventLogs.reduce(
      (freq, event) => {
        freq[event.activity] = (freq[event.activity] || 0) + 1;
        return freq;
      },
      {} as Record<string, number>,
    );
  }

  private calculateFollowRelations(
    caseGroups: Record<string, EventLog[]>,
    allEventLogs: EventLog[],
  ): Record<string, number> {
    const relations: Record<string, number> = {};
    let relationsByPreviousActivityCount = 0;
    let relationsByTimeOrderCount = 0;
    let subprocessEndConnectionsCount = 0;

    Object.values(caseGroups).forEach((events) => {
      // 首先尝试使用前置活动字段构建关系（传入完整的事件数据集）
      const relationsByPreviousActivity = this.buildRelationsByPreviousActivity(
        events,
        allEventLogs,
      );

      // 将基于前置活动字段的关系添加到结果中
      Object.entries(relationsByPreviousActivity).forEach(
        ([relation, count]) => {
          relations[relation] = (relations[relation] || 0) + count;
          relationsByPreviousActivityCount += count;
        },
      );

      // 对于没有前置活动信息的事件，使用时间顺序推断关系
      const relationsByTimeOrder = this.buildRelationsByTimeOrder(events);

      // 只添加那些不与前置活动字段冲突的时间顺序关系
      Object.entries(relationsByTimeOrder).forEach(([relation, count]) => {
        // 如果这个关系还没有通过前置活动字段建立，则添加
        if (!relationsByPreviousActivity[relation]) {
          relations[relation] = (relations[relation] || 0) + count;
          relationsByTimeOrderCount += count;
        }
      });
    });

    // 🔥 新增：处理子流程结束节点的跨层级连接
    const subprocessEndConnections =
      this.buildSubprocessEndConnections(allEventLogs);
    Object.entries(subprocessEndConnections).forEach(([relation, count]) => {
      relations[relation] = (relations[relation] || 0) + count;
      subprocessEndConnectionsCount += count;
    });

    this.logger.log(
      `🔗 流程关系构建完成: 基于前置活动字段 ${relationsByPreviousActivityCount} 个关系, ` +
        `基于时间顺序 ${relationsByTimeOrderCount} 个关系, ` +
        `子流程结束连接 ${subprocessEndConnectionsCount} 个关系`,
    );

    return relations;
  }

  /**
   * 构建子流程结束节点的跨层级连接
   * 识别子流程中的结束节点，并将其连接到父流程中的相应活动
   */
  private buildSubprocessEndConnections(
    allEventLogs: EventLog[],
  ): Record<string, number> {
    const connections: Record<string, number> = {};

    // 按案例分组
    const caseGroups = this.groupByCaseId(allEventLogs);

    // 识别所有子流程案例（有parentCaseId的案例）
    const subprocessCases = Object.keys(caseGroups).filter((caseId) => {
      const events = caseGroups[caseId];
      return events.some((event) => event.parentCaseId);
    });

    console.log(`🔍 识别到 ${subprocessCases.length} 个子流程案例`);

    subprocessCases.forEach((subprocessCaseId) => {
      const subprocessEvents = caseGroups[subprocessCaseId];
      if (subprocessEvents.length === 0) return;

      // 获取父流程案例ID
      const parentCaseId = subprocessEvents[0].parentCaseId;
      if (!parentCaseId) return;

      // 识别子流程的结束节点
      const endNodes = this.identifySubprocessEndNodes(
        subprocessEvents,
        allEventLogs,
      );

      console.log(
        `📍 子流程 ${subprocessCaseId} 的结束节点: ${endNodes.map((node) => node.activity).join(', ')}`,
      );

      // 为每个结束节点建立到父流程的连接
      endNodes.forEach((endNode) => {
        const parentConnection = this.findParentConnectionForSubprocessEnd(
          endNode,
          parentCaseId,
          allEventLogs,
        );

        if (parentConnection) {
          const relation = `${endNode.activity} -> ${parentConnection}`;
          connections[relation] = (connections[relation] || 0) + 1;

          console.log(
            `🔗 子流程结束连接: ${endNode.activity} (${subprocessCaseId}) -> ${parentConnection} (${parentCaseId})`,
          );
        }
      });
    });

    return connections;
  }

  /**
   * 识别子流程中的结束节点
   * 结束节点是指在子流程中没有其他节点将其作为前置活动的节点
   */
  private identifySubprocessEndNodes(
    subprocessEvents: EventLog[],
    allEventLogs: EventLog[],
  ): EventLog[] {
    if (subprocessEvents.length === 0) return [];

    // 按时间排序子流程事件
    const sortedEvents = subprocessEvents.sort(
      (a, b) => a.timestamp.getTime() - b.timestamp.getTime(),
    );

    // 收集子流程内所有被引用为前置活动的活动
    const referencedActivities = new Set<string>();

    subprocessEvents.forEach((event) => {
      if (event.previousActivity && event.previousActivity.trim() !== '') {
        referencedActivities.add(event.previousActivity.trim());
      }
    });

    // 找到没有被引用为前置活动的节点，这些可能是结束节点
    const potentialEndNodes = sortedEvents.filter(
      (event) => !referencedActivities.has(event.activity),
    );

    // 如果没有找到潜在的结束节点，使用最后一个事件作为结束节点
    if (potentialEndNodes.length === 0) {
      console.log(
        `⚠️ 子流程 ${subprocessEvents[0].caseId} 没有明确的结束节点，使用最后一个活动`,
      );
      return [sortedEvents[sortedEvents.length - 1]];
    }

    // 从潜在结束节点中选择时间最晚的节点
    const actualEndNodes = potentialEndNodes.filter((node) => {
      // 检查是否有其他节点在此节点之后
      const hasLaterNodes = sortedEvents.some(
        (event) => event.timestamp.getTime() > node.timestamp.getTime(),
      );
      return (
        !hasLaterNodes ||
        // 或者是时间最晚的几个节点之一（允许并行结束）
        potentialEndNodes.filter(
          (n) => n.timestamp.getTime() >= node.timestamp.getTime(),
        ).length <= 2
      );
    });

    return actualEndNodes.length > 0
      ? actualEndNodes
      : [potentialEndNodes[potentialEndNodes.length - 1]];
  }

  /**
   * 为子流程结束节点找到父流程中的连接目标
   */
  private findParentConnectionForSubprocessEnd(
    endNode: EventLog,
    parentCaseId: string,
    allEventLogs: EventLog[],
  ): string | null {
    // 获取父流程事件
    const parentEvents = allEventLogs.filter(
      (event) => event.caseId === parentCaseId,
    );
    if (parentEvents.length === 0) return null;

    // 按时间排序父流程事件
    const sortedParentEvents = parentEvents.sort(
      (a, b) => a.timestamp.getTime() - b.timestamp.getTime(),
    );

    // 获取子流程结束时间
    const endTime = endNode.endTimestamp
      ? endNode.endTimestamp.getTime()
      : endNode.timestamp.getTime();

    // 查找在子流程结束时正在执行的父流程活动
    for (const parentEvent of sortedParentEvents) {
      const parentStartTime = parentEvent.timestamp.getTime();

      // 计算父活动的结束时间
      let parentEndTime: number;
      if (parentEvent.endTimestamp) {
        parentEndTime = parentEvent.endTimestamp.getTime();
      } else {
        // 使用下一个活动的开始时间作为结束时间
        const nextParentEvent = sortedParentEvents.find(
          (e) => e.timestamp.getTime() > parentStartTime,
        );
        parentEndTime = nextParentEvent
          ? nextParentEvent.timestamp.getTime()
          : endTime + 1;
      }

      // 检查父活动是否在子流程结束时仍在执行
      if (parentStartTime <= endTime && endTime <= parentEndTime) {
        console.log(
          `✅ 找到正在执行的父流程活动: ${parentEvent.activity} (${parentStartTime} <= ${endTime} <= ${parentEndTime})`,
        );
        return parentEvent.activity;
      }
    }

    // 如果没有找到正在执行的活动，查找子流程结束后最近开始的父流程活动
    const nextParentActivity = sortedParentEvents.find(
      (event) => event.timestamp.getTime() > endTime,
    );

    if (nextParentActivity) {
      console.log(
        `⏭️ 连接到子流程结束后的下一个父流程活动: ${nextParentActivity.activity}`,
      );
      return nextParentActivity.activity;
    }

    // 如果都没有找到，连接到父流程的最后一个活动
    if (sortedParentEvents.length > 0) {
      const lastParentActivity =
        sortedParentEvents[sortedParentEvents.length - 1];
      console.log(
        `🔚 连接到父流程的最后一个活动: ${lastParentActivity.activity}`,
      );
      return lastParentActivity.activity;
    }

    return null;
  }

  /**
   * 基于前置活动字段构建活动关系（支持多层级嵌套流程）
   */
  private buildRelationsByPreviousActivity(
    events: EventLog[],
    allEventLogs: EventLog[],
  ): Record<string, number> {
    const relations: Record<string, number> = {};

    events.forEach((event) => {
      if (event.previousActivity && event.previousActivity.trim() !== '') {
        // 直接使用前置活动字段建立关系
        const relation = `${event.previousActivity.trim()} -> ${event.activity}`;
        relations[relation] = (relations[relation] || 0) + 1;
      } else if (event.parentCaseId) {
        // 当活动记录包含parent_case_id但previousActivity为空时，推断前置活动
        const inferredPreviousActivity =
          this.inferPreviousActivityFromParentCase(event, allEventLogs);
        if (inferredPreviousActivity) {
          const relation = `${inferredPreviousActivity} -> ${event.activity}`;
          relations[relation] = (relations[relation] || 0) + 1;
        }
      }
    });

    return relations;
  }

  /**
   * 从父流程推断前置活动节点
   * 当活动记录包含parent_case_id但previousActivity为空时，
   * 该活动的前置活动节点应该是其父流程中当前正在执行的活动节点
   */
  private inferPreviousActivityFromParentCase(
    currentEvent: EventLog,
    allEvents: EventLog[],
  ): string | null {
    if (!currentEvent.parentCaseId) {
      return null;
    }

    // 查找父流程（parent_case_id对应的案例）中的所有活动
    const parentCaseEvents = allEvents.filter(
      (event) => event.caseId === currentEvent.parentCaseId,
    );

    if (parentCaseEvents.length === 0) {
      return null;
    }

    // 按时间排序父流程事件
    const sortedParentEvents = parentCaseEvents.sort(
      (a, b) => a.timestamp.getTime() - b.timestamp.getTime(),
    );

    // 在当前活动开始时间点，确定父流程中正在进行的活动
    const currentEventTime = currentEvent.timestamp.getTime();

    // 查找在当前时间点正在执行的父流程活动
    let activeParentActivity: string | null = null;

    for (const parentEvent of sortedParentEvents) {
      const parentStartTime = parentEvent.timestamp.getTime();

      // 计算父活动的结束时间
      let parentEndTime: number;
      if (parentEvent.endTimestamp) {
        parentEndTime = parentEvent.endTimestamp.getTime();
      } else {
        // 如果没有结束时间，查找下一个活动的开始时间作为结束时间
        const nextParentEvent = sortedParentEvents.find(
          (e) => e.timestamp.getTime() > parentStartTime,
        );
        if (nextParentEvent) {
          parentEndTime = nextParentEvent.timestamp.getTime();
        } else {
          // 如果是最后一个活动，假设它在当前子流程活动开始时仍在执行
          parentEndTime = currentEventTime + 1; // 确保包含当前时间点
        }
      }

      // 检查父活动是否在当前时间点处于执行状态（已开始但未结束）
      if (
        parentStartTime <= currentEventTime &&
        currentEventTime < parentEndTime
      ) {
        activeParentActivity = parentEvent.activity;
        break; // 找到正在执行的活动，退出循环
      }
    }

    // 如果没有找到正在执行的活动，使用边界情况处理
    if (!activeParentActivity) {
      activeParentActivity = this.handleBoundaryCase(
        currentEvent,
        sortedParentEvents,
        currentEventTime,
      );
    }

    // 🔥 优化：避免连接到开始节点，确保跨层级连接的逻辑性
    if (activeParentActivity) {
      // 检查推断出的前置活动是否为开始节点
      if (
        activeParentActivity.startsWith('开始') ||
        activeParentActivity === '开始'
      ) {
        console.log(
          `⚠️ 跨层级连接优化: 子流程活动 "${currentEvent.activity}" 推断出的前置活动为开始节点 "${activeParentActivity}"，` +
            `查找父流程中的第一个业务活动作为替代`,
        );

        // 查找父流程中第一个非开始节点的业务活动
        const firstBusinessActivity = sortedParentEvents.find(
          (event) =>
            !event.activity.startsWith('开始') && event.activity !== '开始',
        );

        if (firstBusinessActivity) {
          activeParentActivity = firstBusinessActivity.activity;
          console.log(
            `✅ 使用父流程第一个业务活动 "${activeParentActivity}" 作为前置活动`,
          );
        } else {
          console.log(`⚠️ 父流程中没有找到业务活动，跳过此连接以避免冗余`);
          return null; // 返回null避免创建冗余连接
        }
      }

      console.log(
        `🔗 推断前置活动: 子流程活动 "${currentEvent.activity}" (案例: ${currentEvent.caseId}) ` +
          `的前置活动为父流程活动 "${activeParentActivity}" (父案例: ${currentEvent.parentCaseId})`,
      );
    }

    return activeParentActivity;
  }

  /**
   * 处理边界情况：如果父流程在该时间点没有正在执行的活动
   */
  private handleBoundaryCase(
    currentEvent: EventLog,
    sortedParentEvents: EventLog[],
    currentEventTime: number,
  ): string | null {
    if (sortedParentEvents.length === 0) {
      return null;
    }

    // 查找最近完成的活动（在当前时间点之前结束的最后一个活动）
    let lastCompletedActivity: EventLog | null = null;

    for (const parentEvent of sortedParentEvents) {
      const parentStartTime = parentEvent.timestamp.getTime();

      if (parentStartTime <= currentEventTime) {
        // 计算活动结束时间
        let parentEndTime: number;
        if (parentEvent.endTimestamp) {
          parentEndTime = parentEvent.endTimestamp.getTime();
        } else {
          // 使用下一个活动的开始时间作为结束时间
          const nextParentEvent = sortedParentEvents.find(
            (e) => e.timestamp.getTime() > parentStartTime,
          );
          parentEndTime = nextParentEvent
            ? nextParentEvent.timestamp.getTime()
            : parentStartTime; // 如果没有下一个活动，假设瞬间完成
        }

        if (parentEndTime <= currentEventTime) {
          lastCompletedActivity = parentEvent;
        }
      }
    }

    if (lastCompletedActivity) {
      console.log(
        `⚠️ 边界情况处理: 父流程在时间点 ${new Date(currentEventTime).toISOString()} ` +
          `没有正在执行的活动，使用最近完成的活动 "${lastCompletedActivity.activity}"`,
      );
      return lastCompletedActivity.activity;
    }

    // 🔥 优化：避免直接连接到父流程的开始节点，改为连接到第一个业务活动
    const firstParentActivity = sortedParentEvents[0];
    if (
      firstParentActivity &&
      firstParentActivity.timestamp.getTime() > currentEventTime
    ) {
      console.log(
        `⚠️ 边界情况处理: 子流程活动早于父流程开始，连接到父流程第一个业务活动`,
      );
      // 不返回开始节点，而是返回第一个业务活动
      return firstParentActivity.activity;
    }

    return firstParentActivity ? firstParentActivity.activity : null;
  }

  /**
   * 基于时间顺序构建活动关系（作为前置活动字段的补充）
   */
  private buildRelationsByTimeOrder(
    events: EventLog[],
  ): Record<string, number> {
    const relations: Record<string, number> = {};

    // 对于没有前置活动信息的事件，尝试从时间顺序推断关系
    for (let i = 0; i < events.length - 1; i++) {
      const currentEvent = events[i];
      const nextEvent = events[i + 1];

      // 只有当下一个事件没有明确的前置活动信息时，才使用时间顺序推断
      if (
        !nextEvent.previousActivity ||
        nextEvent.previousActivity.trim() === ''
      ) {
        // 🔥 新增：避免跨子流程边界创建连接
        // 如果当前事件和下一个事件属于不同的案例（包括主流程和子流程），则跳过
        if (currentEvent.caseId !== nextEvent.caseId) {
          continue;
        }

        // 🔥 新增：避免从子流程跳转到主流程
        // 如果当前事件有parentCaseId而下一个事件没有，说明是从子流程跳转到主流程
        if (currentEvent.parentCaseId && !nextEvent.parentCaseId) {
          continue;
        }

        // 🔥 新增：避免从主流程跳转到子流程
        // 如果当前事件没有parentCaseId而下一个事件有，说明是从主流程跳转到子流程
        if (!currentEvent.parentCaseId && nextEvent.parentCaseId) {
          continue;
        }

        const relation = `${currentEvent.activity} -> ${nextEvent.activity}`;
        relations[relation] = (relations[relation] || 0) + 1;
      }
    }

    return relations;
  }

  /**
   * 基于前置活动字段计算转换耗时（支持多层级嵌套流程）
   */
  private calculateTransitionDurationsByPreviousActivity(
    events: EventLog[],
    transitionStats: Record<string, number[]>,
    allEventLogs: EventLog[],
  ): void {
    events.forEach((event) => {
      let previousActivity: string | null = null;
      let previousEvent: EventLog | null = null;

      if (event.previousActivity && event.previousActivity.trim() !== '') {
        // 直接使用前置活动字段
        previousActivity = event.previousActivity.trim();

        // 找到前置活动的事件来计算转换时间
        const previousEvents = events.filter(
          (e) => e.activity === previousActivity,
        );

        if (previousEvents.length > 0) {
          // 找到最接近当前事件时间的前置活动事件
          previousEvent = previousEvents.reduce((closest, current) => {
            const closestDiff = Math.abs(
              closest.timestamp.getTime() - event.timestamp.getTime(),
            );
            const currentDiff = Math.abs(
              current.timestamp.getTime() - event.timestamp.getTime(),
            );
            return currentDiff < closestDiff &&
              current.timestamp.getTime() <= event.timestamp.getTime()
              ? current
              : closest;
          });
        }
      } else if (event.parentCaseId) {
        // 当活动记录包含parent_case_id但previousActivity为空时，推断前置活动
        previousActivity = this.inferPreviousActivityFromParentCase(
          event,
          allEventLogs,
        );

        if (previousActivity) {
          // 查找推断出的前置活动事件
          if (previousActivity.startsWith('开始_')) {
            // 如果是开始节点，转换时间为0
            const relation = `${previousActivity} -> ${event.activity}`;
            if (!transitionStats[relation]) {
              transitionStats[relation] = [];
            }
            transitionStats[relation].push(0);
            return;
          } else {
            // 查找父流程中的前置活动事件
            const parentCaseEvents = allEventLogs.filter(
              (e) => e.caseId === event.parentCaseId,
            );
            const previousEvents = parentCaseEvents.filter(
              (e) => e.activity === previousActivity,
            );

            if (previousEvents.length > 0) {
              // 找到最接近当前事件时间的前置活动事件
              previousEvent = previousEvents.reduce((closest, current) => {
                const closestDiff = Math.abs(
                  closest.timestamp.getTime() - event.timestamp.getTime(),
                );
                const currentDiff = Math.abs(
                  current.timestamp.getTime() - event.timestamp.getTime(),
                );
                return currentDiff < closestDiff &&
                  current.timestamp.getTime() <= event.timestamp.getTime()
                  ? current
                  : closest;
              });
            }
          }
        }
      }

      // 计算转换时间
      if (previousActivity && previousEvent) {
        const relation = `${previousActivity} -> ${event.activity}`;

        // 计算转换时间（按行业标准）
        let transitionStartTime: number;
        if (previousEvent.endTimestamp) {
          // 有结束时间戳：计算真正的等待时间
          transitionStartTime = previousEvent.endTimestamp.getTime();
        } else {
          // 只有开始时间戳：计算转换时间（包含前一个活动的执行时间 + 等待时间）
          transitionStartTime = previousEvent.timestamp.getTime();
        }

        const duration = event.timestamp.getTime() - transitionStartTime;

        if (!transitionStats[relation]) {
          transitionStats[relation] = [];
        }

        // 只添加非负的转换时间
        if (duration >= 0) {
          transitionStats[relation].push(duration);
        }
      }
    });
  }

  /**
   * 基于时间顺序计算转换耗时（作为前置活动字段的补充）
   */
  private calculateTransitionDurationsByTimeOrder(
    events: EventLog[],
    transitionStats: Record<string, number[]>,
  ): void {
    for (let i = 0; i < events.length - 1; i++) {
      const currentEvent = events[i];
      const nextEvent = events[i + 1];

      // 只有当下一个事件没有明确的前置活动信息时，才使用时间顺序推断
      if (
        !nextEvent.previousActivity ||
        nextEvent.previousActivity.trim() === ''
      ) {
        const relation = `${currentEvent.activity} -> ${nextEvent.activity}`;

        // 计算转换时间：从当前活动结束到下一个活动开始的时间间隔
        // 按行业标准：如果只有开始时间戳，这包含了当前活动的执行时间 + 真正的等待时间
        let transitionStartTime: number;
        if (currentEvent.endTimestamp) {
          // 有结束时间戳：计算真正的等待时间
          transitionStartTime = currentEvent.endTimestamp.getTime();
        } else {
          // 只有开始时间戳：计算转换时间（包含执行时间 + 等待时间）
          transitionStartTime = currentEvent.timestamp.getTime();
        }

        const duration = nextEvent.timestamp.getTime() - transitionStartTime;

        if (!transitionStats[relation]) {
          transitionStats[relation] = [];
        }

        // 只添加非负的转换时间
        if (duration >= 0) {
          transitionStats[relation].push(duration);
        }
      }
    }
  }

  /**
   * 计算活动间转换的耗时统计
   */
  private calculateTransitionDurations(
    caseGroups: Record<string, EventLog[]>,
    allEventLogs: EventLog[],
  ): Record<
    string,
    {
      durations: number[];
      avgDuration: number;
      minDuration: number;
      maxDuration: number;
    }
  > {
    const transitionStats: Record<string, number[]> = {};

    // 收集每个转换的所有耗时数据（等待时间）
    Object.values(caseGroups).forEach((events) => {
      // 首先处理基于前置活动字段的转换耗时
      this.calculateTransitionDurationsByPreviousActivity(
        events,
        transitionStats,
        allEventLogs,
      );

      // 然后处理基于时间顺序的转换耗时（作为补充）
      this.calculateTransitionDurationsByTimeOrder(events, transitionStats);
    });

    // 计算统计信息
    const result: Record<
      string,
      {
        durations: number[];
        avgDuration: number;
        minDuration: number;
        maxDuration: number;
      }
    > = {};

    Object.entries(transitionStats).forEach(([relation, durations]) => {
      if (durations.length > 0) {
        const avgDuration =
          durations.reduce((sum, d) => sum + d, 0) / durations.length;
        const minDuration = Math.min(...durations);
        const maxDuration = Math.max(...durations);

        result[relation] = {
          durations,
          avgDuration,
          minDuration,
          maxDuration,
        };
      }
    });

    return result;
  }

  /**
   * 计算活动的耗时统计
   */
  private calculateActivityDurations(
    caseGroups: Record<string, EventLog[]>,
  ): Record<
    string,
    { avgDuration: number; minDuration: number; maxDuration: number }
  > {
    const activityStats: Record<string, number[]> = {};

    // 收集每个活动的耗时数据
    Object.values(caseGroups).forEach((events) => {
      for (let i = 0; i < events.length; i++) {
        const currentEvent = events[i];
        const activity = currentEvent.activity;

        if (!activityStats[activity]) {
          activityStats[activity] = [];
        }

        let duration = 0;

        // 优先使用endTimestamp计算活动持续时间
        if (currentEvent.endTimestamp) {
          duration =
            currentEvent.endTimestamp.getTime() -
            currentEvent.timestamp.getTime();
        } else if (i < events.length - 1) {
          // 如果没有endTimestamp，使用到下一个活动开始时间的间隔
          duration =
            events[i + 1].timestamp.getTime() -
            currentEvent.timestamp.getTime();
        } else {
          // 对于最后一个活动且没有endTimestamp，尝试估算一个合理的持续时间
          // 使用该活动在其他案例中的平均持续时间，如果没有则使用1小时
          const existingDurations = activityStats[activity].filter(
            (d) => d > 0,
          );
          if (existingDurations.length > 0) {
            duration =
              existingDurations.reduce((sum, d) => sum + d, 0) /
              existingDurations.length;
          } else {
            duration = 1000 * 60 * 60; // 1小时的毫秒数
          }
        }

        // 只添加有效的持续时间（大于0）
        if (duration > 0) {
          activityStats[activity].push(duration);
        }
      }
    });

    // 计算统计信息
    const result: Record<
      string,
      { avgDuration: number; minDuration: number; maxDuration: number }
    > = {};

    Object.entries(activityStats).forEach(([activity, durations]) => {
      const validDurations = durations.filter((d) => d > 0);
      if (validDurations.length > 0) {
        const avgDuration =
          validDurations.reduce((sum, d) => sum + d, 0) / validDurations.length;
        const minDuration = Math.min(...validDurations);
        const maxDuration = Math.max(...validDurations);

        result[activity] = {
          avgDuration,
          minDuration,
          maxDuration,
        };
      } else {
        result[activity] = {
          avgDuration: 0,
          minDuration: 0,
          maxDuration: 0,
        };
      }
    });

    return result;
  }

  private getStartActivities(caseGroups: Record<string, EventLog[]>): string[] {
    const startActivities = new Set<string>();
    Object.values(caseGroups).forEach((events) => {
      if (events.length > 0) {
        startActivities.add(events[0].activity);
      }
    });
    return Array.from(startActivities);
  }

  private getEndActivities(caseGroups: Record<string, EventLog[]>): string[] {
    const endActivities = new Set<string>();
    Object.values(caseGroups).forEach((events) => {
      if (events.length > 0) {
        endActivities.add(events[events.length - 1].activity);
      }
    });
    return Array.from(endActivities);
  }

  private calculateCaseDurations(
    caseGroups: Record<string, EventLog[]>,
  ): number[] {
    return Object.values(caseGroups).map((events) =>
      this.calculateCaseDuration(events),
    );
  }

  private calculateCaseDuration(events: EventLog[]): number {
    if (events.length === 0) return 0;
    if (events.length === 1) {
      // 单个活动的案例，使用活动的持续时间
      const event = events[0];
      if (event.endTimestamp) {
        return event.endTimestamp.getTime() - event.timestamp.getTime();
      }
      return 0; // 如果没有结束时间，持续时间为0
    }

    const start = events[0].timestamp.getTime();
    const lastEvent = events[events.length - 1];

    // 优先使用最后一个活动的结束时间
    let end: number;
    if (lastEvent.endTimestamp) {
      end = lastEvent.endTimestamp.getTime();
    } else {
      end = lastEvent.timestamp.getTime();
    }

    return end - start; // 返回毫秒数
  }

  private async saveAnalysisResult(
    processId: number,
    analysisType: AnalysisType,
    resultData: any,
  ): Promise<void> {
    const analysisResult = new AnalysisResult();
    analysisResult.processId = processId;
    analysisResult.analysisType = analysisType;
    analysisResult.resultData = resultData;

    await this.analysisResultRepository.save(analysisResult);
  }

  /**
   * 保存分析结果到数据库和缓存
   */
  private async saveAnalysisResultWithCache(
    processId: number,
    analysisType: AnalysisType,
    resultData: any,
    dataSourceHash?: string,
    cacheTtl = 3600,
    parameters?: any,
  ): Promise<AnalysisResult> {
    // 获取下一个版本号
    const version = await this.getNextVersion(processId, analysisType);

    // 创建分析结果实体
    const analysisResult = new AnalysisResult();
    analysisResult.processId = processId;
    analysisResult.analysisType = analysisType;
    analysisResult.resultData = resultData;
    analysisResult.parameters = parameters || null;
    analysisResult.status = AnalysisStatus.COMPLETED;
    analysisResult.version = version;
    analysisResult.dataSourceHash = dataSourceHash || null;
    analysisResult.cacheTtl = cacheTtl;
    analysisResult.setExpiresAt(cacheTtl);
    analysisResult.cacheKey = analysisResult.generateCacheKey();

    // 保存到数据库
    const savedResult =
      await this.analysisResultRepository.save(analysisResult);

    // 保存到缓存
    const cacheData = this.cacheService.entityToCacheData(savedResult);
    await this.cacheService.setAnalysisResult(
      processId,
      analysisType,
      cacheData,
      { ttl: cacheTtl },
    );

    // 更新数据源哈希
    if (dataSourceHash) {
      await this.cacheService.setDataSourceHash(processId, dataSourceHash);
    }

    this.logger.log(`Saved analysis result: ${analysisResult.cacheKey}`);
    return savedResult;
  }

  async getAnalysisResult(
    processId: number,
    analysisType: AnalysisType,
  ): Promise<AnalysisResult | null> {
    // 优化查询：只选择必要的字段，并限制结果数量
    return this.analysisResultRepository.findOne({
      where: {
        processId,
        analysisType,
        // 只查询已完成或缓存的结果
        status: In([AnalysisStatus.COMPLETED, AnalysisStatus.CACHED]),
      },
      order: { createdAt: 'DESC' },
      // 使用查询构建器来优化性能
      select: [
        'id',
        'analysisType',
        'status',
        'resultData',
        'parameters',
        'description',
        'version',
        'cacheKey',
        'expiresAt',
        'cacheTtl',
        'dataSourceHash',
        'metadata',
        'createdAt',
        'updatedAt',
        'processId',
      ],
    });
  }

  /**
   * 从缓存或数据库获取分析结果
   */
  private async getCachedAnalysisResult(
    processId: number,
    analysisType: AnalysisType,
  ): Promise<any | null> {
    // 先尝试从缓存获取
    const cached = await this.cacheService.getAnalysisResult(
      processId,
      analysisType,
    );
    if (cached) {
      return cached.resultData;
    }

    // 缓存未命中，从数据库获取
    const dbResult = await this.getAnalysisResult(processId, analysisType);
    if (dbResult && !dbResult.needsRefresh) {
      // 将数据库结果加载到缓存
      await this.cacheService.warmupCache(dbResult);
      return dbResult.resultData;
    }

    return null;
  }

  /**
   * 计算数据源哈希值
   */
  private calculateDataSourceHash(eventLogs: EventLog[]): string {
    const hashData = eventLogs
      .map((log) => `${log.caseId}:${log.activity}:${log.timestamp.getTime()}`)
      .join('|');
    return crypto.createHash('sha256').update(hashData).digest('hex');
  }

  /**
   * 检查数据源是否发生变更
   */
  private async hasDataSourceChanged(
    processId: number,
    currentHash: string,
  ): Promise<boolean> {
    return this.cacheService.hasDataSourceChanged(processId, currentHash);
  }

  /**
   * 获取下一个版本号
   */
  private async getNextVersion(
    processId: number,
    analysisType: AnalysisType,
  ): Promise<number> {
    const latest = await this.analysisResultRepository.findOne({
      where: { processId, analysisType },
      order: { version: 'DESC' },
      select: ['version'], // 只选择需要的字段
    });
    return latest ? latest.version + 1 : 1;
  }

  /**
   * 获取节点详细信息
   */
  async getNodeDetailInfo(
    processId: number,
    nodeId: string,
  ): Promise<NodeDetailInfo> {
    // 获取事件日志数据
    const eventLogs = await this.eventLogRepository.find({
      where: { processId },
      order: { caseId: 'ASC', timestamp: 'ASC' },
    });

    if (eventLogs.length === 0) {
      throw new Error('没有找到事件日志数据');
    }

    // 按案例分组
    const caseGroups = this.groupByCaseId(eventLogs);

    // 过滤出包含目标活动的事件
    const nodeEvents = eventLogs.filter((event) => event.activity === nodeId);

    if (nodeEvents.length === 0) {
      throw new Error(`未找到活动 ${nodeId} 的相关数据`);
    }

    // 计算基础统计信息
    const basicStats = this.calculateNodeBasicStats(nodeEvents, caseGroups);

    // 计算时间分布
    const timeDistribution = this.calculateNodeTimeDistribution(
      nodeEvents,
      caseGroups,
    );

    // 计算资源分析（如果有资源字段）
    const resourceAnalysis = this.calculateNodeResourceAnalysis(nodeEvents);

    // 计算路径分析
    const pathAnalysis = this.calculateNodePathAnalysis(
      nodeId,
      caseGroups,
      eventLogs,
    );

    // 计算时间模式
    const timePatterns = this.calculateNodeTimePatterns(nodeEvents);

    // 检测异常
    const anomalies = this.detectNodeAnomalies(nodeEvents, caseGroups);

    return {
      id: nodeId,
      label: nodeId,
      basicStats,
      timeDistribution,
      resourceAnalysis,
      pathAnalysis,
      timePatterns,
      anomalies,
    };
  }

  /**
   * 获取连接详细信息
   */
  async getEdgeDetailInfo(
    processId: number,
    sourceId: string,
    targetId: string,
  ): Promise<EdgeDetailInfo> {
    // 获取事件日志数据
    const eventLogs = await this.eventLogRepository.find({
      where: { processId },
      order: { caseId: 'ASC', timestamp: 'ASC' },
    });

    if (eventLogs.length === 0) {
      throw new Error('没有找到事件日志数据');
    }

    // 按案例分组
    const caseGroups = this.groupByCaseId(eventLogs);

    // 找到所有包含此连接的案例
    const edgeOccurrences = this.findEdgeOccurrences(
      sourceId,
      targetId,
      caseGroups,
    );

    if (edgeOccurrences.length === 0) {
      throw new Error(`未找到从 ${sourceId} 到 ${targetId} 的连接数据`);
    }

    // 计算基础统计信息
    const basicStats = this.calculateEdgeBasicStats(
      edgeOccurrences,
      eventLogs.length,
    );

    // 计算时间分布
    const timeDistribution =
      this.calculateEdgeTimeDistribution(edgeOccurrences);

    // 计算趋势分析
    const trendAnalysis = this.calculateEdgeTrendAnalysis(edgeOccurrences);

    // 计算效率分析
    const efficiencyAnalysis = this.calculateEdgeEfficiencyAnalysis(
      sourceId,
      targetId,
      caseGroups,
    );

    // 计算上下文分析
    const contextAnalysis = this.calculateEdgeContextAnalysis(
      sourceId,
      targetId,
      caseGroups,
    );

    return {
      source: sourceId,
      target: targetId,
      basicStats,
      timeDistribution,
      trendAnalysis,
      efficiencyAnalysis,
      contextAnalysis,
      caseDetails: edgeOccurrences,
    };
  }

  /**
   * 计算节点基础统计信息
   */
  private calculateNodeBasicStats(
    nodeEvents: EventLog[],
    caseGroups: Record<string, EventLog[]>,
  ) {
    const durations: number[] = [];

    // 计算每个活动实例的持续时间
    Object.values(caseGroups).forEach((events) => {
      for (let i = 0; i < events.length; i++) {
        if (events[i].activity === nodeEvents[0].activity) {
          let duration = 0;

          // 优先使用endTimestamp计算活动持续时间
          if (events[i].endTimestamp) {
            duration =
              events[i].endTimestamp.getTime() - events[i].timestamp.getTime();
          } else if (i < events.length - 1) {
            // 如果没有endTimestamp，使用到下一个活动开始时间的间隔
            duration =
              events[i + 1].timestamp.getTime() - events[i].timestamp.getTime();
          } else {
            // 对于最后一个活动且没有endTimestamp，尝试估算一个合理的持续时间
            // 使用该活动在其他案例中的平均持续时间，如果没有则使用1小时
            const existingDurations = durations.filter((d) => d > 0);
            if (existingDurations.length > 0) {
              duration =
                existingDurations.reduce((sum, d) => sum + d, 0) /
                existingDurations.length;
            } else {
              duration = 1000 * 60 * 60; // 1小时的毫秒数
            }
          }

          if (duration > 0) {
            durations.push(duration);
          }
        }
      }
    });

    durations.sort((a, b) => a - b);

    const frequency = nodeEvents.length;
    const avgDuration =
      durations.length > 0
        ? durations.reduce((sum, d) => sum + d, 0) / durations.length
        : 0;
    const minDuration = durations.length > 0 ? Math.min(...durations) : 0;
    const maxDuration = durations.length > 0 ? Math.max(...durations) : 0;
    const medianDuration =
      durations.length > 0 ? durations[Math.floor(durations.length / 2)] : 0;

    // 计算标准差
    const variance =
      durations.length > 0
        ? durations.reduce((sum, d) => sum + Math.pow(d - avgDuration, 2), 0) /
          durations.length
        : 0;
    const stdDeviation = Math.sqrt(variance);

    return {
      frequency,
      avgDuration,
      minDuration,
      maxDuration,
      medianDuration,
      stdDeviation,
    };
  }

  /**
   * 计算节点时间分布
   */
  private calculateNodeTimeDistribution(
    nodeEvents: EventLog[],
    caseGroups: Record<string, EventLog[]>,
  ) {
    const durations: number[] = [];

    // 收集持续时间数据
    Object.values(caseGroups).forEach((events) => {
      for (let i = 0; i < events.length; i++) {
        if (events[i].activity === nodeEvents[0].activity) {
          let duration = 0;

          // 优先使用endTimestamp计算活动持续时间
          if (events[i].endTimestamp) {
            duration =
              events[i].endTimestamp.getTime() - events[i].timestamp.getTime();
          } else if (i < events.length - 1) {
            // 如果没有endTimestamp，使用到下一个活动开始时间的间隔
            duration =
              events[i + 1].timestamp.getTime() - events[i].timestamp.getTime();
          } else {
            // 对于最后一个活动且没有endTimestamp，尝试估算一个合理的持续时间
            // 使用该活动在其他案例中的平均持续时间，如果没有则使用1小时
            const existingDurations = durations.filter((d) => d > 0);
            if (existingDurations.length > 0) {
              duration =
                existingDurations.reduce((sum, d) => sum + d, 0) /
                existingDurations.length;
            } else {
              duration = 1000 * 60 * 60; // 1小时的毫秒数
            }
          }

          if (duration > 0) {
            durations.push(duration);
          }
        }
      }
    });

    durations.sort((a, b) => a - b);

    // 创建智能时间区间
    const intervals = this.createSmartTimeIntervals(durations);

    // 计算四分位数
    const quartiles =
      durations.length > 0
        ? {
            q1: durations[Math.floor(durations.length * 0.25)],
            q2: durations[Math.floor(durations.length * 0.5)],
            q3: durations[Math.floor(durations.length * 0.75)],
          }
        : { q1: 0, q2: 0, q3: 0 };

    return {
      intervals,
      quartiles,
    };
  }

  /**
   * 创建智能时间区间
   */
  private createSmartTimeIntervals(
    durations: number[],
  ): Array<{ range: string; count: number; percentage: number }> {
    if (durations.length === 0) {
      return [];
    }

    const min = Math.min(...durations);
    const max = Math.max(...durations);
    const range = max - min;

    // 根据数据范围选择合适的区间数量
    let intervalCount = 8;
    if (durations.length < 20) {
      intervalCount = 5;
    } else if (durations.length > 100) {
      intervalCount = 12;
    }

    // 计算合适的区间大小
    const rawStep = range / intervalCount;
    const step = this.roundToNiceNumber(rawStep);

    // 调整起始点到合适的整数
    const adjustedMin = Math.floor(min / step) * step;
    const adjustedMax = Math.ceil(max / step) * step;

    const intervals: Array<{
      range: string;
      count: number;
      percentage: number;
    }> = [];

    for (let start = adjustedMin; start < adjustedMax; start += step) {
      const end = start + step;
      const count = durations.filter((d) => d >= start && d < end).length;

      // 只包含有数据的区间
      if (count > 0 || intervals.length === 0) {
        intervals.push({
          range: `${this.formatDuration(start)} - ${this.formatDuration(end)}`,
          count,
          percentage: (count / durations.length) * 100,
        });
      }
    }

    return intervals;
  }

  /**
   * 将数字四舍五入到合适的"好看"数字
   */
  private roundToNiceNumber(value: number): number {
    const magnitude = Math.pow(10, Math.floor(Math.log10(value)));
    const normalized = value / magnitude;

    let nice: number;
    if (normalized <= 1) {
      nice = 1;
    } else if (normalized <= 2) {
      nice = 2;
    } else if (normalized <= 5) {
      nice = 5;
    } else {
      nice = 10;
    }

    return nice * magnitude;
  }

  /**
   * 格式化持续时间
   */
  private formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * 计算节点资源分析
   */
  private calculateNodeResourceAnalysis(nodeEvents: EventLog[]) {
    // 由于当前EventLog实体可能没有resource字段，这里提供一个基础实现
    // 可以根据实际的数据结构进行调整
    const resources: Array<{
      resource: string;
      frequency: number;
      avgDuration: number;
      efficiency: number;
    }> = [];

    // 如果有resource字段，可以在这里实现资源分析逻辑
    // 目前返回空数组作为占位符
    return { resources };
  }

  /**
   * 计算节点路径分析
   */
  private calculateNodePathAnalysis(
    nodeId: string,
    caseGroups: Record<string, EventLog[]>,
    eventLogs: EventLog[],
  ) {
    const predecessors = new Map<string, number>();
    const successors = new Map<string, number>();

    Object.values(caseGroups).forEach((events) => {
      for (let i = 0; i < events.length; i++) {
        if (events[i].activity === nodeId) {
          // 记录前置活动
          if (i > 0) {
            const predecessor = events[i - 1].activity;
            predecessors.set(
              predecessor,
              (predecessors.get(predecessor) || 0) + 1,
            );
          }
          // 记录后续活动
          if (i < events.length - 1) {
            const successor = events[i + 1].activity;
            successors.set(successor, (successors.get(successor) || 0) + 1);
          }
        }
      }
    });

    const totalPredecessors = Array.from(predecessors.values()).reduce(
      (sum, count) => sum + count,
      0,
    );
    const totalSuccessors = Array.from(successors.values()).reduce(
      (sum, count) => sum + count,
      0,
    );

    return {
      predecessors: Array.from(predecessors.entries()).map(
        ([activity, frequency]) => ({
          activity,
          frequency,
          percentage: (frequency / totalPredecessors) * 100,
        }),
      ),
      successors: Array.from(successors.entries()).map(
        ([activity, frequency]) => ({
          activity,
          frequency,
          percentage: (frequency / totalSuccessors) * 100,
        }),
      ),
    };
  }

  /**
   * 计算节点时间模式
   */
  private calculateNodeTimePatterns(nodeEvents: EventLog[]) {
    const hourlyDistribution = new Array(24).fill(0);
    const weeklyDistribution = new Array(7).fill(0);

    nodeEvents.forEach((event) => {
      const hour = event.timestamp.getHours();
      const dayOfWeek = event.timestamp.getDay();
      hourlyDistribution[hour]++;
      weeklyDistribution[dayOfWeek]++;
    });

    return {
      hourlyDistribution: hourlyDistribution.map((count, hour) => ({
        hour,
        count,
      })),
      weeklyDistribution: weeklyDistribution.map((count, dayOfWeek) => ({
        dayOfWeek,
        count,
      })),
    };
  }

  /**
   * 检测节点异常
   */
  private detectNodeAnomalies(
    nodeEvents: EventLog[],
    caseGroups: Record<string, EventLog[]>,
  ) {
    const anomalies: Array<{
      caseId: string;
      duration: number;
      reason: string;
      severity: 'low' | 'medium' | 'high';
    }> = [];

    // 计算平均持续时间用于异常检测
    const durations: Array<{ caseId: string; duration: number }> = [];

    Object.entries(caseGroups).forEach(([caseId, events]) => {
      for (let i = 0; i < events.length; i++) {
        if (
          events[i].activity === nodeEvents[0].activity &&
          i < events.length - 1
        ) {
          const duration =
            events[i + 1].timestamp.getTime() - events[i].timestamp.getTime();
          durations.push({ caseId, duration });
        }
      }
    });

    if (durations.length > 0) {
      const avgDuration =
        durations.reduce((sum, d) => sum + d.duration, 0) / durations.length;
      const stdDev = Math.sqrt(
        durations.reduce(
          (sum, d) => sum + Math.pow(d.duration - avgDuration, 2),
          0,
        ) / durations.length,
      );

      durations.forEach(({ caseId, duration }) => {
        const zScore = Math.abs((duration - avgDuration) / stdDev);
        if (zScore > 3) {
          anomalies.push({
            caseId,
            duration,
            reason:
              duration > avgDuration ? '执行时间异常长' : '执行时间异常短',
            severity: zScore > 4 ? 'high' : zScore > 3.5 ? 'medium' : 'low',
          });
        }
      });
    }

    return anomalies;
  }

  /**
   * 查找边的出现次数（与主要分析保持一致的计算方法）
   */
  private findEdgeOccurrences(
    sourceId: string,
    targetId: string,
    caseGroups: Record<string, EventLog[]>,
  ): Array<{
    caseId: string;
    sourceTime: Date;
    targetTime: Date;
    duration: number;
  }> {
    const occurrences: Array<{
      caseId: string;
      sourceTime: Date;
      targetTime: Date;
      duration: number;
    }> = [];

    Object.entries(caseGroups).forEach(([caseId, events]) => {
      // 首先基于previousActivity字段查找边
      events.forEach((event) => {
        if (
          event.activity === targetId &&
          event.previousActivity &&
          event.previousActivity.trim() === sourceId
        ) {
          // 找到对应的源活动事件
          const sourceEvents = events.filter((e) => e.activity === sourceId);
          if (sourceEvents.length > 0) {
            // 找到最接近当前事件时间的源活动事件
            const sourceEvent = sourceEvents.reduce((closest, current) => {
              const closestDiff = Math.abs(
                closest.timestamp.getTime() - event.timestamp.getTime(),
              );
              const currentDiff = Math.abs(
                current.timestamp.getTime() - event.timestamp.getTime(),
              );
              return currentDiff < closestDiff &&
                current.timestamp.getTime() <= event.timestamp.getTime()
                ? current
                : closest;
            });

            // 计算转换时间（按行业标准）
            let transitionStartTime: number;
            if (sourceEvent.endTimestamp) {
              // 有结束时间戳：计算真正的等待时间
              transitionStartTime = sourceEvent.endTimestamp.getTime();
            } else {
              // 只有开始时间戳：计算转换时间（包含源活动的执行时间 + 等待时间）
              transitionStartTime = sourceEvent.timestamp.getTime();
            }

            const duration = event.timestamp.getTime() - transitionStartTime;

            if (duration >= 0) {
              occurrences.push({
                caseId,
                sourceTime: sourceEvent.timestamp,
                targetTime: event.timestamp,
                duration,
              });
            }
          }
        }
      });

      // 然后基于时间顺序查找边（作为补充）
      for (let i = 0; i < events.length - 1; i++) {
        if (
          events[i].activity === sourceId &&
          events[i + 1].activity === targetId
        ) {
          // 只有当目标事件没有明确的前置活动信息时，才使用时间顺序推断
          if (
            !events[i + 1].previousActivity ||
            events[i + 1].previousActivity.trim() === ''
          ) {
            // 计算转换时间（按行业标准）
            let transitionStartTime: number;
            if (events[i].endTimestamp) {
              // 有结束时间戳：计算真正的等待时间
              transitionStartTime = events[i].endTimestamp.getTime();
            } else {
              // 只有开始时间戳：计算转换时间（包含源活动的执行时间 + 等待时间）
              transitionStartTime = events[i].timestamp.getTime();
            }

            const duration =
              events[i + 1].timestamp.getTime() - transitionStartTime;

            if (duration >= 0) {
              occurrences.push({
                caseId,
                sourceTime: events[i].timestamp,
                targetTime: events[i + 1].timestamp,
                duration,
              });
            }
          }
        }
      }
    });

    return occurrences;
  }

  /**
   * 计算边基础统计信息
   */
  private calculateEdgeBasicStats(
    edgeOccurrences: Array<{
      caseId: string;
      sourceTime: Date;
      targetTime: Date;
      duration: number;
    }>,
    totalEvents: number,
  ) {
    const durations = edgeOccurrences
      .map((occ) => occ.duration)
      .sort((a, b) => a - b);

    const frequency = edgeOccurrences.length;
    const avgDuration =
      durations.reduce((sum, d) => sum + d, 0) / durations.length;
    const minDuration = Math.min(...durations);
    const maxDuration = Math.max(...durations);
    const medianDuration = durations[Math.floor(durations.length / 2)];
    const percentage = (frequency / totalEvents) * 100;

    return {
      frequency,
      avgDuration,
      minDuration,
      maxDuration,
      medianDuration,
      percentage,
    };
  }

  /**
   * 计算边时间分布
   */
  private calculateEdgeTimeDistribution(
    edgeOccurrences: Array<{
      caseId: string;
      sourceTime: Date;
      targetTime: Date;
      duration: number;
    }>,
  ) {
    const durations = edgeOccurrences
      .map((occ) => occ.duration)
      .sort((a, b) => a - b);

    // 创建智能时间区间
    const intervals = this.createSmartTimeIntervals(durations);

    // 计算四分位数
    const quartiles =
      durations.length > 0
        ? {
            q1: durations[Math.floor(durations.length * 0.25)],
            q2: durations[Math.floor(durations.length * 0.5)],
            q3: durations[Math.floor(durations.length * 0.75)],
          }
        : { q1: 0, q2: 0, q3: 0 };

    return {
      intervals,
      quartiles,
    };
  }

  /**
   * 计算边趋势分析
   */
  private calculateEdgeTrendAnalysis(
    edgeOccurrences: Array<{
      caseId: string;
      sourceTime: Date;
      targetTime: Date;
      duration: number;
    }>,
  ) {
    // 按时间段分组（例如按天）
    const timeSeriesData: Array<{
      period: string;
      frequency: number;
      avgDuration: number;
    }> = [];

    // 简化实现：按月分组
    const monthlyData = new Map<
      string,
      { count: number; totalDuration: number }
    >();

    edgeOccurrences.forEach((occ) => {
      const monthKey = `${occ.sourceTime.getFullYear()}-${String(occ.sourceTime.getMonth() + 1).padStart(2, '0')}`;
      if (!monthlyData.has(monthKey)) {
        monthlyData.set(monthKey, { count: 0, totalDuration: 0 });
      }
      const data = monthlyData.get(monthKey)!;
      data.count++;
      data.totalDuration += occ.duration;
    });

    Array.from(monthlyData.entries()).forEach(([period, data]) => {
      timeSeriesData.push({
        period,
        frequency: data.count,
        avgDuration: data.totalDuration / data.count,
      });
    });

    // 简单的趋势判断
    let trend: 'increasing' | 'decreasing' | 'stable' = 'stable';
    if (timeSeriesData.length >= 2) {
      const firstHalf = timeSeriesData.slice(
        0,
        Math.floor(timeSeriesData.length / 2),
      );
      const secondHalf = timeSeriesData.slice(
        Math.floor(timeSeriesData.length / 2),
      );

      const firstAvg =
        firstHalf.reduce((sum, d) => sum + d.frequency, 0) / firstHalf.length;
      const secondAvg =
        secondHalf.reduce((sum, d) => sum + d.frequency, 0) / secondHalf.length;

      if (secondAvg > firstAvg * 1.1) {
        trend = 'increasing';
      } else if (secondAvg < firstAvg * 0.9) {
        trend = 'decreasing';
      }
    }

    return {
      timeSeriesData,
      trend,
    };
  }

  /**
   * 计算边效率分析
   */
  private calculateEdgeEfficiencyAnalysis(
    sourceId: string,
    targetId: string,
    caseGroups: Record<string, EventLog[]>,
  ) {
    const comparedToOtherPaths: Array<{
      alternativePath: string;
      frequencyDiff: number;
      durationDiff: number;
    }> = [];

    // 找到从source出发的所有其他路径
    const sourceOutgoingPaths = new Map<
      string,
      { frequency: number; avgDuration: number }
    >();

    Object.values(caseGroups).forEach((events) => {
      for (let i = 0; i < events.length - 1; i++) {
        if (events[i].activity === sourceId) {
          const nextActivity = events[i + 1].activity;
          const duration =
            events[i + 1].timestamp.getTime() - events[i].timestamp.getTime();

          if (!sourceOutgoingPaths.has(nextActivity)) {
            sourceOutgoingPaths.set(nextActivity, {
              frequency: 0,
              avgDuration: 0,
            });
          }

          const pathData = sourceOutgoingPaths.get(nextActivity)!;
          const newFreq = pathData.frequency + 1;
          const newAvgDuration =
            (pathData.avgDuration * pathData.frequency + duration) / newFreq;

          sourceOutgoingPaths.set(nextActivity, {
            frequency: newFreq,
            avgDuration: newAvgDuration,
          });
        }
      }
    });

    const currentPath = sourceOutgoingPaths.get(targetId);
    if (currentPath) {
      sourceOutgoingPaths.forEach((pathData, activity) => {
        if (activity !== targetId) {
          comparedToOtherPaths.push({
            alternativePath: `${sourceId} → ${activity}`,
            frequencyDiff: currentPath.frequency - pathData.frequency,
            durationDiff: currentPath.avgDuration - pathData.avgDuration,
          });
        }
      });
    }

    return {
      comparedToOtherPaths,
    };
  }

  /**
   * 计算边上下文分析
   */
  private calculateEdgeContextAnalysis(
    sourceId: string,
    targetId: string,
    caseGroups: Record<string, EventLog[]>,
  ) {
    const commonPredecessors = new Map<string, number>();
    const commonSuccessors = new Map<string, number>();

    Object.values(caseGroups).forEach((events) => {
      for (let i = 0; i < events.length - 1; i++) {
        if (
          events[i].activity === sourceId &&
          events[i + 1].activity === targetId
        ) {
          // 记录共同前置活动
          if (i > 0) {
            const predecessor = events[i - 1].activity;
            commonPredecessors.set(
              predecessor,
              (commonPredecessors.get(predecessor) || 0) + 1,
            );
          }

          // 记录共同后续活动
          if (i + 2 < events.length) {
            const successor = events[i + 2].activity;
            commonSuccessors.set(
              successor,
              (commonSuccessors.get(successor) || 0) + 1,
            );
          }
        }
      }
    });

    return {
      commonPredecessors: Array.from(commonPredecessors.entries()).map(
        ([activity, frequency]) => ({
          activity,
          frequency,
        }),
      ),
      commonSuccessors: Array.from(commonSuccessors.entries()).map(
        ([activity, frequency]) => ({
          activity,
          frequency,
        }),
      ),
    };
  }

  /**
   * 从事件日志中提取业务字段信息
   */
  private extractBusinessFields(
    eventLogs: EventLog[],
  ): Map<string, Record<string, any>> {
    const businessFieldsMap = new Map<string, Record<string, any>>();

    // 按活动分组
    const activityGroups = new Map<string, EventLog[]>();
    eventLogs.forEach((log) => {
      if (!activityGroups.has(log.activity)) {
        activityGroups.set(log.activity, []);
      }
      activityGroups.get(log.activity)!.push(log);
    });

    // 为每个活动提取业务字段
    activityGroups.forEach((logs, activity) => {
      const businessFields: Record<string, any> = {};

      // 收集所有业务字段
      const allFields = new Set<string>();
      logs.forEach((log) => {
        if (log.attributes && typeof log.attributes === 'object') {
          Object.keys(log.attributes).forEach((key) => allFields.add(key));
        }
      });

      // 为每个字段计算统计信息
      allFields.forEach((field) => {
        const values = logs
          .map((log) => log.attributes?.[field])
          .filter((value) => value !== null && value !== undefined);

        if (values.length > 0) {
          // 获取唯一值
          const uniqueValues = [...new Set(values)];

          // 计算值的分布
          const valueDistribution = new Map<any, number>();
          values.forEach((value) => {
            valueDistribution.set(
              value,
              (valueDistribution.get(value) || 0) + 1,
            );
          });

          // 获取最常见的值
          const mostCommonValue = [...valueDistribution.entries()].sort(
            (a, b) => b[1] - a[1],
          )[0]?.[0];

          businessFields[field] = {
            uniqueValues: uniqueValues.slice(0, 50), // 限制数量避免过大
            mostCommonValue,
            totalCount: values.length,
            uniqueCount: uniqueValues.length,
            distribution: Object.fromEntries(
              [...valueDistribution.entries()].slice(0, 20), // 限制分布数据
            ),
          };
        }
      });

      businessFieldsMap.set(activity, businessFields);
    });

    return businessFieldsMap;
  }

  /**
   * 从事件日志中提取资源信息
   */
  private extractResources(eventLogs: EventLog[]): Map<
    string,
    Array<{
      resource: string;
      frequency: number;
      percentage: number;
    }>
  > {
    const resourcesMap = new Map<
      string,
      Array<{
        resource: string;
        frequency: number;
        percentage: number;
      }>
    >();

    // 按活动分组
    const activityGroups = new Map<string, EventLog[]>();
    eventLogs.forEach((log) => {
      if (!activityGroups.has(log.activity)) {
        activityGroups.set(log.activity, []);
      }
      activityGroups.get(log.activity)!.push(log);
    });

    // 为每个活动提取资源信息
    activityGroups.forEach((logs, activity) => {
      const resourceCounts = new Map<string, number>();
      let totalWithResource = 0;

      // 统计资源使用情况
      logs.forEach((log) => {
        if (log.resource && log.resource.trim()) {
          const resource = log.resource.trim();
          resourceCounts.set(resource, (resourceCounts.get(resource) || 0) + 1);
          totalWithResource++;
        }
      });

      // 转换为数组并计算百分比
      const resources = [...resourceCounts.entries()]
        .map(([resource, frequency]) => ({
          resource,
          frequency,
          percentage:
            totalWithResource > 0 ? (frequency / totalWithResource) * 100 : 0,
        }))
        .sort((a, b) => b.frequency - a.frequency); // 按频率排序

      resourcesMap.set(activity, resources);
    });

    return resourcesMap;
  }

  /**
   * 根据必须包含的活动节点过滤事件日志
   * 只保留同一个case_id链路中包含这些活动节点的数据（多个的话就是包含任一）
   */
  private filterEventLogsByRequiredActivities(
    eventLogs: EventLog[],
    requiredActivities: string[],
  ): EventLog[] {
    if (!requiredActivities || requiredActivities.length === 0) {
      return eventLogs;
    }

    // 如果包含 ALL_NODES，表示不进行活动过滤
    if (requiredActivities.includes('ALL_NODES')) {
      return eventLogs;
    }

    // 按案例分组
    const caseGroups = this.groupByCaseId(eventLogs);

    // 找出包含必须活动的案例ID
    const validCaseIds = new Set<string>();

    Object.entries(caseGroups).forEach(([caseId, events]) => {
      // 检查该案例是否包含任一必须的活动
      const caseActivities = new Set(events.map((event) => event.activity));
      const hasRequiredActivity = requiredActivities.some((activity) =>
        caseActivities.has(activity),
      );

      if (hasRequiredActivity) {
        validCaseIds.add(caseId);
      }
    });

    // 只保留有效案例的事件日志
    const filteredEventLogs = eventLogs.filter((event) =>
      validCaseIds.has(event.caseId),
    );

    this.logger.log(
      `Activity filtering: ${validCaseIds.size} cases out of ${Object.keys(caseGroups).length} contain required activities`,
    );

    return filteredEventLogs;
  }

  /**
   * 根据完整的筛选器状态过滤事件日志
   */
  private filterEventLogsByFilters(
    eventLogs: EventLog[],
    filters: any,
  ): EventLog[] {
    if (!filters || !filters.groups || filters.groups.length === 0) {
      return eventLogs;
    }

    // 按案例分组
    const caseGroups = this.groupByCaseId(eventLogs);

    // 计算全局连接频次（用于 ALL_NODES 筛选）
    const globalEdgeFrequency = this.calculateGlobalEdgeFrequency(eventLogs);

    const validCaseIds = new Set<string>();

    // 遍历每个案例，检查是否满足筛选条件
    Object.entries(caseGroups).forEach(([caseId, events]) => {
      const satisfiesFilter = this.evaluateFiltersForCase(
        events,
        filters,
        globalEdgeFrequency,
      );
      if (satisfiesFilter) {
        validCaseIds.add(caseId);
      }
    });

    // 只保留满足条件的案例的事件日志
    const filteredEventLogs = eventLogs.filter((event) =>
      validCaseIds.has(event.caseId),
    );

    this.logger.log(
      `Filter evaluation: ${validCaseIds.size} cases out of ${Object.keys(caseGroups).length} satisfy the filter conditions`,
    );

    return filteredEventLogs;
  }

  /**
   * 计算全局连接频次
   */
  private calculateGlobalEdgeFrequency(
    eventLogs: EventLog[],
  ): Map<string, number> {
    const edgeFrequency = new Map<string, number>();
    const caseGroups = this.groupByCaseId(eventLogs);

    Object.values(caseGroups).forEach((events) => {
      const sortedEvents = events.sort(
        (a, b) =>
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
      );

      for (let i = 0; i < sortedEvents.length - 1; i++) {
        const source = sortedEvents[i].activity;
        const target = sortedEvents[i + 1].activity;
        const edgeKey = `${source}->${target}`;

        edgeFrequency.set(edgeKey, (edgeFrequency.get(edgeKey) || 0) + 1);
      }
    });

    return edgeFrequency;
  }

  /**
   * 评估案例是否满足筛选条件
   */
  private evaluateFiltersForCase(
    events: EventLog[],
    filters: any,
    globalEdgeFrequency?: Map<string, number>,
  ): boolean {
    if (!filters.groups || filters.groups.length === 0) {
      return true;
    }

    const groupResults = filters.groups.map((group: any) => {
      return this.evaluateFilterGroup(events, group, globalEdgeFrequency);
    });

    // 根据全局逻辑组合结果
    if (filters.globalLogic === 'OR') {
      return groupResults.some((result: boolean) => result);
    } else {
      return groupResults.every((result: boolean) => result);
    }
  }

  /**
   * 评估筛选器组
   */
  private evaluateFilterGroup(
    events: EventLog[],
    group: any,
    globalEdgeFrequency?: Map<string, number>,
  ): boolean {
    if (!group.conditions || group.conditions.length === 0) {
      return true;
    }

    const conditionResults = group.conditions
      .filter((condition: any) => condition.enabled)
      .map((condition: any) => {
        return this.evaluateFilterCondition(
          events,
          condition,
          globalEdgeFrequency,
        );
      });

    if (conditionResults.length === 0) {
      return true;
    }

    // 根据组内逻辑组合结果
    if (group.logic === 'OR') {
      return conditionResults.some((result: boolean) => result);
    } else {
      return conditionResults.every((result: boolean) => result);
    }
  }

  /**
   * 评估单个筛选条件
   */
  private evaluateFilterCondition(
    events: EventLog[],
    condition: any,
    globalEdgeFrequency?: Map<string, number>,
  ): boolean {
    switch (condition.type) {
      case 'resource':
        return this.evaluateResourceCondition(events, condition.data);
      case 'duration':
        return this.evaluateDurationCondition(events, condition.data);
      case 'businessField':
        return this.evaluateBusinessFieldCondition(events, condition.data);
      case 'pathway':
        return this.evaluatePathwayCondition(
          events,
          condition.data,
          globalEdgeFrequency,
        );
      default:
        return true;
    }
  }

  /**
   * 评估资源筛选条件
   */
  private evaluateResourceCondition(events: EventLog[], data: any): boolean {
    if (!data.resources || data.resources.length === 0) {
      return true;
    }

    const caseResources = new Set(
      events.map((event) => event.resource).filter((resource) => resource),
    );

    return data.resources.some((resource: string) =>
      caseResources.has(resource),
    );
  }

  /**
   * 评估耗时筛选条件
   */
  private evaluateDurationCondition(events: EventLog[], data: any): boolean {
    if (data.min === null && data.max === null) {
      return true;
    }

    // 计算案例持续时间
    const caseDuration = this.calculateCaseDuration(events);

    // 转换单位到毫秒
    let minMs = data.min;
    let maxMs = data.max;

    if (data.unit === 'minutes') {
      minMs = minMs ? minMs * 60 * 1000 : null;
      maxMs = maxMs ? maxMs * 60 * 1000 : null;
    } else if (data.unit === 'hours') {
      minMs = minMs ? minMs * 60 * 60 * 1000 : null;
      maxMs = maxMs ? maxMs * 60 * 60 * 1000 : null;
    } else if (data.unit === 'days') {
      minMs = minMs ? minMs * 24 * 60 * 60 * 1000 : null;
      maxMs = maxMs ? maxMs * 24 * 60 * 60 * 1000 : null;
    }

    if (minMs !== null && caseDuration < minMs) {
      return false;
    }
    if (maxMs !== null && caseDuration > maxMs) {
      return false;
    }

    return true;
  }

  /**
   * 评估业务字段筛选条件
   */
  private evaluateBusinessFieldCondition(
    events: EventLog[],
    data: any,
  ): boolean {
    if (!data.field) {
      return true;
    }

    // 检查案例中的事件是否有匹配的业务字段值
    return events.some((event) => {
      if (!event.attributes || !event.attributes[data.field]) {
        return false;
      }

      const fieldValue = event.attributes[data.field];
      return this.evaluateFieldValue(fieldValue, data);
    });
  }

  /**
   * 评估字段值
   */
  private evaluateFieldValue(fieldValue: any, data: any): boolean {
    const value = String(fieldValue);

    switch (data.operator) {
      case 'contains':
        return value.includes(data.value);
      case 'equals':
        return value === data.value;
      case 'gt':
        return parseFloat(value) > parseFloat(data.value);
      case 'lt':
        return parseFloat(value) < parseFloat(data.value);
      case 'range':
        const numValue = parseFloat(value);
        const min = parseFloat(data.rangeMin);
        const max = parseFloat(data.rangeMax);
        return numValue >= min && numValue <= max;
      case 'in':
        return data.values && data.values.includes(value);
      default:
        return true;
    }
  }

  /**
   * 评估路径筛选条件
   */
  private evaluatePathwayCondition(
    events: EventLog[],
    data: any,
    globalEdgeFrequency?: Map<string, number>,
  ): boolean {
    if (!data.activity || !data.frequency) {
      return true;
    }

    if (data.activity === 'ALL_NODES') {
      // 全局频次筛选：检查案例中任意连接是否在全局频次范围内
      const edges = this.extractEdgesFromEvents(events);
      return edges.some((edge: any) => {
        const edgeKey = `${edge.source}->${edge.target}`;
        const globalFrequency = globalEdgeFrequency?.get(edgeKey) || 0;
        return (
          globalFrequency >= data.frequency[0] &&
          globalFrequency <= data.frequency[1]
        );
      });
    } else {
      // 特定活动筛选：检查与指定活动相关的连接
      const edges = this.extractEdgesFromEvents(events);
      const relevantEdges = edges.filter((edge: any) => {
        if (data.type === 'predecessor') {
          return edge.target === data.activity;
        } else {
          return edge.source === data.activity;
        }
      });

      return relevantEdges.some((edge: any) => {
        const edgeKey = `${edge.source}->${edge.target}`;
        const globalFrequency = globalEdgeFrequency?.get(edgeKey) || 0;
        return (
          globalFrequency >= data.frequency[0] &&
          globalFrequency <= data.frequency[1]
        );
      });
    }
  }

  /**
   * 从事件序列中提取边（连接）
   */
  private extractEdgesFromEvents(events: EventLog[]): any[] {
    const edges: any[] = [];
    const sortedEvents = events.sort(
      (a, b) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
    );

    for (let i = 0; i < sortedEvents.length - 1; i++) {
      const source = sortedEvents[i].activity;
      const target = sortedEvents[i + 1].activity;

      edges.push({
        source,
        target,
        frequency: 1, // 在单个案例中，每个连接的频次为1
      });
    }

    return edges;
  }

  /**
   * 流程比对分析
   */
  async compareProcess(
    processId: number,
    options: {
      baseStartTime: string;
      baseEndTime: string;
      offsetDays: number;
      requiredActivities?: string[];
      filters?: any; // 完整的筛选器状态
      forceRefresh?: boolean;
      dimension?: 'frequency' | 'duration';
      displayMode?: 'absolute' | 'difference' | 'percentage';
    },
  ): Promise<any> {
    const {
      baseStartTime,
      baseEndTime,
      offsetDays,
      requiredActivities = [],
      filters,
      forceRefresh = false,
      dimension = 'frequency',
      displayMode = 'absolute',
    } = options;

    this.logger.log(
      `Starting process comparison for processId: ${processId}, baseTime: ${baseStartTime} - ${baseEndTime}, offsetDays: ${offsetDays}, requiredActivities: ${JSON.stringify(requiredActivities)}, filters: ${JSON.stringify(filters)}`,
    );

    // 计算对比时间段
    const baseStart = new Date(baseStartTime);
    const baseEnd = new Date(baseEndTime);
    const baseDuration = baseEnd.getTime() - baseStart.getTime();

    const compareStart = new Date(
      baseStart.getTime() - offsetDays * 24 * 60 * 60 * 1000,
    );
    const compareEnd = new Date(
      baseEnd.getTime() - offsetDays * 24 * 60 * 60 * 1000,
    );

    // 生成缓存键
    const cacheKey = this.generateComparisonCacheKey(
      processId,
      baseStartTime,
      baseEndTime,
      offsetDays,
      requiredActivities,
      filters,
      dimension,
      displayMode,
    );

    // 检查缓存
    if (!forceRefresh) {
      const cachedResult = await this.cacheService.getAnalysisResult(
        processId,
        AnalysisType.PROCESS_DISCOVERY, // 使用现有的分析类型
      );
      if (cachedResult && cachedResult.resultData) {
        // 检查缓存键是否匹配（如果存在）
        if (
          !cachedResult.resultData.cacheKey ||
          cachedResult.resultData.cacheKey === cacheKey
        ) {
          this.logger.log(
            `Returning cached comparison result for process: ${processId}`,
          );
          return {
            ...cachedResult.resultData,
            fromCache: true,
          };
        }
      }
    }

    try {
      // 获取基准时间段的数据
      const baseEventLogs = await this.getEventLogsByTimeRange(
        processId,
        baseStart,
        baseEnd,
        requiredActivities,
        filters,
      );

      // 获取对比时间段的数据
      const compareEventLogs = await this.getEventLogsByTimeRange(
        processId,
        compareStart,
        compareEnd,
        requiredActivities,
        filters,
      );

      // 分别进行流程发现
      const baseDfg = await this.performDFGAnalysis(baseEventLogs);
      const compareDfg = await this.performDFGAnalysis(compareEventLogs);

      // 计算比对统计信息
      const compareStatistics = this.calculateCompareStatistics(
        baseDfg,
        compareDfg,
        {
          baseTimeRange: {
            start: baseStartTime,
            end: baseEndTime,
            duration: baseDuration,
          },
          compareTimeRange: {
            start: compareStart.toISOString(),
            end: compareEnd.toISOString(),
            duration: baseDuration,
          },
          offsetDays,
        },
      );

      const result = {
        baseDfg,
        compareDfg,
        compareStatistics,
        timestamp: new Date().toISOString(),
        fromCache: false,
        cacheKey,
      };

      // 缓存结果
      const now = new Date();
      await this.cacheService.setAnalysisResult(
        processId,
        AnalysisType.PROCESS_DISCOVERY,
        {
          resultData: result,
          version: 1,
          createdAt: now,
          expiresAt: new Date(now.getTime() + 3600 * 1000), // 1小时后过期
        },
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Process comparison failed: ${error.message}`,
        error.stack,
      );
      throw new Error(`流程比对分析失败: ${error.message}`);
    }
  }

  /**
   * 根据时间范围获取事件日志
   */
  private async getEventLogsByTimeRange(
    processId: number,
    startTime: Date,
    endTime: Date,
    requiredActivities: string[] = [],
    filters?: any,
  ): Promise<EventLog[]> {
    let eventLogs = await this.eventLogRepository.find({
      where: {
        processId,
        timestamp: Between(startTime, endTime),
      },
      order: {
        caseId: 'ASC',
        timestamp: 'ASC',
      },
    });

    // 应用活动筛选（向后兼容）
    if (requiredActivities.length > 0) {
      this.logger.log(
        `Applying activity filtering with activities: ${requiredActivities.join(', ')}`,
      );
      eventLogs = this.filterEventLogsByRequiredActivities(
        eventLogs,
        requiredActivities,
      );
      this.logger.log(`After filtering: ${eventLogs.length} events remaining`);
    }

    // 应用完整的筛选器
    if (filters) {
      this.logger.log(`Applying complete filters: ${JSON.stringify(filters)}`);
      eventLogs = this.filterEventLogsByFilters(eventLogs, filters);
      this.logger.log(
        `After complete filtering: ${eventLogs.length} events remaining`,
      );
    }

    return eventLogs;
  }

  /**
   * 执行 DFG 分析
   */
  private async performDFGAnalysis(eventLogs: EventLog[]): Promise<any> {
    if (!eventLogs || eventLogs.length === 0) {
      return {
        nodes: [],
        edges: [],
        statistics: {
          totalCases: 0,
          totalActivities: 0,
          avgCaseDuration: 0,
          startActivities: [],
          endActivities: [],
        },
      };
    }

    // 复用现有的 DFG 分析逻辑
    const caseGroups = this.groupByCaseId(eventLogs);

    // 计算活动频率
    const activityFrequency = this.calculateActivityFrequency(eventLogs);

    // 计算直接跟随关系
    const followRelations = this.calculateFollowRelations(
      caseGroups,
      eventLogs,
    );

    // 计算活动间转换的耗时统计
    const transitionDurations = this.calculateTransitionDurations(
      caseGroups,
      eventLogs,
    );

    // 计算活动的耗时统计
    const activityDurations = this.calculateActivityDurations(caseGroups);

    // 计算开始和结束活动
    const startActivities = this.getStartActivities(caseGroups);
    const endActivities = this.getEndActivities(caseGroups);

    // 计算案例持续时间
    const caseDurations = this.calculateCaseDurations(caseGroups);
    const avgCaseDuration =
      caseDurations.length > 0
        ? caseDurations.reduce((sum, duration) => sum + duration, 0) /
          caseDurations.length
        : 0;

    // 构建DFG节点
    const nodes: DFGNode[] = Object.entries(activityFrequency).map(
      ([activity, frequency]) => {
        const durationStats = activityDurations[activity];
        return {
          id: activity,
          label: activity,
          frequency,
          avgDuration: durationStats?.avgDuration || 0,
          minDuration: durationStats?.minDuration || 0,
          maxDuration: durationStats?.maxDuration || 0,
        };
      },
    );

    // 添加开始和结束节点
    const START_NODE_ID = '开始';
    const END_NODE_ID = '结束';

    const startNode: DFGNode = {
      id: START_NODE_ID,
      label: START_NODE_ID,
      frequency: Object.keys(caseGroups).length, // 案例总数
      avgDuration: 0,
      minDuration: 0,
      maxDuration: 0,
    };

    const endNode: DFGNode = {
      id: END_NODE_ID,
      label: END_NODE_ID,
      frequency: Object.keys(caseGroups).length, // 案例总数
      avgDuration: 0,
      minDuration: 0,
      maxDuration: 0,
    };

    // 构建DFG边
    const edges: DFGEdge[] = Object.entries(followRelations).map(
      ([relation, frequency]) => {
        const [source, target] = relation.split(' -> ');
        const durationStats = transitionDurations[relation];
        return {
          source,
          target,
          frequency,
          avgDuration: durationStats?.avgDuration || 0,
          minDuration: durationStats?.minDuration || 0,
          maxDuration: durationStats?.maxDuration || 0,
        };
      },
    );

    // 添加从开始节点到开始活动的边
    startActivities.forEach((activity) => {
      const frequency = Object.values(caseGroups).filter(
        (events) => events.length > 0 && events[0].activity === activity,
      ).length;

      edges.push({
        source: START_NODE_ID,
        target: activity,
        frequency,
        avgDuration: 0, // 开始节点到第一个活动没有等待时间
        minDuration: 0,
        maxDuration: 0,
      });
    });

    // 添加从结束活动到结束节点的边
    endActivities.forEach((activity) => {
      const frequency = Object.values(caseGroups).filter(
        (events) =>
          events.length > 0 && events[events.length - 1].activity === activity,
      ).length;

      edges.push({
        source: activity,
        target: END_NODE_ID,
        frequency,
        avgDuration: 0, // 最后一个活动到结束节点没有等待时间
        minDuration: 0,
        maxDuration: 0,
      });
    });

    const statistics = {
      totalCases: Object.keys(caseGroups).length,
      totalActivities: nodes.length, // 不包含开始和结束节点
      avgCaseDuration,
      startActivities,
      endActivities,
    };

    return {
      nodes: [startNode, ...nodes, endNode], // 添加开始和结束节点
      edges,
      statistics,
    };
  }

  /**
   * 计算比对统计信息
   */
  private calculateCompareStatistics(
    baseDfg: any,
    compareDfg: any,
    timeInfo: any,
  ): any {
    const baseActivities = new Set(baseDfg.nodes.map((node: any) => node.id));
    const compareActivities = new Set(
      compareDfg.nodes.map((node: any) => node.id),
    );

    const commonActivities = Array.from(baseActivities).filter((activity) =>
      compareActivities.has(activity),
    );
    const uniqueBaseActivities = Array.from(baseActivities).filter(
      (activity) => !compareActivities.has(activity),
    );
    const uniqueCompareActivities = Array.from(compareActivities).filter(
      (activity) => !baseActivities.has(activity),
    );

    const baseCaseCount = baseDfg.statistics.totalCases;
    const compareCaseCount = compareDfg.statistics.totalCases;
    const caseCountChange = compareCaseCount - baseCaseCount;
    const caseCountChangePercent =
      baseCaseCount > 0 ? (caseCountChange / baseCaseCount) * 100 : 0;

    const baseActivityCount = baseDfg.statistics.totalActivities;
    const compareActivityCount = compareDfg.statistics.totalActivities;
    const activityCountChange = compareActivityCount - baseActivityCount;
    const activityCountChangePercent =
      baseActivityCount > 0
        ? (activityCountChange / baseActivityCount) * 100
        : 0;

    const baseAvgDuration = baseDfg.statistics.avgCaseDuration;
    const compareAvgDuration = compareDfg.statistics.avgCaseDuration;
    const avgDurationChange = compareAvgDuration - baseAvgDuration;
    const avgDurationChangePercent =
      baseAvgDuration > 0 ? (avgDurationChange / baseAvgDuration) * 100 : 0;

    return {
      ...timeInfo,
      baseCaseCount,
      compareCaseCount,
      baseActivityCount,
      compareActivityCount,
      commonActivities,
      uniqueBaseActivities,
      uniqueCompareActivities,
      changeMetrics: {
        caseCountChange,
        caseCountChangePercent,
        activityCountChange,
        activityCountChangePercent,
        avgDurationChange,
        avgDurationChangePercent,
      },
    };
  }

  /**
   * 生成比对分析缓存键
   */
  private generateComparisonCacheKey(
    processId: number,
    baseStartTime: string,
    baseEndTime: string,
    offsetDays: number,
    requiredActivities: string[],
    filters: any,
    dimension: string,
    displayMode: string,
  ): string {
    const keyData = {
      processId,
      baseStartTime,
      baseEndTime,
      offsetDays,
      requiredActivities: requiredActivities.sort(),
      filters: filters || null,
      dimension,
      displayMode,
    };
    return crypto
      .createHash('md5')
      .update(JSON.stringify(keyData))
      .digest('hex');
  }

  /**
   * 分析层次结构
   */
  private analyzeHierarchicalStructure(eventLogs: EventLog[]): {
    hasHierarchy: boolean;
    hierarchyMap: Map<string, string[]>; // parentCaseId -> childCaseIds
    levelMap: Map<string, number>; // caseId -> level
    maxLevel: number;
  } {
    console.log('🔍 开始分析层次结构...');
    console.log(`📊 总事件数: ${eventLogs.length}`);

    const hierarchyMap = new Map<string, string[]>();
    const levelMap = new Map<string, number>();
    let hasHierarchy = false;
    let maxLevel = 0;

    // 分析父子关系
    const parentChildMap = new Map<string, string>();
    let parentCaseIdCount = 0;

    eventLogs.forEach((event) => {
      if (event.parentCaseId) {
        parentCaseIdCount++;
        hasHierarchy = true;
        parentChildMap.set(event.caseId, event.parentCaseId);

        // 构建层次映射
        if (!hierarchyMap.has(event.parentCaseId)) {
          hierarchyMap.set(event.parentCaseId, []);
        }
        const children = hierarchyMap.get(event.parentCaseId)!;
        if (!children.includes(event.caseId)) {
          children.push(event.caseId);
        }
      }
    });

    console.log(`🔗 发现 ${parentCaseIdCount} 个事件有父案例ID`);
    console.log(`📈 层次结构存在: ${hasHierarchy}`);
    console.log(`🗂️ 父子关系映射数量: ${hierarchyMap.size}`);

    // 计算每个案例的层级
    const calculateLevel = (
      caseId: string,
      visited = new Set<string>(),
    ): number => {
      if (visited.has(caseId)) {
        return 0; // 避免循环引用
      }
      visited.add(caseId);

      const parentCaseId = parentChildMap.get(caseId);
      if (!parentCaseId) {
        return 0; // 顶级案例
      }
      return calculateLevel(parentCaseId, visited) + 1;
    };

    // 为所有案例计算层级
    const allCaseIds = new Set(eventLogs.map((event) => event.caseId));
    allCaseIds.forEach((caseId) => {
      const level = calculateLevel(caseId);
      levelMap.set(caseId, level);
      maxLevel = Math.max(maxLevel, level);
    });

    return {
      hasHierarchy,
      hierarchyMap,
      levelMap,
      maxLevel,
    };
  }

  /**
   * 测试多层级嵌套流程挖掘的前置活动推断逻辑
   */
  async testHierarchicalPreviousActivityInference(processId: number) {
    console.log(`🧪 开始测试流程 ${processId} 的多层级前置活动推断逻辑...`);

    // 获取事件日志
    const eventLogs = await this.eventLogRepository.find({
      where: { processId },
      order: { timestamp: 'ASC' },
    });

    console.log(`📊 总事件数: ${eventLogs.length}`);

    // 统计有parent_case_id但没有previous_activity的事件
    const eventsNeedingInference = eventLogs.filter(
      (event) =>
        event.parentCaseId &&
        (!event.previousActivity || event.previousActivity.trim() === ''),
    );

    console.log(
      `🔍 需要推断前置活动的事件数: ${eventsNeedingInference.length}`,
    );

    // 测试前置活动推断
    let successfulInferences = 0;
    let failedInferences = 0;

    eventsNeedingInference.forEach((event, index) => {
      console.log(
        `\n--- 测试事件 ${index + 1}/${eventsNeedingInference.length} ---`,
      );
      console.log(`案例ID: ${event.caseId}`);
      console.log(`活动: ${event.activity}`);
      console.log(`父案例ID: ${event.parentCaseId}`);
      console.log(`时间戳: ${event.timestamp.toISOString()}`);

      const inferredActivity = this.inferPreviousActivityFromParentCase(
        event,
        eventLogs,
      );

      if (inferredActivity) {
        console.log(`✅ 成功推断前置活动: ${inferredActivity}`);
        successfulInferences++;
      } else {
        console.log(`❌ 无法推断前置活动`);
        failedInferences++;
      }
    });

    console.log(`\n📈 推断结果统计:`);
    console.log(`✅ 成功推断: ${successfulInferences} 个`);
    console.log(`❌ 推断失败: ${failedInferences} 个`);
    console.log(
      `📊 成功率: ${eventsNeedingInference.length > 0 ? ((successfulInferences / eventsNeedingInference.length) * 100).toFixed(1) : 0}%`,
    );

    // 测试关系构建
    console.log(`\n🔗 测试关系构建...`);
    const caseGroups = this.groupByCaseId(eventLogs);
    const relations = this.calculateFollowRelations(caseGroups, eventLogs);

    console.log(`📊 构建的关系总数: ${Object.keys(relations).length}`);

    // 显示前10个关系作为示例
    const topRelations = Object.entries(relations)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10);

    console.log(`🔝 频率最高的10个关系:`);
    topRelations.forEach(([relation, frequency], index) => {
      console.log(`${index + 1}. ${relation} (频率: ${frequency})`);
    });

    return {
      totalEvents: eventLogs.length,
      eventsNeedingInference: eventsNeedingInference.length,
      successfulInferences,
      failedInferences,
      successRate:
        eventsNeedingInference.length > 0
          ? (successfulInferences / eventsNeedingInference.length) * 100
          : 0,
      totalRelations: Object.keys(relations).length,
      topRelations: topRelations.map(([relation, frequency]) => ({
        relation,
        frequency,
      })),
    };
  }

  /**
   * 测试跨层级连接优化效果
   */
  async testCrossLevelConnectionOptimization(processId: number) {
    console.log(`🧪 开始测试流程 ${processId} 的跨层级连接优化...`);

    // 获取事件日志
    const eventLogs = await this.eventLogRepository.find({
      where: { processId },
      order: { timestamp: 'ASC' },
    });

    console.log(`📊 总事件数: ${eventLogs.length}`);

    // 构建关系
    const caseGroups = this.groupByCaseId(eventLogs);
    const relations = this.calculateFollowRelations(caseGroups, eventLogs);

    // 分析跨层级连接
    const crossLevelConnections = Object.keys(relations).filter((relation) => {
      const [from] = relation.split(' -> ');
      return from.startsWith('开始') || from === '开始';
    });

    // 🔥 新增：分析子流程结束节点的跨层级连接
    const subprocessEndConnections = Object.keys(relations).filter(
      (relation) => {
        const [from, to] = relation.split(' -> ');
        // 检查源节点是否为子流程节点，目标节点是否为主流程节点
        const fromEvents = eventLogs.filter((event) => event.activity === from);
        const toEvents = eventLogs.filter((event) => event.activity === to);

        const isFromSubprocess = fromEvents.some((event) => event.parentCaseId);
        const isToMainProcess = toEvents.some((event) => !event.parentCaseId);

        return isFromSubprocess && isToMainProcess;
      },
    );

    const startToSubprocessConnections = crossLevelConnections.filter(
      (relation) => {
        const [, to] = relation.split(' -> ');
        // 检查目标节点是否为子流程节点（通过查找对应的事件是否有parentCaseId）
        const targetEvents = eventLogs.filter((event) => event.activity === to);
        return targetEvents.some((event) => event.parentCaseId);
      },
    );

    const startToMainProcessConnections = crossLevelConnections.filter(
      (relation) => {
        const [, to] = relation.split(' -> ');
        // 检查目标节点是否为主流程节点（没有parentCaseId）
        const targetEvents = eventLogs.filter((event) => event.activity === to);
        return targetEvents.some((event) => !event.parentCaseId);
      },
    );

    console.log(`\n📈 跨层级连接分析:`);
    console.log(`🔗 从开始节点的总连接数: ${crossLevelConnections.length}`);
    console.log(
      `📍 开始节点 -> 主流程活动: ${startToMainProcessConnections.length} 个`,
    );
    console.log(
      `📍 开始节点 -> 子流程活动: ${startToSubprocessConnections.length} 个`,
    );
    console.log(
      `🔚 子流程结束 -> 主流程活动: ${subprocessEndConnections.length} 个`,
    );

    console.log(`\n🔝 从开始节点的连接详情:`);
    crossLevelConnections.forEach((relation, index) => {
      const frequency = relations[relation];
      const [, to] = relation.split(' -> ');
      const targetEvents = eventLogs.filter((event) => event.activity === to);
      const isSubprocess = targetEvents.some((event) => event.parentCaseId);
      console.log(
        `${index + 1}. ${relation} (频率: ${frequency}) ${isSubprocess ? '[子流程]' : '[主流程]'}`,
      );
    });

    // 检查优化效果
    const hasRedundantConnections =
      startToMainProcessConnections.length > 0 &&
      startToSubprocessConnections.length > 0;

    console.log(`\n✅ 优化效果评估:`);
    if (hasRedundantConnections) {
      console.log(`⚠️ 检测到潜在的冗余连接：同时存在到主流程和子流程的连接`);
      console.log(
        `💡 建议：子流程应通过父流程业务活动间接连接，而不是直接从开始节点连接`,
      );
    } else {
      console.log(`✅ 连接逻辑清晰：没有检测到冗余的跨层级连接`);
    }

    return {
      totalEvents: eventLogs.length,
      totalRelations: Object.keys(relations).length,
      crossLevelConnections: crossLevelConnections.length,
      startToMainProcess: startToMainProcessConnections.length,
      startToSubprocess: startToSubprocessConnections.length,
      subprocessEndConnections: subprocessEndConnections.length,
      hasRedundantConnections,
      connectionDetails: crossLevelConnections.map((relation) => ({
        relation,
        frequency: relations[relation],
        isSubprocess: (() => {
          const [, to] = relation.split(' -> ');
          const targetEvents = eventLogs.filter(
            (event) => event.activity === to,
          );
          return targetEvents.some((event) => event.parentCaseId);
        })(),
      })),
      subprocessEndDetails: subprocessEndConnections.map((relation) => ({
        relation,
        frequency: relations[relation],
        type: 'subprocess_end_to_parent',
      })),
    };
  }

  /**
   * 测试层次结构分析
   */
  async testHierarchicalAnalysis(processId: number) {
    console.log(`🧪 开始测试流程 ${processId} 的层次结构分析...`);

    // 获取事件日志
    const eventLogs = await this.eventLogRepository.find({
      where: { processId },
      order: { timestamp: 'ASC' },
    });

    console.log(`📊 获取到 ${eventLogs.length} 条事件日志`);

    // 统计parentCaseId字段
    const withParentCaseId = eventLogs.filter(
      (event) => event.parentCaseId,
    ).length;
    console.log(`🔗 有 ${withParentCaseId} 条记录包含parentCaseId`);

    // 分析层次结构
    const hierarchicalAnalysis = this.analyzeHierarchicalStructure(eventLogs);

    // 统计层级分布
    const levelDistribution = new Map<number, number>();
    hierarchicalAnalysis.levelMap.forEach((level) => {
      levelDistribution.set(level, (levelDistribution.get(level) || 0) + 1);
    });

    return {
      totalEvents: eventLogs.length,
      eventsWithParentCaseId: withParentCaseId,
      hasHierarchy: hierarchicalAnalysis.hasHierarchy,
      maxLevel: hierarchicalAnalysis.maxLevel,
      parentChildRelations: hierarchicalAnalysis.hierarchyMap.size,
      levelDistribution: Object.fromEntries(levelDistribution),
      sampleParentChildPairs: Array.from(
        hierarchicalAnalysis.hierarchyMap.entries(),
      ).slice(0, 5),
    };
  }

  /**
   * 过滤无效子流程（只包含开始和结束节点的子流程）
   */
  private filterEmptySubprocesses(
    nodes: DFGNode[],
    edges: DFGEdge[],
    hierarchicalAnalysis: any,
  ): {
    nodes: DFGNode[];
    edges: DFGEdge[];
    validSubprocessCount: number;
  } {
    console.log('🔍 开始过滤无效子流程...');

    // 按子流程分组分析节点
    const subprocessGroups = new Map<string, DFGNode[]>();
    const mainProcessNodes: DFGNode[] = [];

    // 分组节点
    nodes.forEach((node) => {
      if (node.groupId && node.groupId.startsWith('subprocess_')) {
        if (!subprocessGroups.has(node.groupId)) {
          subprocessGroups.set(node.groupId, []);
        }
        subprocessGroups.get(node.groupId)!.push(node);
      } else {
        mainProcessNodes.push(node);
      }
    });

    console.log(`📊 发现 ${subprocessGroups.size} 个子流程组`);

    // 分析每个子流程组的质量
    const validSubprocessGroups = new Set<string>();
    const invalidSubprocessGroups = new Set<string>();

    subprocessGroups.forEach((groupNodes, groupId) => {
      // 更严格的业务节点识别：排除所有系统生成的节点
      const businessNodes = groupNodes.filter((node) => {
        const nodeId = node.id;
        // 排除开始和结束节点（包括各种变体）
        if (
          nodeId === '开始' ||
          nodeId === '结束' ||
          nodeId.startsWith('开始_') ||
          nodeId.startsWith('结束_') ||
          nodeId.startsWith('START_') ||
          nodeId.startsWith('END_') ||
          nodeId.toLowerCase().includes('start') ||
          nodeId.toLowerCase().includes('end')
        ) {
          return false;
        }
        // 排除其他可能的系统节点
        if (
          nodeId.startsWith('SYSTEM_') ||
          nodeId.startsWith('AUTO_') ||
          nodeId.includes('_GENERATED') ||
          nodeId.includes('_VIRTUAL')
        ) {
          return false;
        }
        return true;
      });

      // 分析子流程内部的连接性
      const subprocessEdges = edges.filter((edge) => {
        const sourceInGroup = groupNodes.some((n) => n.id === edge.source);
        const targetInGroup = groupNodes.some((n) => n.id === edge.target);
        return sourceInGroup && targetInGroup;
      });

      console.log(
        `🔍 子流程 ${groupId}: 总节点 ${groupNodes.length}, 业务节点 ${businessNodes.length}, 内部连接 ${subprocessEdges.length}`,
      );

      // 更严格的过滤条件：
      // 1. 业务节点数量 = 0 的子流程（只有开始/结束节点）
      // 2. 业务节点数量 = 1 且没有内部连接的子流程（孤立节点）
      // 3. 业务节点数量 > 1 但没有任何内部连接的子流程（断开的节点）
      let shouldFilter = false;
      let filterReason = '';

      if (businessNodes.length === 0) {
        shouldFilter = true;
        filterReason = '只包含系统节点（开始/结束节点）';
      } else if (businessNodes.length === 1 && subprocessEdges.length === 0) {
        shouldFilter = true;
        filterReason = '只有一个孤立的业务节点，无内部连接';
      } else if (businessNodes.length > 1 && subprocessEdges.length === 0) {
        shouldFilter = true;
        filterReason = '多个业务节点但无内部连接（断开的子流程）';
      }

      if (shouldFilter) {
        invalidSubprocessGroups.add(groupId);
        console.log(`❌ 子流程 ${groupId} 被标记为无效（${filterReason}）`);
      } else {
        validSubprocessGroups.add(groupId);
        console.log(
          `✅ 子流程 ${groupId} 被保留（包含 ${businessNodes.length} 个业务节点，${subprocessEdges.length} 个内部连接）`,
        );
      }
    });

    // 过滤节点
    const filteredNodes = nodes.filter((node) => {
      if (!node.groupId || !node.groupId.startsWith('subprocess_')) {
        return true; // 保留主流程节点
      }
      return validSubprocessGroups.has(node.groupId);
    });

    // 过滤边
    const filteredEdges = edges.filter((edge) => {
      // 检查边的源节点和目标节点是否都在过滤后的节点中
      const sourceExists = filteredNodes.some(
        (node) => node.id === edge.source,
      );
      const targetExists = filteredNodes.some(
        (node) => node.id === edge.target,
      );
      return sourceExists && targetExists;
    });

    console.log(
      `📈 过滤结果: 节点 ${nodes.length} -> ${filteredNodes.length}, 边 ${edges.length} -> ${filteredEdges.length}`,
    );
    console.log(
      `🗂️ 有效子流程: ${validSubprocessGroups.size}, 无效子流程: ${invalidSubprocessGroups.size}`,
    );

    // 详细记录被过滤的子流程信息
    if (invalidSubprocessGroups.size > 0) {
      console.log(`🗑️ 被过滤的子流程详情:`);
      invalidSubprocessGroups.forEach((groupId) => {
        const groupNodes = subprocessGroups.get(groupId) || [];
        const nodeIds = groupNodes.map((n) => n.id).join(', ');
        console.log(`   - ${groupId}: [${nodeIds}]`);
      });
    }

    return {
      nodes: filteredNodes,
      edges: filteredEdges,
      validSubprocessCount: validSubprocessGroups.size,
    };
  }

  /**
   * 处理层次化案例分组
   */
  private processHierarchicalCases(
    caseGroups: Record<string, EventLog[]>,
    hierarchicalAnalysis: {
      hasHierarchy: boolean;
      hierarchyMap: Map<string, string[]>;
      levelMap: Map<string, number>;
      maxLevel: number;
    },
  ): Record<string, EventLog[]> {
    // 为每个案例添加层次信息
    const processedGroups: Record<string, EventLog[]> = {};

    Object.entries(caseGroups).forEach(([caseId, events]) => {
      const level = hierarchicalAnalysis.levelMap.get(caseId) || 0;
      const processedEvents = events.map((event) => ({
        ...event,
        subprocessLevel: level,
        isSubprocessNode: level > 0,
      }));
      processedGroups[caseId] = processedEvents;
    });

    return processedGroups;
  }

  /**
   * 获取时间范围
   */
  private getTimeRange(eventLogs: EventLog[]) {
    const timestamps = eventLogs.map((log) => log.timestamp);
    const start = new Date(Math.min(...timestamps.map((t) => t.getTime())));
    const end = new Date(Math.max(...timestamps.map((t) => t.getTime())));
    return { start, end };
  }

  /**
   * 查找周期索引
   */
  private findPeriodIndex(timeSeries: any[], period: string): number {
    const periodIndex = timeSeries.findIndex((item) => item.period === period);
    if (periodIndex === -1) {
      throw new NotFoundException(`${period} 无数据`);
    }
    return periodIndex;
  }

  /**
   * 获取周期事件数据
   */
  private getPeriodEvents(
    caseGroups: Record<string, EventLog[]>,
    periodIndex: number,
    timeSeries: any[],
  ) {
    // 获取当前周期的时间范围
    const currentPeriod = timeSeries[periodIndex];
    const currentPeriodEvents = this.filterEventsByTimeRange(
      caseGroups,
      currentPeriod.start,
      currentPeriod.end,
    );

    // 获取上一周期的时间范围
    let previousPeriodEvents: Record<string, EventLog[]> = {};
    if (periodIndex > 0) {
      const previousPeriod = timeSeries[periodIndex - 1];
      previousPeriodEvents = this.filterEventsByTimeRange(
        caseGroups,
        previousPeriod.start,
        previousPeriod.end,
      );
    }

    return { currentPeriodEvents, previousPeriodEvents };
  }

  /**
   * 获取指定周期的详细数据
   */
  async getPeriodDetails(
    processId: number,
    period: string,
    granularity: string = 'week',
    requiredActivities?: string[],
  ) {
    let eventLogs = await this.eventLogRepository.find({
      where: { processId },
      order: { timestamp: 'ASC' },
    });

    if (eventLogs.length === 0) {
      throw new NotFoundException('未找到事件日志数据');
    }

    // 如果配置了必须包含的活动节点，进行过滤
    if (requiredActivities && requiredActivities.length > 0) {
      eventLogs = this.filterEventLogsByRequiredActivities(
        eventLogs,
        requiredActivities,
      );
      this.logger.log(
        `Period details filtered by required activities: ${requiredActivities.join(', ')}. Remaining events: ${eventLogs.length}`,
      );
    }

    // 按案例分组并获取时间范围
    const caseGroups = this.groupByCaseId(eventLogs);
    const { start, end } = this.getTimeRange(eventLogs);

    // 生成时间序列并找到指定周期
    const timeSeries = this.generateTimeSeriesData(
      caseGroups,
      granularity,
      start,
      end,
    );
    const periodIndex = this.findPeriodIndex(timeSeries, period);

    // 获取当前周期和上一周期的事件数据
    const { currentPeriodEvents, previousPeriodEvents } = this.getPeriodEvents(
      caseGroups,
      periodIndex,
      timeSeries,
    );

    // 计算各项指标 - 使用特定周期的数据
    const rankings = this.calculatePeriodRankings(
      currentPeriodEvents,
      previousPeriodEvents,
    );

    // 计算活动耗时占比（包含当前和上期数据）
    const activityDurations = this.calculateActivityDurationChartWithPrevious(
      currentPeriodEvents,
      previousPeriodEvents,
    );

    // 计算资源等待时间对比（包含当前和上期数据）
    const resourceWaitTimes = this.calculateResourceWaitTimesWithPrevious(
      currentPeriodEvents,
      previousPeriodEvents,
    );

    return {
      period,
      rankings,
      activityDurations,
      resourceWaitTimes,
    };
  }

  /**
   * 计算性能排行榜
   */
  private calculateRankings(
    caseGroups: Record<string, EventLog[]>,
    timeSeries: any[],
  ) {
    if (timeSeries.length < 2) {
      return {
        activity: { improvements: [], declines: [] },
        resource: { improvements: [], declines: [] },
      };
    }

    // 分割数据为两个时间段
    const allEvents: EventLog[] = [];
    Object.values(caseGroups).forEach((events) => {
      allEvents.push(...events);
    });
    allEvents.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    const midPoint = Math.floor(allEvents.length / 2);
    const previousEvents = this.groupByCaseId(allEvents.slice(0, midPoint));
    const currentEvents = this.groupByCaseId(allEvents.slice(midPoint));

    // 计算活动性能变化
    const activityPerformance = this.calculateActivityPerformanceByPeriod(
      currentEvents,
      previousEvents,
    );

    // 计算资源性能变化
    const resourcePerformance = this.calculateResourcePerformanceByPeriod(
      currentEvents,
      previousEvents,
    );

    return {
      activity: activityPerformance,
      resource: resourcePerformance,
    };
  }

  /**
   * 计算特定周期的性能排行榜
   */
  private calculatePeriodRankings(
    currentPeriodEvents: Record<string, EventLog[]>,
    previousPeriodEvents: Record<string, EventLog[]>,
  ) {
    // 如果没有上一周期数据，返回空排行榜
    if (Object.keys(previousPeriodEvents).length === 0) {
      return {
        activity: { improvements: [], declines: [] },
        resource: { improvements: [], declines: [] },
      };
    }

    // 计算活动性能变化
    const activityPerformance = this.calculateActivityPerformanceByPeriod(
      currentPeriodEvents,
      previousPeriodEvents,
    );

    // 计算资源性能变化
    const resourcePerformance = this.calculateResourcePerformanceByPeriod(
      currentPeriodEvents,
      previousPeriodEvents,
    );

    return {
      activity: activityPerformance,
      resource: resourcePerformance,
    };
  }

  /**
   * 计算活动耗时占比（前5）
   */
  private calculateActivityDurationChart(
    caseGroups: Record<string, EventLog[]>,
  ) {
    const stats = this.calculateActivityStats(caseGroups);

    // 计算总时间
    const totalDuration = Object.values(stats).reduce(
      (sum, stat) => sum + stat.totalDuration,
      0,
    );

    // 转换为百分比并排序，过滤掉占比为0%的活动
    const activities = Object.entries(stats)
      .map(([name, stat]) => ({
        name,
        value: stat.totalDuration,
        percentage: (stat.totalDuration / totalDuration) * 100,
      }))
      .filter((activity) => activity.percentage > 0) // 过滤掉占比为0%的活动
      .sort((a, b) => b.value - a.value);

    // 取前5个，其余合并为"其他"
    const top5 = activities.slice(0, 5);
    const others = activities.slice(5);

    const result = [...top5];
    if (others.length > 0) {
      const othersTotal = others.reduce((sum, item) => sum + item.value, 0);
      result.push({
        name: '其他',
        value: othersTotal,
        percentage: (othersTotal / totalDuration) * 100,
      });
    }

    return { current: result };
  }

  /**
   * 计算活动耗时占比（包含当前和上期数据）
   */
  private calculateActivityDurationChartWithPrevious(
    currentPeriodEvents: Record<string, EventLog[]>,
    previousPeriodEvents: Record<string, EventLog[]>,
  ) {
    // 计算当前周期数据
    const currentResult =
      this.calculateActivityDurationChart(currentPeriodEvents);

    // 计算上期周期数据
    let previousResult: Array<{
      name: string;
      value: number;
      percentage: number;
    }> = [];
    if (Object.keys(previousPeriodEvents).length > 0) {
      const previousStats = this.calculateActivityStats(previousPeriodEvents);
      const previousTotalDuration = Object.values(previousStats).reduce(
        (sum, stat) => sum + stat.totalDuration,
        0,
      );

      if (previousTotalDuration > 0) {
        const previousActivities = Object.entries(previousStats)
          .map(([name, stat]) => ({
            name,
            value: stat.totalDuration,
            percentage: (stat.totalDuration / previousTotalDuration) * 100,
          }))
          .filter((activity) => activity.percentage > 0) // 过滤掉占比为0%的活动
          .sort((a, b) => b.value - a.value);

        const previousTop5 = previousActivities.slice(0, 5);
        const previousOthers = previousActivities.slice(5);

        previousResult = [...previousTop5];
        if (previousOthers.length > 0) {
          const othersTotal = previousOthers.reduce(
            (sum, item) => sum + item.value,
            0,
          );
          previousResult.push({
            name: '其他',
            value: othersTotal,
            percentage: (othersTotal / previousTotalDuration) * 100,
          });
        }
      }
    }

    return {
      current: currentResult.current,
      previous: previousResult,
    };
  }

  /**
   * 计算资源等待时间对比（前5）
   */
  private calculateResourceWaitTimes(caseGroups: Record<string, EventLog[]>) {
    const stats = this.calculateResourceStats(caseGroups);

    // 转换为图表数据并排序
    const resources = Object.entries(stats)
      .map(([name, stat]) => ({
        name,
        avgWaitTime: stat.count > 0 ? stat.totalDuration / stat.count : 0,
      }))
      .filter((r) => r.avgWaitTime > 0) // 过滤掉没有等待时间的资源
      .sort((a, b) => b.avgWaitTime - a.avgWaitTime);

    // 取前5个，其余合并为"其他"
    const top5 = resources.slice(0, 5);
    const others = resources.slice(5);

    const legend = top5.map((r) => r.name);
    const data = top5.map((r) => r.avgWaitTime);

    if (others.length > 0) {
      const othersAvg =
        others.reduce((sum, r) => sum + r.avgWaitTime, 0) / others.length;
      legend.push('其他');
      data.push(othersAvg);
    }

    const result = {
      current: {
        legend,
        data,
        periods: ['当前周期'],
      },
    };

    return result;
  }

  /**
   * 计算资源等待时间对比（包含当前和上期数据）
   */
  private calculateResourceWaitTimesWithPrevious(
    currentPeriodEvents: Record<string, EventLog[]>,
    previousPeriodEvents: Record<string, EventLog[]>,
  ) {
    // 计算当前周期数据
    const currentStats = this.calculateResourceStats(currentPeriodEvents);
    const currentResources = Object.entries(currentStats)
      .map(([name, stat]) => ({
        name,
        avgWaitTime: stat.count > 0 ? stat.totalDuration / stat.count : 0,
      }))
      .filter((r) => r.avgWaitTime > 0)
      .sort((a, b) => b.avgWaitTime - a.avgWaitTime);

    // 计算上期周期数据
    let previousResources: Array<{ name: string; avgWaitTime: number }> = [];
    if (Object.keys(previousPeriodEvents).length > 0) {
      const previousStats = this.calculateResourceStats(previousPeriodEvents);
      previousResources = Object.entries(previousStats)
        .map(([name, stat]) => ({
          name,
          avgWaitTime: stat.count > 0 ? stat.totalDuration / stat.count : 0,
        }))
        .filter((r) => r.avgWaitTime > 0)
        .sort((a, b) => b.avgWaitTime - a.avgWaitTime);
    }

    // 获取所有资源名称（合并当前和上期）
    const allResourceNames = new Set([
      ...currentResources.map((r) => r.name),
      ...previousResources.map((r) => r.name),
    ]);

    // 取前5个最重要的资源（按当前周期的等待时间排序）
    const topResourceNames = Array.from(allResourceNames)
      .map((name) => {
        const currentResource = currentResources.find((r) => r.name === name);
        const previousResource = previousResources.find((r) => r.name === name);
        return {
          name,
          currentWaitTime: currentResource ? currentResource.avgWaitTime : 0,
          previousWaitTime: previousResource ? previousResource.avgWaitTime : 0,
        };
      })
      .sort((a, b) => b.currentWaitTime - a.currentWaitTime)
      .slice(0, 5);

    const legend = topResourceNames.map((r) => r.name);
    const periods = ['上期', '本期'];

    const seriesData = topResourceNames.map((resource) => ({
      name: resource.name,
      data: [resource.previousWaitTime, resource.currentWaitTime],
    }));

    const result = {
      current: {
        legend,
        periods,
        series: seriesData,
      },
    };

    return result;
  }

  /**
   * 生成时间序列数据
   */
  private generateTimeSeriesData(
    caseGroups: Record<string, EventLog[]>,
    granularity: string,
    startDate: Date,
    endDate: Date,
  ) {
    const periods = this.generateTimePeriods(granularity, startDate, endDate);
    const timeSeriesData: Array<{
      period: string;
      start: Date;
      end: Date;
      avgDuration: number;
      avgWaitTime: number;
      caseCount: number;
    }> = [];

    for (const period of periods) {
      const periodData = this.calculatePeriodMetrics(caseGroups, period);
      timeSeriesData.push({
        period: period.label,
        start: period.start,
        end: period.end,
        ...periodData,
      });
    }

    return timeSeriesData;
  }

  /**
   * 生成时间周期
   */
  private generateTimePeriods(
    granularity: string,
    startDate: Date,
    endDate: Date,
  ): Array<{ start: Date; end: Date; label: string }> {
    const periods: Array<{ start: Date; end: Date; label: string }> = [];
    const current = new Date(startDate);

    while (current <= endDate) {
      const periodStart = new Date(current);
      let periodEnd: Date;
      let label: string;

      switch (granularity) {
        case 'day':
          periodEnd = new Date(current);
          periodEnd.setDate(periodEnd.getDate() + 1);
          label = current.toISOString().split('T')[0];
          current.setDate(current.getDate() + 1);
          break;
        case 'week':
          periodEnd = new Date(current);
          periodEnd.setDate(periodEnd.getDate() + 7);
          label = `第${Math.ceil((current.getTime() - startDate.getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1}周`;
          current.setDate(current.getDate() + 7);
          break;
        case 'month':
          periodEnd = new Date(current);
          periodEnd.setMonth(periodEnd.getMonth() + 1);
          label = `${current.getFullYear()}-${String(current.getMonth() + 1).padStart(2, '0')}`;
          current.setMonth(current.getMonth() + 1);
          break;

        case 'year':
          periodEnd = new Date(current);
          periodEnd.setFullYear(periodEnd.getFullYear() + 1);
          label = `${current.getFullYear()}年`;
          current.setFullYear(current.getFullYear() + 1);
          break;
        default:
          periodEnd = new Date(current);
          periodEnd.setDate(periodEnd.getDate() + 7);
          label = `第${Math.ceil((current.getTime() - startDate.getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1}周`;
          current.setDate(current.getDate() + 7);
      }

      periods.push({
        start: periodStart,
        end: periodEnd,
        label,
      });
    }

    return periods;
  }

  /**
   * 计算周期指标
   */
  private calculatePeriodMetrics(
    caseGroups: Record<string, EventLog[]>,
    period: { start: Date; end: Date; label: string },
  ): {
    avgDuration: number;
    avgWaitTime: number;
    caseCount: number;
  } {
    // 修改过滤逻辑：使用案例开始时间来分配到对应周期，避免重复计算
    const periodCases = Object.entries(caseGroups).filter(([_, events]) => {
      const caseStart = events[0].timestamp;

      // 案例按开始时间分配到对应周期
      return caseStart >= period.start && caseStart < period.end;
    });

    if (periodCases.length === 0) {
      return {
        avgDuration: 0,
        avgWaitTime: 0,
        caseCount: 0,
      };
    }

    // 计算平均持续时间
    const durations = periodCases.map(([_, events]) => {
      const start = events[0].timestamp.getTime();
      const end = events[events.length - 1].timestamp.getTime();
      return (end - start) / (1000 * 60 * 60); // 转换为小时
    });

    const avgDuration =
      durations.reduce((sum, d) => sum + d, 0) / durations.length;

    // 计算平均等待时间
    let totalWaitTime = 0;
    let waitTimeCount = 0;

    periodCases.forEach(([_, events]) => {
      for (let i = 0; i < events.length - 1; i++) {
        const waitTime =
          events[i + 1].timestamp.getTime() - events[i].timestamp.getTime();
        totalWaitTime += waitTime;
        waitTimeCount++;
      }
    });

    const avgWaitTime =
      waitTimeCount > 0 ? totalWaitTime / waitTimeCount / (1000 * 60 * 60) : 0;

    return {
      avgDuration: Number(avgDuration.toFixed(2)),
      avgWaitTime: Number(avgWaitTime.toFixed(2)),
      caseCount: periodCases.length,
    };
  }

  /**
   * 计算整体趋势
   */
  private calculateOverallTrends(timeSeries: any[]) {
    if (timeSeries.length < 2) {
      return {
        avgDurationChange: 0,
        avgWaitTimeChange: 0,
      };
    }

    const firstPeriod = timeSeries[0];
    const lastPeriod = timeSeries[timeSeries.length - 1];

    const avgDurationChange =
      firstPeriod.avgDuration > 0
        ? ((lastPeriod.avgDuration - firstPeriod.avgDuration) /
            firstPeriod.avgDuration) *
          100
        : 0;

    const avgWaitTimeChange =
      firstPeriod.avgWaitTime > 0
        ? ((lastPeriod.avgWaitTime - firstPeriod.avgWaitTime) /
            firstPeriod.avgWaitTime) *
          100
        : 0;

    return {
      avgDurationChange: Number(avgDurationChange.toFixed(2)),
      avgWaitTimeChange: Number(avgWaitTimeChange.toFixed(2)),
    };
  }

  /**
   * 根据时间范围过滤事件数据
   */
  private filterEventsByTimeRange(
    caseGroups: Record<string, EventLog[]>,
    startTime: Date,
    endTime: Date,
  ): Record<string, EventLog[]> {
    const filteredGroups: Record<string, EventLog[]> = {};

    Object.entries(caseGroups).forEach(([caseId, events]) => {
      // 过滤在时间范围内的事件
      const filteredEvents = events.filter((event) => {
        const eventTime = event.timestamp.getTime();
        return (
          eventTime >= startTime.getTime() && eventTime <= endTime.getTime()
        );
      });

      // 如果有事件在时间范围内，保留这个案例
      if (filteredEvents.length > 0) {
        filteredGroups[caseId] = filteredEvents;
      }
    });

    return filteredGroups;
  }

  /**
   * 基于周期数据计算活动性能变化
   */
  private calculateActivityPerformanceByPeriod(
    currentPeriodEvents: Record<string, EventLog[]>,
    previousPeriodEvents: Record<string, EventLog[]>,
  ) {
    const improvements: any[] = [];
    const declines: any[] = [];

    // 计算当前周期的活动统计
    const currentStats = this.calculateActivityStats(currentPeriodEvents);
    const previousStats = this.calculateActivityStats(previousPeriodEvents);

    // 比较两个周期的活动性能
    const allActivities = new Set([
      ...Object.keys(currentStats),
      ...Object.keys(previousStats),
    ]);

    allActivities.forEach((activity) => {
      const current = currentStats[activity];
      const previous = previousStats[activity];

      // 直接使用所有可用数据进行比较
      if (current && previous && current.count > 0 && previous.count > 0) {
        const currentAvg = current.totalDuration / current.count;
        const previousAvg = previous.totalDuration / previous.count;

        // 计算变化百分比（正值表示改进，即时间减少）
        const change = ((previousAvg - currentAvg) / previousAvg) * 100;

        const item = {
          name: activity,
          description: change > 0 ? '执行时间减少' : '执行时间增加',
          change: Number(Math.abs(change).toFixed(2)),
          currentValue: Number(currentAvg.toFixed(2)),
          previousValue: Number(previousAvg.toFixed(2)),
        };

        // 直接使用所有有变化的数据，不设置阈值
        if (change > 0) {
          // 改进（时间减少）
          improvements.push(item);
        } else if (change < 0) {
          // 恶化（时间增加）
          declines.push(item);
        }
      }
    });

    // 按变化幅度排序
    improvements.sort((a, b) => b.change - a.change);
    declines.sort((a, b) => b.change - a.change);

    return { improvements, declines };
  }

  /**
   * 基于周期数据计算资源性能变化
   */
  private calculateResourcePerformanceByPeriod(
    currentPeriodEvents: Record<string, EventLog[]>,
    previousPeriodEvents: Record<string, EventLog[]>,
  ) {
    const improvements: any[] = [];
    const declines: any[] = [];

    // 计算当前周期的资源统计
    const currentStats = this.calculateResourceStats(currentPeriodEvents);
    const previousStats = this.calculateResourceStats(previousPeriodEvents);

    // 比较两个周期的资源性能
    const allResources = new Set([
      ...Object.keys(currentStats),
      ...Object.keys(previousStats),
    ]);

    allResources.forEach((resource) => {
      const current = currentStats[resource];
      const previous = previousStats[resource];

      if (current && previous && current.count > 0 && previous.count > 0) {
        const currentAvg = current.totalDuration / current.count;
        const previousAvg = previous.totalDuration / previous.count;

        // 计算变化百分比（正值表示改进，即时间减少）
        const change = ((previousAvg - currentAvg) / previousAvg) * 100;

        const item = {
          name: resource,
          description: change > 0 ? '等待时间减少' : '等待时间增加',
          change: Number(Math.abs(change).toFixed(2)),
          currentValue: Number(currentAvg.toFixed(2)),
          previousValue: Number(previousAvg.toFixed(2)),
        };

        if (change > 0) {
          // 改进（时间减少）
          improvements.push(item);
        } else if (change < 0) {
          // 恶化（时间增加）
          declines.push(item);
        }
      }
    });

    // 按变化幅度排序
    improvements.sort((a, b) => b.change - a.change);
    declines.sort((a, b) => b.change - a.change);

    return { improvements, declines };
  }

  /**
   * 计算活动统计数据 - 优化版本，充分利用所有数据
   */
  private calculateActivityStats(caseGroups: Record<string, EventLog[]>) {
    const stats: Record<
      string,
      {
        totalDuration: number;
        count: number;
        occurrences: number;
        totalCases: number;
      }
    > = {};

    const processedCases = new Set<string>();

    Object.entries(caseGroups).forEach(([caseId, events]) => {
      if (events.length === 0) return;

      processedCases.add(caseId);

      // 按时间排序确保正确的顺序
      const sortedEvents = [...events].sort(
        (a, b) => a.timestamp.getTime() - b.timestamp.getTime(),
      );

      sortedEvents.forEach((event, index) => {
        const activity = event.activity;

        // 初始化活动统计
        if (!stats[activity]) {
          stats[activity] = {
            totalDuration: 0,
            count: 0,
            occurrences: 0,
            totalCases: 0,
          };
        }

        // 计算活动出现次数
        stats[activity].occurrences += 1;

        // 计算活动持续时间（如果有下一个事件）
        if (index < sortedEvents.length - 1) {
          const duration =
            (sortedEvents[index + 1].timestamp.getTime() -
              event.timestamp.getTime()) /
            (1000 * 60 * 60);

          // 直接使用所有数据，不进行过滤
          if (duration > 0) {
            stats[activity].totalDuration += duration;
            stats[activity].count += 1;
          }
        }
      });
    });

    // 计算每个活动涉及的案例数
    Object.keys(stats).forEach((activity) => {
      const casesWithActivity = new Set<string>();
      Object.entries(caseGroups).forEach(([caseId, events]) => {
        if (events.some((event) => event.activity === activity)) {
          casesWithActivity.add(caseId);
        }
      });
      stats[activity].totalCases = casesWithActivity.size;
    });

    return stats;
  }

  /**
   * 计算资源统计数据 - 计算资源等待时间
   */
  private calculateResourceStats(caseGroups: Record<string, EventLog[]>) {
    const stats: Record<
      string,
      {
        totalDuration: number;
        count: number;
        occurrences: number;
        totalCases: number;
        totalWaitTime: number;
        waitTimeCount: number;
      }
    > = {};

    const processedCases = new Set<string>();

    // 收集所有资源的事件，按资源分组
    const resourceEvents: Record<
      string,
      Array<{ timestamp: Date; activity: string; caseId: string }>
    > = {};

    Object.entries(caseGroups).forEach(([caseId, events]) => {
      if (events.length === 0) return;

      processedCases.add(caseId);

      events.forEach((event) => {
        const resource = event.resource;

        if (!resource || resource.trim() === '') {
          // 跳过没有资源的事件
          return;
        }

        if (!resourceEvents[resource]) {
          resourceEvents[resource] = [];
        }

        resourceEvents[resource].push({
          timestamp: event.timestamp,
          activity: event.activity,
          caseId: caseId,
        });
      });
    });

    // 计算每个资源的等待时间
    Object.entries(resourceEvents).forEach(([resource, events]) => {
      // 按时间排序
      const sortedEvents = events.sort(
        (a, b) => a.timestamp.getTime() - b.timestamp.getTime(),
      );

      stats[resource] = {
        totalDuration: 0,
        count: 0,
        occurrences: sortedEvents.length,
        totalCases: 0,
        totalWaitTime: 0,
        waitTimeCount: 0,
      };

      // 计算资源等待时间（两个连续任务之间的间隔）
      for (let i = 0; i < sortedEvents.length - 1; i++) {
        const currentEvent = sortedEvents[i];
        const nextEvent = sortedEvents[i + 1];

        // 计算等待时间（下一个任务开始时间 - 当前任务开始时间）
        const waitTime =
          (nextEvent.timestamp.getTime() - currentEvent.timestamp.getTime()) /
          (1000 * 60 * 60);

        if (waitTime > 0) {
          stats[resource].totalWaitTime += waitTime;
          stats[resource].waitTimeCount += 1;
        }
      }

      // 使用等待时间作为主要指标
      stats[resource].totalDuration = stats[resource].totalWaitTime;
      stats[resource].count = stats[resource].waitTimeCount;
    });

    // 计算每个资源涉及的案例数
    Object.keys(stats).forEach((resource) => {
      const casesWithResource = new Set<string>();
      Object.entries(caseGroups).forEach(([caseId, events]) => {
        if (
          events.some((event) => {
            let eventResource = event.resource;
            if (!eventResource || eventResource.trim() === '') {
              eventResource = `${event.activity}`;
            }
            return eventResource === resource;
          })
        ) {
          casesWithResource.add(caseId);
        }
      });
      stats[resource].totalCases = casesWithResource.size;
    });

    return stats;
  }
}
