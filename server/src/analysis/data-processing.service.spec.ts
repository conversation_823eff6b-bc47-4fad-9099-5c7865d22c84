import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DataProcessingService } from './data-processing.service';
import { DataStreamService } from './data-stream.service';
import { EventLog } from '../entities/event-log.entity';
import { UploadDataDto } from './dto';

describe('DataProcessingService', () => {
  let service: DataProcessingService;
  let eventLogRepository: Repository<EventLog>;
  let dataStreamService: DataStreamService;

  const mockEventLogRepository = {
    save: jest.fn(),
    find: jest.fn(),
    count: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      groupBy: jest.fn().mockReturnThis(),
      getRawMany: jest.fn(),
    })),
  };

  const mockDataStreamService = {
    processLargeCSV: jest.fn(),
    estimateFileSize: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DataProcessingService,
        {
          provide: getRepositoryToken(EventLog),
          useValue: mockEventLogRepository,
        },
        {
          provide: DataStreamService,
          useValue: mockDataStreamService,
        },
      ],
    }).compile();

    service = module.get<DataProcessingService>(DataProcessingService);
    eventLogRepository = module.get<Repository<EventLog>>(
      getRepositoryToken(EventLog),
    );
    dataStreamService = module.get<DataStreamService>(DataStreamService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('validateData', () => {
    const mockUploadDto: UploadDataDto = {
      processId: 1,
      caseIdField: 'case_id',
      activityField: 'activity',
      timestampField: 'timestamp',
      resourceField: 'resource',
      costField: 'cost',
    };

    it('should validate correct data successfully', () => {
      const testData = [
        {
          case_id: 'case1',
          activity: 'Start',
          timestamp: '2024-01-01 10:00:00',
          resource: 'User1',
          cost: '100',
        },
        {
          case_id: 'case1',
          activity: 'End',
          timestamp: '2024-01-01 11:00:00',
          resource: 'User1',
          cost: '200',
        },
      ];

      const result = service.validateData(testData, mockUploadDto);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.totalRows).toBe(2);
      expect(result.validRows).toBe(2);
      expect(result.preview).toEqual(testData);
    });

    it('should detect missing required fields', () => {
      const testData = [
        {
          case_id: 'case1',
          // missing activity field
          timestamp: '2024-01-01 10:00:00',
        },
      ];

      const result = service.validateData(testData, mockUploadDto);

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0]).toContain('缺少必需字段');
    });

    it('should detect invalid timestamp format', () => {
      const testData = [
        {
          case_id: 'case1',
          activity: 'Start',
          timestamp: 'invalid-date',
        },
      ];

      const result = service.validateData(testData, mockUploadDto);

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0]).toContain('时间格式无效');
    });

    it('should handle empty data', () => {
      const result = service.validateData([], mockUploadDto);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('文件为空或无有效数据');
      expect(result.totalRows).toBe(0);
      expect(result.validRows).toBe(0);
    });
  });

  describe('saveEventLogs', () => {
    it('should save event logs successfully', async () => {
      const testData = [
        {
          case_id: 'case1',
          activity: 'Start',
          timestamp: '2024-01-01 10:00:00',
          resource: 'User1',
        },
      ];

      const mockUploadDto: UploadDataDto = {
        processId: 1,
        caseIdField: 'case_id',
        activityField: 'activity',
        timestampField: 'timestamp',
        resourceField: 'resource',
      };

      const mockSavedLogs = [{ id: 1 }, { id: 2 }];
      mockEventLogRepository.save.mockResolvedValue(mockSavedLogs);

      const result = await service.saveEventLogs(testData, mockUploadDto);

      expect(mockEventLogRepository.save).toHaveBeenCalled();
      expect(result).toEqual(mockSavedLogs);
    });
  });

  describe('getDataStatistics', () => {
    it('should return process statistics', async () => {
      const processId = 1;

      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(100),
        getRawOne: jest
          .fn()
          .mockResolvedValueOnce({ count: '10' }) // unique cases
          .mockResolvedValueOnce({ count: '5' }) // unique activities
          .mockResolvedValueOnce({ count: '3' }) // unique resources
          .mockResolvedValueOnce({ minDate: new Date(), maxDate: new Date() }), // date range
      };

      mockEventLogRepository.createQueryBuilder.mockReturnValue(
        mockQueryBuilder,
      );

      const result = await service.getDataStatistics(processId);

      expect(result).toHaveProperty('totalEvents');
      expect(result).toHaveProperty('uniqueCases');
      expect(result).toHaveProperty('uniqueActivities');
      expect(result).toHaveProperty('uniqueResources');
      expect(result).toHaveProperty('dateRange');
      expect(result.totalEvents).toBe(100);
      expect(result.uniqueCases).toBe(10);
    });
  });

  describe('uploadLargeFile', () => {
    it('should process large file using stream service', async () => {
      const filePath = '/tmp/large-file.csv';
      const mockUploadDto: UploadDataDto = {
        processId: 1,
        caseIdField: 'case_id',
        activityField: 'activity',
        timestampField: 'timestamp',
      };

      const mockResult = {
        message: '文件上传成功',
        totalProcessed: 1000,
        validRows: 950,
        errors: [],
        warnings: ['Some warnings'],
        preview: [],
      };

      mockDataStreamService.processLargeCSV.mockResolvedValue(mockResult);

      const result = await service.uploadLargeFile(filePath, mockUploadDto);

      expect(dataStreamService.processLargeCSV).toHaveBeenCalledWith(
        filePath,
        mockUploadDto,
      );
      expect(result).toEqual(mockResult);
    });
  });
});
