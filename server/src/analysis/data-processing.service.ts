import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as csv from 'csv-parser';
import * as XLSX from 'xlsx';
import * as fs from 'fs';
import * as moment from 'moment';
import { EventLog } from '../entities/event-log.entity';
import { UploadDataDto } from './dto';
import { DataStreamService } from './data-stream.service';

export interface DataValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  totalRows: number;
  validRows: number;
  preview: any[];
}

@Injectable()
export class DataProcessingService {
  constructor(
    @InjectRepository(EventLog)
    private readonly eventLogRepository: Repository<EventLog>,
    private readonly dataStreamService: DataStreamService,
  ) {}

  async parseFile(filePath: string, fileType: string): Promise<any[]> {
    try {
      if (fileType === 'csv') {
        // 检查文件大小，决定是否使用流式处理
        const fileInfo =
          await this.dataStreamService.estimateFileSize(filePath);

        if (fileInfo.shouldUseStreaming) {
          throw new BadRequestException(
            `文件过大 (${Math.round(fileInfo.fileSizeBytes / 1024 / 1024)}MB, 约${fileInfo.estimatedRows}行)，请使用流式上传功能`,
          );
        }

        return this.parseCSV(filePath);
      } else if (fileType === 'xlsx' || fileType === 'xls') {
        return this.parseExcel(filePath);
      } else {
        throw new BadRequestException('不支持的文件格式');
      }
    } catch (error) {
      throw new BadRequestException(`文件解析失败: ${error.message}`);
    }
  }

  private async parseCSV(filePath: string): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const results: any[] = [];
      let rowCount = 0;
      const maxRows = 100000; // 限制最大行数
      const batchSize = 1000; // 批处理大小
      let batch: any[] = [];

      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (data) => {
          rowCount++;

          // 检查是否超过最大行数限制
          if (rowCount > maxRows) {
            return reject(new Error(`文件行数超过限制 (${maxRows} 行)`));
          }

          // 处理CSV中的时间格式
          const processedData = this.processTimeFields(data);
          batch.push(processedData);

          // 批处理，减少内存压力
          if (batch.length >= batchSize) {
            results.push(...batch);
            batch = [];

            // 强制垃圾回收（如果可用）
            if (global.gc) {
              global.gc();
            }
          }
        })
        .on('end', () => {
          // 处理剩余的批次
          if (batch.length > 0) {
            results.push(...batch);
          }
          resolve(results);
        })
        .on('error', (error) => reject(error));
    });
  }

  private parseExcel(filePath: string): any[] {
    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    // 获取原始数据，包含单元格类型信息
    const rawData = XLSX.utils.sheet_to_json(worksheet, {
      raw: false, // 不使用原始值，让XLSX自动格式化
      dateNF: 'yyyy-mm-dd hh:mm:ss', // 设置日期格式
    });

    // 进一步处理可能的Excel日期序列号
    return rawData.map((row: any) => {
      const processedRow = { ...row };

      // 遍历每个字段，检查是否为Excel日期序列号
      Object.keys(processedRow).forEach((key) => {
        const value = processedRow[key];

        // 检查是否为数字且可能是Excel日期序列号
        if (typeof value === 'number' && value > 1 && value < 2958466) {
          // Excel日期序列号范围大约是1到2958465 (1900-01-01 到 9999-12-31)
          try {
            // 尝试将数字转换为日期
            const excelDate = new Date((value - 25569) * 86400 * 1000);

            // 验证转换后的日期是否合理 (1900年到2100年之间)
            if (
              excelDate.getFullYear() >= 1900 &&
              excelDate.getFullYear() <= 2100
            ) {
              // 格式化为标准时间字符串
              processedRow[key] = excelDate
                .toISOString()
                .replace('T', ' ')
                .substring(0, 19);
            }
          } catch (error) {
            // 如果转换失败，保持原值
            console.warn(
              `Failed to convert Excel date for field ${key}:`,
              value,
            );
          }
        }

        // 检查字符串是否看起来像时间戳但格式不标准
        if (typeof value === 'string' && value.length > 0) {
          // 尝试解析各种时间格式
          const timePatterns = [
            /^\d{4}[-\/]\d{1,2}[-\/]\d{1,2}\s+\d{1,2}:\d{1,2}:\d{1,2}\.\d{1,3}$/, // 带毫秒格式 2024-04-30 15:28:56.487
            /^\d{4}[-\/]\d{1,2}[-\/]\d{1,2}\s+\d{1,2}:\d{1,2}:\d{1,2}$/, // 标准格式
            /^\d{1,2}[-\/]\d{1,2}[-\/]\d{4}\s+\d{1,2}:\d{1,2}:\d{1,2}\.\d{1,3}$/, // 美式带毫秒格式
            /^\d{1,2}[-\/]\d{1,2}[-\/]\d{4}\s+\d{1,2}:\d{1,2}:\d{1,2}$/, // 美式格式
            /^\d{4}[-\/]\d{1,2}[-\/]\d{1,2}$/, // 仅日期
          ];

          const isTimeString = timePatterns.some((pattern) =>
            pattern.test(value),
          );

          if (isTimeString) {
            try {
              const parsedDate = new Date(value);
              if (!isNaN(parsedDate.getTime())) {
                // 标准化时间格式
                processedRow[key] = parsedDate
                  .toISOString()
                  .replace('T', ' ')
                  .substring(0, 19);
              }
            } catch (error) {
              // 保持原值
            }
          }
        }
      });

      return processedRow;
    });
  }

  private processTimeFields(data: any): any {
    const processedData = { ...data };

    // 遍历每个字段，检查是否为时间相关字段
    Object.keys(processedData).forEach((key) => {
      const value = processedData[key];

      // 检查字符串是否看起来像时间戳
      if (typeof value === 'string' && value.length > 0) {
        // 尝试解析各种时间格式
        const timePatterns = [
          /^\d{4}[-\/]\d{1,2}[-\/]\d{1,2}\s+\d{1,2}:\d{1,2}:\d{1,2}\.\d{1,3}$/, // 带毫秒格式 2024-04-30 15:28:56.487
          /^\d{4}[-\/]\d{1,2}[-\/]\d{1,2}\s+\d{1,2}:\d{1,2}:\d{1,2}$/, // 标准格式
          /^\d{1,2}[-\/]\d{1,2}[-\/]\d{4}\s+\d{1,2}:\d{1,2}:\d{1,2}\.\d{1,3}$/, // 美式带毫秒格式
          /^\d{1,2}[-\/]\d{1,2}[-\/]\d{4}\s+\d{1,2}:\d{1,2}:\d{1,2}$/, // 美式格式
          /^\d{4}[-\/]\d{1,2}[-\/]\d{1,2}$/, // 仅日期
          /^\d{1,2}[-\/]\d{1,2}[-\/]\d{4}$/, // 美式仅日期
        ];

        const isTimeString = timePatterns.some((pattern) =>
          pattern.test(value),
        );

        if (isTimeString) {
          try {
            const parsedDate = new Date(value);
            if (!isNaN(parsedDate.getTime())) {
              // 标准化时间格式
              processedData[key] = parsedDate
                .toISOString()
                .replace('T', ' ')
                .substring(0, 19);
            }
          } catch (error) {
            // 保持原值
          }
        }
      }

      // 检查是否为数字且可能是时间戳（秒或毫秒）
      if (typeof value === 'string' && /^\d+$/.test(value)) {
        const numValue = parseInt(value);

        // 检查是否为Unix时间戳（秒，大约1970年到2100年）
        if (numValue > 0 && numValue < 4102444800) {
          // 2100年的时间戳
          try {
            let date: Date;

            // 判断是秒还是毫秒时间戳
            if (numValue < 10000000000) {
              // 小于10位数，可能是秒
              date = new Date(numValue * 1000);
            } else {
              // 大于等于10位数，可能是毫秒
              date = new Date(numValue);
            }

            // 验证转换后的日期是否合理
            if (date.getFullYear() >= 1970 && date.getFullYear() <= 2100) {
              processedData[key] = date
                .toISOString()
                .replace('T', ' ')
                .substring(0, 19);
            }
          } catch (error) {
            // 保持原值
          }
        }
      }
    });

    return processedData;
  }

  /**
   * 验证时间戳格式是否有效
   */
  private isValidTimestamp(value: any): boolean {
    if (!value) return false;

    // 如果是数字，可能是Excel日期序列号或时间戳
    const numValue = Number(value);
    if (!isNaN(numValue)) {
      // Excel日期序列号
      if (numValue > 1 && numValue < 2958466) {
        try {
          const excelDate = new Date((numValue - 25569) * 86400 * 1000);
          const momentDate = moment(excelDate);
          return (
            momentDate.isValid() &&
            momentDate.year() >= 1900 &&
            momentDate.year() <= 2100
          );
        } catch {
          return false;
        }
      }

      // Unix时间戳
      if (numValue > 946684800 && numValue < 4102444800) {
        try {
          let momentDate: moment.Moment;
          if (numValue < 10000000000) {
            // 秒时间戳
            momentDate = moment.unix(numValue);
          } else {
            // 毫秒时间戳
            momentDate = moment(numValue);
          }
          return (
            momentDate.isValid() &&
            momentDate.year() >= 1970 &&
            momentDate.year() <= 2100
          );
        } catch {
          return false;
        }
      }
    }

    // 如果是字符串，使用 moment.js 尝试解析各种时间格式
    if (typeof value === 'string' && value.length > 0) {
      // 定义支持的时间格式
      const supportedFormats = [
        // 年月日 时分秒（带毫秒）
        'YYYY-MM-DD HH:mm:ss.SSS', // 新增: 支持 2024-04-30 15:28:56.487 格式
        'YYYY/MM/DD HH:mm:ss.SSS', // 新增: 支持 2024/04/30 15:28:56.487 格式
        'YYYY/M/D HH:mm:ss.SSS', // 新增: 支持 2024/4/30 15:28:56.487 格式
        'YYYY/M/DD HH:mm:ss.SSS', // 新增: 支持 2024/4/30 15:28:56.487 格式
        'YYYY/MM/D HH:mm:ss.SSS', // 新增: 支持 2024/04/30 15:28:56.487 格式
        'YYYY/M/D H:mm:ss.SSS', // 新增: 支持 2024/4/30 9:28:56.487 格式（单位数小时）
        'YYYY/M/DD H:mm:ss.SSS', // 新增: 支持 2024/4/30 9:28:56.487 格式
        'YYYY/MM/D H:mm:ss.SSS', // 新增: 支持 2024/04/30 9:28:56.487 格式

        // 年月日 时分秒
        'YYYY-MM-DD HH:mm:ss',
        'YYYY/MM/DD HH:mm:ss',
        'YYYY/M/D HH:mm:ss', // 新增: 支持 2025/1/1 14:23:30 格式
        'YYYY/M/DD HH:mm:ss', // 新增: 支持 2025/1/01 14:23:30 格式
        'YYYY/MM/D HH:mm:ss', // 新增: 支持 2025/01/1 14:23:30 格式
        'YYYY/M/D H:mm:ss', // 新增: 支持 2025/1/2 9:10:30 格式（单位数小时）
        'YYYY/M/DD H:mm:ss', // 新增: 支持 2025/1/02 9:10:30 格式
        'YYYY/MM/D H:mm:ss', // 新增: 支持 2025/01/2 9:10:30 格式
        'MM-DD-YYYY HH:mm:ss',
        'MM/DD/YYYY HH:mm:ss',
        'DD-MM-YYYY HH:mm:ss',
        'DD/MM/YYYY HH:mm:ss',
        'D/M/YYYY HH:mm:ss', // 新增: 支持单位数日期/月份
        'DD/M/YYYY HH:mm:ss', // 新增: 支持单位数月份
        'D/MM/YYYY HH:mm:ss', // 新增: 支持单位数日期
        'D/M/YYYY H:mm:ss', // 新增: 支持单位数小时
        'DD/M/YYYY H:mm:ss', // 新增: 支持单位数小时
        'D/MM/YYYY H:mm:ss', // 新增: 支持单位数小时

        // 两位数年份格式 时分秒
        'M/D/YY HH:mm:ss', // 新增: 支持两位数年份，如 1/4/25 14:23:00
        'MM/DD/YY HH:mm:ss',
        'D/M/YY HH:mm:ss',
        'DD/MM/YY HH:mm:ss',
        'M-D-YY HH:mm:ss',
        'MM-DD-YY HH:mm:ss',
        'D-M-YY HH:mm:ss',
        'DD-MM-YY HH:mm:ss',

        // 两位数年份格式 时分
        'M/D/YY HH:mm', // 新增: 支持两位数年份，如 1/4/25 14:23
        'MM/DD/YY HH:mm',
        'D/M/YY HH:mm',
        'DD/MM/YY HH:mm',
        'M-D-YY HH:mm',
        'MM-DD-YY HH:mm',
        'D-M-YY HH:mm',
        'DD-MM-YY HH:mm',

        // 年月日
        'YYYY-MM-DD',
        'YYYY/MM/DD',
        'YYYY/M/D', // 新增: 支持 2025/1/1 格式
        'YYYY/M/DD', // 新增: 支持 2025/1/01 格式
        'YYYY/MM/D', // 新增: 支持 2025/01/1 格式
        'MM-DD-YYYY',
        'MM/DD/YYYY',
        'DD-MM-YYYY',
        'DD/MM/YYYY',
        'D/M/YYYY', // 新增: 支持单位数日期/月份
        'DD/M/YYYY', // 新增: 支持单位数月份
        'D/MM/YYYY', // 新增: 支持单位数日期

        // 两位数年份格式 仅日期
        'M/D/YY',
        'MM/DD/YY',
        'D/M/YY',
        'DD/MM/YY',
        'M-D-YY',
        'MM-DD-YY',
        'D-M-YY',
        'DD-MM-YY',

        // ISO格式
        'YYYY-MM-DDTHH:mm:ss',
        'YYYY-MM-DDTHH:mm:ssZ',
        'YYYY-MM-DDTHH:mm:ss.SSSZ',

        // 年月日 时分
        'YYYY-MM-DD HH:mm',
        'YYYY/MM/DD HH:mm',
        'YYYY/M/D HH:mm', // 新增: 支持 2025/1/1 10:21 格式
        'YYYY/M/DD HH:mm', // 新增: 支持 2025/1/01 10:21 格式
        'YYYY/MM/D HH:mm', // 新增: 支持 2025/01/1 10:21 格式
        'YYYY/M/D H:mm', // 新增: 支持 2025/1/2 9:10 格式（单位数小时）
        'YYYY/M/DD H:mm', // 新增: 支持 2025/1/02 9:10 格式
        'YYYY/MM/D H:mm', // 新增: 支持 2025/01/2 9:10 格式
        'MM-DD-YYYY HH:mm',
        'MM/DD/YYYY HH:mm',
        'DD-MM-YYYY HH:mm',
        'DD/MM/YYYY HH:mm',
        'D/M/YYYY HH:mm', // 新增: 支持单位数日期/月份
        'DD/M/YYYY HH:mm', // 新增: 支持单位数月份
        'D/MM/YYYY HH:mm', // 新增: 支持单位数日期
        'D/M/YYYY H:mm', // 新增: 支持单位数小时
        'DD/M/YYYY H:mm', // 新增: 支持单位数小时
        'D/MM/YYYY H:mm', // 新增: 支持单位数小时
      ];

      // 尝试使用指定格式解析
      for (const format of supportedFormats) {
        const momentDate = moment(value, format, true); // strict parsing
        if (
          momentDate.isValid() &&
          momentDate.year() >= 1900 &&
          momentDate.year() <= 2100
        ) {
          return true;
        }
      }

      // 如果所有指定格式都失败，返回false
      // 不再使用moment.js的自动解析，避免deprecation警告
      return false;
    }

    return false;
  }

  /**
   * 将时间值转换为有效的Date对象
   */
  private convertToDate(value: any): Date | null {
    if (!value) return null;

    // 如果是数字，可能是Excel日期序列号或时间戳
    const numValue = Number(value);
    if (!isNaN(numValue)) {
      // Excel日期序列号
      if (numValue > 1 && numValue < 2958466) {
        try {
          const excelDate = new Date((numValue - 25569) * 86400 * 1000);
          const momentDate = moment(excelDate);
          if (
            momentDate.isValid() &&
            momentDate.year() >= 1900 &&
            momentDate.year() <= 2100
          ) {
            return momentDate.toDate();
          }
        } catch {
          // 继续尝试其他方法
        }
      }

      // Unix时间戳
      if (numValue > 946684800 && numValue < 4102444800) {
        try {
          let momentDate: moment.Moment;
          if (numValue < 10000000000) {
            // 秒时间戳
            momentDate = moment.unix(numValue);
          } else {
            // 毫秒时间戳
            momentDate = moment(numValue);
          }
          if (
            momentDate.isValid() &&
            momentDate.year() >= 1970 &&
            momentDate.year() <= 2100
          ) {
            return momentDate.toDate();
          }
        } catch {
          // 继续尝试其他方法
        }
      }
    }

    // 如果是字符串，使用 moment.js 尝试解析各种时间格式
    if (typeof value === 'string' && value.length > 0) {
      // 定义支持的时间格式
      const supportedFormats = [
        // 年月日 时分秒（带毫秒）
        'YYYY-MM-DD HH:mm:ss.SSS', // 新增: 支持 2024-04-30 15:28:56.487 格式
        'YYYY/MM/DD HH:mm:ss.SSS', // 新增: 支持 2024/04/30 15:28:56.487 格式
        'YYYY/M/D HH:mm:ss.SSS', // 新增: 支持 2024/4/30 15:28:56.487 格式
        'YYYY/M/DD HH:mm:ss.SSS', // 新增: 支持 2024/4/30 15:28:56.487 格式
        'YYYY/MM/D HH:mm:ss.SSS', // 新增: 支持 2024/04/30 15:28:56.487 格式
        'YYYY/M/D H:mm:ss.SSS', // 新增: 支持 2024/4/30 9:28:56.487 格式（单位数小时）
        'YYYY/M/DD H:mm:ss.SSS', // 新增: 支持 2024/4/30 9:28:56.487 格式
        'YYYY/MM/D H:mm:ss.SSS', // 新增: 支持 2024/04/30 9:28:56.487 格式

        // 年月日 时分秒
        'YYYY-MM-DD HH:mm:ss',
        'YYYY/MM/DD HH:mm:ss',
        'YYYY/M/D HH:mm:ss', // 新增: 支持 2025/1/1 14:23:30 格式
        'YYYY/M/DD HH:mm:ss', // 新增: 支持 2025/1/01 14:23:30 格式
        'YYYY/MM/D HH:mm:ss', // 新增: 支持 2025/01/1 14:23:30 格式
        'YYYY/M/D H:mm:ss', // 新增: 支持 2025/1/2 9:10:30 格式（单位数小时）
        'YYYY/M/DD H:mm:ss', // 新增: 支持 2025/1/02 9:10:30 格式
        'YYYY/MM/D H:mm:ss', // 新增: 支持 2025/01/2 9:10:30 格式
        'MM-DD-YYYY HH:mm:ss',
        'MM/DD/YYYY HH:mm:ss',
        'DD-MM-YYYY HH:mm:ss',
        'DD/MM/YYYY HH:mm:ss',
        'D/M/YYYY HH:mm:ss', // 新增: 支持单位数日期/月份
        'DD/M/YYYY HH:mm:ss', // 新增: 支持单位数月份
        'D/MM/YYYY HH:mm:ss', // 新增: 支持单位数日期
        'D/M/YYYY H:mm:ss', // 新增: 支持单位数小时
        'DD/M/YYYY H:mm:ss', // 新增: 支持单位数小时
        'D/MM/YYYY H:mm:ss', // 新增: 支持单位数小时

        // 两位数年份格式 时分秒
        'M/D/YY HH:mm:ss', // 新增: 支持两位数年份，如 1/4/25 14:23:00
        'MM/DD/YY HH:mm:ss',
        'D/M/YY HH:mm:ss',
        'DD/MM/YY HH:mm:ss',
        'M-D-YY HH:mm:ss',
        'MM-DD-YY HH:mm:ss',
        'D-M-YY HH:mm:ss',
        'DD-MM-YY HH:mm:ss',

        // 两位数年份格式 时分
        'M/D/YY HH:mm', // 新增: 支持两位数年份，如 1/4/25 14:23
        'MM/DD/YY HH:mm',
        'D/M/YY HH:mm',
        'DD/MM/YY HH:mm',
        'M-D-YY HH:mm',
        'MM-DD-YY HH:mm',
        'D-M-YY HH:mm',
        'DD-MM-YY HH:mm',

        // 年月日
        'YYYY-MM-DD',
        'YYYY/MM/DD',
        'YYYY/M/D', // 新增: 支持 2025/1/1 格式
        'YYYY/M/DD', // 新增: 支持 2025/1/01 格式
        'YYYY/MM/D', // 新增: 支持 2025/01/1 格式
        'MM-DD-YYYY',
        'MM/DD/YYYY',
        'DD-MM-YYYY',
        'DD/MM/YYYY',
        'D/M/YYYY', // 新增: 支持单位数日期/月份
        'DD/M/YYYY', // 新增: 支持单位数月份
        'D/MM/YYYY', // 新增: 支持单位数日期

        // 两位数年份格式 仅日期
        'M/D/YY',
        'MM/DD/YY',
        'D/M/YY',
        'DD/MM/YY',
        'M-D-YY',
        'MM-DD-YY',
        'D-M-YY',
        'DD-MM-YY',

        // ISO格式
        'YYYY-MM-DDTHH:mm:ss',
        'YYYY-MM-DDTHH:mm:ssZ',
        'YYYY-MM-DDTHH:mm:ss.SSSZ',

        // 年月日 时分
        'YYYY-MM-DD HH:mm',
        'YYYY/MM/DD HH:mm',
        'YYYY/M/D HH:mm', // 新增: 支持 2025/1/1 10:21 格式
        'YYYY/M/DD HH:mm', // 新增: 支持 2025/1/01 10:21 格式
        'YYYY/MM/D HH:mm', // 新增: 支持 2025/01/1 10:21 格式
        'YYYY/M/D H:mm', // 新增: 支持 2025/1/2 9:10 格式（单位数小时）
        'YYYY/M/DD H:mm', // 新增: 支持 2025/1/02 9:10 格式
        'YYYY/MM/D H:mm', // 新增: 支持 2025/01/2 9:10 格式
        'MM-DD-YYYY HH:mm',
        'MM/DD/YYYY HH:mm',
        'DD-MM-YYYY HH:mm',
        'DD/MM/YYYY HH:mm',
        'D/M/YYYY HH:mm', // 新增: 支持单位数日期/月份
        'DD/M/YYYY HH:mm', // 新增: 支持单位数月份
        'D/MM/YYYY HH:mm', // 新增: 支持单位数日期
        'D/M/YYYY H:mm', // 新增: 支持单位数小时
        'DD/M/YYYY H:mm', // 新增: 支持单位数小时
        'D/MM/YYYY H:mm', // 新增: 支持单位数小时
      ];

      // 尝试使用指定格式解析
      for (const format of supportedFormats) {
        const momentDate = moment(value, format, true); // strict parsing
        if (
          momentDate.isValid() &&
          momentDate.year() >= 1900 &&
          momentDate.year() <= 2100
        ) {
          return momentDate.toDate();
        }
      }

      // 如果所有指定格式都失败，返回null
      // 不再使用moment.js的自动解析，避免deprecation警告
    }

    return null;
  }

  validateData(data: any[], uploadDto: UploadDataDto): DataValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    let validRows = 0;

    const {
      caseIdField,
      activityField,
      timestampField,
      resourceField,
      costField,
      activityIdField,
      previousActivityField,
      endTimestampField,
      endTimeField,
    } = uploadDto;

    // 检查必需字段是否存在
    if (data.length === 0) {
      errors.push('文件为空或无有效数据');
      return {
        isValid: false,
        errors,
        warnings,
        totalRows: 0,
        validRows: 0,
        preview: [],
      };
    }

    const firstRow = data[0];
    const fields = Object.keys(firstRow);

    if (!fields.includes(caseIdField)) {
      errors.push(`缺少必需字段: ${caseIdField}`);
    }
    if (!fields.includes(activityField)) {
      errors.push(`缺少必需字段: ${activityField}`);
    }
    if (!fields.includes(timestampField)) {
      errors.push(`缺少必需字段: ${timestampField}`);
    }

    if (errors.length > 0) {
      return {
        isValid: false,
        errors,
        warnings,
        totalRows: data.length,
        validRows: 0,
        preview: data.slice(0, 5),
      };
    }

    // 验证每一行数据
    data.forEach((row, index) => {
      const rowErrors: string[] = [];

      // 检查必需字段
      if (!row[caseIdField] || row[caseIdField].toString().trim() === '') {
        rowErrors.push(`第${index + 1}行: ${caseIdField} 不能为空`);
      }
      if (!row[activityField] || row[activityField].toString().trim() === '') {
        rowErrors.push(`第${index + 1}行: ${activityField} 不能为空`);
      }
      if (
        !row[timestampField] ||
        row[timestampField].toString().trim() === ''
      ) {
        rowErrors.push(`第${index + 1}行: ${timestampField} 不能为空`);
      }

      // 验证时间戳格式
      if (row[timestampField]) {
        if (!this.isValidTimestamp(row[timestampField])) {
          rowErrors.push(`第${index + 1}行: ${timestampField} 时间格式无效`);
        }
      }

      // 验证成本字段（如果存在）
      if (costField && row[costField] && isNaN(Number(row[costField]))) {
        warnings.push(`第${index + 1}行: ${costField} 不是有效数字`);
      }

      // 验证活动结束时间字段（如果存在）
      if (endTimestampField && row[endTimestampField]) {
        if (!this.isValidTimestamp(row[endTimestampField])) {
          warnings.push(`第${index + 1}行: ${endTimestampField} 时间格式无效`);
        }
      }

      // 验证结束时间字段（如果存在）
      if (endTimeField && row[endTimeField]) {
        if (!this.isValidTimestamp(row[endTimeField])) {
          warnings.push(`第${index + 1}行: ${endTimeField} 时间格式无效`);
        }
      }

      if (rowErrors.length === 0) {
        validRows++;
      } else {
        errors.push(...rowErrors);
      }
    });

    return {
      isValid: errors.length === 0,
      errors: errors.slice(0, 50), // 限制错误数量
      warnings: warnings.slice(0, 20), // 限制警告数量
      totalRows: data.length,
      validRows,
      preview: data.slice(0, 5),
    };
  }

  async saveEventLogs(
    data: any[],
    uploadDto: UploadDataDto,
  ): Promise<EventLog[]> {
    const {
      processId,
      caseIdField,
      activityField,
      timestampField,
      resourceField,
      costField,
      activityIdField,
      previousActivityField,
      endTimestampField,
      endTimeField,
      parentCaseIdField,
      clearExistingData,
    } = uploadDto;

    console.log(
      `saveEventLogs - processId: ${processId}, clearExistingData: ${clearExistingData}`,
    );

    // 如果需要清空已存在的数据
    if (clearExistingData) {
      const deleteResult = await this.eventLogRepository.delete({ processId });
      console.log(
        `已清空流程 ${processId} 下的所有事件日志数据，删除了 ${deleteResult.affected} 条记录`,
      );
    }

    const eventLogs = data.map((row) => {
      const eventLog = new EventLog();
      eventLog.processId = processId;
      eventLog.caseId = row[caseIdField].toString().trim();
      eventLog.activity = row[activityField].toString().trim();

      // 使用容错的时间转换方法
      const timestamp = this.convertToDate(row[timestampField]);
      if (!timestamp) {
        throw new BadRequestException(`无法解析时间戳: ${row[timestampField]}`);
      }
      eventLog.timestamp = timestamp;

      if (resourceField && row[resourceField]) {
        eventLog.resource = row[resourceField].toString().trim();
      }

      if (costField && row[costField] && !isNaN(Number(row[costField]))) {
        eventLog.cost = Number(row[costField]);
      }

      if (activityIdField && row[activityIdField]) {
        eventLog.activityId = row[activityIdField].toString().trim();
      }

      if (previousActivityField && row[previousActivityField]) {
        eventLog.previousActivity = row[previousActivityField]
          .toString()
          .trim();
      }

      if (endTimestampField && row[endTimestampField]) {
        const endTimestamp = this.convertToDate(row[endTimestampField]);
        if (endTimestamp) {
          eventLog.endTimestamp = endTimestamp;
        }
      }

      if (endTimeField && row[endTimeField]) {
        const endTime = this.convertToDate(row[endTimeField]);
        if (endTime) {
          // 如果没有设置endTimestamp，则使用endTimeField
          if (!eventLog.endTimestamp) {
            eventLog.endTimestamp = endTime;
          }
        }
      }

      // 处理父案例ID字段
      if (parentCaseIdField && row[parentCaseIdField]) {
        eventLog.parentCaseId = row[parentCaseIdField].toString().trim();
      }

      // 保存其他属性到attributes字段
      const attributes: Record<string, any> = {};
      Object.keys(row).forEach((key) => {
        if (
          ![
            caseIdField,
            activityField,
            timestampField,
            resourceField,
            costField,
            activityIdField,
            previousActivityField,
            endTimestampField,
            endTimeField,
            parentCaseIdField,
          ].includes(key)
        ) {
          attributes[key] = row[key];
        }
      });

      if (Object.keys(attributes).length > 0) {
        eventLog.attributes = attributes;
      }

      return eventLog;
    });

    return this.eventLogRepository.save(eventLogs);
  }

  async getDataStatistics(processId: number): Promise<any> {
    const queryBuilder = this.eventLogRepository
      .createQueryBuilder('eventLog')
      .where('eventLog.processId = :processId', { processId });

    const [
      totalEvents,
      uniqueCases,
      uniqueActivities,
      uniqueResources,
      dateRange,
    ] = await Promise.all([
      queryBuilder.getCount(),
      queryBuilder
        .select('COUNT(DISTINCT eventLog.caseId)', 'count')
        .getRawOne(),
      queryBuilder
        .select('COUNT(DISTINCT eventLog.activity)', 'count')
        .getRawOne(),
      queryBuilder
        .select('COUNT(DISTINCT eventLog.resource)', 'count')
        .getRawOne(),
      queryBuilder
        .select('MIN(eventLog.timestamp)', 'minDate')
        .addSelect('MAX(eventLog.timestamp)', 'maxDate')
        .getRawOne(),
    ]);

    return {
      totalEvents,
      uniqueCases: parseInt(uniqueCases.count),
      uniqueActivities: parseInt(uniqueActivities.count),
      uniqueResources: parseInt(uniqueResources.count),
      dateRange: {
        start: dateRange.minDate,
        end: dateRange.maxDate,
      },
    };
  }

  async getDataCountByTimeRange(
    processId: number,
    startTime: string,
    endTime: string,
  ): Promise<{
    totalEvents: number;
    uniqueCases: number;
    uniqueActivities: number;
    uniqueResources: number;
  }> {
    const queryBuilder = this.eventLogRepository
      .createQueryBuilder('eventLog')
      .where('eventLog.processId = :processId', { processId })
      .andWhere('eventLog.timestamp >= :startTime', { startTime })
      .andWhere('eventLog.timestamp <= :endTime', { endTime });

    const [totalEvents, uniqueCases, uniqueActivities, uniqueResources] =
      await Promise.all([
        queryBuilder.getCount(),
        queryBuilder
          .select('COUNT(DISTINCT eventLog.caseId)', 'count')
          .getRawOne(),
        queryBuilder
          .select('COUNT(DISTINCT eventLog.activity)', 'count')
          .getRawOne(),
        queryBuilder
          .select('COUNT(DISTINCT eventLog.resource)', 'count')
          .getRawOne(),
      ]);

    return {
      totalEvents,
      uniqueCases: parseInt(uniqueCases.count),
      uniqueActivities: parseInt(uniqueActivities.count),
      uniqueResources: parseInt(uniqueResources.count),
    };
  }

  /**
   * 流式上传大文件
   */
  async uploadLargeFile(filePath: string, uploadDto: UploadDataDto) {
    const fileType = filePath.split('.').pop()?.toLowerCase();

    if (fileType !== 'csv') {
      throw new BadRequestException('流式上传目前只支持CSV文件');
    }

    // 使用流式处理服务
    const result = await this.dataStreamService.processLargeCSV(
      filePath,
      uploadDto,
    );

    return {
      message: '文件上传成功',
      totalProcessed: result.totalProcessed,
      validRows: result.validRows,
      errors: result.errors.slice(0, 10), // 只返回前10个错误
      warnings: result.warnings.slice(0, 10), // 只返回前10个警告
      preview: result.preview,
    };
  }

  /**
   * 修复父案例ID数据 - 从业务字段迁移到parentCaseId字段
   */
  async fixParentCaseIds(
    processId: number,
    sourceFieldName: string = 'parent_case_id',
  ): Promise<{
    totalRecords: number;
    updatedRecords: number;
    errors: string[];
  }> {
    console.log(
      `🔧 开始修复流程 ${processId} 的父案例ID数据，源字段: ${sourceFieldName}`,
    );

    const errors: string[] = [];
    let updatedRecords = 0;

    try {
      // 查询所有包含指定业务字段的事件日志
      const eventLogs = await this.eventLogRepository
        .createQueryBuilder('eventLog')
        .where('eventLog.processId = :processId', { processId })
        .andWhere('JSON_EXTRACT(eventLog.attributes, :fieldPath) IS NOT NULL', {
          fieldPath: `$.${sourceFieldName}`,
        })
        .getMany();

      console.log(
        `📊 找到 ${eventLogs.length} 条包含 ${sourceFieldName} 字段的记录`,
      );

      // 批量更新
      for (const eventLog of eventLogs) {
        try {
          const parentCaseIdValue = eventLog.attributes?.[sourceFieldName];
          if (parentCaseIdValue !== null && parentCaseIdValue !== undefined) {
            // 转换为字符串
            const parentCaseId = String(parentCaseIdValue).trim();

            if (
              parentCaseId &&
              parentCaseId !== '' &&
              parentCaseId !== 'null'
            ) {
              // 更新parentCaseId字段
              await this.eventLogRepository.update(
                { id: eventLog.id },
                { parentCaseId },
              );
              updatedRecords++;

              if (updatedRecords % 100 === 0) {
                console.log(`✅ 已更新 ${updatedRecords} 条记录...`);
              }
            }
          }
        } catch (error) {
          errors.push(`记录 ${eventLog.id} 更新失败: ${error.message}`);
        }
      }

      console.log(
        `🎉 修复完成！总记录数: ${eventLogs.length}, 更新记录数: ${updatedRecords}`,
      );

      return {
        totalRecords: eventLogs.length,
        updatedRecords,
        errors,
      };
    } catch (error) {
      console.error('❌ 修复父案例ID数据失败:', error);
      throw new Error(`修复父案例ID数据失败: ${error.message}`);
    }
  }
}
