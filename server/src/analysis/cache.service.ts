import { Injectable, Inject, Logger } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import {
  AnalysisType,
  AnalysisResult,
} from '../entities/analysis-result.entity';

export interface CacheOptions {
  ttl?: number; // TTL in seconds
  prefix?: string;
}

export interface AnalysisCacheData {
  resultData: Record<string, any>;
  version: number;
  createdAt: Date;
  expiresAt: Date;
  dataSourceHash?: string | null;
  metadata?: Record<string, any>;
}

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  private readonly defaultTtl = 3600; // 1 hour
  private readonly keyPrefix = 'promined:analysis';

  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  /**
   * 生成缓存键
   */
  generateCacheKey(
    processId: number,
    analysisType: AnalysisType,
    version?: number,
  ): string {
    const versionSuffix = version ? `:v${version}` : '';
    return `${this.keyPrefix}:${processId}:${analysisType}${versionSuffix}`;
  }

  /**
   * 生成数据源哈希键
   */
  generateDataSourceHashKey(processId: number): string {
    return `${this.keyPrefix}:hash:${processId}`;
  }

  /**
   * 设置分析结果缓存
   */
  async setAnalysisResult(
    processId: number,
    analysisType: AnalysisType,
    data: AnalysisCacheData,
    options?: CacheOptions,
  ): Promise<void> {
    try {
      const ttl = options?.ttl || this.defaultTtl;
      const cacheKey = this.generateCacheKey(
        processId,
        analysisType,
        data.version,
      );

      await this.cacheManager.set(cacheKey, data, ttl * 1000); // cache-manager expects milliseconds

      // 同时设置一个不带版本的键，指向最新版本
      const latestKey = this.generateCacheKey(processId, analysisType);
      await this.cacheManager.set(latestKey, data, ttl * 1000);

      this.logger.log(`Cached analysis result: ${cacheKey}, TTL: ${ttl}s`);
    } catch (error) {
      this.logger.error(
        `Failed to cache analysis result: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * 获取分析结果缓存
   */
  async getAnalysisResult(
    processId: number,
    analysisType: AnalysisType,
    version?: number,
  ): Promise<AnalysisCacheData | null> {
    try {
      const cacheKey = this.generateCacheKey(processId, analysisType, version);
      const cached = await this.cacheManager.get<AnalysisCacheData>(cacheKey);

      if (cached) {
        // 检查是否过期
        if (cached.expiresAt && new Date() > new Date(cached.expiresAt)) {
          await this.deleteAnalysisResult(processId, analysisType, version);
          return null;
        }

        this.logger.log(`Cache hit: ${cacheKey}`);
        return cached;
      }

      this.logger.log(`Cache miss: ${cacheKey}`);
      return null;
    } catch (error) {
      this.logger.error(
        `Failed to get cached analysis result: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * 删除分析结果缓存
   */
  async deleteAnalysisResult(
    processId: number,
    analysisType: AnalysisType,
    version?: number,
  ): Promise<void> {
    try {
      const cacheKey = this.generateCacheKey(processId, analysisType, version);
      await this.cacheManager.del(cacheKey);

      // 如果没有指定版本，也删除最新版本的键
      if (!version) {
        const latestKey = this.generateCacheKey(processId, analysisType);
        await this.cacheManager.del(latestKey);
      }

      this.logger.log(`Deleted cache: ${cacheKey}`);
    } catch (error) {
      this.logger.error(
        `Failed to delete cached analysis result: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * 清除指定流程的所有分析结果缓存
   */
  async clearProcessCache(processId: number): Promise<void> {
    try {
      // 获取所有相关的缓存键
      const pattern = `${this.keyPrefix}:${processId}:*`;

      // 注意：这里使用了简化的实现，实际生产环境中可能需要更复杂的键扫描逻辑
      for (const analysisType of Object.values(AnalysisType)) {
        await this.deleteAnalysisResult(processId, analysisType);
      }

      this.logger.log(`Cleared all cache for process: ${processId}`);
    } catch (error) {
      this.logger.error(
        `Failed to clear process cache: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * 设置数据源哈希值
   */
  async setDataSourceHash(processId: number, hash: string): Promise<void> {
    try {
      const key = this.generateDataSourceHashKey(processId);
      await this.cacheManager.set(key, hash, 0); // 永不过期
      this.logger.log(`Set data source hash for process ${processId}: ${hash}`);
    } catch (error) {
      this.logger.error(
        `Failed to set data source hash: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * 获取数据源哈希值
   */
  async getDataSourceHash(processId: number): Promise<string | null> {
    try {
      const key = this.generateDataSourceHashKey(processId);
      const result = await this.cacheManager.get<string>(key);
      return result || null;
    } catch (error) {
      this.logger.error(
        `Failed to get data source hash: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * 检查数据源是否发生变更
   */
  async hasDataSourceChanged(
    processId: number,
    currentHash: string,
  ): Promise<boolean> {
    const cachedHash = await this.getDataSourceHash(processId);
    return cachedHash !== currentHash;
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats(processId: number): Promise<{
    totalCached: number;
    byType: Record<AnalysisType, boolean>;
  }> {
    const stats = {
      totalCached: 0,
      byType: {} as Record<AnalysisType, boolean>,
    };

    for (const analysisType of Object.values(AnalysisType)) {
      const cached = await this.getAnalysisResult(processId, analysisType);
      stats.byType[analysisType] = !!cached;
      if (cached) {
        stats.totalCached++;
      }
    }

    return stats;
  }

  /**
   * 将AnalysisResult实体转换为缓存数据
   */
  entityToCacheData(entity: AnalysisResult): AnalysisCacheData {
    return {
      resultData: entity.resultData,
      version: entity.version,
      createdAt: entity.createdAt,
      expiresAt:
        entity.expiresAt || new Date(Date.now() + entity.cacheTtl * 1000),
      dataSourceHash: entity.dataSourceHash,
      metadata: entity.metadata,
    };
  }

  /**
   * 预热缓存 - 将数据库中的结果加载到缓存
   */
  async warmupCache(entity: AnalysisResult): Promise<void> {
    const cacheData = this.entityToCacheData(entity);
    await this.setAnalysisResult(
      entity.processId,
      entity.analysisType,
      cacheData,
      { ttl: entity.cacheTtl },
    );
  }
}
