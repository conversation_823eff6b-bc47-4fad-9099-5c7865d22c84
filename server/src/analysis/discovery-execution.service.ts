import { Injectable } from '@nestjs/common';
import { extname } from 'path';
import * as fs from 'fs';

import { DataProcessingService } from './data-processing.service';
import { ProcessMiningService } from './process-mining.service';
import { DiscoveryStatusService } from './discovery-status.service';
import { UploadDataDto } from './dto/upload-data.dto';

@Injectable()
export class DiscoveryExecutionService {
  constructor(
    private readonly dataProcessingService: DataProcessingService,
    private readonly processMiningService: ProcessMiningService,
    private readonly discoveryStatusService: DiscoveryStatusService,
  ) {}

  /**
   * 执行文件流程发现
   */
  async executeFileDiscovery(
    processId: number,
    discoveryId: string,
    filePath: string,
    uploadDto: UploadDataDto,
  ): Promise<void> {
    try {
      await this.discoveryStatusService.updatePhase(
        discoveryId,
        'parse',
        10,
        '解析文件...',
      );

      // 解析文件
      const fileType = extname(filePath).substring(1).toLowerCase();
      const data = await this.dataProcessingService.parseFile(
        filePath,
        fileType,
      );

      // 检查取消
      if (await this.discoveryStatusService.isCancelled(discoveryId)) {
        fs.unlinkSync(filePath);
        return;
      }

      await this.discoveryStatusService.updateProgress(
        discoveryId,
        30,
        '验证数据...',
      );

      // 验证数据
      const validation = this.dataProcessingService.validateData(
        data,
        uploadDto,
      );
      if (!validation.isValid) {
        // 清理临时文件
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
        await this.discoveryStatusService.failDiscovery(
          discoveryId,
          '数据验证失败',
          'validate',
          validation.errors,
        );
        return;
      }

      // 检查取消
      if (await this.discoveryStatusService.isCancelled(discoveryId)) {
        fs.unlinkSync(filePath);
        return;
      }

      await this.discoveryStatusService.updatePhase(
        discoveryId,
        'save',
        50,
        '保存数据...',
      );

      const uniqueCases = new Set<string>();
      const uniqueActivities = new Set<string>();
      for (const row of data) {
        const caseVal = row?.[uploadDto.caseIdField as keyof typeof row];
        const actVal = row?.[uploadDto.activityField as keyof typeof row];
        if (
          caseVal !== undefined &&
          caseVal !== null &&
          String(caseVal) !== ''
        ) {
          uniqueCases.add(String(caseVal));
        }
        if (actVal !== undefined && actVal !== null && String(actVal) !== '') {
          uniqueActivities.add(String(actVal));
        }
      }
      await this.discoveryStatusService.mergeResult(discoveryId, {
        statistics: {
          totalRows: Array.isArray(data) ? data.length : 0,
          totalCases: uniqueCases.size,
          totalActivities: uniqueActivities.size,
        },
      });

      const eventLogs = await this.dataProcessingService.saveEventLogs(
        data,
        uploadDto,
      );

      // 清理临时文件
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      // 检查取消
      if (await this.discoveryStatusService.isCancelled(discoveryId)) {
        return;
      }

      await this.discoveryStatusService.updatePhase(
        discoveryId,
        'discover',
        70,
        '流程发现...',
      );

      // 流程发现
      const result = await this.processMiningService.discoverProcess(
        processId,
        {
          forceRefresh: true,
          requiredActivities: [],
        },
      );

      // 完成
      await this.discoveryStatusService.completeDiscovery(discoveryId, {
        ...result,
        savedRecords: eventLogs.length,
      });
    } catch (error) {
      // 清理临时文件
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      await this.discoveryStatusService.failDiscovery(
        discoveryId,
        error.message,
        'discover',
      );
    }
  }

  /**
   * 执行数据库流程发现
   */
  async executeDatabaseDiscovery(
    processId: number,
    discoveryId: string,
    queryData: { columns: string[]; data: any[]; totalRows: number },
    config: UploadDataDto,
  ): Promise<void> {
    try {
      await this.discoveryStatusService.updatePhase(
        discoveryId,
        'validate',
        20,
        '验证数据...',
      );

      // 验证数据
      const validation = this.dataProcessingService.validateData(
        queryData.data,
        config,
      );
      if (!validation.isValid) {
        await this.discoveryStatusService.failDiscovery(
          discoveryId,
          '数据验证失败',
          'validate',
          validation.errors,
        );
        return;
      }

      // 检查取消
      if (await this.discoveryStatusService.isCancelled(discoveryId)) {
        return;
      }

      await this.discoveryStatusService.updatePhase(
        discoveryId,
        'save',
        50,
        '保存数据...',
      );

      // 保存数据
      const eventLogs = await this.dataProcessingService.saveEventLogs(
        queryData.data,
        config,
      );

      const uniqueCases = new Set<string>();
      const uniqueActivities = new Set<string>();
      for (const row of queryData.data) {
        if (row?.caseId) uniqueCases.add(String(row.caseId));
        if (row?.activity) uniqueActivities.add(String(row.activity));
      }
      await this.discoveryStatusService.mergeResult(discoveryId, {
        statistics: {
          totalRows:
            queryData.totalRows ??
            (Array.isArray(queryData.data) ? queryData.data.length : 0),
          totalCases: uniqueCases.size,
          totalActivities: uniqueActivities.size,
        },
      });

      if (await this.discoveryStatusService.isCancelled(discoveryId)) {
        return;
      }

      await this.discoveryStatusService.updatePhase(
        discoveryId,
        'discover',
        70,
        '流程发现...',
      );

      // 流程发现
      const result = await this.processMiningService.discoverProcess(
        processId,
        {
          forceRefresh: false,
          requiredActivities: [],
        },
      );

      // 完成
      await this.discoveryStatusService.completeDiscovery(discoveryId, {
        ...result,
        savedRecords: eventLogs.length,
        validation,
      });
    } catch (error) {
      await this.discoveryStatusService.failDiscovery(
        discoveryId,
        error.message,
        'discover',
      );
    }
  }
}
