import { Test, TestingModule } from '@nestjs/testing';
import { AnalysisController } from '../analysis.controller';
import { ProcessMiningService } from '../process-mining.service';
import { DataProcessingService } from '../data-processing.service';
import { CacheService } from '../cache.service';
import { SubprocessDiscoveryService } from '../subprocess-discovery.service';
import { ProcessCompareOptionsDto } from '../dto/process-compare-options.dto';

describe('AnalysisController - Compare Process', () => {
  let controller: AnalysisController;
  let processMiningService: ProcessMiningService;

  const mockProcessMiningService = {
    compareProcess: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AnalysisController],
      providers: [
        {
          provide: ProcessMiningService,
          useValue: mockProcessMiningService,
        },
        // Mock other required services
        {
          provide: DataProcessingService,
          useValue: {
            uploadData: jest.fn(),
            processData: jest.fn(),
          },
        },
        {
          provide: CacheService,
          useValue: {
            getAnalysisResult: jest.fn(),
            setAnalysisResult: jest.fn(),
          },
        },
        {
          provide: SubprocessDiscoveryService,
          useValue: {
            discoverSubprocesses: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<AnalysisController>(AnalysisController);
    processMiningService =
      module.get<ProcessMiningService>(ProcessMiningService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('compareProcess', () => {
    const mockCompareOptions: ProcessCompareOptionsDto = {
      baseStartTime: '2024-01-01T00:00:00.000Z',
      baseEndTime: '2024-01-07T23:59:59.999Z',
      offsetDays: 7,
      requiredActivities: ['Task A'],
      forceRefresh: false,
      dimension: 'frequency',
      displayMode: 'absolute',
    };

    const mockCompareResult = {
      baseDfg: {
        nodes: [
          { id: 'Start', label: 'Start', frequency: 10 },
          { id: 'Task A', label: 'Task A', frequency: 8 },
          { id: 'End', label: 'End', frequency: 10 },
        ],
        edges: [
          { source: 'Start', target: 'Task A', frequency: 8 },
          { source: 'Task A', target: 'End', frequency: 8 },
        ],
        statistics: {
          totalCases: 10,
          totalActivities: 3,
          avgCaseDuration: 120,
          startActivities: ['Start'],
          endActivities: ['End'],
        },
      },
      compareDfg: {
        nodes: [
          { id: 'Start', label: 'Start', frequency: 12 },
          { id: 'Task A', label: 'Task A', frequency: 10 },
          { id: 'Task B', label: 'Task B', frequency: 5 },
          { id: 'End', label: 'End', frequency: 12 },
        ],
        edges: [
          { source: 'Start', target: 'Task A', frequency: 10 },
          { source: 'Task A', target: 'Task B', frequency: 5 },
          { source: 'Task A', target: 'End', frequency: 5 },
          { source: 'Task B', target: 'End', frequency: 5 },
        ],
        statistics: {
          totalCases: 12,
          totalActivities: 4,
          avgCaseDuration: 150,
          startActivities: ['Start'],
          endActivities: ['End'],
        },
      },
      compareStatistics: {
        baseTimeRange: {
          start: '2024-01-01T00:00:00.000Z',
          end: '2024-01-07T23:59:59.999Z',
          duration: 604799999,
        },
        compareTimeRange: {
          start: '2023-12-25T00:00:00.000Z',
          end: '2023-12-31T23:59:59.999Z',
          duration: 604799999,
        },
        offsetDays: 7,
        baseCaseCount: 10,
        compareCaseCount: 12,
        baseActivityCount: 3,
        compareActivityCount: 4,
        commonActivities: ['Start', 'Task A', 'End'],
        uniqueBaseActivities: [],
        uniqueCompareActivities: ['Task B'],
        changeMetrics: {
          caseCountChange: 2,
          caseCountChangePercent: 20,
          activityCountChange: 1,
          activityCountChangePercent: 33.33,
          avgDurationChange: 30,
          avgDurationChangePercent: 25,
        },
      },
      timestamp: '2024-01-15T10:00:00.000Z',
      fromCache: false,
    };

    it('should be defined', () => {
      expect(controller).toBeDefined();
    });

    it('should call compareProcess with correct parameters', async () => {
      mockProcessMiningService.compareProcess.mockResolvedValue(
        mockCompareResult,
      );

      const result = await controller.compareProcess('1', mockCompareOptions);

      expect(processMiningService.compareProcess).toHaveBeenCalledWith(
        1,
        mockCompareOptions,
      );
      expect(result).toEqual(mockCompareResult);
    });

    it('should handle string processId correctly', async () => {
      mockProcessMiningService.compareProcess.mockResolvedValue(
        mockCompareResult,
      );

      await controller.compareProcess('123', mockCompareOptions);

      expect(processMiningService.compareProcess).toHaveBeenCalledWith(
        123,
        mockCompareOptions,
      );
    });

    it('should return comparison result with correct structure', async () => {
      mockProcessMiningService.compareProcess.mockResolvedValue(
        mockCompareResult,
      );

      const result = await controller.compareProcess('1', mockCompareOptions);

      expect(result).toHaveProperty('baseDfg');
      expect(result).toHaveProperty('compareDfg');
      expect(result).toHaveProperty('compareStatistics');
      expect(result).toHaveProperty('timestamp');
      expect(result).toHaveProperty('fromCache');

      // Verify DFG structure
      expect(result.baseDfg).toHaveProperty('nodes');
      expect(result.baseDfg).toHaveProperty('edges');
      expect(result.baseDfg).toHaveProperty('statistics');

      expect(result.compareDfg).toHaveProperty('nodes');
      expect(result.compareDfg).toHaveProperty('edges');
      expect(result.compareDfg).toHaveProperty('statistics');

      // Verify comparison statistics structure
      expect(result.compareStatistics).toHaveProperty('baseTimeRange');
      expect(result.compareStatistics).toHaveProperty('compareTimeRange');
      expect(result.compareStatistics).toHaveProperty('offsetDays');
      expect(result.compareStatistics).toHaveProperty('changeMetrics');
    });

    it('should handle minimal options correctly', async () => {
      const minimalOptions: ProcessCompareOptionsDto = {
        baseStartTime: '2024-01-01T00:00:00.000Z',
        baseEndTime: '2024-01-07T23:59:59.999Z',
        offsetDays: 7,
      };

      mockProcessMiningService.compareProcess.mockResolvedValue(
        mockCompareResult,
      );

      await controller.compareProcess('1', minimalOptions);

      expect(processMiningService.compareProcess).toHaveBeenCalledWith(
        1,
        minimalOptions,
      );
    });

    it('should handle options with all fields', async () => {
      const fullOptions: ProcessCompareOptionsDto = {
        baseStartTime: '2024-01-01T00:00:00.000Z',
        baseEndTime: '2024-01-07T23:59:59.999Z',
        offsetDays: 14,
        requiredActivities: ['Task A', 'Task B'],
        forceRefresh: true,
        dimension: 'duration',
        displayMode: 'percentage',
      };

      mockProcessMiningService.compareProcess.mockResolvedValue(
        mockCompareResult,
      );

      await controller.compareProcess('1', fullOptions);

      expect(processMiningService.compareProcess).toHaveBeenCalledWith(
        1,
        fullOptions,
      );
    });

    it('should propagate service errors', async () => {
      const errorMessage = 'Process comparison failed';
      mockProcessMiningService.compareProcess.mockRejectedValue(
        new Error(errorMessage),
      );

      await expect(
        controller.compareProcess('1', mockCompareOptions),
      ).rejects.toThrow(errorMessage);
    });

    it('should handle different offset days values', async () => {
      const testCases = [1, 7, 30, 90, 365];

      for (const offsetDays of testCases) {
        const options = { ...mockCompareOptions, offsetDays };
        mockProcessMiningService.compareProcess.mockResolvedValue(
          mockCompareResult,
        );

        await controller.compareProcess('1', options);

        expect(processMiningService.compareProcess).toHaveBeenCalledWith(
          1,
          options,
        );
      }
    });

    it('should handle different dimensions', async () => {
      const dimensions: Array<'frequency' | 'duration'> = [
        'frequency',
        'duration',
      ];

      for (const dimension of dimensions) {
        const options = { ...mockCompareOptions, dimension };
        mockProcessMiningService.compareProcess.mockResolvedValue(
          mockCompareResult,
        );

        await controller.compareProcess('1', options);

        expect(processMiningService.compareProcess).toHaveBeenCalledWith(
          1,
          options,
        );
      }
    });

    it('should handle different display modes', async () => {
      const displayModes: Array<'absolute' | 'difference' | 'percentage'> = [
        'absolute',
        'difference',
        'percentage',
      ];

      for (const displayMode of displayModes) {
        const options = { ...mockCompareOptions, displayMode };
        mockProcessMiningService.compareProcess.mockResolvedValue(
          mockCompareResult,
        );

        await controller.compareProcess('1', options);

        expect(processMiningService.compareProcess).toHaveBeenCalledWith(
          1,
          options,
        );
      }
    });

    it('should handle empty required activities array', async () => {
      const options = { ...mockCompareOptions, requiredActivities: [] };
      mockProcessMiningService.compareProcess.mockResolvedValue(
        mockCompareResult,
      );

      await controller.compareProcess('1', options);

      expect(processMiningService.compareProcess).toHaveBeenCalledWith(
        1,
        options,
      );
    });

    it('should handle undefined optional fields', async () => {
      const options: ProcessCompareOptionsDto = {
        baseStartTime: '2024-01-01T00:00:00.000Z',
        baseEndTime: '2024-01-07T23:59:59.999Z',
        offsetDays: 7,
        // Optional fields are undefined
      };

      mockProcessMiningService.compareProcess.mockResolvedValue(
        mockCompareResult,
      );

      await controller.compareProcess('1', options);

      expect(processMiningService.compareProcess).toHaveBeenCalledWith(
        1,
        options,
      );
    });
  });
});
