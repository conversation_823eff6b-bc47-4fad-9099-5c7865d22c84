import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProcessMiningService } from '../process-mining.service';
import { CacheService } from '../cache.service';
import { EventLog } from '../../entities/event-log.entity';
import {
  AnalysisResult,
  AnalysisType,
} from '../../entities/analysis-result.entity';

describe('ProcessMiningService - Compare Process', () => {
  let service: ProcessMiningService;
  let eventLogRepository: Repository<EventLog>;
  let analysisResultRepository: Repository<AnalysisResult>;
  let cacheService: CacheService;

  const mockEventLogRepository = {
    find: jest.fn(),
    count: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockAnalysisResultRepository = {
    findOne: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
  };

  const mockCacheService = {
    getAnalysisResult: jest.fn(),
    setAnalysisResult: jest.fn(),
    deleteAnalysisResult: jest.fn(),
    clearProcessCache: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProcessMiningService,
        {
          provide: getRepositoryToken(EventLog),
          useValue: mockEventLogRepository,
        },
        {
          provide: getRepositoryToken(AnalysisResult),
          useValue: mockAnalysisResultRepository,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
      ],
    }).compile();

    service = module.get<ProcessMiningService>(ProcessMiningService);
    eventLogRepository = module.get<Repository<EventLog>>(
      getRepositoryToken(EventLog),
    );
    analysisResultRepository = module.get<Repository<AnalysisResult>>(
      getRepositoryToken(AnalysisResult),
    );
    cacheService = module.get<CacheService>(CacheService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('compareProcess', () => {
    const mockOptions = {
      baseStartTime: '2024-01-01T00:00:00.000Z',
      baseEndTime: '2024-01-07T23:59:59.999Z',
      offsetDays: 7,
      requiredActivities: [],
      forceRefresh: false,
      dimension: 'frequency' as const,
      displayMode: 'absolute' as const,
    };

    const mockBaseEventLogs = [
      {
        id: 1,
        processId: 1,
        caseId: 'case1',
        activity: 'Start',
        timestamp: new Date('2024-01-01T09:00:00'),
        endTimestamp: new Date('2024-01-01T09:30:00'),
      },
      {
        id: 2,
        processId: 1,
        caseId: 'case1',
        activity: 'Task A',
        timestamp: new Date('2024-01-01T10:00:00'),
        endTimestamp: new Date('2024-01-01T11:00:00'),
      },
      {
        id: 3,
        processId: 1,
        caseId: 'case1',
        activity: 'End',
        timestamp: new Date('2024-01-01T11:30:00'),
        endTimestamp: new Date('2024-01-01T12:00:00'),
      },
    ];

    const mockCompareEventLogs = [
      {
        id: 4,
        processId: 1,
        caseId: 'case2',
        activity: 'Start',
        timestamp: new Date('2023-12-25T09:00:00'),
        endTimestamp: new Date('2023-12-25T09:30:00'),
      },
      {
        id: 5,
        processId: 1,
        caseId: 'case2',
        activity: 'Task A',
        timestamp: new Date('2023-12-25T10:00:00'),
        endTimestamp: new Date('2023-12-25T11:00:00'),
      },
      {
        id: 6,
        processId: 1,
        caseId: 'case2',
        activity: 'Task B',
        timestamp: new Date('2023-12-25T11:30:00'),
        endTimestamp: new Date('2023-12-25T12:30:00'),
      },
      {
        id: 7,
        processId: 1,
        caseId: 'case2',
        activity: 'End',
        timestamp: new Date('2023-12-25T13:00:00'),
        endTimestamp: new Date('2023-12-25T13:30:00'),
      },
    ];

    it('should perform process comparison successfully', async () => {
      // Mock repository calls
      mockEventLogRepository.find
        .mockResolvedValueOnce(mockBaseEventLogs) // Base time range
        .mockResolvedValueOnce(mockCompareEventLogs); // Compare time range

      mockCacheService.getAnalysisResult.mockResolvedValue(null);
      mockCacheService.setAnalysisResult.mockResolvedValue(undefined);

      const result = await service.compareProcess(1, mockOptions);

      expect(result).toBeDefined();
      expect(result.baseDfg).toBeDefined();
      expect(result.compareDfg).toBeDefined();
      expect(result.compareStatistics).toBeDefined();
      expect(result.timestamp).toBeDefined();
      expect(result.fromCache).toBe(false);
    });

    it('should return cached result when available and not forcing refresh', async () => {
      // Generate the expected cache key based on the mock options
      const crypto = require('crypto');
      const keyData = {
        processId: 1,
        baseStartTime: mockOptions.baseStartTime,
        baseEndTime: mockOptions.baseEndTime,
        offsetDays: mockOptions.offsetDays,
        requiredActivities: (mockOptions.requiredActivities || []).sort(),
        dimension: mockOptions.dimension || 'frequency',
        displayMode: mockOptions.displayMode || 'absolute',
      };
      const expectedCacheKey = crypto
        .createHash('md5')
        .update(JSON.stringify(keyData))
        .digest('hex');

      const cachedResult = {
        resultData: {
          baseDfg: { nodes: [], edges: [] },
          compareDfg: { nodes: [], edges: [] },
          compareStatistics: {},
          cacheKey: expectedCacheKey,
        },
      };

      mockCacheService.getAnalysisResult.mockResolvedValue(cachedResult);

      const result = await service.compareProcess(1, {
        ...mockOptions,
        forceRefresh: false,
      });

      expect(result.fromCache).toBe(true);
      expect(mockEventLogRepository.find).not.toHaveBeenCalled();
    });

    it('should calculate time ranges correctly', async () => {
      mockEventLogRepository.find
        .mockResolvedValueOnce(mockBaseEventLogs)
        .mockResolvedValueOnce(mockCompareEventLogs);

      mockCacheService.getAnalysisResult.mockResolvedValue(null);
      mockCacheService.setAnalysisResult.mockResolvedValue(undefined);

      const result = await service.compareProcess(1, mockOptions);

      const { compareStatistics } = result;

      expect(compareStatistics.baseTimeRange.start).toBe(
        mockOptions.baseStartTime,
      );
      expect(compareStatistics.baseTimeRange.end).toBe(mockOptions.baseEndTime);
      expect(compareStatistics.offsetDays).toBe(mockOptions.offsetDays);

      // Verify compare time range is offset correctly
      const expectedCompareStart = new Date('2023-12-25T00:00:00.000Z');
      const expectedCompareEnd = new Date('2023-12-31T23:59:59.999Z');

      expect(new Date(compareStatistics.compareTimeRange.start)).toEqual(
        expectedCompareStart,
      );
      expect(new Date(compareStatistics.compareTimeRange.end)).toEqual(
        expectedCompareEnd,
      );
    });

    it('should calculate comparison statistics correctly', async () => {
      mockEventLogRepository.find
        .mockResolvedValueOnce(mockBaseEventLogs)
        .mockResolvedValueOnce(mockCompareEventLogs);

      mockCacheService.getAnalysisResult.mockResolvedValue(null);
      mockCacheService.setAnalysisResult.mockResolvedValue(undefined);

      const result = await service.compareProcess(1, mockOptions);

      const { compareStatistics } = result;

      expect(compareStatistics.baseCaseCount).toBe(1);
      expect(compareStatistics.compareCaseCount).toBe(1);
      expect(compareStatistics.changeMetrics.caseCountChange).toBe(0);
      expect(compareStatistics.changeMetrics.caseCountChangePercent).toBe(0);

      // Check for common and unique activities
      expect(compareStatistics.commonActivities).toContain('Start');
      expect(compareStatistics.commonActivities).toContain('Task A');
      expect(compareStatistics.commonActivities).toContain('End');
      expect(compareStatistics.uniqueCompareActivities).toContain('Task B');
    });

    it('should handle empty event logs gracefully', async () => {
      mockEventLogRepository.find
        .mockResolvedValueOnce([]) // Empty base events
        .mockResolvedValueOnce([]); // Empty compare events

      mockCacheService.getAnalysisResult.mockResolvedValue(null);
      mockCacheService.setAnalysisResult.mockResolvedValue(undefined);

      const result = await service.compareProcess(1, mockOptions);

      expect(result.baseDfg.nodes).toEqual([]);
      expect(result.baseDfg.edges).toEqual([]);
      expect(result.compareDfg.nodes).toEqual([]);
      expect(result.compareDfg.edges).toEqual([]);
      expect(result.compareStatistics.baseCaseCount).toBe(0);
      expect(result.compareStatistics.compareCaseCount).toBe(0);
    });

    it('should apply required activities filter', async () => {
      const optionsWithFilter = {
        ...mockOptions,
        requiredActivities: ['Task A'],
      };

      mockEventLogRepository.find
        .mockResolvedValueOnce(mockBaseEventLogs)
        .mockResolvedValueOnce(mockCompareEventLogs);

      mockCacheService.getAnalysisResult.mockResolvedValue(null);
      mockCacheService.setAnalysisResult.mockResolvedValue(undefined);

      const result = await service.compareProcess(1, optionsWithFilter);

      expect(result).toBeDefined();
      // The filtering logic should be applied in getEventLogsByTimeRange
      expect(mockEventLogRepository.find).toHaveBeenCalledTimes(2);
    });

    it('should not apply filter when requiredActivities contains ALL_NODES', async () => {
      const optionsWithAllNodes = {
        ...mockOptions,
        requiredActivities: ['ALL_NODES'],
      };

      mockEventLogRepository.find
        .mockResolvedValueOnce(mockBaseEventLogs)
        .mockResolvedValueOnce(mockCompareEventLogs);

      mockCacheService.getAnalysisResult.mockResolvedValue(null);
      mockCacheService.setAnalysisResult.mockResolvedValue(undefined);

      const result = await service.compareProcess(1, optionsWithAllNodes);

      expect(result).toBeDefined();
      // Should behave the same as no filter
      expect(mockEventLogRepository.find).toHaveBeenCalledTimes(2);
      expect(result.baseDfg).toBeDefined();
      expect(result.compareDfg).toBeDefined();
    });

    it('should generate cache key correctly', async () => {
      mockEventLogRepository.find
        .mockResolvedValueOnce(mockBaseEventLogs)
        .mockResolvedValueOnce(mockCompareEventLogs);

      mockCacheService.getAnalysisResult.mockResolvedValue(null);
      mockCacheService.setAnalysisResult.mockResolvedValue(undefined);

      const result = await service.compareProcess(1, mockOptions);

      expect(result.cacheKey).toBeDefined();
      expect(typeof result.cacheKey).toBe('string');
      expect(result.cacheKey.length).toBe(32); // MD5 hash length
    });

    it('should handle errors gracefully', async () => {
      mockEventLogRepository.find.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(service.compareProcess(1, mockOptions)).rejects.toThrow(
        '流程比对分析失败: Database error',
      );
    });

    it('should cache results after successful analysis', async () => {
      mockEventLogRepository.find
        .mockResolvedValueOnce(mockBaseEventLogs)
        .mockResolvedValueOnce(mockCompareEventLogs);

      mockCacheService.getAnalysisResult.mockResolvedValue(null);
      mockCacheService.setAnalysisResult.mockResolvedValue(undefined);

      await service.compareProcess(1, mockOptions);

      expect(mockCacheService.setAnalysisResult).toHaveBeenCalledWith(
        1,
        AnalysisType.PROCESS_DISCOVERY,
        expect.objectContaining({
          resultData: expect.any(Object),
          version: 1,
          createdAt: expect.any(Date),
          expiresAt: expect.any(Date),
        }),
      );
    });

    it('should force refresh when requested', async () => {
      const cachedResult = {
        resultData: {
          baseDfg: { nodes: [], edges: [] },
          compareDfg: { nodes: [], edges: [] },
          compareStatistics: {},
          cacheKey: 'test-cache-key',
        },
      };

      mockCacheService.getAnalysisResult.mockResolvedValue(cachedResult);
      mockEventLogRepository.find
        .mockResolvedValueOnce(mockBaseEventLogs)
        .mockResolvedValueOnce(mockCompareEventLogs);

      const result = await service.compareProcess(1, {
        ...mockOptions,
        forceRefresh: true,
      });

      expect(result.fromCache).toBe(false);
      expect(mockEventLogRepository.find).toHaveBeenCalledTimes(2);
    });
  });

  describe('generateComparisonCacheKey', () => {
    it('should generate consistent cache keys for same parameters', () => {
      const params = {
        processId: 1,
        baseStartTime: '2024-01-01T00:00:00.000Z',
        baseEndTime: '2024-01-07T23:59:59.999Z',
        offsetDays: 7,
        requiredActivities: ['Task A', 'Task B'],
        dimension: 'frequency',
        displayMode: 'absolute',
      };

      const key1 = (service as any).generateComparisonCacheKey(
        params.processId,
        params.baseStartTime,
        params.baseEndTime,
        params.offsetDays,
        params.requiredActivities,
        params.dimension,
        params.displayMode,
      );

      const key2 = (service as any).generateComparisonCacheKey(
        params.processId,
        params.baseStartTime,
        params.baseEndTime,
        params.offsetDays,
        params.requiredActivities,
        params.dimension,
        params.displayMode,
      );

      expect(key1).toBe(key2);
      expect(key1).toHaveLength(32); // MD5 hash length
    });

    it('should generate different cache keys for different parameters', () => {
      const baseParams = {
        processId: 1,
        baseStartTime: '2024-01-01T00:00:00.000Z',
        baseEndTime: '2024-01-07T23:59:59.999Z',
        offsetDays: 7,
        requiredActivities: ['Task A'],
        dimension: 'frequency',
        displayMode: 'absolute',
      };

      const key1 = (service as any).generateComparisonCacheKey(
        baseParams.processId,
        baseParams.baseStartTime,
        baseParams.baseEndTime,
        baseParams.offsetDays,
        baseParams.requiredActivities,
        baseParams.dimension,
        baseParams.displayMode,
      );

      // Different offset days
      const key2 = (service as any).generateComparisonCacheKey(
        baseParams.processId,
        baseParams.baseStartTime,
        baseParams.baseEndTime,
        14, // Different offset
        baseParams.requiredActivities,
        baseParams.dimension,
        baseParams.displayMode,
      );

      expect(key1).not.toBe(key2);
    });
  });
});
