import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CacheModule } from '@nestjs/cache-manager';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { ProcessesModule } from './processes/processes.module';
import { AnalysisModule } from './analysis/analysis.module';
import { ConformanceModule } from './conformance/conformance.module';
import { DatabaseConnectionsModule } from './database-connections/database-connections.module';
import { MemoryMonitorMiddleware } from './common/memory-monitor.middleware';
import { MySQLOptimizationService } from './config/mysql-optimization.service';
import redisConfig from './config/redis.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
      load: [redisConfig],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get('DB_PORT', 3306),
        username: configService.get('DB_USERNAME', 'root'),
        password: configService.get('DB_PASSWORD', 'password'),
        database: configService.get('DB_DATABASE', 'promined'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        migrations: [__dirname + '/database/migrations/*{.ts,.js}'],
        synchronize: false, // 禁用自动同步，使用migration管理数据库变更
        logging: configService.get('NODE_ENV') === 'development',
        charset: 'utf8mb4',
        timezone: '+08:00',
      }),
      inject: [ConfigService],
    }),
    CacheModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        store: 'ioredis',
        host: configService.get('REDIS_HOST', 'localhost'),
        port: configService.get('REDIS_PORT', 6379),
        password: configService.get('REDIS_PASSWORD', ''),
        keyPrefix: configService.get('REDIS_KEY_PREFIX', 'promined:'),
        ttl: configService.get('REDIS_DEFAULT_EXPIRE', 600),
      }),
      inject: [ConfigService],
      isGlobal: true,
    }),
    AuthModule,
    UsersModule,
    ProcessesModule,
    AnalysisModule,
    ConformanceModule,
    DatabaseConnectionsModule,
  ],
  controllers: [AppController],
  providers: [AppService, MySQLOptimizationService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(MemoryMonitorMiddleware).forRoutes('*'); // 监控所有路由
  }
}
