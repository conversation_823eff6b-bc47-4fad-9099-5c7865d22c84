# 应用配置
NODE_ENV=development
PORT=3003

# 数据库配置 (MySQL)
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=promined

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_KEY_PREFIX=promined:
REDIS_DEFAULT_EXPIRE=600

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# 文件上传配置
UPLOAD_MAX_SIZE=100MB
UPLOAD_ALLOWED_TYPES=csv,xlsx,xls,json

# API配置
API_PREFIX=api
API_VERSION=v1

# CORS配置
CORS_ORIGIN=http://localhost:3001
CORS_CREDENTIALS=true

# Python Mining Service配置
PYTHON_MINING_SERVICE_URL=http://localhost:8001
