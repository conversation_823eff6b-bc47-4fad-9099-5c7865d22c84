{"/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/app.controller.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/app.controller.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 49}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 43}}, "2": {"start": {"line": 5, "column": 7}, "end": {"line": 12, "column": null}}, "3": {"start": {"line": 6, "column": 31}, "end": {"line": 6, "column": 43}}, "4": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 38}}, "5": {"start": {"line": 5, "column": 13}, "end": {"line": 5, "column": 26}}, "6": {"start": {"line": 9, "column": 2}, "end": {"line": 11, "column": null}}, "7": {"start": {"line": 5, "column": 13}, "end": {"line": 12, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": 31}}, "loc": {"start": {"line": 6, "column": 53}, "end": {"line": 6, "column": 57}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 10}}, "loc": {"start": {"line": 9, "column": 10}, "end": {"line": 11, "column": 3}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1}, "f": {"0": 1, "1": 1}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/app.module.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/app.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 61}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 48}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 52}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 49}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 43}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 48}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 51}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 63}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 60}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 69}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 48}}, "12": {"start": {"line": 61, "column": 7}, "end": {"line": 61, "column": null}}, "13": {"start": {"line": 61, "column": 13}, "end": {"line": 61, "column": 22}}, "14": {"start": {"line": 61, "column": 13}, "end": {"line": 61, "column": null}}, "15": {"start": {"line": 23, "column": 53}, "end": {"line": 36, "column": 8}}, "16": {"start": {"line": 41, "column": 53}, "end": {"line": 48, "column": 8}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 23, "column": 18}, "end": {"line": 23, "column": 19}}, "loc": {"start": {"line": 23, "column": 53}, "end": {"line": 36, "column": 8}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 41, "column": 18}, "end": {"line": 41, "column": 19}}, "loc": {"start": {"line": 41, "column": 53}, "end": {"line": 48, "column": 8}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/app.service.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/app.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "1": {"start": {"line": 4, "column": 7}, "end": {"line": 8, "column": null}}, "2": {"start": {"line": 6, "column": 4}, "end": {"line": 6, "column": 26}}, "3": {"start": {"line": 4, "column": 13}, "end": {"line": 4, "column": 23}}, "4": {"start": {"line": 4, "column": 13}, "end": {"line": 8, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": 10}}, "loc": {"start": {"line": 5, "column": 10}, "end": {"line": 7, "column": 3}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1}, "f": {"0": 1}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/main.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/main.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 43}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 48}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 65}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 41}}, "4": {"start": {"line": 7, "column": 14}, "end": {"line": 7, "column": 49}}, "5": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": 32}}, "6": {"start": {"line": 13, "column": 2}, "end": {"line": 24, "column": 5}}, "7": {"start": {"line": 27, "column": 2}, "end": {"line": 33, "column": 4}}, "8": {"start": {"line": 36, "column": 17}, "end": {"line": 43, "column": 12}}, "9": {"start": {"line": 45, "column": 19}, "end": {"line": 45, "column": 60}}, "10": {"start": {"line": 46, "column": 2}, "end": {"line": 50, "column": 5}}, "11": {"start": {"line": 52, "column": 15}, "end": {"line": 52, "column": 39}}, "12": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": 25}}, "13": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": 63}}, "14": {"start": {"line": 56, "column": 2}, "end": {"line": 56, "column": 64}}, "15": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": 62}}, "16": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 12}}}, "fnMap": {"0": {"name": "bootstrap", "decl": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 24}}, "loc": {"start": {"line": 6, "column": 24}, "end": {"line": 58, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 6}, "end": {"line": 22, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 16, "column": 10}, "end": {"line": 16, "column": 37}}, {"start": {"line": 17, "column": 10}, "end": {"line": 22, "column": null}}]}, "1": {"loc": {"start": {"line": 52, "column": 15}, "end": {"line": 52, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 52, "column": 15}, "end": {"line": 52, "column": 31}}, {"start": {"line": 52, "column": 35}, "end": {"line": 52, "column": 39}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/analysis.controller.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/analysis.controller.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 59}}, "2": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": null}}, "3": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 37}}, "4": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 31}}, "5": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 25}}, "6": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 66}}, "7": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 64}}, "8": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 47}}, "9": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 104}}, "10": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 69}}, "11": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 61}}, "12": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 66}}, "13": {"start": {"line": 40, "column": 7}, "end": {"line": 419, "column": null}}, "14": {"start": {"line": 42, "column": 21}, "end": {"line": 42, "column": 44}}, "15": {"start": {"line": 43, "column": 21}, "end": {"line": 43, "column": 43}}, "16": {"start": {"line": 44, "column": 21}, "end": {"line": 44, "column": 35}}, "17": {"start": {"line": 45, "column": 21}, "end": {"line": 45, "column": 49}}, "18": {"start": {"line": 86, "column": 4}, "end": {"line": 88, "column": 5}}, "19": {"start": {"line": 87, "column": 6}, "end": {"line": 87, "column": 49}}, "20": {"start": {"line": 90, "column": 4}, "end": {"line": 134, "column": 5}}, "21": {"start": {"line": 92, "column": 23}, "end": {"line": 92, "column": 76}}, "22": {"start": {"line": 93, "column": 19}, "end": {"line": 95, "column": null}}, "23": {"start": {"line": 99, "column": 25}, "end": {"line": 101, "column": null}}, "24": {"start": {"line": 104, "column": 6}, "end": {"line": 111, "column": 7}}, "25": {"start": {"line": 106, "column": 8}, "end": {"line": 106, "column": 33}}, "26": {"start": {"line": 107, "column": 8}, "end": {"line": 110, "column": 10}}, "27": {"start": {"line": 114, "column": 24}, "end": {"line": 116, "column": null}}, "28": {"start": {"line": 120, "column": 6}, "end": {"line": 120, "column": 31}}, "29": {"start": {"line": 122, "column": 6}, "end": {"line": 127, "column": 8}}, "30": {"start": {"line": 130, "column": 6}, "end": {"line": 132, "column": 7}}, "31": {"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": 33}}, "32": {"start": {"line": 133, "column": 6}, "end": {"line": 133, "column": 64}}, "33": {"start": {"line": 173, "column": 4}, "end": {"line": 175, "column": 5}}, "34": {"start": {"line": 174, "column": 6}, "end": {"line": 174, "column": 49}}, "35": {"start": {"line": 177, "column": 4}, "end": {"line": 205, "column": 5}}, "36": {"start": {"line": 179, "column": 23}, "end": {"line": 179, "column": 76}}, "37": {"start": {"line": 180, "column": 19}, "end": {"line": 182, "column": null}}, "38": {"start": {"line": 186, "column": 25}, "end": {"line": 188, "column": null}}, "39": {"start": {"line": 192, "column": 6}, "end": {"line": 192, "column": 31}}, "40": {"start": {"line": 194, "column": 6}, "end": {"line": 198, "column": 8}}, "41": {"start": {"line": 201, "column": 6}, "end": {"line": 203, "column": 7}}, "42": {"start": {"line": 202, "column": 8}, "end": {"line": 202, "column": 33}}, "43": {"start": {"line": 204, "column": 6}, "end": {"line": 204, "column": 64}}, "44": {"start": {"line": 258, "column": 4}, "end": {"line": 260, "column": 5}}, "45": {"start": {"line": 259, "column": 6}, "end": {"line": 259, "column": 49}}, "46": {"start": {"line": 262, "column": 4}, "end": {"line": 278, "column": 5}}, "47": {"start": {"line": 263, "column": 21}, "end": {"line": 265, "column": null}}, "48": {"start": {"line": 269, "column": 6}, "end": {"line": 269, "column": 31}}, "49": {"start": {"line": 271, "column": 6}, "end": {"line": 271, "column": 20}}, "50": {"start": {"line": 274, "column": 6}, "end": {"line": 276, "column": 7}}, "51": {"start": {"line": 275, "column": 8}, "end": {"line": 275, "column": 33}}, "52": {"start": {"line": 277, "column": 6}, "end": {"line": 277, "column": 18}}, "53": {"start": {"line": 285, "column": 4}, "end": {"line": 285, "column": 68}}, "54": {"start": {"line": 301, "column": 4}, "end": {"line": 304, "column": 6}}, "55": {"start": {"line": 311, "column": 4}, "end": {"line": 311, "column": 65}}, "56": {"start": {"line": 318, "column": 4}, "end": {"line": 318, "column": 68}}, "57": {"start": {"line": 328, "column": 19}, "end": {"line": 330, "column": null}}, "58": {"start": {"line": 334, "column": 4}, "end": {"line": 334, "column": 45}}, "59": {"start": {"line": 341, "column": 4}, "end": {"line": 341, "column": 55}}, "60": {"start": {"line": 348, "column": 4}, "end": {"line": 348, "column": 58}}, "61": {"start": {"line": 349, "column": 4}, "end": {"line": 349, "column": 32}}, "62": {"start": {"line": 359, "column": 4}, "end": {"line": 359, "column": 75}}, "63": {"start": {"line": 360, "column": 4}, "end": {"line": 360, "column": 32}}, "64": {"start": {"line": 367, "column": 17}, "end": {"line": 367, "column": 70}}, "65": {"start": {"line": 368, "column": 4}, "end": {"line": 368, "column": 43}}, "66": {"start": {"line": 378, "column": 4}, "end": {"line": 381, "column": 6}}, "67": {"start": {"line": 391, "column": 19}, "end": {"line": 393, "column": null}}, "68": {"start": {"line": 395, "column": 4}, "end": {"line": 398, "column": 6}}, "69": {"start": {"line": 405, "column": 19}, "end": {"line": 406, "column": null}}, "70": {"start": {"line": 408, "column": 4}, "end": {"line": 417, "column": 6}}, "71": {"start": {"line": 413, "column": 10}, "end": {"line": 413, "column": 59}}, "72": {"start": {"line": 414, "column": 10}, "end": {"line": 414, "column": 21}}, "73": {"start": {"line": 40, "column": 13}, "end": {"line": 40, "column": 31}}, "74": {"start": {"line": 81, "column": 8}, "end": {"line": 135, "column": null}}, "75": {"start": {"line": 58, "column": 29}, "end": {"line": 61, "column": 21}}, "76": {"start": {"line": 60, "column": 23}, "end": {"line": 60, "column": 66}}, "77": {"start": {"line": 62, "column": 10}, "end": {"line": 62, "column": 65}}, "78": {"start": {"line": 66, "column": 8}, "end": {"line": 74, "column": 9}}, "79": {"start": {"line": 71, "column": 10}, "end": {"line": 71, "column": 25}}, "80": {"start": {"line": 73, "column": 10}, "end": {"line": 73, "column": 67}}, "81": {"start": {"line": 169, "column": 8}, "end": {"line": 206, "column": null}}, "82": {"start": {"line": 146, "column": 29}, "end": {"line": 149, "column": 21}}, "83": {"start": {"line": 148, "column": 23}, "end": {"line": 148, "column": 66}}, "84": {"start": {"line": 150, "column": 10}, "end": {"line": 150, "column": 65}}, "85": {"start": {"line": 154, "column": 8}, "end": {"line": 162, "column": 9}}, "86": {"start": {"line": 159, "column": 10}, "end": {"line": 159, "column": 25}}, "87": {"start": {"line": 161, "column": 10}, "end": {"line": 161, "column": 67}}, "88": {"start": {"line": 254, "column": 8}, "end": {"line": 279, "column": null}}, "89": {"start": {"line": 214, "column": 29}, "end": {"line": 217, "column": 21}}, "90": {"start": {"line": 216, "column": 23}, "end": {"line": 216, "column": 66}}, "91": {"start": {"line": 218, "column": 10}, "end": {"line": 218, "column": 65}}, "92": {"start": {"line": 222, "column": 8}, "end": {"line": 226, "column": 9}}, "93": {"start": {"line": 223, "column": 10}, "end": {"line": 223, "column": 25}}, "94": {"start": {"line": 225, "column": 10}, "end": {"line": 225, "column": 64}}, "95": {"start": {"line": 284, "column": 8}, "end": {"line": 286, "column": null}}, "96": {"start": {"line": 297, "column": 8}, "end": {"line": 305, "column": null}}, "97": {"start": {"line": 310, "column": 8}, "end": {"line": 312, "column": null}}, "98": {"start": {"line": 317, "column": 8}, "end": {"line": 319, "column": null}}, "99": {"start": {"line": 324, "column": 8}, "end": {"line": 335, "column": null}}, "100": {"start": {"line": 340, "column": 8}, "end": {"line": 342, "column": null}}, "101": {"start": {"line": 347, "column": 8}, "end": {"line": 350, "column": null}}, "102": {"start": {"line": 355, "column": 8}, "end": {"line": 361, "column": null}}, "103": {"start": {"line": 366, "column": 8}, "end": {"line": 369, "column": null}}, "104": {"start": {"line": 374, "column": 8}, "end": {"line": 382, "column": null}}, "105": {"start": {"line": 387, "column": 8}, "end": {"line": 399, "column": null}}, "106": {"start": {"line": 404, "column": 8}, "end": {"line": 418, "column": null}}, "107": {"start": {"line": 40, "column": 13}, "end": {"line": 419, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": null}}, "loc": {"start": {"line": 45, "column": 75}, "end": {"line": 46, "column": 6}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 7}}, "loc": {"start": {"line": 84, "column": 18}, "end": {"line": 135, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 169, "column": 2}, "end": {"line": 169, "column": 7}}, "loc": {"start": {"line": 171, "column": 36}, "end": {"line": 206, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 254, "column": 2}, "end": {"line": 254, "column": 7}}, "loc": {"start": {"line": 256, "column": 36}, "end": {"line": 279, "column": 3}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 284, "column": 2}, "end": {"line": 284, "column": 7}}, "loc": {"start": {"line": 284, "column": 59}, "end": {"line": 286, "column": 3}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 297, "column": 2}, "end": {"line": 297, "column": 7}}, "loc": {"start": {"line": 299, "column": 49}, "end": {"line": 305, "column": 3}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 310, "column": 2}, "end": {"line": 310, "column": 7}}, "loc": {"start": {"line": 310, "column": 61}, "end": {"line": 312, "column": 3}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 317, "column": 2}, "end": {"line": 317, "column": 7}}, "loc": {"start": {"line": 317, "column": 64}, "end": {"line": 319, "column": 3}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 324, "column": 2}, "end": {"line": 324, "column": 7}}, "loc": {"start": {"line": 326, "column": 53}, "end": {"line": 335, "column": 3}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 340, "column": 2}, "end": {"line": 340, "column": 7}}, "loc": {"start": {"line": 340, "column": 60}, "end": {"line": 342, "column": 3}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 347, "column": 2}, "end": {"line": 347, "column": 7}}, "loc": {"start": {"line": 347, "column": 63}, "end": {"line": 350, "column": 3}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 355, "column": 2}, "end": {"line": 355, "column": 7}}, "loc": {"start": {"line": 357, "column": 53}, "end": {"line": 361, "column": 3}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 366, "column": 2}, "end": {"line": 366, "column": 7}}, "loc": {"start": {"line": 366, "column": 63}, "end": {"line": 369, "column": 3}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 374, "column": 2}, "end": {"line": 374, "column": 7}}, "loc": {"start": {"line": 376, "column": 51}, "end": {"line": 382, "column": 3}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 387, "column": 2}, "end": {"line": 387, "column": 7}}, "loc": {"start": {"line": 389, "column": 51}, "end": {"line": 399, "column": 3}}}, "15": {"name": "(anonymous_19)", "decl": {"start": {"line": 404, "column": 2}, "end": {"line": 404, "column": 7}}, "loc": {"start": {"line": 404, "column": 67}, "end": {"line": 418, "column": 3}}}, "16": {"name": "(anonymous_20)", "decl": {"start": {"line": 412, "column": 43}, "end": {"line": 412, "column": 44}}, "loc": {"start": {"line": 412, "column": 60}, "end": {"line": 415, "column": 9}}}, "17": {"name": "(anonymous_21)", "decl": {"start": {"line": 57, "column": 18}, "end": {"line": 57, "column": 19}}, "loc": {"start": {"line": 57, "column": 36}, "end": {"line": 63, "column": 9}}}, "18": {"name": "(anonymous_22)", "decl": {"start": {"line": 60, "column": 17}, "end": {"line": 60, "column": 20}}, "loc": {"start": {"line": 60, "column": 23}, "end": {"line": 60, "column": 66}}}, "19": {"name": "(anonymous_23)", "decl": {"start": {"line": 65, "column": 18}, "end": {"line": 65, "column": 19}}, "loc": {"start": {"line": 65, "column": 36}, "end": {"line": 75, "column": 7}}}, "20": {"name": "(anonymous_24)", "decl": {"start": {"line": 145, "column": 18}, "end": {"line": 145, "column": 19}}, "loc": {"start": {"line": 145, "column": 36}, "end": {"line": 151, "column": 9}}}, "21": {"name": "(anonymous_25)", "decl": {"start": {"line": 148, "column": 17}, "end": {"line": 148, "column": 20}}, "loc": {"start": {"line": 148, "column": 23}, "end": {"line": 148, "column": 66}}}, "22": {"name": "(anonymous_26)", "decl": {"start": {"line": 153, "column": 18}, "end": {"line": 153, "column": 19}}, "loc": {"start": {"line": 153, "column": 36}, "end": {"line": 163, "column": 7}}}, "23": {"name": "(anonymous_27)", "decl": {"start": {"line": 213, "column": 18}, "end": {"line": 213, "column": 19}}, "loc": {"start": {"line": 213, "column": 36}, "end": {"line": 219, "column": 9}}}, "24": {"name": "(anonymous_28)", "decl": {"start": {"line": 216, "column": 17}, "end": {"line": 216, "column": 20}}, "loc": {"start": {"line": 216, "column": 23}, "end": {"line": 216, "column": 66}}}, "25": {"name": "(anonymous_29)", "decl": {"start": {"line": 221, "column": 18}, "end": {"line": 221, "column": 19}}, "loc": {"start": {"line": 221, "column": 36}, "end": {"line": 227, "column": 7}}}}, "branchMap": {"0": {"loc": {"start": {"line": 86, "column": 4}, "end": {"line": 88, "column": 5}}, "type": "if", "locations": [{"start": {"line": 86, "column": 4}, "end": {"line": 88, "column": 5}}]}, "1": {"loc": {"start": {"line": 104, "column": 6}, "end": {"line": 111, "column": 7}}, "type": "if", "locations": [{"start": {"line": 104, "column": 6}, "end": {"line": 111, "column": 7}}]}, "2": {"loc": {"start": {"line": 130, "column": 6}, "end": {"line": 132, "column": 7}}, "type": "if", "locations": [{"start": {"line": 130, "column": 6}, "end": {"line": 132, "column": 7}}]}, "3": {"loc": {"start": {"line": 173, "column": 4}, "end": {"line": 175, "column": 5}}, "type": "if", "locations": [{"start": {"line": 173, "column": 4}, "end": {"line": 175, "column": 5}}]}, "4": {"loc": {"start": {"line": 201, "column": 6}, "end": {"line": 203, "column": 7}}, "type": "if", "locations": [{"start": {"line": 201, "column": 6}, "end": {"line": 203, "column": 7}}]}, "5": {"loc": {"start": {"line": 258, "column": 4}, "end": {"line": 260, "column": 5}}, "type": "if", "locations": [{"start": {"line": 258, "column": 4}, "end": {"line": 260, "column": 5}}]}, "6": {"loc": {"start": {"line": 274, "column": 6}, "end": {"line": 276, "column": 7}}, "type": "if", "locations": [{"start": {"line": 274, "column": 6}, "end": {"line": 276, "column": 7}}]}, "7": {"loc": {"start": {"line": 303, "column": 6}, "end": {"line": 303, "column": 27}}, "type": "binary-expr", "locations": [{"start": {"line": 303, "column": 6}, "end": {"line": 303, "column": 18}}, {"start": {"line": 303, "column": 22}, "end": {"line": 303, "column": 27}}]}, "8": {"loc": {"start": {"line": 334, "column": 11}, "end": {"line": 334, "column": 44}}, "type": "cond-expr", "locations": [{"start": {"line": 334, "column": 20}, "end": {"line": 334, "column": 37}}, {"start": {"line": 334, "column": 40}, "end": {"line": 334, "column": 44}}]}, "9": {"loc": {"start": {"line": 380, "column": 6}, "end": {"line": 380, "column": 19}}, "type": "binary-expr", "locations": [{"start": {"line": 380, "column": 6}, "end": {"line": 380, "column": 13}}, {"start": {"line": 380, "column": 17}, "end": {"line": 380, "column": 19}}]}, "10": {"loc": {"start": {"line": 393, "column": 6}, "end": {"line": 393, "column": 19}}, "type": "binary-expr", "locations": [{"start": {"line": 393, "column": 6}, "end": {"line": 393, "column": 13}}, {"start": {"line": 393, "column": 17}, "end": {"line": 393, "column": 19}}]}, "11": {"loc": {"start": {"line": 413, "column": 31}, "end": {"line": 413, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 413, "column": 31}, "end": {"line": 413, "column": 48}}, {"start": {"line": 413, "column": 52}, "end": {"line": 413, "column": 53}}]}, "12": {"loc": {"start": {"line": 66, "column": 8}, "end": {"line": 74, "column": 9}}, "type": "if", "locations": [{"start": {"line": 66, "column": 8}, "end": {"line": 74, "column": 9}}, {"start": {"line": 72, "column": 15}, "end": {"line": 74, "column": 9}}]}, "13": {"loc": {"start": {"line": 154, "column": 8}, "end": {"line": 162, "column": 9}}, "type": "if", "locations": [{"start": {"line": 154, "column": 8}, "end": {"line": 162, "column": 9}}, {"start": {"line": 160, "column": 15}, "end": {"line": 162, "column": 9}}]}, "14": {"loc": {"start": {"line": 222, "column": 8}, "end": {"line": 226, "column": 9}}, "type": "if", "locations": [{"start": {"line": 222, "column": 8}, "end": {"line": 226, "column": 9}}, {"start": {"line": 224, "column": 15}, "end": {"line": 226, "column": 9}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 10, "15": 10, "16": 10, "17": 10, "18": 2, "19": 0, "20": 2, "21": 2, "22": 2, "23": 2, "24": 2, "25": 1, "26": 1, "27": 1, "28": 1, "29": 1, "30": 0, "31": 0, "32": 0, "33": 1, "34": 0, "35": 1, "36": 1, "37": 1, "38": 1, "39": 1, "40": 1, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 1, "54": 1, "55": 1, "56": 1, "57": 2, "58": 2, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 1, "74": 1, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 1, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 1, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 1, "96": 1, "97": 1, "98": 1, "99": 1, "100": 1, "101": 1, "102": 1, "103": 1, "104": 1, "105": 1, "106": 1, "107": 1}, "f": {"0": 10, "1": 2, "2": 1, "3": 0, "4": 1, "5": 1, "6": 1, "7": 1, "8": 2, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0}, "b": {"0": [0], "1": [1], "2": [0], "3": [0], "4": [0], "5": [0], "6": [0], "7": [1, 1], "8": [1, 1], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/analysis.module.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/analysis.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 48}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 59}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 66}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 58}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 64}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 47}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 76}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 56}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 68}}, "10": {"start": {"line": 18, "column": 7}, "end": {"line": 18, "column": null}}, "11": {"start": {"line": 18, "column": 13}, "end": {"line": 18, "column": 27}}, "12": {"start": {"line": 18, "column": 13}, "end": {"line": 18, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "f": {}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/cache.service.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/cache.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 60}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 54}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 24, "column": 25}, "end": {"line": 259, "column": null}}, "4": {"start": {"line": 29, "column": 45}, "end": {"line": 29, "column": 64}}, "5": {"start": {"line": 25, "column": 19}, "end": {"line": 25, "column": 58}}, "6": {"start": {"line": 26, "column": 19}, "end": {"line": 26, "column": 37}}, "7": {"start": {"line": 27, "column": 19}, "end": {"line": 27, "column": 51}}, "8": {"start": {"line": 39, "column": 26}, "end": {"line": 39, "column": 55}}, "9": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 76}}, "10": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 49}}, "11": {"start": {"line": 59, "column": 4}, "end": {"line": 79, "column": 5}}, "12": {"start": {"line": 60, "column": 18}, "end": {"line": 60, "column": 49}}, "13": {"start": {"line": 61, "column": 23}, "end": {"line": 64, "column": null}}, "14": {"start": {"line": 67, "column": 6}, "end": {"line": 67, "column": 62}}, "15": {"start": {"line": 70, "column": 24}, "end": {"line": 70, "column": 70}}, "16": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 63}}, "17": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 75}}, "18": {"start": {"line": 75, "column": 6}, "end": {"line": 78, "column": 8}}, "19": {"start": {"line": 90, "column": 4}, "end": {"line": 113, "column": 5}}, "20": {"start": {"line": 91, "column": 23}, "end": {"line": 91, "column": 78}}, "21": {"start": {"line": 92, "column": 21}, "end": {"line": 92, "column": 77}}, "22": {"start": {"line": 94, "column": 6}, "end": {"line": 103, "column": 7}}, "23": {"start": {"line": 96, "column": 8}, "end": {"line": 99, "column": 9}}, "24": {"start": {"line": 97, "column": 10}, "end": {"line": 97, "column": 76}}, "25": {"start": {"line": 98, "column": 10}, "end": {"line": 98, "column": 22}}, "26": {"start": {"line": 101, "column": 8}, "end": {"line": 101, "column": 50}}, "27": {"start": {"line": 102, "column": 8}, "end": {"line": 102, "column": 22}}, "28": {"start": {"line": 105, "column": 6}, "end": {"line": 105, "column": 49}}, "29": {"start": {"line": 106, "column": 6}, "end": {"line": 106, "column": 18}}, "30": {"start": {"line": 108, "column": 6}, "end": {"line": 111, "column": 8}}, "31": {"start": {"line": 112, "column": 6}, "end": {"line": 112, "column": 18}}, "32": {"start": {"line": 124, "column": 4}, "end": {"line": 140, "column": 5}}, "33": {"start": {"line": 125, "column": 23}, "end": {"line": 125, "column": 78}}, "34": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 44}}, "35": {"start": {"line": 129, "column": 6}, "end": {"line": 132, "column": 7}}, "36": {"start": {"line": 130, "column": 26}, "end": {"line": 130, "column": 72}}, "37": {"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": 47}}, "38": {"start": {"line": 134, "column": 6}, "end": {"line": 134, "column": 52}}, "39": {"start": {"line": 136, "column": 6}, "end": {"line": 139, "column": 8}}, "40": {"start": {"line": 147, "column": 4}, "end": {"line": 162, "column": 5}}, "41": {"start": {"line": 149, "column": 22}, "end": {"line": 149, "column": 56}}, "42": {"start": {"line": 152, "column": 6}, "end": {"line": 154, "column": 7}}, "43": {"start": {"line": 153, "column": 8}, "end": {"line": 153, "column": 65}}, "44": {"start": {"line": 156, "column": 6}, "end": {"line": 156, "column": 69}}, "45": {"start": {"line": 158, "column": 6}, "end": {"line": 161, "column": 8}}, "46": {"start": {"line": 169, "column": 4}, "end": {"line": 178, "column": 5}}, "47": {"start": {"line": 170, "column": 18}, "end": {"line": 170, "column": 59}}, "48": {"start": {"line": 171, "column": 6}, "end": {"line": 171, "column": 48}}, "49": {"start": {"line": 172, "column": 6}, "end": {"line": 172, "column": 80}}, "50": {"start": {"line": 174, "column": 6}, "end": {"line": 177, "column": 8}}, "51": {"start": {"line": 185, "column": 4}, "end": {"line": 195, "column": 5}}, "52": {"start": {"line": 186, "column": 18}, "end": {"line": 186, "column": 59}}, "53": {"start": {"line": 187, "column": 21}, "end": {"line": 187, "column": 61}}, "54": {"start": {"line": 188, "column": 6}, "end": {"line": 188, "column": 28}}, "55": {"start": {"line": 190, "column": 6}, "end": {"line": 193, "column": 8}}, "56": {"start": {"line": 194, "column": 6}, "end": {"line": 194, "column": 18}}, "57": {"start": {"line": 205, "column": 23}, "end": {"line": 205, "column": 62}}, "58": {"start": {"line": 206, "column": 4}, "end": {"line": 206, "column": 38}}, "59": {"start": {"line": 216, "column": 18}, "end": {"line": 219, "column": 6}}, "60": {"start": {"line": 221, "column": 4}, "end": {"line": 227, "column": 5}}, "61": {"start": {"line": 222, "column": 21}, "end": {"line": 222, "column": 74}}, "62": {"start": {"line": 223, "column": 6}, "end": {"line": 223, "column": 44}}, "63": {"start": {"line": 224, "column": 6}, "end": {"line": 226, "column": 7}}, "64": {"start": {"line": 225, "column": 8}, "end": {"line": 225, "column": 28}}, "65": {"start": {"line": 229, "column": 4}, "end": {"line": 229, "column": 17}}, "66": {"start": {"line": 236, "column": 4}, "end": {"line": 244, "column": 6}}, "67": {"start": {"line": 251, "column": 22}, "end": {"line": 251, "column": 52}}, "68": {"start": {"line": 252, "column": 4}, "end": {"line": 257, "column": 6}}, "69": {"start": {"line": 24, "column": 13}, "end": {"line": 24, "column": 25}}, "70": {"start": {"line": 24, "column": 13}, "end": {"line": 259, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 15}}, "loc": {"start": {"line": 29, "column": 64}, "end": {"line": 29, "column": 68}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": 18}}, "loc": {"start": {"line": 37, "column": 20}, "end": {"line": 41, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 27}}, "loc": {"start": {"line": 46, "column": 45}, "end": {"line": 48, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": 7}}, "loc": {"start": {"line": 57, "column": 26}, "end": {"line": 80, "column": 3}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 85, "column": 2}, "end": {"line": 85, "column": 7}}, "loc": {"start": {"line": 88, "column": 20}, "end": {"line": 114, "column": 3}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 119, "column": 2}, "end": {"line": 119, "column": 7}}, "loc": {"start": {"line": 122, "column": 20}, "end": {"line": 141, "column": 3}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 146, "column": 2}, "end": {"line": 146, "column": 7}}, "loc": {"start": {"line": 146, "column": 43}, "end": {"line": 163, "column": 3}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 168, "column": 2}, "end": {"line": 168, "column": 7}}, "loc": {"start": {"line": 168, "column": 57}, "end": {"line": 179, "column": 3}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 184, "column": 2}, "end": {"line": 184, "column": 7}}, "loc": {"start": {"line": 184, "column": 43}, "end": {"line": 196, "column": 3}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 201, "column": 2}, "end": {"line": 201, "column": 7}}, "loc": {"start": {"line": 203, "column": 23}, "end": {"line": 207, "column": 3}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 212, "column": 2}, "end": {"line": 212, "column": 7}}, "loc": {"start": {"line": 212, "column": 39}, "end": {"line": 230, "column": 3}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 235, "column": 2}, "end": {"line": 235, "column": 19}}, "loc": {"start": {"line": 235, "column": 42}, "end": {"line": 245, "column": 3}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 250, "column": 2}, "end": {"line": 250, "column": 7}}, "loc": {"start": {"line": 250, "column": 42}, "end": {"line": 258, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 39, "column": 26}, "end": {"line": 39, "column": 55}}, "type": "cond-expr", "locations": [{"start": {"line": 39, "column": 36}, "end": {"line": 39, "column": 50}}, {"start": {"line": 39, "column": 53}, "end": {"line": 39, "column": 55}}]}, "1": {"loc": {"start": {"line": 60, "column": 18}, "end": {"line": 60, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 60, "column": 18}, "end": {"line": 60, "column": 30}}, {"start": {"line": 60, "column": 34}, "end": {"line": 60, "column": 49}}]}, "2": {"loc": {"start": {"line": 94, "column": 6}, "end": {"line": 103, "column": 7}}, "type": "if", "locations": [{"start": {"line": 94, "column": 6}, "end": {"line": 103, "column": 7}}]}, "3": {"loc": {"start": {"line": 96, "column": 8}, "end": {"line": 99, "column": 9}}, "type": "if", "locations": [{"start": {"line": 96, "column": 8}, "end": {"line": 99, "column": 9}}]}, "4": {"loc": {"start": {"line": 96, "column": 12}, "end": {"line": 96, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 96, "column": 12}, "end": {"line": 96, "column": 28}}, {"start": {"line": 96, "column": 32}, "end": {"line": 96, "column": 71}}]}, "5": {"loc": {"start": {"line": 129, "column": 6}, "end": {"line": 132, "column": 7}}, "type": "if", "locations": [{"start": {"line": 129, "column": 6}, "end": {"line": 132, "column": 7}}]}, "6": {"loc": {"start": {"line": 188, "column": 13}, "end": {"line": 188, "column": 27}}, "type": "binary-expr", "locations": [{"start": {"line": 188, "column": 13}, "end": {"line": 188, "column": 19}}, {"start": {"line": 188, "column": 23}, "end": {"line": 188, "column": 27}}]}, "7": {"loc": {"start": {"line": 224, "column": 6}, "end": {"line": 226, "column": 7}}, "type": "if", "locations": [{"start": {"line": 224, "column": 6}, "end": {"line": 226, "column": 7}}]}, "8": {"loc": {"start": {"line": 241, "column": 8}, "end": {"line": 241, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 241, "column": 8}, "end": {"line": 241, "column": 24}}, {"start": {"line": 241, "column": 28}, "end": {"line": 241, "column": 73}}]}}, "s": {"0": 3, "1": 3, "2": 3, "3": 3, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 3, "70": 3}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0], "3": [0], "4": [0, 0], "5": [0], "6": [0, 0], "7": [0], "8": [0, 0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/data-processing.service.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/data-processing.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 65}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 37}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 34}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 29}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 25}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 56}}, "7": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 58}}, "8": {"start": {"line": 21, "column": 7}, "end": {"line": 315, "column": null}}, "9": {"start": {"line": 24, "column": 21}, "end": {"line": 24, "column": 41}}, "10": {"start": {"line": 25, "column": 21}, "end": {"line": 25, "column": 40}}, "11": {"start": {"line": 29, "column": 4}, "end": {"line": 48, "column": 5}}, "12": {"start": {"line": 30, "column": 6}, "end": {"line": 45, "column": 7}}, "13": {"start": {"line": 32, "column": 25}, "end": {"line": 32, "column": 80}}, "14": {"start": {"line": 34, "column": 8}, "end": {"line": 38, "column": 9}}, "15": {"start": {"line": 35, "column": 10}, "end": {"line": 37, "column": 12}}, "16": {"start": {"line": 40, "column": 8}, "end": {"line": 40, "column": 39}}, "17": {"start": {"line": 41, "column": 13}, "end": {"line": 45, "column": 7}}, "18": {"start": {"line": 42, "column": 8}, "end": {"line": 42, "column": 41}}, "19": {"start": {"line": 44, "column": 8}, "end": {"line": 44, "column": 50}}, "20": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 64}}, "21": {"start": {"line": 52, "column": 4}, "end": {"line": 90, "column": 7}}, "22": {"start": {"line": 53, "column": 29}, "end": {"line": 53, "column": 31}}, "23": {"start": {"line": 54, "column": 21}, "end": {"line": 54, "column": 22}}, "24": {"start": {"line": 55, "column": 22}, "end": {"line": 55, "column": 28}}, "25": {"start": {"line": 56, "column": 24}, "end": {"line": 56, "column": 28}}, "26": {"start": {"line": 57, "column": 25}, "end": {"line": 57, "column": 27}}, "27": {"start": {"line": 59, "column": 6}, "end": {"line": 89, "column": 47}}, "28": {"start": {"line": 62, "column": 10}, "end": {"line": 62, "column": 21}}, "29": {"start": {"line": 65, "column": 10}, "end": {"line": 67, "column": 11}}, "30": {"start": {"line": 66, "column": 12}, "end": {"line": 66, "column": 64}}, "31": {"start": {"line": 69, "column": 10}, "end": {"line": 69, "column": 27}}, "32": {"start": {"line": 72, "column": 10}, "end": {"line": 80, "column": 11}}, "33": {"start": {"line": 73, "column": 12}, "end": {"line": 73, "column": 35}}, "34": {"start": {"line": 74, "column": 12}, "end": {"line": 74, "column": 23}}, "35": {"start": {"line": 77, "column": 12}, "end": {"line": 79, "column": 13}}, "36": {"start": {"line": 78, "column": 14}, "end": {"line": 78, "column": 26}}, "37": {"start": {"line": 84, "column": 10}, "end": {"line": 86, "column": 11}}, "38": {"start": {"line": 85, "column": 12}, "end": {"line": 85, "column": 35}}, "39": {"start": {"line": 87, "column": 10}, "end": {"line": 87, "column": 27}}, "40": {"start": {"line": 89, "column": 32}, "end": {"line": 89, "column": 45}}, "41": {"start": {"line": 94, "column": 21}, "end": {"line": 94, "column": 44}}, "42": {"start": {"line": 95, "column": 22}, "end": {"line": 95, "column": 44}}, "43": {"start": {"line": 96, "column": 22}, "end": {"line": 96, "column": 48}}, "44": {"start": {"line": 98, "column": 4}, "end": {"line": 98, "column": 47}}, "45": {"start": {"line": 102, "column": 29}, "end": {"line": 102, "column": 31}}, "46": {"start": {"line": 103, "column": 31}, "end": {"line": 103, "column": 33}}, "47": {"start": {"line": 104, "column": 20}, "end": {"line": 104, "column": 21}}, "48": {"start": {"line": 112, "column": 8}, "end": {"line": 112, "column": 17}}, "49": {"start": {"line": 115, "column": 4}, "end": {"line": 125, "column": 5}}, "50": {"start": {"line": 116, "column": 6}, "end": {"line": 116, "column": 32}}, "51": {"start": {"line": 117, "column": 6}, "end": {"line": 124, "column": 8}}, "52": {"start": {"line": 127, "column": 21}, "end": {"line": 127, "column": 28}}, "53": {"start": {"line": 128, "column": 19}, "end": {"line": 128, "column": 40}}, "54": {"start": {"line": 130, "column": 4}, "end": {"line": 132, "column": 5}}, "55": {"start": {"line": 131, "column": 6}, "end": {"line": 131, "column": 44}}, "56": {"start": {"line": 133, "column": 4}, "end": {"line": 135, "column": 5}}, "57": {"start": {"line": 134, "column": 6}, "end": {"line": 134, "column": 46}}, "58": {"start": {"line": 136, "column": 4}, "end": {"line": 138, "column": 5}}, "59": {"start": {"line": 137, "column": 6}, "end": {"line": 137, "column": 47}}, "60": {"start": {"line": 140, "column": 4}, "end": {"line": 149, "column": 5}}, "61": {"start": {"line": 141, "column": 6}, "end": {"line": 148, "column": 8}}, "62": {"start": {"line": 152, "column": 4}, "end": {"line": 187, "column": 7}}, "63": {"start": {"line": 153, "column": 34}, "end": {"line": 153, "column": 36}}, "64": {"start": {"line": 156, "column": 6}, "end": {"line": 158, "column": 7}}, "65": {"start": {"line": 157, "column": 8}, "end": {"line": 157, "column": 62}}, "66": {"start": {"line": 159, "column": 6}, "end": {"line": 161, "column": 7}}, "67": {"start": {"line": 160, "column": 8}, "end": {"line": 160, "column": 64}}, "68": {"start": {"line": 162, "column": 6}, "end": {"line": 167, "column": 7}}, "69": {"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 65}}, "70": {"start": {"line": 170, "column": 6}, "end": {"line": 175, "column": 7}}, "71": {"start": {"line": 171, "column": 26}, "end": {"line": 171, "column": 55}}, "72": {"start": {"line": 172, "column": 8}, "end": {"line": 174, "column": 9}}, "73": {"start": {"line": 173, "column": 10}, "end": {"line": 173, "column": 69}}, "74": {"start": {"line": 178, "column": 6}, "end": {"line": 180, "column": 7}}, "75": {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 61}}, "76": {"start": {"line": 182, "column": 6}, "end": {"line": 186, "column": 7}}, "77": {"start": {"line": 183, "column": 8}, "end": {"line": 183, "column": 20}}, "78": {"start": {"line": 185, "column": 8}, "end": {"line": 185, "column": 34}}, "79": {"start": {"line": 189, "column": 4}, "end": {"line": 196, "column": 6}}, "80": {"start": {"line": 210, "column": 8}, "end": {"line": 210, "column": 17}}, "81": {"start": {"line": 212, "column": 22}, "end": {"line": 248, "column": 6}}, "82": {"start": {"line": 213, "column": 23}, "end": {"line": 213, "column": 37}}, "83": {"start": {"line": 214, "column": 6}, "end": {"line": 214, "column": 37}}, "84": {"start": {"line": 215, "column": 6}, "end": {"line": 215, "column": 59}}, "85": {"start": {"line": 216, "column": 6}, "end": {"line": 216, "column": 63}}, "86": {"start": {"line": 217, "column": 6}, "end": {"line": 217, "column": 57}}, "87": {"start": {"line": 219, "column": 6}, "end": {"line": 221, "column": 7}}, "88": {"start": {"line": 220, "column": 8}, "end": {"line": 220, "column": 65}}, "89": {"start": {"line": 223, "column": 6}, "end": {"line": 225, "column": 7}}, "90": {"start": {"line": 224, "column": 8}, "end": {"line": 224, "column": 47}}, "91": {"start": {"line": 228, "column": 46}, "end": {"line": 228, "column": 48}}, "92": {"start": {"line": 229, "column": 6}, "end": {"line": 241, "column": 9}}, "93": {"start": {"line": 230, "column": 8}, "end": {"line": 240, "column": 9}}, "94": {"start": {"line": 239, "column": 10}, "end": {"line": 239, "column": 37}}, "95": {"start": {"line": 243, "column": 6}, "end": {"line": 245, "column": 7}}, "96": {"start": {"line": 244, "column": 8}, "end": {"line": 244, "column": 41}}, "97": {"start": {"line": 247, "column": 6}, "end": {"line": 247, "column": 22}}, "98": {"start": {"line": 250, "column": 4}, "end": {"line": 250, "column": 51}}, "99": {"start": {"line": 254, "column": 25}, "end": {"line": 256, "column": 62}}, "100": {"start": {"line": 264, "column": 8}, "end": {"line": 279, "column": 6}}, "101": {"start": {"line": 281, "column": 4}, "end": {"line": 290, "column": 6}}, "102": {"start": {"line": 297, "column": 21}, "end": {"line": 297, "column": 61}}, "103": {"start": {"line": 299, "column": 4}, "end": {"line": 301, "column": 5}}, "104": {"start": {"line": 300, "column": 6}, "end": {"line": 300, "column": 54}}, "105": {"start": {"line": 304, "column": 19}, "end": {"line": 304, "column": 84}}, "106": {"start": {"line": 306, "column": 4}, "end": {"line": 313, "column": 6}}, "107": {"start": {"line": 21, "column": 13}, "end": {"line": 21, "column": 34}}, "108": {"start": {"line": 21, "column": 13}, "end": {"line": 315, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": null}}, "loc": {"start": {"line": 25, "column": 57}, "end": {"line": 26, "column": 6}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 7}}, "loc": {"start": {"line": 28, "column": 52}, "end": {"line": 49, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 51, "column": 10}, "end": {"line": 51, "column": 15}}, "loc": {"start": {"line": 51, "column": 41}, "end": {"line": 91, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 52, "column": 23}, "end": {"line": 52, "column": 24}}, "loc": {"start": {"line": 52, "column": 43}, "end": {"line": 90, "column": 5}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 61, "column": 20}, "end": {"line": 61, "column": 21}}, "loc": {"start": {"line": 61, "column": 29}, "end": {"line": 81, "column": 9}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 82, "column": 19}, "end": {"line": 82, "column": 22}}, "loc": {"start": {"line": 82, "column": 24}, "end": {"line": 88, "column": 9}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 89, "column": 21}, "end": {"line": 89, "column": 22}}, "loc": {"start": {"line": 89, "column": 32}, "end": {"line": 89, "column": 45}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 93, "column": 10}, "end": {"line": 93, "column": 20}}, "loc": {"start": {"line": 93, "column": 37}, "end": {"line": 99, "column": 3}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 101, "column": 2}, "end": {"line": 101, "column": 14}}, "loc": {"start": {"line": 101, "column": 52}, "end": {"line": 197, "column": 3}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 152, "column": 17}, "end": {"line": 152, "column": 18}}, "loc": {"start": {"line": 152, "column": 32}, "end": {"line": 187, "column": 5}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 199, "column": 2}, "end": {"line": 199, "column": 7}}, "loc": {"start": {"line": 201, "column": 28}, "end": {"line": 251, "column": 3}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 212, "column": 31}, "end": {"line": 212, "column": 32}}, "loc": {"start": {"line": 212, "column": 39}, "end": {"line": 248, "column": 5}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 229, "column": 31}, "end": {"line": 229, "column": 32}}, "loc": {"start": {"line": 229, "column": 39}, "end": {"line": 241, "column": 7}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 253, "column": 2}, "end": {"line": 253, "column": 7}}, "loc": {"start": {"line": 253, "column": 43}, "end": {"line": 291, "column": 3}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 296, "column": 2}, "end": {"line": 296, "column": 7}}, "loc": {"start": {"line": 296, "column": 66}, "end": {"line": 314, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 30, "column": 6}, "end": {"line": 45, "column": 7}}, "type": "if", "locations": [{"start": {"line": 30, "column": 6}, "end": {"line": 45, "column": 7}}, {"start": {"line": 41, "column": 13}, "end": {"line": 45, "column": 7}}]}, "1": {"loc": {"start": {"line": 34, "column": 8}, "end": {"line": 38, "column": 9}}, "type": "if", "locations": [{"start": {"line": 34, "column": 8}, "end": {"line": 38, "column": 9}}]}, "2": {"loc": {"start": {"line": 41, "column": 13}, "end": {"line": 45, "column": 7}}, "type": "if", "locations": [{"start": {"line": 41, "column": 13}, "end": {"line": 45, "column": 7}}, {"start": {"line": 43, "column": 13}, "end": {"line": 45, "column": 7}}]}, "3": {"loc": {"start": {"line": 41, "column": 17}, "end": {"line": 41, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 17}, "end": {"line": 41, "column": 36}}, {"start": {"line": 41, "column": 40}, "end": {"line": 41, "column": 58}}]}, "4": {"loc": {"start": {"line": 65, "column": 10}, "end": {"line": 67, "column": 11}}, "type": "if", "locations": [{"start": {"line": 65, "column": 10}, "end": {"line": 67, "column": 11}}]}, "5": {"loc": {"start": {"line": 72, "column": 10}, "end": {"line": 80, "column": 11}}, "type": "if", "locations": [{"start": {"line": 72, "column": 10}, "end": {"line": 80, "column": 11}}]}, "6": {"loc": {"start": {"line": 77, "column": 12}, "end": {"line": 79, "column": 13}}, "type": "if", "locations": [{"start": {"line": 77, "column": 12}, "end": {"line": 79, "column": 13}}]}, "7": {"loc": {"start": {"line": 84, "column": 10}, "end": {"line": 86, "column": 11}}, "type": "if", "locations": [{"start": {"line": 84, "column": 10}, "end": {"line": 86, "column": 11}}]}, "8": {"loc": {"start": {"line": 115, "column": 4}, "end": {"line": 125, "column": 5}}, "type": "if", "locations": [{"start": {"line": 115, "column": 4}, "end": {"line": 125, "column": 5}}]}, "9": {"loc": {"start": {"line": 130, "column": 4}, "end": {"line": 132, "column": 5}}, "type": "if", "locations": [{"start": {"line": 130, "column": 4}, "end": {"line": 132, "column": 5}}]}, "10": {"loc": {"start": {"line": 133, "column": 4}, "end": {"line": 135, "column": 5}}, "type": "if", "locations": [{"start": {"line": 133, "column": 4}, "end": {"line": 135, "column": 5}}]}, "11": {"loc": {"start": {"line": 136, "column": 4}, "end": {"line": 138, "column": 5}}, "type": "if", "locations": [{"start": {"line": 136, "column": 4}, "end": {"line": 138, "column": 5}}]}, "12": {"loc": {"start": {"line": 140, "column": 4}, "end": {"line": 149, "column": 5}}, "type": "if", "locations": [{"start": {"line": 140, "column": 4}, "end": {"line": 149, "column": 5}}]}, "13": {"loc": {"start": {"line": 156, "column": 6}, "end": {"line": 158, "column": 7}}, "type": "if", "locations": [{"start": {"line": 156, "column": 6}, "end": {"line": 158, "column": 7}}]}, "14": {"loc": {"start": {"line": 156, "column": 10}, "end": {"line": 156, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 156, "column": 10}, "end": {"line": 156, "column": 27}}, {"start": {"line": 156, "column": 31}, "end": {"line": 156, "column": 72}}]}, "15": {"loc": {"start": {"line": 159, "column": 6}, "end": {"line": 161, "column": 7}}, "type": "if", "locations": [{"start": {"line": 159, "column": 6}, "end": {"line": 161, "column": 7}}]}, "16": {"loc": {"start": {"line": 159, "column": 10}, "end": {"line": 159, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 159, "column": 10}, "end": {"line": 159, "column": 29}}, {"start": {"line": 159, "column": 33}, "end": {"line": 159, "column": 76}}]}, "17": {"loc": {"start": {"line": 162, "column": 6}, "end": {"line": 167, "column": 7}}, "type": "if", "locations": [{"start": {"line": 162, "column": 6}, "end": {"line": 167, "column": 7}}]}, "18": {"loc": {"start": {"line": 163, "column": 8}, "end": {"line": 164, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 28}}, {"start": {"line": 164, "column": 8}, "end": {"line": 164, "column": 52}}]}, "19": {"loc": {"start": {"line": 170, "column": 6}, "end": {"line": 175, "column": 7}}, "type": "if", "locations": [{"start": {"line": 170, "column": 6}, "end": {"line": 175, "column": 7}}]}, "20": {"loc": {"start": {"line": 172, "column": 8}, "end": {"line": 174, "column": 9}}, "type": "if", "locations": [{"start": {"line": 172, "column": 8}, "end": {"line": 174, "column": 9}}]}, "21": {"loc": {"start": {"line": 178, "column": 6}, "end": {"line": 180, "column": 7}}, "type": "if", "locations": [{"start": {"line": 178, "column": 6}, "end": {"line": 180, "column": 7}}]}, "22": {"loc": {"start": {"line": 178, "column": 10}, "end": {"line": 178, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 178, "column": 10}, "end": {"line": 178, "column": 19}}, {"start": {"line": 178, "column": 23}, "end": {"line": 178, "column": 37}}, {"start": {"line": 178, "column": 41}, "end": {"line": 178, "column": 70}}]}, "23": {"loc": {"start": {"line": 182, "column": 6}, "end": {"line": 186, "column": 7}}, "type": "if", "locations": [{"start": {"line": 182, "column": 6}, "end": {"line": 186, "column": 7}}, {"start": {"line": 184, "column": 13}, "end": {"line": 186, "column": 7}}]}, "24": {"loc": {"start": {"line": 219, "column": 6}, "end": {"line": 221, "column": 7}}, "type": "if", "locations": [{"start": {"line": 219, "column": 6}, "end": {"line": 221, "column": 7}}]}, "25": {"loc": {"start": {"line": 219, "column": 10}, "end": {"line": 219, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 219, "column": 10}, "end": {"line": 219, "column": 23}}, {"start": {"line": 219, "column": 27}, "end": {"line": 219, "column": 45}}]}, "26": {"loc": {"start": {"line": 223, "column": 6}, "end": {"line": 225, "column": 7}}, "type": "if", "locations": [{"start": {"line": 223, "column": 6}, "end": {"line": 225, "column": 7}}]}, "27": {"loc": {"start": {"line": 223, "column": 10}, "end": {"line": 223, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 223, "column": 10}, "end": {"line": 223, "column": 19}}, {"start": {"line": 223, "column": 23}, "end": {"line": 223, "column": 37}}, {"start": {"line": 223, "column": 41}, "end": {"line": 223, "column": 71}}]}, "28": {"loc": {"start": {"line": 230, "column": 8}, "end": {"line": 240, "column": 9}}, "type": "if", "locations": [{"start": {"line": 230, "column": 8}, "end": {"line": 240, "column": 9}}]}, "29": {"loc": {"start": {"line": 243, "column": 6}, "end": {"line": 245, "column": 7}}, "type": "if", "locations": [{"start": {"line": 243, "column": 6}, "end": {"line": 245, "column": 7}}]}, "30": {"loc": {"start": {"line": 299, "column": 4}, "end": {"line": 301, "column": 5}}, "type": "if", "locations": [{"start": {"line": 299, "column": 4}, "end": {"line": 301, "column": 5}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 1, "108": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "b": {"0": [0, 0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0], "5": [0], "6": [0], "7": [0], "8": [0], "9": [0], "10": [0], "11": [0], "12": [0], "13": [0], "14": [0, 0], "15": [0], "16": [0, 0], "17": [0], "18": [0, 0], "19": [0], "20": [0], "21": [0], "22": [0, 0, 0], "23": [0, 0], "24": [0], "25": [0, 0], "26": [0], "27": [0, 0, 0], "28": [0], "29": [0], "30": [0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/data-stream.service.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/data-stream.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 65}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 37}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 34}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 25}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 56}}, "6": {"start": {"line": 18, "column": 7}, "end": {"line": 249, "column": null}}, "7": {"start": {"line": 21, "column": 21}, "end": {"line": 21, "column": 41}}, "8": {"start": {"line": 31, "column": 4}, "end": {"line": 142, "column": 7}}, "9": {"start": {"line": 32, "column": 45}, "end": {"line": 38, "column": 8}}, "10": {"start": {"line": 40, "column": 24}, "end": {"line": 40, "column": 28}}, "11": {"start": {"line": 41, "column": 22}, "end": {"line": 41, "column": 28}}, "12": {"start": {"line": 42, "column": 24}, "end": {"line": 42, "column": 27}}, "13": {"start": {"line": 44, "column": 25}, "end": {"line": 44, "column": 27}}, "14": {"start": {"line": 45, "column": 21}, "end": {"line": 45, "column": 22}}, "15": {"start": {"line": 46, "column": 23}, "end": {"line": 46, "column": 27}}, "16": {"start": {"line": 47, "column": 30}, "end": {"line": 47, "column": 32}}, "17": {"start": {"line": 56, "column": 10}, "end": {"line": 56, "column": 19}}, "18": {"start": {"line": 58, "column": 21}, "end": {"line": 141, "column": 10}}, "19": {"start": {"line": 61, "column": 10}, "end": {"line": 61, "column": 31}}, "20": {"start": {"line": 64, "column": 10}, "end": {"line": 66, "column": 11}}, "21": {"start": {"line": 65, "column": 12}, "end": {"line": 65, "column": 77}}, "22": {"start": {"line": 67, "column": 10}, "end": {"line": 69, "column": 11}}, "23": {"start": {"line": 68, "column": 12}, "end": {"line": 68, "column": 79}}, "24": {"start": {"line": 70, "column": 10}, "end": {"line": 72, "column": 11}}, "25": {"start": {"line": 71, "column": 12}, "end": {"line": 71, "column": 80}}, "26": {"start": {"line": 75, "column": 10}, "end": {"line": 75, "column": 21}}, "27": {"start": {"line": 76, "column": 10}, "end": {"line": 76, "column": 34}}, "28": {"start": {"line": 79, "column": 10}, "end": {"line": 82, "column": 11}}, "29": {"start": {"line": 80, "column": 12}, "end": {"line": 80, "column": 29}}, "30": {"start": {"line": 81, "column": 12}, "end": {"line": 81, "column": 78}}, "31": {"start": {"line": 85, "column": 10}, "end": {"line": 87, "column": 11}}, "32": {"start": {"line": 86, "column": 12}, "end": {"line": 86, "column": 38}}, "33": {"start": {"line": 90, "column": 34}, "end": {"line": 90, "column": 77}}, "34": {"start": {"line": 91, "column": 10}, "end": {"line": 100, "column": 11}}, "35": {"start": {"line": 92, "column": 12}, "end": {"line": 92, "column": 48}}, "36": {"start": {"line": 95, "column": 12}, "end": {"line": 98, "column": 13}}, "37": {"start": {"line": 96, "column": 14}, "end": {"line": 96, "column": 31}}, "38": {"start": {"line": 97, "column": 14}, "end": {"line": 97, "column": 71}}, "39": {"start": {"line": 99, "column": 12}, "end": {"line": 99, "column": 19}}, "40": {"start": {"line": 103, "column": 27}, "end": {"line": 103, "column": 63}}, "41": {"start": {"line": 104, "column": 10}, "end": {"line": 104, "column": 31}}, "42": {"start": {"line": 105, "column": 10}, "end": {"line": 105, "column": 29}}, "43": {"start": {"line": 108, "column": 10}, "end": {"line": 125, "column": 11}}, "44": {"start": {"line": 109, "column": 12}, "end": {"line": 109, "column": 27}}, "45": {"start": {"line": 111, "column": 12}, "end": {"line": 124, "column": 13}}, "46": {"start": {"line": 112, "column": 14}, "end": {"line": 112, "column": 56}}, "47": {"start": {"line": 113, "column": 14}, "end": {"line": 113, "column": 25}}, "48": {"start": {"line": 116, "column": 14}, "end": {"line": 118, "column": 15}}, "49": {"start": {"line": 117, "column": 16}, "end": {"line": 117, "column": 28}}, "50": {"start": {"line": 120, "column": 14}, "end": {"line": 120, "column": 30}}, "51": {"start": {"line": 122, "column": 14}, "end": {"line": 122, "column": 31}}, "52": {"start": {"line": 123, "column": 14}, "end": {"line": 123, "column": 81}}, "53": {"start": {"line": 128, "column": 10}, "end": {"line": 137, "column": 11}}, "54": {"start": {"line": 130, "column": 12}, "end": {"line": 132, "column": 13}}, "55": {"start": {"line": 131, "column": 14}, "end": {"line": 131, "column": 56}}, "56": {"start": {"line": 134, "column": 12}, "end": {"line": 134, "column": 28}}, "57": {"start": {"line": 136, "column": 12}, "end": {"line": 136, "column": 74}}, "58": {"start": {"line": 140, "column": 10}, "end": {"line": 140, "column": 70}}, "59": {"start": {"line": 149, "column": 70}, "end": {"line": 149, "column": 79}}, "60": {"start": {"line": 152, "column": 4}, "end": {"line": 154, "column": 5}}, "61": {"start": {"line": 153, "column": 6}, "end": {"line": 153, "column": 50}}, "62": {"start": {"line": 155, "column": 4}, "end": {"line": 157, "column": 5}}, "63": {"start": {"line": 156, "column": 6}, "end": {"line": 156, "column": 52}}, "64": {"start": {"line": 158, "column": 4}, "end": {"line": 160, "column": 5}}, "65": {"start": {"line": 159, "column": 6}, "end": {"line": 159, "column": 53}}, "66": {"start": {"line": 163, "column": 22}, "end": {"line": 163, "column": 51}}, "67": {"start": {"line": 164, "column": 4}, "end": {"line": 166, "column": 5}}, "68": {"start": {"line": 165, "column": 6}, "end": {"line": 165, "column": 55}}, "69": {"start": {"line": 169, "column": 4}, "end": {"line": 171, "column": 5}}, "70": {"start": {"line": 170, "column": 6}, "end": {"line": 170, "column": 50}}, "71": {"start": {"line": 173, "column": 4}, "end": {"line": 173, "column": 16}}, "72": {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 17}}, "73": {"start": {"line": 189, "column": 21}, "end": {"line": 189, "column": 35}}, "74": {"start": {"line": 190, "column": 4}, "end": {"line": 190, "column": 35}}, "75": {"start": {"line": 191, "column": 4}, "end": {"line": 191, "column": 57}}, "76": {"start": {"line": 192, "column": 4}, "end": {"line": 192, "column": 61}}, "77": {"start": {"line": 193, "column": 4}, "end": {"line": 193, "column": 55}}, "78": {"start": {"line": 195, "column": 4}, "end": {"line": 197, "column": 5}}, "79": {"start": {"line": 196, "column": 6}, "end": {"line": 196, "column": 63}}, "80": {"start": {"line": 199, "column": 4}, "end": {"line": 201, "column": 5}}, "81": {"start": {"line": 200, "column": 6}, "end": {"line": 200, "column": 45}}, "82": {"start": {"line": 204, "column": 44}, "end": {"line": 204, "column": 46}}, "83": {"start": {"line": 205, "column": 4}, "end": {"line": 217, "column": 7}}, "84": {"start": {"line": 206, "column": 6}, "end": {"line": 216, "column": 7}}, "85": {"start": {"line": 215, "column": 8}, "end": {"line": 215, "column": 35}}, "86": {"start": {"line": 219, "column": 4}, "end": {"line": 221, "column": 5}}, "87": {"start": {"line": 220, "column": 6}, "end": {"line": 220, "column": 39}}, "88": {"start": {"line": 223, "column": 4}, "end": {"line": 223, "column": 20}}, "89": {"start": {"line": 234, "column": 18}, "end": {"line": 234, "column": 39}}, "90": {"start": {"line": 235, "column": 26}, "end": {"line": 235, "column": 36}}, "91": {"start": {"line": 238, "column": 26}, "end": {"line": 238, "column": 57}}, "92": {"start": {"line": 241, "column": 31}, "end": {"line": 241, "column": 88}}, "93": {"start": {"line": 243, "column": 4}, "end": {"line": 247, "column": 6}}, "94": {"start": {"line": 18, "column": 13}, "end": {"line": 18, "column": 30}}, "95": {"start": {"line": 18, "column": 13}, "end": {"line": 249, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": null}}, "loc": {"start": {"line": 21, "column": 61}, "end": {"line": 22, "column": 6}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 7}}, "loc": {"start": {"line": 29, "column": 28}, "end": {"line": 143, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 31, "column": 23}, "end": {"line": 31, "column": 24}}, "loc": {"start": {"line": 31, "column": 43}, "end": {"line": 142, "column": 5}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 60, "column": 23}, "end": {"line": 60, "column": 24}}, "loc": {"start": {"line": 60, "column": 38}, "end": {"line": 73, "column": 9}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 74, "column": 20}, "end": {"line": 74, "column": 25}}, "loc": {"start": {"line": 74, "column": 35}, "end": {"line": 126, "column": 9}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 127, "column": 19}, "end": {"line": 127, "column": 24}}, "loc": {"start": {"line": 127, "column": 30}, "end": {"line": 138, "column": 9}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 139, "column": 21}, "end": {"line": 139, "column": 22}}, "loc": {"start": {"line": 139, "column": 31}, "end": {"line": 141, "column": 9}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 148, "column": 10}, "end": {"line": 148, "column": 21}}, "loc": {"start": {"line": 148, "column": 74}, "end": {"line": 174, "column": 3}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 179, "column": 10}, "end": {"line": 179, "column": 24}}, "loc": {"start": {"line": 179, "column": 59}, "end": {"line": 224, "column": 3}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 205, "column": 29}, "end": {"line": 205, "column": 30}}, "loc": {"start": {"line": 205, "column": 37}, "end": {"line": 217, "column": 5}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 229, "column": 2}, "end": {"line": 229, "column": 7}}, "loc": {"start": {"line": 229, "column": 41}, "end": {"line": 248, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 64, "column": 10}, "end": {"line": 66, "column": 11}}, "type": "if", "locations": [{"start": {"line": 64, "column": 10}, "end": {"line": 66, "column": 11}}]}, "1": {"loc": {"start": {"line": 67, "column": 10}, "end": {"line": 69, "column": 11}}, "type": "if", "locations": [{"start": {"line": 67, "column": 10}, "end": {"line": 69, "column": 11}}]}, "2": {"loc": {"start": {"line": 70, "column": 10}, "end": {"line": 72, "column": 11}}, "type": "if", "locations": [{"start": {"line": 70, "column": 10}, "end": {"line": 72, "column": 11}}]}, "3": {"loc": {"start": {"line": 79, "column": 10}, "end": {"line": 82, "column": 11}}, "type": "if", "locations": [{"start": {"line": 79, "column": 10}, "end": {"line": 82, "column": 11}}]}, "4": {"loc": {"start": {"line": 85, "column": 10}, "end": {"line": 87, "column": 11}}, "type": "if", "locations": [{"start": {"line": 85, "column": 10}, "end": {"line": 87, "column": 11}}]}, "5": {"loc": {"start": {"line": 91, "column": 10}, "end": {"line": 100, "column": 11}}, "type": "if", "locations": [{"start": {"line": 91, "column": 10}, "end": {"line": 100, "column": 11}}]}, "6": {"loc": {"start": {"line": 95, "column": 12}, "end": {"line": 98, "column": 13}}, "type": "if", "locations": [{"start": {"line": 95, "column": 12}, "end": {"line": 98, "column": 13}}]}, "7": {"loc": {"start": {"line": 108, "column": 10}, "end": {"line": 125, "column": 11}}, "type": "if", "locations": [{"start": {"line": 108, "column": 10}, "end": {"line": 125, "column": 11}}]}, "8": {"loc": {"start": {"line": 116, "column": 14}, "end": {"line": 118, "column": 15}}, "type": "if", "locations": [{"start": {"line": 116, "column": 14}, "end": {"line": 118, "column": 15}}]}, "9": {"loc": {"start": {"line": 130, "column": 12}, "end": {"line": 132, "column": 13}}, "type": "if", "locations": [{"start": {"line": 130, "column": 12}, "end": {"line": 132, "column": 13}}]}, "10": {"loc": {"start": {"line": 152, "column": 4}, "end": {"line": 154, "column": 5}}, "type": "if", "locations": [{"start": {"line": 152, "column": 4}, "end": {"line": 154, "column": 5}}]}, "11": {"loc": {"start": {"line": 152, "column": 8}, "end": {"line": 152, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 152, "column": 8}, "end": {"line": 152, "column": 25}}, {"start": {"line": 152, "column": 29}, "end": {"line": 152, "column": 70}}]}, "12": {"loc": {"start": {"line": 155, "column": 4}, "end": {"line": 157, "column": 5}}, "type": "if", "locations": [{"start": {"line": 155, "column": 4}, "end": {"line": 157, "column": 5}}]}, "13": {"loc": {"start": {"line": 155, "column": 8}, "end": {"line": 155, "column": 74}}, "type": "binary-expr", "locations": [{"start": {"line": 155, "column": 8}, "end": {"line": 155, "column": 27}}, {"start": {"line": 155, "column": 31}, "end": {"line": 155, "column": 74}}]}, "14": {"loc": {"start": {"line": 158, "column": 4}, "end": {"line": 160, "column": 5}}, "type": "if", "locations": [{"start": {"line": 158, "column": 4}, "end": {"line": 160, "column": 5}}]}, "15": {"loc": {"start": {"line": 158, "column": 8}, "end": {"line": 158, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 158, "column": 8}, "end": {"line": 158, "column": 28}}, {"start": {"line": 158, "column": 32}, "end": {"line": 158, "column": 76}}]}, "16": {"loc": {"start": {"line": 164, "column": 4}, "end": {"line": 166, "column": 5}}, "type": "if", "locations": [{"start": {"line": 164, "column": 4}, "end": {"line": 166, "column": 5}}]}, "17": {"loc": {"start": {"line": 169, "column": 4}, "end": {"line": 171, "column": 5}}, "type": "if", "locations": [{"start": {"line": 169, "column": 4}, "end": {"line": 171, "column": 5}}]}, "18": {"loc": {"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 17}}, {"start": {"line": 169, "column": 21}, "end": {"line": 169, "column": 35}}, {"start": {"line": 169, "column": 39}, "end": {"line": 169, "column": 68}}]}, "19": {"loc": {"start": {"line": 195, "column": 4}, "end": {"line": 197, "column": 5}}, "type": "if", "locations": [{"start": {"line": 195, "column": 4}, "end": {"line": 197, "column": 5}}]}, "20": {"loc": {"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 21}}, {"start": {"line": 195, "column": 25}, "end": {"line": 195, "column": 43}}]}, "21": {"loc": {"start": {"line": 199, "column": 4}, "end": {"line": 201, "column": 5}}, "type": "if", "locations": [{"start": {"line": 199, "column": 4}, "end": {"line": 201, "column": 5}}]}, "22": {"loc": {"start": {"line": 199, "column": 8}, "end": {"line": 199, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 199, "column": 8}, "end": {"line": 199, "column": 17}}, {"start": {"line": 199, "column": 21}, "end": {"line": 199, "column": 35}}, {"start": {"line": 199, "column": 39}, "end": {"line": 199, "column": 69}}]}, "23": {"loc": {"start": {"line": 206, "column": 6}, "end": {"line": 216, "column": 7}}, "type": "if", "locations": [{"start": {"line": 206, "column": 6}, "end": {"line": 216, "column": 7}}]}, "24": {"loc": {"start": {"line": 219, "column": 4}, "end": {"line": 221, "column": 5}}, "type": "if", "locations": [{"start": {"line": 219, "column": 4}, "end": {"line": 221, "column": 5}}]}, "25": {"loc": {"start": {"line": 241, "column": 31}, "end": {"line": 241, "column": 88}}, "type": "binary-expr", "locations": [{"start": {"line": 241, "column": 31}, "end": {"line": 241, "column": 63}}, {"start": {"line": 241, "column": 67}, "end": {"line": 241, "column": 88}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 1, "95": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0], "6": [0], "7": [0], "8": [0], "9": [0], "10": [0], "11": [0, 0], "12": [0], "13": [0, 0], "14": [0], "15": [0, 0], "16": [0], "17": [0], "18": [0, 0, 0], "19": [0], "20": [0, 0], "21": [0], "22": [0, 0, 0], "23": [0], "24": [0], "25": [0, 0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/process-mining.service.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/process-mining.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 37}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 56}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": null}}, "5": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 47}}, "6": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 33}}, "7": {"start": {"line": 81, "column": 33}, "end": {"line": 741, "column": null}}, "8": {"start": {"line": 86, "column": 21}, "end": {"line": 86, "column": 41}}, "9": {"start": {"line": 88, "column": 21}, "end": {"line": 88, "column": 47}}, "10": {"start": {"line": 89, "column": 21}, "end": {"line": 89, "column": 35}}, "11": {"start": {"line": 82, "column": 19}, "end": {"line": 82, "column": 66}}, "12": {"start": {"line": 96, "column": 4}, "end": {"line": 98, "column": 6}}, "13": {"start": {"line": 101, "column": 4}, "end": {"line": 112, "column": 5}}, "14": {"start": {"line": 102, "column": 27}, "end": {"line": 104, "column": null}}, "15": {"start": {"line": 106, "column": 6}, "end": {"line": 111, "column": 7}}, "16": {"start": {"line": 107, "column": 8}, "end": {"line": 109, "column": 10}}, "17": {"start": {"line": 110, "column": 8}, "end": {"line": 110, "column": 28}}, "18": {"start": {"line": 115, "column": 26}, "end": {"line": 117, "column": 6}}, "19": {"start": {"line": 119, "column": 4}, "end": {"line": 121, "column": 5}}, "20": {"start": {"line": 120, "column": 6}, "end": {"line": 120, "column": 36}}, "21": {"start": {"line": 124, "column": 23}, "end": {"line": 124, "column": 28}}, "22": {"start": {"line": 125, "column": 4}, "end": {"line": 127, "column": 5}}, "23": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 67}}, "24": {"start": {"line": 130, "column": 22}, "end": {"line": 133, "column": 6}}, "25": {"start": {"line": 136, "column": 27}, "end": {"line": 136, "column": 66}}, "26": {"start": {"line": 139, "column": 23}, "end": {"line": 139, "column": 52}}, "27": {"start": {"line": 142, "column": 30}, "end": {"line": 142, "column": 72}}, "28": {"start": {"line": 145, "column": 28}, "end": {"line": 145, "column": 69}}, "29": {"start": {"line": 148, "column": 32}, "end": {"line": 148, "column": 77}}, "30": {"start": {"line": 151, "column": 30}, "end": {"line": 151, "column": 73}}, "31": {"start": {"line": 154, "column": 28}, "end": {"line": 154, "column": 63}}, "32": {"start": {"line": 155, "column": 26}, "end": {"line": 155, "column": 59}}, "33": {"start": {"line": 158, "column": 26}, "end": {"line": 158, "column": 65}}, "34": {"start": {"line": 160, "column": 6}, "end": {"line": 161, "column": 26}}, "35": {"start": {"line": 160, "column": 46}, "end": {"line": 160, "column": 60}}, "36": {"start": {"line": 164, "column": 29}, "end": {"line": 175, "column": null}}, "37": {"start": {"line": 166, "column": 30}, "end": {"line": 166, "column": 57}}, "38": {"start": {"line": 167, "column": 8}, "end": {"line": 174, "column": 10}}, "39": {"start": {"line": 179, "column": 29}, "end": {"line": 191, "column": null}}, "40": {"start": {"line": 181, "column": 33}, "end": {"line": 181, "column": 55}}, "41": {"start": {"line": 182, "column": 30}, "end": {"line": 182, "column": 59}}, "42": {"start": {"line": 183, "column": 8}, "end": {"line": 190, "column": 10}}, "43": {"start": {"line": 194, "column": 30}, "end": {"line": 204, "column": 6}}, "44": {"start": {"line": 207, "column": 4}, "end": {"line": 212, "column": 6}}, "45": {"start": {"line": 214, "column": 4}, "end": {"line": 214, "column": 79}}, "46": {"start": {"line": 215, "column": 4}, "end": {"line": 215, "column": 18}}, "47": {"start": {"line": 220, "column": 26}, "end": {"line": 222, "column": 6}}, "48": {"start": {"line": 224, "column": 23}, "end": {"line": 224, "column": 28}}, "49": {"start": {"line": 225, "column": 4}, "end": {"line": 227, "column": 5}}, "50": {"start": {"line": 226, "column": 6}, "end": {"line": 226, "column": 67}}, "51": {"start": {"line": 229, "column": 22}, "end": {"line": 232, "column": 6}}, "52": {"start": {"line": 234, "column": 23}, "end": {"line": 234, "column": 52}}, "53": {"start": {"line": 235, "column": 21}, "end": {"line": 235, "column": null}}, "54": {"start": {"line": 241, "column": 4}, "end": {"line": 253, "column": 7}}, "55": {"start": {"line": 242, "column": 19}, "end": {"line": 242, "column": 56}}, "56": {"start": {"line": 242, "column": 41}, "end": {"line": 242, "column": 55}}, "57": {"start": {"line": 243, "column": 25}, "end": {"line": 243, "column": 42}}, "58": {"start": {"line": 245, "column": 23}, "end": {"line": 245, "column": 57}}, "59": {"start": {"line": 247, "column": 6}, "end": {"line": 249, "column": 7}}, "60": {"start": {"line": 248, "column": 8}, "end": {"line": 248, "column": 63}}, "61": {"start": {"line": 251, "column": 6}, "end": {"line": 251, "column": 51}}, "62": {"start": {"line": 252, "column": 6}, "end": {"line": 252, "column": 57}}, "63": {"start": {"line": 255, "column": 23}, "end": {"line": 255, "column": 53}}, "64": {"start": {"line": 256, "column": 27}, "end": {"line": 269, "column": 48}}, "65": {"start": {"line": 259, "column": 10}, "end": {"line": 259, "column": 79}}, "66": {"start": {"line": 259, "column": 44}, "end": {"line": 259, "column": 51}}, "67": {"start": {"line": 260, "column": 8}, "end": {"line": 267, "column": 10}}, "68": {"start": {"line": 269, "column": 22}, "end": {"line": 269, "column": 47}}, "69": {"start": {"line": 271, "column": 34}, "end": {"line": 280, "column": 6}}, "70": {"start": {"line": 278, "column": 30}, "end": {"line": 278, "column": 48}}, "71": {"start": {"line": 282, "column": 4}, "end": {"line": 286, "column": 6}}, "72": {"start": {"line": 287, "column": 4}, "end": {"line": 287, "column": 18}}, "73": {"start": {"line": 292, "column": 26}, "end": {"line": 294, "column": 6}}, "74": {"start": {"line": 296, "column": 4}, "end": {"line": 298, "column": 5}}, "75": {"start": {"line": 297, "column": 6}, "end": {"line": 297, "column": 36}}, "76": {"start": {"line": 300, "column": 23}, "end": {"line": 300, "column": 28}}, "77": {"start": {"line": 301, "column": 4}, "end": {"line": 303, "column": 5}}, "78": {"start": {"line": 302, "column": 6}, "end": {"line": 302, "column": 67}}, "79": {"start": {"line": 305, "column": 22}, "end": {"line": 308, "column": 6}}, "80": {"start": {"line": 310, "column": 23}, "end": {"line": 310, "column": 52}}, "81": {"start": {"line": 313, "column": 26}, "end": {"line": 314, "column": null}}, "82": {"start": {"line": 314, "column": 6}, "end": {"line": 314, "column": 40}}, "83": {"start": {"line": 316, "column": 4}, "end": {"line": 316, "column": 40}}, "84": {"start": {"line": 316, "column": 33}, "end": {"line": 316, "column": 38}}, "85": {"start": {"line": 318, "column": 27}, "end": {"line": 324, "column": 6}}, "86": {"start": {"line": 320, "column": 41}, "end": {"line": 320, "column": 48}}, "87": {"start": {"line": 327, "column": 26}, "end": {"line": 327, "column": null}}, "88": {"start": {"line": 332, "column": 4}, "end": {"line": 363, "column": 7}}, "89": {"start": {"line": 333, "column": 6}, "end": {"line": 362, "column": 9}}, "90": {"start": {"line": 334, "column": 8}, "end": {"line": 340, "column": 9}}, "91": {"start": {"line": 335, "column": 10}, "end": {"line": 339, "column": 13}}, "92": {"start": {"line": 342, "column": 22}, "end": {"line": 342, "column": 56}}, "93": {"start": {"line": 343, "column": 8}, "end": {"line": 343, "column": 26}}, "94": {"start": {"line": 346, "column": 8}, "end": {"line": 350, "column": 9}}, "95": {"start": {"line": 348, "column": 12}, "end": {"line": 348, "column": 77}}, "96": {"start": {"line": 349, "column": 10}, "end": {"line": 349, "column": 47}}, "97": {"start": {"line": 353, "column": 8}, "end": {"line": 361, "column": 9}}, "98": {"start": {"line": 354, "column": 28}, "end": {"line": 354, "column": 45}}, "99": {"start": {"line": 356, "column": 12}, "end": {"line": 356, "column": 69}}, "100": {"start": {"line": 357, "column": 10}, "end": {"line": 357, "column": 41}}, "101": {"start": {"line": 360, "column": 10}, "end": {"line": 360, "column": 47}}, "102": {"start": {"line": 365, "column": 31}, "end": {"line": 384, "column": null}}, "103": {"start": {"line": 369, "column": 10}, "end": {"line": 372, "column": 15}}, "104": {"start": {"line": 370, "column": 49}, "end": {"line": 370, "column": 56}}, "105": {"start": {"line": 374, "column": 8}, "end": {"line": 383, "column": 10}}, "106": {"start": {"line": 380, "column": 54}, "end": {"line": 380, "column": 61}}, "107": {"start": {"line": 388, "column": 24}, "end": {"line": 397, "column": 18}}, "108": {"start": {"line": 389, "column": 24}, "end": {"line": 389, "column": 47}}, "109": {"start": {"line": 390, "column": 22}, "end": {"line": 395, "column": 8}}, "110": {"start": {"line": 396, "column": 22}, "end": {"line": 396, "column": 41}}, "111": {"start": {"line": 399, "column": 38}, "end": {"line": 403, "column": 6}}, "112": {"start": {"line": 405, "column": 4}, "end": {"line": 409, "column": 6}}, "113": {"start": {"line": 410, "column": 4}, "end": {"line": 410, "column": 18}}, "114": {"start": {"line": 414, "column": 4}, "end": {"line": 423, "column": 6}}, "115": {"start": {"line": 416, "column": 8}, "end": {"line": 418, "column": 9}}, "116": {"start": {"line": 417, "column": 10}, "end": {"line": 417, "column": 36}}, "117": {"start": {"line": 419, "column": 8}, "end": {"line": 419, "column": 41}}, "118": {"start": {"line": 420, "column": 8}, "end": {"line": 420, "column": 22}}, "119": {"start": {"line": 429, "column": 4}, "end": {"line": 435, "column": 6}}, "120": {"start": {"line": 431, "column": 8}, "end": {"line": 431, "column": 63}}, "121": {"start": {"line": 432, "column": 8}, "end": {"line": 432, "column": 20}}, "122": {"start": {"line": 441, "column": 46}, "end": {"line": 441, "column": 48}}, "123": {"start": {"line": 443, "column": 4}, "end": {"line": 448, "column": 7}}, "124": {"start": {"line": 444, "column": 6}, "end": {"line": 447, "column": 7}}, "125": {"start": {"line": 444, "column": 19}, "end": {"line": 444, "column": 20}}, "126": {"start": {"line": 445, "column": 25}, "end": {"line": 445, "column": 77}}, "127": {"start": {"line": 446, "column": 8}, "end": {"line": 446, "column": 61}}, "128": {"start": {"line": 450, "column": 4}, "end": {"line": 450, "column": 21}}, "129": {"start": {"line": 467, "column": 54}, "end": {"line": 467, "column": 56}}, "130": {"start": {"line": 470, "column": 4}, "end": {"line": 481, "column": 7}}, "131": {"start": {"line": 471, "column": 6}, "end": {"line": 480, "column": 7}}, "132": {"start": {"line": 471, "column": 19}, "end": {"line": 471, "column": 20}}, "133": {"start": {"line": 472, "column": 25}, "end": {"line": 472, "column": 77}}, "134": {"start": {"line": 474, "column": 10}, "end": {"line": 474, "column": 75}}, "135": {"start": {"line": 476, "column": 8}, "end": {"line": 478, "column": 9}}, "136": {"start": {"line": 477, "column": 10}, "end": {"line": 477, "column": 41}}, "137": {"start": {"line": 479, "column": 8}, "end": {"line": 479, "column": 49}}, "138": {"start": {"line": 492, "column": 8}, "end": {"line": 492, "column": 10}}, "139": {"start": {"line": 494, "column": 4}, "end": {"line": 508, "column": 7}}, "140": {"start": {"line": 495, "column": 6}, "end": {"line": 507, "column": 7}}, "141": {"start": {"line": 497, "column": 10}, "end": {"line": 497, "column": 69}}, "142": {"start": {"line": 497, "column": 39}, "end": {"line": 497, "column": 46}}, "143": {"start": {"line": 498, "column": 28}, "end": {"line": 498, "column": 50}}, "144": {"start": {"line": 499, "column": 28}, "end": {"line": 499, "column": 50}}, "145": {"start": {"line": 501, "column": 8}, "end": {"line": 506, "column": 10}}, "146": {"start": {"line": 510, "column": 4}, "end": {"line": 510, "column": 18}}, "147": {"start": {"line": 522, "column": 52}, "end": {"line": 522, "column": 54}}, "148": {"start": {"line": 525, "column": 4}, "end": {"line": 543, "column": 7}}, "149": {"start": {"line": 526, "column": 6}, "end": {"line": 542, "column": 7}}, "150": {"start": {"line": 526, "column": 19}, "end": {"line": 526, "column": 20}}, "151": {"start": {"line": 527, "column": 25}, "end": {"line": 527, "column": 43}}, "152": {"start": {"line": 529, "column": 8}, "end": {"line": 531, "column": 9}}, "153": {"start": {"line": 530, "column": 10}, "end": {"line": 530, "column": 39}}, "154": {"start": {"line": 534, "column": 8}, "end": {"line": 541, "column": 9}}, "155": {"start": {"line": 536, "column": 12}, "end": {"line": 536, "column": 77}}, "156": {"start": {"line": 537, "column": 10}, "end": {"line": 537, "column": 49}}, "157": {"start": {"line": 540, "column": 10}, "end": {"line": 540, "column": 42}}, "158": {"start": {"line": 549, "column": 8}, "end": {"line": 549, "column": 10}}, "159": {"start": {"line": 551, "column": 4}, "end": {"line": 571, "column": 7}}, "160": {"start": {"line": 552, "column": 29}, "end": {"line": 552, "column": 59}}, "161": {"start": {"line": 552, "column": 53}, "end": {"line": 552, "column": 58}}, "162": {"start": {"line": 553, "column": 6}, "end": {"line": 570, "column": 7}}, "163": {"start": {"line": 555, "column": 10}, "end": {"line": 555, "column": 79}}, "164": {"start": {"line": 555, "column": 44}, "end": {"line": 555, "column": 51}}, "165": {"start": {"line": 556, "column": 28}, "end": {"line": 556, "column": 55}}, "166": {"start": {"line": 557, "column": 28}, "end": {"line": 557, "column": 55}}, "167": {"start": {"line": 559, "column": 8}, "end": {"line": 563, "column": 10}}, "168": {"start": {"line": 565, "column": 8}, "end": {"line": 569, "column": 10}}, "169": {"start": {"line": 573, "column": 4}, "end": {"line": 573, "column": 18}}, "170": {"start": {"line": 577, "column": 28}, "end": {"line": 577, "column": 45}}, "171": {"start": {"line": 578, "column": 4}, "end": {"line": 582, "column": 7}}, "172": {"start": {"line": 579, "column": 6}, "end": {"line": 581, "column": 7}}, "173": {"start": {"line": 580, "column": 8}, "end": {"line": 580, "column": 48}}, "174": {"start": {"line": 583, "column": 4}, "end": {"line": 583, "column": 39}}, "175": {"start": {"line": 587, "column": 26}, "end": {"line": 587, "column": 43}}, "176": {"start": {"line": 588, "column": 4}, "end": {"line": 592, "column": 7}}, "177": {"start": {"line": 589, "column": 6}, "end": {"line": 591, "column": 7}}, "178": {"start": {"line": 590, "column": 8}, "end": {"line": 590, "column": 62}}, "179": {"start": {"line": 593, "column": 4}, "end": {"line": 593, "column": 37}}, "180": {"start": {"line": 599, "column": 4}, "end": {"line": 601, "column": 6}}, "181": {"start": {"line": 600, "column": 6}, "end": {"line": 600, "column": 40}}, "182": {"start": {"line": 605, "column": 4}, "end": {"line": 605, "column": 36}}, "183": {"start": {"line": 605, "column": 27}, "end": {"line": 605, "column": 36}}, "184": {"start": {"line": 606, "column": 18}, "end": {"line": 606, "column": 47}}, "185": {"start": {"line": 607, "column": 16}, "end": {"line": 607, "column": 61}}, "186": {"start": {"line": 608, "column": 4}, "end": {"line": 608, "column": 23}}, "187": {"start": {"line": 616, "column": 27}, "end": {"line": 616, "column": 47}}, "188": {"start": {"line": 617, "column": 4}, "end": {"line": 617, "column": 41}}, "189": {"start": {"line": 618, "column": 4}, "end": {"line": 618, "column": 47}}, "190": {"start": {"line": 619, "column": 4}, "end": {"line": 619, "column": 43}}, "191": {"start": {"line": 621, "column": 4}, "end": {"line": 621, "column": 61}}, "192": {"start": {"line": 635, "column": 20}, "end": {"line": 635, "column": 70}}, "193": {"start": {"line": 638, "column": 27}, "end": {"line": 638, "column": 47}}, "194": {"start": {"line": 639, "column": 4}, "end": {"line": 639, "column": 41}}, "195": {"start": {"line": 640, "column": 4}, "end": {"line": 640, "column": 47}}, "196": {"start": {"line": 641, "column": 4}, "end": {"line": 641, "column": 43}}, "197": {"start": {"line": 642, "column": 4}, "end": {"line": 642, "column": 53}}, "198": {"start": {"line": 643, "column": 4}, "end": {"line": 643, "column": 37}}, "199": {"start": {"line": 644, "column": 4}, "end": {"line": 644, "column": 59}}, "200": {"start": {"line": 645, "column": 4}, "end": {"line": 645, "column": 39}}, "201": {"start": {"line": 646, "column": 4}, "end": {"line": 646, "column": 42}}, "202": {"start": {"line": 647, "column": 4}, "end": {"line": 647, "column": 64}}, "203": {"start": {"line": 651, "column": 6}, "end": {"line": 651, "column": 62}}, "204": {"start": {"line": 654, "column": 22}, "end": {"line": 654, "column": 70}}, "205": {"start": {"line": 655, "column": 4}, "end": {"line": 660, "column": 6}}, "206": {"start": {"line": 663, "column": 4}, "end": {"line": 665, "column": 5}}, "207": {"start": {"line": 664, "column": 6}, "end": {"line": 664, "column": 75}}, "208": {"start": {"line": 667, "column": 4}, "end": {"line": 667, "column": 73}}, "209": {"start": {"line": 668, "column": 4}, "end": {"line": 668, "column": 23}}, "210": {"start": {"line": 675, "column": 4}, "end": {"line": 678, "column": 7}}, "211": {"start": {"line": 689, "column": 19}, "end": {"line": 691, "column": null}}, "212": {"start": {"line": 693, "column": 4}, "end": {"line": 695, "column": 5}}, "213": {"start": {"line": 694, "column": 6}, "end": {"line": 694, "column": 31}}, "214": {"start": {"line": 698, "column": 21}, "end": {"line": 698, "column": 74}}, "215": {"start": {"line": 699, "column": 4}, "end": {"line": 703, "column": 5}}, "216": {"start": {"line": 701, "column": 6}, "end": {"line": 701, "column": 52}}, "217": {"start": {"line": 702, "column": 6}, "end": {"line": 702, "column": 33}}, "218": {"start": {"line": 705, "column": 4}, "end": {"line": 705, "column": 16}}, "219": {"start": {"line": 712, "column": 21}, "end": {"line": 714, "column": 16}}, "220": {"start": {"line": 713, "column": 20}, "end": {"line": 713, "column": 78}}, "221": {"start": {"line": 715, "column": 4}, "end": {"line": 715, "column": 70}}, "222": {"start": {"line": 725, "column": 4}, "end": {"line": 725, "column": 74}}, "223": {"start": {"line": 735, "column": 19}, "end": {"line": 738, "column": 6}}, "224": {"start": {"line": 739, "column": 4}, "end": {"line": 739, "column": 43}}, "225": {"start": {"line": 81, "column": 13}, "end": {"line": 81, "column": 33}}, "226": {"start": {"line": 81, "column": 13}, "end": {"line": 741, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 84, "column": 2}, "end": {"line": 84, "column": null}}, "loc": {"start": {"line": 89, "column": 47}, "end": {"line": 90, "column": 6}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 92, "column": 2}, "end": {"line": 92, "column": 7}}, "loc": {"start": {"line": 94, "column": 24}, "end": {"line": 216, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 160, "column": 27}, "end": {"line": 160, "column": 28}}, "loc": {"start": {"line": 160, "column": 46}, "end": {"line": 160, "column": 60}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 165, "column": 6}, "end": {"line": 165, "column": 7}}, "loc": {"start": {"line": 165, "column": 32}, "end": {"line": 175, "column": 7}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 180, "column": 6}, "end": {"line": 180, "column": 7}}, "loc": {"start": {"line": 180, "column": 32}, "end": {"line": 191, "column": 7}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 218, "column": 2}, "end": {"line": 218, "column": 7}}, "loc": {"start": {"line": 218, "column": 41}, "end": {"line": 288, "column": 3}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 241, "column": 39}, "end": {"line": 241, "column": 40}}, "loc": {"start": {"line": 241, "column": 60}, "end": {"line": 253, "column": 5}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 242, "column": 30}, "end": {"line": 242, "column": 31}}, "loc": {"start": {"line": 242, "column": 41}, "end": {"line": 242, "column": 55}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 257, "column": 11}, "end": {"line": 257, "column": 12}}, "loc": {"start": {"line": 257, "column": 35}, "end": {"line": 268, "column": 7}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 259, "column": 32}, "end": {"line": 259, "column": 33}}, "loc": {"start": {"line": 259, "column": 44}, "end": {"line": 259, "column": 51}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 269, "column": 12}, "end": {"line": 269, "column": 13}}, "loc": {"start": {"line": 269, "column": 22}, "end": {"line": 269, "column": 47}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 278, "column": 18}, "end": {"line": 278, "column": 19}}, "loc": {"start": {"line": 278, "column": 30}, "end": {"line": 278, "column": 48}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 290, "column": 2}, "end": {"line": 290, "column": 7}}, "loc": {"start": {"line": 290, "column": 44}, "end": {"line": 411, "column": 3}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 313, "column": 56}, "end": {"line": 313, "column": 57}}, "loc": {"start": {"line": 314, "column": 6}, "end": {"line": 314, "column": 40}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 316, "column": 23}, "end": {"line": 316, "column": 24}}, "loc": {"start": {"line": 316, "column": 33}, "end": {"line": 316, "column": 38}}}, "15": {"name": "(anonymous_19)", "decl": {"start": {"line": 320, "column": 29}, "end": {"line": 320, "column": 30}}, "loc": {"start": {"line": 320, "column": 41}, "end": {"line": 320, "column": 48}}}, "16": {"name": "(anonymous_20)", "decl": {"start": {"line": 332, "column": 38}, "end": {"line": 332, "column": 39}}, "loc": {"start": {"line": 332, "column": 49}, "end": {"line": 363, "column": 5}}}, "17": {"name": "(anonymous_21)", "decl": {"start": {"line": 333, "column": 21}, "end": {"line": 333, "column": 22}}, "loc": {"start": {"line": 333, "column": 38}, "end": {"line": 362, "column": 7}}}, "18": {"name": "(anonymous_22)", "decl": {"start": {"line": 366, "column": 6}, "end": {"line": 366, "column": 7}}, "loc": {"start": {"line": 366, "column": 28}, "end": {"line": 384, "column": 7}}}, "19": {"name": "(anonymous_23)", "decl": {"start": {"line": 370, "column": 37}, "end": {"line": 370, "column": 38}}, "loc": {"start": {"line": 370, "column": 49}, "end": {"line": 370, "column": 56}}}, "20": {"name": "(anonymous_24)", "decl": {"start": {"line": 380, "column": 42}, "end": {"line": 380, "column": 43}}, "loc": {"start": {"line": 380, "column": 54}, "end": {"line": 380, "column": 61}}}, "21": {"name": "(anonymous_25)", "decl": {"start": {"line": 389, "column": 14}, "end": {"line": 389, "column": 15}}, "loc": {"start": {"line": 389, "column": 24}, "end": {"line": 389, "column": 47}}}, "22": {"name": "(anonymous_26)", "decl": {"start": {"line": 390, "column": 11}, "end": {"line": 390, "column": 12}}, "loc": {"start": {"line": 390, "column": 22}, "end": {"line": 395, "column": 8}}}, "23": {"name": "(anonymous_27)", "decl": {"start": {"line": 396, "column": 12}, "end": {"line": 396, "column": 13}}, "loc": {"start": {"line": 396, "column": 22}, "end": {"line": 396, "column": 41}}}, "24": {"name": "(anonymous_28)", "decl": {"start": {"line": 413, "column": 10}, "end": {"line": 413, "column": 23}}, "loc": {"start": {"line": 413, "column": 45}, "end": {"line": 424, "column": 3}}}, "25": {"name": "(anonymous_29)", "decl": {"start": {"line": 415, "column": 6}, "end": {"line": 415, "column": 7}}, "loc": {"start": {"line": 415, "column": 24}, "end": {"line": 421, "column": 7}}}, "26": {"name": "(anonymous_30)", "decl": {"start": {"line": 426, "column": 10}, "end": {"line": 426, "column": 36}}, "loc": {"start": {"line": 427, "column": 25}, "end": {"line": 436, "column": 3}}}, "27": {"name": "(anonymous_31)", "decl": {"start": {"line": 430, "column": 6}, "end": {"line": 430, "column": 7}}, "loc": {"start": {"line": 430, "column": 22}, "end": {"line": 433, "column": 7}}}, "28": {"name": "(anonymous_32)", "decl": {"start": {"line": 438, "column": 10}, "end": {"line": 438, "column": 34}}, "loc": {"start": {"line": 439, "column": 42}, "end": {"line": 451, "column": 3}}}, "29": {"name": "(anonymous_33)", "decl": {"start": {"line": 443, "column": 38}, "end": {"line": 443, "column": 39}}, "loc": {"start": {"line": 443, "column": 49}, "end": {"line": 448, "column": 5}}}, "30": {"name": "(anonymous_34)", "decl": {"start": {"line": 456, "column": 10}, "end": {"line": 456, "column": 38}}, "loc": {"start": {"line": 457, "column": 42}, "end": {"line": 511, "column": 3}}}, "31": {"name": "(anonymous_35)", "decl": {"start": {"line": 470, "column": 38}, "end": {"line": 470, "column": 39}}, "loc": {"start": {"line": 470, "column": 49}, "end": {"line": 481, "column": 5}}}, "32": {"name": "(anonymous_36)", "decl": {"start": {"line": 494, "column": 44}, "end": {"line": 494, "column": 45}}, "loc": {"start": {"line": 494, "column": 70}, "end": {"line": 508, "column": 5}}}, "33": {"name": "(anonymous_37)", "decl": {"start": {"line": 497, "column": 27}, "end": {"line": 497, "column": 28}}, "loc": {"start": {"line": 497, "column": 39}, "end": {"line": 497, "column": 46}}}, "34": {"name": "(anonymous_38)", "decl": {"start": {"line": 516, "column": 10}, "end": {"line": 516, "column": 36}}, "loc": {"start": {"line": 517, "column": 42}, "end": {"line": 574, "column": 3}}}, "35": {"name": "(anonymous_39)", "decl": {"start": {"line": 525, "column": 38}, "end": {"line": 525, "column": 39}}, "loc": {"start": {"line": 525, "column": 49}, "end": {"line": 543, "column": 5}}}, "36": {"name": "(anonymous_40)", "decl": {"start": {"line": 551, "column": 42}, "end": {"line": 551, "column": 43}}, "loc": {"start": {"line": 551, "column": 68}, "end": {"line": 571, "column": 5}}}, "37": {"name": "(anonymous_41)", "decl": {"start": {"line": 552, "column": 46}, "end": {"line": 552, "column": 47}}, "loc": {"start": {"line": 552, "column": 53}, "end": {"line": 552, "column": 58}}}, "38": {"name": "(anonymous_42)", "decl": {"start": {"line": 555, "column": 32}, "end": {"line": 555, "column": 33}}, "loc": {"start": {"line": 555, "column": 44}, "end": {"line": 555, "column": 51}}}, "39": {"name": "(anonymous_43)", "decl": {"start": {"line": 576, "column": 10}, "end": {"line": 576, "column": 28}}, "loc": {"start": {"line": 576, "column": 67}, "end": {"line": 584, "column": 3}}}, "40": {"name": "(anonymous_44)", "decl": {"start": {"line": 578, "column": 38}, "end": {"line": 578, "column": 39}}, "loc": {"start": {"line": 578, "column": 49}, "end": {"line": 582, "column": 5}}}, "41": {"name": "(anonymous_45)", "decl": {"start": {"line": 586, "column": 10}, "end": {"line": 586, "column": 26}}, "loc": {"start": {"line": 586, "column": 65}, "end": {"line": 594, "column": 3}}}, "42": {"name": "(anonymous_46)", "decl": {"start": {"line": 588, "column": 38}, "end": {"line": 588, "column": 39}}, "loc": {"start": {"line": 588, "column": 49}, "end": {"line": 592, "column": 5}}}, "43": {"name": "(anonymous_47)", "decl": {"start": {"line": 596, "column": 10}, "end": {"line": 596, "column": 32}}, "loc": {"start": {"line": 597, "column": 42}, "end": {"line": 602, "column": 3}}}, "44": {"name": "(anonymous_48)", "decl": {"start": {"line": 599, "column": 41}, "end": {"line": 599, "column": 42}}, "loc": {"start": {"line": 600, "column": 6}, "end": {"line": 600, "column": 40}}}, "45": {"name": "(anonymous_49)", "decl": {"start": {"line": 604, "column": 10}, "end": {"line": 604, "column": 31}}, "loc": {"start": {"line": 604, "column": 50}, "end": {"line": 609, "column": 3}}}, "46": {"name": "(anonymous_50)", "decl": {"start": {"line": 611, "column": 10}, "end": {"line": 611, "column": 15}}, "loc": {"start": {"line": 614, "column": 19}, "end": {"line": 622, "column": 3}}}, "47": {"name": "(anonymous_51)", "decl": {"start": {"line": 627, "column": 10}, "end": {"line": 627, "column": 15}}, "loc": {"start": {"line": 632, "column": 19}, "end": {"line": 669, "column": 3}}}, "48": {"name": "(anonymous_52)", "decl": {"start": {"line": 671, "column": 2}, "end": {"line": 671, "column": 7}}, "loc": {"start": {"line": 673, "column": 30}, "end": {"line": 679, "column": 3}}}, "49": {"name": "(anonymous_53)", "decl": {"start": {"line": 684, "column": 10}, "end": {"line": 684, "column": 15}}, "loc": {"start": {"line": 686, "column": 30}, "end": {"line": 706, "column": 3}}}, "50": {"name": "(anonymous_54)", "decl": {"start": {"line": 711, "column": 10}, "end": {"line": 711, "column": 33}}, "loc": {"start": {"line": 711, "column": 55}, "end": {"line": 716, "column": 3}}}, "51": {"name": "(anonymous_55)", "decl": {"start": {"line": 713, "column": 11}, "end": {"line": 713, "column": 12}}, "loc": {"start": {"line": 713, "column": 20}, "end": {"line": 713, "column": 78}}}, "52": {"name": "(anonymous_56)", "decl": {"start": {"line": 721, "column": 10}, "end": {"line": 721, "column": 15}}, "loc": {"start": {"line": 723, "column": 23}, "end": {"line": 726, "column": 3}}}, "53": {"name": "(anonymous_57)", "decl": {"start": {"line": 731, "column": 10}, "end": {"line": 731, "column": 15}}, "loc": {"start": {"line": 733, "column": 30}, "end": {"line": 740, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 94, "column": 4}, "end": {"line": 94, "column": 24}}, "type": "default-arg", "locations": [{"start": {"line": 94, "column": 19}, "end": {"line": 94, "column": 24}}]}, "1": {"loc": {"start": {"line": 101, "column": 4}, "end": {"line": 112, "column": 5}}, "type": "if", "locations": [{"start": {"line": 101, "column": 4}, "end": {"line": 112, "column": 5}}]}, "2": {"loc": {"start": {"line": 106, "column": 6}, "end": {"line": 111, "column": 7}}, "type": "if", "locations": [{"start": {"line": 106, "column": 6}, "end": {"line": 111, "column": 7}}]}, "3": {"loc": {"start": {"line": 119, "column": 4}, "end": {"line": 121, "column": 5}}, "type": "if", "locations": [{"start": {"line": 119, "column": 4}, "end": {"line": 121, "column": 5}}]}, "4": {"loc": {"start": {"line": 125, "column": 4}, "end": {"line": 127, "column": 5}}, "type": "if", "locations": [{"start": {"line": 125, "column": 4}, "end": {"line": 127, "column": 5}}]}, "5": {"loc": {"start": {"line": 171, "column": 23}, "end": {"line": 171, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 171, "column": 23}, "end": {"line": 171, "column": 49}}, {"start": {"line": 171, "column": 53}, "end": {"line": 171, "column": 54}}]}, "6": {"loc": {"start": {"line": 172, "column": 23}, "end": {"line": 172, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 172, "column": 23}, "end": {"line": 172, "column": 49}}, {"start": {"line": 172, "column": 53}, "end": {"line": 172, "column": 54}}]}, "7": {"loc": {"start": {"line": 173, "column": 23}, "end": {"line": 173, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 173, "column": 23}, "end": {"line": 173, "column": 49}}, {"start": {"line": 173, "column": 53}, "end": {"line": 173, "column": 54}}]}, "8": {"loc": {"start": {"line": 187, "column": 23}, "end": {"line": 187, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 187, "column": 23}, "end": {"line": 187, "column": 49}}, {"start": {"line": 187, "column": 53}, "end": {"line": 187, "column": 54}}]}, "9": {"loc": {"start": {"line": 188, "column": 23}, "end": {"line": 188, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 188, "column": 23}, "end": {"line": 188, "column": 49}}, {"start": {"line": 188, "column": 53}, "end": {"line": 188, "column": 54}}]}, "10": {"loc": {"start": {"line": 189, "column": 23}, "end": {"line": 189, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 189, "column": 23}, "end": {"line": 189, "column": 49}}, {"start": {"line": 189, "column": 53}, "end": {"line": 189, "column": 54}}]}, "11": {"loc": {"start": {"line": 225, "column": 4}, "end": {"line": 227, "column": 5}}, "type": "if", "locations": [{"start": {"line": 225, "column": 4}, "end": {"line": 227, "column": 5}}]}, "12": {"loc": {"start": {"line": 247, "column": 6}, "end": {"line": 249, "column": 7}}, "type": "if", "locations": [{"start": {"line": 247, "column": 6}, "end": {"line": 249, "column": 7}}]}, "13": {"loc": {"start": {"line": 275, "column": 29}, "end": {"line": 275, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 275, "column": 29}, "end": {"line": 275, "column": 50}}, {"start": {"line": 275, "column": 54}, "end": {"line": 275, "column": 56}}]}, "14": {"loc": {"start": {"line": 296, "column": 4}, "end": {"line": 298, "column": 5}}, "type": "if", "locations": [{"start": {"line": 296, "column": 4}, "end": {"line": 298, "column": 5}}]}, "15": {"loc": {"start": {"line": 301, "column": 4}, "end": {"line": 303, "column": 5}}, "type": "if", "locations": [{"start": {"line": 301, "column": 4}, "end": {"line": 303, "column": 5}}]}, "16": {"loc": {"start": {"line": 334, "column": 8}, "end": {"line": 340, "column": 9}}, "type": "if", "locations": [{"start": {"line": 334, "column": 8}, "end": {"line": 340, "column": 9}}]}, "17": {"loc": {"start": {"line": 346, "column": 8}, "end": {"line": 350, "column": 9}}, "type": "if", "locations": [{"start": {"line": 346, "column": 8}, "end": {"line": 350, "column": 9}}]}, "18": {"loc": {"start": {"line": 353, "column": 8}, "end": {"line": 361, "column": 9}}, "type": "if", "locations": [{"start": {"line": 353, "column": 8}, "end": {"line": 361, "column": 9}}, {"start": {"line": 358, "column": 15}, "end": {"line": 361, "column": 9}}]}, "19": {"loc": {"start": {"line": 369, "column": 10}, "end": {"line": 372, "column": 15}}, "type": "cond-expr", "locations": [{"start": {"line": 370, "column": 14}, "end": {"line": 371, "column": 36}}, {"start": {"line": 372, "column": 14}, "end": {"line": 372, "column": 15}}]}, "20": {"loc": {"start": {"line": 379, "column": 12}, "end": {"line": 382, "column": 17}}, "type": "cond-expr", "locations": [{"start": {"line": 380, "column": 16}, "end": {"line": 381, "column": 41}}, {"start": {"line": 382, "column": 16}, "end": {"line": 382, "column": 17}}]}, "21": {"loc": {"start": {"line": 416, "column": 8}, "end": {"line": 418, "column": 9}}, "type": "if", "locations": [{"start": {"line": 416, "column": 8}, "end": {"line": 418, "column": 9}}]}, "22": {"loc": {"start": {"line": 431, "column": 32}, "end": {"line": 431, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 431, "column": 32}, "end": {"line": 431, "column": 52}}, {"start": {"line": 431, "column": 56}, "end": {"line": 431, "column": 57}}]}, "23": {"loc": {"start": {"line": 446, "column": 31}, "end": {"line": 446, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 446, "column": 31}, "end": {"line": 446, "column": 50}}, {"start": {"line": 446, "column": 54}, "end": {"line": 446, "column": 55}}]}, "24": {"loc": {"start": {"line": 476, "column": 8}, "end": {"line": 478, "column": 9}}, "type": "if", "locations": [{"start": {"line": 476, "column": 8}, "end": {"line": 478, "column": 9}}]}, "25": {"loc": {"start": {"line": 495, "column": 6}, "end": {"line": 507, "column": 7}}, "type": "if", "locations": [{"start": {"line": 495, "column": 6}, "end": {"line": 507, "column": 7}}]}, "26": {"loc": {"start": {"line": 529, "column": 8}, "end": {"line": 531, "column": 9}}, "type": "if", "locations": [{"start": {"line": 529, "column": 8}, "end": {"line": 531, "column": 9}}]}, "27": {"loc": {"start": {"line": 534, "column": 8}, "end": {"line": 541, "column": 9}}, "type": "if", "locations": [{"start": {"line": 534, "column": 8}, "end": {"line": 541, "column": 9}}, {"start": {"line": 538, "column": 15}, "end": {"line": 541, "column": 9}}]}, "28": {"loc": {"start": {"line": 553, "column": 6}, "end": {"line": 570, "column": 7}}, "type": "if", "locations": [{"start": {"line": 553, "column": 6}, "end": {"line": 570, "column": 7}}, {"start": {"line": 564, "column": 13}, "end": {"line": 570, "column": 7}}]}, "29": {"loc": {"start": {"line": 579, "column": 6}, "end": {"line": 581, "column": 7}}, "type": "if", "locations": [{"start": {"line": 579, "column": 6}, "end": {"line": 581, "column": 7}}]}, "30": {"loc": {"start": {"line": 589, "column": 6}, "end": {"line": 591, "column": 7}}, "type": "if", "locations": [{"start": {"line": 589, "column": 6}, "end": {"line": 591, "column": 7}}]}, "31": {"loc": {"start": {"line": 605, "column": 4}, "end": {"line": 605, "column": 36}}, "type": "if", "locations": [{"start": {"line": 605, "column": 4}, "end": {"line": 605, "column": 36}}]}, "32": {"loc": {"start": {"line": 632, "column": 4}, "end": {"line": 632, "column": 19}}, "type": "default-arg", "locations": [{"start": {"line": 632, "column": 15}, "end": {"line": 632, "column": 19}}]}, "33": {"loc": {"start": {"line": 644, "column": 36}, "end": {"line": 644, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 644, "column": 36}, "end": {"line": 644, "column": 50}}, {"start": {"line": 644, "column": 54}, "end": {"line": 644, "column": 58}}]}, "34": {"loc": {"start": {"line": 663, "column": 4}, "end": {"line": 665, "column": 5}}, "type": "if", "locations": [{"start": {"line": 663, "column": 4}, "end": {"line": 665, "column": 5}}]}, "35": {"loc": {"start": {"line": 693, "column": 4}, "end": {"line": 695, "column": 5}}, "type": "if", "locations": [{"start": {"line": 693, "column": 4}, "end": {"line": 695, "column": 5}}]}, "36": {"loc": {"start": {"line": 699, "column": 4}, "end": {"line": 703, "column": 5}}, "type": "if", "locations": [{"start": {"line": 699, "column": 4}, "end": {"line": 703, "column": 5}}]}, "37": {"loc": {"start": {"line": 699, "column": 8}, "end": {"line": 699, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 699, "column": 8}, "end": {"line": 699, "column": 16}}, {"start": {"line": 699, "column": 20}, "end": {"line": 699, "column": 42}}]}, "38": {"loc": {"start": {"line": 739, "column": 11}, "end": {"line": 739, "column": 42}}, "type": "cond-expr", "locations": [{"start": {"line": 739, "column": 20}, "end": {"line": 739, "column": 38}}, {"start": {"line": 739, "column": 41}, "end": {"line": 739, "column": 42}}]}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 2, "6": 2, "7": 2, "8": 9, "9": 9, "10": 9, "11": 9, "12": 3, "13": 3, "14": 2, "15": 2, "16": 1, "17": 1, "18": 2, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 2, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 1, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0, "145": 0, "146": 0, "147": 0, "148": 0, "149": 0, "150": 0, "151": 0, "152": 0, "153": 0, "154": 0, "155": 0, "156": 0, "157": 0, "158": 0, "159": 0, "160": 0, "161": 0, "162": 0, "163": 0, "164": 0, "165": 0, "166": 0, "167": 0, "168": 0, "169": 0, "170": 0, "171": 0, "172": 0, "173": 0, "174": 0, "175": 0, "176": 0, "177": 0, "178": 0, "179": 0, "180": 0, "181": 0, "182": 0, "183": 0, "184": 0, "185": 0, "186": 0, "187": 0, "188": 0, "189": 0, "190": 0, "191": 0, "192": 0, "193": 0, "194": 0, "195": 0, "196": 0, "197": 0, "198": 0, "199": 0, "200": 0, "201": 0, "202": 0, "203": 0, "204": 0, "205": 0, "206": 0, "207": 0, "208": 0, "209": 0, "210": 3, "211": 2, "212": 2, "213": 1, "214": 1, "215": 1, "216": 0, "217": 0, "218": 1, "219": 0, "220": 0, "221": 0, "222": 0, "223": 0, "224": 0, "225": 2, "226": 2}, "f": {"0": 9, "1": 3, "2": 0, "3": 0, "4": 0, "5": 2, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 1, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 3, "49": 2, "50": 0, "51": 0, "52": 0, "53": 0}, "b": {"0": [2], "1": [2], "2": [1], "3": [0], "4": [0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0], "12": [0], "13": [0, 0], "14": [0], "15": [0], "16": [0], "17": [0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0], "22": [0, 0], "23": [0, 0], "24": [0], "25": [0], "26": [0], "27": [0, 0], "28": [0, 0], "29": [0], "30": [0], "31": [0], "32": [0], "33": [0, 0], "34": [0], "35": [1], "36": [0], "37": [1, 0], "38": [0, 0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/subprocess-discovery.service.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/subprocess-discovery.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 37}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 56}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 82}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 47}}, "6": {"start": {"line": 62, "column": 39}, "end": {"line": 601, "column": null}}, "7": {"start": {"line": 67, "column": 21}, "end": {"line": 67, "column": 41}}, "8": {"start": {"line": 69, "column": 21}, "end": {"line": 69, "column": 47}}, "9": {"start": {"line": 70, "column": 21}, "end": {"line": 70, "column": 35}}, "10": {"start": {"line": 63, "column": 19}, "end": {"line": 63, "column": 72}}, "11": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 81}}, "12": {"start": {"line": 82, "column": 55}, "end": {"line": 92, "column": 6}}, "13": {"start": {"line": 95, "column": 22}, "end": {"line": 98, "column": 6}}, "14": {"start": {"line": 100, "column": 4}, "end": {"line": 102, "column": 5}}, "15": {"start": {"line": 101, "column": 6}, "end": {"line": 101, "column": 36}}, "16": {"start": {"line": 105, "column": 23}, "end": {"line": 105, "column": 52}}, "17": {"start": {"line": 108, "column": 31}, "end": {"line": 110, "column": null}}, "18": {"start": {"line": 114, "column": 29}, "end": {"line": 116, "column": 10}}, "19": {"start": {"line": 119, "column": 25}, "end": {"line": 121, "column": 10}}, "20": {"start": {"line": 124, "column": 27}, "end": {"line": 126, "column": null}}, "21": {"start": {"line": 130, "column": 28}, "end": {"line": 135, "column": 6}}, "22": {"start": {"line": 138, "column": 28}, "end": {"line": 141, "column": null}}, "23": {"start": {"line": 145, "column": 23}, "end": {"line": 148, "column": null}}, "24": {"start": {"line": 151, "column": 46}, "end": {"line": 155, "column": 6}}, "25": {"start": {"line": 158, "column": 4}, "end": {"line": 162, "column": 6}}, "26": {"start": {"line": 164, "column": 4}, "end": {"line": 166, "column": 6}}, "27": {"start": {"line": 168, "column": 4}, "end": {"line": 168, "column": 18}}, "28": {"start": {"line": 178, "column": 21}, "end": {"line": 178, "column": null}}, "29": {"start": {"line": 185, "column": 4}, "end": {"line": 212, "column": 7}}, "30": {"start": {"line": 186, "column": 25}, "end": {"line": 186, "column": 52}}, "31": {"start": {"line": 186, "column": 41}, "end": {"line": 186, "column": 51}}, "32": {"start": {"line": 188, "column": 6}, "end": {"line": 211, "column": 7}}, "33": {"start": {"line": 188, "column": 24}, "end": {"line": 188, "column": 41}}, "34": {"start": {"line": 189, "column": 8}, "end": {"line": 210, "column": 9}}, "35": {"start": {"line": 189, "column": 21}, "end": {"line": 189, "column": 22}}, "36": {"start": {"line": 190, "column": 27}, "end": {"line": 190, "column": 58}}, "37": {"start": {"line": 191, "column": 30}, "end": {"line": 191, "column": 51}}, "38": {"start": {"line": 193, "column": 10}, "end": {"line": 199, "column": 11}}, "39": {"start": {"line": 194, "column": 12}, "end": {"line": 198, "column": 15}}, "40": {"start": {"line": 201, "column": 26}, "end": {"line": 201, "column": 52}}, "41": {"start": {"line": 202, "column": 10}, "end": {"line": 209, "column": 11}}, "42": {"start": {"line": 203, "column": 12}, "end": {"line": 203, "column": 39}}, "43": {"start": {"line": 206, "column": 30}, "end": {"line": 206, "column": 59}}, "44": {"start": {"line": 207, "column": 28}, "end": {"line": 207, "column": 70}}, "45": {"start": {"line": 208, "column": 12}, "end": {"line": 208, "column": 56}}, "46": {"start": {"line": 215, "column": 4}, "end": {"line": 228, "column": 49}}, "47": {"start": {"line": 216, "column": 29}, "end": {"line": 216, "column": 70}}, "48": {"start": {"line": 217, "column": 44}, "end": {"line": 226, "column": 8}}, "49": {"start": {"line": 222, "column": 55}, "end": {"line": 222, "column": 62}}, "50": {"start": {"line": 227, "column": 25}, "end": {"line": 227, "column": 74}}, "51": {"start": {"line": 228, "column": 22}, "end": {"line": 228, "column": 47}}, "52": {"start": {"line": 238, "column": 50}, "end": {"line": 238, "column": 52}}, "53": {"start": {"line": 241, "column": 4}, "end": {"line": 255, "column": 7}}, "54": {"start": {"line": 243, "column": 6}, "end": {"line": 254, "column": 7}}, "55": {"start": {"line": 243, "column": 19}, "end": {"line": 243, "column": 20}}, "56": {"start": {"line": 244, "column": 8}, "end": {"line": 253, "column": 9}}, "57": {"start": {"line": 244, "column": 21}, "end": {"line": 244, "column": 26}}, "58": {"start": {"line": 245, "column": 25}, "end": {"line": 245, "column": 34}}, "59": {"start": {"line": 246, "column": 25}, "end": {"line": 246, "column": 34}}, "60": {"start": {"line": 249, "column": 27}, "end": {"line": 249, "column": 92}}, "61": {"start": {"line": 250, "column": 10}, "end": {"line": 252, "column": 11}}, "62": {"start": {"line": 257, "column": 4}, "end": {"line": 257, "column": 28}}, "63": {"start": {"line": 267, "column": 46}, "end": {"line": 267, "column": 48}}, "64": {"start": {"line": 269, "column": 4}, "end": {"line": 285, "column": 7}}, "65": {"start": {"line": 270, "column": 25}, "end": {"line": 270, "column": 52}}, "66": {"start": {"line": 270, "column": 41}, "end": {"line": 270, "column": 51}}, "67": {"start": {"line": 273, "column": 6}, "end": {"line": 284, "column": 7}}, "68": {"start": {"line": 273, "column": 24}, "end": {"line": 273, "column": 41}}, "69": {"start": {"line": 274, "column": 8}, "end": {"line": 283, "column": 9}}, "70": {"start": {"line": 274, "column": 21}, "end": {"line": 274, "column": 22}}, "71": {"start": {"line": 275, "column": 27}, "end": {"line": 275, "column": 58}}, "72": {"start": {"line": 276, "column": 27}, "end": {"line": 276, "column": 71}}, "73": {"start": {"line": 278, "column": 10}, "end": {"line": 282, "column": 11}}, "74": {"start": {"line": 280, "column": 28}, "end": {"line": 280, "column": 49}}, "75": {"start": {"line": 287, "column": 4}, "end": {"line": 287, "column": 24}}, "76": {"start": {"line": 297, "column": 48}, "end": {"line": 297, "column": 50}}, "77": {"start": {"line": 300, "column": 25}, "end": {"line": 300, "column": 58}}, "78": {"start": {"line": 301, "column": 24}, "end": {"line": 301, "column": 56}}, "79": {"start": {"line": 304, "column": 4}, "end": {"line": 312, "column": 7}}, "80": {"start": {"line": 305, "column": 6}, "end": {"line": 311, "column": 9}}, "81": {"start": {"line": 307, "column": 22}, "end": {"line": 307, "column": 80}}, "82": {"start": {"line": 308, "column": 8}, "end": {"line": 310, "column": 9}}, "83": {"start": {"line": 314, "column": 4}, "end": {"line": 314, "column": 26}}, "84": {"start": {"line": 325, "column": 36}, "end": {"line": 325, "column": 38}}, "85": {"start": {"line": 326, "column": 36}, "end": {"line": 326, "column": 38}}, "86": {"start": {"line": 329, "column": 4}, "end": {"line": 338, "column": 7}}, "87": {"start": {"line": 330, "column": 6}, "end": {"line": 337, "column": 9}}, "88": {"start": {"line": 341, "column": 33}, "end": {"line": 342, "column": null}}, "89": {"start": {"line": 342, "column": 33}, "end": {"line": 342, "column": 46}}, "90": {"start": {"line": 345, "column": 26}, "end": {"line": 345, "column": 70}}, "91": {"start": {"line": 345, "column": 57}, "end": {"line": 345, "column": 67}}, "92": {"start": {"line": 346, "column": 29}, "end": {"line": 347, "column": null}}, "93": {"start": {"line": 347, "column": 18}, "end": {"line": 347, "column": 53}}, "94": {"start": {"line": 350, "column": 4}, "end": {"line": 360, "column": 7}}, "95": {"start": {"line": 351, "column": 24}, "end": {"line": 351, "column": 77}}, "96": {"start": {"line": 351, "column": 46}, "end": {"line": 351, "column": 69}}, "97": {"start": {"line": 352, "column": 6}, "end": {"line": 359, "column": 9}}, "98": {"start": {"line": 363, "column": 24}, "end": {"line": 363, "column": 85}}, "99": {"start": {"line": 365, "column": 4}, "end": {"line": 384, "column": 7}}, "100": {"start": {"line": 366, "column": 27}, "end": {"line": 366, "column": 70}}, "101": {"start": {"line": 368, "column": 6}, "end": {"line": 383, "column": 7}}, "102": {"start": {"line": 368, "column": 19}, "end": {"line": 368, "column": 20}}, "103": {"start": {"line": 369, "column": 23}, "end": {"line": 369, "column": 41}}, "104": {"start": {"line": 370, "column": 23}, "end": {"line": 370, "column": 45}}, "105": {"start": {"line": 371, "column": 20}, "end": {"line": 371, "column": 42}}, "106": {"start": {"line": 373, "column": 8}, "end": {"line": 375, "column": 9}}, "107": {"start": {"line": 374, "column": 10}, "end": {"line": 374, "column": 64}}, "108": {"start": {"line": 377, "column": 21}, "end": {"line": 377, "column": 42}}, "109": {"start": {"line": 378, "column": 8}, "end": {"line": 378, "column": 25}}, "110": {"start": {"line": 380, "column": 25}, "end": {"line": 381, "column": 59}}, "111": {"start": {"line": 382, "column": 8}, "end": {"line": 382, "column": 38}}, "112": {"start": {"line": 387, "column": 4}, "end": {"line": 397, "column": 7}}, "113": {"start": {"line": 388, "column": 31}, "end": {"line": 388, "column": 46}}, "114": {"start": {"line": 389, "column": 26}, "end": {"line": 389, "column": 95}}, "115": {"start": {"line": 389, "column": 60}, "end": {"line": 389, "column": 67}}, "116": {"start": {"line": 391, "column": 6}, "end": {"line": 396, "column": 9}}, "117": {"start": {"line": 399, "column": 4}, "end": {"line": 399, "column": 28}}, "118": {"start": {"line": 409, "column": 65}, "end": {"line": 409, "column": 67}}, "119": {"start": {"line": 410, "column": 12}, "end": {"line": 410, "column": 13}}, "120": {"start": {"line": 412, "column": 4}, "end": {"line": 436, "column": 5}}, "121": {"start": {"line": 413, "column": 20}, "end": {"line": 413, "column": 25}}, "122": {"start": {"line": 416, "column": 6}, "end": {"line": 426, "column": 7}}, "123": {"start": {"line": 417, "column": 8}, "end": {"line": 425, "column": 9}}, "124": {"start": {"line": 418, "column": 10}, "end": {"line": 421, "column": 13}}, "125": {"start": {"line": 422, "column": 10}, "end": {"line": 422, "column": 44}}, "126": {"start": {"line": 423, "column": 10}, "end": {"line": 423, "column": 25}}, "127": {"start": {"line": 424, "column": 10}, "end": {"line": 424, "column": 16}}, "128": {"start": {"line": 429, "column": 6}, "end": {"line": 435, "column": 7}}, "129": {"start": {"line": 430, "column": 8}, "end": {"line": 433, "column": 11}}, "130": {"start": {"line": 434, "column": 8}, "end": {"line": 434, "column": 12}}, "131": {"start": {"line": 438, "column": 4}, "end": {"line": 438, "column": 24}}, "132": {"start": {"line": 449, "column": 4}, "end": {"line": 451, "column": 5}}, "133": {"start": {"line": 450, "column": 6}, "end": {"line": 450, "column": 19}}, "134": {"start": {"line": 453, "column": 4}, "end": {"line": 457, "column": 5}}, "135": {"start": {"line": 453, "column": 17}, "end": {"line": 453, "column": 18}}, "136": {"start": {"line": 454, "column": 6}, "end": {"line": 456, "column": 7}}, "137": {"start": {"line": 455, "column": 8}, "end": {"line": 455, "column": 21}}, "138": {"start": {"line": 459, "column": 4}, "end": {"line": 459, "column": 16}}, "139": {"start": {"line": 470, "column": 31}, "end": {"line": 470, "column": 82}}, "140": {"start": {"line": 470, "column": 62}, "end": {"line": 470, "column": 72}}, "141": {"start": {"line": 471, "column": 33}, "end": {"line": 471, "column": 61}}, "142": {"start": {"line": 473, "column": 4}, "end": {"line": 483, "column": 6}}, "143": {"start": {"line": 476, "column": 43}, "end": {"line": 476, "column": 69}}, "144": {"start": {"line": 490, "column": 4}, "end": {"line": 490, "column": 34}}, "145": {"start": {"line": 497, "column": 25}, "end": {"line": 497, "column": 55}}, "146": {"start": {"line": 499, "column": 4}, "end": {"line": 509, "column": 7}}, "147": {"start": {"line": 500, "column": 6}, "end": {"line": 508, "column": 7}}, "148": {"start": {"line": 500, "column": 19}, "end": {"line": 500, "column": 20}}, "149": {"start": {"line": 501, "column": 24}, "end": {"line": 501, "column": 42}}, "150": {"start": {"line": 502, "column": 21}, "end": {"line": 502, "column": 43}}, "151": {"start": {"line": 504, "column": 8}, "end": {"line": 506, "column": 9}}, "152": {"start": {"line": 505, "column": 10}, "end": {"line": 505, "column": 47}}, "153": {"start": {"line": 507, "column": 8}, "end": {"line": 507, "column": 45}}, "154": {"start": {"line": 511, "column": 4}, "end": {"line": 513, "column": 40}}, "155": {"start": {"line": 512, "column": 34}, "end": {"line": 512, "column": 52}}, "156": {"start": {"line": 513, "column": 30}, "end": {"line": 513, "column": 38}}, "157": {"start": {"line": 520, "column": 26}, "end": {"line": 520, "column": 56}}, "158": {"start": {"line": 522, "column": 4}, "end": {"line": 532, "column": 7}}, "159": {"start": {"line": 523, "column": 6}, "end": {"line": 531, "column": 7}}, "160": {"start": {"line": 523, "column": 19}, "end": {"line": 523, "column": 20}}, "161": {"start": {"line": 524, "column": 24}, "end": {"line": 524, "column": 42}}, "162": {"start": {"line": 525, "column": 21}, "end": {"line": 525, "column": 43}}, "163": {"start": {"line": 527, "column": 8}, "end": {"line": 529, "column": 9}}, "164": {"start": {"line": 528, "column": 10}, "end": {"line": 528, "column": 48}}, "165": {"start": {"line": 530, "column": 8}, "end": {"line": 530, "column": 46}}, "166": {"start": {"line": 534, "column": 4}, "end": {"line": 536, "column": 40}}, "167": {"start": {"line": 535, "column": 37}, "end": {"line": 535, "column": 58}}, "168": {"start": {"line": 536, "column": 30}, "end": {"line": 536, "column": 38}}, "169": {"start": {"line": 547, "column": 30}, "end": {"line": 547, "column": 32}}, "170": {"start": {"line": 549, "column": 4}, "end": {"line": 558, "column": 7}}, "171": {"start": {"line": 550, "column": 25}, "end": {"line": 550, "column": 52}}, "172": {"start": {"line": 550, "column": 41}, "end": {"line": 550, "column": 51}}, "173": {"start": {"line": 551, "column": 25}, "end": {"line": 551, "column": 50}}, "174": {"start": {"line": 552, "column": 23}, "end": {"line": 552, "column": 50}}, "175": {"start": {"line": 554, "column": 6}, "end": {"line": 557, "column": 7}}, "176": {"start": {"line": 555, "column": 21}, "end": {"line": 555, "column": 63}}, "177": {"start": {"line": 556, "column": 8}, "end": {"line": 556, "column": 25}}, "178": {"start": {"line": 560, "column": 4}, "end": {"line": 560, "column": 17}}, "179": {"start": {"line": 567, "column": 4}, "end": {"line": 567, "column": 70}}, "180": {"start": {"line": 567, "column": 56}, "end": {"line": 567, "column": 68}}, "181": {"start": {"line": 574, "column": 4}, "end": {"line": 583, "column": 6}}, "182": {"start": {"line": 576, "column": 8}, "end": {"line": 578, "column": 9}}, "183": {"start": {"line": 577, "column": 10}, "end": {"line": 577, "column": 36}}, "184": {"start": {"line": 579, "column": 8}, "end": {"line": 579, "column": 41}}, "185": {"start": {"line": 580, "column": 8}, "end": {"line": 580, "column": 22}}, "186": {"start": {"line": 594, "column": 27}, "end": {"line": 594, "column": 47}}, "187": {"start": {"line": 595, "column": 4}, "end": {"line": 595, "column": 41}}, "188": {"start": {"line": 596, "column": 4}, "end": {"line": 596, "column": 47}}, "189": {"start": {"line": 597, "column": 4}, "end": {"line": 597, "column": 43}}, "190": {"start": {"line": 599, "column": 4}, "end": {"line": 599, "column": 61}}, "191": {"start": {"line": 62, "column": 13}, "end": {"line": 62, "column": 39}}, "192": {"start": {"line": 62, "column": 13}, "end": {"line": 601, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": null}}, "loc": {"start": {"line": 70, "column": 47}, "end": {"line": 71, "column": 6}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 76, "column": 2}, "end": {"line": 76, "column": 7}}, "loc": {"start": {"line": 78, "column": 53}, "end": {"line": 169, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 174, "column": 10}, "end": {"line": 174, "column": 36}}, "loc": {"start": {"line": 176, "column": 39}, "end": {"line": 229, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 185, "column": 39}, "end": {"line": 185, "column": 40}}, "loc": {"start": {"line": 185, "column": 60}, "end": {"line": 212, "column": 5}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 186, "column": 36}, "end": {"line": 186, "column": 37}}, "loc": {"start": {"line": 186, "column": 41}, "end": {"line": 186, "column": 51}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 216, "column": 14}, "end": {"line": 216, "column": 15}}, "loc": {"start": {"line": 216, "column": 29}, "end": {"line": 216, "column": 70}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 217, "column": 11}, "end": {"line": 217, "column": 12}}, "loc": {"start": {"line": 217, "column": 44}, "end": {"line": 226, "column": 8}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 222, "column": 43}, "end": {"line": 222, "column": 44}}, "loc": {"start": {"line": 222, "column": 55}, "end": {"line": 222, "column": 62}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 227, "column": 14}, "end": {"line": 227, "column": 21}}, "loc": {"start": {"line": 227, "column": 25}, "end": {"line": 227, "column": 74}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 228, "column": 12}, "end": {"line": 228, "column": 13}}, "loc": {"start": {"line": 228, "column": 22}, "end": {"line": 228, "column": 47}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 234, "column": 10}, "end": {"line": 234, "column": 34}}, "loc": {"start": {"line": 236, "column": 39}, "end": {"line": 258, "column": 3}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 241, "column": 39}, "end": {"line": 241, "column": 40}}, "loc": {"start": {"line": 241, "column": 60}, "end": {"line": 255, "column": 5}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 263, "column": 10}, "end": {"line": 263, "column": 30}}, "loc": {"start": {"line": 265, "column": 39}, "end": {"line": 288, "column": 3}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 269, "column": 39}, "end": {"line": 269, "column": 40}}, "loc": {"start": {"line": 269, "column": 60}, "end": {"line": 285, "column": 5}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 270, "column": 36}, "end": {"line": 270, "column": 37}}, "loc": {"start": {"line": 270, "column": 41}, "end": {"line": 270, "column": 51}}}, "15": {"name": "(anonymous_19)", "decl": {"start": {"line": 293, "column": 10}, "end": {"line": 293, "column": 32}}, "loc": {"start": {"line": 295, "column": 39}, "end": {"line": 315, "column": 3}}}, "16": {"name": "(anonymous_20)", "decl": {"start": {"line": 304, "column": 25}, "end": {"line": 304, "column": 36}}, "loc": {"start": {"line": 304, "column": 39}, "end": {"line": 312, "column": 5}}}, "17": {"name": "(anonymous_21)", "decl": {"start": {"line": 305, "column": 26}, "end": {"line": 305, "column": 36}}, "loc": {"start": {"line": 305, "column": 39}, "end": {"line": 311, "column": 7}}}, "18": {"name": "(anonymous_22)", "decl": {"start": {"line": 320, "column": 10}, "end": {"line": 320, "column": 30}}, "loc": {"start": {"line": 323, "column": 25}, "end": {"line": 400, "column": 3}}}, "19": {"name": "(anonymous_23)", "decl": {"start": {"line": 329, "column": 25}, "end": {"line": 329, "column": 35}}, "loc": {"start": {"line": 329, "column": 38}, "end": {"line": 338, "column": 5}}}, "20": {"name": "(anonymous_24)", "decl": {"start": {"line": 342, "column": 27}, "end": {"line": 342, "column": 29}}, "loc": {"start": {"line": 342, "column": 33}, "end": {"line": 342, "column": 46}}}, "21": {"name": "(anonymous_25)", "decl": {"start": {"line": 345, "column": 52}, "end": {"line": 345, "column": 53}}, "loc": {"start": {"line": 345, "column": 57}, "end": {"line": 345, "column": 67}}}, "22": {"name": "(anonymous_26)", "decl": {"start": {"line": 347, "column": 6}, "end": {"line": 347, "column": 14}}, "loc": {"start": {"line": 347, "column": 18}, "end": {"line": 347, "column": 53}}}, "23": {"name": "(anonymous_27)", "decl": {"start": {"line": 350, "column": 29}, "end": {"line": 350, "column": 37}}, "loc": {"start": {"line": 350, "column": 40}, "end": {"line": 360, "column": 5}}}, "24": {"name": "(anonymous_28)", "decl": {"start": {"line": 351, "column": 41}, "end": {"line": 351, "column": 42}}, "loc": {"start": {"line": 351, "column": 46}, "end": {"line": 351, "column": 69}}}, "25": {"name": "(anonymous_29)", "decl": {"start": {"line": 365, "column": 38}, "end": {"line": 365, "column": 44}}, "loc": {"start": {"line": 365, "column": 47}, "end": {"line": 384, "column": 5}}}, "26": {"name": "(anonymous_30)", "decl": {"start": {"line": 387, "column": 24}, "end": {"line": 387, "column": 25}}, "loc": {"start": {"line": 387, "column": 38}, "end": {"line": 397, "column": 5}}}, "27": {"name": "(anonymous_31)", "decl": {"start": {"line": 389, "column": 48}, "end": {"line": 389, "column": 49}}, "loc": {"start": {"line": 389, "column": 60}, "end": {"line": 389, "column": 67}}}, "28": {"name": "(anonymous_32)", "decl": {"start": {"line": 405, "column": 10}, "end": {"line": 405, "column": 26}}, "loc": {"start": {"line": 407, "column": 37}, "end": {"line": 439, "column": 3}}}, "29": {"name": "(anonymous_33)", "decl": {"start": {"line": 444, "column": 10}, "end": {"line": 444, "column": 27}}, "loc": {"start": {"line": 447, "column": 33}, "end": {"line": 460, "column": 3}}}, "30": {"name": "(anonymous_34)", "decl": {"start": {"line": 465, "column": 10}, "end": {"line": 465, "column": 29}}, "loc": {"start": {"line": 468, "column": 25}, "end": {"line": 484, "column": 3}}}, "31": {"name": "(anonymous_35)", "decl": {"start": {"line": 470, "column": 57}, "end": {"line": 470, "column": 58}}, "loc": {"start": {"line": 470, "column": 62}, "end": {"line": 470, "column": 72}}}, "32": {"name": "(anonymous_36)", "decl": {"start": {"line": 476, "column": 30}, "end": {"line": 476, "column": 31}}, "loc": {"start": {"line": 476, "column": 43}, "end": {"line": 476, "column": 69}}}, "33": {"name": "(anonymous_37)", "decl": {"start": {"line": 489, "column": 10}, "end": {"line": 489, "column": 29}}, "loc": {"start": {"line": 489, "column": 67}, "end": {"line": 491, "column": 3}}}, "34": {"name": "(anonymous_38)", "decl": {"start": {"line": 496, "column": 10}, "end": {"line": 496, "column": 26}}, "loc": {"start": {"line": 496, "column": 65}, "end": {"line": 514, "column": 3}}}, "35": {"name": "(anonymous_39)", "decl": {"start": {"line": 499, "column": 38}, "end": {"line": 499, "column": 44}}, "loc": {"start": {"line": 499, "column": 47}, "end": {"line": 509, "column": 5}}}, "36": {"name": "(anonymous_40)", "decl": {"start": {"line": 512, "column": 14}, "end": {"line": 512, "column": 15}}, "loc": {"start": {"line": 512, "column": 34}, "end": {"line": 512, "column": 52}}}, "37": {"name": "(anonymous_41)", "decl": {"start": {"line": 513, "column": 11}, "end": {"line": 513, "column": 12}}, "loc": {"start": {"line": 513, "column": 30}, "end": {"line": 513, "column": 38}}}, "38": {"name": "(anonymous_42)", "decl": {"start": {"line": 519, "column": 10}, "end": {"line": 519, "column": 25}}, "loc": {"start": {"line": 519, "column": 64}, "end": {"line": 537, "column": 3}}}, "39": {"name": "(anonymous_43)", "decl": {"start": {"line": 522, "column": 38}, "end": {"line": 522, "column": 44}}, "loc": {"start": {"line": 522, "column": 47}, "end": {"line": 532, "column": 5}}}, "40": {"name": "(anonymous_44)", "decl": {"start": {"line": 535, "column": 14}, "end": {"line": 535, "column": 15}}, "loc": {"start": {"line": 535, "column": 37}, "end": {"line": 535, "column": 58}}}, "41": {"name": "(anonymous_45)", "decl": {"start": {"line": 536, "column": 11}, "end": {"line": 536, "column": 12}}, "loc": {"start": {"line": 536, "column": 30}, "end": {"line": 536, "column": 38}}}, "42": {"name": "(anonymous_46)", "decl": {"start": {"line": 542, "column": 10}, "end": {"line": 542, "column": 26}}, "loc": {"start": {"line": 545, "column": 15}, "end": {"line": 561, "column": 3}}}, "43": {"name": "(anonymous_47)", "decl": {"start": {"line": 549, "column": 38}, "end": {"line": 549, "column": 44}}, "loc": {"start": {"line": 549, "column": 47}, "end": {"line": 558, "column": 5}}}, "44": {"name": "(anonymous_48)", "decl": {"start": {"line": 550, "column": 36}, "end": {"line": 550, "column": 37}}, "loc": {"start": {"line": 550, "column": 41}, "end": {"line": 550, "column": 51}}}, "45": {"name": "(anonymous_49)", "decl": {"start": {"line": 566, "column": 10}, "end": {"line": 566, "column": 21}}, "loc": {"start": {"line": 566, "column": 39}, "end": {"line": 568, "column": 3}}}, "46": {"name": "(anonymous_50)", "decl": {"start": {"line": 567, "column": 44}, "end": {"line": 567, "column": 45}}, "loc": {"start": {"line": 567, "column": 56}, "end": {"line": 567, "column": 68}}}, "47": {"name": "(anonymous_51)", "decl": {"start": {"line": 573, "column": 10}, "end": {"line": 573, "column": 23}}, "loc": {"start": {"line": 573, "column": 45}, "end": {"line": 584, "column": 3}}}, "48": {"name": "(anonymous_52)", "decl": {"start": {"line": 575, "column": 6}, "end": {"line": 575, "column": 7}}, "loc": {"start": {"line": 575, "column": 24}, "end": {"line": 581, "column": 7}}}, "49": {"name": "(anonymous_53)", "decl": {"start": {"line": 589, "column": 10}, "end": {"line": 589, "column": 15}}, "loc": {"start": {"line": 592, "column": 19}, "end": {"line": 600, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 53}}, "type": "default-arg", "locations": [{"start": {"line": 78, "column": 51}, "end": {"line": 78, "column": 53}}]}, "1": {"loc": {"start": {"line": 100, "column": 4}, "end": {"line": 102, "column": 5}}, "type": "if", "locations": [{"start": {"line": 100, "column": 4}, "end": {"line": 102, "column": 5}}]}, "2": {"loc": {"start": {"line": 114, "column": 29}, "end": {"line": 116, "column": 10}}, "type": "cond-expr", "locations": [{"start": {"line": 115, "column": 8}, "end": {"line": 115, "column": 65}}, {"start": {"line": 116, "column": 8}, "end": {"line": 116, "column": 10}}]}, "3": {"loc": {"start": {"line": 119, "column": 25}, "end": {"line": 121, "column": 10}}, "type": "cond-expr", "locations": [{"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": 61}}, {"start": {"line": 121, "column": 8}, "end": {"line": 121, "column": 10}}]}, "4": {"loc": {"start": {"line": 193, "column": 10}, "end": {"line": 199, "column": 11}}, "type": "if", "locations": [{"start": {"line": 193, "column": 10}, "end": {"line": 199, "column": 11}}]}, "5": {"loc": {"start": {"line": 202, "column": 10}, "end": {"line": 209, "column": 11}}, "type": "if", "locations": [{"start": {"line": 202, "column": 10}, "end": {"line": 209, "column": 11}}]}, "6": {"loc": {"start": {"line": 250, "column": 10}, "end": {"line": 252, "column": 11}}, "type": "if", "locations": [{"start": {"line": 250, "column": 10}, "end": {"line": 252, "column": 11}}]}, "7": {"loc": {"start": {"line": 278, "column": 10}, "end": {"line": 282, "column": 11}}, "type": "if", "locations": [{"start": {"line": 278, "column": 10}, "end": {"line": 282, "column": 11}}]}, "8": {"loc": {"start": {"line": 308, "column": 8}, "end": {"line": 310, "column": 9}}, "type": "if", "locations": [{"start": {"line": 308, "column": 8}, "end": {"line": 310, "column": 9}}]}, "9": {"loc": {"start": {"line": 373, "column": 8}, "end": {"line": 375, "column": 9}}, "type": "if", "locations": [{"start": {"line": 373, "column": 8}, "end": {"line": 375, "column": 9}}]}, "10": {"loc": {"start": {"line": 417, "column": 8}, "end": {"line": 425, "column": 9}}, "type": "if", "locations": [{"start": {"line": 417, "column": 8}, "end": {"line": 425, "column": 9}}]}, "11": {"loc": {"start": {"line": 429, "column": 6}, "end": {"line": 435, "column": 7}}, "type": "if", "locations": [{"start": {"line": 429, "column": 6}, "end": {"line": 435, "column": 7}}]}, "12": {"loc": {"start": {"line": 449, "column": 4}, "end": {"line": 451, "column": 5}}, "type": "if", "locations": [{"start": {"line": 449, "column": 4}, "end": {"line": 451, "column": 5}}]}, "13": {"loc": {"start": {"line": 454, "column": 6}, "end": {"line": 456, "column": 7}}, "type": "if", "locations": [{"start": {"line": 454, "column": 6}, "end": {"line": 456, "column": 7}}]}, "14": {"loc": {"start": {"line": 475, "column": 27}, "end": {"line": 477, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 476, "column": 10}, "end": {"line": 476, "column": 95}}, {"start": {"line": 477, "column": 10}, "end": {"line": 477, "column": 11}}]}, "15": {"loc": {"start": {"line": 478, "column": 24}, "end": {"line": 480, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 479, "column": 10}, "end": {"line": 479, "column": 74}}, {"start": {"line": 480, "column": 10}, "end": {"line": 480, "column": 11}}]}, "16": {"loc": {"start": {"line": 504, "column": 8}, "end": {"line": 506, "column": 9}}, "type": "if", "locations": [{"start": {"line": 504, "column": 8}, "end": {"line": 506, "column": 9}}]}, "17": {"loc": {"start": {"line": 527, "column": 8}, "end": {"line": 529, "column": 9}}, "type": "if", "locations": [{"start": {"line": 527, "column": 8}, "end": {"line": 529, "column": 9}}]}, "18": {"loc": {"start": {"line": 554, "column": 6}, "end": {"line": 557, "column": 7}}, "type": "if", "locations": [{"start": {"line": 554, "column": 6}, "end": {"line": 557, "column": 7}}]}, "19": {"loc": {"start": {"line": 554, "column": 10}, "end": {"line": 554, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 554, "column": 10}, "end": {"line": 554, "column": 27}}, {"start": {"line": 554, "column": 31}, "end": {"line": 554, "column": 46}}, {"start": {"line": 554, "column": 50}, "end": {"line": 554, "column": 71}}]}, "20": {"loc": {"start": {"line": 567, "column": 11}, "end": {"line": 567, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 567, "column": 11}, "end": {"line": 567, "column": 32}}, {"start": {"line": 567, "column": 36}, "end": {"line": 567, "column": 69}}]}, "21": {"loc": {"start": {"line": 576, "column": 8}, "end": {"line": 578, "column": 9}}, "type": "if", "locations": [{"start": {"line": 576, "column": 8}, "end": {"line": 578, "column": 9}}]}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 2, "6": 2, "7": 5, "8": 5, "9": 5, "10": 5, "11": 4, "12": 4, "13": 4, "14": 4, "15": 1, "16": 3, "17": 3, "18": 3, "19": 3, "20": 3, "21": 3, "22": 3, "23": 3, "24": 3, "25": 3, "26": 3, "27": 3, "28": 3, "29": 3, "30": 9, "31": 150, "32": 9, "33": 9, "34": 36, "35": 36, "36": 510, "37": 510, "38": 510, "39": 270, "40": 510, "41": 510, "42": 510, "43": 510, "44": 510, "45": 510, "46": 3, "47": 270, "48": 140, "49": 300, "50": 140, "51": 235, "52": 3, "53": 3, "54": 9, "55": 9, "56": 141, "57": 141, "58": 1200, "59": 1200, "60": 1200, "61": 1200, "62": 3, "63": 3, "64": 3, "65": 9, "66": 150, "67": 9, "68": 9, "69": 36, "70": 36, "71": 384, "72": 384, "73": 384, "74": 0, "75": 3, "76": 3, "77": 3, "78": 3, "79": 3, "80": 9, "81": 27, "82": 27, "83": 3, "84": 3, "85": 3, "86": 3, "87": 80, "88": 3, "89": 80, "90": 3, "91": 150, "92": 3, "93": 57, "94": 3, "95": 27, "96": 1350, "97": 27, "98": 3, "99": 3, "100": 9, "101": 9, "102": 9, "103": 99, "104": 99, "105": 99, "106": 99, "107": 49, "108": 99, "109": 99, "110": 99, "111": 99, "112": 3, "113": 49, "114": 49, "115": 99, "116": 49, "117": 3, "118": 9, "119": 9, "120": 9, "121": 108, "122": 108, "123": 892, "124": 42, "125": 42, "126": 42, "127": 42, "128": 108, "129": 66, "130": 66, "131": 9, "132": 892, "133": 240, "134": 652, "135": 652, "136": 694, "137": 610, "138": 42, "139": 3, "140": 150, "141": 3, "142": 3, "143": 80, "144": 140, "145": 3, "146": 3, "147": 9, "148": 9, "149": 141, "150": 141, "151": 141, "152": 54, "153": 141, "154": 3, "155": 54, "156": 9, "157": 3, "158": 3, "159": 9, "160": 9, "161": 141, "162": 141, "163": 141, "164": 54, "165": 141, "166": 3, "167": 54, "168": 9, "169": 27, "170": 27, "171": 81, "172": 1350, "173": 81, "174": 81, "175": 81, "176": 42, "177": 42, "178": 27, "179": 384, "180": 384, "181": 3, "182": 150, "183": 9, "184": 150, "185": 150, "186": 3, "187": 3, "188": 3, "189": 3, "190": 3, "191": 2, "192": 2}, "f": {"0": 5, "1": 4, "2": 3, "3": 9, "4": 150, "5": 270, "6": 140, "7": 300, "8": 140, "9": 235, "10": 3, "11": 9, "12": 3, "13": 9, "14": 150, "15": 3, "16": 9, "17": 27, "18": 3, "19": 80, "20": 80, "21": 150, "22": 57, "23": 27, "24": 1350, "25": 9, "26": 49, "27": 99, "28": 9, "29": 892, "30": 3, "31": 150, "32": 80, "33": 140, "34": 3, "35": 9, "36": 54, "37": 9, "38": 3, "39": 9, "40": 54, "41": 9, "42": 27, "43": 81, "44": 1350, "45": 384, "46": 384, "47": 3, "48": 150, "49": 3}, "b": {"0": [1], "1": [1], "2": [3, 0], "3": [3, 0], "4": [270], "5": [510], "6": [9], "7": [0], "8": [9], "9": [49], "10": [42], "11": [66], "12": [240], "13": [610], "14": [2, 1], "15": [3, 0], "16": [54], "17": [54], "18": [42], "19": [81, 63, 51], "20": [384, 384], "21": [9]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/dto/index.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/dto/index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 9}}, "1": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 50}}, "2": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 9}}, "3": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 75}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 22}}, "loc": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 50}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 38}}, "loc": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 75}}}}, "branchMap": {}, "s": {"0": 1, "1": 4, "2": 1, "3": 3}, "f": {"0": 3, "1": 2}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/dto/subprocess-discovery.dto.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/dto/subprocess-discovery.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 46}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 76}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 13}}, "3": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": null}}, "4": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": null}}, "5": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": null}}, "6": {"start": {"line": 44, "column": 2}, "end": {"line": 44, "column": null}}, "7": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": null}}, "8": {"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": null}}, "9": {"start": {"line": 71, "column": 2}, "end": {"line": 71, "column": null}}, "10": {"start": {"line": 80, "column": 2}, "end": {"line": 80, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1}, "f": {}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/dto/upload-data.dto.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/dto/upload-data.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 46}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 77}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 46}}, "3": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 13}}, "4": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": null}}, "5": {"start": {"line": 9, "column": 28}, "end": {"line": 9, "column": 47}}, "6": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": null}}, "7": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": null}}, "8": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": null}}, "9": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": null}}, "10": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 9, "column": 13}, "end": {"line": 9, "column": 14}}, "loc": {"start": {"line": 9, "column": 28}, "end": {"line": 9, "column": 47}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 0, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/auth.controller.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/auth.controller.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": null}}, "2": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 45}}, "3": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 46}}, "4": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 55}}, "5": {"start": {"line": 22, "column": 7}, "end": {"line": 51, "column": null}}, "6": {"start": {"line": 23, "column": 31}, "end": {"line": 23, "column": 44}}, "7": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": 50}}, "8": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 44}}, "9": {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 54}}, "10": {"start": {"line": 22, "column": 13}, "end": {"line": 22, "column": 27}}, "11": {"start": {"line": 29, "column": 2}, "end": {"line": 31, "column": null}}, "12": {"start": {"line": 38, "column": 2}, "end": {"line": 40, "column": null}}, "13": {"start": {"line": 48, "column": 2}, "end": {"line": 50, "column": null}}, "14": {"start": {"line": 22, "column": 13}, "end": {"line": 51, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 31}}, "loc": {"start": {"line": 23, "column": 55}, "end": {"line": 23, "column": 59}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 10}}, "loc": {"start": {"line": 29, "column": 43}, "end": {"line": 31, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": 7}}, "loc": {"start": {"line": 38, "column": 34}, "end": {"line": 40, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 12}}, "loc": {"start": {"line": 48, "column": 27}, "end": {"line": 50, "column": 3}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 7, "7": 2, "8": 2, "9": 2, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1}, "f": {"0": 7, "1": 2, "2": 2, "3": 2}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/auth.module.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/auth.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 40}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 50}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 61}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 45}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 51}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 52}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 56}}, "8": {"start": {"line": 29, "column": 7}, "end": {"line": 29, "column": null}}, "9": {"start": {"line": 29, "column": 13}, "end": {"line": 29, "column": 23}}, "10": {"start": {"line": 29, "column": 13}, "end": {"line": 29, "column": null}}, "11": {"start": {"line": 16, "column": 59}, "end": {"line": 21, "column": 8}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 16, "column": 18}, "end": {"line": 16, "column": 23}}, "loc": {"start": {"line": 16, "column": 59}, "end": {"line": 21, "column": 8}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/auth.service.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/auth.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 67}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 41}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 54}}, "3": {"start": {"line": 8, "column": 7}, "end": {"line": 82, "column": null}}, "4": {"start": {"line": 10, "column": 21}, "end": {"line": 10, "column": 35}}, "5": {"start": {"line": 11, "column": 21}, "end": {"line": 11, "column": 33}}, "6": {"start": {"line": 15, "column": 17}, "end": {"line": 15, "column": 60}}, "7": {"start": {"line": 17, "column": 32}, "end": {"line": 21, "column": 6}}, "8": {"start": {"line": 23, "column": 4}, "end": {"line": 32, "column": 6}}, "9": {"start": {"line": 36, "column": 42}, "end": {"line": 36, "column": 50}}, "10": {"start": {"line": 39, "column": 15}, "end": {"line": 39, "column": 70}}, "11": {"start": {"line": 40, "column": 4}, "end": {"line": 42, "column": 5}}, "12": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 66}}, "13": {"start": {"line": 44, "column": 4}, "end": {"line": 46, "column": 5}}, "14": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 53}}, "15": {"start": {"line": 48, "column": 4}, "end": {"line": 50, "column": 5}}, "16": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 48}}, "17": {"start": {"line": 53, "column": 28}, "end": {"line": 55, "column": null}}, "18": {"start": {"line": 57, "column": 4}, "end": {"line": 59, "column": 5}}, "19": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 53}}, "20": {"start": {"line": 61, "column": 32}, "end": {"line": 65, "column": 6}}, "21": {"start": {"line": 67, "column": 4}, "end": {"line": 76, "column": 6}}, "22": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 41}}, "23": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 24}}, "24": {"start": {"line": 8, "column": 13}, "end": {"line": 82, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": null}}, "loc": {"start": {"line": 11, "column": 43}, "end": {"line": 12, "column": 6}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 7}}, "loc": {"start": {"line": 14, "column": 41}, "end": {"line": 33, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": 7}}, "loc": {"start": {"line": 35, "column": 32}, "end": {"line": 77, "column": 3}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 79, "column": 2}, "end": {"line": 79, "column": 7}}, "loc": {"start": {"line": 79, "column": 31}, "end": {"line": 81, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 40, "column": 4}, "end": {"line": 42, "column": 5}}, "type": "if", "locations": [{"start": {"line": 40, "column": 4}, "end": {"line": 42, "column": 5}}]}, "1": {"loc": {"start": {"line": 44, "column": 4}, "end": {"line": 46, "column": 5}}, "type": "if", "locations": [{"start": {"line": 44, "column": 4}, "end": {"line": 46, "column": 5}}]}, "2": {"loc": {"start": {"line": 48, "column": 4}, "end": {"line": 50, "column": 5}}, "type": "if", "locations": [{"start": {"line": 48, "column": 4}, "end": {"line": 50, "column": 5}}]}, "3": {"loc": {"start": {"line": 57, "column": 4}, "end": {"line": 59, "column": 5}}, "type": "if", "locations": [{"start": {"line": 57, "column": 4}, "end": {"line": 59, "column": 5}}]}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 10, "5": 10, "6": 3, "7": 1, "8": 1, "9": 4, "10": 4, "11": 4, "12": 2, "13": 4, "14": 1, "15": 3, "16": 0, "17": 3, "18": 3, "19": 1, "20": 2, "21": 2, "22": 2, "23": 2, "24": 2}, "f": {"0": 10, "1": 3, "2": 4, "3": 2}, "b": {"0": [2], "1": [1], "2": [0], "3": [1]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/dto/index.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/dto/index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 9}}, "1": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 39}}, "2": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 9}}, "3": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 45}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 17}}, "loc": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 39}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 20}}, "loc": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 45}}}}, "branchMap": {}, "s": {"0": 1, "1": 2, "2": 1, "3": 2}, "f": {"0": 1, "1": 1}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/dto/login.dto.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/dto/login.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 46}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 55}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 13}}, "3": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": null}}, "4": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1}, "f": {}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/dto/register.dto.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/dto/register.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 46}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": null}}, "2": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 13}}, "3": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": null}}, "4": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": null}}, "5": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": null}}, "6": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1}, "f": {}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/guards/jwt-auth.guard.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/guards/jwt-auth.guard.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 45}}, "2": {"start": {"line": 5, "column": 7}, "end": {"line": 5, "column": null}}, "3": {"start": {"line": 5, "column": 13}, "end": {"line": 5, "column": 25}}, "4": {"start": {"line": 5, "column": 13}, "end": {"line": 5, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 4, "1": 4, "2": 4, "3": 4, "4": 4}, "f": {}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/strategies/jwt.strategy.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/strategies/jwt.strategy.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 67}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 52}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 52}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 47}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 57}}, "5": {"start": {"line": 14, "column": 7}, "end": {"line": 39, "column": null}}, "6": {"start": {"line": 19, "column": 4}, "end": {"line": 23, "column": 7}}, "7": {"start": {"line": 16, "column": 21}, "end": {"line": 16, "column": 36}}, "8": {"start": {"line": 17, "column": 21}, "end": {"line": 17, "column": 35}}, "9": {"start": {"line": 27, "column": 17}, "end": {"line": 27, "column": 61}}, "10": {"start": {"line": 29, "column": 4}, "end": {"line": 31, "column": 5}}, "11": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 52}}, "12": {"start": {"line": 33, "column": 4}, "end": {"line": 37, "column": 6}}, "13": {"start": {"line": 14, "column": 13}, "end": {"line": 14, "column": 24}}, "14": {"start": {"line": 14, "column": 13}, "end": {"line": 39, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": null}}, "loc": {"start": {"line": 17, "column": 47}, "end": {"line": 24, "column": 3}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 7}}, "loc": {"start": {"line": 26, "column": 36}, "end": {"line": 38, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 22, "column": 19}, "end": {"line": 22, "column": 78}}, "type": "binary-expr", "locations": [{"start": {"line": 22, "column": 19}, "end": {"line": 22, "column": 58}}, {"start": {"line": 22, "column": 62}, "end": {"line": 22, "column": 78}}]}, "1": {"loc": {"start": {"line": 29, "column": 4}, "end": {"line": 31, "column": 5}}, "type": "if", "locations": [{"start": {"line": 29, "column": 4}, "end": {"line": 31, "column": 5}}]}, "2": {"loc": {"start": {"line": 29, "column": 8}, "end": {"line": 29, "column": 31}}, "type": "binary-expr", "locations": [{"start": {"line": 29, "column": 8}, "end": {"line": 29, "column": 13}}, {"start": {"line": 29, "column": 17}, "end": {"line": 29, "column": 31}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0], "2": [0, 0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/config/database.config.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/config/database.config.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "1": {"start": {"line": 4, "column": 0}, "end": {"line": 20, "column": 2}}, "2": {"start": {"line": 6, "column": 31}, "end": {"line": 19, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": 27}}, "loc": {"start": {"line": 6, "column": 31}, "end": {"line": 19, "column": 4}}}}, "branchMap": {"0": {"loc": {"start": {"line": 8, "column": 10}, "end": {"line": 8, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 8, "column": 10}, "end": {"line": 8, "column": 29}}, {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 44}}]}, "1": {"loc": {"start": {"line": 9, "column": 19}, "end": {"line": 9, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 9, "column": 19}, "end": {"line": 9, "column": 38}}, {"start": {"line": 9, "column": 42}, "end": {"line": 9, "column": 48}}]}, "2": {"loc": {"start": {"line": 10, "column": 14}, "end": {"line": 10, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 10, "column": 14}, "end": {"line": 10, "column": 37}}, {"start": {"line": 10, "column": 41}, "end": {"line": 10, "column": 47}}]}, "3": {"loc": {"start": {"line": 11, "column": 14}, "end": {"line": 11, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 11, "column": 14}, "end": {"line": 11, "column": 37}}, {"start": {"line": 11, "column": 41}, "end": {"line": 11, "column": 51}}]}, "4": {"loc": {"start": {"line": 12, "column": 14}, "end": {"line": 12, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 12, "column": 14}, "end": {"line": 12, "column": 37}}, {"start": {"line": 12, "column": 41}, "end": {"line": 12, "column": 51}}]}}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/config/jwt.config.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/config/jwt.config.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 6, "column": 4}}, "2": {"start": {"line": 3, "column": 40}, "end": {"line": 6, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 33}, "end": {"line": 3, "column": 36}}, "loc": {"start": {"line": 3, "column": 40}, "end": {"line": 6, "column": 2}}}}, "branchMap": {"0": {"loc": {"start": {"line": 4, "column": 10}, "end": {"line": 4, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 4, "column": 10}, "end": {"line": 4, "column": 32}}, {"start": {"line": 4, "column": 36}, "end": {"line": 4, "column": 63}}]}, "1": {"loc": {"start": {"line": 5, "column": 13}, "end": {"line": 5, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 5, "column": 13}, "end": {"line": 5, "column": 39}}, {"start": {"line": 5, "column": 43}, "end": {"line": 5, "column": 47}}]}}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/config/redis.config.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/config/redis.config.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 10, "column": 4}}, "2": {"start": {"line": 3, "column": 42}, "end": {"line": 10, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 35}, "end": {"line": 3, "column": 38}}, "loc": {"start": {"line": 3, "column": 42}, "end": {"line": 10, "column": 2}}}}, "branchMap": {"0": {"loc": {"start": {"line": 4, "column": 8}, "end": {"line": 4, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 4, "column": 8}, "end": {"line": 4, "column": 30}}, {"start": {"line": 4, "column": 34}, "end": {"line": 4, "column": 45}}]}, "1": {"loc": {"start": {"line": 5, "column": 17}, "end": {"line": 5, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 5, "column": 17}, "end": {"line": 5, "column": 39}}, {"start": {"line": 5, "column": 43}, "end": {"line": 5, "column": 49}}]}, "2": {"loc": {"start": {"line": 6, "column": 12}, "end": {"line": 6, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 6, "column": 12}, "end": {"line": 6, "column": 38}}, {"start": {"line": 6, "column": 42}, "end": {"line": 6, "column": 44}}]}, "3": {"loc": {"start": {"line": 7, "column": 13}, "end": {"line": 7, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 7, "column": 13}, "end": {"line": 7, "column": 41}}, {"start": {"line": 7, "column": 45}, "end": {"line": 7, "column": 56}}]}, "4": {"loc": {"start": {"line": 8, "column": 26}, "end": {"line": 8, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 8, "column": 26}, "end": {"line": 8, "column": 58}}, {"start": {"line": 8, "column": 62}, "end": {"line": 8, "column": 67}}]}}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/bpmn-model.service.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/bpmn-model.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 51}}, "2": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 37}}, "3": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 75}}, "4": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 53}}, "5": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 33}}, "6": {"start": {"line": 15, "column": 29}, "end": {"line": 311, "column": null}}, "7": {"start": {"line": 20, "column": 12}, "end": {"line": 20, "column": 33}}, "8": {"start": {"line": 22, "column": 12}, "end": {"line": 22, "column": 31}}, "9": {"start": {"line": 16, "column": 19}, "end": {"line": 16, "column": 62}}, "10": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 51}}, "11": {"start": {"line": 32, "column": 20}, "end": {"line": 34, "column": 6}}, "12": {"start": {"line": 35, "column": 4}, "end": {"line": 37, "column": 5}}, "13": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 43}}, "14": {"start": {"line": 40, "column": 4}, "end": {"line": 42, "column": 5}}, "15": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 53}}, "16": {"start": {"line": 45, "column": 22}, "end": {"line": 45, "column": 64}}, "17": {"start": {"line": 48, "column": 22}, "end": {"line": 48, "column": 63}}, "18": {"start": {"line": 51, "column": 22}, "end": {"line": 56, "column": 6}}, "19": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 52}}, "20": {"start": {"line": 65, "column": 18}, "end": {"line": 65, "column": 48}}, "21": {"start": {"line": 66, "column": 4}, "end": {"line": 70, "column": 7}}, "22": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": 44}}, "23": {"start": {"line": 84, "column": 22}, "end": {"line": 87, "column": 6}}, "24": {"start": {"line": 89, "column": 4}, "end": {"line": 91, "column": 5}}, "25": {"start": {"line": 90, "column": 6}, "end": {"line": 90, "column": 47}}, "26": {"start": {"line": 93, "column": 4}, "end": {"line": 93, "column": 21}}, "27": {"start": {"line": 100, "column": 22}, "end": {"line": 100, "column": 44}}, "28": {"start": {"line": 103, "column": 4}, "end": {"line": 114, "column": 5}}, "29": {"start": {"line": 104, "column": 6}, "end": {"line": 106, "column": 7}}, "30": {"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 55}}, "31": {"start": {"line": 108, "column": 24}, "end": {"line": 108, "column": 66}}, "32": {"start": {"line": 109, "column": 24}, "end": {"line": 109, "column": 65}}, "33": {"start": {"line": 111, "column": 6}, "end": {"line": 111, "column": 68}}, "34": {"start": {"line": 113, "column": 6}, "end": {"line": 113, "column": 42}}, "35": {"start": {"line": 116, "column": 4}, "end": {"line": 116, "column": 52}}, "36": {"start": {"line": 123, "column": 22}, "end": {"line": 123, "column": 44}}, "37": {"start": {"line": 124, "column": 4}, "end": {"line": 124, "column": 53}}, "38": {"start": {"line": 131, "column": 22}, "end": {"line": 131, "column": 44}}, "39": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": 46}}, "40": {"start": {"line": 133, "column": 4}, "end": {"line": 133, "column": 52}}, "41": {"start": {"line": 140, "column": 22}, "end": {"line": 140, "column": 44}}, "42": {"start": {"line": 141, "column": 4}, "end": {"line": 141, "column": 48}}, "43": {"start": {"line": 142, "column": 4}, "end": {"line": 142, "column": 52}}, "44": {"start": {"line": 149, "column": 4}, "end": {"line": 152, "column": 7}}, "45": {"start": {"line": 159, "column": 4}, "end": {"line": 186, "column": 5}}, "46": {"start": {"line": 161, "column": 6}, "end": {"line": 163, "column": 7}}, "47": {"start": {"line": 162, "column": 8}, "end": {"line": 162, "column": 21}}, "48": {"start": {"line": 167, "column": 8}, "end": {"line": 170, "column": 35}}, "49": {"start": {"line": 172, "column": 6}, "end": {"line": 174, "column": 7}}, "50": {"start": {"line": 173, "column": 8}, "end": {"line": 173, "column": 21}}, "51": {"start": {"line": 178, "column": 8}, "end": {"line": 180, "column": 36}}, "52": {"start": {"line": 182, "column": 6}, "end": {"line": 182, "column": 24}}, "53": {"start": {"line": 184, "column": 6}, "end": {"line": 184, "column": 58}}, "54": {"start": {"line": 185, "column": 6}, "end": {"line": 185, "column": 19}}, "55": {"start": {"line": 193, "column": 4}, "end": {"line": 220, "column": 5}}, "56": {"start": {"line": 197, "column": 25}, "end": {"line": 197, "column": 56}}, "57": {"start": {"line": 198, "column": 20}, "end": {"line": 198, "column": 46}}, "58": {"start": {"line": 199, "column": 23}, "end": {"line": 199, "column": 52}}, "59": {"start": {"line": 200, "column": 21}, "end": {"line": 200, "column": 48}}, "60": {"start": {"line": 202, "column": 6}, "end": {"line": 209, "column": 8}}, "61": {"start": {"line": 211, "column": 6}, "end": {"line": 211, "column": 58}}, "62": {"start": {"line": 212, "column": 6}, "end": {"line": 219, "column": 8}}, "63": {"start": {"line": 227, "column": 33}, "end": {"line": 227, "column": 35}}, "64": {"start": {"line": 231, "column": 6}, "end": {"line": 231, "column": 127}}, "65": {"start": {"line": 234, "column": 4}, "end": {"line": 238, "column": 5}}, "66": {"start": {"line": 235, "column": 6}, "end": {"line": 237, "column": 7}}, "67": {"start": {"line": 236, "column": 8}, "end": {"line": 236, "column": 34}}, "68": {"start": {"line": 240, "column": 4}, "end": {"line": 240, "column": 22}}, "69": {"start": {"line": 247, "column": 55}, "end": {"line": 247, "column": 57}}, "70": {"start": {"line": 252, "column": 4}, "end": {"line": 252, "column": 17}}, "71": {"start": {"line": 259, "column": 31}, "end": {"line": 259, "column": 33}}, "72": {"start": {"line": 262, "column": 6}, "end": {"line": 262, "column": 109}}, "73": {"start": {"line": 265, "column": 4}, "end": {"line": 269, "column": 5}}, "74": {"start": {"line": 266, "column": 6}, "end": {"line": 268, "column": 7}}, "75": {"start": {"line": 267, "column": 8}, "end": {"line": 267, "column": 32}}, "76": {"start": {"line": 271, "column": 4}, "end": {"line": 271, "column": 20}}, "77": {"start": {"line": 278, "column": 29}, "end": {"line": 278, "column": 31}}, "78": {"start": {"line": 281, "column": 6}, "end": {"line": 281, "column": 121}}, "79": {"start": {"line": 284, "column": 4}, "end": {"line": 288, "column": 5}}, "80": {"start": {"line": 285, "column": 6}, "end": {"line": 287, "column": 7}}, "81": {"start": {"line": 286, "column": 8}, "end": {"line": 286, "column": 30}}, "82": {"start": {"line": 290, "column": 4}, "end": {"line": 290, "column": 18}}, "83": {"start": {"line": 302, "column": 4}, "end": {"line": 302, "column": 66}}, "84": {"start": {"line": 309, "column": 4}, "end": {"line": 309, "column": 69}}, "85": {"start": {"line": 15, "column": 13}, "end": {"line": 15, "column": 29}}, "86": {"start": {"line": 15, "column": 13}, "end": {"line": 311, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": null}}, "loc": {"start": {"line": 22, "column": 50}, "end": {"line": 23, "column": 6}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 7}}, "loc": {"start": {"line": 28, "column": 44}, "end": {"line": 59, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": 7}}, "loc": {"start": {"line": 64, "column": 34}, "end": {"line": 71, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 76, "column": 2}, "end": {"line": 76, "column": 7}}, "loc": {"start": {"line": 76, "column": 21}, "end": {"line": 78, "column": 3}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 83, "column": 2}, "end": {"line": 83, "column": 7}}, "loc": {"start": {"line": 83, "column": 26}, "end": {"line": 94, "column": 3}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 99, "column": 2}, "end": {"line": 99, "column": 7}}, "loc": {"start": {"line": 99, "column": 56}, "end": {"line": 117, "column": 3}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 122, "column": 2}, "end": {"line": 122, "column": 7}}, "loc": {"start": {"line": 122, "column": 25}, "end": {"line": 125, "column": 3}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 130, "column": 2}, "end": {"line": 130, "column": 7}}, "loc": {"start": {"line": 130, "column": 27}, "end": {"line": 134, "column": 3}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 139, "column": 2}, "end": {"line": 139, "column": 7}}, "loc": {"start": {"line": 139, "column": 26}, "end": {"line": 143, "column": 3}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 148, "column": 2}, "end": {"line": 148, "column": 7}}, "loc": {"start": {"line": 148, "column": 41}, "end": {"line": 153, "column": 3}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 158, "column": 10}, "end": {"line": 158, "column": 25}}, "loc": {"start": {"line": 158, "column": 41}, "end": {"line": 187, "column": 3}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 192, "column": 10}, "end": {"line": 192, "column": 15}}, "loc": {"start": {"line": 192, "column": 44}, "end": {"line": 221, "column": 3}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 226, "column": 10}, "end": {"line": 226, "column": 27}}, "loc": {"start": {"line": 226, "column": 43}, "end": {"line": 241, "column": 3}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 246, "column": 10}, "end": {"line": 246, "column": 22}}, "loc": {"start": {"line": 246, "column": 38}, "end": {"line": 253, "column": 3}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 258, "column": 10}, "end": {"line": 258, "column": 25}}, "loc": {"start": {"line": 258, "column": 41}, "end": {"line": 272, "column": 3}}}, "15": {"name": "(anonymous_19)", "decl": {"start": {"line": 277, "column": 10}, "end": {"line": 277, "column": 23}}, "loc": {"start": {"line": 277, "column": 39}, "end": {"line": 291, "column": 3}}}, "16": {"name": "(anonymous_20)", "decl": {"start": {"line": 296, "column": 10}, "end": {"line": 296, "column": 29}}, "loc": {"start": {"line": 299, "column": 22}, "end": {"line": 303, "column": 3}}}, "17": {"name": "(anonymous_21)", "decl": {"start": {"line": 308, "column": 10}, "end": {"line": 308, "column": 27}}, "loc": {"start": {"line": 308, "column": 43}, "end": {"line": 310, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 35, "column": 4}, "end": {"line": 37, "column": 5}}, "type": "if", "locations": [{"start": {"line": 35, "column": 4}, "end": {"line": 37, "column": 5}}]}, "1": {"loc": {"start": {"line": 40, "column": 4}, "end": {"line": 42, "column": 5}}, "type": "if", "locations": [{"start": {"line": 40, "column": 4}, "end": {"line": 42, "column": 5}}]}, "2": {"loc": {"start": {"line": 65, "column": 18}, "end": {"line": 65, "column": 48}}, "type": "cond-expr", "locations": [{"start": {"line": 65, "column": 30}, "end": {"line": 65, "column": 43}}, {"start": {"line": 65, "column": 46}, "end": {"line": 65, "column": 48}}]}, "3": {"loc": {"start": {"line": 89, "column": 4}, "end": {"line": 91, "column": 5}}, "type": "if", "locations": [{"start": {"line": 89, "column": 4}, "end": {"line": 91, "column": 5}}]}, "4": {"loc": {"start": {"line": 103, "column": 4}, "end": {"line": 114, "column": 5}}, "type": "if", "locations": [{"start": {"line": 103, "column": 4}, "end": {"line": 114, "column": 5}}, {"start": {"line": 112, "column": 11}, "end": {"line": 114, "column": 5}}]}, "5": {"loc": {"start": {"line": 104, "column": 6}, "end": {"line": 106, "column": 7}}, "type": "if", "locations": [{"start": {"line": 104, "column": 6}, "end": {"line": 106, "column": 7}}]}, "6": {"loc": {"start": {"line": 161, "column": 6}, "end": {"line": 163, "column": 7}}, "type": "if", "locations": [{"start": {"line": 161, "column": 6}, "end": {"line": 163, "column": 7}}]}, "7": {"loc": {"start": {"line": 161, "column": 10}, "end": {"line": 161, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 161, "column": 10}, "end": {"line": 161, "column": 18}}, {"start": {"line": 161, "column": 22}, "end": {"line": 161, "column": 49}}]}, "8": {"loc": {"start": {"line": 167, "column": 8}, "end": {"line": 170, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 167, "column": 8}, "end": {"line": 167, "column": 38}}, {"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 39}}, {"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 34}}, {"start": {"line": 170, "column": 8}, "end": {"line": 170, "column": 35}}]}, "9": {"loc": {"start": {"line": 172, "column": 6}, "end": {"line": 174, "column": 7}}, "type": "if", "locations": [{"start": {"line": 172, "column": 6}, "end": {"line": 174, "column": 7}}]}, "10": {"loc": {"start": {"line": 178, "column": 8}, "end": {"line": 180, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 178, "column": 8}, "end": {"line": 178, "column": 41}}, {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 42}}, {"start": {"line": 180, "column": 8}, "end": {"line": 180, "column": 36}}]}, "11": {"loc": {"start": {"line": 235, "column": 6}, "end": {"line": 237, "column": 7}}, "type": "if", "locations": [{"start": {"line": 235, "column": 6}, "end": {"line": 237, "column": 7}}]}, "12": {"loc": {"start": {"line": 235, "column": 10}, "end": {"line": 235, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 235, "column": 10}, "end": {"line": 235, "column": 18}}, {"start": {"line": 235, "column": 22}, "end": {"line": 235, "column": 52}}]}, "13": {"loc": {"start": {"line": 266, "column": 6}, "end": {"line": 268, "column": 7}}, "type": "if", "locations": [{"start": {"line": 266, "column": 6}, "end": {"line": 268, "column": 7}}]}, "14": {"loc": {"start": {"line": 266, "column": 10}, "end": {"line": 266, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 266, "column": 10}, "end": {"line": 266, "column": 18}}, {"start": {"line": 266, "column": 22}, "end": {"line": 266, "column": 50}}]}, "15": {"loc": {"start": {"line": 285, "column": 6}, "end": {"line": 287, "column": 7}}, "type": "if", "locations": [{"start": {"line": 285, "column": 6}, "end": {"line": 287, "column": 7}}]}, "16": {"loc": {"start": {"line": 285, "column": 10}, "end": {"line": 285, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 285, "column": 10}, "end": {"line": 285, "column": 18}}, {"start": {"line": 285, "column": 22}, "end": {"line": 285, "column": 48}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0], "4": [0, 0], "5": [0], "6": [0], "7": [0, 0], "8": [0, 0, 0, 0], "9": [0], "10": [0, 0, 0], "11": [0], "12": [0, 0], "13": [0], "14": [0, 0], "15": [0], "16": [0, 0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/conformance-algorithm.service.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/conformance-algorithm.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "1": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 70}}, "2": {"start": {"line": 53, "column": 40}, "end": {"line": 651, "column": null}}, "3": {"start": {"line": 54, "column": 19}, "end": {"line": 54, "column": 73}}, "4": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 60}}, "5": {"start": {"line": 67, "column": 23}, "end": {"line": 67, "column": 59}}, "6": {"start": {"line": 70, "column": 28}, "end": {"line": 70, "column": 54}}, "7": {"start": {"line": 71, "column": 23}, "end": {"line": 71, "column": 44}}, "8": {"start": {"line": 74, "column": 24}, "end": {"line": 78, "column": null}}, "9": {"start": {"line": 82, "column": 20}, "end": {"line": 82, "column": 71}}, "10": {"start": {"line": 85, "column": 26}, "end": {"line": 93, "column": null}}, "11": {"start": {"line": 86, "column": 6}, "end": {"line": 93, "column": 9}}, "12": {"start": {"line": 86, "column": 48}, "end": {"line": 93, "column": 8}}, "13": {"start": {"line": 96, "column": 36}, "end": {"line": 108, "column": 6}}, "14": {"start": {"line": 102, "column": 51}, "end": {"line": 102, "column": 65}}, "15": {"start": {"line": 104, "column": 41}, "end": {"line": 104, "column": 65}}, "16": {"start": {"line": 111, "column": 4}, "end": {"line": 120, "column": 5}}, "17": {"start": {"line": 112, "column": 6}, "end": {"line": 119, "column": 10}}, "18": {"start": {"line": 112, "column": 61}, "end": {"line": 119, "column": 8}}, "19": {"start": {"line": 122, "column": 4}, "end": {"line": 127, "column": 5}}, "20": {"start": {"line": 123, "column": 6}, "end": {"line": 126, "column": 8}}, "21": {"start": {"line": 129, "column": 4}, "end": {"line": 131, "column": 6}}, "22": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": 18}}, "23": {"start": {"line": 139, "column": 23}, "end": {"line": 139, "column": 52}}, "24": {"start": {"line": 141, "column": 4}, "end": {"line": 146, "column": 7}}, "25": {"start": {"line": 142, "column": 6}, "end": {"line": 144, "column": 7}}, "26": {"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 39}}, "27": {"start": {"line": 145, "column": 6}, "end": {"line": 145, "column": 44}}, "28": {"start": {"line": 149, "column": 4}, "end": {"line": 151, "column": 7}}, "29": {"start": {"line": 150, "column": 6}, "end": {"line": 150, "column": 73}}, "30": {"start": {"line": 150, "column": 26}, "end": {"line": 150, "column": 71}}, "31": {"start": {"line": 153, "column": 4}, "end": {"line": 153, "column": 22}}, "32": {"start": {"line": 165, "column": 52}, "end": {"line": 165, "column": 54}}, "33": {"start": {"line": 167, "column": 4}, "end": {"line": 181, "column": 5}}, "34": {"start": {"line": 168, "column": 20}, "end": {"line": 168, "column": 51}}, "35": {"start": {"line": 168, "column": 38}, "end": {"line": 168, "column": 50}}, "36": {"start": {"line": 169, "column": 30}, "end": {"line": 173, "column": null}}, "37": {"start": {"line": 176, "column": 6}, "end": {"line": 180, "column": 9}}, "38": {"start": {"line": 183, "column": 4}, "end": {"line": 183, "column": 19}}, "39": {"start": {"line": 195, "column": 22}, "end": {"line": 195, "column": 67}}, "40": {"start": {"line": 197, "column": 4}, "end": {"line": 219, "column": 5}}, "41": {"start": {"line": 199, "column": 8}, "end": {"line": 204, "column": 10}}, "42": {"start": {"line": 206, "column": 8}, "end": {"line": 211, "column": 10}}, "43": {"start": {"line": 213, "column": 8}, "end": {"line": 218, "column": 10}}, "44": {"start": {"line": 237, "column": 9}, "end": {"line": 237, "column": 11}}, "45": {"start": {"line": 239, "column": 35}, "end": {"line": 239, "column": 37}}, "46": {"start": {"line": 240, "column": 27}, "end": {"line": 240, "column": 30}}, "47": {"start": {"line": 241, "column": 25}, "end": {"line": 241, "column": 26}}, "48": {"start": {"line": 244, "column": 22}, "end": {"line": 248, "column": 6}}, "49": {"start": {"line": 251, "column": 23}, "end": {"line": 251, "column": 72}}, "50": {"start": {"line": 254, "column": 28}, "end": {"line": 254, "column": 58}}, "51": {"start": {"line": 255, "column": 20}, "end": {"line": 255, "column": 21}}, "52": {"start": {"line": 257, "column": 4}, "end": {"line": 300, "column": 5}}, "53": {"start": {"line": 257, "column": 17}, "end": {"line": 257, "column": 18}}, "54": {"start": {"line": 258, "column": 23}, "end": {"line": 258, "column": 31}}, "55": {"start": {"line": 261, "column": 34}, "end": {"line": 263, "column": null}}, "56": {"start": {"line": 265, "column": 30}, "end": {"line": 266, "column": null}}, "57": {"start": {"line": 266, "column": 15}, "end": {"line": 266, "column": 38}}, "58": {"start": {"line": 269, "column": 6}, "end": {"line": 299, "column": 7}}, "59": {"start": {"line": 271, "column": 8}, "end": {"line": 271, "column": 36}}, "60": {"start": {"line": 272, "column": 8}, "end": {"line": 272, "column": 56}}, "61": {"start": {"line": 273, "column": 8}, "end": {"line": 273, "column": 47}}, "62": {"start": {"line": 274, "column": 13}, "end": {"line": 299, "column": 7}}, "63": {"start": {"line": 276, "column": 8}, "end": {"line": 276, "column": 36}}, "64": {"start": {"line": 277, "column": 8}, "end": {"line": 277, "column": 39}}, "65": {"start": {"line": 279, "column": 8}, "end": {"line": 284, "column": 11}}, "66": {"start": {"line": 285, "column": 8}, "end": {"line": 285, "column": 25}}, "67": {"start": {"line": 286, "column": 8}, "end": {"line": 286, "column": 32}}, "68": {"start": {"line": 289, "column": 8}, "end": {"line": 289, "column": 39}}, "69": {"start": {"line": 291, "column": 8}, "end": {"line": 296, "column": 11}}, "70": {"start": {"line": 297, "column": 8}, "end": {"line": 297, "column": 25}}, "71": {"start": {"line": 298, "column": 8}, "end": {"line": 298, "column": 33}}, "72": {"start": {"line": 303, "column": 30}, "end": {"line": 305, "column": null}}, "73": {"start": {"line": 307, "column": 4}, "end": {"line": 317, "column": 5}}, "74": {"start": {"line": 308, "column": 6}, "end": {"line": 313, "column": 9}}, "75": {"start": {"line": 314, "column": 6}, "end": {"line": 314, "column": 23}}, "76": {"start": {"line": 315, "column": 6}, "end": {"line": 315, "column": 30}}, "77": {"start": {"line": 316, "column": 6}, "end": {"line": 316, "column": 39}}, "78": {"start": {"line": 320, "column": 27}, "end": {"line": 320, "column": 52}}, "79": {"start": {"line": 321, "column": 4}, "end": {"line": 323, "column": 7}}, "80": {"start": {"line": 322, "column": 6}, "end": {"line": 322, "column": 76}}, "81": {"start": {"line": 325, "column": 4}, "end": {"line": 336, "column": 5}}, "82": {"start": {"line": 326, "column": 6}, "end": {"line": 335, "column": 7}}, "83": {"start": {"line": 327, "column": 8}, "end": {"line": 332, "column": 11}}, "84": {"start": {"line": 333, "column": 8}, "end": {"line": 333, "column": 25}}, "85": {"start": {"line": 334, "column": 8}, "end": {"line": 334, "column": 47}}, "86": {"start": {"line": 339, "column": 4}, "end": {"line": 339, "column": 53}}, "87": {"start": {"line": 341, "column": 25}, "end": {"line": 341, "column": 45}}, "88": {"start": {"line": 343, "column": 4}, "end": {"line": 349, "column": 6}}, "89": {"start": {"line": 363, "column": 19}, "end": {"line": 367, "column": null}}, "90": {"start": {"line": 371, "column": 4}, "end": {"line": 371, "column": 76}}, "91": {"start": {"line": 373, "column": 4}, "end": {"line": 373, "column": 18}}, "92": {"start": {"line": 387, "column": 4}, "end": {"line": 392, "column": 6}}, "93": {"start": {"line": 408, "column": 23}, "end": {"line": 408, "column": 41}}, "94": {"start": {"line": 409, "column": 28}, "end": {"line": 409, "column": 76}}, "95": {"start": {"line": 409, "column": 54}, "end": {"line": 409, "column": 68}}, "96": {"start": {"line": 412, "column": 25}, "end": {"line": 412, "column": 74}}, "97": {"start": {"line": 415, "column": 28}, "end": {"line": 417, "column": null}}, "98": {"start": {"line": 416, "column": 18}, "end": {"line": 416, "column": 38}}, "99": {"start": {"line": 419, "column": 30}, "end": {"line": 421, "column": null}}, "100": {"start": {"line": 420, "column": 18}, "end": {"line": 420, "column": 45}}, "101": {"start": {"line": 424, "column": 6}, "end": {"line": 424, "column": 67}}, "102": {"start": {"line": 427, "column": 32}, "end": {"line": 427, "column": 78}}, "103": {"start": {"line": 430, "column": 28}, "end": {"line": 430, "column": 74}}, "104": {"start": {"line": 432, "column": 4}, "end": {"line": 437, "column": 6}}, "105": {"start": {"line": 452, "column": 26}, "end": {"line": 452, "column": null}}, "106": {"start": {"line": 462, "column": 4}, "end": {"line": 486, "column": 7}}, "107": {"start": {"line": 463, "column": 6}, "end": {"line": 485, "column": 9}}, "108": {"start": {"line": 464, "column": 8}, "end": {"line": 470, "column": 9}}, "109": {"start": {"line": 465, "column": 10}, "end": {"line": 469, "column": 13}}, "110": {"start": {"line": 472, "column": 22}, "end": {"line": 472, "column": 50}}, "111": {"start": {"line": 473, "column": 8}, "end": {"line": 473, "column": 26}}, "112": {"start": {"line": 475, "column": 8}, "end": {"line": 477, "column": 9}}, "113": {"start": {"line": 476, "column": 10}, "end": {"line": 476, "column": 34}}, "114": {"start": {"line": 480, "column": 8}, "end": {"line": 484, "column": 11}}, "115": {"start": {"line": 481, "column": 10}, "end": {"line": 483, "column": 11}}, "116": {"start": {"line": 482, "column": 12}, "end": {"line": 482, "column": 57}}, "117": {"start": {"line": 489, "column": 4}, "end": {"line": 495, "column": 8}}, "118": {"start": {"line": 489, "column": 75}, "end": {"line": 495, "column": 6}}, "119": {"start": {"line": 505, "column": 18}, "end": {"line": 505, "column": null}}, "120": {"start": {"line": 511, "column": 19}, "end": {"line": 511, "column": 36}}, "121": {"start": {"line": 512, "column": 4}, "end": {"line": 512, "column": 24}}, "122": {"start": {"line": 513, "column": 4}, "end": {"line": 513, "column": 22}}, "123": {"start": {"line": 515, "column": 4}, "end": {"line": 517, "column": 7}}, "124": {"start": {"line": 516, "column": 6}, "end": {"line": 516, "column": 27}}, "125": {"start": {"line": 520, "column": 4}, "end": {"line": 528, "column": 7}}, "126": {"start": {"line": 521, "column": 6}, "end": {"line": 523, "column": 7}}, "127": {"start": {"line": 522, "column": 8}, "end": {"line": 522, "column": 33}}, "128": {"start": {"line": 524, "column": 6}, "end": {"line": 527, "column": 9}}, "129": {"start": {"line": 531, "column": 4}, "end": {"line": 551, "column": 5}}, "130": {"start": {"line": 532, "column": 6}, "end": {"line": 534, "column": 9}}, "131": {"start": {"line": 536, "column": 6}, "end": {"line": 543, "column": 7}}, "132": {"start": {"line": 536, "column": 19}, "end": {"line": 536, "column": 20}}, "133": {"start": {"line": 537, "column": 8}, "end": {"line": 542, "column": 11}}, "134": {"start": {"line": 545, "column": 6}, "end": {"line": 550, "column": 9}}, "135": {"start": {"line": 553, "column": 4}, "end": {"line": 553, "column": 17}}, "136": {"start": {"line": 562, "column": 4}, "end": {"line": 562, "column": 19}}, "137": {"start": {"line": 572, "column": 4}, "end": {"line": 572, "column": 41}}, "138": {"start": {"line": 582, "column": 40}, "end": {"line": 582, "column": 42}}, "139": {"start": {"line": 585, "column": 4}, "end": {"line": 595, "column": 5}}, "140": {"start": {"line": 586, "column": 34}, "end": {"line": 588, "column": null}}, "141": {"start": {"line": 590, "column": 6}, "end": {"line": 594, "column": 9}}, "142": {"start": {"line": 591, "column": 8}, "end": {"line": 593, "column": 9}}, "143": {"start": {"line": 592, "column": 10}, "end": {"line": 592, "column": 54}}, "144": {"start": {"line": 597, "column": 4}, "end": {"line": 597, "column": 29}}, "145": {"start": {"line": 604, "column": 14}, "end": {"line": 604, "column": 27}}, "146": {"start": {"line": 605, "column": 14}, "end": {"line": 605, "column": 27}}, "147": {"start": {"line": 606, "column": 27}, "end": {"line": 608, "column": 38}}, "148": {"start": {"line": 608, "column": 17}, "end": {"line": 608, "column": 37}}, "149": {"start": {"line": 611, "column": 4}, "end": {"line": 613, "column": 5}}, "150": {"start": {"line": 611, "column": 17}, "end": {"line": 611, "column": 18}}, "151": {"start": {"line": 612, "column": 6}, "end": {"line": 612, "column": 19}}, "152": {"start": {"line": 614, "column": 4}, "end": {"line": 616, "column": 5}}, "153": {"start": {"line": 614, "column": 17}, "end": {"line": 614, "column": 18}}, "154": {"start": {"line": 615, "column": 6}, "end": {"line": 615, "column": 19}}, "155": {"start": {"line": 619, "column": 4}, "end": {"line": 631, "column": 5}}, "156": {"start": {"line": 619, "column": 17}, "end": {"line": 619, "column": 18}}, "157": {"start": {"line": 620, "column": 6}, "end": {"line": 630, "column": 7}}, "158": {"start": {"line": 620, "column": 19}, "end": {"line": 620, "column": 20}}, "159": {"start": {"line": 621, "column": 8}, "end": {"line": 629, "column": 9}}, "160": {"start": {"line": 622, "column": 10}, "end": {"line": 622, "column": 38}}, "161": {"start": {"line": 624, "column": 10}, "end": {"line": 628, "column": 12}}, "162": {"start": {"line": 633, "column": 4}, "end": {"line": 633, "column": 20}}, "163": {"start": {"line": 643, "column": 4}, "end": {"line": 643, "column": 63}}, "164": {"start": {"line": 643, "column": 52}, "end": {"line": 643, "column": 63}}, "165": {"start": {"line": 644, "column": 4}, "end": {"line": 644, "column": 63}}, "166": {"start": {"line": 644, "column": 52}, "end": {"line": 644, "column": 63}}, "167": {"start": {"line": 646, "column": 25}, "end": {"line": 646, "column": 67}}, "168": {"start": {"line": 647, "column": 22}, "end": {"line": 647, "column": 60}}, "169": {"start": {"line": 649, "column": 4}, "end": {"line": 649, "column": 40}}, "170": {"start": {"line": 53, "column": 13}, "end": {"line": 53, "column": 40}}, "171": {"start": {"line": 53, "column": 13}, "end": {"line": 651, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 53, "column": 7}, "end": {"line": 53, "column": 13}}, "loc": {"start": {"line": 53, "column": 7}, "end": {"line": 651, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": 7}}, "loc": {"start": {"line": 62, "column": 20}, "end": {"line": 133, "column": 3}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 85, "column": 46}, "end": {"line": 85, "column": 47}}, "loc": {"start": {"line": 86, "column": 6}, "end": {"line": 93, "column": 9}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 86, "column": 32}, "end": {"line": 86, "column": 33}}, "loc": {"start": {"line": 86, "column": 48}, "end": {"line": 93, "column": 8}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 102, "column": 44}, "end": {"line": 102, "column": 45}}, "loc": {"start": {"line": 102, "column": 51}, "end": {"line": 102, "column": 65}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 104, "column": 29}, "end": {"line": 104, "column": 30}}, "loc": {"start": {"line": 104, "column": 41}, "end": {"line": 104, "column": 65}}}, "6": {"name": "(anonymous_7)", "decl": {"start": {"line": 112, "column": 44}, "end": {"line": 112, "column": 45}}, "loc": {"start": {"line": 112, "column": 61}, "end": {"line": 119, "column": 8}}}, "7": {"name": "(anonymous_8)", "decl": {"start": {"line": 138, "column": 10}, "end": {"line": 138, "column": 30}}, "loc": {"start": {"line": 138, "column": 52}, "end": {"line": 154, "column": 3}}}, "8": {"name": "(anonymous_9)", "decl": {"start": {"line": 141, "column": 22}, "end": {"line": 141, "column": 23}}, "loc": {"start": {"line": 141, "column": 30}, "end": {"line": 146, "column": 5}}}, "9": {"name": "(anonymous_10)", "decl": {"start": {"line": 149, "column": 23}, "end": {"line": 149, "column": 24}}, "loc": {"start": {"line": 149, "column": 32}, "end": {"line": 151, "column": 5}}}, "10": {"name": "(anonymous_11)", "decl": {"start": {"line": 150, "column": 16}, "end": {"line": 150, "column": 17}}, "loc": {"start": {"line": 150, "column": 26}, "end": {"line": 150, "column": 71}}}, "11": {"name": "(anonymous_12)", "decl": {"start": {"line": 159, "column": 10}, "end": {"line": 159, "column": 15}}, "loc": {"start": {"line": 163, "column": 20}, "end": {"line": 184, "column": 3}}}, "12": {"name": "(anonymous_13)", "decl": {"start": {"line": 168, "column": 29}, "end": {"line": 168, "column": 30}}, "loc": {"start": {"line": 168, "column": 38}, "end": {"line": 168, "column": 50}}}, "13": {"name": "(anonymous_14)", "decl": {"start": {"line": 189, "column": 10}, "end": {"line": 189, "column": 20}}, "loc": {"start": {"line": 193, "column": 20}, "end": {"line": 220, "column": 3}}}, "14": {"name": "(anonymous_15)", "decl": {"start": {"line": 225, "column": 10}, "end": {"line": 225, "column": 28}}, "loc": {"start": {"line": 229, "column": 20}, "end": {"line": 350, "column": 3}}}, "15": {"name": "(anonymous_16)", "decl": {"start": {"line": 266, "column": 8}, "end": {"line": 266, "column": 9}}, "loc": {"start": {"line": 266, "column": 15}, "end": {"line": 266, "column": 38}}}, "16": {"name": "(anonymous_17)", "decl": {"start": {"line": 321, "column": 18}, "end": {"line": 321, "column": 19}}, "loc": {"start": {"line": 321, "column": 31}, "end": {"line": 323, "column": 5}}}, "17": {"name": "(anonymous_18)", "decl": {"start": {"line": 355, "column": 10}, "end": {"line": 355, "column": 26}}, "loc": {"start": {"line": 359, "column": 20}, "end": {"line": 374, "column": 3}}}, "18": {"name": "(anonymous_19)", "decl": {"start": {"line": 379, "column": 10}, "end": {"line": 379, "column": 26}}, "loc": {"start": {"line": 383, "column": 20}, "end": {"line": 393, "column": 3}}}, "19": {"name": "(anonymous_20)", "decl": {"start": {"line": 398, "column": 10}, "end": {"line": 398, "column": 26}}, "loc": {"start": {"line": 400, "column": 29}, "end": {"line": 438, "column": 3}}}, "20": {"name": "(anonymous_21)", "decl": {"start": {"line": 409, "column": 47}, "end": {"line": 409, "column": 48}}, "loc": {"start": {"line": 409, "column": 54}, "end": {"line": 409, "column": 68}}}, "21": {"name": "(anonymous_22)", "decl": {"start": {"line": 416, "column": 6}, "end": {"line": 416, "column": 7}}, "loc": {"start": {"line": 416, "column": 18}, "end": {"line": 416, "column": 38}}}, "22": {"name": "(anonymous_23)", "decl": {"start": {"line": 420, "column": 6}, "end": {"line": 420, "column": 7}}, "loc": {"start": {"line": 420, "column": 18}, "end": {"line": 420, "column": 45}}}, "23": {"name": "(anonymous_24)", "decl": {"start": {"line": 443, "column": 10}, "end": {"line": 443, "column": 33}}, "loc": {"start": {"line": 445, "column": 29}, "end": {"line": 496, "column": 3}}}, "24": {"name": "(anonymous_25)", "decl": {"start": {"line": 462, "column": 24}, "end": {"line": 462, "column": 25}}, "loc": {"start": {"line": 462, "column": 39}, "end": {"line": 486, "column": 5}}}, "25": {"name": "(anonymous_26)", "decl": {"start": {"line": 463, "column": 31}, "end": {"line": 463, "column": 32}}, "loc": {"start": {"line": 463, "column": 44}, "end": {"line": 485, "column": 7}}}, "26": {"name": "(anonymous_27)", "decl": {"start": {"line": 480, "column": 38}, "end": {"line": 480, "column": 39}}, "loc": {"start": {"line": 480, "column": 52}, "end": {"line": 484, "column": 9}}}, "27": {"name": "(anonymous_28)", "decl": {"start": {"line": 489, "column": 51}, "end": {"line": 489, "column": 52}}, "loc": {"start": {"line": 489, "column": 75}, "end": {"line": 495, "column": 6}}}, "28": {"name": "(anonymous_29)", "decl": {"start": {"line": 501, "column": 10}, "end": {"line": 501, "column": 25}}, "loc": {"start": {"line": 503, "column": 51}, "end": {"line": 554, "column": 3}}}, "29": {"name": "(anonymous_30)", "decl": {"start": {"line": 515, "column": 28}, "end": {"line": 515, "column": 29}}, "loc": {"start": {"line": 515, "column": 41}, "end": {"line": 517, "column": 5}}}, "30": {"name": "(anonymous_31)", "decl": {"start": {"line": 520, "column": 23}, "end": {"line": 520, "column": 24}}, "loc": {"start": {"line": 520, "column": 32}, "end": {"line": 528, "column": 5}}}, "31": {"name": "(anonymous_32)", "decl": {"start": {"line": 559, "column": 10}, "end": {"line": 559, "column": 23}}, "loc": {"start": {"line": 560, "column": 72}, "end": {"line": 563, "column": 3}}}, "32": {"name": "(anonymous_33)", "decl": {"start": {"line": 568, "column": 10}, "end": {"line": 568, "column": 32}}, "loc": {"start": {"line": 570, "column": 72}, "end": {"line": 573, "column": 3}}}, "33": {"name": "(anonymous_34)", "decl": {"start": {"line": 578, "column": 10}, "end": {"line": 578, "column": 31}}, "loc": {"start": {"line": 580, "column": 72}, "end": {"line": 598, "column": 3}}}, "34": {"name": "(anonymous_35)", "decl": {"start": {"line": 590, "column": 34}, "end": {"line": 590, "column": 35}}, "loc": {"start": {"line": 590, "column": 49}, "end": {"line": 594, "column": 7}}}, "35": {"name": "(anonymous_36)", "decl": {"start": {"line": 603, "column": 10}, "end": {"line": 603, "column": 31}}, "loc": {"start": {"line": 603, "column": 66}, "end": {"line": 634, "column": 3}}}, "36": {"name": "(anonymous_37)", "decl": {"start": {"line": 608, "column": 11}, "end": {"line": 608, "column": 14}}, "loc": {"start": {"line": 608, "column": 17}, "end": {"line": 608, "column": 37}}}, "37": {"name": "(anonymous_38)", "decl": {"start": {"line": 639, "column": 10}, "end": {"line": 639, "column": 37}}, "loc": {"start": {"line": 641, "column": 20}, "end": {"line": 650, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 70, "column": 28}, "end": {"line": 70, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 70, "column": 28}, "end": {"line": 70, "column": 48}}, {"start": {"line": 70, "column": 52}, "end": {"line": 70, "column": 54}}]}, "1": {"loc": {"start": {"line": 71, "column": 23}, "end": {"line": 71, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 71, "column": 23}, "end": {"line": 71, "column": 38}}, {"start": {"line": 71, "column": 42}, "end": {"line": 71, "column": 44}}]}, "2": {"loc": {"start": {"line": 100, "column": 19}, "end": {"line": 100, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 100, "column": 19}, "end": {"line": 100, "column": 49}}, {"start": {"line": 100, "column": 53}, "end": {"line": 100, "column": 64}}]}, "3": {"loc": {"start": {"line": 111, "column": 4}, "end": {"line": 120, "column": 5}}, "type": "if", "locations": [{"start": {"line": 111, "column": 4}, "end": {"line": 120, "column": 5}}]}, "4": {"loc": {"start": {"line": 122, "column": 4}, "end": {"line": 127, "column": 5}}, "type": "if", "locations": [{"start": {"line": 122, "column": 4}, "end": {"line": 127, "column": 5}}]}, "5": {"loc": {"start": {"line": 142, "column": 6}, "end": {"line": 144, "column": 7}}, "type": "if", "locations": [{"start": {"line": 142, "column": 6}, "end": {"line": 144, "column": 7}}]}, "6": {"loc": {"start": {"line": 195, "column": 22}, "end": {"line": 195, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 195, "column": 22}, "end": {"line": 195, "column": 52}}, {"start": {"line": 195, "column": 56}, "end": {"line": 195, "column": 67}}]}, "7": {"loc": {"start": {"line": 197, "column": 4}, "end": {"line": 219, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 198, "column": 6}, "end": {"line": 204, "column": 10}}, {"start": {"line": 205, "column": 6}, "end": {"line": 211, "column": 10}}, {"start": {"line": 212, "column": 6}, "end": {"line": 218, "column": 10}}]}, "8": {"loc": {"start": {"line": 244, "column": 22}, "end": {"line": 248, "column": 6}}, "type": "binary-expr", "locations": [{"start": {"line": 244, "column": 22}, "end": {"line": 244, "column": 43}}, {"start": {"line": 244, "column": 47}, "end": {"line": 248, "column": 6}}]}, "9": {"loc": {"start": {"line": 269, "column": 6}, "end": {"line": 299, "column": 7}}, "type": "if", "locations": [{"start": {"line": 269, "column": 6}, "end": {"line": 299, "column": 7}}, {"start": {"line": 274, "column": 13}, "end": {"line": 299, "column": 7}}]}, "10": {"loc": {"start": {"line": 274, "column": 13}, "end": {"line": 299, "column": 7}}, "type": "if", "locations": [{"start": {"line": 274, "column": 13}, "end": {"line": 299, "column": 7}}, {"start": {"line": 287, "column": 13}, "end": {"line": 299, "column": 7}}]}, "11": {"loc": {"start": {"line": 322, "column": 36}, "end": {"line": 322, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 322, "column": 36}, "end": {"line": 322, "column": 64}}, {"start": {"line": 322, "column": 68}, "end": {"line": 322, "column": 69}}]}, "12": {"loc": {"start": {"line": 326, "column": 6}, "end": {"line": 335, "column": 7}}, "type": "if", "locations": [{"start": {"line": 326, "column": 6}, "end": {"line": 335, "column": 7}}]}, "13": {"loc": {"start": {"line": 326, "column": 10}, "end": {"line": 326, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 326, "column": 10}, "end": {"line": 326, "column": 19}}, {"start": {"line": 326, "column": 23}, "end": {"line": 326, "column": 57}}]}, "14": {"loc": {"start": {"line": 412, "column": 25}, "end": {"line": 412, "column": 74}}, "type": "cond-expr", "locations": [{"start": {"line": 412, "column": 42}, "end": {"line": 412, "column": 70}}, {"start": {"line": 412, "column": 73}, "end": {"line": 412, "column": 74}}]}, "15": {"loc": {"start": {"line": 424, "column": 6}, "end": {"line": 424, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 424, "column": 28}, "end": {"line": 424, "column": 63}}, {"start": {"line": 424, "column": 66}, "end": {"line": 424, "column": 67}}]}, "16": {"loc": {"start": {"line": 464, "column": 8}, "end": {"line": 470, "column": 9}}, "type": "if", "locations": [{"start": {"line": 464, "column": 8}, "end": {"line": 470, "column": 9}}]}, "17": {"loc": {"start": {"line": 475, "column": 8}, "end": {"line": 477, "column": 9}}, "type": "if", "locations": [{"start": {"line": 475, "column": 8}, "end": {"line": 477, "column": 9}}]}, "18": {"loc": {"start": {"line": 481, "column": 10}, "end": {"line": 483, "column": 11}}, "type": "if", "locations": [{"start": {"line": 481, "column": 10}, "end": {"line": 483, "column": 11}}]}, "19": {"loc": {"start": {"line": 493, "column": 8}, "end": {"line": 493, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 493, "column": 30}, "end": {"line": 493, "column": 69}}, {"start": {"line": 493, "column": 72}, "end": {"line": 493, "column": 73}}]}, "20": {"loc": {"start": {"line": 521, "column": 6}, "end": {"line": 523, "column": 7}}, "type": "if", "locations": [{"start": {"line": 521, "column": 6}, "end": {"line": 523, "column": 7}}]}, "21": {"loc": {"start": {"line": 531, "column": 4}, "end": {"line": 551, "column": 5}}, "type": "if", "locations": [{"start": {"line": 531, "column": 4}, "end": {"line": 551, "column": 5}}]}, "22": {"loc": {"start": {"line": 531, "column": 8}, "end": {"line": 531, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 531, "column": 8}, "end": {"line": 531, "column": 31}}, {"start": {"line": 531, "column": 35}, "end": {"line": 531, "column": 61}}]}, "23": {"loc": {"start": {"line": 572, "column": 11}, "end": {"line": 572, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 572, "column": 11}, "end": {"line": 572, "column": 34}}, {"start": {"line": 572, "column": 38}, "end": {"line": 572, "column": 40}}]}, "24": {"loc": {"start": {"line": 585, "column": 4}, "end": {"line": 595, "column": 5}}, "type": "if", "locations": [{"start": {"line": 585, "column": 4}, "end": {"line": 595, "column": 5}}]}, "25": {"loc": {"start": {"line": 591, "column": 8}, "end": {"line": 593, "column": 9}}, "type": "if", "locations": [{"start": {"line": 591, "column": 8}, "end": {"line": 593, "column": 9}}]}, "26": {"loc": {"start": {"line": 621, "column": 8}, "end": {"line": 629, "column": 9}}, "type": "if", "locations": [{"start": {"line": 621, "column": 8}, "end": {"line": 629, "column": 9}}, {"start": {"line": 623, "column": 15}, "end": {"line": 629, "column": 9}}]}, "27": {"loc": {"start": {"line": 643, "column": 4}, "end": {"line": 643, "column": 63}}, "type": "if", "locations": [{"start": {"line": 643, "column": 4}, "end": {"line": 643, "column": 63}}]}, "28": {"loc": {"start": {"line": 643, "column": 8}, "end": {"line": 643, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 643, "column": 8}, "end": {"line": 643, "column": 27}}, {"start": {"line": 643, "column": 31}, "end": {"line": 643, "column": 50}}]}, "29": {"loc": {"start": {"line": 644, "column": 4}, "end": {"line": 644, "column": 63}}, "type": "if", "locations": [{"start": {"line": 644, "column": 4}, "end": {"line": 644, "column": 63}}]}, "30": {"loc": {"start": {"line": 644, "column": 8}, "end": {"line": 644, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 644, "column": 8}, "end": {"line": 644, "column": 27}}, {"start": {"line": 644, "column": 31}, "end": {"line": 644, "column": 50}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0, "145": 0, "146": 0, "147": 0, "148": 0, "149": 0, "150": 0, "151": 0, "152": 0, "153": 0, "154": 0, "155": 0, "156": 0, "157": 0, "158": 0, "159": 0, "160": 0, "161": 0, "162": 0, "163": 0, "164": 0, "165": 0, "166": 0, "167": 0, "168": 0, "169": 0, "170": 1, "171": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0], "4": [0], "5": [0], "6": [0, 0], "7": [0, 0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0], "17": [0], "18": [0], "19": [0, 0], "20": [0], "21": [0], "22": [0, 0], "23": [0, 0], "24": [0], "25": [0], "26": [0, 0], "27": [0], "28": [0, 0], "29": [0], "30": [0, 0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/conformance-cache.service.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/conformance-cache.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 60}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 37}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 54}}, "4": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 74}}, "5": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 56}}, "6": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 33}}, "7": {"start": {"line": 11, "column": 36}, "end": {"line": 532, "column": null}}, "8": {"start": {"line": 16, "column": 12}, "end": {"line": 16, "column": 41}}, "9": {"start": {"line": 18, "column": 12}, "end": {"line": 18, "column": 32}}, "10": {"start": {"line": 20, "column": 12}, "end": {"line": 20, "column": 31}}, "11": {"start": {"line": 12, "column": 19}, "end": {"line": 12, "column": 69}}, "12": {"start": {"line": 30, "column": 4}, "end": {"line": 66, "column": 5}}, "13": {"start": {"line": 32, "column": 23}, "end": {"line": 32, "column": 68}}, "14": {"start": {"line": 35, "column": 25}, "end": {"line": 35, "column": 67}}, "15": {"start": {"line": 36, "column": 6}, "end": {"line": 39, "column": 7}}, "16": {"start": {"line": 37, "column": 8}, "end": {"line": 37, "column": 58}}, "17": {"start": {"line": 38, "column": 8}, "end": {"line": 38, "column": 50}}, "18": {"start": {"line": 42, "column": 23}, "end": {"line": 45, "column": 8}}, "19": {"start": {"line": 47, "column": 6}, "end": {"line": 60, "column": 7}}, "20": {"start": {"line": 49, "column": 32}, "end": {"line": 49, "column": 71}}, "21": {"start": {"line": 50, "column": 8}, "end": {"line": 59, "column": 9}}, "22": {"start": {"line": 51, "column": 10}, "end": {"line": 51, "column": 62}}, "23": {"start": {"line": 54, "column": 10}, "end": {"line": 54, "column": 54}}, "24": {"start": {"line": 56, "column": 10}, "end": {"line": 56, "column": 26}}, "25": {"start": {"line": 58, "column": 10}, "end": {"line": 58, "column": 59}}, "26": {"start": {"line": 62, "column": 6}, "end": {"line": 62, "column": 18}}, "27": {"start": {"line": 64, "column": 6}, "end": {"line": 64, "column": 54}}, "28": {"start": {"line": 65, "column": 6}, "end": {"line": 65, "column": 18}}, "29": {"start": {"line": 73, "column": 4}, "end": {"line": 102, "column": 5}}, "30": {"start": {"line": 74, "column": 23}, "end": {"line": 76, "column": null}}, "31": {"start": {"line": 80, "column": 29}, "end": {"line": 80, "column": 75}}, "32": {"start": {"line": 83, "column": 6}, "end": {"line": 87, "column": 8}}, "33": {"start": {"line": 90, "column": 6}, "end": {"line": 90, "column": 33}}, "34": {"start": {"line": 91, "column": 6}, "end": {"line": 91, "column": 28}}, "35": {"start": {"line": 94, "column": 6}, "end": {"line": 94, "column": 58}}, "36": {"start": {"line": 97, "column": 6}, "end": {"line": 97, "column": 48}}, "37": {"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": 49}}, "38": {"start": {"line": 101, "column": 6}, "end": {"line": 101, "column": 52}}, "39": {"start": {"line": 112, "column": 4}, "end": {"line": 127, "column": 5}}, "40": {"start": {"line": 113, "column": 23}, "end": {"line": 113, "column": 68}}, "41": {"start": {"line": 116, "column": 6}, "end": {"line": 116, "column": 44}}, "42": {"start": {"line": 119, "column": 6}, "end": {"line": 122, "column": 9}}, "43": {"start": {"line": 124, "column": 6}, "end": {"line": 124, "column": 49}}, "44": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 52}}, "45": {"start": {"line": 134, "column": 4}, "end": {"line": 156, "column": 5}}, "46": {"start": {"line": 136, "column": 22}, "end": {"line": 139, "column": 8}}, "47": {"start": {"line": 142, "column": 24}, "end": {"line": 144, "column": 38}}, "48": {"start": {"line": 143, "column": 20}, "end": {"line": 143, "column": 30}}, "49": {"start": {"line": 144, "column": 25}, "end": {"line": 144, "column": 37}}, "50": {"start": {"line": 146, "column": 6}, "end": {"line": 148, "column": 7}}, "51": {"start": {"line": 147, "column": 8}, "end": {"line": 147, "column": 78}}, "52": {"start": {"line": 147, "column": 49}, "end": {"line": 147, "column": 75}}, "53": {"start": {"line": 151, "column": 6}, "end": {"line": 151, "column": 67}}, "54": {"start": {"line": 153, "column": 6}, "end": {"line": 153, "column": 55}}, "55": {"start": {"line": 155, "column": 6}, "end": {"line": 155, "column": 54}}, "56": {"start": {"line": 168, "column": 4}, "end": {"line": 194, "column": 5}}, "57": {"start": {"line": 169, "column": 22}, "end": {"line": 171, "column": 8}}, "58": {"start": {"line": 173, "column": 27}, "end": {"line": 173, "column": 41}}, "59": {"start": {"line": 174, "column": 28}, "end": {"line": 176, "column": 14}}, "60": {"start": {"line": 175, "column": 15}, "end": {"line": 175, "column": 41}}, "61": {"start": {"line": 177, "column": 29}, "end": {"line": 177, "column": 70}}, "62": {"start": {"line": 177, "column": 51}, "end": {"line": 177, "column": 62}}, "63": {"start": {"line": 178, "column": 27}, "end": {"line": 178, "column": 78}}, "64": {"start": {"line": 180, "column": 6}, "end": {"line": 185, "column": 8}}, "65": {"start": {"line": 187, "column": 6}, "end": {"line": 187, "column": 54}}, "66": {"start": {"line": 188, "column": 6}, "end": {"line": 193, "column": 8}}, "67": {"start": {"line": 201, "column": 4}, "end": {"line": 222, "column": 5}}, "68": {"start": {"line": 203, "column": 24}, "end": {"line": 206, "column": 8}}, "69": {"start": {"line": 208, "column": 6}, "end": {"line": 210, "column": 7}}, "70": {"start": {"line": 209, "column": 8}, "end": {"line": 209, "column": 18}}, "71": {"start": {"line": 213, "column": 23}, "end": {"line": 215, "column": 8}}, "72": {"start": {"line": 216, "column": 24}, "end": {"line": 216, "column": 83}}, "73": {"start": {"line": 218, "column": 6}, "end": {"line": 218, "column": 70}}, "74": {"start": {"line": 220, "column": 6}, "end": {"line": 220, "column": 55}}, "75": {"start": {"line": 221, "column": 6}, "end": {"line": 221, "column": 16}}, "76": {"start": {"line": 229, "column": 4}, "end": {"line": 229, "column": 62}}, "77": {"start": {"line": 239, "column": 4}, "end": {"line": 246, "column": 5}}, "78": {"start": {"line": 240, "column": 29}, "end": {"line": 240, "column": 57}}, "79": {"start": {"line": 241, "column": 18}, "end": {"line": 241, "column": 41}}, "80": {"start": {"line": 243, "column": 6}, "end": {"line": 243, "column": 72}}, "81": {"start": {"line": 245, "column": 6}, "end": {"line": 245, "column": 55}}, "82": {"start": {"line": 253, "column": 4}, "end": {"line": 273, "column": 6}}, "83": {"start": {"line": 280, "column": 19}, "end": {"line": 280, "column": 42}}, "84": {"start": {"line": 281, "column": 4}, "end": {"line": 281, "column": 32}}, "85": {"start": {"line": 282, "column": 4}, "end": {"line": 282, "column": 18}}, "86": {"start": {"line": 289, "column": 4}, "end": {"line": 317, "column": 5}}, "87": {"start": {"line": 290, "column": 6}, "end": {"line": 290, "column": 54}}, "88": {"start": {"line": 293, "column": 22}, "end": {"line": 300, "column": 8}}, "89": {"start": {"line": 303, "column": 28}, "end": {"line": 309, "column": 8}}, "90": {"start": {"line": 304, "column": 25}, "end": {"line": 306, "column": null}}, "91": {"start": {"line": 308, "column": 8}, "end": {"line": 308, "column": 50}}, "92": {"start": {"line": 311, "column": 6}, "end": {"line": 311, "column": 39}}, "93": {"start": {"line": 312, "column": 6}, "end": {"line": 314, "column": 8}}, "94": {"start": {"line": 316, "column": 6}, "end": {"line": 316, "column": 52}}, "95": {"start": {"line": 324, "column": 4}, "end": {"line": 356, "column": 5}}, "96": {"start": {"line": 325, "column": 6}, "end": {"line": 325, "column": 40}}, "97": {"start": {"line": 328, "column": 29}, "end": {"line": 332, "column": 8}}, "98": {"start": {"line": 335, "column": 29}, "end": {"line": 339, "column": 8}}, "99": {"start": {"line": 336, "column": 8}, "end": {"line": 338, "column": 9}}, "100": {"start": {"line": 337, "column": 10}, "end": {"line": 337, "column": 55}}, "101": {"start": {"line": 341, "column": 6}, "end": {"line": 341, "column": 40}}, "102": {"start": {"line": 344, "column": 6}, "end": {"line": 349, "column": 7}}, "103": {"start": {"line": 345, "column": 8}, "end": {"line": 348, "column": 10}}, "104": {"start": {"line": 346, "column": 49}, "end": {"line": 346, "column": 53}}, "105": {"start": {"line": 351, "column": 6}, "end": {"line": 353, "column": 8}}, "106": {"start": {"line": 355, "column": 6}, "end": {"line": 355, "column": 54}}, "107": {"start": {"line": 370, "column": 4}, "end": {"line": 429, "column": 5}}, "108": {"start": {"line": 372, "column": 25}, "end": {"line": 374, "column": 8}}, "109": {"start": {"line": 376, "column": 18}, "end": {"line": 376, "column": 28}}, "110": {"start": {"line": 377, "column": 30}, "end": {"line": 377, "column": 75}}, "111": {"start": {"line": 377, "column": 55}, "end": {"line": 377, "column": 67}}, "112": {"start": {"line": 378, "column": 31}, "end": {"line": 378, "column": 75}}, "113": {"start": {"line": 378, "column": 56}, "end": {"line": 378, "column": 67}}, "114": {"start": {"line": 381, "column": 24}, "end": {"line": 383, "column": 76}}, "115": {"start": {"line": 382, "column": 23}, "end": {"line": 382, "column": 43}}, "116": {"start": {"line": 383, "column": 20}, "end": {"line": 383, "column": 75}}, "117": {"start": {"line": 386, "column": 8}, "end": {"line": 391, "column": 13}}, "118": {"start": {"line": 387, "column": 43}, "end": {"line": 387, "column": 52}}, "119": {"start": {"line": 394, "column": 27}, "end": {"line": 394, "column": 52}}, "120": {"start": {"line": 395, "column": 6}, "end": {"line": 400, "column": 9}}, "121": {"start": {"line": 396, "column": 8}, "end": {"line": 399, "column": 10}}, "122": {"start": {"line": 402, "column": 34}, "end": {"line": 405, "column": 70}}, "123": {"start": {"line": 403, "column": 24}, "end": {"line": 403, "column": 35}}, "124": {"start": {"line": 405, "column": 43}, "end": {"line": 405, "column": 68}}, "125": {"start": {"line": 409, "column": 8}, "end": {"line": 409, "column": 67}}, "126": {"start": {"line": 411, "column": 6}, "end": {"line": 418, "column": 8}}, "127": {"start": {"line": 420, "column": 6}, "end": {"line": 420, "column": 56}}, "128": {"start": {"line": 421, "column": 6}, "end": {"line": 428, "column": 8}}, "129": {"start": {"line": 439, "column": 4}, "end": {"line": 490, "column": 5}}, "130": {"start": {"line": 441, "column": 22}, "end": {"line": 445, "column": 8}}, "131": {"start": {"line": 447, "column": 6}, "end": {"line": 449, "column": 7}}, "132": {"start": {"line": 448, "column": 8}, "end": {"line": 448, "column": 20}}, "133": {"start": {"line": 452, "column": 34}, "end": {"line": 452, "column": 36}}, "134": {"start": {"line": 453, "column": 6}, "end": {"line": 457, "column": 7}}, "135": {"start": {"line": 453, "column": 19}, "end": {"line": 453, "column": 20}}, "136": {"start": {"line": 455, "column": 10}, "end": {"line": 455, "column": 77}}, "137": {"start": {"line": 456, "column": 8}, "end": {"line": 456, "column": 33}}, "138": {"start": {"line": 459, "column": 6}, "end": {"line": 461, "column": 7}}, "139": {"start": {"line": 460, "column": 8}, "end": {"line": 460, "column": 20}}, "140": {"start": {"line": 464, "column": 8}, "end": {"line": 465, "column": 24}}, "141": {"start": {"line": 464, "column": 44}, "end": {"line": 464, "column": 58}}, "142": {"start": {"line": 466, "column": 37}, "end": {"line": 466, "column": 59}}, "143": {"start": {"line": 470, "column": 6}, "end": {"line": 481, "column": 7}}, "144": {"start": {"line": 472, "column": 8}, "end": {"line": 472, "column": 28}}, "145": {"start": {"line": 473, "column": 13}, "end": {"line": 481, "column": 7}}, "146": {"start": {"line": 475, "column": 8}, "end": {"line": 475, "column": 28}}, "147": {"start": {"line": 476, "column": 13}, "end": {"line": 481, "column": 7}}, "148": {"start": {"line": 478, "column": 8}, "end": {"line": 478, "column": 28}}, "149": {"start": {"line": 480, "column": 8}, "end": {"line": 480, "column": 27}}, "150": {"start": {"line": 483, "column": 6}, "end": {"line": 485, "column": 8}}, "151": {"start": {"line": 486, "column": 6}, "end": {"line": 486, "column": 26}}, "152": {"start": {"line": 488, "column": 6}, "end": {"line": 488, "column": 55}}, "153": {"start": {"line": 489, "column": 6}, "end": {"line": 489, "column": 18}}, "154": {"start": {"line": 499, "column": 20}, "end": {"line": 499, "column": 21}}, "155": {"start": {"line": 500, "column": 17}, "end": {"line": 500, "column": 18}}, "156": {"start": {"line": 502, "column": 28}, "end": {"line": 525, "column": 6}}, "157": {"start": {"line": 503, "column": 6}, "end": {"line": 524, "column": 7}}, "158": {"start": {"line": 504, "column": 23}, "end": {"line": 511, "column": 10}}, "159": {"start": {"line": 513, "column": 8}, "end": {"line": 520, "column": 9}}, "160": {"start": {"line": 514, "column": 27}, "end": {"line": 516, "column": null}}, "161": {"start": {"line": 518, "column": 10}, "end": {"line": 518, "column": 52}}, "162": {"start": {"line": 519, "column": 10}, "end": {"line": 519, "column": 22}}, "163": {"start": {"line": 522, "column": 8}, "end": {"line": 522, "column": 55}}, "164": {"start": {"line": 523, "column": 8}, "end": {"line": 523, "column": 17}}, "165": {"start": {"line": 527, "column": 4}, "end": {"line": 527, "column": 39}}, "166": {"start": {"line": 529, "column": 4}, "end": {"line": 529, "column": 64}}, "167": {"start": {"line": 530, "column": 4}, "end": {"line": 530, "column": 33}}, "168": {"start": {"line": 11, "column": 13}, "end": {"line": 11, "column": 36}}, "169": {"start": {"line": 11, "column": 13}, "end": {"line": 532, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": null}}, "loc": {"start": {"line": 20, "column": 31}, "end": {"line": 21, "column": 6}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 7}}, "loc": {"start": {"line": 28, "column": 23}, "end": {"line": 67, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 7}}, "loc": {"start": {"line": 72, "column": 45}, "end": {"line": 103, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 108, "column": 2}, "end": {"line": 108, "column": 7}}, "loc": {"start": {"line": 110, "column": 23}, "end": {"line": 128, "column": 3}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 133, "column": 2}, "end": {"line": 133, "column": 7}}, "loc": {"start": {"line": 133, "column": 43}, "end": {"line": 157, "column": 3}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 143, "column": 13}, "end": {"line": 143, "column": 14}}, "loc": {"start": {"line": 143, "column": 20}, "end": {"line": 143, "column": 30}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 144, "column": 16}, "end": {"line": 144, "column": 17}}, "loc": {"start": {"line": 144, "column": 25}, "end": {"line": 144, "column": 37}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 147, "column": 40}, "end": {"line": 147, "column": 41}}, "loc": {"start": {"line": 147, "column": 49}, "end": {"line": 147, "column": 75}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 162, "column": 2}, "end": {"line": 162, "column": 7}}, "loc": {"start": {"line": 162, "column": 39}, "end": {"line": 195, "column": 3}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 175, "column": 8}, "end": {"line": 175, "column": 9}}, "loc": {"start": {"line": 175, "column": 15}, "end": {"line": 175, "column": 41}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 177, "column": 44}, "end": {"line": 177, "column": 45}}, "loc": {"start": {"line": 177, "column": 51}, "end": {"line": 177, "column": 62}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 200, "column": 2}, "end": {"line": 200, "column": 7}}, "loc": {"start": {"line": 200, "column": 43}, "end": {"line": 223, "column": 3}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 228, "column": 10}, "end": {"line": 228, "column": 26}}, "loc": {"start": {"line": 228, "column": 65}, "end": {"line": 230, "column": 3}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 235, "column": 10}, "end": {"line": 235, "column": 15}}, "loc": {"start": {"line": 237, "column": 29}, "end": {"line": 247, "column": 3}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 252, "column": 10}, "end": {"line": 252, "column": 25}}, "loc": {"start": {"line": 252, "column": 51}, "end": {"line": 274, "column": 3}}}, "15": {"name": "(anonymous_19)", "decl": {"start": {"line": 279, "column": 10}, "end": {"line": 279, "column": 27}}, "loc": {"start": {"line": 279, "column": 37}, "end": {"line": 283, "column": 3}}}, "16": {"name": "(anonymous_20)", "decl": {"start": {"line": 288, "column": 2}, "end": {"line": 288, "column": 7}}, "loc": {"start": {"line": 288, "column": 37}, "end": {"line": 318, "column": 3}}}, "17": {"name": "(anonymous_21)", "decl": {"start": {"line": 303, "column": 40}, "end": {"line": 303, "column": 45}}, "loc": {"start": {"line": 303, "column": 57}, "end": {"line": 309, "column": 7}}}, "18": {"name": "(anonymous_22)", "decl": {"start": {"line": 323, "column": 2}, "end": {"line": 323, "column": 7}}, "loc": {"start": {"line": 323, "column": 27}, "end": {"line": 357, "column": 3}}}, "19": {"name": "(anonymous_23)", "decl": {"start": {"line": 335, "column": 48}, "end": {"line": 335, "column": 53}}, "loc": {"start": {"line": 335, "column": 65}, "end": {"line": 339, "column": 7}}}, "20": {"name": "(anonymous_24)", "decl": {"start": {"line": 346, "column": 42}, "end": {"line": 346, "column": 43}}, "loc": {"start": {"line": 346, "column": 49}, "end": {"line": 346, "column": 53}}}, "21": {"name": "(anonymous_25)", "decl": {"start": {"line": 362, "column": 2}, "end": {"line": 362, "column": 7}}, "loc": {"start": {"line": 362, "column": 26}, "end": {"line": 430, "column": 3}}}, "22": {"name": "(anonymous_26)", "decl": {"start": {"line": 377, "column": 48}, "end": {"line": 377, "column": 49}}, "loc": {"start": {"line": 377, "column": 55}, "end": {"line": 377, "column": 67}}}, "23": {"name": "(anonymous_27)", "decl": {"start": {"line": 378, "column": 49}, "end": {"line": 378, "column": 50}}, "loc": {"start": {"line": 378, "column": 56}, "end": {"line": 378, "column": 67}}}, "24": {"name": "(anonymous_28)", "decl": {"start": {"line": 382, "column": 16}, "end": {"line": 382, "column": 17}}, "loc": {"start": {"line": 382, "column": 23}, "end": {"line": 382, "column": 43}}}, "25": {"name": "(anonymous_29)", "decl": {"start": {"line": 383, "column": 13}, "end": {"line": 383, "column": 14}}, "loc": {"start": {"line": 383, "column": 20}, "end": {"line": 383, "column": 75}}}, "26": {"name": "(anonymous_30)", "decl": {"start": {"line": 387, "column": 29}, "end": {"line": 387, "column": 30}}, "loc": {"start": {"line": 387, "column": 43}, "end": {"line": 387, "column": 52}}}, "27": {"name": "(anonymous_31)", "decl": {"start": {"line": 395, "column": 25}, "end": {"line": 395, "column": 26}}, "loc": {"start": {"line": 395, "column": 36}, "end": {"line": 400, "column": 7}}}, "28": {"name": "(anonymous_32)", "decl": {"start": {"line": 403, "column": 14}, "end": {"line": 403, "column": 15}}, "loc": {"start": {"line": 403, "column": 24}, "end": {"line": 403, "column": 35}}}, "29": {"name": "(anonymous_33)", "decl": {"start": {"line": 405, "column": 13}, "end": {"line": 405, "column": 14}}, "loc": {"start": {"line": 405, "column": 43}, "end": {"line": 405, "column": 68}}}, "30": {"name": "(anonymous_34)", "decl": {"start": {"line": 435, "column": 2}, "end": {"line": 435, "column": 7}}, "loc": {"start": {"line": 437, "column": 23}, "end": {"line": 491, "column": 3}}}, "31": {"name": "(anonymous_35)", "decl": {"start": {"line": 464, "column": 25}, "end": {"line": 464, "column": 26}}, "loc": {"start": {"line": 464, "column": 44}, "end": {"line": 464, "column": 58}}}, "32": {"name": "(anonymous_36)", "decl": {"start": {"line": 496, "column": 2}, "end": {"line": 496, "column": 7}}, "loc": {"start": {"line": 497, "column": 63}, "end": {"line": 531, "column": 3}}}, "33": {"name": "(anonymous_37)", "decl": {"start": {"line": 502, "column": 41}, "end": {"line": 502, "column": 46}}, "loc": {"start": {"line": 502, "column": 59}, "end": {"line": 525, "column": 5}}}}, "branchMap": {"0": {"loc": {"start": {"line": 36, "column": 6}, "end": {"line": 39, "column": 7}}, "type": "if", "locations": [{"start": {"line": 36, "column": 6}, "end": {"line": 39, "column": 7}}]}, "1": {"loc": {"start": {"line": 47, "column": 6}, "end": {"line": 60, "column": 7}}, "type": "if", "locations": [{"start": {"line": 47, "column": 6}, "end": {"line": 60, "column": 7}}]}, "2": {"loc": {"start": {"line": 47, "column": 10}, "end": {"line": 47, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 47, "column": 10}, "end": {"line": 47, "column": 18}}, {"start": {"line": 47, "column": 22}, "end": {"line": 47, "column": 44}}]}, "3": {"loc": {"start": {"line": 50, "column": 8}, "end": {"line": 59, "column": 9}}, "type": "if", "locations": [{"start": {"line": 50, "column": 8}, "end": {"line": 59, "column": 9}}, {"start": {"line": 57, "column": 15}, "end": {"line": 59, "column": 9}}]}, "4": {"loc": {"start": {"line": 146, "column": 6}, "end": {"line": 148, "column": 7}}, "type": "if", "locations": [{"start": {"line": 146, "column": 6}, "end": {"line": 148, "column": 7}}]}, "5": {"loc": {"start": {"line": 175, "column": 15}, "end": {"line": 175, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 175, "column": 15}, "end": {"line": 175, "column": 25}}, {"start": {"line": 175, "column": 29}, "end": {"line": 175, "column": 41}}]}, "6": {"loc": {"start": {"line": 178, "column": 27}, "end": {"line": 178, "column": 78}}, "type": "cond-expr", "locations": [{"start": {"line": 178, "column": 46}, "end": {"line": 178, "column": 74}}, {"start": {"line": 178, "column": 77}, "end": {"line": 178, "column": 78}}]}, "7": {"loc": {"start": {"line": 208, "column": 6}, "end": {"line": 210, "column": 7}}, "type": "if", "locations": [{"start": {"line": 208, "column": 6}, "end": {"line": 210, "column": 7}}]}, "8": {"loc": {"start": {"line": 241, "column": 18}, "end": {"line": 241, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 241, "column": 18}, "end": {"line": 241, "column": 33}}, {"start": {"line": 241, "column": 37}, "end": {"line": 241, "column": 41}}]}, "9": {"loc": {"start": {"line": 336, "column": 8}, "end": {"line": 338, "column": 9}}, "type": "if", "locations": [{"start": {"line": 336, "column": 8}, "end": {"line": 338, "column": 9}}]}, "10": {"loc": {"start": {"line": 344, "column": 6}, "end": {"line": 349, "column": 7}}, "type": "if", "locations": [{"start": {"line": 344, "column": 6}, "end": {"line": 349, "column": 7}}]}, "11": {"loc": {"start": {"line": 386, "column": 8}, "end": {"line": 391, "column": 13}}, "type": "cond-expr", "locations": [{"start": {"line": 387, "column": 12}, "end": {"line": 390, "column": 14}}, {"start": {"line": 391, "column": 12}, "end": {"line": 391, "column": 13}}]}, "12": {"loc": {"start": {"line": 398, "column": 11}, "end": {"line": 398, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 398, "column": 11}, "end": {"line": 398, "column": 45}}, {"start": {"line": 398, "column": 49}, "end": {"line": 398, "column": 50}}]}, "13": {"loc": {"start": {"line": 409, "column": 8}, "end": {"line": 409, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 409, "column": 8}, "end": {"line": 409, "column": 62}}, {"start": {"line": 409, "column": 66}, "end": {"line": 409, "column": 67}}]}, "14": {"loc": {"start": {"line": 447, "column": 6}, "end": {"line": 449, "column": 7}}, "type": "if", "locations": [{"start": {"line": 447, "column": 6}, "end": {"line": 449, "column": 7}}]}, "15": {"loc": {"start": {"line": 459, "column": 6}, "end": {"line": 461, "column": 7}}, "type": "if", "locations": [{"start": {"line": 459, "column": 6}, "end": {"line": 461, "column": 7}}]}, "16": {"loc": {"start": {"line": 470, "column": 6}, "end": {"line": 481, "column": 7}}, "type": "if", "locations": [{"start": {"line": 470, "column": 6}, "end": {"line": 481, "column": 7}}, {"start": {"line": 473, "column": 13}, "end": {"line": 481, "column": 7}}]}, "17": {"loc": {"start": {"line": 473, "column": 13}, "end": {"line": 481, "column": 7}}, "type": "if", "locations": [{"start": {"line": 473, "column": 13}, "end": {"line": 481, "column": 7}}, {"start": {"line": 476, "column": 13}, "end": {"line": 481, "column": 7}}]}, "18": {"loc": {"start": {"line": 476, "column": 13}, "end": {"line": 481, "column": 7}}, "type": "if", "locations": [{"start": {"line": 476, "column": 13}, "end": {"line": 481, "column": 7}}, {"start": {"line": 479, "column": 13}, "end": {"line": 481, "column": 7}}]}, "19": {"loc": {"start": {"line": 513, "column": 8}, "end": {"line": 520, "column": 9}}, "type": "if", "locations": [{"start": {"line": 513, "column": 8}, "end": {"line": 520, "column": 9}}]}, "20": {"loc": {"start": {"line": 513, "column": 12}, "end": {"line": 513, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 513, "column": 12}, "end": {"line": 513, "column": 18}}, {"start": {"line": 513, "column": 22}, "end": {"line": 513, "column": 39}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0, "145": 0, "146": 0, "147": 0, "148": 0, "149": 0, "150": 0, "151": 0, "152": 0, "153": 0, "154": 0, "155": 0, "156": 0, "157": 0, "158": 0, "159": 0, "160": 0, "161": 0, "162": 0, "163": 0, "164": 0, "165": 0, "166": 0, "167": 0, "168": 1, "169": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0], "5": [0, 0], "6": [0, 0], "7": [0], "8": [0, 0], "9": [0], "10": [0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0], "15": [0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0], "20": [0, 0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/conformance.controller.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/conformance.controller.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 59}}, "2": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": null}}, "3": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 37}}, "4": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 31}}, "5": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 25}}, "6": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 59}}, "7": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 56}}, "8": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 70}}, "9": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": null}}, "10": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 61}}, "11": {"start": {"line": 45, "column": 7}, "end": {"line": 653, "column": null}}, "12": {"start": {"line": 47, "column": 21}, "end": {"line": 47, "column": 41}}, "13": {"start": {"line": 48, "column": 21}, "end": {"line": 48, "column": 39}}, "14": {"start": {"line": 49, "column": 21}, "end": {"line": 49, "column": 46}}, "15": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 51}}, "16": {"start": {"line": 107, "column": 4}, "end": {"line": 109, "column": 5}}, "17": {"start": {"line": 108, "column": 6}, "end": {"line": 108, "column": 53}}, "18": {"start": {"line": 111, "column": 4}, "end": {"line": 142, "column": 5}}, "19": {"start": {"line": 113, "column": 22}, "end": {"line": 113, "column": 57}}, "20": {"start": {"line": 116, "column": 44}, "end": {"line": 124, "column": 8}}, "21": {"start": {"line": 126, "column": 21}, "end": {"line": 126, "column": 66}}, "22": {"start": {"line": 129, "column": 6}, "end": {"line": 129, "column": 31}}, "23": {"start": {"line": 131, "column": 6}, "end": {"line": 135, "column": 8}}, "24": {"start": {"line": 138, "column": 6}, "end": {"line": 140, "column": 7}}, "25": {"start": {"line": 139, "column": 8}, "end": {"line": 139, "column": 33}}, "26": {"start": {"line": 141, "column": 6}, "end": {"line": 141, "column": 68}}, "27": {"start": {"line": 155, "column": 4}, "end": {"line": 155, "column": 52}}, "28": {"start": {"line": 164, "column": 4}, "end": {"line": 164, "column": 45}}, "29": {"start": {"line": 176, "column": 4}, "end": {"line": 176, "column": 55}}, "30": {"start": {"line": 185, "column": 4}, "end": {"line": 185, "column": 43}}, "31": {"start": {"line": 186, "column": 4}, "end": {"line": 186, "column": 37}}, "32": {"start": {"line": 194, "column": 4}, "end": {"line": 194, "column": 46}}, "33": {"start": {"line": 202, "column": 4}, "end": {"line": 202, "column": 45}}, "34": {"start": {"line": 220, "column": 6}, "end": {"line": 220, "column": 69}}, "35": {"start": {"line": 222, "column": 4}, "end": {"line": 244, "column": 6}}, "36": {"start": {"line": 261, "column": 19}, "end": {"line": 263, "column": null}}, "37": {"start": {"line": 266, "column": 4}, "end": {"line": 268, "column": 5}}, "38": {"start": {"line": 267, "column": 6}, "end": {"line": 267, "column": 18}}, "39": {"start": {"line": 270, "column": 4}, "end": {"line": 275, "column": 6}}, "40": {"start": {"line": 285, "column": 4}, "end": {"line": 285, "column": 68}}, "41": {"start": {"line": 293, "column": 4}, "end": {"line": 293, "column": 62}}, "42": {"start": {"line": 294, "column": 4}, "end": {"line": 294, "column": 38}}, "43": {"start": {"line": 304, "column": 4}, "end": {"line": 304, "column": 65}}, "44": {"start": {"line": 312, "column": 4}, "end": {"line": 312, "column": 68}}, "45": {"start": {"line": 313, "column": 4}, "end": {"line": 313, "column": 37}}, "46": {"start": {"line": 325, "column": 4}, "end": {"line": 328, "column": 6}}, "47": {"start": {"line": 329, "column": 4}, "end": {"line": 329, "column": 37}}, "48": {"start": {"line": 339, "column": 24}, "end": {"line": 339, "column": 67}}, "49": {"start": {"line": 342, "column": 23}, "end": {"line": 342, "column": 79}}, "50": {"start": {"line": 344, "column": 24}, "end": {"line": 344, "column": 41}}, "51": {"start": {"line": 348, "column": 6}, "end": {"line": 351, "column": 11}}, "52": {"start": {"line": 349, "column": 45}, "end": {"line": 349, "column": 74}}, "53": {"start": {"line": 354, "column": 25}, "end": {"line": 354, "column": 35}}, "54": {"start": {"line": 355, "column": 4}, "end": {"line": 355, "column": 28}}, "55": {"start": {"line": 356, "column": 4}, "end": {"line": 356, "column": 38}}, "56": {"start": {"line": 358, "column": 25}, "end": {"line": 360, "column": 12}}, "57": {"start": {"line": 359, "column": 18}, "end": {"line": 359, "column": 50}}, "58": {"start": {"line": 362, "column": 4}, "end": {"line": 367, "column": 6}}, "59": {"start": {"line": 380, "column": 24}, "end": {"line": 380, "column": 55}}, "60": {"start": {"line": 382, "column": 6}, "end": {"line": 382, "column": 76}}, "61": {"start": {"line": 384, "column": 4}, "end": {"line": 405, "column": 8}}, "62": {"start": {"line": 384, "column": 36}, "end": {"line": 405, "column": 6}}, "63": {"start": {"line": 416, "column": 6}, "end": {"line": 416, "column": 68}}, "64": {"start": {"line": 417, "column": 19}, "end": {"line": 417, "column": 65}}, "65": {"start": {"line": 419, "column": 4}, "end": {"line": 430, "column": 5}}, "66": {"start": {"line": 420, "column": 6}, "end": {"line": 429, "column": 8}}, "67": {"start": {"line": 432, "column": 30}, "end": {"line": 432, "column": 68}}, "68": {"start": {"line": 432, "column": 49}, "end": {"line": 432, "column": 67}}, "69": {"start": {"line": 434, "column": 6}, "end": {"line": 435, "column": 30}}, "70": {"start": {"line": 434, "column": 47}, "end": {"line": 434, "column": 58}}, "71": {"start": {"line": 436, "column": 22}, "end": {"line": 436, "column": 52}}, "72": {"start": {"line": 437, "column": 23}, "end": {"line": 437, "column": 53}}, "73": {"start": {"line": 440, "column": 31}, "end": {"line": 440, "column": 56}}, "74": {"start": {"line": 441, "column": 4}, "end": {"line": 445, "column": 7}}, "75": {"start": {"line": 442, "column": 6}, "end": {"line": 444, "column": 9}}, "76": {"start": {"line": 443, "column": 8}, "end": {"line": 443, "column": 78}}, "77": {"start": {"line": 447, "column": 33}, "end": {"line": 450, "column": 48}}, "78": {"start": {"line": 448, "column": 22}, "end": {"line": 448, "column": 33}}, "79": {"start": {"line": 450, "column": 31}, "end": {"line": 450, "column": 46}}, "80": {"start": {"line": 452, "column": 4}, "end": {"line": 467, "column": 6}}, "81": {"start": {"line": 460, "column": 56}, "end": {"line": 466, "column": 8}}, "82": {"start": {"line": 484, "column": 23}, "end": {"line": 484, "column": 53}}, "83": {"start": {"line": 486, "column": 6}, "end": {"line": 486, "column": 68}}, "84": {"start": {"line": 487, "column": 23}, "end": {"line": 487, "column": 33}}, "85": {"start": {"line": 488, "column": 4}, "end": {"line": 488, "column": 58}}, "86": {"start": {"line": 490, "column": 26}, "end": {"line": 490, "column": 74}}, "87": {"start": {"line": 490, "column": 48}, "end": {"line": 490, "column": 73}}, "88": {"start": {"line": 492, "column": 4}, "end": {"line": 500, "column": 5}}, "89": {"start": {"line": 493, "column": 6}, "end": {"line": 499, "column": 8}}, "90": {"start": {"line": 503, "column": 24}, "end": {"line": 503, "column": 51}}, "91": {"start": {"line": 504, "column": 4}, "end": {"line": 510, "column": 7}}, "92": {"start": {"line": 505, "column": 22}, "end": {"line": 505, "column": 66}}, "93": {"start": {"line": 506, "column": 6}, "end": {"line": 508, "column": 7}}, "94": {"start": {"line": 507, "column": 8}, "end": {"line": 507, "column": 37}}, "95": {"start": {"line": 509, "column": 6}, "end": {"line": 509, "column": 62}}, "96": {"start": {"line": 513, "column": 23}, "end": {"line": 520, "column": 51}}, "97": {"start": {"line": 514, "column": 32}, "end": {"line": 519, "column": 8}}, "98": {"start": {"line": 517, "column": 40}, "end": {"line": 517, "column": 51}}, "99": {"start": {"line": 520, "column": 22}, "end": {"line": 520, "column": 50}}, "100": {"start": {"line": 523, "column": 16}, "end": {"line": 523, "column": 24}}, "101": {"start": {"line": 524, "column": 22}, "end": {"line": 524, "column": 23}}, "102": {"start": {"line": 525, "column": 4}, "end": {"line": 532, "column": 5}}, "103": {"start": {"line": 526, "column": 25}, "end": {"line": 526, "column": 51}}, "104": {"start": {"line": 527, "column": 24}, "end": {"line": 527, "column": 70}}, "105": {"start": {"line": 528, "column": 6}, "end": {"line": 528, "column": 66}}, "106": {"start": {"line": 530, "column": 6}, "end": {"line": 531, "column": 53}}, "107": {"start": {"line": 530, "column": 27}, "end": {"line": 530, "column": 47}}, "108": {"start": {"line": 531, "column": 11}, "end": {"line": 531, "column": 53}}, "109": {"start": {"line": 531, "column": 33}, "end": {"line": 531, "column": 53}}, "110": {"start": {"line": 534, "column": 4}, "end": {"line": 540, "column": 6}}, "111": {"start": {"line": 548, "column": 18}, "end": {"line": 548, "column": 57}}, "112": {"start": {"line": 550, "column": 23}, "end": {"line": 559, "column": 6}}, "113": {"start": {"line": 562, "column": 4}, "end": {"line": 565, "column": 5}}, "114": {"start": {"line": 563, "column": 6}, "end": {"line": 563, "column": 33}}, "115": {"start": {"line": 564, "column": 6}, "end": {"line": 564, "column": 45}}, "116": {"start": {"line": 567, "column": 4}, "end": {"line": 570, "column": 5}}, "117": {"start": {"line": 568, "column": 6}, "end": {"line": 568, "column": 33}}, "118": {"start": {"line": 569, "column": 6}, "end": {"line": 569, "column": 45}}, "119": {"start": {"line": 572, "column": 4}, "end": {"line": 574, "column": 5}}, "120": {"start": {"line": 573, "column": 6}, "end": {"line": 573, "column": 46}}, "121": {"start": {"line": 576, "column": 4}, "end": {"line": 578, "column": 5}}, "122": {"start": {"line": 577, "column": 6}, "end": {"line": 577, "column": 54}}, "123": {"start": {"line": 581, "column": 21}, "end": {"line": 584, "column": null}}, "124": {"start": {"line": 583, "column": 8}, "end": {"line": 584, "column": 45}}, "125": {"start": {"line": 586, "column": 19}, "end": {"line": 589, "column": null}}, "126": {"start": {"line": 588, "column": 8}, "end": {"line": 589, "column": 45}}, "127": {"start": {"line": 592, "column": 4}, "end": {"line": 594, "column": 5}}, "128": {"start": {"line": 593, "column": 6}, "end": {"line": 593, "column": 46}}, "129": {"start": {"line": 595, "column": 4}, "end": {"line": 597, "column": 5}}, "130": {"start": {"line": 596, "column": 6}, "end": {"line": 596, "column": 46}}, "131": {"start": {"line": 599, "column": 4}, "end": {"line": 599, "column": 22}}, "132": {"start": {"line": 619, "column": 9}, "end": {"line": 619, "column": 11}}, "133": {"start": {"line": 621, "column": 4}, "end": {"line": 644, "column": 5}}, "134": {"start": {"line": 622, "column": 6}, "end": {"line": 643, "column": 7}}, "135": {"start": {"line": 623, "column": 46}, "end": {"line": 628, "column": 10}}, "136": {"start": {"line": 631, "column": 10}, "end": {"line": 631, "column": 73}}, "137": {"start": {"line": 632, "column": 8}, "end": {"line": 636, "column": 11}}, "138": {"start": {"line": 638, "column": 8}, "end": {"line": 642, "column": 11}}, "139": {"start": {"line": 646, "column": 4}, "end": {"line": 651, "column": 6}}, "140": {"start": {"line": 650, "column": 48}, "end": {"line": 650, "column": 70}}, "141": {"start": {"line": 45, "column": 13}, "end": {"line": 45, "column": 34}}, "142": {"start": {"line": 58, "column": 8}, "end": {"line": 60, "column": null}}, "143": {"start": {"line": 96, "column": 8}, "end": {"line": 143, "column": null}}, "144": {"start": {"line": 72, "column": 29}, "end": {"line": 75, "column": 21}}, "145": {"start": {"line": 74, "column": 23}, "end": {"line": 74, "column": 66}}, "146": {"start": {"line": 76, "column": 10}, "end": {"line": 76, "column": 65}}, "147": {"start": {"line": 80, "column": 8}, "end": {"line": 89, "column": 9}}, "148": {"start": {"line": 86, "column": 10}, "end": {"line": 86, "column": 25}}, "149": {"start": {"line": 88, "column": 10}, "end": {"line": 88, "column": 64}}, "150": {"start": {"line": 154, "column": 8}, "end": {"line": 156, "column": null}}, "151": {"start": {"line": 163, "column": 8}, "end": {"line": 165, "column": null}}, "152": {"start": {"line": 172, "column": 8}, "end": {"line": 177, "column": null}}, "153": {"start": {"line": 184, "column": 8}, "end": {"line": 187, "column": null}}, "154": {"start": {"line": 193, "column": 8}, "end": {"line": 195, "column": null}}, "155": {"start": {"line": 201, "column": 8}, "end": {"line": 203, "column": null}}, "156": {"start": {"line": 216, "column": 8}, "end": {"line": 245, "column": null}}, "157": {"start": {"line": 257, "column": 8}, "end": {"line": 276, "column": null}}, "158": {"start": {"line": 282, "column": 8}, "end": {"line": 286, "column": null}}, "159": {"start": {"line": 292, "column": 8}, "end": {"line": 295, "column": null}}, "160": {"start": {"line": 303, "column": 8}, "end": {"line": 305, "column": null}}, "161": {"start": {"line": 311, "column": 8}, "end": {"line": 314, "column": null}}, "162": {"start": {"line": 321, "column": 8}, "end": {"line": 330, "column": null}}, "163": {"start": {"line": 337, "column": 8}, "end": {"line": 368, "column": null}}, "164": {"start": {"line": 379, "column": 8}, "end": {"line": 406, "column": null}}, "165": {"start": {"line": 412, "column": 8}, "end": {"line": 468, "column": null}}, "166": {"start": {"line": 480, "column": 8}, "end": {"line": 541, "column": null}}, "167": {"start": {"line": 547, "column": 8}, "end": {"line": 600, "column": null}}, "168": {"start": {"line": 605, "column": 8}, "end": {"line": 652, "column": null}}, "169": {"start": {"line": 45, "column": 13}, "end": {"line": 653, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": null}}, "loc": {"start": {"line": 49, "column": 69}, "end": {"line": 50, "column": 6}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": 7}}, "loc": {"start": {"line": 58, "column": 61}, "end": {"line": 60, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 96, "column": 2}, "end": {"line": 96, "column": 7}}, "loc": {"start": {"line": 105, "column": 5}, "end": {"line": 143, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 154, "column": 2}, "end": {"line": 154, "column": 7}}, "loc": {"start": {"line": 154, "column": 60}, "end": {"line": 156, "column": 3}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 163, "column": 2}, "end": {"line": 163, "column": 7}}, "loc": {"start": {"line": 163, "column": 58}, "end": {"line": 165, "column": 3}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 172, "column": 2}, "end": {"line": 172, "column": 7}}, "loc": {"start": {"line": 174, "column": 41}, "end": {"line": 177, "column": 3}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 184, "column": 2}, "end": {"line": 184, "column": 7}}, "loc": {"start": {"line": 184, "column": 61}, "end": {"line": 187, "column": 3}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 193, "column": 2}, "end": {"line": 193, "column": 7}}, "loc": {"start": {"line": 193, "column": 63}, "end": {"line": 195, "column": 3}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 201, "column": 2}, "end": {"line": 201, "column": 7}}, "loc": {"start": {"line": 201, "column": 62}, "end": {"line": 203, "column": 3}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 216, "column": 2}, "end": {"line": 216, "column": 7}}, "loc": {"start": {"line": 217, "column": 41}, "end": {"line": 245, "column": 3}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 257, "column": 2}, "end": {"line": 257, "column": 7}}, "loc": {"start": {"line": 259, "column": 59}, "end": {"line": 276, "column": 3}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 282, "column": 2}, "end": {"line": 282, "column": 7}}, "loc": {"start": {"line": 283, "column": 55}, "end": {"line": 286, "column": 3}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 292, "column": 2}, "end": {"line": 292, "column": 7}}, "loc": {"start": {"line": 292, "column": 69}, "end": {"line": 295, "column": 3}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 303, "column": 2}, "end": {"line": 303, "column": 7}}, "loc": {"start": {"line": 303, "column": 73}, "end": {"line": 305, "column": 3}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 311, "column": 2}, "end": {"line": 311, "column": 7}}, "loc": {"start": {"line": 311, "column": 77}, "end": {"line": 314, "column": 3}}}, "15": {"name": "(anonymous_19)", "decl": {"start": {"line": 321, "column": 2}, "end": {"line": 321, "column": 7}}, "loc": {"start": {"line": 323, "column": 59}, "end": {"line": 330, "column": 3}}}, "16": {"name": "(anonymous_20)", "decl": {"start": {"line": 337, "column": 2}, "end": {"line": 337, "column": 7}}, "loc": {"start": {"line": 337, "column": 38}, "end": {"line": 368, "column": 3}}}, "17": {"name": "(anonymous_21)", "decl": {"start": {"line": 349, "column": 28}, "end": {"line": 349, "column": 29}}, "loc": {"start": {"line": 349, "column": 45}, "end": {"line": 349, "column": 74}}}, "18": {"name": "(anonymous_22)", "decl": {"start": {"line": 359, "column": 6}, "end": {"line": 359, "column": 7}}, "loc": {"start": {"line": 359, "column": 18}, "end": {"line": 359, "column": 50}}}, "19": {"name": "(anonymous_23)", "decl": {"start": {"line": 379, "column": 2}, "end": {"line": 379, "column": 7}}, "loc": {"start": {"line": 379, "column": 66}, "end": {"line": 406, "column": 3}}}, "20": {"name": "(anonymous_24)", "decl": {"start": {"line": 384, "column": 23}, "end": {"line": 384, "column": 24}}, "loc": {"start": {"line": 384, "column": 36}, "end": {"line": 405, "column": 6}}}, "21": {"name": "(anonymous_25)", "decl": {"start": {"line": 412, "column": 2}, "end": {"line": 412, "column": 7}}, "loc": {"start": {"line": 413, "column": 55}, "end": {"line": 468, "column": 3}}}, "22": {"name": "(anonymous_26)", "decl": {"start": {"line": 432, "column": 42}, "end": {"line": 432, "column": 43}}, "loc": {"start": {"line": 432, "column": 49}, "end": {"line": 432, "column": 67}}}, "23": {"name": "(anonymous_27)", "decl": {"start": {"line": 434, "column": 31}, "end": {"line": 434, "column": 32}}, "loc": {"start": {"line": 434, "column": 47}, "end": {"line": 434, "column": 58}}}, "24": {"name": "(anonymous_28)", "decl": {"start": {"line": 441, "column": 20}, "end": {"line": 441, "column": 21}}, "loc": {"start": {"line": 441, "column": 31}, "end": {"line": 445, "column": 5}}}, "25": {"name": "(anonymous_29)", "decl": {"start": {"line": 442, "column": 41}, "end": {"line": 442, "column": 42}}, "loc": {"start": {"line": 442, "column": 50}, "end": {"line": 444, "column": 7}}}, "26": {"name": "(anonymous_30)", "decl": {"start": {"line": 448, "column": 12}, "end": {"line": 448, "column": 13}}, "loc": {"start": {"line": 448, "column": 22}, "end": {"line": 448, "column": 33}}}, "27": {"name": "(anonymous_31)", "decl": {"start": {"line": 450, "column": 11}, "end": {"line": 450, "column": 12}}, "loc": {"start": {"line": 450, "column": 31}, "end": {"line": 450, "column": 46}}}, "28": {"name": "(anonymous_32)", "decl": {"start": {"line": 460, "column": 48}, "end": {"line": 460, "column": 49}}, "loc": {"start": {"line": 460, "column": 56}, "end": {"line": 466, "column": 8}}}, "29": {"name": "(anonymous_33)", "decl": {"start": {"line": 480, "column": 2}, "end": {"line": 480, "column": 7}}, "loc": {"start": {"line": 482, "column": 32}, "end": {"line": 541, "column": 3}}}, "30": {"name": "(anonymous_34)", "decl": {"start": {"line": 490, "column": 41}, "end": {"line": 490, "column": 42}}, "loc": {"start": {"line": 490, "column": 48}, "end": {"line": 490, "column": 73}}}, "31": {"name": "(anonymous_35)", "decl": {"start": {"line": 504, "column": 26}, "end": {"line": 504, "column": 27}}, "loc": {"start": {"line": 504, "column": 37}, "end": {"line": 510, "column": 5}}}, "32": {"name": "(anonymous_36)", "decl": {"start": {"line": 514, "column": 11}, "end": {"line": 514, "column": 12}}, "loc": {"start": {"line": 514, "column": 32}, "end": {"line": 519, "column": 8}}}, "33": {"name": "(anonymous_37)", "decl": {"start": {"line": 517, "column": 24}, "end": {"line": 517, "column": 25}}, "loc": {"start": {"line": 517, "column": 40}, "end": {"line": 517, "column": 51}}}, "34": {"name": "(anonymous_38)", "decl": {"start": {"line": 520, "column": 12}, "end": {"line": 520, "column": 13}}, "loc": {"start": {"line": 520, "column": 22}, "end": {"line": 520, "column": 50}}}, "35": {"name": "(anonymous_39)", "decl": {"start": {"line": 547, "column": 2}, "end": {"line": 547, "column": 7}}, "loc": {"start": {"line": 547, "column": 63}, "end": {"line": 600, "column": 3}}}, "36": {"name": "(anonymous_40)", "decl": {"start": {"line": 582, "column": 6}, "end": {"line": 582, "column": 7}}, "loc": {"start": {"line": 583, "column": 8}, "end": {"line": 584, "column": 45}}}, "37": {"name": "(anonymous_41)", "decl": {"start": {"line": 587, "column": 6}, "end": {"line": 587, "column": 7}}, "loc": {"start": {"line": 588, "column": 8}, "end": {"line": 589, "column": 45}}}, "38": {"name": "(anonymous_42)", "decl": {"start": {"line": 605, "column": 2}, "end": {"line": 605, "column": 7}}, "loc": {"start": {"line": 612, "column": 5}, "end": {"line": 652, "column": 3}}}, "39": {"name": "(anonymous_43)", "decl": {"start": {"line": 650, "column": 41}, "end": {"line": 650, "column": 42}}, "loc": {"start": {"line": 650, "column": 48}, "end": {"line": 650, "column": 70}}}, "40": {"name": "(anonymous_44)", "decl": {"start": {"line": 71, "column": 18}, "end": {"line": 71, "column": 19}}, "loc": {"start": {"line": 71, "column": 36}, "end": {"line": 77, "column": 9}}}, "41": {"name": "(anonymous_45)", "decl": {"start": {"line": 74, "column": 17}, "end": {"line": 74, "column": 20}}, "loc": {"start": {"line": 74, "column": 23}, "end": {"line": 74, "column": 66}}}, "42": {"name": "(anonymous_46)", "decl": {"start": {"line": 79, "column": 18}, "end": {"line": 79, "column": 19}}, "loc": {"start": {"line": 79, "column": 36}, "end": {"line": 90, "column": 7}}}}, "branchMap": {"0": {"loc": {"start": {"line": 107, "column": 4}, "end": {"line": 109, "column": 5}}, "type": "if", "locations": [{"start": {"line": 107, "column": 4}, "end": {"line": 109, "column": 5}}]}, "1": {"loc": {"start": {"line": 138, "column": 6}, "end": {"line": 140, "column": 7}}, "type": "if", "locations": [{"start": {"line": 138, "column": 6}, "end": {"line": 140, "column": 7}}]}, "2": {"loc": {"start": {"line": 235, "column": 18}, "end": {"line": 237, "column": 19}}, "type": "cond-expr", "locations": [{"start": {"line": 236, "column": 10}, "end": {"line": 236, "column": 27}}, {"start": {"line": 237, "column": 10}, "end": {"line": 237, "column": 19}}]}, "3": {"loc": {"start": {"line": 243, "column": 17}, "end": {"line": 243, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 243, "column": 17}, "end": {"line": 243, "column": 33}}, {"start": {"line": 243, "column": 37}, "end": {"line": 243, "column": 46}}]}, "4": {"loc": {"start": {"line": 266, "column": 4}, "end": {"line": 268, "column": 5}}, "type": "if", "locations": [{"start": {"line": 266, "column": 4}, "end": {"line": 268, "column": 5}}]}, "5": {"loc": {"start": {"line": 348, "column": 6}, "end": {"line": 351, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 349, "column": 10}, "end": {"line": 350, "column": 21}}, {"start": {"line": 351, "column": 10}, "end": {"line": 351, "column": 11}}]}, "6": {"loc": {"start": {"line": 380, "column": 24}, "end": {"line": 380, "column": 55}}, "type": "cond-expr", "locations": [{"start": {"line": 380, "column": 32}, "end": {"line": 380, "column": 51}}, {"start": {"line": 380, "column": 54}, "end": {"line": 380, "column": 55}}]}, "7": {"loc": {"start": {"line": 393, "column": 17}, "end": {"line": 398, "column": 14}}, "type": "cond-expr", "locations": [{"start": {"line": 394, "column": 10}, "end": {"line": 397, "column": null}}, {"start": {"line": 398, "column": 10}, "end": {"line": 398, "column": 14}}]}, "8": {"loc": {"start": {"line": 399, "column": 15}, "end": {"line": 404, "column": 14}}, "type": "cond-expr", "locations": [{"start": {"line": 400, "column": 10}, "end": {"line": 403, "column": null}}, {"start": {"line": 404, "column": 10}, "end": {"line": 404, "column": 14}}]}, "9": {"loc": {"start": {"line": 419, "column": 4}, "end": {"line": 430, "column": 5}}, "type": "if", "locations": [{"start": {"line": 419, "column": 4}, "end": {"line": 430, "column": 5}}]}, "10": {"loc": {"start": {"line": 443, "column": 38}, "end": {"line": 443, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 443, "column": 38}, "end": {"line": 443, "column": 66}}, {"start": {"line": 443, "column": 70}, "end": {"line": 443, "column": 71}}]}, "11": {"loc": {"start": {"line": 484, "column": 23}, "end": {"line": 484, "column": 53}}, "type": "cond-expr", "locations": [{"start": {"line": 484, "column": 30}, "end": {"line": 484, "column": 48}}, {"start": {"line": 484, "column": 51}, "end": {"line": 484, "column": 53}}]}, "12": {"loc": {"start": {"line": 492, "column": 4}, "end": {"line": 500, "column": 5}}, "type": "if", "locations": [{"start": {"line": 492, "column": 4}, "end": {"line": 500, "column": 5}}]}, "13": {"loc": {"start": {"line": 506, "column": 6}, "end": {"line": 508, "column": 7}}, "type": "if", "locations": [{"start": {"line": 506, "column": 6}, "end": {"line": 508, "column": 7}}]}, "14": {"loc": {"start": {"line": 525, "column": 4}, "end": {"line": 532, "column": 5}}, "type": "if", "locations": [{"start": {"line": 525, "column": 4}, "end": {"line": 532, "column": 5}}]}, "15": {"loc": {"start": {"line": 530, "column": 6}, "end": {"line": 531, "column": 53}}, "type": "if", "locations": [{"start": {"line": 530, "column": 6}, "end": {"line": 531, "column": 53}}, {"start": {"line": 531, "column": 11}, "end": {"line": 531, "column": 53}}]}, "16": {"loc": {"start": {"line": 531, "column": 11}, "end": {"line": 531, "column": 53}}, "type": "if", "locations": [{"start": {"line": 531, "column": 11}, "end": {"line": 531, "column": 53}}]}, "17": {"loc": {"start": {"line": 557, "column": 20}, "end": {"line": 557, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 557, "column": 20}, "end": {"line": 557, "column": 47}}, {"start": {"line": 557, "column": 51}, "end": {"line": 557, "column": 52}}]}, "18": {"loc": {"start": {"line": 562, "column": 4}, "end": {"line": 565, "column": 5}}, "type": "if", "locations": [{"start": {"line": 562, "column": 4}, "end": {"line": 565, "column": 5}}]}, "19": {"loc": {"start": {"line": 562, "column": 8}, "end": {"line": 562, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 562, "column": 8}, "end": {"line": 562, "column": 22}}, {"start": {"line": 562, "column": 26}, "end": {"line": 562, "column": 59}}]}, "20": {"loc": {"start": {"line": 567, "column": 4}, "end": {"line": 570, "column": 5}}, "type": "if", "locations": [{"start": {"line": 567, "column": 4}, "end": {"line": 570, "column": 5}}]}, "21": {"loc": {"start": {"line": 572, "column": 4}, "end": {"line": 574, "column": 5}}, "type": "if", "locations": [{"start": {"line": 572, "column": 4}, "end": {"line": 574, "column": 5}}]}, "22": {"loc": {"start": {"line": 576, "column": 4}, "end": {"line": 578, "column": 5}}, "type": "if", "locations": [{"start": {"line": 576, "column": 4}, "end": {"line": 578, "column": 5}}]}, "23": {"loc": {"start": {"line": 583, "column": 8}, "end": {"line": 584, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 583, "column": 8}, "end": {"line": 583, "column": 48}}, {"start": {"line": 584, "column": 8}, "end": {"line": 584, "column": 45}}]}, "24": {"loc": {"start": {"line": 588, "column": 8}, "end": {"line": 589, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 588, "column": 8}, "end": {"line": 588, "column": 46}}, {"start": {"line": 589, "column": 8}, "end": {"line": 589, "column": 45}}]}, "25": {"loc": {"start": {"line": 592, "column": 4}, "end": {"line": 594, "column": 5}}, "type": "if", "locations": [{"start": {"line": 592, "column": 4}, "end": {"line": 594, "column": 5}}]}, "26": {"loc": {"start": {"line": 595, "column": 4}, "end": {"line": 597, "column": 5}}, "type": "if", "locations": [{"start": {"line": 595, "column": 4}, "end": {"line": 597, "column": 5}}]}, "27": {"loc": {"start": {"line": 80, "column": 8}, "end": {"line": 89, "column": 9}}, "type": "if", "locations": [{"start": {"line": 80, "column": 8}, "end": {"line": 89, "column": 9}}, {"start": {"line": 87, "column": 15}, "end": {"line": 89, "column": 9}}]}, "28": {"loc": {"start": {"line": 81, "column": 10}, "end": {"line": 84, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 81, "column": 10}, "end": {"line": 81, "column": 45}}, {"start": {"line": 82, "column": 10}, "end": {"line": 82, "column": 38}}, {"start": {"line": 83, "column": 10}, "end": {"line": 83, "column": 45}}, {"start": {"line": 84, "column": 10}, "end": {"line": 84, "column": 44}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0, "145": 0, "146": 0, "147": 0, "148": 0, "149": 0, "150": 0, "151": 0, "152": 0, "153": 0, "154": 0, "155": 0, "156": 0, "157": 0, "158": 0, "159": 0, "160": 0, "161": 0, "162": 0, "163": 0, "164": 0, "165": 0, "166": 0, "167": 0, "168": 0, "169": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0], "10": [0, 0], "11": [0, 0], "12": [0], "13": [0], "14": [0], "15": [0, 0], "16": [0], "17": [0, 0], "18": [0], "19": [0, 0], "20": [0], "21": [0], "22": [0], "23": [0, 0], "24": [0, 0], "25": [0], "26": [0], "27": [0, 0], "28": [0, 0, 0, 0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/conformance.module.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/conformance.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 48}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 65}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 59}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 56}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 78}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 70}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 74}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 58}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 56}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 53}}, "11": {"start": {"line": 31, "column": 7}, "end": {"line": 31, "column": null}}, "12": {"start": {"line": 31, "column": 13}, "end": {"line": 31, "column": 30}}, "13": {"start": {"line": 31, "column": 13}, "end": {"line": 31, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "f": {}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/conformance.service.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/conformance.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 51}}, "2": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 37}}, "3": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": null}}, "4": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 58}}, "5": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 56}}, "6": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 53}}, "7": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 78}}, "8": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 70}}, "9": {"start": {"line": 22, "column": 31}, "end": {"line": 325, "column": null}}, "10": {"start": {"line": 27, "column": 12}, "end": {"line": 27, "column": 41}}, "11": {"start": {"line": 29, "column": 12}, "end": {"line": 29, "column": 33}}, "12": {"start": {"line": 31, "column": 12}, "end": {"line": 31, "column": 32}}, "13": {"start": {"line": 33, "column": 12}, "end": {"line": 33, "column": 31}}, "14": {"start": {"line": 34, "column": 12}, "end": {"line": 34, "column": 41}}, "15": {"start": {"line": 35, "column": 12}, "end": {"line": 35, "column": 37}}, "16": {"start": {"line": 23, "column": 19}, "end": {"line": 23, "column": 64}}, "17": {"start": {"line": 44, "column": 4}, "end": {"line": 46, "column": 6}}, "18": {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 62}}, "19": {"start": {"line": 52, "column": 4}, "end": {"line": 61, "column": 5}}, "20": {"start": {"line": 53, "column": 27}, "end": {"line": 55, "column": null}}, "21": {"start": {"line": 57, "column": 6}, "end": {"line": 60, "column": 7}}, "22": {"start": {"line": 58, "column": 8}, "end": {"line": 58, "column": 60}}, "23": {"start": {"line": 59, "column": 8}, "end": {"line": 59, "column": 28}}, "24": {"start": {"line": 64, "column": 30}, "end": {"line": 80, "column": 6}}, "25": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 68}}, "26": {"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": 59}}, "27": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 23}}, "28": {"start": {"line": 98, "column": 4}, "end": {"line": 161, "column": 5}}, "29": {"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": 61}}, "30": {"start": {"line": 102, "column": 24}, "end": {"line": 104, "column": null}}, "31": {"start": {"line": 108, "column": 24}, "end": {"line": 110, "column": 8}}, "32": {"start": {"line": 112, "column": 6}, "end": {"line": 114, "column": 7}}, "33": {"start": {"line": 113, "column": 8}, "end": {"line": 113, "column": 49}}, "34": {"start": {"line": 118, "column": 8}, "end": {"line": 121, "column": null}}, "35": {"start": {"line": 125, "column": 22}, "end": {"line": 125, "column": 71}}, "36": {"start": {"line": 128, "column": 6}, "end": {"line": 138, "column": 9}}, "37": {"start": {"line": 141, "column": 28}, "end": {"line": 143, "column": 8}}, "38": {"start": {"line": 145, "column": 6}, "end": {"line": 147, "column": 7}}, "39": {"start": {"line": 146, "column": 8}, "end": {"line": 146, "column": 70}}, "40": {"start": {"line": 149, "column": 6}, "end": {"line": 151, "column": 8}}, "41": {"start": {"line": 153, "column": 6}, "end": {"line": 155, "column": 8}}, "42": {"start": {"line": 157, "column": 6}, "end": {"line": 160, "column": 9}}, "43": {"start": {"line": 171, "column": 4}, "end": {"line": 174, "column": 7}}, "44": {"start": {"line": 181, "column": 4}, "end": {"line": 185, "column": 7}}, "45": {"start": {"line": 192, "column": 4}, "end": {"line": 195, "column": 7}}, "46": {"start": {"line": 204, "column": 4}, "end": {"line": 208, "column": 7}}, "47": {"start": {"line": 215, "column": 19}, "end": {"line": 217, "column": 6}}, "48": {"start": {"line": 218, "column": 4}, "end": {"line": 220, "column": 5}}, "49": {"start": {"line": 219, "column": 6}, "end": {"line": 219, "column": 48}}, "50": {"start": {"line": 222, "column": 4}, "end": {"line": 225, "column": 6}}, "51": {"start": {"line": 226, "column": 4}, "end": {"line": 226, "column": 54}}, "52": {"start": {"line": 236, "column": 20}, "end": {"line": 238, "column": 6}}, "53": {"start": {"line": 239, "column": 4}, "end": {"line": 241, "column": 5}}, "54": {"start": {"line": 240, "column": 6}, "end": {"line": 240, "column": 43}}, "55": {"start": {"line": 243, "column": 22}, "end": {"line": 245, "column": 6}}, "56": {"start": {"line": 246, "column": 4}, "end": {"line": 248, "column": 5}}, "57": {"start": {"line": 247, "column": 6}, "end": {"line": 247, "column": 47}}, "58": {"start": {"line": 250, "column": 4}, "end": {"line": 252, "column": 5}}, "59": {"start": {"line": 251, "column": 6}, "end": {"line": 251, "column": 54}}, "60": {"start": {"line": 262, "column": 25}, "end": {"line": 265, "column": 53}}, "61": {"start": {"line": 268, "column": 4}, "end": {"line": 292, "column": 5}}, "62": {"start": {"line": 269, "column": 6}, "end": {"line": 273, "column": 7}}, "63": {"start": {"line": 270, "column": 8}, "end": {"line": 272, "column": 11}}, "64": {"start": {"line": 274, "column": 6}, "end": {"line": 278, "column": 7}}, "65": {"start": {"line": 275, "column": 8}, "end": {"line": 277, "column": 11}}, "66": {"start": {"line": 279, "column": 6}, "end": {"line": 283, "column": 7}}, "67": {"start": {"line": 280, "column": 8}, "end": {"line": 282, "column": 11}}, "68": {"start": {"line": 284, "column": 6}, "end": {"line": 291, "column": 7}}, "69": {"start": {"line": 285, "column": 8}, "end": {"line": 290, "column": 10}}, "70": {"start": {"line": 294, "column": 4}, "end": {"line": 294, "column": 34}}, "71": {"start": {"line": 301, "column": 23}, "end": {"line": 301, "column": 57}}, "72": {"start": {"line": 302, "column": 28}, "end": {"line": 304, "column": 12}}, "73": {"start": {"line": 303, "column": 18}, "end": {"line": 303, "column": 32}}, "74": {"start": {"line": 305, "column": 27}, "end": {"line": 305, "column": 55}}, "75": {"start": {"line": 307, "column": 29}, "end": {"line": 307, "column": 78}}, "76": {"start": {"line": 308, "column": 25}, "end": {"line": 308, "column": 73}}, "77": {"start": {"line": 309, "column": 27}, "end": {"line": 309, "column": 64}}, "78": {"start": {"line": 310, "column": 32}, "end": {"line": 310, "column": 74}}, "79": {"start": {"line": 311, "column": 28}, "end": {"line": 311, "column": 66}}, "80": {"start": {"line": 313, "column": 4}, "end": {"line": 323, "column": 6}}, "81": {"start": {"line": 22, "column": 13}, "end": {"line": 22, "column": 31}}, "82": {"start": {"line": 22, "column": 13}, "end": {"line": 325, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": null}}, "loc": {"start": {"line": 35, "column": 60}, "end": {"line": 36, "column": 6}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": 7}}, "loc": {"start": {"line": 42, "column": 28}, "end": {"line": 89, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 94, "column": 10}, "end": {"line": 94, "column": 15}}, "loc": {"start": {"line": 96, "column": 28}, "end": {"line": 162, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 167, "column": 2}, "end": {"line": 167, "column": 7}}, "loc": {"start": {"line": 169, "column": 23}, "end": {"line": 175, "column": 3}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 180, "column": 2}, "end": {"line": 180, "column": 7}}, "loc": {"start": {"line": 180, "column": 47}, "end": {"line": 186, "column": 3}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 191, "column": 2}, "end": {"line": 191, "column": 7}}, "loc": {"start": {"line": 191, "column": 32}, "end": {"line": 196, "column": 3}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 201, "column": 2}, "end": {"line": 201, "column": 7}}, "loc": {"start": {"line": 202, "column": 21}, "end": {"line": 209, "column": 3}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 214, "column": 2}, "end": {"line": 214, "column": 7}}, "loc": {"start": {"line": 214, "column": 42}, "end": {"line": 227, "column": 3}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 232, "column": 10}, "end": {"line": 232, "column": 15}}, "loc": {"start": {"line": 234, "column": 23}, "end": {"line": 253, "column": 3}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 258, "column": 10}, "end": {"line": 258, "column": 15}}, "loc": {"start": {"line": 260, "column": 20}, "end": {"line": 295, "column": 3}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 300, "column": 10}, "end": {"line": 300, "column": 37}}, "loc": {"start": {"line": 300, "column": 58}, "end": {"line": 324, "column": 3}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 303, "column": 6}, "end": {"line": 303, "column": 7}}, "loc": {"start": {"line": 303, "column": 18}, "end": {"line": 303, "column": 32}}}}, "branchMap": {"0": {"loc": {"start": {"line": 52, "column": 4}, "end": {"line": 61, "column": 5}}, "type": "if", "locations": [{"start": {"line": 52, "column": 4}, "end": {"line": 61, "column": 5}}]}, "1": {"loc": {"start": {"line": 57, "column": 6}, "end": {"line": 60, "column": 7}}, "type": "if", "locations": [{"start": {"line": 57, "column": 6}, "end": {"line": 60, "column": 7}}]}, "2": {"loc": {"start": {"line": 112, "column": 6}, "end": {"line": 114, "column": 7}}, "type": "if", "locations": [{"start": {"line": 112, "column": 6}, "end": {"line": 114, "column": 7}}]}, "3": {"loc": {"start": {"line": 132, "column": 22}, "end": {"line": 134, "column": 21}}, "type": "cond-expr", "locations": [{"start": {"line": 133, "column": 12}, "end": {"line": 133, "column": 40}}, {"start": {"line": 134, "column": 12}, "end": {"line": 134, "column": 21}}]}, "4": {"loc": {"start": {"line": 135, "column": 26}, "end": {"line": 137, "column": 21}}, "type": "cond-expr", "locations": [{"start": {"line": 136, "column": 12}, "end": {"line": 136, "column": 44}}, {"start": {"line": 137, "column": 12}, "end": {"line": 137, "column": 21}}]}, "5": {"loc": {"start": {"line": 145, "column": 6}, "end": {"line": 147, "column": 7}}, "type": "if", "locations": [{"start": {"line": 145, "column": 6}, "end": {"line": 147, "column": 7}}]}, "6": {"loc": {"start": {"line": 202, "column": 4}, "end": {"line": 202, "column": 21}}, "type": "default-arg", "locations": [{"start": {"line": 202, "column": 20}, "end": {"line": 202, "column": 21}}]}, "7": {"loc": {"start": {"line": 218, "column": 4}, "end": {"line": 220, "column": 5}}, "type": "if", "locations": [{"start": {"line": 218, "column": 4}, "end": {"line": 220, "column": 5}}]}, "8": {"loc": {"start": {"line": 239, "column": 4}, "end": {"line": 241, "column": 5}}, "type": "if", "locations": [{"start": {"line": 239, "column": 4}, "end": {"line": 241, "column": 5}}]}, "9": {"loc": {"start": {"line": 246, "column": 4}, "end": {"line": 248, "column": 5}}, "type": "if", "locations": [{"start": {"line": 246, "column": 4}, "end": {"line": 248, "column": 5}}]}, "10": {"loc": {"start": {"line": 250, "column": 4}, "end": {"line": 252, "column": 5}}, "type": "if", "locations": [{"start": {"line": 250, "column": 4}, "end": {"line": 252, "column": 5}}]}, "11": {"loc": {"start": {"line": 268, "column": 4}, "end": {"line": 292, "column": 5}}, "type": "if", "locations": [{"start": {"line": 268, "column": 4}, "end": {"line": 292, "column": 5}}]}, "12": {"loc": {"start": {"line": 269, "column": 6}, "end": {"line": 273, "column": 7}}, "type": "if", "locations": [{"start": {"line": 269, "column": 6}, "end": {"line": 273, "column": 7}}]}, "13": {"loc": {"start": {"line": 274, "column": 6}, "end": {"line": 278, "column": 7}}, "type": "if", "locations": [{"start": {"line": 274, "column": 6}, "end": {"line": 278, "column": 7}}]}, "14": {"loc": {"start": {"line": 279, "column": 6}, "end": {"line": 283, "column": 7}}, "type": "if", "locations": [{"start": {"line": 279, "column": 6}, "end": {"line": 283, "column": 7}}]}, "15": {"loc": {"start": {"line": 284, "column": 6}, "end": {"line": 291, "column": 7}}, "type": "if", "locations": [{"start": {"line": 284, "column": 6}, "end": {"line": 291, "column": 7}}]}, "16": {"loc": {"start": {"line": 307, "column": 29}, "end": {"line": 307, "column": 78}}, "type": "cond-expr", "locations": [{"start": {"line": 307, "column": 46}, "end": {"line": 307, "column": 74}}, {"start": {"line": 307, "column": 77}, "end": {"line": 307, "column": 78}}]}, "17": {"loc": {"start": {"line": 308, "column": 25}, "end": {"line": 308, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 308, "column": 25}, "end": {"line": 308, "column": 53}}, {"start": {"line": 308, "column": 57}, "end": {"line": 308, "column": 73}}]}, "18": {"loc": {"start": {"line": 309, "column": 27}, "end": {"line": 309, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 309, "column": 27}, "end": {"line": 309, "column": 57}}, {"start": {"line": 309, "column": 61}, "end": {"line": 309, "column": 64}}]}, "19": {"loc": {"start": {"line": 310, "column": 32}, "end": {"line": 310, "column": 74}}, "type": "binary-expr", "locations": [{"start": {"line": 310, "column": 32}, "end": {"line": 310, "column": 67}}, {"start": {"line": 310, "column": 71}, "end": {"line": 310, "column": 74}}]}, "20": {"loc": {"start": {"line": 311, "column": 28}, "end": {"line": 311, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 311, "column": 28}, "end": {"line": 311, "column": 59}}, {"start": {"line": 311, "column": 63}, "end": {"line": 311, "column": 66}}]}, "21": {"loc": {"start": {"line": 322, "column": 18}, "end": {"line": 322, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 322, "column": 18}, "end": {"line": 322, "column": 44}}, {"start": {"line": 322, "column": 48}, "end": {"line": 322, "column": 50}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 10, "11": 10, "12": 10, "13": 10, "14": 10, "15": 10, "16": 10, "17": 4, "18": 4, "19": 2, "20": 2, "21": 2, "22": 1, "23": 1, "24": 1, "25": 1, "26": 1, "27": 1, "28": 1, "29": 1, "30": 1, "31": 1, "32": 1, "33": 0, "34": 1, "35": 1, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 1, "42": 1, "43": 2, "44": 1, "45": 0, "46": 0, "47": 2, "48": 2, "49": 1, "50": 1, "51": 1, "52": 4, "53": 4, "54": 1, "55": 3, "56": 3, "57": 1, "58": 2, "59": 0, "60": 1, "61": 1, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 1, "71": 1, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 1, "82": 1}, "f": {"0": 10, "1": 4, "2": 1, "3": 2, "4": 1, "5": 0, "6": 0, "7": 2, "8": 4, "9": 1, "10": 1, "11": 0}, "b": {"0": [2], "1": [1], "2": [0], "3": [0, 0], "4": [0, 0], "5": [0], "6": [0], "7": [1], "8": [1], "9": [1], "10": [0], "11": [0], "12": [0], "13": [0], "14": [0], "15": [0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/dto/bpmn-model.dto.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/dto/bpmn-model.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 80}}, "2": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": null}}, "3": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 13}}, "4": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": null}}, "5": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": null}}, "6": {"start": {"line": 32, "column": 2}, "end": {"line": 32, "column": null}}, "7": {"start": {"line": 36, "column": 2}, "end": {"line": 36, "column": null}}, "8": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": null}}, "9": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": null}}, "10": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": null}}, "11": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": null}}, "12": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 13}}, "13": {"start": {"line": 67, "column": 2}, "end": {"line": 67, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "f": {}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/dto/conformance-check.dto.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/dto/conformance-check.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 67}}, "2": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": null}}, "3": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 13}}, "4": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": null}}, "5": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": null}}, "6": {"start": {"line": 27, "column": 2}, "end": {"line": 56, "column": null}}, "7": {"start": {"line": 61, "column": 2}, "end": {"line": 61, "column": null}}, "8": {"start": {"line": 66, "column": 2}, "end": {"line": 66, "column": null}}, "9": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 13}}, "10": {"start": {"line": 71, "column": 2}, "end": {"line": 71, "column": null}}, "11": {"start": {"line": 74, "column": 2}, "end": {"line": 74, "column": null}}, "12": {"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": null}}, "13": {"start": {"line": 80, "column": 2}, "end": {"line": 80, "column": null}}, "14": {"start": {"line": 83, "column": 2}, "end": {"line": 83, "column": null}}, "15": {"start": {"line": 86, "column": 2}, "end": {"line": 86, "column": null}}, "16": {"start": {"line": 89, "column": 2}, "end": {"line": 89, "column": null}}, "17": {"start": {"line": 92, "column": 2}, "end": {"line": 92, "column": null}}, "18": {"start": {"line": 95, "column": 2}, "end": {"line": 95, "column": null}}, "19": {"start": {"line": 98, "column": 2}, "end": {"line": 98, "column": null}}, "20": {"start": {"line": 101, "column": 2}, "end": {"line": 101, "column": null}}, "21": {"start": {"line": 104, "column": 2}, "end": {"line": 104, "column": null}}, "22": {"start": {"line": 107, "column": 2}, "end": {"line": 115, "column": null}}, "23": {"start": {"line": 118, "column": 2}, "end": {"line": 125, "column": null}}, "24": {"start": {"line": 128, "column": 2}, "end": {"line": 133, "column": null}}, "25": {"start": {"line": 136, "column": 2}, "end": {"line": 136, "column": null}}, "26": {"start": {"line": 139, "column": 2}, "end": {"line": 139, "column": null}}, "27": {"start": {"line": 142, "column": 2}, "end": {"line": 142, "column": null}}, "28": {"start": {"line": 145, "column": 2}, "end": {"line": 145, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0}, "f": {}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/dto/conformance-result.dto.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/dto/conformance-result.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 67}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": null}}, "2": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 13}}, "3": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": null}}, "4": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": null}}, "5": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": null}}, "6": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": null}}, "7": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": null}}, "8": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": null}}, "9": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": null}}, "10": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 13}}, "11": {"start": {"line": 32, "column": 2}, "end": {"line": 32, "column": null}}, "12": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": null}}, "13": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": null}}, "14": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": null}}, "15": {"start": {"line": 44, "column": 2}, "end": {"line": 44, "column": null}}, "16": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": null}}, "17": {"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": null}}, "18": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": null}}, "19": {"start": {"line": 56, "column": 2}, "end": {"line": 56, "column": null}}, "20": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": null}}, "21": {"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": null}}, "22": {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": null}}, "23": {"start": {"line": 68, "column": 2}, "end": {"line": 68, "column": null}}, "24": {"start": {"line": 71, "column": 2}, "end": {"line": 71, "column": null}}, "25": {"start": {"line": 74, "column": 2}, "end": {"line": 74, "column": null}}, "26": {"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": null}}, "27": {"start": {"line": 80, "column": 2}, "end": {"line": 80, "column": null}}, "28": {"start": {"line": 83, "column": 2}, "end": {"line": 83, "column": null}}, "29": {"start": {"line": 86, "column": 2}, "end": {"line": 86, "column": null}}, "30": {"start": {"line": 89, "column": 2}, "end": {"line": 89, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0}, "f": {}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/dto/index.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/dto/index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 9}}, "1": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 29}}, "2": {"start": {"line": 1, "column": 29}, "end": {"line": 1, "column": 74}}, "3": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": null}}, "4": {"start": {"line": 3, "column": 2}, "end": {"line": 3, "column": null}}, "5": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": null}}, "6": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": null}}, "7": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": null}}, "8": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 27}}, "loc": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 29}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 1, "column": 29}, "end": {"line": 1, "column": 47}}, "loc": {"start": {"line": 1, "column": 29}, "end": {"line": 1, "column": 74}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 3, "column": 2}, "end": {"line": 3, "column": 21}}, "loc": {"start": {"line": 3, "column": 2}, "end": {"line": 3, "column": null}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": 29}}, "loc": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": null}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": 22}}, "loc": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": null}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": 25}}, "loc": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": null}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/database/migrations/1750602780460-InitialMigrationMySQL.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/database/migrations/1750602780460-InitialMigrationMySQL.ts", "statementMap": {"0": {"start": {"line": 6, "column": 4}, "end": {"line": 21, "column": 11}}, "1": {"start": {"line": 24, "column": 4}, "end": {"line": 37, "column": 11}}, "2": {"start": {"line": 40, "column": 4}, "end": {"line": 53, "column": 11}}, "3": {"start": {"line": 56, "column": 4}, "end": {"line": 67, "column": 11}}, "4": {"start": {"line": 70, "column": 4}, "end": {"line": 73, "column": 11}}, "5": {"start": {"line": 75, "column": 4}, "end": {"line": 78, "column": 11}}, "6": {"start": {"line": 80, "column": 4}, "end": {"line": 83, "column": 11}}, "7": {"start": {"line": 86, "column": 4}, "end": {"line": 88, "column": 6}}, "8": {"start": {"line": 89, "column": 4}, "end": {"line": 91, "column": 6}}, "9": {"start": {"line": 92, "column": 4}, "end": {"line": 94, "column": 6}}, "10": {"start": {"line": 99, "column": 4}, "end": {"line": 101, "column": 6}}, "11": {"start": {"line": 102, "column": 4}, "end": {"line": 104, "column": 6}}, "12": {"start": {"line": 105, "column": 4}, "end": {"line": 107, "column": 6}}, "13": {"start": {"line": 110, "column": 4}, "end": {"line": 112, "column": 6}}, "14": {"start": {"line": 113, "column": 4}, "end": {"line": 115, "column": 6}}, "15": {"start": {"line": 116, "column": 4}, "end": {"line": 118, "column": 6}}, "16": {"start": {"line": 121, "column": 4}, "end": {"line": 121, "column": 63}}, "17": {"start": {"line": 122, "column": 4}, "end": {"line": 122, "column": 57}}, "18": {"start": {"line": 123, "column": 4}, "end": {"line": 123, "column": 56}}, "19": {"start": {"line": 124, "column": 4}, "end": {"line": 124, "column": 52}}, "20": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 13}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 9}, "end": {"line": 4, "column": 14}}, "loc": {"start": {"line": 4, "column": 42}, "end": {"line": 95, "column": 3}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 97, "column": 9}, "end": {"line": 97, "column": 14}}, "loc": {"start": {"line": 97, "column": 44}, "end": {"line": 125, "column": 3}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/database/migrations/1750602780461-AddTestUser.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/database/migrations/1750602780461-AddTestUser.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 35}}, "1": {"start": {"line": 7, "column": 23}, "end": {"line": 7, "column": 25}}, "2": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 68}}, "3": {"start": {"line": 11, "column": 4}, "end": {"line": 29, "column": 7}}, "4": {"start": {"line": 34, "column": 4}, "end": {"line": 36, "column": 7}}, "5": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 13}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 9}, "end": {"line": 5, "column": 14}}, "loc": {"start": {"line": 5, "column": 42}, "end": {"line": 30, "column": 3}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 32, "column": 9}, "end": {"line": 32, "column": 14}}, "loc": {"start": {"line": 32, "column": 44}, "end": {"line": 37, "column": 3}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/database/migrations/1750669022006-AddAnalysisResultCacheFields.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/database/migrations/1750669022006-AddAnalysisResultCacheFields.ts", "statementMap": {"0": {"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": 53}}, "1": {"start": {"line": 10, "column": 4}, "end": {"line": 14, "column": 11}}, "2": {"start": {"line": 17, "column": 4}, "end": {"line": 20, "column": 11}}, "3": {"start": {"line": 23, "column": 4}, "end": {"line": 26, "column": 11}}, "4": {"start": {"line": 29, "column": 4}, "end": {"line": 32, "column": 11}}, "5": {"start": {"line": 35, "column": 4}, "end": {"line": 38, "column": 11}}, "6": {"start": {"line": 41, "column": 4}, "end": {"line": 44, "column": 11}}, "7": {"start": {"line": 47, "column": 4}, "end": {"line": 50, "column": 11}}, "8": {"start": {"line": 53, "column": 4}, "end": {"line": 56, "column": 11}}, "9": {"start": {"line": 59, "column": 4}, "end": {"line": 62, "column": 11}}, "10": {"start": {"line": 64, "column": 4}, "end": {"line": 67, "column": 11}}, "11": {"start": {"line": 70, "column": 4}, "end": {"line": 74, "column": 11}}, "12": {"start": {"line": 79, "column": 4}, "end": {"line": 81, "column": 6}}, "13": {"start": {"line": 82, "column": 4}, "end": {"line": 84, "column": 6}}, "14": {"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 6}}, "15": {"start": {"line": 90, "column": 4}, "end": {"line": 92, "column": 6}}, "16": {"start": {"line": 93, "column": 4}, "end": {"line": 95, "column": 6}}, "17": {"start": {"line": 96, "column": 4}, "end": {"line": 98, "column": 6}}, "18": {"start": {"line": 99, "column": 4}, "end": {"line": 101, "column": 6}}, "19": {"start": {"line": 102, "column": 4}, "end": {"line": 104, "column": 6}}, "20": {"start": {"line": 105, "column": 4}, "end": {"line": 107, "column": 6}}, "21": {"start": {"line": 108, "column": 4}, "end": {"line": 110, "column": 6}}, "22": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 13}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 13}}, "loc": {"start": {"line": 3, "column": 0}, "end": {"line": 112, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 9}, "end": {"line": 8, "column": 14}}, "loc": {"start": {"line": 8, "column": 42}, "end": {"line": 75, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 77, "column": 9}, "end": {"line": 77, "column": 14}}, "loc": {"start": {"line": 77, "column": 44}, "end": {"line": 111, "column": 3}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/database/migrations/1750670463601-FixDataSourceHashType.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/database/migrations/1750670463601-FixDataSourceHashType.ts", "statementMap": {"0": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": 46}}, "1": {"start": {"line": 8, "column": 4}, "end": {"line": 11, "column": 11}}, "2": {"start": {"line": 16, "column": 4}, "end": {"line": 19, "column": 11}}, "3": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 13}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 13}}, "loc": {"start": {"line": 3, "column": 0}, "end": {"line": 21, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 6, "column": 9}, "end": {"line": 6, "column": 14}}, "loc": {"start": {"line": 6, "column": 42}, "end": {"line": 12, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 14, "column": 9}, "end": {"line": 14, "column": 14}}, "loc": {"start": {"line": 14, "column": 44}, "end": {"line": 20, "column": 3}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/database/migrations/1750700000000-AddConformanceCheckTables.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/database/migrations/1750700000000-AddConformanceCheckTables.ts", "statementMap": {"0": {"start": {"line": 8, "column": 4}, "end": {"line": 26, "column": 7}}, "1": {"start": {"line": 29, "column": 4}, "end": {"line": 57, "column": 7}}, "2": {"start": {"line": 60, "column": 4}, "end": {"line": 63, "column": 7}}, "3": {"start": {"line": 65, "column": 4}, "end": {"line": 68, "column": 7}}, "4": {"start": {"line": 70, "column": 4}, "end": {"line": 73, "column": 7}}, "5": {"start": {"line": 76, "column": 4}, "end": {"line": 78, "column": 6}}, "6": {"start": {"line": 79, "column": 4}, "end": {"line": 81, "column": 6}}, "7": {"start": {"line": 82, "column": 4}, "end": {"line": 84, "column": 6}}, "8": {"start": {"line": 85, "column": 4}, "end": {"line": 87, "column": 6}}, "9": {"start": {"line": 88, "column": 4}, "end": {"line": 90, "column": 6}}, "10": {"start": {"line": 91, "column": 4}, "end": {"line": 93, "column": 6}}, "11": {"start": {"line": 98, "column": 4}, "end": {"line": 100, "column": 6}}, "12": {"start": {"line": 101, "column": 4}, "end": {"line": 103, "column": 6}}, "13": {"start": {"line": 104, "column": 4}, "end": {"line": 106, "column": 6}}, "14": {"start": {"line": 107, "column": 4}, "end": {"line": 109, "column": 6}}, "15": {"start": {"line": 110, "column": 4}, "end": {"line": 112, "column": 6}}, "16": {"start": {"line": 113, "column": 4}, "end": {"line": 115, "column": 6}}, "17": {"start": {"line": 118, "column": 4}, "end": {"line": 120, "column": 6}}, "18": {"start": {"line": 121, "column": 4}, "end": {"line": 123, "column": 6}}, "19": {"start": {"line": 124, "column": 4}, "end": {"line": 126, "column": 6}}, "20": {"start": {"line": 129, "column": 4}, "end": {"line": 129, "column": 66}}, "21": {"start": {"line": 130, "column": 4}, "end": {"line": 130, "column": 58}}, "22": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 13}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 6, "column": 9}, "end": {"line": 6, "column": 14}}, "loc": {"start": {"line": 6, "column": 42}, "end": {"line": 94, "column": 3}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 96, "column": 9}, "end": {"line": 96, "column": 14}}, "loc": {"start": {"line": 96, "column": 44}, "end": {"line": 131, "column": 3}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/analysis-result.entity.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/analysis-result.entity.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 43}}, "2": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": null}}, "3": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": null}}, "4": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": null}}, "5": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": null}}, "6": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": null}}, "7": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": null}}, "8": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": null}}, "9": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": null}}, "10": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": null}}, "11": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": null}}, "12": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": null}}, "13": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": null}}, "14": {"start": {"line": 33, "column": 7}, "end": {"line": 113, "column": null}}, "15": {"start": {"line": 94, "column": 4}, "end": {"line": 94, "column": 38}}, "16": {"start": {"line": 94, "column": 25}, "end": {"line": 94, "column": 38}}, "17": {"start": {"line": 95, "column": 4}, "end": {"line": 95, "column": 39}}, "18": {"start": {"line": 100, "column": 4}, "end": {"line": 100, "column": 67}}, "19": {"start": {"line": 105, "column": 4}, "end": {"line": 105, "column": 87}}, "20": {"start": {"line": 110, "column": 16}, "end": {"line": 110, "column": 43}}, "21": {"start": {"line": 111, "column": 4}, "end": {"line": 111, "column": 55}}, "22": {"start": {"line": 33, "column": 13}, "end": {"line": 33, "column": 27}}, "23": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": null}}, "24": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": null}}, "25": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": null}}, "26": {"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": null}}, "27": {"start": {"line": 54, "column": 2}, "end": {"line": 54, "column": null}}, "28": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": null}}, "29": {"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": null}}, "30": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": null}}, "31": {"start": {"line": 66, "column": 2}, "end": {"line": 66, "column": null}}, "32": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": null}}, "33": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": null}}, "34": {"start": {"line": 75, "column": 2}, "end": {"line": 75, "column": null}}, "35": {"start": {"line": 78, "column": 2}, "end": {"line": 78, "column": null}}, "36": {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": null}}, "37": {"start": {"line": 87, "column": 2}, "end": {"line": 87, "column": null}}, "38": {"start": {"line": 83, "column": 19}, "end": {"line": 83, "column": 26}}, "39": {"start": {"line": 83, "column": 41}, "end": {"line": 83, "column": 64}}, "40": {"start": {"line": 90, "column": 2}, "end": {"line": 90, "column": null}}, "41": {"start": {"line": 33, "column": 13}, "end": {"line": 113, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 12}}, "loc": {"start": {"line": 13, "column": 24}, "end": {"line": 19, "column": 1}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 12}}, "loc": {"start": {"line": 21, "column": 26}, "end": {"line": 27, "column": 1}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 93, "column": 2}, "end": {"line": 93, "column": 6}}, "loc": {"start": {"line": 93, "column": 15}, "end": {"line": 96, "column": 3}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 99, "column": 2}, "end": {"line": 99, "column": 6}}, "loc": {"start": {"line": 99, "column": 18}, "end": {"line": 101, "column": 3}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 104, "column": 2}, "end": {"line": 104, "column": 18}}, "loc": {"start": {"line": 104, "column": 18}, "end": {"line": 106, "column": 3}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 109, "column": 2}, "end": {"line": 109, "column": 14}}, "loc": {"start": {"line": 109, "column": 34}, "end": {"line": 112, "column": 3}}}, "6": {"name": "(anonymous_8)", "decl": {"start": {"line": 83, "column": 13}, "end": {"line": 83, "column": 16}}, "loc": {"start": {"line": 83, "column": 19}, "end": {"line": 83, "column": 26}}}, "7": {"name": "(anonymous_9)", "decl": {"start": {"line": 83, "column": 28}, "end": {"line": 83, "column": 29}}, "loc": {"start": {"line": 83, "column": 41}, "end": {"line": 83, "column": 64}}}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 12}, "end": {"line": 13, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 13, "column": 12}, "end": {"line": 13, "column": 24}}, {"start": {"line": 13, "column": 24}, "end": {"line": 13, "column": null}}]}, "1": {"loc": {"start": {"line": 21, "column": 12}, "end": {"line": 21, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 21, "column": 12}, "end": {"line": 21, "column": 26}}, {"start": {"line": 21, "column": 26}, "end": {"line": 21, "column": null}}]}, "2": {"loc": {"start": {"line": 94, "column": 4}, "end": {"line": 94, "column": 38}}, "type": "if", "locations": [{"start": {"line": 94, "column": 4}, "end": {"line": 94, "column": 38}}]}, "3": {"loc": {"start": {"line": 100, "column": 11}, "end": {"line": 100, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 100, "column": 11}, "end": {"line": 100, "column": 25}}, {"start": {"line": 100, "column": 29}, "end": {"line": 100, "column": 66}}]}, "4": {"loc": {"start": {"line": 110, "column": 16}, "end": {"line": 110, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 110, "column": 16}, "end": {"line": 110, "column": 26}}, {"start": {"line": 110, "column": 30}, "end": {"line": 110, "column": 43}}]}}, "s": {"0": 9, "1": 9, "2": 9, "3": 9, "4": 9, "5": 9, "6": 9, "7": 9, "8": 9, "9": 9, "10": 9, "11": 9, "12": 9, "13": 9, "14": 9, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 9, "23": 9, "24": 9, "25": 9, "26": 9, "27": 9, "28": 9, "29": 9, "30": 9, "31": 9, "32": 9, "33": 9, "34": 9, "35": 9, "36": 9, "37": 9, "38": 0, "39": 0, "40": 9, "41": 9}, "f": {"0": 9, "1": 9, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "b": {"0": [9, 9], "1": [9, 9], "2": [0], "3": [0, 0], "4": [0, 0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/bpmn-model.entity.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/bpmn-model.entity.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 43}}, "2": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 64}}, "3": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": null}}, "4": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": null}}, "5": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": null}}, "6": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": null}}, "7": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": null}}, "8": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": null}}, "9": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": null}}, "10": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": null}}, "11": {"start": {"line": 30, "column": 7}, "end": {"line": 126, "column": null}}, "12": {"start": {"line": 95, "column": 4}, "end": {"line": 97, "column": 5}}, "13": {"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": 16}}, "14": {"start": {"line": 98, "column": 4}, "end": {"line": 98, "column": 37}}, "15": {"start": {"line": 103, "column": 4}, "end": {"line": 105, "column": 5}}, "16": {"start": {"line": 104, "column": 6}, "end": {"line": 104, "column": 16}}, "17": {"start": {"line": 106, "column": 4}, "end": {"line": 106, "column": 32}}, "18": {"start": {"line": 111, "column": 19}, "end": {"line": 111, "column": 36}}, "19": {"start": {"line": 112, "column": 4}, "end": {"line": 112, "column": 74}}, "20": {"start": {"line": 117, "column": 4}, "end": {"line": 124, "column": 5}}, "21": {"start": {"line": 119, "column": 6}, "end": {"line": 121, "column": 8}}, "22": {"start": {"line": 123, "column": 6}, "end": {"line": 123, "column": 19}}, "23": {"start": {"line": 30, "column": 13}, "end": {"line": 30, "column": 22}}, "24": {"start": {"line": 32, "column": 2}, "end": {"line": 32, "column": null}}, "25": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": null}}, "26": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": null}}, "27": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": null}}, "28": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": null}}, "29": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": null}}, "30": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": null}}, "31": {"start": {"line": 61, "column": 2}, "end": {"line": 61, "column": null}}, "32": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": null}}, "33": {"start": {"line": 67, "column": 2}, "end": {"line": 67, "column": null}}, "34": {"start": {"line": 70, "column": 2}, "end": {"line": 70, "column": null}}, "35": {"start": {"line": 73, "column": 2}, "end": {"line": 73, "column": null}}, "36": {"start": {"line": 76, "column": 2}, "end": {"line": 76, "column": null}}, "37": {"start": {"line": 82, "column": 2}, "end": {"line": 82, "column": null}}, "38": {"start": {"line": 78, "column": 19}, "end": {"line": 78, "column": 26}}, "39": {"start": {"line": 78, "column": 41}, "end": {"line": 78, "column": 59}}, "40": {"start": {"line": 85, "column": 2}, "end": {"line": 85, "column": null}}, "41": {"start": {"line": 91, "column": 2}, "end": {"line": 91, "column": null}}, "42": {"start": {"line": 88, "column": 10}, "end": {"line": 88, "column": 27}}, "43": {"start": {"line": 89, "column": 27}, "end": {"line": 89, "column": 54}}, "44": {"start": {"line": 30, "column": 13}, "end": {"line": 126, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 12}}, "loc": {"start": {"line": 15, "column": 25}, "end": {"line": 19, "column": 1}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 12}}, "loc": {"start": {"line": 21, "column": 27}, "end": {"line": 25, "column": 1}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 94, "column": 2}, "end": {"line": 94, "column": 6}}, "loc": {"start": {"line": 94, "column": 16}, "end": {"line": 99, "column": 3}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 102, "column": 2}, "end": {"line": 102, "column": 6}}, "loc": {"start": {"line": 102, "column": 11}, "end": {"line": 107, "column": 3}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 110, "column": 2}, "end": {"line": 110, "column": 19}}, "loc": {"start": {"line": 110, "column": 19}, "end": {"line": 113, "column": 3}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 116, "column": 2}, "end": {"line": 116, "column": 17}}, "loc": {"start": {"line": 116, "column": 17}, "end": {"line": 125, "column": 3}}}, "6": {"name": "(anonymous_8)", "decl": {"start": {"line": 78, "column": 13}, "end": {"line": 78, "column": 16}}, "loc": {"start": {"line": 78, "column": 19}, "end": {"line": 78, "column": 26}}}, "7": {"name": "(anonymous_9)", "decl": {"start": {"line": 78, "column": 28}, "end": {"line": 78, "column": 29}}, "loc": {"start": {"line": 78, "column": 41}, "end": {"line": 78, "column": 59}}}, "8": {"name": "(anonymous_10)", "decl": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 7}}, "loc": {"start": {"line": 88, "column": 10}, "end": {"line": 88, "column": 27}}}, "9": {"name": "(anonymous_11)", "decl": {"start": {"line": 89, "column": 4}, "end": {"line": 89, "column": 5}}, "loc": {"start": {"line": 89, "column": 27}, "end": {"line": 89, "column": 54}}}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 12}, "end": {"line": 15, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 15, "column": 12}, "end": {"line": 15, "column": 25}}, {"start": {"line": 15, "column": 25}, "end": {"line": 15, "column": null}}]}, "1": {"loc": {"start": {"line": 21, "column": 12}, "end": {"line": 21, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 21, "column": 12}, "end": {"line": 21, "column": 27}}, {"start": {"line": 21, "column": 27}, "end": {"line": 21, "column": null}}]}, "2": {"loc": {"start": {"line": 95, "column": 4}, "end": {"line": 97, "column": 5}}, "type": "if", "locations": [{"start": {"line": 95, "column": 4}, "end": {"line": 97, "column": 5}}]}, "3": {"loc": {"start": {"line": 95, "column": 8}, "end": {"line": 95, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 95, "column": 8}, "end": {"line": 95, "column": 23}}, {"start": {"line": 95, "column": 27}, "end": {"line": 95, "column": 53}}]}, "4": {"loc": {"start": {"line": 103, "column": 4}, "end": {"line": 105, "column": 5}}, "type": "if", "locations": [{"start": {"line": 103, "column": 4}, "end": {"line": 105, "column": 5}}]}, "5": {"loc": {"start": {"line": 103, "column": 8}, "end": {"line": 103, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 103, "column": 8}, "end": {"line": 103, "column": 23}}, {"start": {"line": 103, "column": 27}, "end": {"line": 103, "column": 48}}]}, "6": {"loc": {"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": 39}}, {"start": {"line": 120, "column": 43}, "end": {"line": 120, "column": 75}}]}}, "s": {"0": 9, "1": 9, "2": 9, "3": 9, "4": 9, "5": 9, "6": 9, "7": 9, "8": 9, "9": 9, "10": 9, "11": 9, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 9, "24": 9, "25": 9, "26": 9, "27": 9, "28": 9, "29": 9, "30": 9, "31": 9, "32": 9, "33": 9, "34": 9, "35": 9, "36": 9, "37": 9, "38": 0, "39": 0, "40": 9, "41": 9, "42": 0, "43": 0, "44": 9}, "f": {"0": 9, "1": 9, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [9, 9], "1": [9, 9], "2": [0], "3": [0, 0], "4": [0], "5": [0, 0], "6": [0, 0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/conformance-result.entity.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/conformance-result.entity.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 43}}, "2": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 48}}, "3": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": null}}, "4": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": null}}, "5": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": null}}, "6": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": null}}, "7": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": null}}, "8": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": null}}, "9": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": null}}, "10": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": null}}, "11": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": null}}, "12": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": null}}, "13": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": null}}, "14": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": null}}, "15": {"start": {"line": 34, "column": 7}, "end": {"line": 187, "column": null}}, "16": {"start": {"line": 145, "column": 4}, "end": {"line": 145, "column": 38}}, "17": {"start": {"line": 145, "column": 25}, "end": {"line": 145, "column": 38}}, "18": {"start": {"line": 146, "column": 4}, "end": {"line": 146, "column": 39}}, "19": {"start": {"line": 151, "column": 4}, "end": {"line": 151, "column": 70}}, "20": {"start": {"line": 156, "column": 4}, "end": {"line": 156, "column": 57}}, "21": {"start": {"line": 156, "column": 38}, "end": {"line": 156, "column": 57}}, "22": {"start": {"line": 157, "column": 4}, "end": {"line": 157, "column": 52}}, "23": {"start": {"line": 157, "column": 38}, "end": {"line": 157, "column": 52}}, "24": {"start": {"line": 158, "column": 4}, "end": {"line": 158, "column": 52}}, "25": {"start": {"line": 158, "column": 38}, "end": {"line": 158, "column": 52}}, "26": {"start": {"line": 159, "column": 4}, "end": {"line": 159, "column": 18}}, "27": {"start": {"line": 164, "column": 28}, "end": {"line": 164, "column": 60}}, "28": {"start": {"line": 166, "column": 4}, "end": {"line": 169, "column": 7}}, "29": {"start": {"line": 167, "column": 20}, "end": {"line": 167, "column": 60}}, "30": {"start": {"line": 168, "column": 6}, "end": {"line": 168, "column": 53}}, "31": {"start": {"line": 171, "column": 4}, "end": {"line": 174, "column": 29}}, "32": {"start": {"line": 172, "column": 22}, "end": {"line": 172, "column": 33}}, "33": {"start": {"line": 174, "column": 23}, "end": {"line": 174, "column": 27}}, "34": {"start": {"line": 179, "column": 4}, "end": {"line": 179, "column": 72}}, "35": {"start": {"line": 184, "column": 16}, "end": {"line": 184, "column": 43}}, "36": {"start": {"line": 185, "column": 4}, "end": {"line": 185, "column": 55}}, "37": {"start": {"line": 34, "column": 13}, "end": {"line": 34, "column": 30}}, "38": {"start": {"line": 36, "column": 2}, "end": {"line": 36, "column": null}}, "39": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": null}}, "40": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": null}}, "41": {"start": {"line": 49, "column": 2}, "end": {"line": 49, "column": null}}, "42": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": null}}, "43": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": null}}, "44": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": null}}, "45": {"start": {"line": 61, "column": 2}, "end": {"line": 61, "column": null}}, "46": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": null}}, "47": {"start": {"line": 67, "column": 2}, "end": {"line": 67, "column": null}}, "48": {"start": {"line": 70, "column": 2}, "end": {"line": 78, "column": null}}, "49": {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": null}}, "50": {"start": {"line": 84, "column": 2}, "end": {"line": 91, "column": null}}, "51": {"start": {"line": 94, "column": 2}, "end": {"line": 99, "column": null}}, "52": {"start": {"line": 102, "column": 2}, "end": {"line": 102, "column": null}}, "53": {"start": {"line": 105, "column": 2}, "end": {"line": 105, "column": null}}, "54": {"start": {"line": 108, "column": 2}, "end": {"line": 108, "column": null}}, "55": {"start": {"line": 111, "column": 2}, "end": {"line": 111, "column": null}}, "56": {"start": {"line": 114, "column": 2}, "end": {"line": 114, "column": null}}, "57": {"start": {"line": 117, "column": 2}, "end": {"line": 117, "column": null}}, "58": {"start": {"line": 120, "column": 2}, "end": {"line": 120, "column": null}}, "59": {"start": {"line": 123, "column": 2}, "end": {"line": 123, "column": null}}, "60": {"start": {"line": 129, "column": 2}, "end": {"line": 129, "column": null}}, "61": {"start": {"line": 125, "column": 19}, "end": {"line": 125, "column": 26}}, "62": {"start": {"line": 125, "column": 41}, "end": {"line": 125, "column": 67}}, "63": {"start": {"line": 132, "column": 2}, "end": {"line": 132, "column": null}}, "64": {"start": {"line": 138, "column": 2}, "end": {"line": 138, "column": null}}, "65": {"start": {"line": 134, "column": 19}, "end": {"line": 134, "column": 28}}, "66": {"start": {"line": 134, "column": 45}, "end": {"line": 134, "column": 73}}, "67": {"start": {"line": 141, "column": 2}, "end": {"line": 141, "column": null}}, "68": {"start": {"line": 34, "column": 13}, "end": {"line": 187, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 12}}, "loc": {"start": {"line": 14, "column": 29}, "end": {"line": 19, "column": 1}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 12}}, "loc": {"start": {"line": 21, "column": 25}, "end": {"line": 28, "column": 1}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 144, "column": 2}, "end": {"line": 144, "column": 6}}, "loc": {"start": {"line": 144, "column": 15}, "end": {"line": 147, "column": 3}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 150, "column": 2}, "end": {"line": 150, "column": 6}}, "loc": {"start": {"line": 150, "column": 18}, "end": {"line": 152, "column": 3}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 155, "column": 2}, "end": {"line": 155, "column": 6}}, "loc": {"start": {"line": 155, "column": 22}, "end": {"line": 160, "column": 3}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 163, "column": 2}, "end": {"line": 163, "column": 6}}, "loc": {"start": {"line": 163, "column": 25}, "end": {"line": 175, "column": 3}}}, "6": {"name": "(anonymous_8)", "decl": {"start": {"line": 166, "column": 28}, "end": {"line": 166, "column": 29}}, "loc": {"start": {"line": 166, "column": 42}, "end": {"line": 169, "column": 5}}}, "7": {"name": "(anonymous_9)", "decl": {"start": {"line": 172, "column": 12}, "end": {"line": 172, "column": 13}}, "loc": {"start": {"line": 172, "column": 22}, "end": {"line": 172, "column": 33}}}, "8": {"name": "(anonymous_10)", "decl": {"start": {"line": 174, "column": 11}, "end": {"line": 174, "column": 12}}, "loc": {"start": {"line": 174, "column": 23}, "end": {"line": 174, "column": 27}}}, "9": {"name": "(anonymous_11)", "decl": {"start": {"line": 178, "column": 2}, "end": {"line": 178, "column": 18}}, "loc": {"start": {"line": 178, "column": 18}, "end": {"line": 180, "column": 3}}}, "10": {"name": "(anonymous_12)", "decl": {"start": {"line": 183, "column": 2}, "end": {"line": 183, "column": 14}}, "loc": {"start": {"line": 183, "column": 34}, "end": {"line": 186, "column": 3}}}, "11": {"name": "(anonymous_13)", "decl": {"start": {"line": 125, "column": 13}, "end": {"line": 125, "column": 16}}, "loc": {"start": {"line": 125, "column": 19}, "end": {"line": 125, "column": 26}}}, "12": {"name": "(anonymous_14)", "decl": {"start": {"line": 125, "column": 28}, "end": {"line": 125, "column": 29}}, "loc": {"start": {"line": 125, "column": 41}, "end": {"line": 125, "column": 67}}}, "13": {"name": "(anonymous_15)", "decl": {"start": {"line": 134, "column": 13}, "end": {"line": 134, "column": 16}}, "loc": {"start": {"line": 134, "column": 19}, "end": {"line": 134, "column": 28}}}, "14": {"name": "(anonymous_16)", "decl": {"start": {"line": 134, "column": 30}, "end": {"line": 134, "column": 31}}, "loc": {"start": {"line": 134, "column": 45}, "end": {"line": 134, "column": 73}}}}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": 29}}, {"start": {"line": 14, "column": 29}, "end": {"line": 14, "column": null}}]}, "1": {"loc": {"start": {"line": 21, "column": 12}, "end": {"line": 21, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 21, "column": 12}, "end": {"line": 21, "column": 25}}, {"start": {"line": 21, "column": 25}, "end": {"line": 21, "column": null}}]}, "2": {"loc": {"start": {"line": 145, "column": 4}, "end": {"line": 145, "column": 38}}, "type": "if", "locations": [{"start": {"line": 145, "column": 4}, "end": {"line": 145, "column": 38}}]}, "3": {"loc": {"start": {"line": 151, "column": 11}, "end": {"line": 151, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 151, "column": 11}, "end": {"line": 151, "column": 25}}, {"start": {"line": 151, "column": 29}, "end": {"line": 151, "column": 69}}]}, "4": {"loc": {"start": {"line": 156, "column": 4}, "end": {"line": 156, "column": 57}}, "type": "if", "locations": [{"start": {"line": 156, "column": 4}, "end": {"line": 156, "column": 57}}]}, "5": {"loc": {"start": {"line": 157, "column": 4}, "end": {"line": 157, "column": 52}}, "type": "if", "locations": [{"start": {"line": 157, "column": 4}, "end": {"line": 157, "column": 52}}]}, "6": {"loc": {"start": {"line": 158, "column": 4}, "end": {"line": 158, "column": 52}}, "type": "if", "locations": [{"start": {"line": 158, "column": 4}, "end": {"line": 158, "column": 52}}]}, "7": {"loc": {"start": {"line": 167, "column": 20}, "end": {"line": 167, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 167, "column": 20}, "end": {"line": 167, "column": 55}}, {"start": {"line": 167, "column": 59}, "end": {"line": 167, "column": 60}}]}, "8": {"loc": {"start": {"line": 184, "column": 16}, "end": {"line": 184, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 184, "column": 16}, "end": {"line": 184, "column": 26}}, {"start": {"line": 184, "column": 30}, "end": {"line": 184, "column": 43}}]}}, "s": {"0": 9, "1": 9, "2": 9, "3": 9, "4": 9, "5": 9, "6": 9, "7": 9, "8": 9, "9": 9, "10": 9, "11": 9, "12": 9, "13": 9, "14": 9, "15": 9, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 9, "38": 9, "39": 9, "40": 9, "41": 9, "42": 9, "43": 9, "44": 9, "45": 9, "46": 9, "47": 9, "48": 9, "49": 9, "50": 9, "51": 9, "52": 9, "53": 9, "54": 9, "55": 9, "56": 9, "57": 9, "58": 9, "59": 9, "60": 9, "61": 0, "62": 0, "63": 9, "64": 9, "65": 0, "66": 0, "67": 9, "68": 9}, "f": {"0": 9, "1": 9, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "b": {"0": [9, 9], "1": [9, 9], "2": [0], "3": [0, 0], "4": [0], "5": [0], "6": [0], "7": [0, 0], "8": [0, 0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/event-log.entity.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/event-log.entity.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 43}}, "2": {"start": {"line": 15, "column": 7}, "end": {"line": 48, "column": null}}, "3": {"start": {"line": 15, "column": 13}, "end": {"line": 15, "column": 21}}, "4": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": null}}, "5": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": null}}, "6": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": null}}, "7": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": null}}, "8": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": null}}, "9": {"start": {"line": 32, "column": 2}, "end": {"line": 32, "column": null}}, "10": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": null}}, "11": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": null}}, "12": {"start": {"line": 44, "column": 2}, "end": {"line": 44, "column": null}}, "13": {"start": {"line": 40, "column": 19}, "end": {"line": 40, "column": 26}}, "14": {"start": {"line": 40, "column": 41}, "end": {"line": 40, "column": 58}}, "15": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": null}}, "16": {"start": {"line": 15, "column": 13}, "end": {"line": 48, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 40, "column": 13}, "end": {"line": 40, "column": 16}}, "loc": {"start": {"line": 40, "column": 19}, "end": {"line": 40, "column": 26}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 40, "column": 28}, "end": {"line": 40, "column": 29}}, "loc": {"start": {"line": 40, "column": 41}, "end": {"line": 40, "column": 58}}}}, "branchMap": {}, "s": {"0": 9, "1": 9, "2": 9, "3": 9, "4": 9, "5": 9, "6": 9, "7": 9, "8": 9, "9": 9, "10": 9, "11": 9, "12": 9, "13": 0, "14": 0, "15": 9, "16": 9}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/index.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 9}}, "1": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 37}}, "2": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 9}}, "3": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 18}}, "4": {"start": {"line": 2, "column": 18}, "end": {"line": 2, "column": 58}}, "5": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 9}}, "6": {"start": {"line": 3, "column": 9}, "end": {"line": 3, "column": 46}}, "7": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": null}}, "8": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": null}}, "9": {"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": null}}, "10": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": null}}, "11": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 9}}, "12": {"start": {"line": 9, "column": 9}, "end": {"line": 9, "column": 48}}, "13": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": null}}, "14": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": null}}, "15": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": null}}, "16": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 13}}, "loc": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 37}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 16}}, "loc": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 18}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 2, "column": 18}, "end": {"line": 2, "column": 31}}, "loc": {"start": {"line": 2, "column": 18}, "end": {"line": 2, "column": 58}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 3, "column": 9}, "end": {"line": 3, "column": 17}}, "loc": {"start": {"line": 3, "column": 9}, "end": {"line": 3, "column": 46}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": 16}}, "loc": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": null}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": 14}}, "loc": {"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": null}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": 16}}, "loc": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": null}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 9, "column": 9}, "end": {"line": 9, "column": 18}}, "loc": {"start": {"line": 9, "column": 9}, "end": {"line": 9, "column": 48}}}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 19}}, "loc": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": null}}}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 19}}, "loc": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": null}}}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": 15}}, "loc": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": null}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/process.entity.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/process.entity.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 37}}, "2": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 46}}, "3": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 58}}, "4": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 48}}, "5": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 64}}, "6": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": null}}, "7": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": null}}, "8": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": null}}, "9": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": null}}, "10": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": null}}, "11": {"start": {"line": 25, "column": 7}, "end": {"line": 75, "column": null}}, "12": {"start": {"line": 25, "column": 13}, "end": {"line": 25, "column": 20}}, "13": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": null}}, "14": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": null}}, "15": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": null}}, "16": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": null}}, "17": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": null}}, "18": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": null}}, "19": {"start": {"line": 49, "column": 2}, "end": {"line": 49, "column": null}}, "20": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": null}}, "21": {"start": {"line": 56, "column": 2}, "end": {"line": 56, "column": null}}, "22": {"start": {"line": 54, "column": 19}, "end": {"line": 54, "column": 23}}, "23": {"start": {"line": 54, "column": 35}, "end": {"line": 54, "column": 49}}, "24": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": null}}, "25": {"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": null}}, "26": {"start": {"line": 61, "column": 19}, "end": {"line": 61, "column": 27}}, "27": {"start": {"line": 61, "column": 43}, "end": {"line": 61, "column": 59}}, "28": {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": null}}, "29": {"start": {"line": 64, "column": 19}, "end": {"line": 64, "column": 33}}, "30": {"start": {"line": 64, "column": 55}, "end": {"line": 64, "column": 77}}, "31": {"start": {"line": 68, "column": 2}, "end": {"line": 68, "column": null}}, "32": {"start": {"line": 67, "column": 19}, "end": {"line": 67, "column": 28}}, "33": {"start": {"line": 67, "column": 45}, "end": {"line": 67, "column": 62}}, "34": {"start": {"line": 74, "column": 2}, "end": {"line": 74, "column": null}}, "35": {"start": {"line": 71, "column": 10}, "end": {"line": 71, "column": 27}}, "36": {"start": {"line": 72, "column": 27}, "end": {"line": 72, "column": 52}}, "37": {"start": {"line": 25, "column": 13}, "end": {"line": 75, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 12}}, "loc": {"start": {"line": 17, "column": 25}, "end": {"line": 22, "column": 1}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 54, "column": 13}, "end": {"line": 54, "column": 16}}, "loc": {"start": {"line": 54, "column": 19}, "end": {"line": 54, "column": 23}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 54, "column": 25}, "end": {"line": 54, "column": 26}}, "loc": {"start": {"line": 54, "column": 35}, "end": {"line": 54, "column": 49}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 61, "column": 13}, "end": {"line": 61, "column": 16}}, "loc": {"start": {"line": 61, "column": 19}, "end": {"line": 61, "column": 27}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 61, "column": 29}, "end": {"line": 61, "column": 30}}, "loc": {"start": {"line": 61, "column": 43}, "end": {"line": 61, "column": 59}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 64, "column": 13}, "end": {"line": 64, "column": 16}}, "loc": {"start": {"line": 64, "column": 19}, "end": {"line": 64, "column": 33}}}, "6": {"name": "(anonymous_8)", "decl": {"start": {"line": 64, "column": 35}, "end": {"line": 64, "column": 36}}, "loc": {"start": {"line": 64, "column": 55}, "end": {"line": 64, "column": 77}}}, "7": {"name": "(anonymous_9)", "decl": {"start": {"line": 67, "column": 13}, "end": {"line": 67, "column": 16}}, "loc": {"start": {"line": 67, "column": 19}, "end": {"line": 67, "column": 28}}}, "8": {"name": "(anonymous_10)", "decl": {"start": {"line": 67, "column": 30}, "end": {"line": 67, "column": 31}}, "loc": {"start": {"line": 67, "column": 45}, "end": {"line": 67, "column": 62}}}, "9": {"name": "(anonymous_11)", "decl": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 7}}, "loc": {"start": {"line": 71, "column": 10}, "end": {"line": 71, "column": 27}}}, "10": {"name": "(anonymous_12)", "decl": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 5}}, "loc": {"start": {"line": 72, "column": 27}, "end": {"line": 72, "column": 52}}}}, "branchMap": {"0": {"loc": {"start": {"line": 17, "column": 12}, "end": {"line": 17, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 17, "column": 12}, "end": {"line": 17, "column": 25}}, {"start": {"line": 17, "column": 25}, "end": {"line": 17, "column": null}}]}}, "s": {"0": 9, "1": 9, "2": 9, "3": 9, "4": 9, "5": 9, "6": 9, "7": 9, "8": 9, "9": 9, "10": 9, "11": 9, "12": 9, "13": 9, "14": 9, "15": 9, "16": 9, "17": 9, "18": 9, "19": 9, "20": 9, "21": 9, "22": 0, "23": 0, "24": 9, "25": 9, "26": 0, "27": 0, "28": 9, "29": 0, "30": 0, "31": 9, "32": 0, "33": 0, "34": 9, "35": 0, "36": 0, "37": 9}, "f": {"0": 9, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "b": {"0": [9, 9]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/user.entity.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/user.entity.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 44}}, "2": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 43}}, "3": {"start": {"line": 13, "column": 7}, "end": {"line": 44, "column": null}}, "4": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": 17}}, "5": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": null}}, "6": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": null}}, "7": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": null}}, "8": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": null}}, "9": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": null}}, "10": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": null}}, "11": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": null}}, "12": {"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": null}}, "13": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": null}}, "14": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": null}}, "15": {"start": {"line": 42, "column": 19}, "end": {"line": 42, "column": 26}}, "16": {"start": {"line": 42, "column": 41}, "end": {"line": 42, "column": 53}}, "17": {"start": {"line": 13, "column": 13}, "end": {"line": 44, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 42, "column": 13}, "end": {"line": 42, "column": 16}}, "loc": {"start": {"line": 42, "column": 19}, "end": {"line": 42, "column": 26}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 42, "column": 28}, "end": {"line": 42, "column": 29}}, "loc": {"start": {"line": 42, "column": 41}, "end": {"line": 42, "column": 53}}}}, "branchMap": {}, "s": {"0": 9, "1": 9, "2": 9, "3": 9, "4": 9, "5": 9, "6": 9, "7": 9, "8": 9, "9": 9, "10": 9, "11": 9, "12": 9, "13": 9, "14": 9, "15": 0, "16": 0, "17": 9}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/processes/processes.controller.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/processes/processes.controller.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": null}}, "2": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 55}}, "3": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 59}}, "4": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 61}}, "5": {"start": {"line": 28, "column": 7}, "end": {"line": 95, "column": null}}, "6": {"start": {"line": 29, "column": 31}, "end": {"line": 29, "column": 49}}, "7": {"start": {"line": 35, "column": 4}, "end": {"line": 35, "column": 71}}, "8": {"start": {"line": 47, "column": 19}, "end": {"line": 47, "column": 59}}, "9": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 49}}, "10": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 67}}, "11": {"start": {"line": 68, "column": 4}, "end": {"line": 70, "column": 5}}, "12": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": 70}}, "13": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 59}}, "14": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 76}}, "15": {"start": {"line": 93, "column": 4}, "end": {"line": 93, "column": 58}}, "16": {"start": {"line": 28, "column": 13}, "end": {"line": 28, "column": 32}}, "17": {"start": {"line": 34, "column": 2}, "end": {"line": 36, "column": null}}, "18": {"start": {"line": 46, "column": 2}, "end": {"line": 49, "column": null}}, "19": {"start": {"line": 54, "column": 2}, "end": {"line": 56, "column": null}}, "20": {"start": {"line": 67, "column": 2}, "end": {"line": 72, "column": null}}, "21": {"start": {"line": 79, "column": 2}, "end": {"line": 85, "column": null}}, "22": {"start": {"line": 92, "column": 2}, "end": {"line": 94, "column": null}}, "23": {"start": {"line": 28, "column": 13}, "end": {"line": 95, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 31}}, "loc": {"start": {"line": 29, "column": 65}, "end": {"line": 29, "column": 69}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": 8}}, "loc": {"start": {"line": 34, "column": 67}, "end": {"line": 36, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 9}}, "loc": {"start": {"line": 46, "column": 52}, "end": {"line": 49, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 54, "column": 2}, "end": {"line": 54, "column": 15}}, "loc": {"start": {"line": 54, "column": 30}, "end": {"line": 56, "column": 3}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 67, "column": 2}, "end": {"line": 67, "column": 9}}, "loc": {"start": {"line": 67, "column": 89}, "end": {"line": 72, "column": 3}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 79, "column": 2}, "end": {"line": 79, "column": 8}}, "loc": {"start": {"line": 82, "column": 18}, "end": {"line": 85, "column": 3}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 92, "column": 2}, "end": {"line": 92, "column": 8}}, "loc": {"start": {"line": 92, "column": 48}, "end": {"line": 94, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 47, "column": 19}, "end": {"line": 47, "column": 59}}, "type": "cond-expr", "locations": [{"start": {"line": 47, "column": 36}, "end": {"line": 47, "column": 45}}, {"start": {"line": 47, "column": 48}, "end": {"line": 47, "column": 59}}]}, "1": {"loc": {"start": {"line": 68, "column": 4}, "end": {"line": 70, "column": 5}}, "type": "if", "locations": [{"start": {"line": 68, "column": 4}, "end": {"line": 70, "column": 5}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 12, "7": 2, "8": 2, "9": 2, "10": 1, "11": 2, "12": 0, "13": 2, "14": 2, "15": 2, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1}, "f": {"0": 12, "1": 2, "2": 2, "3": 1, "4": 2, "5": 2, "6": 2}, "b": {"0": [1, 1], "1": [0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/processes/processes.module.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/processes/processes.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 48}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 55}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 61}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 53}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 56}}, "6": {"start": {"line": 14, "column": 7}, "end": {"line": 14, "column": null}}, "7": {"start": {"line": 14, "column": 13}, "end": {"line": 14, "column": 28}}, "8": {"start": {"line": 14, "column": 13}, "end": {"line": 14, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/processes/processes.service.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/processes/processes.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 51}}, "2": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 37}}, "3": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 53}}, "4": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 56}}, "5": {"start": {"line": 13, "column": 7}, "end": {"line": 173, "column": null}}, "6": {"start": {"line": 16, "column": 21}, "end": {"line": 16, "column": 40}}, "7": {"start": {"line": 18, "column": 21}, "end": {"line": 18, "column": 41}}, "8": {"start": {"line": 25, "column": 20}, "end": {"line": 28, "column": 6}}, "9": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": 48}}, "10": {"start": {"line": 34, "column": 25}, "end": {"line": 49, "column": 8}}, "11": {"start": {"line": 51, "column": 4}, "end": {"line": 53, "column": 5}}, "12": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 65}}, "13": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 34}}, "14": {"start": {"line": 59, "column": 25}, "end": {"line": 63, "column": 40}}, "15": {"start": {"line": 65, "column": 4}, "end": {"line": 67, "column": 5}}, "16": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 68}}, "17": {"start": {"line": 69, "column": 20}, "end": {"line": 69, "column": 47}}, "18": {"start": {"line": 71, "column": 4}, "end": {"line": 73, "column": 5}}, "19": {"start": {"line": 72, "column": 6}, "end": {"line": 72, "column": 43}}, "20": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": 19}}, "21": {"start": {"line": 79, "column": 20}, "end": {"line": 79, "column": 50}}, "22": {"start": {"line": 82, "column": 26}, "end": {"line": 93, "column": 18}}, "23": {"start": {"line": 95, "column": 4}, "end": {"line": 105, "column": 6}}, "24": {"start": {"line": 113, "column": 20}, "end": {"line": 113, "column": 42}}, "25": {"start": {"line": 116, "column": 4}, "end": {"line": 118, "column": 5}}, "26": {"start": {"line": 117, "column": 6}, "end": {"line": 117, "column": 47}}, "27": {"start": {"line": 120, "column": 4}, "end": {"line": 120, "column": 45}}, "28": {"start": {"line": 122, "column": 4}, "end": {"line": 122, "column": 48}}, "29": {"start": {"line": 126, "column": 20}, "end": {"line": 126, "column": 42}}, "30": {"start": {"line": 129, "column": 4}, "end": {"line": 131, "column": 5}}, "31": {"start": {"line": 130, "column": 6}, "end": {"line": 130, "column": 47}}, "32": {"start": {"line": 133, "column": 4}, "end": {"line": 133, "column": 49}}, "33": {"start": {"line": 137, "column": 25}, "end": {"line": 137, "column": 77}}, "34": {"start": {"line": 139, "column": 4}, "end": {"line": 141, "column": 5}}, "35": {"start": {"line": 140, "column": 6}, "end": {"line": 140, "column": 65}}, "36": {"start": {"line": 143, "column": 56}, "end": {"line": 161, "column": 6}}, "37": {"start": {"line": 163, "column": 4}, "end": {"line": 171, "column": 6}}, "38": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": 29}}, "39": {"start": {"line": 13, "column": 13}, "end": {"line": 173, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": null}}, "loc": {"start": {"line": 18, "column": 61}, "end": {"line": 19, "column": 6}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 7}}, "loc": {"start": {"line": 23, "column": 18}, "end": {"line": 31, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": 7}}, "loc": {"start": {"line": 33, "column": 31}, "end": {"line": 56, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": 7}}, "loc": {"start": {"line": 58, "column": 43}, "end": {"line": 76, "column": 3}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 78, "column": 2}, "end": {"line": 78, "column": 7}}, "loc": {"start": {"line": 78, "column": 52}, "end": {"line": 106, "column": 3}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 108, "column": 2}, "end": {"line": 108, "column": 7}}, "loc": {"start": {"line": 111, "column": 18}, "end": {"line": 123, "column": 3}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 125, "column": 2}, "end": {"line": 125, "column": 7}}, "loc": {"start": {"line": 125, "column": 41}, "end": {"line": 134, "column": 3}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 136, "column": 2}, "end": {"line": 136, "column": 7}}, "loc": {"start": {"line": 136, "column": 44}, "end": {"line": 172, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 51, "column": 4}, "end": {"line": 53, "column": 5}}, "type": "if", "locations": [{"start": {"line": 51, "column": 4}, "end": {"line": 53, "column": 5}}]}, "1": {"loc": {"start": {"line": 65, "column": 4}, "end": {"line": 67, "column": 5}}, "type": "if", "locations": [{"start": {"line": 65, "column": 4}, "end": {"line": 67, "column": 5}}]}, "2": {"loc": {"start": {"line": 71, "column": 4}, "end": {"line": 73, "column": 5}}, "type": "if", "locations": [{"start": {"line": 71, "column": 4}, "end": {"line": 73, "column": 5}}]}, "3": {"loc": {"start": {"line": 98, "column": 21}, "end": {"line": 98, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 98, "column": 21}, "end": {"line": 98, "column": 56}}, {"start": {"line": 98, "column": 60}, "end": {"line": 98, "column": 61}}]}, "4": {"loc": {"start": {"line": 99, "column": 21}, "end": {"line": 99, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 99, "column": 21}, "end": {"line": 99, "column": 56}}, {"start": {"line": 99, "column": 60}, "end": {"line": 99, "column": 61}}]}, "5": {"loc": {"start": {"line": 100, "column": 26}, "end": {"line": 100, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 100, "column": 26}, "end": {"line": 100, "column": 66}}, {"start": {"line": 100, "column": 70}, "end": {"line": 100, "column": 71}}]}, "6": {"loc": {"start": {"line": 101, "column": 25}, "end": {"line": 101, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 101, "column": 25}, "end": {"line": 101, "column": 64}}, {"start": {"line": 101, "column": 68}, "end": {"line": 101, "column": 69}}]}, "7": {"loc": {"start": {"line": 116, "column": 4}, "end": {"line": 118, "column": 5}}, "type": "if", "locations": [{"start": {"line": 116, "column": 4}, "end": {"line": 118, "column": 5}}]}, "8": {"loc": {"start": {"line": 129, "column": 4}, "end": {"line": 131, "column": 5}}, "type": "if", "locations": [{"start": {"line": 129, "column": 4}, "end": {"line": 131, "column": 5}}]}, "9": {"loc": {"start": {"line": 139, "column": 4}, "end": {"line": 141, "column": 5}}, "type": "if", "locations": [{"start": {"line": 139, "column": 4}, "end": {"line": 141, "column": 5}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 1, "39": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0], "8": [0], "9": [0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/processes/dto/create-process.dto.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/processes/dto/create-process.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 46}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": null}}, "2": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 62}}, "3": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 13}}, "4": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": null}}, "5": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": null}}, "6": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": null}}, "7": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": null}}, "8": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1}, "f": {}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/processes/dto/index.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/processes/dto/index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 9}}, "1": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 56}}, "2": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 9}}, "3": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 56}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 25}}, "loc": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 56}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 25}}, "loc": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 56}}}}, "branchMap": {}, "s": {"0": 1, "1": 2, "2": 1, "3": 2}, "f": {"0": 1, "1": 1}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/processes/dto/update-process.dto.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/processes/dto/update-process.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 46}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 73}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 62}}, "3": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 13}}, "4": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": null}}, "5": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": null}}, "6": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": null}}, "7": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": null}}, "8": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1}, "f": {}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/test-utils/conformance-test-data-generator.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/test-utils/conformance-test-data-generator.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 25}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 29}}, "2": {"start": {"line": 21, "column": 19}, "end": {"line": 24, "column": 4}}, "3": {"start": {"line": 28, "column": 4}, "end": {"line": 30, "column": 5}}, "4": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 56}}, "5": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 36}}, "6": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 36}}, "7": {"start": {"line": 43, "column": 22}, "end": {"line": 43, "column": 50}}, "8": {"start": {"line": 46, "column": 4}, "end": {"line": 48, "column": 5}}, "9": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 43}}, "10": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 52}}, "11": {"start": {"line": 57, "column": 19}, "end": {"line": 70, "column": 6}}, "12": {"start": {"line": 72, "column": 4}, "end": {"line": 76, "column": 5}}, "13": {"start": {"line": 73, "column": 23}, "end": {"line": 73, "column": 60}}, "14": {"start": {"line": 74, "column": 6}, "end": {"line": 74, "column": 57}}, "15": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 45}}, "16": {"start": {"line": 83, "column": 4}, "end": {"line": 88, "column": 6}}, "17": {"start": {"line": 95, "column": 38}, "end": {"line": 95, "column": 40}}, "18": {"start": {"line": 96, "column": 23}, "end": {"line": 96, "column": 61}}, "19": {"start": {"line": 99, "column": 4}, "end": {"line": 115, "column": 5}}, "20": {"start": {"line": 99, "column": 22}, "end": {"line": 99, "column": 23}}, "21": {"start": {"line": 100, "column": 22}, "end": {"line": 100, "column": 44}}, "22": {"start": {"line": 102, "column": 6}, "end": {"line": 114, "column": 9}}, "23": {"start": {"line": 103, "column": 8}, "end": {"line": 105, "column": 10}}, "24": {"start": {"line": 107, "column": 8}, "end": {"line": 113, "column": 11}}, "25": {"start": {"line": 117, "column": 4}, "end": {"line": 123, "column": 6}}, "26": {"start": {"line": 130, "column": 38}, "end": {"line": 130, "column": 40}}, "27": {"start": {"line": 131, "column": 23}, "end": {"line": 131, "column": 61}}, "28": {"start": {"line": 134, "column": 4}, "end": {"line": 158, "column": 5}}, "29": {"start": {"line": 134, "column": 22}, "end": {"line": 134, "column": 23}}, "30": {"start": {"line": 135, "column": 22}, "end": {"line": 135, "column": 44}}, "31": {"start": {"line": 136, "column": 29}, "end": {"line": 136, "column": 44}}, "32": {"start": {"line": 139, "column": 6}, "end": {"line": 143, "column": 7}}, "33": {"start": {"line": 141, "column": 26}, "end": {"line": 141, "column": 59}}, "34": {"start": {"line": 142, "column": 8}, "end": {"line": 142, "column": 44}}, "35": {"start": {"line": 145, "column": 6}, "end": {"line": 157, "column": 9}}, "36": {"start": {"line": 146, "column": 8}, "end": {"line": 148, "column": 10}}, "37": {"start": {"line": 150, "column": 8}, "end": {"line": 156, "column": 11}}, "38": {"start": {"line": 160, "column": 4}, "end": {"line": 166, "column": 6}}, "39": {"start": {"line": 173, "column": 38}, "end": {"line": 173, "column": 40}}, "40": {"start": {"line": 174, "column": 23}, "end": {"line": 174, "column": 61}}, "41": {"start": {"line": 175, "column": 28}, "end": {"line": 175, "column": 52}}, "42": {"start": {"line": 178, "column": 4}, "end": {"line": 219, "column": 5}}, "43": {"start": {"line": 178, "column": 22}, "end": {"line": 178, "column": 23}}, "44": {"start": {"line": 179, "column": 22}, "end": {"line": 179, "column": 44}}, "45": {"start": {"line": 180, "column": 29}, "end": {"line": 180, "column": 44}}, "46": {"start": {"line": 183, "column": 6}, "end": {"line": 204, "column": 7}}, "47": {"start": {"line": 185, "column": 8}, "end": {"line": 191, "column": 9}}, "48": {"start": {"line": 187, "column": 12}, "end": {"line": 187, "column": 79}}, "49": {"start": {"line": 189, "column": 12}, "end": {"line": 189, "column": 71}}, "50": {"start": {"line": 190, "column": 10}, "end": {"line": 190, "column": 63}}, "51": {"start": {"line": 194, "column": 8}, "end": {"line": 203, "column": 9}}, "52": {"start": {"line": 196, "column": 12}, "end": {"line": 196, "column": 71}}, "53": {"start": {"line": 198, "column": 12}, "end": {"line": 198, "column": 71}}, "54": {"start": {"line": 199, "column": 10}, "end": {"line": 202, "column": 12}}, "55": {"start": {"line": 206, "column": 6}, "end": {"line": 218, "column": 9}}, "56": {"start": {"line": 207, "column": 8}, "end": {"line": 209, "column": 10}}, "57": {"start": {"line": 211, "column": 8}, "end": {"line": 217, "column": 11}}, "58": {"start": {"line": 221, "column": 4}, "end": {"line": 227, "column": 6}}, "59": {"start": {"line": 234, "column": 38}, "end": {"line": 234, "column": 40}}, "60": {"start": {"line": 235, "column": 23}, "end": {"line": 235, "column": 61}}, "61": {"start": {"line": 236, "column": 28}, "end": {"line": 242, "column": 6}}, "62": {"start": {"line": 245, "column": 4}, "end": {"line": 294, "column": 5}}, "63": {"start": {"line": 245, "column": 22}, "end": {"line": 245, "column": 23}}, "64": {"start": {"line": 246, "column": 22}, "end": {"line": 246, "column": 44}}, "65": {"start": {"line": 247, "column": 27}, "end": {"line": 247, "column": 42}}, "66": {"start": {"line": 250, "column": 6}, "end": {"line": 279, "column": 7}}, "67": {"start": {"line": 252, "column": 25}, "end": {"line": 252, "column": 58}}, "68": {"start": {"line": 253, "column": 8}, "end": {"line": 258, "column": 9}}, "69": {"start": {"line": 253, "column": 21}, "end": {"line": 253, "column": 22}}, "70": {"start": {"line": 255, "column": 12}, "end": {"line": 255, "column": 79}}, "71": {"start": {"line": 256, "column": 30}, "end": {"line": 256, "column": 79}}, "72": {"start": {"line": 257, "column": 10}, "end": {"line": 257, "column": 63}}, "73": {"start": {"line": 261, "column": 8}, "end": {"line": 264, "column": 9}}, "74": {"start": {"line": 262, "column": 28}, "end": {"line": 262, "column": 61}}, "75": {"start": {"line": 263, "column": 10}, "end": {"line": 263, "column": 46}}, "76": {"start": {"line": 267, "column": 8}, "end": {"line": 278, "column": 9}}, "77": {"start": {"line": 268, "column": 29}, "end": {"line": 268, "column": 56}}, "78": {"start": {"line": 269, "column": 10}, "end": {"line": 272, "column": 11}}, "79": {"start": {"line": 269, "column": 23}, "end": {"line": 269, "column": 44}}, "80": {"start": {"line": 270, "column": 22}, "end": {"line": 270, "column": 57}}, "81": {"start": {"line": 271, "column": 12}, "end": {"line": 271, "column": 76}}, "82": {"start": {"line": 273, "column": 10}, "end": {"line": 277, "column": 12}}, "83": {"start": {"line": 281, "column": 6}, "end": {"line": 293, "column": 9}}, "84": {"start": {"line": 282, "column": 8}, "end": {"line": 284, "column": 10}}, "85": {"start": {"line": 286, "column": 8}, "end": {"line": 292, "column": 11}}, "86": {"start": {"line": 296, "column": 4}, "end": {"line": 302, "column": 6}}, "87": {"start": {"line": 309, "column": 22}, "end": {"line": 309, "column": 65}}, "88": {"start": {"line": 310, "column": 20}, "end": {"line": 315, "column": 17}}, "89": {"start": {"line": 313, "column": 10}, "end": {"line": 313, "column": 112}}, "90": {"start": {"line": 317, "column": 23}, "end": {"line": 317, "column": 42}}, "91": {"start": {"line": 318, "column": 21}, "end": {"line": 318, "column": 43}}, "92": {"start": {"line": 319, "column": 21}, "end": {"line": 319, "column": 56}}, "93": {"start": {"line": 321, "column": 4}, "end": {"line": 321, "column": 52}}, "94": {"start": {"line": 322, "column": 4}, "end": {"line": 324, "column": 6}}, "95": {"start": {"line": 327, "column": 28}, "end": {"line": 327, "column": 51}}, "96": {"start": {"line": 328, "column": 28}, "end": {"line": 328, "column": 70}}, "97": {"start": {"line": 329, "column": 24}, "end": {"line": 345, "column": 6}}, "98": {"start": {"line": 335, "column": 59}, "end": {"line": 335, "column": 69}}, "99": {"start": {"line": 336, "column": 62}, "end": {"line": 336, "column": 74}}, "100": {"start": {"line": 339, "column": 45}, "end": {"line": 339, "column": 68}}, "101": {"start": {"line": 342, "column": 45}, "end": {"line": 342, "column": 68}}, "102": {"start": {"line": 347, "column": 4}, "end": {"line": 351, "column": 6}}, "103": {"start": {"line": 358, "column": 4}, "end": {"line": 394, "column": 21}}, "104": {"start": {"line": 401, "column": 4}, "end": {"line": 455, "column": 21}}, "105": {"start": {"line": 462, "column": 4}, "end": {"line": 511, "column": 21}}, "106": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 13}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": null}}, "loc": {"start": {"line": 26, "column": 2}, "end": {"line": 31, "column": 3}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 36, "column": 2}, "end": {"line": 36, "column": 7}}, "loc": {"start": {"line": 36, "column": 27}, "end": {"line": 51, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 56, "column": 10}, "end": {"line": 56, "column": 15}}, "loc": {"start": {"line": 56, "column": 34}, "end": {"line": 77, "column": 3}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 82, "column": 10}, "end": {"line": 82, "column": 31}}, "loc": {"start": {"line": 82, "column": 31}, "end": {"line": 89, "column": 3}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 94, "column": 10}, "end": {"line": 94, "column": 46}}, "loc": {"start": {"line": 94, "column": 46}, "end": {"line": 124, "column": 3}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 102, "column": 25}, "end": {"line": 102, "column": 26}}, "loc": {"start": {"line": 102, "column": 45}, "end": {"line": 114, "column": 7}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 129, "column": 10}, "end": {"line": 129, "column": 41}}, "loc": {"start": {"line": 129, "column": 41}, "end": {"line": 167, "column": 3}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 145, "column": 29}, "end": {"line": 145, "column": 30}}, "loc": {"start": {"line": 145, "column": 49}, "end": {"line": 157, "column": 7}}}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 172, "column": 10}, "end": {"line": 172, "column": 41}}, "loc": {"start": {"line": 172, "column": 41}, "end": {"line": 228, "column": 3}}}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 206, "column": 29}, "end": {"line": 206, "column": 30}}, "loc": {"start": {"line": 206, "column": 49}, "end": {"line": 218, "column": 7}}}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 233, "column": 10}, "end": {"line": 233, "column": 41}}, "loc": {"start": {"line": 233, "column": 41}, "end": {"line": 303, "column": 3}}}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 281, "column": 29}, "end": {"line": 281, "column": 30}}, "loc": {"start": {"line": 281, "column": 49}, "end": {"line": 293, "column": 7}}}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 308, "column": 10}, "end": {"line": 308, "column": 15}}, "loc": {"start": {"line": 308, "column": 54}, "end": {"line": 352, "column": 3}}}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 312, "column": 8}, "end": {"line": 312, "column": 9}}, "loc": {"start": {"line": 313, "column": 10}, "end": {"line": 313, "column": 112}}}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 335, "column": 50}, "end": {"line": 335, "column": 51}}, "loc": {"start": {"line": 335, "column": 59}, "end": {"line": 335, "column": 69}}}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 336, "column": 53}, "end": {"line": 336, "column": 54}}, "loc": {"start": {"line": 336, "column": 62}, "end": {"line": 336, "column": 74}}}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 339, "column": 36}, "end": {"line": 339, "column": 37}}, "loc": {"start": {"line": 339, "column": 45}, "end": {"line": 339, "column": 68}}}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 342, "column": 36}, "end": {"line": 342, "column": 37}}, "loc": {"start": {"line": 342, "column": 45}, "end": {"line": 342, "column": 68}}}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 357, "column": 10}, "end": {"line": 357, "column": 35}}, "loc": {"start": {"line": 357, "column": 35}, "end": {"line": 395, "column": 3}}}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 400, "column": 10}, "end": {"line": 400, "column": 36}}, "loc": {"start": {"line": 400, "column": 36}, "end": {"line": 456, "column": 3}}}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 461, "column": 10}, "end": {"line": 461, "column": 37}}, "loc": {"start": {"line": 461, "column": 37}, "end": {"line": 512, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 28, "column": 4}, "end": {"line": 30, "column": 5}}, "type": "if", "locations": [{"start": {"line": 28, "column": 4}, "end": {"line": 30, "column": 5}}]}, "1": {"loc": {"start": {"line": 139, "column": 6}, "end": {"line": 143, "column": 7}}, "type": "if", "locations": [{"start": {"line": 139, "column": 6}, "end": {"line": 143, "column": 7}}]}, "2": {"loc": {"start": {"line": 183, "column": 6}, "end": {"line": 204, "column": 7}}, "type": "if", "locations": [{"start": {"line": 183, "column": 6}, "end": {"line": 204, "column": 7}}]}, "3": {"loc": {"start": {"line": 185, "column": 8}, "end": {"line": 191, "column": 9}}, "type": "if", "locations": [{"start": {"line": 185, "column": 8}, "end": {"line": 191, "column": 9}}]}, "4": {"loc": {"start": {"line": 194, "column": 8}, "end": {"line": 203, "column": 9}}, "type": "if", "locations": [{"start": {"line": 194, "column": 8}, "end": {"line": 203, "column": 9}}]}, "5": {"loc": {"start": {"line": 250, "column": 6}, "end": {"line": 279, "column": 7}}, "type": "if", "locations": [{"start": {"line": 250, "column": 6}, "end": {"line": 279, "column": 7}}]}, "6": {"loc": {"start": {"line": 261, "column": 8}, "end": {"line": 264, "column": 9}}, "type": "if", "locations": [{"start": {"line": 261, "column": 8}, "end": {"line": 264, "column": 9}}]}, "7": {"loc": {"start": {"line": 267, "column": 8}, "end": {"line": 278, "column": 9}}, "type": "if", "locations": [{"start": {"line": 267, "column": 8}, "end": {"line": 278, "column": 9}}]}, "8": {"loc": {"start": {"line": 313, "column": 74}, "end": {"line": 313, "column": 92}}, "type": "binary-expr", "locations": [{"start": {"line": 313, "column": 74}, "end": {"line": 313, "column": 86}}, {"start": {"line": 313, "column": 90}, "end": {"line": 313, "column": 92}}]}, "9": {"loc": {"start": {"line": 313, "column": 96}, "end": {"line": 313, "column": 110}}, "type": "binary-expr", "locations": [{"start": {"line": 313, "column": 96}, "end": {"line": 313, "column": 104}}, {"start": {"line": 313, "column": 108}, "end": {"line": 313, "column": 110}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0], "6": [0], "7": [0], "8": [0, 0], "9": [0, 0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/test-utils/mock-data.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/test-utils/mock-data.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 68}}, "1": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": null}}, "2": {"start": {"line": 16, "column": 13}, "end": {"line": 37, "column": 2}}, "3": {"start": {"line": 42, "column": 13}, "end": {"line": 61, "column": 2}}, "4": {"start": {"line": 66, "column": 13}, "end": {"line": 91, "column": 2}}, "5": {"start": {"line": 96, "column": 13}, "end": {"line": 110, "column": 2}}, "6": {"start": {"line": 115, "column": 13}, "end": {"line": 120, "column": 2}}, "7": {"start": {"line": 122, "column": 13}, "end": {"line": 125, "column": 2}}, "8": {"start": {"line": 127, "column": 13}, "end": {"line": 130, "column": 2}}, "9": {"start": {"line": 132, "column": 13}, "end": {"line": 136, "column": 2}}, "10": {"start": {"line": 138, "column": 13}, "end": {"line": 141, "column": 2}}, "11": {"start": {"line": 143, "column": 13}, "end": {"line": 149, "column": 2}}, "12": {"start": {"line": 151, "column": 13}, "end": {"line": 156, "column": 2}}, "13": {"start": {"line": 161, "column": 13}, "end": {"line": 170, "column": 2}}, "14": {"start": {"line": 172, "column": 13}, "end": {"line": 178, "column": 2}}, "15": {"start": {"line": 180, "column": 13}, "end": {"line": 225, "column": 2}}, "16": {"start": {"line": 232, "column": 4}, "end": {"line": 241, "column": 6}}, "17": {"start": {"line": 245, "column": 4}, "end": {"line": 254, "column": 6}}, "18": {"start": {"line": 258, "column": 4}, "end": {"line": 266, "column": 6}}, "19": {"start": {"line": 230, "column": 0}, "end": {"line": 230, "column": 13}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 231, "column": 2}, "end": {"line": 231, "column": 8}}, "loc": {"start": {"line": 231, "column": 49}, "end": {"line": 242, "column": 3}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 244, "column": 2}, "end": {"line": 244, "column": 8}}, "loc": {"start": {"line": 244, "column": 55}, "end": {"line": 255, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 257, "column": 2}, "end": {"line": 257, "column": 8}}, "loc": {"start": {"line": 257, "column": 57}, "end": {"line": 267, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 231, "column": 20}, "end": {"line": 231, "column": 49}}, "type": "default-arg", "locations": [{"start": {"line": 231, "column": 47}, "end": {"line": 231, "column": 49}}]}, "1": {"loc": {"start": {"line": 244, "column": 23}, "end": {"line": 244, "column": 55}}, "type": "default-arg", "locations": [{"start": {"line": 244, "column": 53}, "end": {"line": 244, "column": 55}}]}, "2": {"loc": {"start": {"line": 257, "column": 24}, "end": {"line": 257, "column": 57}}, "type": "default-arg", "locations": [{"start": {"line": 257, "column": 55}, "end": {"line": 257, "column": 57}}]}}, "s": {"0": 6, "1": 6, "2": 6, "3": 6, "4": 6, "5": 6, "6": 6, "7": 6, "8": 6, "9": 6, "10": 6, "11": 6, "12": 6, "13": 6, "14": 6, "15": 6, "16": 1, "17": 1, "18": 0, "19": 6}, "f": {"0": 1, "1": 1, "2": 0}, "b": {"0": [1], "1": [1], "2": [0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/test-utils/test-helpers.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/test-utils/test-helpers.ts", "statementMap": {"0": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 7}}, "1": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 7}}, "2": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 16}}, "3": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 16}}, "4": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 16}}, "5": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 16}}, "6": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 16}}, "7": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 54}}, "8": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 48}}, "9": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 46}}, "10": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 40}}, "11": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 50}}, "12": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 52}}, "13": {"start": {"line": 12, "column": 37}, "end": {"line": 18, "column": 2}}, "14": {"start": {"line": 12, "column": 44}, "end": {"line": 18, "column": 2}}, "15": {"start": {"line": 12, "column": 13}, "end": {"line": 12, "column": 37}}, "16": {"start": {"line": 23, "column": 35}, "end": {"line": 39, "column": 2}}, "17": {"start": {"line": 23, "column": 42}, "end": {"line": 39, "column": 2}}, "18": {"start": {"line": 23, "column": 13}, "end": {"line": 23, "column": 35}}, "19": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 60}}, "20": {"start": {"line": 50, "column": 14}, "end": {"line": 50, "column": 51}}, "21": {"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": 32}}, "22": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": 19}}, "23": {"start": {"line": 54, "column": 2}, "end": {"line": 54, "column": 13}}, "24": {"start": {"line": 61, "column": 2}, "end": {"line": 63, "column": 3}}, "25": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": 22}}, "26": {"start": {"line": 72, "column": 14}, "end": {"line": 72, "column": 37}}, "27": {"start": {"line": 73, "column": 2}, "end": {"line": 73, "column": 63}}, "28": {"start": {"line": 80, "column": 20}, "end": {"line": 80, "column": 48}}, "29": {"start": {"line": 81, "column": 2}, "end": {"line": 83, "column": 4}}, "30": {"start": {"line": 90, "column": 2}, "end": {"line": 90, "column": 59}}, "31": {"start": {"line": 90, "column": 34}, "end": {"line": 90, "column": 57}}, "32": {"start": {"line": 98, "column": 4}, "end": {"line": 98, "column": 68}}, "33": {"start": {"line": 99, "column": 15}, "end": {"line": 99, "column": 17}}, "34": {"start": {"line": 100, "column": 2}, "end": {"line": 102, "column": 3}}, "35": {"start": {"line": 100, "column": 15}, "end": {"line": 100, "column": 16}}, "36": {"start": {"line": 101, "column": 4}, "end": {"line": 101, "column": 69}}, "37": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 16}}, "38": {"start": {"line": 110, "column": 2}, "end": {"line": 110, "column": 54}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 12, "column": 37}, "end": {"line": 12, "column": 40}}, "loc": {"start": {"line": 12, "column": 44}, "end": {"line": 18, "column": 2}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 23, "column": 35}, "end": {"line": 23, "column": 38}}, "loc": {"start": {"line": 23, "column": 42}, "end": {"line": 39, "column": 2}}}, "2": {"name": "createTestApp", "decl": {"start": {"line": 44, "column": 22}, "end": {"line": 44, "column": 35}}, "loc": {"start": {"line": 45, "column": 21}, "end": {"line": 55, "column": 1}}}, "3": {"name": "cleanupTestApp", "decl": {"start": {"line": 60, "column": 22}, "end": {"line": 60, "column": 36}}, "loc": {"start": {"line": 60, "column": 58}, "end": {"line": 64, "column": 1}}}, "4": {"name": "generateTestToken", "decl": {"start": {"line": 69, "column": 16}, "end": {"line": 69, "column": 33}}, "loc": {"start": {"line": 70, "column": 48}, "end": {"line": 74, "column": 1}}}, "5": {"name": "createAuthHeader", "decl": {"start": {"line": 79, "column": 16}, "end": {"line": 79, "column": 32}}, "loc": {"start": {"line": 79, "column": 47}, "end": {"line": 84, "column": 1}}}, "6": {"name": "delay", "decl": {"start": {"line": 89, "column": 16}, "end": {"line": 89, "column": 21}}, "loc": {"start": {"line": 89, "column": 32}, "end": {"line": 91, "column": 1}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 90, "column": 21}, "end": {"line": 90, "column": 22}}, "loc": {"start": {"line": 90, "column": 34}, "end": {"line": 90, "column": 57}}}, "8": {"name": "generateRandomString", "decl": {"start": {"line": 96, "column": 16}, "end": {"line": 96, "column": 36}}, "loc": {"start": {"line": 96, "column": 56}, "end": {"line": 104, "column": 1}}}, "9": {"name": "generateRandomEmail", "decl": {"start": {"line": 109, "column": 16}, "end": {"line": 109, "column": 35}}, "loc": {"start": {"line": 109, "column": 35}, "end": {"line": 111, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 61, "column": 2}, "end": {"line": 63, "column": 3}}, "type": "if", "locations": [{"start": {"line": 61, "column": 2}, "end": {"line": 63, "column": 3}}]}, "1": {"loc": {"start": {"line": 70, "column": 2}, "end": {"line": 70, "column": 48}}, "type": "default-arg", "locations": [{"start": {"line": 70, "column": 17}, "end": {"line": 70, "column": 48}}]}, "2": {"loc": {"start": {"line": 80, "column": 20}, "end": {"line": 80, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 80, "column": 20}, "end": {"line": 80, "column": 25}}, {"start": {"line": 80, "column": 29}, "end": {"line": 80, "column": 48}}]}, "3": {"loc": {"start": {"line": 96, "column": 37}, "end": {"line": 96, "column": 56}}, "type": "default-arg", "locations": [{"start": {"line": 96, "column": 54}, "end": {"line": 96, "column": 56}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/users/users.controller.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/users/users.controller.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": null}}, "2": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 47}}, "3": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 53}}, "4": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 61}}, "5": {"start": {"line": 24, "column": 7}, "end": {"line": 82, "column": null}}, "6": {"start": {"line": 25, "column": 31}, "end": {"line": 25, "column": 45}}, "7": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 51}}, "8": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 39}}, "9": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 50}}, "10": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 42}}, "11": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 56}}, "12": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 41}}, "13": {"start": {"line": 24, "column": 13}, "end": {"line": 24, "column": 28}}, "14": {"start": {"line": 31, "column": 2}, "end": {"line": 33, "column": null}}, "15": {"start": {"line": 40, "column": 2}, "end": {"line": 42, "column": null}}, "16": {"start": {"line": 49, "column": 2}, "end": {"line": 51, "column": null}}, "17": {"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": null}}, "18": {"start": {"line": 69, "column": 2}, "end": {"line": 71, "column": null}}, "19": {"start": {"line": 79, "column": 2}, "end": {"line": 81, "column": null}}, "20": {"start": {"line": 24, "column": 13}, "end": {"line": 82, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 31}}, "loc": {"start": {"line": 25, "column": 57}, "end": {"line": 25, "column": 61}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": 8}}, "loc": {"start": {"line": 31, "column": 45}, "end": {"line": 33, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": 9}}, "loc": {"start": {"line": 40, "column": 9}, "end": {"line": 42, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 49, "column": 2}, "end": {"line": 49, "column": 12}}, "loc": {"start": {"line": 49, "column": 27}, "end": {"line": 51, "column": 3}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": 9}}, "loc": {"start": {"line": 59, "column": 33}, "end": {"line": 61, "column": 3}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 8}}, "loc": {"start": {"line": 69, "column": 70}, "end": {"line": 71, "column": 3}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 79, "column": 2}, "end": {"line": 79, "column": 8}}, "loc": {"start": {"line": 79, "column": 32}, "end": {"line": 81, "column": 3}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 11, "7": 2, "8": 1, "9": 1, "10": 2, "11": 2, "12": 2, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1}, "f": {"0": 11, "1": 2, "2": 1, "3": 1, "4": 2, "5": 2, "6": 2}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/users/users.module.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/users/users.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 48}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 47}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 53}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 47}}, "5": {"start": {"line": 13, "column": 7}, "end": {"line": 13, "column": null}}, "6": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": 24}}, "7": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/users/users.service.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/users/users.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 51}}, "2": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 37}}, "3": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 35}}, "4": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 47}}, "5": {"start": {"line": 13, "column": 7}, "end": {"line": 115, "column": null}}, "6": {"start": {"line": 16, "column": 21}, "end": {"line": 16, "column": 37}}, "7": {"start": {"line": 20, "column": 52}, "end": {"line": 20, "column": 65}}, "8": {"start": {"line": 23, "column": 25}, "end": {"line": 25, "column": 6}}, "9": {"start": {"line": 27, "column": 4}, "end": {"line": 34, "column": 5}}, "10": {"start": {"line": 28, "column": 6}, "end": {"line": 30, "column": 7}}, "11": {"start": {"line": 29, "column": 8}, "end": {"line": 29, "column": 46}}, "12": {"start": {"line": 31, "column": 6}, "end": {"line": 33, "column": 7}}, "13": {"start": {"line": 32, "column": 8}, "end": {"line": 32, "column": 45}}, "14": {"start": {"line": 37, "column": 23}, "end": {"line": 37, "column": 25}}, "15": {"start": {"line": 38, "column": 25}, "end": {"line": 38, "column": 64}}, "16": {"start": {"line": 40, "column": 17}, "end": {"line": 45, "column": 6}}, "17": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 42}}, "18": {"start": {"line": 51, "column": 4}, "end": {"line": 62, "column": 7}}, "19": {"start": {"line": 66, "column": 17}, "end": {"line": 78, "column": 6}}, "20": {"start": {"line": 80, "column": 4}, "end": {"line": 82, "column": 5}}, "21": {"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": 43}}, "22": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 16}}, "23": {"start": {"line": 88, "column": 4}, "end": {"line": 90, "column": 7}}, "24": {"start": {"line": 94, "column": 4}, "end": {"line": 96, "column": 7}}, "25": {"start": {"line": 100, "column": 17}, "end": {"line": 100, "column": 39}}, "26": {"start": {"line": 102, "column": 4}, "end": {"line": 102, "column": 39}}, "27": {"start": {"line": 104, "column": 4}, "end": {"line": 104, "column": 42}}, "28": {"start": {"line": 108, "column": 17}, "end": {"line": 108, "column": 39}}, "29": {"start": {"line": 109, "column": 4}, "end": {"line": 109, "column": 43}}, "30": {"start": {"line": 113, "column": 4}, "end": {"line": 113, "column": 55}}, "31": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": 25}}, "32": {"start": {"line": 13, "column": 13}, "end": {"line": 115, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": null}}, "loc": {"start": {"line": 16, "column": 53}, "end": {"line": 17, "column": 6}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 7}}, "loc": {"start": {"line": 19, "column": 43}, "end": {"line": 48, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": 7}}, "loc": {"start": {"line": 50, "column": 15}, "end": {"line": 63, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": 7}}, "loc": {"start": {"line": 65, "column": 26}, "end": {"line": 85, "column": 3}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 87, "column": 2}, "end": {"line": 87, "column": 7}}, "loc": {"start": {"line": 87, "column": 39}, "end": {"line": 91, "column": 3}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 93, "column": 2}, "end": {"line": 93, "column": 7}}, "loc": {"start": {"line": 93, "column": 33}, "end": {"line": 97, "column": 3}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 99, "column": 2}, "end": {"line": 99, "column": 7}}, "loc": {"start": {"line": 99, "column": 55}, "end": {"line": 105, "column": 3}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 107, "column": 2}, "end": {"line": 107, "column": 7}}, "loc": {"start": {"line": 107, "column": 25}, "end": {"line": 110, "column": 3}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 112, "column": 2}, "end": {"line": 112, "column": 7}}, "loc": {"start": {"line": 112, "column": 53}, "end": {"line": 114, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 27, "column": 4}, "end": {"line": 34, "column": 5}}, "type": "if", "locations": [{"start": {"line": 27, "column": 4}, "end": {"line": 34, "column": 5}}]}, "1": {"loc": {"start": {"line": 28, "column": 6}, "end": {"line": 30, "column": 7}}, "type": "if", "locations": [{"start": {"line": 28, "column": 6}, "end": {"line": 30, "column": 7}}]}, "2": {"loc": {"start": {"line": 31, "column": 6}, "end": {"line": 33, "column": 7}}, "type": "if", "locations": [{"start": {"line": 31, "column": 6}, "end": {"line": 33, "column": 7}}]}, "3": {"loc": {"start": {"line": 80, "column": 4}, "end": {"line": 82, "column": 5}}, "type": "if", "locations": [{"start": {"line": 80, "column": 4}, "end": {"line": 82, "column": 5}}]}}, "s": {"0": 4, "1": 4, "2": 4, "3": 4, "4": 4, "5": 4, "6": 13, "7": 3, "8": 3, "9": 3, "10": 2, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 6, "20": 6, "21": 3, "22": 3, "23": 1, "24": 1, "25": 2, "26": 1, "27": 1, "28": 2, "29": 1, "30": 0, "31": 4, "32": 4}, "f": {"0": 13, "1": 3, "2": 1, "3": 6, "4": 1, "5": 1, "6": 2, "7": 2, "8": 0}, "b": {"0": [2], "1": [1], "2": [1], "3": [3]}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/users/dto/create-user.dto.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/users/dto/create-user.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 46}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": null}}, "2": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 13}}, "3": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": null}}, "4": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": null}}, "5": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": null}}, "6": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1}, "f": {}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/users/dto/index.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/users/dto/index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 9}}, "1": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 50}}, "2": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 9}}, "3": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 50}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 22}}, "loc": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 50}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 22}}, "loc": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 50}}}}, "branchMap": {}, "s": {"0": 1, "1": 2, "2": 1, "3": 2}, "f": {"0": 1, "1": 1}, "b": {}}, "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/users/dto/update-user.dto.ts": {"path": "/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/users/dto/update-user.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 46}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 66}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 13}}, "3": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": null}}, "4": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": null}}, "5": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1}, "f": {}, "b": {}}}