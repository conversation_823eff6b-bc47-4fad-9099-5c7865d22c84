<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1750777138982" clover="3.2.0">
  <project timestamp="1750777138982" name="All files">
    <metrics statements="2264" coveredstatements="711" conditionals="502" coveredconditionals="65" methods="485" coveredmethods="123" elements="3251" coveredelements="899" complexity="0" loc="2264" ncloc="2264" packages="17" files="62" classes="62"/>
    <package name="src">
      <metrics statements="41" coveredstatements="9" conditionals="4" coveredconditionals="0" methods="6" coveredmethods="3"/>
      <file name="app.controller.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/app.controller.ts">
        <metrics statements="6" coveredstatements="6" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
      </file>
      <file name="app.module.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/app.module.ts">
        <metrics statements="15" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
      </file>
      <file name="app.service.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/app.service.ts">
        <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
      </file>
      <file name="main.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/main.ts">
        <metrics statements="17" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.analysis">
      <metrics statements="774" coveredstatements="277" conditionals="200" coveredconditionals="35" methods="169" coveredmethods="64"/>
      <file name="analysis.controller.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/analysis.controller.ts">
        <metrics statements="106" coveredstatements="57" conditionals="23" coveredconditionals="5" methods="26" coveredmethods="8"/>
        <line num="1" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="42" count="10" type="stmt"/>
        <line num="43" count="10" type="stmt"/>
        <line num="44" count="10" type="stmt"/>
        <line num="45" count="10" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="86" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="2" type="stmt"/>
        <line num="92" count="2" type="stmt"/>
        <line num="93" count="2" type="stmt"/>
        <line num="99" count="2" type="stmt"/>
        <line num="104" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="159" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
        <line num="173" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="174" count="0" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="179" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="186" count="1" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="201" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="202" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="223" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="254" count="1" type="stmt"/>
        <line num="258" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="259" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="275" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="284" count="1" type="stmt"/>
        <line num="285" count="1" type="stmt"/>
        <line num="297" count="1" type="stmt"/>
        <line num="301" count="1" type="stmt"/>
        <line num="310" count="1" type="stmt"/>
        <line num="311" count="1" type="stmt"/>
        <line num="317" count="1" type="stmt"/>
        <line num="318" count="1" type="stmt"/>
        <line num="324" count="1" type="stmt"/>
        <line num="328" count="2" type="stmt"/>
        <line num="334" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="340" count="1" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="347" count="1" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="355" count="1" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="366" count="1" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="374" count="1" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="387" count="1" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="404" count="1" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="408" count="0" type="stmt"/>
        <line num="413" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="414" count="0" type="stmt"/>
      </file>
      <file name="analysis.module.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/analysis.module.ts">
        <metrics statements="11" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
      </file>
      <file name="cache.service.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/cache.service.ts">
        <metrics statements="69" coveredstatements="4" conditionals="14" coveredconditionals="0" methods="13" coveredmethods="0"/>
        <line num="1" count="3" type="stmt"/>
        <line num="2" count="3" type="stmt"/>
        <line num="4" count="3" type="stmt"/>
        <line num="24" count="3" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="190" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="225" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
      </file>
      <file name="data-processing.service.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/data-processing.service.ts">
        <metrics statements="107" coveredstatements="9" conditionals="43" coveredconditionals="0" methods="15" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="35" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="78" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="137" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="141" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="166" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="173" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="219" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="220" count="0" type="stmt"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="224" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="239" count="0" type="stmt"/>
        <line num="243" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="244" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="299" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="300" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
      </file>
      <file name="data-stream.service.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/data-stream.service.ts">
        <metrics statements="94" coveredstatements="7" conditionals="35" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="71" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="86" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="117" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="159" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="165" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="170" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="195" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="196" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="200" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="215" count="0" type="stmt"/>
        <line num="219" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="220" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="241" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="243" count="0" type="stmt"/>
      </file>
      <file name="process-mining.service.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/process-mining.service.ts">
        <metrics statements="214" coveredstatements="28" conditionals="56" coveredconditionals="5" methods="54" coveredmethods="6"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="4" count="2" type="stmt"/>
        <line num="5" count="2" type="stmt"/>
        <line num="10" count="2" type="stmt"/>
        <line num="11" count="2" type="stmt"/>
        <line num="81" count="2" type="stmt"/>
        <line num="82" count="9" type="stmt"/>
        <line num="86" count="9" type="stmt"/>
        <line num="88" count="9" type="stmt"/>
        <line num="89" count="9" type="stmt"/>
        <line num="96" count="3" type="stmt"/>
        <line num="101" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="102" count="2" type="stmt"/>
        <line num="106" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="107" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="115" count="2" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="120" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="126" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="220" count="2" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="226" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="248" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="292" count="1" type="stmt"/>
        <line num="296" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="297" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="301" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="302" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="334" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="335" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="346" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="348" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="353" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="354" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
        <line num="369" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="370" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="389" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="410" count="0" type="stmt"/>
        <line num="414" count="0" type="stmt"/>
        <line num="416" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="417" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="431" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="432" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
        <line num="444" count="0" type="stmt"/>
        <line num="445" count="0" type="stmt"/>
        <line num="446" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="450" count="0" type="stmt"/>
        <line num="467" count="0" type="stmt"/>
        <line num="470" count="0" type="stmt"/>
        <line num="471" count="0" type="stmt"/>
        <line num="472" count="0" type="stmt"/>
        <line num="474" count="0" type="stmt"/>
        <line num="476" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="477" count="0" type="stmt"/>
        <line num="479" count="0" type="stmt"/>
        <line num="492" count="0" type="stmt"/>
        <line num="494" count="0" type="stmt"/>
        <line num="495" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="497" count="0" type="stmt"/>
        <line num="498" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="501" count="0" type="stmt"/>
        <line num="510" count="0" type="stmt"/>
        <line num="522" count="0" type="stmt"/>
        <line num="525" count="0" type="stmt"/>
        <line num="526" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="529" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="530" count="0" type="stmt"/>
        <line num="534" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="536" count="0" type="stmt"/>
        <line num="537" count="0" type="stmt"/>
        <line num="540" count="0" type="stmt"/>
        <line num="549" count="0" type="stmt"/>
        <line num="551" count="0" type="stmt"/>
        <line num="552" count="0" type="stmt"/>
        <line num="553" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="555" count="0" type="stmt"/>
        <line num="556" count="0" type="stmt"/>
        <line num="557" count="0" type="stmt"/>
        <line num="559" count="0" type="stmt"/>
        <line num="565" count="0" type="stmt"/>
        <line num="573" count="0" type="stmt"/>
        <line num="577" count="0" type="stmt"/>
        <line num="578" count="0" type="stmt"/>
        <line num="579" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="580" count="0" type="stmt"/>
        <line num="583" count="0" type="stmt"/>
        <line num="587" count="0" type="stmt"/>
        <line num="588" count="0" type="stmt"/>
        <line num="589" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="590" count="0" type="stmt"/>
        <line num="593" count="0" type="stmt"/>
        <line num="599" count="0" type="stmt"/>
        <line num="600" count="0" type="stmt"/>
        <line num="605" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="606" count="0" type="stmt"/>
        <line num="607" count="0" type="stmt"/>
        <line num="608" count="0" type="stmt"/>
        <line num="616" count="0" type="stmt"/>
        <line num="617" count="0" type="stmt"/>
        <line num="618" count="0" type="stmt"/>
        <line num="619" count="0" type="stmt"/>
        <line num="621" count="0" type="stmt"/>
        <line num="635" count="0" type="stmt"/>
        <line num="638" count="0" type="stmt"/>
        <line num="639" count="0" type="stmt"/>
        <line num="640" count="0" type="stmt"/>
        <line num="641" count="0" type="stmt"/>
        <line num="642" count="0" type="stmt"/>
        <line num="643" count="0" type="stmt"/>
        <line num="644" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="645" count="0" type="stmt"/>
        <line num="646" count="0" type="stmt"/>
        <line num="647" count="0" type="stmt"/>
        <line num="651" count="0" type="stmt"/>
        <line num="654" count="0" type="stmt"/>
        <line num="655" count="0" type="stmt"/>
        <line num="663" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="664" count="0" type="stmt"/>
        <line num="667" count="0" type="stmt"/>
        <line num="668" count="0" type="stmt"/>
        <line num="675" count="3" type="stmt"/>
        <line num="689" count="2" type="stmt"/>
        <line num="693" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="694" count="1" type="stmt"/>
        <line num="698" count="1" type="stmt"/>
        <line num="699" count="1" type="cond" truecount="1" falsecount="2"/>
        <line num="701" count="0" type="stmt"/>
        <line num="702" count="0" type="stmt"/>
        <line num="705" count="1" type="stmt"/>
        <line num="712" count="0" type="stmt"/>
        <line num="713" count="0" type="stmt"/>
        <line num="715" count="0" type="stmt"/>
        <line num="725" count="0" type="stmt"/>
        <line num="735" count="0" type="stmt"/>
        <line num="739" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="subprocess-discovery.service.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/subprocess-discovery.service.ts">
        <metrics statements="173" coveredstatements="172" conditionals="29" coveredconditionals="25" methods="50" coveredmethods="50"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="4" count="2" type="stmt"/>
        <line num="5" count="2" type="stmt"/>
        <line num="6" count="2" type="stmt"/>
        <line num="62" count="2" type="stmt"/>
        <line num="63" count="5" type="stmt"/>
        <line num="67" count="5" type="stmt"/>
        <line num="69" count="5" type="stmt"/>
        <line num="70" count="5" type="stmt"/>
        <line num="80" count="4" type="stmt"/>
        <line num="82" count="4" type="stmt"/>
        <line num="95" count="4" type="stmt"/>
        <line num="100" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="101" count="1" type="stmt"/>
        <line num="105" count="3" type="stmt"/>
        <line num="108" count="3" type="stmt"/>
        <line num="114" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="119" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="124" count="3" type="stmt"/>
        <line num="130" count="3" type="stmt"/>
        <line num="138" count="3" type="stmt"/>
        <line num="145" count="3" type="stmt"/>
        <line num="151" count="3" type="stmt"/>
        <line num="158" count="3" type="stmt"/>
        <line num="164" count="3" type="stmt"/>
        <line num="168" count="3" type="stmt"/>
        <line num="178" count="3" type="stmt"/>
        <line num="185" count="3" type="stmt"/>
        <line num="186" count="150" type="stmt"/>
        <line num="188" count="9" type="stmt"/>
        <line num="189" count="36" type="stmt"/>
        <line num="190" count="510" type="stmt"/>
        <line num="191" count="510" type="stmt"/>
        <line num="193" count="510" type="cond" truecount="1" falsecount="0"/>
        <line num="194" count="270" type="stmt"/>
        <line num="201" count="510" type="stmt"/>
        <line num="202" count="510" type="cond" truecount="1" falsecount="0"/>
        <line num="203" count="510" type="stmt"/>
        <line num="206" count="510" type="stmt"/>
        <line num="207" count="510" type="stmt"/>
        <line num="208" count="510" type="stmt"/>
        <line num="215" count="3" type="stmt"/>
        <line num="216" count="270" type="stmt"/>
        <line num="217" count="140" type="stmt"/>
        <line num="222" count="300" type="stmt"/>
        <line num="227" count="140" type="stmt"/>
        <line num="228" count="235" type="stmt"/>
        <line num="238" count="3" type="stmt"/>
        <line num="241" count="3" type="stmt"/>
        <line num="243" count="9" type="stmt"/>
        <line num="244" count="141" type="stmt"/>
        <line num="245" count="1200" type="stmt"/>
        <line num="246" count="1200" type="stmt"/>
        <line num="249" count="1200" type="stmt"/>
        <line num="250" count="1200" type="cond" truecount="1" falsecount="0"/>
        <line num="257" count="3" type="stmt"/>
        <line num="267" count="3" type="stmt"/>
        <line num="269" count="3" type="stmt"/>
        <line num="270" count="150" type="stmt"/>
        <line num="273" count="9" type="stmt"/>
        <line num="274" count="36" type="stmt"/>
        <line num="275" count="384" type="stmt"/>
        <line num="276" count="384" type="stmt"/>
        <line num="278" count="384" type="cond" truecount="0" falsecount="1"/>
        <line num="280" count="0" type="stmt"/>
        <line num="287" count="3" type="stmt"/>
        <line num="297" count="3" type="stmt"/>
        <line num="300" count="3" type="stmt"/>
        <line num="301" count="3" type="stmt"/>
        <line num="304" count="3" type="stmt"/>
        <line num="305" count="9" type="stmt"/>
        <line num="307" count="27" type="stmt"/>
        <line num="308" count="27" type="cond" truecount="1" falsecount="0"/>
        <line num="314" count="3" type="stmt"/>
        <line num="325" count="3" type="stmt"/>
        <line num="326" count="3" type="stmt"/>
        <line num="329" count="3" type="stmt"/>
        <line num="330" count="80" type="stmt"/>
        <line num="341" count="3" type="stmt"/>
        <line num="342" count="80" type="stmt"/>
        <line num="345" count="150" type="stmt"/>
        <line num="346" count="3" type="stmt"/>
        <line num="347" count="57" type="stmt"/>
        <line num="350" count="3" type="stmt"/>
        <line num="351" count="1350" type="stmt"/>
        <line num="352" count="27" type="stmt"/>
        <line num="363" count="3" type="stmt"/>
        <line num="365" count="3" type="stmt"/>
        <line num="366" count="9" type="stmt"/>
        <line num="368" count="9" type="stmt"/>
        <line num="369" count="99" type="stmt"/>
        <line num="370" count="99" type="stmt"/>
        <line num="371" count="99" type="stmt"/>
        <line num="373" count="99" type="cond" truecount="1" falsecount="0"/>
        <line num="374" count="49" type="stmt"/>
        <line num="377" count="99" type="stmt"/>
        <line num="378" count="99" type="stmt"/>
        <line num="380" count="99" type="stmt"/>
        <line num="382" count="99" type="stmt"/>
        <line num="387" count="3" type="stmt"/>
        <line num="388" count="49" type="stmt"/>
        <line num="389" count="99" type="stmt"/>
        <line num="391" count="49" type="stmt"/>
        <line num="399" count="3" type="stmt"/>
        <line num="409" count="9" type="stmt"/>
        <line num="410" count="9" type="stmt"/>
        <line num="412" count="9" type="stmt"/>
        <line num="413" count="108" type="stmt"/>
        <line num="416" count="108" type="stmt"/>
        <line num="417" count="892" type="cond" truecount="1" falsecount="0"/>
        <line num="418" count="42" type="stmt"/>
        <line num="422" count="42" type="stmt"/>
        <line num="423" count="42" type="stmt"/>
        <line num="424" count="42" type="stmt"/>
        <line num="429" count="108" type="cond" truecount="1" falsecount="0"/>
        <line num="430" count="66" type="stmt"/>
        <line num="434" count="66" type="stmt"/>
        <line num="438" count="9" type="stmt"/>
        <line num="449" count="892" type="cond" truecount="1" falsecount="0"/>
        <line num="450" count="240" type="stmt"/>
        <line num="453" count="652" type="stmt"/>
        <line num="454" count="694" type="cond" truecount="1" falsecount="0"/>
        <line num="455" count="610" type="stmt"/>
        <line num="459" count="42" type="stmt"/>
        <line num="470" count="150" type="stmt"/>
        <line num="471" count="3" type="stmt"/>
        <line num="473" count="3" type="stmt"/>
        <line num="476" count="80" type="stmt"/>
        <line num="490" count="140" type="stmt"/>
        <line num="497" count="3" type="stmt"/>
        <line num="499" count="3" type="stmt"/>
        <line num="500" count="9" type="stmt"/>
        <line num="501" count="141" type="stmt"/>
        <line num="502" count="141" type="stmt"/>
        <line num="504" count="141" type="cond" truecount="1" falsecount="0"/>
        <line num="505" count="54" type="stmt"/>
        <line num="507" count="141" type="stmt"/>
        <line num="511" count="3" type="stmt"/>
        <line num="512" count="54" type="stmt"/>
        <line num="513" count="9" type="stmt"/>
        <line num="520" count="3" type="stmt"/>
        <line num="522" count="3" type="stmt"/>
        <line num="523" count="9" type="stmt"/>
        <line num="524" count="141" type="stmt"/>
        <line num="525" count="141" type="stmt"/>
        <line num="527" count="141" type="cond" truecount="1" falsecount="0"/>
        <line num="528" count="54" type="stmt"/>
        <line num="530" count="141" type="stmt"/>
        <line num="534" count="3" type="stmt"/>
        <line num="535" count="54" type="stmt"/>
        <line num="536" count="9" type="stmt"/>
        <line num="547" count="27" type="stmt"/>
        <line num="549" count="27" type="stmt"/>
        <line num="550" count="1350" type="stmt"/>
        <line num="551" count="81" type="stmt"/>
        <line num="552" count="81" type="stmt"/>
        <line num="554" count="81" type="cond" truecount="4" falsecount="0"/>
        <line num="555" count="42" type="stmt"/>
        <line num="556" count="42" type="stmt"/>
        <line num="560" count="27" type="stmt"/>
        <line num="567" count="384" type="cond" truecount="2" falsecount="0"/>
        <line num="574" count="3" type="stmt"/>
        <line num="576" count="150" type="cond" truecount="1" falsecount="0"/>
        <line num="577" count="9" type="stmt"/>
        <line num="579" count="150" type="stmt"/>
        <line num="580" count="150" type="stmt"/>
        <line num="594" count="3" type="stmt"/>
        <line num="595" count="3" type="stmt"/>
        <line num="596" count="3" type="stmt"/>
        <line num="597" count="3" type="stmt"/>
        <line num="599" count="3" type="stmt"/>
      </file>
    </package>
    <package name="src.analysis.dto">
      <metrics statements="24" coveredstatements="23" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="2"/>
      <file name="index.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/dto/index.ts">
        <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="1" count="4" type="stmt"/>
        <line num="2" count="3" type="stmt"/>
      </file>
      <file name="subprocess-discovery.dto.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/dto/subprocess-discovery.dto.ts">
        <metrics statements="11" coveredstatements="11" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
      </file>
      <file name="upload-data.dto.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/analysis/dto/upload-data.dto.ts">
        <metrics statements="11" coveredstatements="10" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.auth">
      <metrics statements="46" coveredstatements="35" conditionals="4" coveredconditionals="3" methods="9" coveredmethods="8"/>
      <file name="auth.controller.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/auth.controller.ts">
        <metrics statements="13" coveredstatements="13" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="4"/>
        <line num="1" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="7" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="2" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="2" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="2" type="stmt"/>
      </file>
      <file name="auth.module.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/auth.module.ts">
        <metrics statements="10" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
      </file>
      <file name="auth.service.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/auth.service.ts">
        <metrics statements="23" coveredstatements="22" conditionals="4" coveredconditionals="3" methods="4" coveredmethods="4"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="8" count="2" type="stmt"/>
        <line num="10" count="10" type="stmt"/>
        <line num="11" count="10" type="stmt"/>
        <line num="15" count="3" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="36" count="4" type="stmt"/>
        <line num="39" count="4" type="stmt"/>
        <line num="40" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="41" count="2" type="stmt"/>
        <line num="44" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="45" count="1" type="stmt"/>
        <line num="48" count="3" type="cond" truecount="0" falsecount="1"/>
        <line num="49" count="0" type="stmt"/>
        <line num="53" count="3" type="stmt"/>
        <line num="57" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="58" count="1" type="stmt"/>
        <line num="61" count="2" type="stmt"/>
        <line num="67" count="2" type="stmt"/>
        <line num="80" count="2" type="stmt"/>
      </file>
    </package>
    <package name="src.auth.dto">
      <metrics statements="14" coveredstatements="14" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
      <file name="index.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/dto/index.ts">
        <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
      </file>
      <file name="login.dto.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/dto/login.dto.ts">
        <metrics statements="5" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
      </file>
      <file name="register.dto.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/dto/register.dto.ts">
        <metrics statements="7" coveredstatements="7" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.auth.guards">
      <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="jwt-auth.guard.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/guards/jwt-auth.guard.ts">
        <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="4" type="stmt"/>
        <line num="2" count="4" type="stmt"/>
        <line num="5" count="4" type="stmt"/>
      </file>
    </package>
    <package name="src.auth.strategies">
      <metrics statements="13" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="jwt.strategy.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/auth/strategies/jwt.strategy.ts">
        <metrics statements="13" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.config">
      <metrics statements="7" coveredstatements="0" conditionals="24" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="database.config.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/config/database.config.ts">
        <metrics statements="3" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
      </file>
      <file name="jwt.config.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/config/jwt.config.ts">
        <metrics statements="2" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
      </file>
      <file name="redis.config.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/config/redis.config.ts">
        <metrics statements="2" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.conformance">
      <metrics statements="658" coveredstatements="66" conditionals="187" coveredconditionals="5" methods="145" coveredmethods="9"/>
      <file name="bpmn-model.service.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/bpmn-model.service.ts">
        <metrics statements="85" coveredstatements="0" conditionals="28" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="36" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="41" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="90" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="105" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="162" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="173" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="182" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="236" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="267" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="286" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
      </file>
      <file name="conformance-algorithm.service.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/conformance-algorithm.service.ts">
        <metrics statements="156" coveredstatements="3" conditionals="50" coveredconditionals="0" methods="38" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="112" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="123" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="143" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="195" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="199" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="244" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="251" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="269" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="327" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="408" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="412" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="415" count="0" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="424" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="427" count="0" type="stmt"/>
        <line num="430" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="462" count="0" type="stmt"/>
        <line num="463" count="0" type="stmt"/>
        <line num="464" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="465" count="0" type="stmt"/>
        <line num="472" count="0" type="stmt"/>
        <line num="473" count="0" type="stmt"/>
        <line num="475" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="476" count="0" type="stmt"/>
        <line num="480" count="0" type="stmt"/>
        <line num="481" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="482" count="0" type="stmt"/>
        <line num="489" count="0" type="stmt"/>
        <line num="505" count="0" type="stmt"/>
        <line num="511" count="0" type="stmt"/>
        <line num="512" count="0" type="stmt"/>
        <line num="513" count="0" type="stmt"/>
        <line num="515" count="0" type="stmt"/>
        <line num="516" count="0" type="stmt"/>
        <line num="520" count="0" type="stmt"/>
        <line num="521" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="522" count="0" type="stmt"/>
        <line num="524" count="0" type="stmt"/>
        <line num="531" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="532" count="0" type="stmt"/>
        <line num="536" count="0" type="stmt"/>
        <line num="537" count="0" type="stmt"/>
        <line num="545" count="0" type="stmt"/>
        <line num="553" count="0" type="stmt"/>
        <line num="562" count="0" type="stmt"/>
        <line num="572" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="582" count="0" type="stmt"/>
        <line num="585" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="586" count="0" type="stmt"/>
        <line num="590" count="0" type="stmt"/>
        <line num="591" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="592" count="0" type="stmt"/>
        <line num="597" count="0" type="stmt"/>
        <line num="604" count="0" type="stmt"/>
        <line num="605" count="0" type="stmt"/>
        <line num="606" count="0" type="stmt"/>
        <line num="608" count="0" type="stmt"/>
        <line num="611" count="0" type="stmt"/>
        <line num="612" count="0" type="stmt"/>
        <line num="614" count="0" type="stmt"/>
        <line num="615" count="0" type="stmt"/>
        <line num="619" count="0" type="stmt"/>
        <line num="620" count="0" type="stmt"/>
        <line num="621" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="622" count="0" type="stmt"/>
        <line num="624" count="0" type="stmt"/>
        <line num="633" count="0" type="stmt"/>
        <line num="643" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="644" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="646" count="0" type="stmt"/>
        <line num="647" count="0" type="stmt"/>
        <line num="649" count="0" type="stmt"/>
      </file>
      <file name="conformance-cache.service.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/conformance-cache.service.ts">
        <metrics statements="162" coveredstatements="8" conditionals="33" coveredconditionals="0" methods="34" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="147" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="180" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="208" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="209" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="243" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="336" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="337" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="344" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="345" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="386" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="387" count="0" type="stmt"/>
        <line num="394" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="409" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="411" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="439" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
        <line num="447" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="448" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
        <line num="455" count="0" type="stmt"/>
        <line num="456" count="0" type="stmt"/>
        <line num="459" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="460" count="0" type="stmt"/>
        <line num="464" count="0" type="stmt"/>
        <line num="466" count="0" type="stmt"/>
        <line num="470" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="472" count="0" type="stmt"/>
        <line num="473" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="475" count="0" type="stmt"/>
        <line num="476" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="478" count="0" type="stmt"/>
        <line num="480" count="0" type="stmt"/>
        <line num="483" count="0" type="stmt"/>
        <line num="486" count="0" type="stmt"/>
        <line num="488" count="0" type="stmt"/>
        <line num="489" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="500" count="0" type="stmt"/>
        <line num="502" count="0" type="stmt"/>
        <line num="503" count="0" type="stmt"/>
        <line num="504" count="0" type="stmt"/>
        <line num="513" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="514" count="0" type="stmt"/>
        <line num="518" count="0" type="stmt"/>
        <line num="519" count="0" type="stmt"/>
        <line num="522" count="0" type="stmt"/>
        <line num="523" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="529" count="0" type="stmt"/>
        <line num="530" count="0" type="stmt"/>
      </file>
      <file name="conformance.controller.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/conformance.controller.ts">
        <metrics statements="162" coveredstatements="0" conditionals="46" coveredconditionals="0" methods="43" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="266" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="267" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="348" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="349" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="380" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="382" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="412" count="0" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="419" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="420" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="434" count="0" type="stmt"/>
        <line num="436" count="0" type="stmt"/>
        <line num="437" count="0" type="stmt"/>
        <line num="440" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
        <line num="442" count="0" type="stmt"/>
        <line num="443" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="447" count="0" type="stmt"/>
        <line num="448" count="0" type="stmt"/>
        <line num="450" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
        <line num="480" count="0" type="stmt"/>
        <line num="484" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="486" count="0" type="stmt"/>
        <line num="487" count="0" type="stmt"/>
        <line num="488" count="0" type="stmt"/>
        <line num="490" count="0" type="stmt"/>
        <line num="492" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="493" count="0" type="stmt"/>
        <line num="503" count="0" type="stmt"/>
        <line num="504" count="0" type="stmt"/>
        <line num="505" count="0" type="stmt"/>
        <line num="506" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="507" count="0" type="stmt"/>
        <line num="509" count="0" type="stmt"/>
        <line num="513" count="0" type="stmt"/>
        <line num="514" count="0" type="stmt"/>
        <line num="517" count="0" type="stmt"/>
        <line num="520" count="0" type="stmt"/>
        <line num="523" count="0" type="stmt"/>
        <line num="524" count="0" type="stmt"/>
        <line num="525" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="526" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="528" count="0" type="stmt"/>
        <line num="530" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="531" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="534" count="0" type="stmt"/>
        <line num="547" count="0" type="stmt"/>
        <line num="548" count="0" type="stmt"/>
        <line num="550" count="0" type="stmt"/>
        <line num="562" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="563" count="0" type="stmt"/>
        <line num="564" count="0" type="stmt"/>
        <line num="567" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="568" count="0" type="stmt"/>
        <line num="569" count="0" type="stmt"/>
        <line num="572" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="573" count="0" type="stmt"/>
        <line num="576" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="577" count="0" type="stmt"/>
        <line num="581" count="0" type="stmt"/>
        <line num="583" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="586" count="0" type="stmt"/>
        <line num="588" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="592" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="593" count="0" type="stmt"/>
        <line num="595" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="596" count="0" type="stmt"/>
        <line num="599" count="0" type="stmt"/>
        <line num="605" count="0" type="stmt"/>
        <line num="619" count="0" type="stmt"/>
        <line num="621" count="0" type="stmt"/>
        <line num="622" count="0" type="stmt"/>
        <line num="623" count="0" type="stmt"/>
        <line num="631" count="0" type="stmt"/>
        <line num="632" count="0" type="stmt"/>
        <line num="638" count="0" type="stmt"/>
        <line num="646" count="0" type="stmt"/>
        <line num="650" count="0" type="stmt"/>
      </file>
      <file name="conformance.module.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/conformance.module.ts">
        <metrics statements="12" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
      </file>
      <file name="conformance.service.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/conformance.service.ts">
        <metrics statements="81" coveredstatements="55" conditionals="30" coveredconditionals="5" methods="12" coveredmethods="9"/>
        <line num="1" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="10" type="stmt"/>
        <line num="27" count="10" type="stmt"/>
        <line num="29" count="10" type="stmt"/>
        <line num="31" count="10" type="stmt"/>
        <line num="33" count="10" type="stmt"/>
        <line num="34" count="10" type="stmt"/>
        <line num="35" count="10" type="stmt"/>
        <line num="44" count="4" type="stmt"/>
        <line num="49" count="4" type="stmt"/>
        <line num="52" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="53" count="2" type="stmt"/>
        <line num="57" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="112" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="113" count="0" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="146" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="171" count="2" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="215" count="2" type="stmt"/>
        <line num="218" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="219" count="1" type="stmt"/>
        <line num="222" count="1" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="236" count="4" type="stmt"/>
        <line num="239" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="240" count="1" type="stmt"/>
        <line num="243" count="3" type="stmt"/>
        <line num="246" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="247" count="1" type="stmt"/>
        <line num="250" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="251" count="0" type="stmt"/>
        <line num="262" count="1" type="stmt"/>
        <line num="268" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="269" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="270" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="275" count="0" type="stmt"/>
        <line num="279" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="280" count="0" type="stmt"/>
        <line num="284" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="285" count="0" type="stmt"/>
        <line num="294" count="1" type="stmt"/>
        <line num="301" count="1" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="307" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="308" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="309" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="310" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="311" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="313" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.conformance.dto">
      <metrics statements="81" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="6" coveredmethods="0"/>
      <file name="bpmn-model.dto.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/dto/bpmn-model.dto.ts">
        <metrics statements="14" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
      </file>
      <file name="conformance-check.dto.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/dto/conformance-check.dto.ts">
        <metrics statements="29" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
      </file>
      <file name="conformance-result.dto.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/dto/conformance-result.dto.ts">
        <metrics statements="31" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/conformance/dto/index.ts">
        <metrics statements="7" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.database.migrations">
      <metrics statements="77" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="12" coveredmethods="0"/>
      <file name="1750602780460-InitialMigrationMySQL.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/database/migrations/1750602780460-InitialMigrationMySQL.ts">
        <metrics statements="21" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
      </file>
      <file name="1750602780461-AddTestUser.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/database/migrations/1750602780461-AddTestUser.ts">
        <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
      </file>
      <file name="1750669022006-AddAnalysisResultCacheFields.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/database/migrations/1750669022006-AddAnalysisResultCacheFields.ts">
        <metrics statements="23" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
      </file>
      <file name="1750670463601-FixDataSourceHashType.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/database/migrations/1750670463601-FixDataSourceHashType.ts">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
      </file>
      <file name="1750700000000-AddConformanceCheckTables.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/database/migrations/1750700000000-AddConformanceCheckTables.ts">
        <metrics statements="23" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.entities">
      <metrics statements="214" coveredstatements="154" conditionals="37" coveredconditionals="14" methods="59" coveredmethods="7"/>
      <file name="analysis-result.entity.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/analysis-result.entity.ts">
        <metrics statements="38" coveredstatements="31" conditionals="9" coveredconditionals="4" methods="8" coveredmethods="2"/>
        <line num="1" count="9" type="stmt"/>
        <line num="11" count="9" type="stmt"/>
        <line num="13" count="9" type="cond" truecount="2" falsecount="0"/>
        <line num="14" count="9" type="stmt"/>
        <line num="15" count="9" type="stmt"/>
        <line num="16" count="9" type="stmt"/>
        <line num="17" count="9" type="stmt"/>
        <line num="18" count="9" type="stmt"/>
        <line num="21" count="9" type="cond" truecount="2" falsecount="0"/>
        <line num="22" count="9" type="stmt"/>
        <line num="23" count="9" type="stmt"/>
        <line num="24" count="9" type="stmt"/>
        <line num="25" count="9" type="stmt"/>
        <line num="26" count="9" type="stmt"/>
        <line num="33" count="9" type="stmt"/>
        <line num="35" count="9" type="stmt"/>
        <line num="41" count="9" type="stmt"/>
        <line num="48" count="9" type="stmt"/>
        <line num="51" count="9" type="stmt"/>
        <line num="54" count="9" type="stmt"/>
        <line num="57" count="9" type="stmt"/>
        <line num="60" count="9" type="stmt"/>
        <line num="63" count="9" type="stmt"/>
        <line num="66" count="9" type="stmt"/>
        <line num="69" count="9" type="stmt"/>
        <line num="72" count="9" type="stmt"/>
        <line num="75" count="9" type="stmt"/>
        <line num="78" count="9" type="stmt"/>
        <line num="81" count="9" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="9" type="stmt"/>
        <line num="90" count="9" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="95" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="111" count="0" type="stmt"/>
      </file>
      <file name="bpmn-model.entity.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/bpmn-model.entity.ts">
        <metrics statements="42" coveredstatements="28" conditionals="12" coveredconditionals="4" methods="10" coveredmethods="2"/>
        <line num="1" count="9" type="stmt"/>
        <line num="12" count="9" type="stmt"/>
        <line num="13" count="9" type="stmt"/>
        <line num="15" count="9" type="cond" truecount="2" falsecount="0"/>
        <line num="16" count="9" type="stmt"/>
        <line num="17" count="9" type="stmt"/>
        <line num="18" count="9" type="stmt"/>
        <line num="21" count="9" type="cond" truecount="2" falsecount="0"/>
        <line num="22" count="9" type="stmt"/>
        <line num="23" count="9" type="stmt"/>
        <line num="24" count="9" type="stmt"/>
        <line num="30" count="9" type="stmt"/>
        <line num="32" count="9" type="stmt"/>
        <line num="35" count="9" type="stmt"/>
        <line num="38" count="9" type="stmt"/>
        <line num="45" count="9" type="stmt"/>
        <line num="52" count="9" type="stmt"/>
        <line num="55" count="9" type="stmt"/>
        <line num="58" count="9" type="stmt"/>
        <line num="61" count="9" type="stmt"/>
        <line num="64" count="9" type="stmt"/>
        <line num="67" count="9" type="stmt"/>
        <line num="70" count="9" type="stmt"/>
        <line num="73" count="9" type="stmt"/>
        <line num="76" count="9" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="82" count="9" type="stmt"/>
        <line num="85" count="9" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="9" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
      </file>
      <file name="conformance-result.entity.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/conformance-result.entity.ts">
        <metrics statements="61" coveredstatements="42" conditionals="14" coveredconditionals="4" methods="15" coveredmethods="2"/>
        <line num="1" count="9" type="stmt"/>
        <line num="11" count="9" type="stmt"/>
        <line num="12" count="9" type="stmt"/>
        <line num="14" count="9" type="cond" truecount="2" falsecount="0"/>
        <line num="15" count="9" type="stmt"/>
        <line num="16" count="9" type="stmt"/>
        <line num="17" count="9" type="stmt"/>
        <line num="18" count="9" type="stmt"/>
        <line num="21" count="9" type="cond" truecount="2" falsecount="0"/>
        <line num="22" count="9" type="stmt"/>
        <line num="23" count="9" type="stmt"/>
        <line num="24" count="9" type="stmt"/>
        <line num="25" count="9" type="stmt"/>
        <line num="26" count="9" type="stmt"/>
        <line num="27" count="9" type="stmt"/>
        <line num="34" count="9" type="stmt"/>
        <line num="36" count="9" type="stmt"/>
        <line num="43" count="9" type="stmt"/>
        <line num="46" count="9" type="stmt"/>
        <line num="49" count="9" type="stmt"/>
        <line num="52" count="9" type="stmt"/>
        <line num="55" count="9" type="stmt"/>
        <line num="58" count="9" type="stmt"/>
        <line num="61" count="9" type="stmt"/>
        <line num="64" count="9" type="stmt"/>
        <line num="67" count="9" type="stmt"/>
        <line num="70" count="9" type="stmt"/>
        <line num="81" count="9" type="stmt"/>
        <line num="84" count="9" type="stmt"/>
        <line num="94" count="9" type="stmt"/>
        <line num="102" count="9" type="stmt"/>
        <line num="105" count="9" type="stmt"/>
        <line num="108" count="9" type="stmt"/>
        <line num="111" count="9" type="stmt"/>
        <line num="114" count="9" type="stmt"/>
        <line num="117" count="9" type="stmt"/>
        <line num="120" count="9" type="stmt"/>
        <line num="123" count="9" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="129" count="9" type="stmt"/>
        <line num="132" count="9" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="138" count="9" type="stmt"/>
        <line num="141" count="9" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="146" count="0" type="stmt"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="159" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="168" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="185" count="0" type="stmt"/>
      </file>
      <file name="event-log.entity.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/event-log.entity.ts">
        <metrics statements="14" coveredstatements="13" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="9" type="stmt"/>
        <line num="10" count="9" type="stmt"/>
        <line num="15" count="9" type="stmt"/>
        <line num="17" count="9" type="stmt"/>
        <line num="20" count="9" type="stmt"/>
        <line num="23" count="9" type="stmt"/>
        <line num="26" count="9" type="stmt"/>
        <line num="29" count="9" type="stmt"/>
        <line num="32" count="9" type="stmt"/>
        <line num="35" count="9" type="stmt"/>
        <line num="38" count="9" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="44" count="9" type="stmt"/>
        <line num="47" count="9" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/index.ts">
        <metrics statements="12" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
      </file>
      <file name="process.entity.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/process.entity.ts">
        <metrics statements="32" coveredstatements="26" conditionals="2" coveredconditionals="2" methods="11" coveredmethods="1"/>
        <line num="1" count="9" type="stmt"/>
        <line num="11" count="9" type="stmt"/>
        <line num="12" count="9" type="stmt"/>
        <line num="13" count="9" type="stmt"/>
        <line num="14" count="9" type="stmt"/>
        <line num="15" count="9" type="stmt"/>
        <line num="17" count="9" type="cond" truecount="2" falsecount="0"/>
        <line num="18" count="9" type="stmt"/>
        <line num="19" count="9" type="stmt"/>
        <line num="20" count="9" type="stmt"/>
        <line num="21" count="9" type="stmt"/>
        <line num="25" count="9" type="stmt"/>
        <line num="27" count="9" type="stmt"/>
        <line num="30" count="9" type="stmt"/>
        <line num="33" count="9" type="stmt"/>
        <line num="40" count="9" type="stmt"/>
        <line num="43" count="9" type="stmt"/>
        <line num="46" count="9" type="stmt"/>
        <line num="49" count="9" type="stmt"/>
        <line num="52" count="9" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="9" type="stmt"/>
        <line num="59" count="9" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="9" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="9" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="9" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="9" type="stmt"/>
      </file>
      <file name="user.entity.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/entities/user.entity.ts">
        <metrics statements="15" coveredstatements="14" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="9" type="stmt"/>
        <line num="9" count="9" type="stmt"/>
        <line num="10" count="9" type="stmt"/>
        <line num="13" count="9" type="stmt"/>
        <line num="15" count="9" type="stmt"/>
        <line num="18" count="9" type="stmt"/>
        <line num="21" count="9" type="stmt"/>
        <line num="25" count="9" type="stmt"/>
        <line num="28" count="9" type="stmt"/>
        <line num="31" count="9" type="stmt"/>
        <line num="34" count="9" type="stmt"/>
        <line num="37" count="9" type="stmt"/>
        <line num="40" count="9" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="9" type="stmt"/>
      </file>
    </package>
    <package name="src.processes">
      <metrics statements="67" coveredstatements="27" conditionals="17" coveredconditionals="2" methods="15" coveredmethods="7"/>
      <file name="processes.controller.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/processes/processes.controller.ts">
        <metrics statements="22" coveredstatements="21" conditionals="3" coveredconditionals="2" methods="7" coveredmethods="7"/>
        <line num="1" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="12" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="2" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="48" count="2" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="2" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="84" count="2" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="2" type="stmt"/>
      </file>
      <file name="processes.module.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/processes/processes.module.ts">
        <metrics statements="7" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
      </file>
      <file name="processes.service.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/processes/processes.service.ts">
        <metrics statements="38" coveredstatements="6" conditionals="14" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="117" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="130" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="140" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.processes.dto">
      <metrics statements="20" coveredstatements="20" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
      <file name="create-process.dto.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/processes/dto/create-process.dto.ts">
        <metrics statements="9" coveredstatements="9" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/processes/dto/index.ts">
        <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
      </file>
      <file name="update-process.dto.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/processes/dto/update-process.dto.ts">
        <metrics statements="9" coveredstatements="9" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.test-utils">
      <metrics statements="154" coveredstatements="19" conditionals="20" coveredconditionals="2" methods="34" coveredmethods="2"/>
      <file name="conformance-test-data-generator.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/test-utils/conformance-test-data-generator.ts">
        <metrics statements="101" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="21" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="29" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="196" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="250" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="252" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="261" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="262" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="267" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="268" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="313" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="317" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="462" count="0" type="stmt"/>
      </file>
      <file name="mock-data.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/test-utils/mock-data.ts">
        <metrics statements="20" coveredstatements="19" conditionals="3" coveredconditionals="2" methods="3" coveredmethods="2"/>
        <line num="2" count="6" type="stmt"/>
        <line num="4" count="6" type="stmt"/>
        <line num="16" count="6" type="stmt"/>
        <line num="42" count="6" type="stmt"/>
        <line num="66" count="6" type="stmt"/>
        <line num="96" count="6" type="stmt"/>
        <line num="115" count="6" type="stmt"/>
        <line num="122" count="6" type="stmt"/>
        <line num="127" count="6" type="stmt"/>
        <line num="132" count="6" type="stmt"/>
        <line num="138" count="6" type="stmt"/>
        <line num="143" count="6" type="stmt"/>
        <line num="151" count="6" type="stmt"/>
        <line num="161" count="6" type="stmt"/>
        <line num="172" count="6" type="stmt"/>
        <line num="180" count="6" type="stmt"/>
        <line num="230" count="6" type="stmt"/>
        <line num="232" count="1" type="stmt"/>
        <line num="245" count="1" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
      </file>
      <file name="test-helpers.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/test-utils/test-helpers.ts">
        <metrics statements="33" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="62" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.users">
      <metrics statements="56" coveredstatements="49" conditionals="4" coveredconditionals="4" methods="16" coveredmethods="15"/>
      <file name="users.controller.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/users/users.controller.ts">
        <metrics statements="19" coveredstatements="19" conditionals="0" coveredconditionals="0" methods="7" coveredmethods="7"/>
        <line num="1" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="11" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="2" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="2" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="2" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="2" type="stmt"/>
      </file>
      <file name="users.module.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/users/users.module.ts">
        <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
      </file>
      <file name="users.service.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/users/users.service.ts">
        <metrics statements="31" coveredstatements="30" conditionals="4" coveredconditionals="4" methods="9" coveredmethods="8"/>
        <line num="1" count="4" type="stmt"/>
        <line num="6" count="4" type="stmt"/>
        <line num="7" count="4" type="stmt"/>
        <line num="8" count="4" type="stmt"/>
        <line num="9" count="4" type="stmt"/>
        <line num="13" count="4" type="stmt"/>
        <line num="16" count="13" type="stmt"/>
        <line num="20" count="3" type="stmt"/>
        <line num="23" count="3" type="stmt"/>
        <line num="27" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="28" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="29" count="1" type="stmt"/>
        <line num="31" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="32" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="66" count="6" type="stmt"/>
        <line num="80" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="81" count="3" type="stmt"/>
        <line num="84" count="3" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="100" count="2" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="108" count="2" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.users.dto">
      <metrics statements="15" coveredstatements="15" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
      <file name="create-user.dto.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/users/dto/create-user.dto.ts">
        <metrics statements="7" coveredstatements="7" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/users/dto/index.ts">
        <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
      </file>
      <file name="update-user.dto.ts" path="/Users/<USER>/Development/Cicada/bpmax-pro-mined/server/src/users/dto/update-user.dto.ts">
        <metrics statements="6" coveredstatements="6" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
