
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/analysis</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/analysis</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">37.57% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>307/817</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">17.5% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>35/200</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">37.86% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>64/169</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">35.78% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>277/774</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="analysis.controller.ts"><a href="analysis.controller.ts.html">analysis.controller.ts</a></td>
	<td data-value="54.62" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 54%"></div><div class="cover-empty" style="width: 46%"></div></div>
	</td>
	<td data-value="54.62" class="pct medium">54.62%</td>
	<td data-value="108" class="abs medium">59/108</td>
	<td data-value="21.73" class="pct low">21.73%</td>
	<td data-value="23" class="abs low">5/23</td>
	<td data-value="30.76" class="pct low">30.76%</td>
	<td data-value="26" class="abs low">8/26</td>
	<td data-value="53.77" class="pct medium">53.77%</td>
	<td data-value="106" class="abs medium">57/106</td>
	</tr>

<tr>
	<td class="file low" data-value="analysis.module.ts"><a href="analysis.module.ts.html">analysis.module.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="13" class="abs low">0/13</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="11" class="abs low">0/11</td>
	</tr>

<tr>
	<td class="file low" data-value="cache.service.ts"><a href="cache.service.ts.html">cache.service.ts</a></td>
	<td data-value="8.45" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 8%"></div><div class="cover-empty" style="width: 92%"></div></div>
	</td>
	<td data-value="8.45" class="pct low">8.45%</td>
	<td data-value="71" class="abs low">6/71</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="14" class="abs low">0/14</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="13" class="abs low">0/13</td>
	<td data-value="5.79" class="pct low">5.79%</td>
	<td data-value="69" class="abs low">4/69</td>
	</tr>

<tr>
	<td class="file low" data-value="data-processing.service.ts"><a href="data-processing.service.ts.html">data-processing.service.ts</a></td>
	<td data-value="10.09" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 10%"></div><div class="cover-empty" style="width: 90%"></div></div>
	</td>
	<td data-value="10.09" class="pct low">10.09%</td>
	<td data-value="109" class="abs low">11/109</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="43" class="abs low">0/43</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="15" class="abs low">0/15</td>
	<td data-value="8.41" class="pct low">8.41%</td>
	<td data-value="107" class="abs low">9/107</td>
	</tr>

<tr>
	<td class="file low" data-value="data-stream.service.ts"><a href="data-stream.service.ts.html">data-stream.service.ts</a></td>
	<td data-value="9.37" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 9%"></div><div class="cover-empty" style="width: 91%"></div></div>
	</td>
	<td data-value="9.37" class="pct low">9.37%</td>
	<td data-value="96" class="abs low">9/96</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="35" class="abs low">0/35</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="11" class="abs low">0/11</td>
	<td data-value="7.44" class="pct low">7.44%</td>
	<td data-value="94" class="abs low">7/94</td>
	</tr>

<tr>
	<td class="file low" data-value="process-mining.service.ts"><a href="process-mining.service.ts.html">process-mining.service.ts</a></td>
	<td data-value="13.21" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 13%"></div><div class="cover-empty" style="width: 87%"></div></div>
	</td>
	<td data-value="13.21" class="pct low">13.21%</td>
	<td data-value="227" class="abs low">30/227</td>
	<td data-value="8.92" class="pct low">8.92%</td>
	<td data-value="56" class="abs low">5/56</td>
	<td data-value="11.11" class="pct low">11.11%</td>
	<td data-value="54" class="abs low">6/54</td>
	<td data-value="13.08" class="pct low">13.08%</td>
	<td data-value="214" class="abs low">28/214</td>
	</tr>

<tr>
	<td class="file high" data-value="subprocess-discovery.service.ts"><a href="subprocess-discovery.service.ts.html">subprocess-discovery.service.ts</a></td>
	<td data-value="99.48" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 99%"></div><div class="cover-empty" style="width: 1%"></div></div>
	</td>
	<td data-value="99.48" class="pct high">99.48%</td>
	<td data-value="193" class="abs high">192/193</td>
	<td data-value="86.2" class="pct high">86.2%</td>
	<td data-value="29" class="abs high">25/29</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="50" class="abs high">50/50</td>
	<td data-value="99.42" class="pct high">99.42%</td>
	<td data-value="173" class="abs high">172/173</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-24T14:58:58.931Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    