const BASE_URL = 'http://localhost:3003/api/v1';

let authToken = null;

// 简单的HTTP客户端
async function httpGet(url) {
  const headers = authToken ? { 'Authorization': `Bearer ${authToken}` } : {};
  const response = await fetch(url, { headers });
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  return { data: await response.json() };
}

async function httpPost(url, body = null) {
  const headers = {
    'Content-Type': 'application/json',
    ...(authToken ? { 'Authorization': `Bearer ${authToken}` } : {})
  };
  const response = await fetch(url, {
    method: 'POST',
    headers,
    body: body ? JSON.stringify(body) : null
  });
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  return { data: await response.json() };
}

// 登录获取token
async function login() {
  console.log('🔐 Logging in...');
  try {
    const response = await httpPost(`${BASE_URL}/auth/login`, {
      usernameOrEmail: 'admin',
      password: 'admin123'
    });
    authToken = response.data.access_token;
    console.log('   ✅ Login successful');
    return true;
  } catch (error) {
    console.log('   ❌ Login failed:', error.message);
    return false;
  }
}

async function testCacheFlow() {
  console.log('🧪 Testing Process Discovery Cache Flow...\n');

  try {
    // Step 0: 登录 (暂时跳过，已禁用认证)
    console.log('🔓 Authentication disabled for testing\n');

    // Step 1: 检查初始状态
    console.log('1️⃣ Checking initial state...');
    try {
      const initialResult = await httpGet(`${BASE_URL}/analysis/results/1/process_discovery`);
      console.log('   ✅ Found existing result:', !!initialResult.data);
      if (initialResult.data) {
        console.log('   📊 Result has nodes:', initialResult.data.nodes?.length || 0);
        console.log('   📊 Result has edges:', initialResult.data.edges?.length || 0);
      }
    } catch (error) {
      console.log('   ❌ No existing result found (expected for first run)');
    }

    // Step 2: 执行流程发现
    console.log('\n2️⃣ Running process discovery...');
    const discoveryResponse = await httpPost(`${BASE_URL}/analysis/discover/1`);
    console.log('   ✅ Discovery completed successfully');
    console.log('   📊 Generated nodes:', discoveryResponse.data.nodes?.length || 0);
    console.log('   📊 Generated edges:', discoveryResponse.data.edges?.length || 0);
    console.log('   📊 Statistics:', JSON.stringify(discoveryResponse.data.statistics, null, 2));

    // Step 3: 检查缓存状态
    console.log('\n3️⃣ Checking cache status...');
    try {
      const cacheStatus = await httpGet(`${BASE_URL}/analysis/cache/status/1`);
      console.log('   ✅ Cache status:', JSON.stringify(cacheStatus.data, null, 2));
    } catch (error) {
      console.log('   ❌ Failed to get cache status:', error.message);
    }

    // Step 4: 验证结果是否已保存
    console.log('\n4️⃣ Verifying saved result...');
    const savedResult = await httpGet(`${BASE_URL}/analysis/results/1/process_discovery`);
    console.log('   ✅ Result retrieved successfully');
    console.log('   📊 Saved nodes:', savedResult.data.nodes?.length || 0);
    console.log('   📊 Saved edges:', savedResult.data.edges?.length || 0);
    
    // Step 5: 比较结果一致性
    console.log('\n5️⃣ Comparing result consistency...');
    const originalNodes = discoveryResponse.data.nodes?.length || 0;
    const savedNodes = savedResult.data.nodes?.length || 0;
    const originalEdges = discoveryResponse.data.edges?.length || 0;
    const savedEdges = savedResult.data.edges?.length || 0;
    
    if (originalNodes === savedNodes && originalEdges === savedEdges) {
      console.log('   ✅ Results are consistent!');
    } else {
      console.log('   ❌ Results are inconsistent!');
      console.log(`   📊 Original: ${originalNodes} nodes, ${originalEdges} edges`);
      console.log(`   📊 Saved: ${savedNodes} nodes, ${savedEdges} edges`);
    }

    // Step 6: 测试缓存命中
    console.log('\n6️⃣ Testing cache hit...');
    const startTime = Date.now();
    const cachedResponse = await httpPost(`${BASE_URL}/analysis/discover/1`);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`   ✅ Second discovery completed in ${duration}ms`);
    console.log('   📊 Cached nodes:', cachedResponse.data.nodes?.length || 0);
    console.log('   📊 Cached edges:', cachedResponse.data.edges?.length || 0);
    
    if (duration < 1000) {
      console.log('   🚀 Fast response suggests cache hit!');
    } else {
      console.log('   🐌 Slow response suggests cache miss');
    }

    console.log('\n🎉 Cache flow test completed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Response status:', error.response.status);
      console.error('   Response data:', error.response.data);
    }
  }
}

// 运行测试
testCacheFlow();
