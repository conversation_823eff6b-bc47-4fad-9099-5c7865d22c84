# ProMax 数据库设置指南

本指南将帮助您快速设置 ProMax 项目所需的 MySQL 数据库和 Redis 缓存。

## 前置要求

### 1. MySQL 8.0+
- **macOS**: `brew install mysql`
- **Ubuntu**: `sudo apt-get install mysql-server`
- **CentOS**: `sudo yum install mysql-server`
- **Windows**: 从 [MySQL 官网](https://dev.mysql.com/downloads/mysql/) 下载安装

### 2. Redis
- **macOS**: `brew install redis`
- **Ubuntu**: `sudo apt-get install redis-server`
- **CentOS**: `sudo yum install redis`
- **Windows**: 从 [Redis GitHub](https://github.com/microsoftarchive/redis/releases) 下载安装

### 3. Node.js 18+
- 从 [Node.js 官网](https://nodejs.org/) 下载安装

## 快速设置

### 方法一：使用自动化脚本（推荐）

#### Linux/macOS:
```bash
cd server
chmod +x scripts/setup-database.sh
./scripts/setup-database.sh
```

#### Windows:
```cmd
cd server
scripts\setup-database.bat
```

脚本将自动：
1. 检查 MySQL 和 Redis 是否安装
2. 测试数据库连接
3. 创建数据库
4. 生成 `.env` 配置文件
5. 安装项目依赖
6. 运行数据库迁移
7. 创建默认管理员账户

### 方法二：手动设置

#### 1. 创建数据库
```sql
CREATE DATABASE promined CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 2. 创建 .env 文件
复制 `.env.example` 到 `.env` 并修改配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=promined

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_KEY_PREFIX=promined:
REDIS_DEFAULT_EXPIRE=600

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
```

#### 3. 安装依赖
```bash
yarn install
# 或
npm install
```

#### 4. 运行数据库迁移
```bash
yarn migration:run
# 或
npm run migration:run
```

## 数据库结构

### 主要表结构

1. **users** - 用户表
   - 存储用户账户信息
   - 包含用户名、邮箱、密码哈希等

2. **processes** - 流程表
   - 存储业务流程项目信息
   - 包含流程名称、描述、状态等

3. **event_logs** - 事件日志表
   - 存储流程挖掘的原始数据
   - 包含案例ID、活动、时间戳、资源等

4. **analysis_results** - 分析结果表
   - 存储各种分析结果
   - 支持流程发现、性能分析等多种分析类型

### 索引优化

- `event_logs` 表针对查询优化创建了复合索引
- `analysis_results` 表按分析类型和流程ID创建索引
- 所有外键关系都有对应的索引

## 数据库迁移管理

### 创建新迁移
```bash
yarn migration:create --name=YourMigrationName
# 或
npm run migration:create --name=YourMigrationName
```

### 运行迁移
```bash
yarn migration:run
# 或
npm run migration:run
```

### 回滚迁移
```bash
yarn migration:revert
# 或
npm run migration:revert
```

### 生成迁移（基于实体变更）
```bash
yarn migration:generate --name=YourMigrationName
# 或
npm run migration:generate --name=YourMigrationName
```

## 默认账户

设置完成后，系统会自动创建一个默认管理员账户：

- **用户名**: admin
- **密码**: adminadmin
- **邮箱**: <EMAIL>

⚠️ **重要**: 请在生产环境中立即修改默认密码！

## 启动服务

数据库设置完成后，可以启动开发服务器：

```bash
yarn start:dev
# 或
npm run start:dev
```

服务器将在 `http://localhost:3003` 启动。

## 故障排除

### 常见问题

1. **MySQL 连接失败**
   - 检查 MySQL 服务是否启动
   - 验证用户名和密码是否正确
   - 确认端口号是否正确

2. **Redis 连接失败**
   - 检查 Redis 服务是否启动
   - 验证主机和端口配置
   - 检查是否需要密码认证

3. **迁移失败**
   - 确保数据库已创建
   - 检查数据库用户权限
   - 查看错误日志获取详细信息

4. **权限问题**
   - 确保数据库用户有足够的权限
   - 检查文件系统权限

### 日志查看

开发模式下，数据库查询日志会在控制台显示，有助于调试问题。

### 重置数据库

如果需要重置数据库：

```bash
# 回滚所有迁移
yarn migration:revert

# 重新运行迁移
yarn migration:run
```

## 生产环境注意事项

1. **安全配置**
   - 使用强密码
   - 限制数据库访问权限
   - 配置防火墙规则

2. **性能优化**
   - 配置适当的连接池大小
   - 启用查询缓存
   - 定期优化数据库

3. **备份策略**
   - 设置定期数据库备份
   - 测试备份恢复流程
   - 监控数据库性能

4. **监控**
   - 设置数据库监控
   - 配置告警规则
   - 记录访问日志
