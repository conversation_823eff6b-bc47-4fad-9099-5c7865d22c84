import { Test, TestingModule } from '@nestjs/testing';
import { ProcessMiningService } from '../src/analysis/process-mining.service';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { EventLog } from '../src/entities/event-log.entity';
import { AnalysisResult } from '../src/entities/analysis-result.entity';
import { CacheService } from '../src/analysis/cache.service';

describe('ProcessMiningService - Empty Subprocess Filter', () => {
  let service: ProcessMiningService;
  let eventLogRepository: Repository<EventLog>;

  const mockEventLogRepository = {
    find: jest.fn(),
    count: jest.fn(),
  };

  const mockAnalysisResultRepository = {
    save: jest.fn(),
    findOne: jest.fn(),
  };

  const mockCacheService = {
    getDataSourceHash: jest.fn(),
    getCachedAnalysisResult: jest.fn(),
    setCachedAnalysisResult: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProcessMiningService,
        {
          provide: getRepositoryToken(EventLog),
          useValue: mockEventLogRepository,
        },
        {
          provide: getRepositoryToken(AnalysisResult),
          useValue: mockAnalysisResultRepository,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
      ],
    }).compile();

    service = module.get<ProcessMiningService>(ProcessMiningService);
    eventLogRepository = module.get<Repository<EventLog>>(
      getRepositoryToken(EventLog),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('filterEmptySubprocesses', () => {
    it('should filter out subprocesses with only start/end nodes', () => {
      // 创建测试数据：包含空子流程的节点和边
      const testNodes = [
        // 主流程节点
        { id: '开始', label: '开始', frequency: 10 },
        { id: '结束', label: '结束', frequency: 10 },
        { id: '业务活动A', label: '业务活动A', frequency: 8 },
        { id: '业务活动B', label: '业务活动B', frequency: 6 },

        // 有效子流程节点（包含多个业务节点）
        {
          id: '开始_子流程1',
          label: '开始_子流程1',
          frequency: 5,
          groupId: 'subprocess_1',
        },
        {
          id: '子流程业务1',
          label: '子流程业务1',
          frequency: 5,
          groupId: 'subprocess_1',
        },
        {
          id: '子流程业务2',
          label: '子流程业务2',
          frequency: 4,
          groupId: 'subprocess_1',
        },
        {
          id: '结束_子流程1',
          label: '结束_子流程1',
          frequency: 4,
          groupId: 'subprocess_1',
        },

        // 空子流程节点（只有开始和结束节点）
        {
          id: '开始_子流程2',
          label: '开始_子流程2',
          frequency: 3,
          groupId: 'subprocess_2',
        },
        {
          id: '结束_子流程2',
          label: '结束_子流程2',
          frequency: 3,
          groupId: 'subprocess_2',
        },

        // 孤立节点子流程（只有一个业务节点，无连接）
        {
          id: '开始_子流程3',
          label: '开始_子流程3',
          frequency: 2,
          groupId: 'subprocess_3',
        },
        {
          id: '孤立业务节点',
          label: '孤立业务节点',
          frequency: 2,
          groupId: 'subprocess_3',
        },
        {
          id: '结束_子流程3',
          label: '结束_子流程3',
          frequency: 2,
          groupId: 'subprocess_3',
        },

        // 断开连接的子流程（多个业务节点但无内部连接）
        {
          id: '开始_子流程4',
          label: '开始_子流程4',
          frequency: 3,
          groupId: 'subprocess_4',
        },
        {
          id: '断开节点1',
          label: '断开节点1',
          frequency: 2,
          groupId: 'subprocess_4',
        },
        {
          id: '断开节点2',
          label: '断开节点2',
          frequency: 1,
          groupId: 'subprocess_4',
        },
        {
          id: '结束_子流程4',
          label: '结束_子流程4',
          frequency: 1,
          groupId: 'subprocess_4',
        },
      ];

      const testEdges = [
        // 主流程边
        { source: '开始', target: '业务活动A', frequency: 8 },
        { source: '业务活动A', target: '业务活动B', frequency: 6 },
        { source: '业务活动B', target: '结束', frequency: 6 },

        // 有效子流程内部边
        { source: '开始_子流程1', target: '子流程业务1', frequency: 5 },
        { source: '子流程业务1', target: '子流程业务2', frequency: 4 },
        { source: '子流程业务2', target: '结束_子流程1', frequency: 4 },

        // 空子流程边（只有开始到结束）
        { source: '开始_子流程2', target: '结束_子流程2', frequency: 3 },

        // 孤立节点子流程边（无业务节点间连接）
        { source: '开始_子流程3', target: '孤立业务节点', frequency: 2 },
        { source: '孤立业务节点', target: '结束_子流程3', frequency: 2 },

        // 断开连接子流程边（业务节点间无连接）
        { source: '开始_子流程4', target: '断开节点1', frequency: 2 },
        { source: '开始_子流程4', target: '断开节点2', frequency: 1 },
        { source: '断开节点1', target: '结束_子流程4', frequency: 1 },
        { source: '断开节点2', target: '结束_子流程4', frequency: 1 },
      ];

      // 使用反射访问私有方法进行测试
      const filterMethod = (service as any).filterEmptySubprocesses.bind(
        service,
      );
      const result = filterMethod(testNodes, testEdges, {});

      // 验证过滤结果
      expect(result.nodes.length).toBeLessThan(testNodes.length);
      expect(result.edges.length).toBeLessThan(testEdges.length);

      // 验证主流程节点被保留
      const mainProcessNodes = result.nodes.filter((n) => !n.groupId);
      expect(mainProcessNodes.length).toBe(4); // 开始、结束、业务活动A、业务活动B

      // 验证有效子流程被保留
      const validSubprocessNodes = result.nodes.filter(
        (n) => n.groupId === 'subprocess_1',
      );
      expect(validSubprocessNodes.length).toBe(4); // 包含2个业务节点和开始结束节点

      // 验证空子流程被过滤
      const emptySubprocessNodes = result.nodes.filter(
        (n) => n.groupId === 'subprocess_2',
      );
      expect(emptySubprocessNodes.length).toBe(0);

      // 验证孤立节点子流程被保留（因为有内部连接）
      const isolatedSubprocessNodes = result.nodes.filter(
        (n) => n.groupId === 'subprocess_3',
      );
      expect(isolatedSubprocessNodes.length).toBe(3); // 有连接的单业务节点子流程被保留

      // 验证断开连接子流程被保留（因为有内部连接）
      const disconnectedSubprocessNodes = result.nodes.filter(
        (n) => n.groupId === 'subprocess_4',
      );
      expect(disconnectedSubprocessNodes.length).toBe(4); // 有连接的多业务节点子流程被保留

      // 验证有效子流程计数（只有真正的空子流程被过滤）
      expect(result.validSubprocessCount).toBe(3);
    });

    it('should handle edge cases correctly', () => {
      // 测试边缘情况
      const edgeCaseNodes = [
        // 包含系统节点变体的子流程
        {
          id: 'START_SYSTEM',
          label: 'START_SYSTEM',
          frequency: 1,
          groupId: 'subprocess_edge1',
        },
        {
          id: 'END_SYSTEM',
          label: 'END_SYSTEM',
          frequency: 1,
          groupId: 'subprocess_edge1',
        },
        {
          id: 'SYSTEM_GENERATED',
          label: 'SYSTEM_GENERATED',
          frequency: 1,
          groupId: 'subprocess_edge1',
        },

        // 包含英文开始结束节点的子流程
        {
          id: 'start_process',
          label: 'start_process',
          frequency: 2,
          groupId: 'subprocess_edge2',
        },
        {
          id: 'end_process',
          label: 'end_process',
          frequency: 2,
          groupId: 'subprocess_edge2',
        },

        // 正常业务子流程
        {
          id: '正常开始',
          label: '正常开始',
          frequency: 3,
          groupId: 'subprocess_normal',
        },
        {
          id: '正常业务',
          label: '正常业务',
          frequency: 3,
          groupId: 'subprocess_normal',
        },
        {
          id: '正常结束',
          label: '正常结束',
          frequency: 3,
          groupId: 'subprocess_normal',
        },
      ];

      const edgeCaseEdges = [
        { source: 'START_SYSTEM', target: 'SYSTEM_GENERATED', frequency: 1 },
        { source: 'SYSTEM_GENERATED', target: 'END_SYSTEM', frequency: 1 },
        { source: 'start_process', target: 'end_process', frequency: 2 },
        { source: '正常开始', target: '正常业务', frequency: 3 },
        { source: '正常业务', target: '正常结束', frequency: 3 },
      ];

      const filterMethod = (service as any).filterEmptySubprocesses.bind(
        service,
      );
      const result = filterMethod(edgeCaseNodes, edgeCaseEdges, {});

      // 验证系统节点子流程被过滤
      const systemSubprocess = result.nodes.filter(
        (n) => n.groupId === 'subprocess_edge1',
      );
      expect(systemSubprocess.length).toBe(0);

      // 验证英文开始结束节点子流程被过滤
      const englishSubprocess = result.nodes.filter(
        (n) => n.groupId === 'subprocess_edge2',
      );
      expect(englishSubprocess.length).toBe(0);

      // 验证正常业务子流程被保留
      const normalSubprocess = result.nodes.filter(
        (n) => n.groupId === 'subprocess_normal',
      );
      expect(normalSubprocess.length).toBe(3);

      expect(result.validSubprocessCount).toBe(1);
    });

    it('should filter out truly empty subprocesses with no business connections', () => {
      // 创建真正的空子流程测试数据
      const testNodes = [
        // 主流程节点
        { id: '开始', label: '开始', frequency: 10 },
        { id: '结束', label: '结束', frequency: 10 },
        { id: '业务活动', label: '业务活动', frequency: 8 },

        // 真正的空子流程（只有开始结束，无业务节点）
        {
          id: '开始_空流程',
          label: '开始_空流程',
          frequency: 3,
          groupId: 'subprocess_empty',
        },
        {
          id: '结束_空流程',
          label: '结束_空流程',
          frequency: 3,
          groupId: 'subprocess_empty',
        },

        // 孤立业务节点子流程（业务节点无连接）
        {
          id: '开始_孤立',
          label: '开始_孤立',
          frequency: 2,
          groupId: 'subprocess_isolated',
        },
        {
          id: '孤立节点',
          label: '孤立节点',
          frequency: 2,
          groupId: 'subprocess_isolated',
        },
        {
          id: '结束_孤立',
          label: '结束_孤立',
          frequency: 2,
          groupId: 'subprocess_isolated',
        },

        // 断开的多节点子流程（业务节点间无连接）
        {
          id: '开始_断开',
          label: '开始_断开',
          frequency: 3,
          groupId: 'subprocess_broken',
        },
        {
          id: '断开1',
          label: '断开1',
          frequency: 2,
          groupId: 'subprocess_broken',
        },
        {
          id: '断开2',
          label: '断开2',
          frequency: 1,
          groupId: 'subprocess_broken',
        },
        {
          id: '结束_断开',
          label: '结束_断开',
          frequency: 1,
          groupId: 'subprocess_broken',
        },
      ];

      const testEdges = [
        // 主流程边
        { source: '开始', target: '业务活动', frequency: 8 },
        { source: '业务活动', target: '结束', frequency: 8 },

        // 空子流程边（只有开始到结束）
        { source: '开始_空流程', target: '结束_空流程', frequency: 3 },

        // 孤立节点子流程边（业务节点无连接，只有开始/结束连接）
        { source: '开始_孤立', target: '结束_孤立', frequency: 2 }, // 跳过业务节点

        // 断开连接子流程边（业务节点间无连接）
        { source: '开始_断开', target: '结束_断开', frequency: 1 }, // 跳过所有业务节点
      ];

      const filterMethod = (service as any).filterEmptySubprocesses.bind(
        service,
      );
      const result = filterMethod(testNodes, testEdges, {});

      // 验证主流程节点被保留
      const mainProcessNodes = result.nodes.filter((n) => !n.groupId);
      expect(mainProcessNodes.length).toBe(3);

      // 验证空子流程被过滤
      const emptySubprocessNodes = result.nodes.filter(
        (n) => n.groupId === 'subprocess_empty',
      );
      expect(emptySubprocessNodes.length).toBe(0);

      // 验证孤立节点子流程被过滤（业务节点无连接）
      const isolatedSubprocessNodes = result.nodes.filter(
        (n) => n.groupId === 'subprocess_isolated',
      );
      expect(isolatedSubprocessNodes.length).toBe(0);

      // 验证断开连接子流程被过滤（业务节点无连接）
      const brokenSubprocessNodes = result.nodes.filter(
        (n) => n.groupId === 'subprocess_broken',
      );
      expect(brokenSubprocessNodes.length).toBe(0);

      // 验证所有无效子流程都被过滤
      expect(result.validSubprocessCount).toBe(0);

      // 验证过滤后只剩主流程
      expect(result.nodes.length).toBe(3);
      expect(result.edges.length).toBe(2);
    });
  });
});
