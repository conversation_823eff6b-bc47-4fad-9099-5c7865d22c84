import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { CacheService } from '../src/analysis/cache.service';
import { AnalysisType } from '../src/entities/analysis-result.entity';

describe('Cache Basic (e2e)', () => {
  let app: INestApplication;
  let cacheService: CacheService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    cacheService = moduleFixture.get<CacheService>(CacheService);
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // 清理缓存
    await cacheService.clearProcessCache(1);
  });

  describe('Cache Service', () => {
    it('should set and get cache data', async () => {
      const processId = 1;
      const analysisType = AnalysisType.PROCESS_DISCOVERY;
      const testData = {
        resultData: { nodes: [], edges: [] },
        version: 1,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000), // 1 hour
        dataSourceHash: 'test-hash',
        metadata: { test: true },
      };

      // 设置缓存
      await cacheService.setAnalysisResult(processId, analysisType, testData);

      // 获取缓存
      const cached = await cacheService.getAnalysisResult(
        processId,
        analysisType,
      );

      expect(cached).toBeTruthy();
      expect(cached!.resultData).toEqual(testData.resultData);
      expect(cached!.version).toBe(testData.version);
      expect(cached!.dataSourceHash).toBe(testData.dataSourceHash);
    });

    it('should generate correct cache keys', () => {
      const processId = 123;
      const analysisType = AnalysisType.PROCESS_DISCOVERY;
      const version = 2;

      const keyWithVersion = cacheService.generateCacheKey(
        processId,
        analysisType,
        version,
      );
      const keyWithoutVersion = cacheService.generateCacheKey(
        processId,
        analysisType,
      );

      expect(keyWithVersion).toBe('promined:analysis:123:process_discovery:v2');
      expect(keyWithoutVersion).toBe('promined:analysis:123:process_discovery');
    });

    it('should handle data source hash operations', async () => {
      const processId = 456;
      const hash = 'test-data-source-hash';

      // 设置哈希
      await cacheService.setDataSourceHash(processId, hash);

      // 获取哈希
      const retrievedHash = await cacheService.getDataSourceHash(processId);
      expect(retrievedHash).toBe(hash);

      // 检查变更
      const hasChanged = await cacheService.hasDataSourceChanged(
        processId,
        hash,
      );
      expect(hasChanged).toBe(false);

      const hasChangedDifferent = await cacheService.hasDataSourceChanged(
        processId,
        'different-hash',
      );
      expect(hasChangedDifferent).toBe(true);
    });

    it('should clear cache correctly', async () => {
      const processId = 789;
      const analysisType = AnalysisType.PROCESS_DISCOVERY;
      const testData = {
        resultData: { test: 'data' },
        version: 1,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
      };

      // 设置缓存
      await cacheService.setAnalysisResult(processId, analysisType, testData);

      // 验证缓存存在
      let cached = await cacheService.getAnalysisResult(
        processId,
        analysisType,
      );
      expect(cached).toBeTruthy();

      // 清除缓存
      await cacheService.deleteAnalysisResult(processId, analysisType);

      // 验证缓存已清除
      cached = await cacheService.getAnalysisResult(processId, analysisType);
      expect(cached).toBeNull();
    });

    it('should get cache statistics', async () => {
      const processId = 999;

      // 初始状态应该没有缓存
      let stats = await cacheService.getCacheStats(processId);
      expect(stats.totalCached).toBe(0);
      expect(stats.byType.process_discovery).toBe(false);

      // 添加一些缓存
      const testData = {
        resultData: { test: 'data' },
        version: 1,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
      };

      await cacheService.setAnalysisResult(
        processId,
        AnalysisType.PROCESS_DISCOVERY,
        testData,
      );

      // 检查统计信息
      stats = await cacheService.getCacheStats(processId);
      expect(stats.totalCached).toBe(1);
      expect(stats.byType.process_discovery).toBe(true);
    });
  });

  describe('Health Check', () => {
    it('should respond to health check', async () => {
      const response = await request(app.getHttpServer()).get('/').expect(200);

      expect(response.text).toContain('Hello World');
    });
  });
});
