import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { AppModule } from '../src/app.module';
import { CacheService } from '../src/analysis/cache.service';
import { AnalysisType } from '../src/entities/analysis-result.entity';

describe('Cache Integration (e2e)', () => {
  let app: INestApplication;
  let cacheService: CacheService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    cacheService = moduleFixture.get<CacheService>(CacheService);
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // 清理缓存
    await cacheService.clearProcessCache(999);
  });

  describe('Cache Service Integration', () => {
    it('should set and get cache data successfully', async () => {
      const processId = 999;
      const analysisType = AnalysisType.PROCESS_DISCOVERY;
      const testData = {
        resultData: {
          nodes: [
            { id: 'A', label: 'Start', frequency: 10 },
            { id: 'B', label: 'End', frequency: 10 },
          ],
          edges: [{ source: 'A', target: 'B', frequency: 10 }],
        },
        version: 1,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000), // 1 hour
        dataSourceHash: 'test-hash-123',
        metadata: { test: true },
      };

      // 设置缓存
      await cacheService.setAnalysisResult(processId, analysisType, testData);

      // 获取缓存
      const cached = await cacheService.getAnalysisResult(
        processId,
        analysisType,
      );

      expect(cached).toBeTruthy();
      expect(cached!.resultData).toEqual(testData.resultData);
      expect(cached!.version).toBe(testData.version);
      expect(cached!.dataSourceHash).toBe(testData.dataSourceHash);
    });

    it('should handle cache expiration correctly', async () => {
      const processId = 998;
      const analysisType = AnalysisType.PROCESS_DISCOVERY;
      const expiredData = {
        resultData: { nodes: [], edges: [] },
        version: 1,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() - 1000), // 已过期
        dataSourceHash: 'expired-hash',
      };

      // 设置已过期的缓存
      await cacheService.setAnalysisResult(
        processId,
        analysisType,
        expiredData,
      );

      // 尝试获取过期缓存，应该返回null
      const cached = await cacheService.getAnalysisResult(
        processId,
        analysisType,
      );
      expect(cached).toBeNull();
    });

    it('should generate correct cache keys', () => {
      const processId = 123;
      const analysisType = AnalysisType.PROCESS_DISCOVERY;
      const version = 2;

      const keyWithVersion = cacheService.generateCacheKey(
        processId,
        analysisType,
        version,
      );
      const keyWithoutVersion = cacheService.generateCacheKey(
        processId,
        analysisType,
      );

      expect(keyWithVersion).toBe('promined:analysis:123:process_discovery:v2');
      expect(keyWithoutVersion).toBe('promined:analysis:123:process_discovery');
    });

    it('should handle data source hash operations', async () => {
      const processId = 456;
      const hash = 'test-data-source-hash-456';

      // 设置哈希
      await cacheService.setDataSourceHash(processId, hash);

      // 获取哈希
      const retrievedHash = await cacheService.getDataSourceHash(processId);
      expect(retrievedHash).toBe(hash);

      // 检查变更
      const hasChanged = await cacheService.hasDataSourceChanged(
        processId,
        hash,
      );
      expect(hasChanged).toBe(false);

      const hasChangedDifferent = await cacheService.hasDataSourceChanged(
        processId,
        'different-hash',
      );
      expect(hasChangedDifferent).toBe(true);
    });

    it('should clear cache correctly', async () => {
      const processId = 789;
      const analysisType = AnalysisType.PROCESS_DISCOVERY;
      const testData = {
        resultData: { test: 'data' },
        version: 1,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
      };

      // 设置缓存
      await cacheService.setAnalysisResult(processId, analysisType, testData);

      // 验证缓存存在
      let cached = await cacheService.getAnalysisResult(
        processId,
        analysisType,
      );
      expect(cached).toBeTruthy();

      // 清除缓存
      await cacheService.deleteAnalysisResult(processId, analysisType);

      // 验证缓存已清除
      cached = await cacheService.getAnalysisResult(processId, analysisType);
      expect(cached).toBeNull();
    });

    it('should get cache statistics', async () => {
      const processId = 888;

      // 初始状态应该没有缓存
      let stats = await cacheService.getCacheStats(processId);
      expect(stats.totalCached).toBe(0);
      expect(stats.byType.process_discovery).toBe(false);

      // 添加一些缓存
      const testData = {
        resultData: { test: 'data' },
        version: 1,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
      };

      await cacheService.setAnalysisResult(
        processId,
        AnalysisType.PROCESS_DISCOVERY,
        testData,
      );

      // 检查统计信息
      stats = await cacheService.getCacheStats(processId);
      expect(stats.totalCached).toBe(1);
      expect(stats.byType.process_discovery).toBe(true);
    });

    it('should handle multiple cache versions', async () => {
      const processId = 777;
      const analysisType = AnalysisType.PROCESS_DISCOVERY;

      // 设置版本1
      const data1 = {
        resultData: { version: 1 },
        version: 1,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
      };
      await cacheService.setAnalysisResult(processId, analysisType, data1);

      // 设置版本2
      const data2 = {
        resultData: { version: 2 },
        version: 2,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
      };
      await cacheService.setAnalysisResult(processId, analysisType, data2);

      // 获取最新版本（不指定版本）
      const latest = await cacheService.getAnalysisResult(
        processId,
        analysisType,
      );
      expect(latest!.version).toBe(2);

      // 获取特定版本
      const v1 = await cacheService.getAnalysisResult(
        processId,
        analysisType,
        1,
      );
      expect(v1!.version).toBe(1);

      const v2 = await cacheService.getAnalysisResult(
        processId,
        analysisType,
        2,
      );
      expect(v2!.version).toBe(2);
    });

    it('should handle cache miss gracefully', async () => {
      const processId = 666;
      const analysisType = AnalysisType.PERFORMANCE_ANALYSIS;

      // 尝试获取不存在的缓存
      const cached = await cacheService.getAnalysisResult(
        processId,
        analysisType,
      );
      expect(cached).toBeNull();

      // 尝试获取不存在的哈希
      const hash = await cacheService.getDataSourceHash(processId);
      expect(hash).toBeNull();
    });
  });
});
