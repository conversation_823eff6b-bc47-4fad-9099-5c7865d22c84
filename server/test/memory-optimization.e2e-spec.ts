import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { CacheModule } from '@nestjs/cache-manager';
import { SubprocessDiscoveryService } from '../src/analysis/subprocess-discovery.service';
import { EventLog } from '../src/entities/event-log.entity';
import { AnalysisResult } from '../src/entities/analysis-result.entity';
import { CacheService } from '../src/analysis/cache.service';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';

describe('Memory Optimization Tests (e2e)', () => {
  let app: INestApplication;
  let subprocessService: SubprocessDiscoveryService;
  let eventLogRepository: Repository<EventLog>;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        TypeOrmModule.forRoot({
          type: 'mysql',
          host: process.env.DB_HOST || 'localhost',
          port: parseInt(process.env.DB_PORT || '3306'),
          username: process.env.DB_USERNAME || 'root',
          password: process.env.DB_PASSWORD || 'password',
          database: process.env.DB_TEST_DATABASE || 'promined_test_db',
          entities: [EventLog, AnalysisResult],
          synchronize: true,
          dropSchema: true,
        }),
        TypeOrmModule.forFeature([EventLog, AnalysisResult]),
        CacheModule.register({
          store: 'memory',
          ttl: 600,
        }),
      ],
      providers: [SubprocessDiscoveryService, CacheService],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    subprocessService = moduleFixture.get<SubprocessDiscoveryService>(
      SubprocessDiscoveryService,
    );
    eventLogRepository = moduleFixture.get<Repository<EventLog>>(
      getRepositoryToken(EventLog),
    );
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    await eventLogRepository.clear();
  });

  describe('Memory Usage Tests', () => {
    it('should handle large dataset without memory overflow', async () => {
      const processId = 1;
      const largeDataset: Partial<EventLog>[] = [];

      // 创建大量测试数据（模拟内存压力）
      const caseCount = 1000;
      const activitiesPerCase = 20;

      const activities = [
        '原材料采购申请',
        '供应商资质审核',
        '采购合同签署',
        '原材料入库检验',
        '入库确认',
        '生产准备',
        '首件检验',
        '批量生产',
        '过程检验',
        '不合格品处理',
        '供应商整改通知',
        '重新检验',
        '成品检验',
        '包装',
        '出库检验',
        '质量报告生成',
        '客户验收',
        '发货确认',
        '售后服务',
        '客户反馈收集',
      ];

      for (let caseId = 1; caseId <= caseCount; caseId++) {
        const baseTime = new Date('2024-01-01');

        for (
          let activityIndex = 0;
          activityIndex < activitiesPerCase;
          activityIndex++
        ) {
          const activity = activities[activityIndex % activities.length];
          const timestamp = new Date(
            baseTime.getTime() + caseId * 1000000 + activityIndex * 60000,
          );

          largeDataset.push({
            processId,
            caseId: `case_${caseId}`,
            activity,
            timestamp,
            resource: `resource_${(activityIndex % 5) + 1}`,
            // department: `dept_${(activityIndex % 3) + 1}`, // 移除不存在的字段
          });
        }
      }

      console.log(`Created ${largeDataset.length} test events`);

      // 记录初始内存使用
      const initialMemory = process.memoryUsage();
      console.log(
        `Initial memory: ${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB`,
      );

      // 批量插入数据
      const batchSize = 1000;
      for (let i = 0; i < largeDataset.length; i += batchSize) {
        const batch = largeDataset.slice(i, i + batchSize);
        await eventLogRepository.save(batch);

        // 每批次后检查内存
        if (i % 5000 === 0) {
          const currentMemory = process.memoryUsage();
          console.log(
            `Memory after inserting ${i + batch.length} events: ${Math.round(currentMemory.heapUsed / 1024 / 1024)}MB`,
          );
        }
      }

      const afterInsertMemory = process.memoryUsage();
      console.log(
        `Memory after data insertion: ${Math.round(afterInsertMemory.heapUsed / 1024 / 1024)}MB`,
      );

      // 执行子流程发现
      const startTime = Date.now();
      const result = await subprocessService.discoverSubprocesses(processId, {
        minFrequency: 5,
        minLength: 2,
        maxLength: 4,
        confidenceThreshold: 0.1,
        enableParallelDetection: false,
        enableLoopDetection: false,
      });
      const endTime = Date.now();

      const finalMemory = process.memoryUsage();
      console.log(
        `Memory after subprocess discovery: ${Math.round(finalMemory.heapUsed / 1024 / 1024)}MB`,
      );
      console.log(`Processing time: ${endTime - startTime}ms`);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.subprocesses).toBeDefined();
      expect(result.hierarchicalDFG).toBeDefined();
      expect(result.hierarchicalDFG.nodes.length).toBeGreaterThan(0);
      expect(result.hierarchicalDFG.nodes.length).toBeLessThan(20); // 确保节点压缩有效

      // 内存使用不应该超过合理范围
      const memoryIncreaseMB = Math.round(
        (finalMemory.heapUsed - initialMemory.heapUsed) / 1024 / 1024,
      );
      console.log(`Memory increase: ${memoryIncreaseMB}MB`);
      expect(memoryIncreaseMB).toBeLessThan(2000); // 不超过2GB增长

      console.log(`Found ${result.subprocesses.length} subprocesses`);
      console.log(
        `Hierarchical DFG has ${result.hierarchicalDFG.nodes.length} nodes`,
      );
    }, 300000); // 5分钟超时

    it('should use sampling for very large datasets', async () => {
      const processId = 2;

      // 模拟超大数据集的计数
      jest.spyOn(eventLogRepository, 'count').mockResolvedValue(100000);

      const initialMemory = process.memoryUsage();
      console.log(
        `Initial memory for sampling test: ${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB`,
      );

      // 这应该触发采样逻辑
      const result = await subprocessService.discoverSubprocesses(processId, {
        minFrequency: 2,
        minLength: 2,
        maxLength: 3,
      });

      const finalMemory = process.memoryUsage();
      console.log(
        `Memory after sampling test: ${Math.round(finalMemory.heapUsed / 1024 / 1024)}MB`,
      );

      expect(result).toBeDefined();

      // 恢复mock
      jest.restoreAllMocks();
    });

    it('should force garbage collection when memory is high', async () => {
      const initialMemory = process.memoryUsage();

      // 创建一些内存压力
      const largeArray = new Array(1000000).fill('test data');

      const beforeGcMemory = process.memoryUsage();
      console.log(
        `Before GC: ${Math.round(beforeGcMemory.heapUsed / 1024 / 1024)}MB`,
      );

      // 强制垃圾回收
      if (global.gc) {
        global.gc();
      }

      const afterGcMemory = process.memoryUsage();
      console.log(
        `After GC: ${Math.round(afterGcMemory.heapUsed / 1024 / 1024)}MB`,
      );

      // 清理引用
      largeArray.length = 0;

      expect(afterGcMemory.heapUsed).toBeLessThanOrEqual(
        beforeGcMemory.heapUsed,
      );
    });
  });
});
