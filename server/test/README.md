# E2E 测试配置说明

## 概述

本项目的 E2E 测试现在使用 MySQL 数据库，而不是之前的 SQLite 内存数据库。这样可以更好地模拟生产环境，确保测试的准确性。

## 配置文件

### 测试环境变量 (.env.test)

```bash
# 测试环境配置
NODE_ENV=test

# 数据库配置 (使用MySQL测试数据库)
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=33066
DB_USERNAME=root
DB_PASSWORD=abcde
DB_DATABASE=promined_test_db
DB_SYNCHRONIZE=true
DB_LOGGING=false

# JWT配置
JWT_SECRET=test-secret-key
JWT_EXPIRES_IN=1h

# Redis配置 (测试环境可以禁用)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_KEY_PREFIX=promined:test:
REDIS_DEFAULT_EXPIRE=600

# 应用配置
PORT=3001
API_PREFIX=api/v1

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads/test
```

## 测试数据库管理

### 自动化脚本

项目提供了自动化的测试数据库管理脚本：

- `scripts/setup-test-db.js` - 数据库设置和清理脚本

### 可用命令

```bash
# 运行完整的 E2E 测试（包含数据库设置和清理）
yarn test:e2e

# 手动设置测试数据库
yarn test:e2e:setup

# 手动清理测试数据库
yarn test:e2e:cleanup
```

## 测试流程

1. **数据库设置**: 自动创建 `promined_test_db` 测试数据库
2. **运行测试**: 执行所有 E2E 测试用例
3. **数据库清理**: 自动删除测试数据库

## 测试模块配置

### TestAppModule

测试模块 (`test/test-app.module.ts`) 包含：

- MySQL 数据库连接配置
- 所有必要的实体 (User, Process, EventLog, AnalysisResult)
- 内存缓存（避免 Redis 依赖）
- 认证和授权模块

### Jest 配置

- `test/jest-e2e.json` - Jest E2E 测试配置
- `test/jest.setup.ts` - 测试环境设置

## 注意事项

1. **数据库要求**: 确保 MySQL 服务器运行在指定端口 (33066)
2. **权限**: 确保数据库用户有创建和删除数据库的权限
3. **隔离性**: 每次测试运行都会重新创建数据库，确保测试隔离
4. **缓存**: 测试环境使用内存缓存，不依赖 Redis

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 MySQL 服务是否运行
   - 验证连接参数（主机、端口、用户名、密码）

2. **权限错误**
   - 确保数据库用户有 CREATE/DROP DATABASE 权限

3. **测试超时**
   - 检查数据库连接性能
   - 确保没有长时间运行的查询

### 调试命令

```bash
# 检查测试数据库是否存在
mysql -h localhost -P 33066 -u root -p -e "SHOW DATABASES LIKE 'promined_test_db';"

# 手动创建测试数据库
node scripts/setup-test-db.js setup

# 手动清理测试数据库
node scripts/setup-test-db.js cleanup
```

## 测试覆盖范围

当前 E2E 测试覆盖：

- ✅ 根端点测试
- ✅ 用户认证（注册、登录、获取用户信息）
- ✅ 用户管理
- ✅ 流程管理（创建、查询、更新、统计）
- ✅ 错误处理

所有测试都在真实的 MySQL 数据库环境中运行，确保与生产环境的一致性。
