import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { CacheModule } from '@nestjs/cache-manager';

// 导入基本模块
import { AuthModule } from '../src/auth/auth.module';
import { UsersModule } from '../src/users/users.module';
import { ProcessesModule } from '../src/processes/processes.module';
import { AnalysisModule } from '../src/analysis/analysis.module';
import { ConformanceModule } from '../src/conformance/conformance.module';

// 导入所有实体
import { User } from '../src/entities/user.entity';
import { Process } from '../src/entities/process.entity';
import { EventLog } from '../src/entities/event-log.entity';
import { AnalysisResult } from '../src/entities/analysis-result.entity';
import { BpmnModel } from '../src/entities/bpmn-model.entity';
import { ConformanceResult } from '../src/entities/conformance-result.entity';

// 导入控制器和服务
import { AppController } from '../src/app.controller';
import { AppService } from '../src/app.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env.test',
    }),
    TypeOrmModule.forRoot({
      type: process.env.DB_TYPE as any,
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT || '3306', 10),
      username: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_DATABASE,
      entities: [
        User,
        Process,
        EventLog,
        AnalysisResult,
        BpmnModel,
        ConformanceResult,
      ],
      synchronize: true,
      logging: false,
      dropSchema: true,
    }),
    PassportModule,
    JwtModule.register({
      secret: 'test-secret-key',
      signOptions: { expiresIn: '1h' },
    }),
    CacheModule.register({
      isGlobal: true,
      store: 'memory',
      ttl: 600,
    }),
    AuthModule,
    UsersModule,
    ProcessesModule,
    AnalysisModule,
    ConformanceModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class TestAppModule {}
