import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { CacheModule } from '@nestjs/cache-manager';
import { SubprocessDiscoveryService } from '../src/analysis/subprocess-discovery.service';
import { EventLog } from '../src/entities/event-log.entity';
import { AnalysisResult } from '../src/entities/analysis-result.entity';
import { CacheService } from '../src/analysis/cache.service';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';

describe('Memory Optimization Simple Tests (e2e)', () => {
  let app: INestApplication;
  let subprocessService: SubprocessDiscoveryService;
  let eventLogRepository: Repository<EventLog>;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        TypeOrmModule.forRoot({
          type: 'mysql',
          host: process.env.DB_HOST || 'localhost',
          port: parseInt(process.env.DB_PORT || '3306'),
          username: process.env.DB_USERNAME || 'root',
          password: process.env.DB_PASSWORD || 'password',
          database: process.env.DB_TEST_DATABASE || 'promined_test_db',
          entities: [EventLog, AnalysisResult],
          synchronize: true,
          dropSchema: true,
        }),
        TypeOrmModule.forFeature([EventLog, AnalysisResult]),
        CacheModule.register({
          store: 'memory',
          ttl: 600,
        }),
      ],
      providers: [SubprocessDiscoveryService, CacheService],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    subprocessService = moduleFixture.get<SubprocessDiscoveryService>(
      SubprocessDiscoveryService,
    );
    eventLogRepository = moduleFixture.get<Repository<EventLog>>(
      getRepositoryToken(EventLog),
    );
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    await eventLogRepository.clear();
  });

  describe('Memory Optimization Features', () => {
    it('should handle moderate dataset efficiently', async () => {
      const processId = 1;
      const testData: Partial<EventLog>[] = [];

      // 创建适中的测试数据集
      const caseCount = 100;
      const activitiesPerCase = 10;

      const activities = [
        '原材料采购申请',
        '供应商资质审核',
        '采购合同签署',
        '原材料入库检验',
        '入库确认',
        '生产准备',
        '首件检验',
        '批量生产',
        '过程检验',
        '成品检验',
      ];

      for (let caseId = 1; caseId <= caseCount; caseId++) {
        const baseTime = new Date('2024-01-01');

        for (
          let activityIndex = 0;
          activityIndex < activitiesPerCase;
          activityIndex++
        ) {
          const activity = activities[activityIndex % activities.length];
          const timestamp = new Date(
            baseTime.getTime() + caseId * 100000 + activityIndex * 60000,
          );

          testData.push({
            processId,
            caseId: `case_${caseId}`,
            activity,
            timestamp,
            resource: `resource_${(activityIndex % 3) + 1}`,
          });
        }
      }

      console.log(`Created ${testData.length} test events`);

      // 记录初始内存使用
      const initialMemory = process.memoryUsage();
      console.log(
        `Initial memory: ${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB`,
      );

      // 插入数据
      await eventLogRepository.save(testData);

      const afterInsertMemory = process.memoryUsage();
      console.log(
        `Memory after data insertion: ${Math.round(afterInsertMemory.heapUsed / 1024 / 1024)}MB`,
      );

      // 执行子流程发现
      const startTime = Date.now();
      const result = await subprocessService.discoverSubprocesses(processId, {
        minFrequency: 2,
        minLength: 2,
        maxLength: 4,
        confidenceThreshold: 0.1,
        enableParallelDetection: false,
        enableLoopDetection: false,
      });
      const endTime = Date.now();

      const finalMemory = process.memoryUsage();
      console.log(
        `Memory after subprocess discovery: ${Math.round(finalMemory.heapUsed / 1024 / 1024)}MB`,
      );
      console.log(`Processing time: ${endTime - startTime}ms`);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.subprocesses).toBeDefined();
      expect(result.hierarchicalDFG).toBeDefined();
      expect(result.hierarchicalDFG.nodes.length).toBeGreaterThan(0);
      expect(result.hierarchicalDFG.nodes.length).toBeLessThan(15); // 确保节点压缩有效

      console.log(`Found ${result.subprocesses.length} subprocesses`);
      console.log(
        `Hierarchical DFG has ${result.hierarchicalDFG.nodes.length} nodes`,
      );
    }, 60000); // 1分钟超时

    it('should trigger sampling for large dataset count', async () => {
      const processId = 2;

      // 模拟大数据集的计数
      jest.spyOn(eventLogRepository, 'count').mockResolvedValue(100000);

      const initialMemory = process.memoryUsage();
      console.log(
        `Initial memory for sampling test: ${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB`,
      );

      // 这应该触发采样逻辑
      const result = await subprocessService.discoverSubprocesses(processId, {
        minFrequency: 2,
        minLength: 2,
        maxLength: 3,
      });

      const finalMemory = process.memoryUsage();
      console.log(
        `Memory after sampling test: ${Math.round(finalMemory.heapUsed / 1024 / 1024)}MB`,
      );

      expect(result).toBeDefined();

      // 恢复mock
      jest.restoreAllMocks();
    });

    it('should use memory monitoring', async () => {
      const initialMemory = process.memoryUsage();
      console.log(
        `Memory monitoring test - Initial: ${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB`,
      );

      // 创建一些内存压力
      const largeArray = new Array(100000).fill('test data');

      const beforeGcMemory = process.memoryUsage();
      console.log(
        `Before GC: ${Math.round(beforeGcMemory.heapUsed / 1024 / 1024)}MB`,
      );

      // 强制垃圾回收
      if (global.gc) {
        global.gc();
        const afterGcMemory = process.memoryUsage();
        console.log(
          `After GC: ${Math.round(afterGcMemory.heapUsed / 1024 / 1024)}MB`,
        );
        expect(afterGcMemory.heapUsed).toBeLessThanOrEqual(
          beforeGcMemory.heapUsed,
        );
      } else {
        console.log('GC not exposed, skipping GC test');
      }

      // 清理引用
      largeArray.length = 0;
    });

    it('should handle business pattern detection', async () => {
      const processId = 3;
      const testData: Partial<EventLog>[] = [];

      // 创建包含业务模式的数据
      const businessSequence = [
        '原材料采购申请',
        '供应商资质审核',
        '采购合同签署',
      ];

      for (let caseId = 1; caseId <= 10; caseId++) {
        const baseTime = new Date('2024-01-01');

        businessSequence.forEach((activity, index) => {
          const timestamp = new Date(
            baseTime.getTime() + caseId * 100000 + index * 60000,
          );

          testData.push({
            processId,
            caseId: `case_${caseId}`,
            activity,
            timestamp,
            resource: `resource_${index + 1}`,
          });
        });
      }

      await eventLogRepository.save(testData);

      const result = await subprocessService.discoverSubprocesses(processId, {
        minFrequency: 2,
        minLength: 2,
        maxLength: 4,
        confidenceThreshold: 0.1,
      });

      expect(result.subprocesses.length).toBeGreaterThan(0);

      // 应该找到采购子流程
      const purchaseSubprocess = result.subprocesses.find(
        (sp) =>
          sp.name.includes('采购') || sp.activities.includes('原材料采购申请'),
      );
      expect(purchaseSubprocess).toBeDefined();

      console.log(
        `Business pattern test - Found subprocesses:`,
        result.subprocesses.map((sp) => ({
          name: sp.name,
          frequency: sp.frequency,
        })),
      );
    });
  });
});
