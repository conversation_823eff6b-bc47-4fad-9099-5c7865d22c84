import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { App } from 'supertest/types';
import { TestAppModule } from './test-app.module';
import { EventLog } from '../src/entities/event-log.entity';
import { Process } from '../src/entities/process.entity';
import { User } from '../src/entities/user.entity';
import { Repository, DataSource } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';

describe('Process Compare (e2e)', () => {
  let app: INestApplication<App>;
  let eventLogRepository: Repository<EventLog>;
  let processRepository: Repository<Process>;
  let userRepository: Repository<User>;
  let jwtService: JwtService;
  let dataSource: DataSource;
  let authToken: string;
  let testProcess: Process;
  let testUser: User;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [TestAppModule],
    }).compile();

    app = moduleFixture.createNestApplication();

    // 设置全局API前缀
    app.setGlobalPrefix('api/v1');

    // 全局验证管道
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      }),
    );

    // 获取数据源
    dataSource = moduleFixture.get<DataSource>(DataSource);

    await app.init();

    eventLogRepository = moduleFixture.get<Repository<EventLog>>(
      getRepositoryToken(EventLog),
    );
    processRepository = moduleFixture.get<Repository<Process>>(
      getRepositoryToken(Process),
    );
    userRepository = moduleFixture.get<Repository<User>>(
      getRepositoryToken(User),
    );
    jwtService = moduleFixture.get<JwtService>(JwtService);

    // Create test user
    testUser = await userRepository.save({
      username: 'testuser',
      email: '<EMAIL>',
      passwordHash: 'hashedpassword',
      fullName: '测试用户',
    });

    // Generate auth token
    authToken = jwtService.sign({
      sub: testUser.id,
      username: testUser.username,
    });

    // Create test process
    testProcess = await processRepository.save({
      name: 'Test Process for Comparison',
      description: 'Test process for e2e comparison testing',
      userId: testUser.id,
    });

    // Create test event logs for base time period (2024-01-01 to 2024-01-07)
    const baseEventLogs = [
      {
        processId: testProcess.id,
        caseId: 'case1',
        activity: 'Start',
        timestamp: new Date('2024-01-01T09:00:00'),
        endTimestamp: new Date('2024-01-01T09:30:00'),
      },
      {
        processId: testProcess.id,
        caseId: 'case1',
        activity: 'Task A',
        timestamp: new Date('2024-01-01T10:00:00'),
        endTimestamp: new Date('2024-01-01T11:00:00'),
      },
      {
        processId: testProcess.id,
        caseId: 'case1',
        activity: 'End',
        timestamp: new Date('2024-01-01T11:30:00'),
        endTimestamp: new Date('2024-01-01T12:00:00'),
      },
      {
        processId: testProcess.id,
        caseId: 'case2',
        activity: 'Start',
        timestamp: new Date('2024-01-02T09:00:00'),
        endTimestamp: new Date('2024-01-02T09:30:00'),
      },
      {
        processId: testProcess.id,
        caseId: 'case2',
        activity: 'Task A',
        timestamp: new Date('2024-01-02T10:00:00'),
        endTimestamp: new Date('2024-01-02T11:00:00'),
      },
      {
        processId: testProcess.id,
        caseId: 'case2',
        activity: 'Task B',
        timestamp: new Date('2024-01-02T11:30:00'),
        endTimestamp: new Date('2024-01-02T12:30:00'),
      },
      {
        processId: testProcess.id,
        caseId: 'case2',
        activity: 'End',
        timestamp: new Date('2024-01-02T13:00:00'),
        endTimestamp: new Date('2024-01-02T13:30:00'),
      },
    ];

    // Create test event logs for compare time period (2023-12-25 to 2023-12-31)
    const compareEventLogs = [
      {
        processId: testProcess.id,
        caseId: 'case3',
        activity: 'Start',
        timestamp: new Date('2023-12-25T09:00:00'),
        endTimestamp: new Date('2023-12-25T09:30:00'),
      },
      {
        processId: testProcess.id,
        caseId: 'case3',
        activity: 'Task A',
        timestamp: new Date('2023-12-25T10:00:00'),
        endTimestamp: new Date('2023-12-25T11:00:00'),
      },
      {
        processId: testProcess.id,
        caseId: 'case3',
        activity: 'End',
        timestamp: new Date('2023-12-25T11:30:00'),
        endTimestamp: new Date('2023-12-25T12:00:00'),
      },
      {
        processId: testProcess.id,
        caseId: 'case4',
        activity: 'Start',
        timestamp: new Date('2023-12-26T09:00:00'),
        endTimestamp: new Date('2023-12-26T09:30:00'),
      },
      {
        processId: testProcess.id,
        caseId: 'case4',
        activity: 'Task C',
        timestamp: new Date('2023-12-26T10:00:00'),
        endTimestamp: new Date('2023-12-26T11:00:00'),
      },
      {
        processId: testProcess.id,
        caseId: 'case4',
        activity: 'End',
        timestamp: new Date('2023-12-26T11:30:00'),
        endTimestamp: new Date('2023-12-26T12:00:00'),
      },
    ];

    await eventLogRepository.save([...baseEventLogs, ...compareEventLogs]);
  }, 30000); // 增加超时时间

  afterAll(async () => {
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }
    if (app) {
      await app.close();
    }
  });

  describe('/analysis/compare/:processId (POST)', () => {
    it('should perform process comparison successfully', async () => {
      const compareOptions = {
        baseStartTime: '2024-01-01T00:00:00.000Z',
        baseEndTime: '2024-01-07T23:59:59.999Z',
        offsetDays: 7,
        dimension: 'frequency',
        displayMode: 'absolute',
        forceRefresh: true,
      };

      const response = await request(app.getHttpServer())
        .post(`/api/v1/analysis/compare/${testProcess.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(compareOptions)
        .expect(201);

      expect(response.body).toHaveProperty('baseDfg');
      expect(response.body).toHaveProperty('compareDfg');
      expect(response.body).toHaveProperty('compareStatistics');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('fromCache');

      // Verify DFG structure
      expect(response.body.baseDfg).toHaveProperty('nodes');
      expect(response.body.baseDfg).toHaveProperty('edges');
      expect(response.body.baseDfg).toHaveProperty('statistics');

      expect(response.body.compareDfg).toHaveProperty('nodes');
      expect(response.body.compareDfg).toHaveProperty('edges');
      expect(response.body.compareDfg).toHaveProperty('statistics');

      // Verify comparison statistics
      const { compareStatistics } = response.body;
      expect(compareStatistics).toHaveProperty('baseTimeRange');
      expect(compareStatistics).toHaveProperty('compareTimeRange');
      expect(compareStatistics).toHaveProperty('offsetDays', 7);
      expect(compareStatistics).toHaveProperty('changeMetrics');

      // Verify time ranges
      expect(compareStatistics.baseTimeRange.start).toBe(
        compareOptions.baseStartTime,
      );
      expect(compareStatistics.baseTimeRange.end).toBe(
        compareOptions.baseEndTime,
      );

      // Verify case counts
      expect(compareStatistics.baseCaseCount).toBe(2);
      expect(compareStatistics.compareCaseCount).toBe(2);

      // Verify activity analysis
      expect(compareStatistics.commonActivities).toContain('Start');
      expect(compareStatistics.commonActivities).toContain('End');
      expect(compareStatistics.uniqueBaseActivities).toContain('Task B');
      expect(compareStatistics.uniqueCompareActivities).toContain('Task C');
    });

    it('should handle required activities filter', async () => {
      const compareOptions = {
        baseStartTime: '2024-01-01T00:00:00.000Z',
        baseEndTime: '2024-01-07T23:59:59.999Z',
        offsetDays: 7,
        requiredActivities: ['Task A'],
        forceRefresh: true,
      };

      const response = await request(app.getHttpServer())
        .post(`/api/v1/analysis/compare/${testProcess.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(compareOptions)
        .expect(201);

      expect(response.body).toHaveProperty('baseDfg');
      expect(response.body).toHaveProperty('compareDfg');

      // Should only include cases with Task A
      expect(response.body.compareStatistics.baseCaseCount).toBe(1);
      expect(response.body.compareStatistics.compareCaseCount).toBe(1);
    });

    it('should not filter when requiredActivities contains ALL_NODES', async () => {
      const compareOptions = {
        baseStartTime: '2024-01-01T00:00:00.000Z',
        baseEndTime: '2024-01-07T23:59:59.999Z',
        offsetDays: 7,
        requiredActivities: ['ALL_NODES'],
        forceRefresh: true,
      };

      const response = await request(app.getHttpServer())
        .post(`/api/v1/analysis/compare/${testProcess.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(compareOptions)
        .expect(201);

      expect(response.body).toHaveProperty('baseDfg');
      expect(response.body).toHaveProperty('compareDfg');

      // Should include all cases (same as no filter)
      expect(response.body.compareStatistics.baseCaseCount).toBe(2);
      expect(response.body.compareStatistics.compareCaseCount).toBe(2);
    });

    it('should handle different dimensions', async () => {
      const dimensions = ['frequency', 'duration'];

      for (const dimension of dimensions) {
        const compareOptions = {
          baseStartTime: '2024-01-01T00:00:00.000Z',
          baseEndTime: '2024-01-07T23:59:59.999Z',
          offsetDays: 7,
          dimension,
          forceRefresh: true,
        };

        const response = await request(app.getHttpServer())
          .post(`/api/v1/analysis/compare/${testProcess.id}`)
          .set('Authorization', `Bearer ${authToken}`)
          .send(compareOptions)
          .expect(201);

        expect(response.body).toHaveProperty('baseDfg');
        expect(response.body).toHaveProperty('compareDfg');
      }
    });

    it('should handle different display modes', async () => {
      const displayModes = ['absolute', 'difference', 'percentage'];

      for (const displayMode of displayModes) {
        const compareOptions = {
          baseStartTime: '2024-01-01T00:00:00.000Z',
          baseEndTime: '2024-01-07T23:59:59.999Z',
          offsetDays: 7,
          displayMode,
          forceRefresh: true,
        };

        const response = await request(app.getHttpServer())
          .post(`/api/v1/analysis/compare/${testProcess.id}`)
          .set('Authorization', `Bearer ${authToken}`)
          .send(compareOptions)
          .expect(201);

        expect(response.body).toHaveProperty('baseDfg');
        expect(response.body).toHaveProperty('compareDfg');
      }
    });

    it('should return 400 for invalid request body', async () => {
      const invalidOptions = {
        // Missing required fields
        offsetDays: 7,
      };

      await request(app.getHttpServer())
        .post(`/api/v1/analysis/compare/${testProcess.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidOptions)
        .expect(400);
    });

    it('should return 401 without authentication', async () => {
      const compareOptions = {
        baseStartTime: '2024-01-01T00:00:00.000Z',
        baseEndTime: '2024-01-07T23:59:59.999Z',
        offsetDays: 7,
      };

      await request(app.getHttpServer())
        .post(`/api/v1/analysis/compare/${testProcess.id}`)
        .send(compareOptions)
        .expect(401);
    });

    it('should return 404 for non-existent process', async () => {
      const compareOptions = {
        baseStartTime: '2024-01-01T00:00:00.000Z',
        baseEndTime: '2024-01-07T23:59:59.999Z',
        offsetDays: 7,
      };

      await request(app.getHttpServer())
        .post('/api/v1/analysis/compare/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .send(compareOptions)
        .expect(404);
    });

    it('should handle edge case with no data in time ranges', async () => {
      const compareOptions = {
        baseStartTime: '2025-01-01T00:00:00.000Z',
        baseEndTime: '2025-01-07T23:59:59.999Z',
        offsetDays: 7,
        forceRefresh: true,
      };

      const response = await request(app.getHttpServer())
        .post(`/api/v1/analysis/compare/${testProcess.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(compareOptions)
        .expect(201);

      expect(response.body.baseDfg.nodes).toEqual([]);
      expect(response.body.baseDfg.edges).toEqual([]);
      expect(response.body.compareDfg.nodes).toEqual([]);
      expect(response.body.compareDfg.edges).toEqual([]);
      expect(response.body.compareStatistics.baseCaseCount).toBe(0);
      expect(response.body.compareStatistics.compareCaseCount).toBe(0);
    });
  });
});
