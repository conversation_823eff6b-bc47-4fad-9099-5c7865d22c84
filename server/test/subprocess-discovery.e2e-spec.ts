import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { TestAppModule } from './test-app.module';
import { DataSource } from 'typeorm';
import { EventLog } from '../src/entities/event-log.entity';
import { Process } from '../src/entities/process.entity';
import { User } from '../src/entities/user.entity';
import { JwtService } from '@nestjs/jwt';

describe('Subprocess Discovery (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let jwtService: JwtService;
  let authToken: string;
  let testProcessId: number;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [TestAppModule],
    }).compile();

    app = moduleFixture.createNestApplication();

    // 设置全局API前缀
    app.setGlobalPrefix('api/v1');

    // 全局验证管道
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      }),
    );

    await app.init();

    dataSource = moduleFixture.get<DataSource>(DataSource);
    jwtService = moduleFixture.get<JwtService>(JwtService);

    // 创建测试用户和认证token
    const testUser = await dataSource.getRepository(User).save({
      username: 'testuser',
      email: '<EMAIL>',
      passwordHash: 'hashedpassword',
    });

    authToken = jwtService.sign({
      sub: testUser.id,
      username: testUser.username,
    });

    // 创建测试流程
    const testProcess = await dataSource.getRepository(Process).save({
      name: '制造业质量管理流程',
      description: '测试子流程发现功能',
      userId: testUser.id,
    });
    testProcessId = testProcess.id;

    // 插入测试事件日志数据
    await insertTestEventLogs();
  });

  afterAll(async () => {
    // 清理测试数据
    await dataSource
      .getRepository(EventLog)
      .delete({ processId: testProcessId });
    await dataSource.getRepository(Process).delete({ id: testProcessId });
    await dataSource.getRepository(User).delete({});

    await app.close();
  });

  const insertTestEventLogs = async () => {
    const eventLogs = [
      // QM_001 - 正常流程
      {
        processId: testProcessId,
        caseId: 'QM_001',
        activity: '原材料采购申请',
        timestamp: new Date('2024-01-15 08:00:00'),
        resource: '采购经理_张明',
      },
      {
        processId: testProcessId,
        caseId: 'QM_001',
        activity: '供应商资质审核',
        timestamp: new Date('2024-01-15 09:30:00'),
        resource: '质量工程师_李华',
      },
      {
        processId: testProcessId,
        caseId: 'QM_001',
        activity: '采购合同签署',
        timestamp: new Date('2024-01-15 11:00:00'),
        resource: '采购主管_王强',
      },
      {
        processId: testProcessId,
        caseId: 'QM_001',
        activity: '原材料入库检验',
        timestamp: new Date('2024-01-16 09:00:00'),
        resource: '检验员_赵敏',
      },
      {
        processId: testProcessId,
        caseId: 'QM_001',
        activity: '入库确认',
        timestamp: new Date('2024-01-16 10:30:00'),
        resource: '仓库管理员_钱伟',
      },
      {
        processId: testProcessId,
        caseId: 'QM_001',
        activity: '生产计划制定',
        timestamp: new Date('2024-01-16 14:00:00'),
        resource: '生产计划员_孙丽',
      },
      {
        processId: testProcessId,
        caseId: 'QM_001',
        activity: '生产准备',
        timestamp: new Date('2024-01-17 08:00:00'),
        resource: '生产主管_周杰',
      },
      {
        processId: testProcessId,
        caseId: 'QM_001',
        activity: '首件检验',
        timestamp: new Date('2024-01-17 09:30:00'),
        resource: '质量检验员_吴琳',
      },
      {
        processId: testProcessId,
        caseId: 'QM_001',
        activity: '批量生产',
        timestamp: new Date('2024-01-17 10:00:00'),
        resource: '生产操作员_郑涛',
      },
      {
        processId: testProcessId,
        caseId: 'QM_001',
        activity: '过程检验',
        timestamp: new Date('2024-01-17 14:00:00'),
        resource: '质量检验员_冯雪',
      },
      {
        processId: testProcessId,
        caseId: 'QM_001',
        activity: '成品检验',
        timestamp: new Date('2024-01-18 09:00:00'),
        resource: '质量工程师_陈阳',
      },
      {
        processId: testProcessId,
        caseId: 'QM_001',
        activity: '包装',
        timestamp: new Date('2024-01-18 11:00:00'),
        resource: '包装工_褚明',
      },
      {
        processId: testProcessId,
        caseId: 'QM_001',
        activity: '出库检验',
        timestamp: new Date('2024-01-18 14:00:00'),
        resource: '出库检验员_卫强',
      },
      {
        processId: testProcessId,
        caseId: 'QM_001',
        activity: '发货',
        timestamp: new Date('2024-01-18 16:00:00'),
        resource: '物流专员_蒋丽',
      },
      {
        processId: testProcessId,
        caseId: 'QM_001',
        activity: '客户验收',
        timestamp: new Date('2024-01-19 10:00:00'),
        resource: '客户代表_沈杰',
      },

      // QM_002 - 包含不合格品处理的流程
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '原材料采购申请',
        timestamp: new Date('2024-01-15 10:00:00'),
        resource: '采购经理_张明',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '供应商资质审核',
        timestamp: new Date('2024-01-15 11:30:00'),
        resource: '质量工程师_李华',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '采购合同签署',
        timestamp: new Date('2024-01-15 13:00:00'),
        resource: '采购主管_王强',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '原材料入库检验',
        timestamp: new Date('2024-01-16 11:00:00'),
        resource: '检验员_赵敏',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '不合格品处理',
        timestamp: new Date('2024-01-16 13:00:00'),
        resource: '质量工程师_李华',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '供应商整改通知',
        timestamp: new Date('2024-01-16 15:00:00'),
        resource: '采购主管_王强',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '重新检验',
        timestamp: new Date('2024-01-17 09:00:00'),
        resource: '检验员_赵敏',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '入库确认',
        timestamp: new Date('2024-01-17 10:30:00'),
        resource: '仓库管理员_钱伟',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '生产计划制定',
        timestamp: new Date('2024-01-17 14:00:00'),
        resource: '生产计划员_孙丽',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '生产准备',
        timestamp: new Date('2024-01-18 08:00:00'),
        resource: '生产主管_周杰',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '首件检验',
        timestamp: new Date('2024-01-18 09:30:00'),
        resource: '质量检验员_吴琳',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '批量生产',
        timestamp: new Date('2024-01-18 10:00:00'),
        resource: '生产操作员_郑涛',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '过程检验',
        timestamp: new Date('2024-01-18 14:00:00'),
        resource: '质量检验员_冯雪',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '返工处理',
        timestamp: new Date('2024-01-18 16:00:00'),
        resource: '生产操作员_郑涛',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '重新检验',
        timestamp: new Date('2024-01-19 09:00:00'),
        resource: '质量检验员_冯雪',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '成品检验',
        timestamp: new Date('2024-01-19 11:00:00'),
        resource: '质量工程师_陈阳',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '包装',
        timestamp: new Date('2024-01-19 13:00:00'),
        resource: '包装工_褚明',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '出库检验',
        timestamp: new Date('2024-01-19 15:00:00'),
        resource: '出库检验员_卫强',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '发货',
        timestamp: new Date('2024-01-19 17:00:00'),
        resource: '物流专员_蒋丽',
      },
      {
        processId: testProcessId,
        caseId: 'QM_002',
        activity: '客户验收',
        timestamp: new Date('2024-01-20 10:00:00'),
        resource: '客户代表_沈杰',
      },

      // QM_003 - 另一个正常流程实例
      {
        processId: testProcessId,
        caseId: 'QM_003',
        activity: '原材料采购申请',
        timestamp: new Date('2024-01-16 08:00:00'),
        resource: '采购经理_张明',
      },
      {
        processId: testProcessId,
        caseId: 'QM_003',
        activity: '供应商资质审核',
        timestamp: new Date('2024-01-16 09:30:00'),
        resource: '质量工程师_李华',
      },
      {
        processId: testProcessId,
        caseId: 'QM_003',
        activity: '采购合同签署',
        timestamp: new Date('2024-01-16 13:00:00'),
        resource: '采购主管_王强',
      },
      {
        processId: testProcessId,
        caseId: 'QM_003',
        activity: '原材料入库检验',
        timestamp: new Date('2024-01-17 09:00:00'),
        resource: '检验员_赵敏',
      },
      {
        processId: testProcessId,
        caseId: 'QM_003',
        activity: '入库确认',
        timestamp: new Date('2024-01-17 14:00:00'),
        resource: '仓库管理员_钱伟',
      },
      {
        processId: testProcessId,
        caseId: 'QM_003',
        activity: '生产计划制定',
        timestamp: new Date('2024-01-17 16:00:00'),
        resource: '生产计划员_孙丽',
      },
      {
        processId: testProcessId,
        caseId: 'QM_003',
        activity: '生产准备',
        timestamp: new Date('2024-01-18 09:30:00'),
        resource: '生产主管_周杰',
      },
      {
        processId: testProcessId,
        caseId: 'QM_003',
        activity: '首件检验',
        timestamp: new Date('2024-01-18 11:00:00'),
        resource: '质量检验员_吴琳',
      },
      {
        processId: testProcessId,
        caseId: 'QM_003',
        activity: '批量生产',
        timestamp: new Date('2024-01-18 11:30:00'),
        resource: '生产操作员_郑涛',
      },
      {
        processId: testProcessId,
        caseId: 'QM_003',
        activity: '过程检验',
        timestamp: new Date('2024-01-18 15:30:00'),
        resource: '质量检验员_冯雪',
      },
      {
        processId: testProcessId,
        caseId: 'QM_003',
        activity: '成品检验',
        timestamp: new Date('2024-01-19 09:00:00'),
        resource: '质量工程师_陈阳',
      },
      {
        processId: testProcessId,
        caseId: 'QM_003',
        activity: '包装',
        timestamp: new Date('2024-01-19 11:00:00'),
        resource: '包装工_褚明',
      },
      {
        processId: testProcessId,
        caseId: 'QM_003',
        activity: '出库检验',
        timestamp: new Date('2024-01-19 13:00:00'),
        resource: '出库检验员_卫强',
      },
      {
        processId: testProcessId,
        caseId: 'QM_003',
        activity: '发货',
        timestamp: new Date('2024-01-19 16:00:00'),
        resource: '物流专员_蒋丽',
      },
      {
        processId: testProcessId,
        caseId: 'QM_003',
        activity: '客户验收',
        timestamp: new Date('2024-01-20 10:00:00'),
        resource: '客户代表_沈杰',
      },
    ];

    await dataSource.getRepository(EventLog).save(eventLogs);
  };

  describe('/api/v1/analysis/subprocess-discovery/:processId (POST)', () => {
    it('should discover subprocesses successfully', async () => {
      const response = await request(app.getHttpServer())
        .post(`/api/v1/analysis/subprocess-discovery/${testProcessId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          minFrequency: 2,
          minLength: 2,
          maxLength: 5,
          confidenceThreshold: 0.6,
          enableParallelDetection: true,
          enableLoopDetection: true,
        })
        .expect(201);

      expect(response.body).toHaveProperty('subprocesses');
      expect(response.body).toHaveProperty('hierarchicalDFG');
      expect(response.body).toHaveProperty('statistics');

      // 验证子流程数组
      expect(Array.isArray(response.body.subprocesses)).toBe(true);
      expect(response.body.subprocesses.length).toBeGreaterThan(0);

      // 验证第一个子流程的结构
      const firstSubprocess = response.body.subprocesses[0];
      expect(firstSubprocess).toHaveProperty('id');
      expect(firstSubprocess).toHaveProperty('name');
      expect(firstSubprocess).toHaveProperty('activities');
      expect(firstSubprocess).toHaveProperty('frequency');
      expect(firstSubprocess).toHaveProperty('type');
      expect(firstSubprocess).toHaveProperty('confidence');

      // 验证层次化DFG
      expect(response.body.hierarchicalDFG).toHaveProperty('nodes');
      expect(response.body.hierarchicalDFG).toHaveProperty('edges');
      expect(Array.isArray(response.body.hierarchicalDFG.nodes)).toBe(true);
      expect(Array.isArray(response.body.hierarchicalDFG.edges)).toBe(true);

      // 验证统计信息
      expect(response.body.statistics).toHaveProperty('totalSubprocesses');
      expect(response.body.statistics).toHaveProperty('compressionRatio');
      expect(response.body.statistics.totalSubprocesses).toBeGreaterThan(0);
    });

    it('should return 401 without authentication', async () => {
      await request(app.getHttpServer())
        .post(`/api/v1/analysis/subprocess-discovery/${testProcessId}`)
        .send({})
        .expect(401);
    });

    it('should handle invalid process ID', async () => {
      await request(app.getHttpServer())
        .post('/api/v1/analysis/subprocess-discovery/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .send({})
        .expect(400);
    });
  });

  describe('/api/v1/analysis/hierarchical-dfg/:processId (POST)', () => {
    it('should generate hierarchical DFG successfully', async () => {
      const response = await request(app.getHttpServer())
        .post(`/api/v1/analysis/hierarchical-dfg/${testProcessId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          minFrequency: 2,
          minLength: 2,
          maxLength: 5,
        })
        .expect(201);

      expect(response.body).toHaveProperty('hierarchicalDFG');
      expect(response.body).toHaveProperty('statistics');

      // 验证层次化DFG结构
      expect(response.body.hierarchicalDFG).toHaveProperty('nodes');
      expect(response.body.hierarchicalDFG).toHaveProperty('edges');

      // 验证节点数量应该少于原始活动数量（由于子流程压缩）
      expect(response.body.hierarchicalDFG.nodes.length).toBeLessThan(15);
    });
  });

  describe('/api/v1/analysis/subprocess-patterns/:processId (GET)', () => {
    it('should get subprocess patterns successfully', async () => {
      // 先执行子流程发现
      await request(app.getHttpServer())
        .post(`/api/v1/analysis/subprocess-discovery/${testProcessId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({});

      // 获取子流程模式
      const response = await request(app.getHttpServer())
        .get(`/api/v1/analysis/subprocess-patterns/${testProcessId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('patterns');
      expect(response.body).toHaveProperty('statistics');
      expect(Array.isArray(response.body.patterns)).toBe(true);
      expect(response.body.statistics).toHaveProperty('totalPatterns');
      expect(response.body.statistics).toHaveProperty('byType');
    });
  });
});
