import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { App } from 'supertest/types';
import { TestAppModule } from './test-app.module';
import { DataSource } from 'typeorm';
import { EventLog } from '../src/entities/event-log.entity';
import * as fs from 'fs';
import * as path from 'path';

describe('Extended Fields Data Upload (e2e)', () => {
  let app: INestApplication<App>;
  let authToken: string;
  let dataSource: DataSource;
  let processId: number;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [TestAppModule],
    }).compile();

    app = moduleFixture.createNestApplication();

    // 设置全局API前缀
    app.setGlobalPrefix('api/v1');

    // 全局验证管道
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      }),
    );

    // 获取数据源
    dataSource = moduleFixture.get<DataSource>(DataSource);

    await app.init();

    // 注册用户并获取token
    const registerResponse = await request(app.getHttpServer())
      .post('/api/v1/auth/register')
      .send({
        username: 'testuser_extended',
        email: '<EMAIL>',
        password: 'password123',
        fullName: 'Test User Extended',
      });

    authToken = registerResponse.body.access_token;

    // 创建测试流程
    const processResponse = await request(app.getHttpServer())
      .post('/api/v1/processes')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        name: 'Extended Fields Test Process',
        description: 'Test process for extended fields functionality',
      });

    processId = processResponse.body.id;
  }, 30000);

  afterAll(async () => {
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }
    if (app) {
      await app.close();
    }
  });

  describe('Data Upload with Extended Fields', () => {
    it('should upload CSV data with all extended fields', async () => {
      // 创建测试CSV文件
      const csvContent = `case_id,activity,timestamp,resource,cost,activity_id,previous_activity,end_timestamp
1,Start,2023-01-01T10:00:00Z,User1,100,ACT001,,2023-01-01T10:30:00Z
1,Process,2023-01-01T11:00:00Z,User2,200,ACT002,Start,2023-01-01T11:45:00Z
1,End,2023-01-01T12:00:00Z,User1,50,ACT003,Process,2023-01-01T12:15:00Z
2,Start,2023-01-02T09:00:00Z,User3,100,ACT001,,2023-01-02T09:20:00Z
2,Process,2023-01-02T10:00:00Z,User2,250,ACT002,Start,2023-01-02T10:30:00Z`;

      const testFilePath = path.join(__dirname, 'test-extended-fields.csv');
      fs.writeFileSync(testFilePath, csvContent);

      try {
        const response = await request(app.getHttpServer())
          .post('/api/v1/analysis/upload')
          .set('Authorization', `Bearer ${authToken}`)
          .field('processId', processId.toString())
          .field('caseIdField', 'case_id')
          .field('activityField', 'activity')
          .field('timestampField', 'timestamp')
          .field('resourceField', 'resource')
          .field('costField', 'cost')
          .field('activityIdField', 'activity_id')
          .field('previousActivityField', 'previous_activity')
          .field('endTimestampField', 'end_timestamp')
          .attach('file', testFilePath);

        expect(response.status).toBe(201);
        expect(response.body.success).toBe(true);
        expect(response.body.savedRecords).toBe(5);

        // 验证数据库中的数据
        const eventLogRepository = dataSource.getRepository(EventLog);
        const savedLogs = await eventLogRepository.find({
          where: { processId },
          order: { timestamp: 'ASC' },
        });

        expect(savedLogs).toHaveLength(5);

        // 验证第一条记录
        const firstLog = savedLogs[0];
        expect(firstLog.caseId).toBe('1');
        expect(firstLog.activity).toBe('Start');
        expect(firstLog.activityId).toBe('ACT001');
        expect(firstLog.previousActivity).toBeNull();
        expect(firstLog.endTimestamp).toEqual(new Date('2023-01-01T10:30:00Z'));

        // 验证第二条记录
        const secondLog = savedLogs[1];
        expect(secondLog.caseId).toBe('1');
        expect(secondLog.activity).toBe('Process');
        expect(secondLog.activityId).toBe('ACT002');
        expect(secondLog.previousActivity).toBe('Start');
        expect(secondLog.endTimestamp).toEqual(
          new Date('2023-01-01T11:45:00Z'),
        );
      } finally {
        // 清理测试文件
        if (fs.existsSync(testFilePath)) {
          fs.unlinkSync(testFilePath);
        }
      }
    });

    it('should upload CSV data with partial extended fields', async () => {
      // 创建只包含部分扩展字段的测试CSV文件
      const csvContent = `case_id,activity,timestamp,activity_id
3,Start,2023-01-03T10:00:00Z,ACT001
3,Process,2023-01-03T11:00:00Z,ACT002
3,End,2023-01-03T12:00:00Z,ACT003`;

      const testFilePath = path.join(
        __dirname,
        'test-partial-extended-fields.csv',
      );
      fs.writeFileSync(testFilePath, csvContent);

      try {
        const response = await request(app.getHttpServer())
          .post('/api/v1/analysis/upload')
          .set('Authorization', `Bearer ${authToken}`)
          .field('processId', processId.toString())
          .field('caseIdField', 'case_id')
          .field('activityField', 'activity')
          .field('timestampField', 'timestamp')
          .field('activityIdField', 'activity_id')
          .attach('file', testFilePath);

        expect(response.status).toBe(201);
        expect(response.body.success).toBe(true);
        expect(response.body.savedRecords).toBe(3);

        // 验证数据库中的数据
        const eventLogRepository = dataSource.getRepository(EventLog);
        const savedLogs = await eventLogRepository.find({
          where: { processId, caseId: '3' },
          order: { timestamp: 'ASC' },
        });

        expect(savedLogs).toHaveLength(3);

        // 验证字段值
        savedLogs.forEach((log) => {
          expect(log.activityId).toMatch(/^ACT00[1-3]$/);
          expect(log.previousActivity).toBeNull();
          expect(log.endTimestamp).toBeNull();
          expect(log.resource).toBeNull();
          expect(log.cost).toBeNull();
        });
      } finally {
        // 清理测试文件
        if (fs.existsSync(testFilePath)) {
          fs.unlinkSync(testFilePath);
        }
      }
    });

    it('should handle invalid end timestamp format gracefully', async () => {
      // 创建包含无效时间格式的测试CSV文件
      const csvContent = `case_id,activity,timestamp,end_timestamp
4,Start,2023-01-04T10:00:00Z,invalid-date
4,Process,2023-01-04T11:00:00Z,2023-01-04T11:30:00Z`;

      const testFilePath = path.join(__dirname, 'test-invalid-timestamp.csv');
      fs.writeFileSync(testFilePath, csvContent);

      try {
        const response = await request(app.getHttpServer())
          .post('/api/v1/analysis/upload')
          .set('Authorization', `Bearer ${authToken}`)
          .field('processId', processId.toString())
          .field('caseIdField', 'case_id')
          .field('activityField', 'activity')
          .field('timestampField', 'timestamp')
          .field('endTimestampField', 'end_timestamp')
          .attach('file', testFilePath);

        expect(response.status).toBe(201);
        expect(response.body.success).toBe(true);

        // 验证数据库中的数据
        const eventLogRepository = dataSource.getRepository(EventLog);
        const savedLogs = await eventLogRepository.find({
          where: { processId, caseId: '4' },
          order: { timestamp: 'ASC' },
        });

        expect(savedLogs).toHaveLength(2);

        // 第一条记录应该没有endTimestamp（因为格式无效）
        expect(savedLogs[0].endTimestamp).toBeNull();

        // 第二条记录应该有有效的endTimestamp
        expect(savedLogs[1].endTimestamp).toEqual(
          new Date('2023-01-04T11:30:00Z'),
        );
      } finally {
        // 清理测试文件
        if (fs.existsSync(testFilePath)) {
          fs.unlinkSync(testFilePath);
        }
      }
    });
  });
});
