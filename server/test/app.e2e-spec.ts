import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { App } from 'supertest/types';
import { TestAppModule } from './test-app.module';
import { DataSource } from 'typeorm';
import * as fs from 'fs';
import * as path from 'path';

describe('ProMax API (e2e)', () => {
  let app: INestApplication<App>;
  let authToken: string;
  let dataSource: DataSource;
  let registeredUserId: number;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [TestAppModule],
    }).compile();

    app = moduleFixture.createNestApplication();

    // 设置全局API前缀
    app.setGlobalPrefix('api/v1');

    // 全局验证管道
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      }),
    );

    // 获取数据源
    dataSource = moduleFixture.get<DataSource>(DataSource);

    await app.init();
  }, 30000); // 增加超时时间

  afterAll(async () => {
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }
    if (app) {
      await app.close();
    }
  });

  describe('Root endpoint', () => {
    it('/ (GET) - should return Hello World', () => {
      return request(app.getHttpServer())
        .get('/api/v1')
        .expect(200)
        .expect('Hello World!');
    });
  });

  describe('Authentication', () => {
    const testUser = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123',
      fullName: '测试用户',
    };

    it('/api/v1/auth/register (POST) - should register a new user', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/register')
        .send(testUser)
        .expect(201);

      expect(response.body).toHaveProperty('access_token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.username).toBe(testUser.username);
      expect(response.body.user.email).toBe(testUser.email);

      // 保存token和用户ID供后续测试使用
      authToken = response.body.access_token;
      registeredUserId = response.body.user.id;
    });

    it('/api/v1/auth/login (POST) - should login with valid credentials', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/login')
        .send({
          usernameOrEmail: testUser.username,
          password: testUser.password,
        })
        .expect(200);

      expect(response.body).toHaveProperty('access_token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.username).toBe(testUser.username);

      // 更新token
      authToken = response.body.access_token;
    });

    it('/api/v1/auth/login (POST) - should fail with invalid credentials', () => {
      return request(app.getHttpServer())
        .post('/api/v1/auth/login')
        .send({
          usernameOrEmail: testUser.username,
          password: 'wrongpassword',
        })
        .expect(401);
    });

    it('/api/v1/auth/profile (GET) - should get user profile with valid token', () => {
      return request(app.getHttpServer())
        .get('/api/v1/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.username).toBe(testUser.username);
          expect(res.body.email).toBe(testUser.email);
        });
    });

    it('/api/v1/auth/profile (GET) - should fail without token', () => {
      return request(app.getHttpServer())
        .get('/api/v1/auth/profile')
        .expect(401);
    });
  });

  describe('Users', () => {
    it('/api/v1/users (GET) - should get all users', () => {
      return request(app.getHttpServer())
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });

    it('/api/v1/users/profile (GET) - should get current user profile', () => {
      return request(app.getHttpServer())
        .get('/api/v1/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe(registeredUserId);
        });
    });
  });

  describe('Processes', () => {
    let processId: number;

    it('/api/v1/processes (POST) - should create a new process', async () => {
      const newProcess = {
        name: '测试流程',
        description: '这是一个测试流程',
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/processes')
        .set('Authorization', `Bearer ${authToken}`)
        .send(newProcess)
        .expect(201);

      expect(response.body.name).toBe(newProcess.name);
      expect(response.body.description).toBe(newProcess.description);
      processId = response.body.id;

      // 确保 processId 是有效的数字
      expect(typeof processId).toBe('number');
      expect(processId).toBeGreaterThan(0);
    });

    it('/api/v1/processes (GET) - should get all processes', () => {
      return request(app.getHttpServer())
        .get('/api/v1/processes')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });

    it('/api/v1/processes/statistics (GET) - should get process statistics', () => {
      return request(app.getHttpServer())
        .get('/api/v1/processes/statistics')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
    });

    it('/api/v1/processes/:id (GET) - should get process by id', () => {
      return request(app.getHttpServer())
        .get(`/api/v1/processes/${processId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.id).toBe(processId);
        });
    });

    it('/api/v1/processes/:id (PATCH) - should update process', () => {
      const updateData = {
        name: '更新的流程名称',
        description: '更新的流程描述',
      };

      return request(app.getHttpServer())
        .patch(`/api/v1/processes/${processId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200)
        .expect((res) => {
          expect(res.body.name).toBe(updateData.name);
        });
    });
  });

  describe('Data Analysis', () => {
    let processId: number;
    let testFilePath: string;

    beforeAll(() => {
      // 创建测试CSV文件
      const csvContent = `case_id,activity,timestamp,resource
1,Start,2023-01-01T10:00:00Z,User1
1,Process,2023-01-01T10:30:00Z,User1
1,End,2023-01-01T11:00:00Z,User1
2,Start,2023-01-01T14:00:00Z,User2
2,Process,2023-01-01T14:45:00Z,User2
2,End,2023-01-01T15:30:00Z,User2`;

      testFilePath = path.join(__dirname, 'test-data.csv');
      fs.writeFileSync(testFilePath, csvContent);
    });

    afterAll(() => {
      // 清理测试文件
      if (fs.existsSync(testFilePath)) {
        fs.unlinkSync(testFilePath);
      }
    });

    it('should create a process for data upload testing', async () => {
      const newProcess = {
        name: '数据上传测试流程',
        description: '用于测试数据上传功能的流程',
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/processes')
        .set('Authorization', `Bearer ${authToken}`)
        .send(newProcess)
        .expect(201);

      processId = response.body.id;
      expect(typeof processId).toBe('number');
      expect(processId).toBeGreaterThan(0);
    });

    it('/api/v1/analysis/upload (POST) - should upload data file successfully', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/analysis/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', testFilePath)
        .field('processId', processId.toString())
        .field('caseIdField', 'case_id')
        .field('activityField', 'activity')
        .field('timestampField', 'timestamp')
        .field('resourceField', 'resource')
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('数据上传成功');
      expect(response.body.validation.isValid).toBe(true);
      expect(response.body.savedRecords).toBeGreaterThan(0);
    });

    it('/api/v1/analysis/upload (POST) - should fail with invalid processId', async () => {
      await request(app.getHttpServer())
        .post('/api/v1/analysis/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', testFilePath)
        .field('processId', 'invalid')
        .field('caseIdField', 'case_id')
        .field('activityField', 'activity')
        .field('timestampField', 'timestamp')
        .field('resourceField', 'resource')
        .expect(400);
    });

    it('/api/v1/analysis/upload (POST) - should fail without file', async () => {
      await request(app.getHttpServer())
        .post('/api/v1/analysis/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('processId', processId.toString())
        .field('caseIdField', 'case_id')
        .field('activityField', 'activity')
        .field('timestampField', 'timestamp')
        .field('resourceField', 'resource')
        .expect(400);
    });

    it('/api/v1/analysis/statistics/:processId (GET) - should get data statistics', async () => {
      const response = await request(app.getHttpServer())
        .get(`/api/v1/analysis/statistics/${processId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.totalEvents).toBeGreaterThan(0);
      expect(response.body.uniqueCases).toBeGreaterThan(0);
      expect(response.body.uniqueActivities).toBeGreaterThan(0);
    });

    it('/api/v1/analysis/discover/:processId (POST) - should perform process discovery', async () => {
      const response = await request(app.getHttpServer())
        .post(`/api/v1/analysis/discover/${processId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(201);

      expect(response.body.nodes).toBeDefined();
      expect(response.body.edges).toBeDefined();
      expect(response.body.statistics).toBeDefined();
      expect(Array.isArray(response.body.nodes)).toBe(true);
      expect(Array.isArray(response.body.edges)).toBe(true);
    });

    it('/api/v1/analysis/performance/:processId (POST) - should perform performance analysis', async () => {
      const response = await request(app.getHttpServer())
        .post(`/api/v1/analysis/performance/${processId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(201);

      expect(response.body.caseStatistics).toBeDefined();
      expect(response.body.activityStatistics).toBeDefined();
      expect(response.body.bottlenecks).toBeDefined();
    });
  });

  describe('Error handling', () => {
    it('should return 404 for non-existent routes', () => {
      return request(app.getHttpServer())
        .get('/api/v1/non-existent')
        .expect(404);
    });

    it('should return 401 for protected routes without auth', () => {
      return request(app.getHttpServer()).get('/api/v1/processes').expect(401);
    });
  });
});
