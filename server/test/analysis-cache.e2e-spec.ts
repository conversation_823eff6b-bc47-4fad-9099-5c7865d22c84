import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { TestAppModule } from './test-app.module';
import { DataSource } from 'typeorm';
import { User } from '../src/entities/user.entity';
import { Process } from '../src/entities/process.entity';
import { EventLog } from '../src/entities/event-log.entity';
import {
  AnalysisResult,
  AnalysisType,
} from '../src/entities/analysis-result.entity';
import { CacheService } from '../src/analysis/cache.service';
import { ProcessMiningService } from '../src/analysis/process-mining.service';
import * as bcrypt from 'bcryptjs';

describe('Analysis Cache (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let cacheService: CacheService;
  let processMiningService: ProcessMiningService;
  let testUser: User;
  let testProcess: Process;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [TestAppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    dataSource = moduleFixture.get<DataSource>(DataSource);
    cacheService = moduleFixture.get<CacheService>(CacheService);
    processMiningService =
      moduleFixture.get<ProcessMiningService>(ProcessMiningService);

    // 创建测试用户（使用已存在的admin用户）
    const existingUser = await dataSource.getRepository(User).findOne({
      where: { username: 'admin' },
    });

    if (!existingUser) {
      const hashedPassword = await bcrypt.hash('admin123', 10);
      testUser = new User();
      testUser.username = 'admin';
      testUser.email = '<EMAIL>';
      testUser.passwordHash = hashedPassword;
      testUser.fullName = '系统管理员';
      await dataSource.getRepository(User).save(testUser);
    } else {
      testUser = existingUser;
    }

    // 创建测试流程
    testProcess = new Process();
    testProcess.name = 'Cache Test Process';
    testProcess.description = 'Test process for cache functionality';
    testProcess.userId = testUser.id;
    await dataSource.getRepository(Process).save(testProcess);

    // 创建测试事件日志
    const eventLogs = [
      {
        processId: testProcess.id,
        caseId: 'case1',
        activity: 'Start',
        timestamp: new Date('2024-01-01T10:00:00Z'),
        resource: 'User1',
      },
      {
        processId: testProcess.id,
        caseId: 'case1',
        activity: 'Task A',
        timestamp: new Date('2024-01-01T10:30:00Z'),
        resource: 'User1',
      },
      {
        processId: testProcess.id,
        caseId: 'case1',
        activity: 'End',
        timestamp: new Date('2024-01-01T11:00:00Z'),
        resource: 'User1',
      },
      {
        processId: testProcess.id,
        caseId: 'case2',
        activity: 'Start',
        timestamp: new Date('2024-01-01T14:00:00Z'),
        resource: 'User2',
      },
      {
        processId: testProcess.id,
        caseId: 'case2',
        activity: 'Task B',
        timestamp: new Date('2024-01-01T14:30:00Z'),
        resource: 'User2',
      },
      {
        processId: testProcess.id,
        caseId: 'case2',
        activity: 'End',
        timestamp: new Date('2024-01-01T15:00:00Z'),
        resource: 'User2',
      },
    ];

    for (const logData of eventLogs) {
      const eventLog = new EventLog();
      Object.assign(eventLog, logData);
      await dataSource.getRepository(EventLog).save(eventLog);
    }
  });

  afterAll(async () => {
    // 清理测试数据
    if (testProcess?.id) {
      await dataSource
        .getRepository(AnalysisResult)
        .delete({ processId: testProcess.id });
      await dataSource
        .getRepository(EventLog)
        .delete({ processId: testProcess.id });
      await dataSource.getRepository(Process).delete({ id: testProcess.id });
    }
    if (testUser?.id) {
      await dataSource.getRepository(User).delete({ id: testUser.id });
    }

    await app.close();
  });

  beforeEach(async () => {
    // 清理缓存
    if (testProcess?.id) {
      await cacheService.clearProcessCache(testProcess.id);
      // 清理数据库中的分析结果
      await dataSource
        .getRepository(AnalysisResult)
        .delete({ processId: testProcess.id });
    }
  });

  describe('Process Discovery Cache', () => {
    it('should perform fresh analysis on first request', async () => {
      const startTime = Date.now();

      // 直接调用服务方法
      const result = await processMiningService.discoverProcess(
        testProcess.id,
        false,
      );

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(result).toHaveProperty('nodes');
      expect(result).toHaveProperty('edges');
      expect(result).toHaveProperty('statistics');
      expect(result.nodes.length).toBeGreaterThan(0);

      // 验证数据库中保存了结果
      const savedResult = await dataSource
        .getRepository(AnalysisResult)
        .findOne({
          where: {
            processId: testProcess.id,
            analysisType: AnalysisType.PROCESS_DISCOVERY,
          },
          order: { version: 'DESC' },
        });

      expect(savedResult).toBeTruthy();
      expect(savedResult!.status).toBe('completed');
      expect(savedResult!.version).toBe(1);
      expect(savedResult!.cacheKey).toBeTruthy();

      console.log(`Fresh analysis took ${duration}ms`);
    });

    it('should return cached result on second request', async () => {
      // 第一次请求 - 新分析
      await processMiningService.discoverProcess(testProcess.id, false);

      // 第二次请求 - 应该从缓存返回
      const startTime = Date.now();

      const result = await processMiningService.discoverProcess(
        testProcess.id,
        false,
      );

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(result).toHaveProperty('nodes');
      expect(result).toHaveProperty('edges');
      expect(result.nodes.length).toBeGreaterThan(0);

      // 缓存请求应该更快
      expect(duration).toBeLessThan(1000);

      console.log(`Cached analysis took ${duration}ms`);
    });

    it('should force refresh when requested', async () => {
      // 第一次请求
      await processMiningService.discoverProcess(testProcess.id, false);

      // 强制刷新请求
      const result = await processMiningService.discoverProcess(
        testProcess.id,
        true,
      );

      expect(result).toHaveProperty('nodes');
      expect(result).toHaveProperty('edges');

      // 验证版本号递增
      const latestResult = await dataSource
        .getRepository(AnalysisResult)
        .findOne({
          where: {
            processId: testProcess.id,
            analysisType: AnalysisType.PROCESS_DISCOVERY,
          },
          order: { version: 'DESC' },
        });

      expect(latestResult!.version).toBe(2);
    });
  });

  describe('Cache Management Service', () => {
    beforeEach(async () => {
      // 创建一些缓存数据
      await processMiningService.discoverProcess(testProcess.id, false);
    });

    it('should get cache status', async () => {
      const stats = await cacheService.getCacheStats(testProcess.id);

      expect(stats).toHaveProperty('totalCached');
      expect(stats).toHaveProperty('byType');
      expect(stats.byType).toHaveProperty('process_discovery');
      expect(stats.byType.process_discovery).toBe(true);
    });

    it('should clear process cache', async () => {
      // 清除缓存
      await cacheService.clearProcessCache(testProcess.id);

      // 验证缓存已清除
      const stats = await cacheService.getCacheStats(testProcess.id);
      expect(stats.totalCached).toBe(0);
    });

    it('should clear specific analysis cache', async () => {
      // 清除特定分析类型的缓存
      await cacheService.deleteAnalysisResult(
        testProcess.id,
        AnalysisType.PROCESS_DISCOVERY,
      );

      // 验证特定缓存已清除
      const stats = await cacheService.getCacheStats(testProcess.id);
      expect(stats.byType.process_discovery).toBe(false);
    });

    it('should get data source hash', async () => {
      const hash = await cacheService.getDataSourceHash(testProcess.id);

      expect(hash).toBeTruthy();
      expect(typeof hash).toBe('string');
    });
  });

  describe('Data Source Change Detection', () => {
    it('should detect data source changes and invalidate cache', async () => {
      // 第一次分析
      const result1 = await processMiningService.discoverProcess(
        testProcess.id,
        false,
      );
      const originalNodeCount = result1.nodes.length;

      // 添加新的事件日志
      const newEventLog = new EventLog();
      newEventLog.processId = testProcess.id;
      newEventLog.caseId = 'case3';
      newEventLog.activity = 'New Activity';
      newEventLog.timestamp = new Date('2024-01-02T10:00:00Z');
      newEventLog.resource = 'User3';
      await dataSource.getRepository(EventLog).save(newEventLog);

      // 再次分析 - 应该检测到数据变更并重新计算
      const result2 = await processMiningService.discoverProcess(
        testProcess.id,
        false,
      );

      expect(result2.nodes.length).toBeGreaterThanOrEqual(originalNodeCount); // 应该包含新活动或保持原有结构

      // 清理新添加的事件日志
      await dataSource.getRepository(EventLog).delete({ id: newEventLog.id });
    });
  });
});
