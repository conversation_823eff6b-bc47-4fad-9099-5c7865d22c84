import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as request from 'supertest';
import { TestAppModule } from './test-app.module';
import { User } from '../src/entities/user.entity';
import { Process } from '../src/entities/process.entity';
import { EventLog } from '../src/entities/event-log.entity';
import { BpmnModel } from '../src/entities/bpmn-model.entity';
import { ConformanceResult } from '../src/entities/conformance-result.entity';
import { AuthService } from '../src/auth/auth.service';
import { UsersService } from '../src/users/users.service';

describe('Conformance Checking (e2e)', () => {
  let app: INestApplication;
  let userRepository: Repository<User>;
  let processRepository: Repository<Process>;
  let eventLogRepository: Repository<EventLog>;
  let bpmnModelRepository: Repository<BpmnModel>;
  let conformanceResultRepository: Repository<ConformanceResult>;
  let authService: AuthService;
  let usersService: UsersService;
  let accessToken: string;
  let testUser: User;
  let testProcess: Process;
  let testBpmnModel: BpmnModel;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [TestAppModule],
    }).compile();

    app = moduleFixture.createNestApplication();

    // 设置全局前缀
    app.setGlobalPrefix('api/v1');

    await app.init();

    // 获取仓库实例
    userRepository = moduleFixture.get<Repository<User>>(
      getRepositoryToken(User),
    );
    processRepository = moduleFixture.get<Repository<Process>>(
      getRepositoryToken(Process),
    );
    eventLogRepository = moduleFixture.get<Repository<EventLog>>(
      getRepositoryToken(EventLog),
    );
    bpmnModelRepository = moduleFixture.get<Repository<BpmnModel>>(
      getRepositoryToken(BpmnModel),
    );
    conformanceResultRepository = moduleFixture.get<
      Repository<ConformanceResult>
    >(getRepositoryToken(ConformanceResult));

    // 获取服务实例
    authService = moduleFixture.get<AuthService>(AuthService);
    usersService = moduleFixture.get<UsersService>(UsersService);
  });

  beforeEach(async () => {
    // 清理数据库 - 按照外键依赖顺序清理
    await conformanceResultRepository.query('SET FOREIGN_KEY_CHECKS = 0');
    await conformanceResultRepository.clear();
    await bpmnModelRepository.clear();
    await eventLogRepository.clear();
    await processRepository.clear();
    await userRepository.clear();
    await conformanceResultRepository.query('SET FOREIGN_KEY_CHECKS = 1');

    // 创建测试用户
    testUser = await usersService.create({
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123',
      fullName: '测试用户',
    });

    // 生成访问令牌
    const loginResult = await authService.login({
      usernameOrEmail: 'testuser',
      password: 'password123',
    });
    accessToken = loginResult.access_token;

    // 创建测试流程
    testProcess = processRepository.create({
      name: '测试流程',
      description: '用于符合性检查测试的流程',
      userId: testUser.id,
      status: 'active' as any,
    });
    testProcess = await processRepository.save(testProcess);

    // 创建测试事件日志
    const eventLogs = [
      {
        caseId: 'CASE_001',
        activity: '开始',
        timestamp: new Date('2024-01-01T09:00:00Z'),
        resource: 'User1',
      },
      {
        caseId: 'CASE_001',
        activity: '申请提交',
        timestamp: new Date('2024-01-01T09:30:00Z'),
        resource: 'User1',
      },
      {
        caseId: 'CASE_001',
        activity: '初审',
        timestamp: new Date('2024-01-01T10:00:00Z'),
        resource: 'User2',
      },
      {
        caseId: 'CASE_001',
        activity: '复审',
        timestamp: new Date('2024-01-01T11:00:00Z'),
        resource: 'User3',
      },
      {
        caseId: 'CASE_001',
        activity: '批准',
        timestamp: new Date('2024-01-01T12:00:00Z'),
        resource: 'User3',
      },
      {
        caseId: 'CASE_001',
        activity: '结束',
        timestamp: new Date('2024-01-01T12:30:00Z'),
        resource: 'User1',
      },

      {
        caseId: 'CASE_002',
        activity: '开始',
        timestamp: new Date('2024-01-02T09:00:00Z'),
        resource: 'User1',
      },
      {
        caseId: 'CASE_002',
        activity: '申请提交',
        timestamp: new Date('2024-01-02T09:30:00Z'),
        resource: 'User1',
      },
      {
        caseId: 'CASE_002',
        activity: '初审',
        timestamp: new Date('2024-01-02T10:00:00Z'),
        resource: 'User2',
      },
      {
        caseId: 'CASE_002',
        activity: '批准',
        timestamp: new Date('2024-01-02T11:00:00Z'),
        resource: 'User3',
      }, // 跳过复审
      {
        caseId: 'CASE_002',
        activity: '结束',
        timestamp: new Date('2024-01-02T11:30:00Z'),
        resource: 'User1',
      },
    ];

    for (const logData of eventLogs) {
      const eventLog = eventLogRepository.create({
        ...logData,
        processId: testProcess.id,
      });
      await eventLogRepository.save(eventLog);
    }

    // 创建测试BPMN模型
    const bpmnXml = `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" id="Definitions_1">
  <bpmn:process id="TestProcess" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:task id="Task_1" name="申请提交">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Task_2" name="初审">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Task_3" name="复审">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Task_4" name="批准">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:task>
    <bpmn:endEvent id="EndEvent_1" name="结束">
      <bpmn:incoming>Flow_5</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="Task_1" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_1" targetRef="Task_2" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_2" targetRef="Task_3" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_3" targetRef="Task_4" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_4" targetRef="EndEvent_1" />
  </bpmn:process>
</bpmn:definitions>`;

    testBpmnModel = bpmnModelRepository.create({
      name: '测试BPMN模型',
      description: '用于符合性检查测试的BPMN模型',
      modelType: 'reference' as any,
      status: 'active' as any,
      bpmnXml,
      processId: testProcess.id,
      modelData: {
        activities: ['开始', '申请提交', '初审', '复审', '批准', '结束'],
        paths: [
          { from: '开始', to: '申请提交' },
          { from: '申请提交', to: '初审' },
          { from: '初审', to: '复审' },
          { from: '复审', to: '批准' },
          { from: '批准', to: '结束' },
        ],
        complexity: 6,
      },
    });
    testBpmnModel = await bpmnModelRepository.save(testBpmnModel);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('BPMN Model Management', () => {
    it('should create a new BPMN model', async () => {
      const createDto = {
        name: '新BPMN模型',
        description: '测试创建BPMN模型',
        modelType: 'reference',
        processId: testProcess.id,
        bpmnXml: testBpmnModel.bpmnXml,
        version: '1.0',
        author: '测试用户',
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/conformance/models')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(createDto)
        .expect(201);

      expect(response.body).toMatchObject({
        name: createDto.name,
        description: createDto.description,
        modelType: createDto.modelType,
        processId: createDto.processId,
        version: createDto.version,
        author: createDto.author,
      });
      expect(response.body.id).toBeDefined();
      expect(response.body.modelHash).toBeDefined();
    });

    it('should get BPMN models list', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/conformance/models')
        .set('Authorization', `Bearer ${accessToken}`)
        .query({ processId: testProcess.id })
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0]).toMatchObject({
        name: testBpmnModel.name,
        processId: testProcess.id,
      });
    });

    it('should get BPMN model by id', async () => {
      const response = await request(app.getHttpServer())
        .get(`/api/v1/conformance/models/${testBpmnModel.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        id: testBpmnModel.id,
        name: testBpmnModel.name,
        description: testBpmnModel.description,
        processId: testProcess.id,
      });
    });

    it('should update BPMN model', async () => {
      const updateDto = {
        name: '更新的BPMN模型',
        description: '更新的描述',
        status: 'active',
      };

      const response = await request(app.getHttpServer())
        .put(`/api/v1/conformance/models/${testBpmnModel.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(updateDto)
        .expect(200);

      expect(response.body).toMatchObject({
        id: testBpmnModel.id,
        name: updateDto.name,
        description: updateDto.description,
        status: updateDto.status,
      });
    });

    it('should validate BPMN model', async () => {
      const response = await request(app.getHttpServer())
        .get(`/api/v1/conformance/models/${testBpmnModel.id}/validation`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        isValid: true,
        errors: [],
        modelInfo: {
          activities: expect.any(Number),
          paths: expect.any(Number),
          complexity: expect.any(Number),
        },
      });
    });

    it('should activate BPMN model', async () => {
      const response = await request(app.getHttpServer())
        .post(`/api/v1/conformance/models/${testBpmnModel.id}/activate`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(201);

      expect(response.body.status).toBe('active');
    });

    it('should delete BPMN model', async () => {
      // 创建一个新的模型用于删除测试
      const newModel = bpmnModelRepository.create({
        name: '待删除的模型',
        bpmnXml: testBpmnModel.bpmnXml,
        processId: testProcess.id,
        modelType: 'reference' as any,
      });
      const savedModel = await bpmnModelRepository.save(newModel);

      await request(app.getHttpServer())
        .delete(`/api/v1/conformance/models/${savedModel.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      // 验证模型已被删除
      const deletedModel = await bpmnModelRepository.findOne({
        where: { id: savedModel.id },
      });
      expect(deletedModel).toBeNull();
    });
  });

  describe('Conformance Checking', () => {
    it('should perform conformance check', async () => {
      const checkDto = {
        processId: testProcess.id,
        bpmnModelId: testBpmnModel.id,
        parameters: {
          alignmentAlgorithm: 'heuristic',
          includeCaseAnalysis: true,
          includeActivityAnalysis: true,
        },
        description: '测试符合性检查',
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/conformance/check')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(checkDto)
        .expect(201);

      expect(response.body).toMatchObject({
        id: expect.any(Number),
        status: 'processing',
        conformanceScore: expect.any(Number),
        fitnessScore: expect.any(Number),
        precisionScore: expect.any(Number),
        generalizationScore: expect.any(Number),
        simplicityScore: expect.any(Number),
        totalCases: expect.any(Number),
        conformingCases: expect.any(Number),
        deviatingCases: expect.any(Number),
        conformanceLevel: expect.stringMatching(/^(excellent|good|fair|poor)$/),
        majorDeviationTypes: expect.any(Array),
      });
    });

    it('should get conformance check result', async () => {
      // 先创建一个符合性检查结果
      const conformanceResult = conformanceResultRepository.create({
        processId: testProcess.id,
        bpmnModelId: testBpmnModel.id,
        status: 'completed' as any,
        conformanceScore: 0.8,
        fitnessScore: 0.8,
        precisionScore: 0.9,
        generalizationScore: 0.7,
        simplicityScore: 0.9,
        totalCases: 2,
        conformingCases: 1,
        deviatingCases: 1,
        deviations: [
          {
            caseId: 'CASE_002',
            type: 'missing_activity' as any,
            description: '缺失活动: 复审',
            expectedActivity: '复审',
            severity: 'medium' as any,
          },
        ],
        alignmentResult: {
          algorithm: 'heuristic',
          totalCases: 2,
          conformingCases: 1,
        },
      });
      const savedResult =
        await conformanceResultRepository.save(conformanceResult);

      const response = await request(app.getHttpServer())
        .get(
          `/api/v1/conformance/results/${testProcess.id}/${testBpmnModel.id}`,
        )
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        id: savedResult.id,
        processId: testProcess.id,
        bpmnModelId: testBpmnModel.id,
        status: 'completed',
        conformanceLevel: 'good',
        majorDeviationTypes: expect.any(Array),
      });

      // 检查数值字段（可能是字符串格式）
      expect(parseFloat(response.body.conformanceScore)).toBeCloseTo(0.8, 4);
    });

    it('should get conformance results list for process', async () => {
      // 创建多个符合性检查结果
      const results: any[] = [];
      for (let i = 0; i < 3; i++) {
        const result = conformanceResultRepository.create({
          processId: testProcess.id,
          bpmnModelId: testBpmnModel.id,
          status: 'completed' as any,
          conformanceScore: 0.7 + i * 0.1,
          fitnessScore: 0.7 + i * 0.1,
          precisionScore: 0.8,
          generalizationScore: 0.7,
          simplicityScore: 0.9,
          totalCases: 10,
          conformingCases: 7 + i,
          deviatingCases: 3 - i,
          deviations: [],
          alignmentResult: {},
        });
        results.push(await conformanceResultRepository.save(result));
      }

      const response = await request(app.getHttpServer())
        .get(`/api/v1/conformance/results/${testProcess.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBe(3);
      expect(response.body[0]).toMatchObject({
        processId: testProcess.id,
        bpmnModelId: testBpmnModel.id,
        status: 'completed',
      });
    });

    it('should delete conformance result', async () => {
      // 创建一个符合性检查结果用于删除测试
      const result = conformanceResultRepository.create({
        processId: testProcess.id,
        bpmnModelId: testBpmnModel.id,
        status: 'completed' as any,
        conformanceScore: 0.5,
        fitnessScore: 0.5,
        precisionScore: 0.6,
        generalizationScore: 0.7,
        simplicityScore: 0.8,
        totalCases: 5,
        conformingCases: 2,
        deviatingCases: 3,
        deviations: [],
        alignmentResult: {},
      });
      const savedResult = await conformanceResultRepository.save(result);

      await request(app.getHttpServer())
        .delete(`/api/v1/conformance/results/${savedResult.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      // 验证结果已被删除
      const deletedResult = await conformanceResultRepository.findOne({
        where: { id: savedResult.id },
      });
      expect(deletedResult).toBeNull();
    });
  });

  describe('Analysis and Statistics', () => {
    beforeEach(async () => {
      // 创建一些测试数据
      const results = [
        {
          conformanceScore: 0.9,
          fitnessScore: 0.9,
          precisionScore: 0.85,
          generalizationScore: 0.8,
          simplicityScore: 0.95,
          totalCases: 10,
          conformingCases: 9,
          deviatingCases: 1,
          deviations: [],
          alignmentResult: {},
        },
        {
          conformanceScore: 0.7,
          fitnessScore: 0.7,
          precisionScore: 0.75,
          generalizationScore: 0.8,
          simplicityScore: 0.9,
          totalCases: 10,
          conformingCases: 7,
          deviatingCases: 3,
          deviations: [
            {
              caseId: 'CASE_001',
              type: 'missing_activity' as any,
              description: '缺失活动',
              severity: 'medium' as any,
            },
          ],
          alignmentResult: {},
        },
      ];

      for (const resultData of results) {
        const result = conformanceResultRepository.create({
          processId: testProcess.id,
          bpmnModelId: testBpmnModel.id,
          status: 'completed' as any,
          ...resultData,
        });
        await conformanceResultRepository.save(result);
      }
    });

    it('should get conformance analysis summary', async () => {
      const response = await request(app.getHttpServer())
        .get(`/api/v1/conformance/analysis/summary/${testProcess.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        processId: testProcess.id,
        totalModels: expect.any(Number),
        totalAnalyses: expect.any(Number),
        commonDeviationTypes: expect.any(Array),
        analysisHistory: expect.any(Array),
      });

      // 检查分数字段（可能为null或数字）
      if (response.body.averageConformanceScore !== null) {
        expect(response.body.averageConformanceScore).toBeGreaterThan(0);
        expect(response.body.bestConformanceScore).toBeGreaterThanOrEqual(
          response.body.averageConformanceScore,
        );
        expect(response.body.worstConformanceScore).toBeLessThanOrEqual(
          response.body.averageConformanceScore,
        );
      }
    });

    it('should get conformance trends', async () => {
      const response = await request(app.getHttpServer())
        .get(`/api/v1/conformance/analysis/trends/${testProcess.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .query({ days: 30 })
        .expect(200);

      expect(response.body).toMatchObject({
        processId: testProcess.id,
        period: '30天',
        dataPoints: expect.any(Array),
        trend: expect.stringMatching(/^(improving|stable|declining)$/),
        improvement: expect.any(Number),
      });
    });
  });

  describe('Cache Management', () => {
    it('should get cache statistics', async () => {
      const response = await request(app.getHttpServer())
        .get(`/api/v1/conformance/cache/stats/${testProcess.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        totalResults: expect.any(Number),
        cachedResults: expect.any(Number),
        expiredResults: expect.any(Number),
        cacheHitRate: expect.any(Number),
      });
    });

    it('should clear process cache', async () => {
      await request(app.getHttpServer())
        .delete(`/api/v1/conformance/cache/${testProcess.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);
    });

    it('should clear model cache', async () => {
      await request(app.getHttpServer())
        .delete(
          `/api/v1/conformance/cache/${testProcess.id}/${testBpmnModel.id}`,
        )
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);
    });
  });
});
