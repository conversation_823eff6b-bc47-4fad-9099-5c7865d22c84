# Nodejs
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log
.env
.env.*
!.env.example

# NestJS
/dist
/coverage
.nyc_output

# Nuxt
.nuxt
.nitro
.cache
.output
.data
dist

# IDE
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
.project

# OS
.DS_Store
Thumbs.db
*.swp

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Test
/coverage
.nyc_output

# Build
/build
/dist
server/uploads
test-data
reports/
result-data/
deployment-temp/
