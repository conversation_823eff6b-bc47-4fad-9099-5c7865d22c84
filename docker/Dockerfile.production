# ProMax Production Dockerfile
# 基于现有的 runtime 镜像构建生产环境镜像

FROM node:18-alpine AS frontend-builder

# 设置工作目录
WORKDIR /app/client

# 复制前端依赖文件
COPY client/package*.json client/yarn.lock ./

# 安装前端依赖
RUN yarn install --frozen-lockfile

# 复制前端源码
COPY client/ ./

# 构建前端
RUN yarn build

# 后端构建阶段
FROM node:18-alpine AS backend-builder

# 设置工作目录
WORKDIR /app/server

# 复制后端依赖文件
COPY server/package*.json server/yarn.lock ./

# 安装后端依赖
RUN yarn install --frozen-lockfile

# 复制后端源码
COPY server/ ./

# 构建后端
RUN yarn build

# Python 环境准备阶段
FROM python:3.9-slim AS python-builder

# 设置工作目录
WORKDIR /app/python-mining-service

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制 Python 依赖文件
COPY python-mining-service/requirements.txt ./

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制 Python 源码
COPY python-mining-service/ ./

# 生产环境最终镜像
FROM ubuntu:20.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV NODE_ENV=production
ENV PYTHONPATH=/app/python-mining-service

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    nginx \
    supervisor \
    python3 \
    python3-pip \
    && curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/*

# 创建应用目录
RUN mkdir -p /app/client /app/server /app/python-mining-service /app/data/logs /app/data/uploads /app/data/reports

# 从构建阶段复制文件
COPY --from=frontend-builder /app/client/.output /app/client/.output
COPY --from=frontend-builder /app/client/node_modules /app/client/node_modules
COPY --from=frontend-builder /app/client/package.json /app/client/package.json

COPY --from=backend-builder /app/server/dist /app/server/dist
COPY --from=backend-builder /app/server/node_modules /app/server/node_modules
COPY --from=backend-builder /app/server/package.json /app/server/package.json

COPY --from=python-builder /usr/local/lib/python3.9/site-packages /usr/local/lib/python3.9/site-packages
COPY --from=python-builder /app/python-mining-service /app/python-mining-service

# 复制配置文件
COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY docker/start-mount.sh /app/start-mount.sh
COPY docker/validate-env.sh /app/validate-env.sh

# 设置权限
RUN chmod +x /app/start-mount.sh /app/validate-env.sh

# 创建非root用户
RUN useradd -m -s /bin/bash promax && \
    chown -R promax:promax /app /var/log/nginx /var/lib/nginx

# 暴露端口
EXPOSE 80 3000 8000

# 设置工作目录
WORKDIR /app

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# 启动命令
CMD ["/app/start-mount.sh"]