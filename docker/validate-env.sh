#!/bin/bash

# ProMax 环境变量验证脚本
# 检查Docker容器中的环境变量是否正确设置

echo "🔍 ProMax 环境变量验证"
echo "======================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 验证函数
validate_env() {
    local var_name=$1
    local var_value=$2
    local required=${3:-true}
    
    if [ -z "$var_value" ]; then
        if [ "$required" = "true" ]; then
            echo -e "${RED}❌ $var_name: 未设置 (必需)${NC}"
            return 1
        else
            echo -e "${YELLOW}⚠️  $var_name: 未设置 (可选)${NC}"
            return 0
        fi
    else
        echo -e "${GREEN}✅ $var_name: $var_value${NC}"
        return 0
    fi
}

# 测试网络连接
test_connection() {
    local host=$1
    local port=$2
    local service_name=$3
    
    if nc -z "$host" "$port" 2>/dev/null; then
        echo -e "${GREEN}✅ $service_name 连接成功: $host:$port${NC}"
        return 0
    else
        echo -e "${RED}❌ $service_name 连接失败: $host:$port${NC}"
        return 1
    fi
}

echo ""
echo "📋 检查通用环境变量..."
validate_env "NODE_ENV" "$NODE_ENV"
validate_env "PYTHONPATH" "$PYTHONPATH"

echo ""
echo "📋 检查NestJS Server环境变量..."
validate_env "PORT" "$PORT"
validate_env "DB_HOST" "$DB_HOST"
validate_env "DB_PORT" "$DB_PORT"
validate_env "DB_USERNAME" "$DB_USERNAME"
validate_env "DB_PASSWORD" "$DB_PASSWORD"
validate_env "DB_DATABASE" "$DB_DATABASE"
validate_env "REDIS_HOST" "$REDIS_HOST"
validate_env "REDIS_PORT" "$REDIS_PORT"
validate_env "REDIS_KEY_PREFIX" "$REDIS_KEY_PREFIX"
validate_env "REDIS_PASSWORD" "$REDIS_PASSWORD" false
validate_env "REDIS_DEFAULT_EXPIRE" "$REDIS_DEFAULT_EXPIRE"
validate_env "JWT_SECRET" "$JWT_SECRET"
validate_env "JWT_EXPIRES_IN" "$JWT_EXPIRES_IN"
validate_env "PYTHON_MINING_SERVICE_URL" "$PYTHON_MINING_SERVICE_URL"

echo ""
echo "📋 检查Python Mining Service环境变量..."
validate_env "HOST" "$HOST"
validate_env "DEBUG" "$DEBUG"
validate_env "MYSQL_HOST" "$MYSQL_HOST"
validate_env "MYSQL_PORT" "$MYSQL_PORT"
validate_env "MYSQL_USER" "$MYSQL_USER"
validate_env "MYSQL_PASSWORD" "$MYSQL_PASSWORD"
validate_env "MYSQL_DATABASE" "$MYSQL_DATABASE"
validate_env "REDIS_DB" "$REDIS_DB"
validate_env "MAX_WORKERS" "$MAX_WORKERS"
validate_env "ENABLE_GPU_ACCELERATION" "$ENABLE_GPU_ACCELERATION"

echo ""
echo "📋 检查Nuxt3 Frontend环境变量..."
validate_env "NUXT_HOST" "$NUXT_HOST" false
validate_env "NUXT_PORT" "$NUXT_PORT" false
validate_env "NUXT_PUBLIC_API_BASE" "$NUXT_PUBLIC_API_BASE" false

echo ""
echo "🌐 测试网络连接..."

# 测试MySQL连接
if [ -n "$DB_HOST" ] && [ -n "$DB_PORT" ]; then
    test_connection "$DB_HOST" "$DB_PORT" "MySQL数据库"
elif [ -n "$MYSQL_HOST" ] && [ -n "$MYSQL_PORT" ]; then
    test_connection "$MYSQL_HOST" "$MYSQL_PORT" "MySQL数据库"
fi

# 测试Redis连接
if [ -n "$REDIS_HOST" ] && [ -n "$REDIS_PORT" ]; then
    test_connection "$REDIS_HOST" "$REDIS_PORT" "Redis缓存"
elif [ -n "$REDIS_HOST" ]; then
    test_connection "$REDIS_HOST" "6379" "Redis缓存"
fi

echo ""
echo "🔧 检查系统工具..."

# 检查必要的系统工具
if command -v node >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Node.js: $(node --version)${NC}"
else
    echo -e "${RED}❌ Node.js: 未安装${NC}"
fi

if command -v python >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Python: $(python --version)${NC}"
else
    echo -e "${RED}❌ Python: 未安装${NC}"
fi

if command -v nginx >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Nginx: $(nginx -v 2>&1)${NC}"
else
    echo -e "${RED}❌ Nginx: 未安装${NC}"
fi

if command -v supervisorctl >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Supervisor: 已安装${NC}"
else
    echo -e "${RED}❌ Supervisor: 未安装${NC}"
fi

echo ""
echo "📊 检查端口占用..."

# 检查端口占用
check_port() {
    local port=$1
    local service=$2
    
    if netstat -tlnp 2>/dev/null | grep ":$port " >/dev/null; then
        echo -e "${GREEN}✅ 端口 $port ($service): 已监听${NC}"
    else
        echo -e "${YELLOW}⚠️  端口 $port ($service): 未监听${NC}"
    fi
}

check_port "80" "Nginx"
check_port "3000" "Nuxt3 Frontend"
check_port "3003" "NestJS API"
check_port "8000" "Python Mining Service"

echo ""
echo "🎯 验证完成！"

# 显示访问地址
echo ""
echo "🌐 服务访问地址:"
echo "主页: http://localhost"
echo "API文档: http://localhost:3003/api-docs"
echo "挖掘服务文档: http://localhost:8000/docs"
