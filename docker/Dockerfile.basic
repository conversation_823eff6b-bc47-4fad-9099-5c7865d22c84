# ProMax 基础镜像
# 包含编译和运行时环境，不包含业务代码和业务依赖
# 用于提升构建速度的基础镜像

FROM docker.m.daocloud.io/library/python:3.12-slim

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV NODE_VERSION=22
ENV PYTHON_VERSION=3.12

# 安装系统依赖和Node.js
RUN apt-get update && apt-get install -y \
    curl \
    gnupg \
    supervisor \
    nginx \
    netcat-openbsd \
    build-essential \
    git \
    && curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash - \
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/*

# 安装yarn包管理器
RUN npm install -g yarn && \
    yarn --version && \
    echo "✅ Yarn installed successfully"

# 配置pip使用国内镜像源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# 安装PyTorch (CPU版本，支持多平台)
RUN pip install --no-cache-dir \
    torch>=2.0.0 \
    torchvision>=0.15.0 \
    torchaudio>=2.0.0

# 安装Python基础运行时依赖（通用包，非业务特定）
RUN pip install --no-cache-dir \
    fastapi==0.104.1 \
    uvicorn[standard]==0.24.0 \
    pydantic==2.5.0 \
    pydantic-settings==2.1.0 \
    pymysql==1.1.0 \
    sqlalchemy==2.0.23 \
    redis==5.0.1 \
    loguru==0.7.2 \
    httpx==0.25.2 \
    aiohttp==3.9.1 \
    pandas==2.3.1 \
    numpy==1.26.4 \
    scipy==1.13.1 \
    scikit-learn==1.5.1 \
    networkx==3.3 \
    psutil==6.0.0 \
    memory-profiler==0.61.0 \
    numba>=0.58.0 \
    python-igraph>=0.11.0 \
    joblib>=1.3.0 \
    orjson>=3.9.0 \
    msgpack>=1.0.0

# 创建应用目录结构
WORKDIR /app
RUN mkdir -p /app/logs /app/uploads /app/reports /app/result-data /var/log/supervisor

# 创建非root用户
RUN groupadd -r promax && useradd -r -g promax promax

# 设置目录权限
RUN chown -R promax:promax /app && \
    chown -R promax:promax /var/log/supervisor

# 暴露端口
EXPOSE 80 3000 8000 5000

# 设置默认工作目录
WORKDIR /app

# 默认命令（可被覆盖）
CMD ["/bin/bash"]