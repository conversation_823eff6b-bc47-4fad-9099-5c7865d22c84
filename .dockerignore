# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
docs/
dev-docs/
*.md

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Dependencies
node_modules/
*/node_modules/

# Build outputs
client/.nuxt/
client/.output/
client/dist/
server/dist/
server/coverage/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# Testing
coverage/
.nyc_output/
.coverage
.pytest_cache/
test-results/

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Deployment
deploy*.sh
manage.sh
*.tar.gz
deployment-temp/
docker-data/

# Data directories
uploads/
reports/
result-data/
test-data/

# Environment files
.env
.env.local
.env.*.local

# Lock files (will be copied separately)
package-lock.json
yarn.lock

# Python specific
python-mining-service/logs/
python-mining-service/__pycache__/
python-mining-service/*.pyc
python-mining-service/.pytest_cache/

# Server specific
server/uploads/
server/test-data/
server/coverage/
