# ProMax 流程挖掘平台 Docker 部署指南

## 概述

本部署方案采用**挂载式部署架构**，将 ProMax 流程挖掘平台的三个核心服务（client、server、python-mining-service）运行在一个 Docker 容器中，通过代码挂载实现快速更新和调试。

## 架构说明

```
┌─────────────────────────────────────────┐
│              Docker Container           │
├─────────────────────────────────────────┤
│  Nginx (端口 80 → 外部端口 3100)        │
│  ├── / → Nuxt3 Frontend (端口 3000)     │
│  ├── /api/ → NestJS Backend (端口 3003) │
│  └── /mining/ → Python Service (端口 8000) │
├─────────────────────────────────────────┤
│  Supervisor 进程管理                     │
│  ├── nginx                             │
│  ├── nestjs-server                     │
│  ├── python-mining-service             │
│  └── nuxt-frontend                     │
├─────────────────────────────────────────┤
│  挂载卷                                 │
│  ├── /app/code ← 源代码目录              │
│  ├── /app/logs ← 日志文件               │
│  ├── /app/uploads ← 上传文件            │
│  ├── /app/reports ← 分析报告            │
│  └── /app/result-data ← 挖掘结果        │
└─────────────────────────────────────────┘
```

## 部署策略

### 挂载式部署优势
- **快速更新**: 代码修改后无需重新构建镜像，只需重启容器
- **便于调试**: 可直接在服务器上修改代码进行调试
- **分离关注点**: 运行时环境与业务代码分离
- **高效开发**: 支持热更新和快速迭代

## 服务器信息

- **服务器地址**: *************
- **用户名**: bpmax4090
- **登录方式**: SSH 免密登录
- **代码目录**: `/home/<USER>/promax-code`
- **数据目录**: `/home/<USER>/promax-data`

## 数据库配置

- **MySQL**:
  - 主机: **************
  - 端口: 33068
  - 用户: root
  - 密码: J#fe9Yu{
  - 数据库: promax_db

- **Redis**:
  - 主机: **************
  - 端口: 63790
  - 前缀: promax_yitaiyitai_com:
  - 密码: (空)

## 统一配置管理

### 环境变量管理策略
- **配置文件**: 所有环境变量统一配置在 `config/production.env` 文件中
- **容器启动**: 环境变量通过 `docker run -e` 参数传递给容器
- **运行时设置**: 容器内不预设环境变量，完全依赖启动时传入
- **配置验证**: 容器启动时自动检查环境变量完整性

### 配置文件位置
所有环境变量统一配置在 `config/production.env` 文件中，包括：
- 服务器信息
- 数据库配置
- Redis配置
- 应用配置
- 服务端口
- 数据目录

### 配置加载脚本

```bash
# 加载环境配置
source scripts/load-env.sh

# 静默加载（不显示配置摘要）
source scripts/load-env.sh --quiet

# 验证配置完整性
scripts/load-env.sh
```

### 主要配置项

#### 服务器配置
- `SERVER_HOST=*************` - 目标服务器地址
- `SERVER_USER=bpmax4090` - 服务器用户名
- `CONTAINER_NAME=promax-platform` - Docker容器名称

#### 数据库配置
- `DB_HOST=**************` - MySQL主机地址
- `DB_PORT=33068` - MySQL端口
- `DB_USERNAME=root` - 数据库用户名
- `DB_PASSWORD=J#fe9Yu{` - 数据库密码
- `DB_DATABASE=promax_db` - 数据库名称

#### Redis配置
- `REDIS_HOST=**************` - Redis主机地址
- `REDIS_PORT=63790` - Redis端口
- `REDIS_KEY_PREFIX=promax_yitaiyitai_com:` - 键前缀

#### 服务端口配置
- `NGINX_PORT=3100` - Nginx代理端口（外部访问）
- `NESTJS_EXTERNAL_PORT=3101` - NestJS API外部端口
- `PYTHON_SERVICE_EXTERNAL_PORT=3102` - Python挖掘服务外部端口
- `NESTJS_INTERNAL_PORT=3003` - NestJS内部端口
- `PYTHON_SERVICE_INTERNAL_PORT=8000` - Python服务内部端口
- `NUXT_PORT=3000` - Nuxt前端内部端口

#### 性能配置
- `MAX_WORKERS=4` - Python服务工作进程数
- `ENABLE_GPU_ACCELERATION=true` - 启用GPU加速
- `MAX_MEMORY_USAGE_PERCENT=80.0` - 最大内存使用百分比
- `BATCH_SIZE=1000` - 批处理大小

## 快速部署

### 1. 配置环境变量

```bash
# 如需修改配置，直接编辑配置文件
vim config/production.env
```

### 2. 挂载式部署（推荐）

```bash
# 给脚本执行权限
chmod +x deploy-volume-mount.sh rebuild-runtime.sh scripts/*.sh

# 挂载式部署（推荐方式）
./deploy-volume-mount.sh
```

### 3. 重建运行时镜像（可选）

```bash
# 如果需要更新基础运行时环境
./rebuild-runtime.sh
```

## 部署流程详解

### 挂载式部署流程
1. **代码同步**: 使用 rsync 将本地代码同步到远程服务器
2. **远程构建**: 在远程服务器的 Docker 容器中构建前端和后端代码
3. **容器启动**: 启动运行时容器，挂载代码和数据目录
4. **服务启动**: 通过 Supervisor 管理各个服务进程

## 管理命令

### 部署相关命令

```bash
# 挂载式部署（推荐）
./deploy-volume-mount.sh

# 重建运行时镜像
./rebuild-runtime.sh
```

### 远程服务器管理

```bash
# 查看容器状态
ssh bpmax4090@************* 'sudo docker ps --filter name=promax-platform'

# 查看容器日志
ssh bpmax4090@************* 'sudo docker logs -f promax-platform'

# 查看Supervisor日志
ssh bpmax4090@************* 'tail -f ~/promax-data/supervisor-logs/*.log'

# 查看服务状态
ssh bpmax4090@************* 'sudo docker exec promax-platform supervisorctl status'

# 重启容器
ssh bpmax4090@************* 'sudo docker restart promax-platform'

# 停止容器
ssh bpmax4090@************* 'sudo docker stop promax-platform'

# 进入容器
ssh bpmax4090@************* 'sudo docker exec -it promax-platform bash'

# 重启特定服务
ssh bpmax4090@************* 'sudo docker exec promax-platform supervisorctl restart nestjs-server'
ssh bpmax4090@************* 'sudo docker exec promax-platform supervisorctl restart python-mining-service'
ssh bpmax4090@************* 'sudo docker exec promax-platform supervisorctl restart nuxt-frontend'
```

## 访问地址

### 远程服务器
- **主页**: http://*************:3100
- **API 文档**: http://*************:3100/api/docs
- **挖掘服务文档**: http://*************:3100/mining/docs
- **直接API访问**: http://*************:3101/api/docs
- **直接挖掘服务访问**: http://*************:3102/docs

### 本地测试（如果本地部署）
- **主页**: http://localhost:3100
- **API 文档**: http://localhost:3100/api/docs
- **挖掘服务文档**: http://localhost:3100/mining/docs

## 端口说明

### 外部端口（主机映射）
- **3100**: Nginx 反向代理入口（主要访问端口）
- **3101**: NestJS API 服务直接访问
- **3102**: Python 挖掘服务直接访问

### 内部端口（容器内）
- **80**: Nginx 内部端口
- **3003**: NestJS API 服务内部端口
- **8000**: Python 挖掘服务内部端口
- **3000**: Nuxt3 前端服务内部端口

## 数据持久化

容器使用数据卷挂载以下目录：

### 代码挂载
- `/app/code` ← `~/promax-code` (源代码目录)

### 数据挂载
- `/app/logs` ← `~/promax-data/logs` (应用日志)
- `/app/uploads` ← `~/promax-data/uploads` (上传文件)
- `/app/reports` ← `~/promax-data/reports` (分析报告)
- `/app/result-data` ← `~/promax-data/result-data` (挖掘结果数据)
- `/var/log/supervisor` ← `~/promax-data/supervisor-logs` (Supervisor日志)

## 故障排查

### 1. 查看容器状态

```bash
# 远程服务器
ssh bpmax4090@************* 'sudo docker ps --filter name=promax-platform'

# 查看容器详细信息
ssh bpmax4090@************* 'sudo docker inspect promax-platform'
```

### 2. 查看详细日志

```bash
# 查看容器日志
ssh bpmax4090@************* 'sudo docker logs -f promax-platform'

# 查看Supervisor日志
ssh bpmax4090@************* 'tail -f ~/promax-data/supervisor-logs/*.log'

# 查看特定服务日志
ssh bpmax4090@************* 'sudo docker exec promax-platform supervisorctl tail -f nestjs-server'
ssh bpmax4090@************* 'sudo docker exec promax-platform supervisorctl tail -f python-mining-service'
ssh bpmax4090@************* 'sudo docker exec promax-platform supervisorctl tail -f nuxt-frontend'
ssh bpmax4090@************* 'sudo docker exec promax-platform supervisorctl tail -f nginx'
```

### 3. 进入容器调试

```bash
# 进入容器
ssh bpmax4090@************* 'sudo docker exec -it promax-platform bash'

# 在容器内检查
supervisorctl status
ps aux
netstat -tlnp
df -h
free -h
```

### 4. 健康检查

```bash
# 检查各服务状态
ssh bpmax4090@************* 'sudo docker exec promax-platform supervisorctl status'

# 手动检查服务可用性
curl http://*************:3100/health
curl http://*************:3100/api/v1/health
curl http://*************:3100/mining/health
```

### 5. 重启服务

```bash
# 重启整个容器
ssh bpmax4090@************* 'sudo docker restart promax-platform'

# 重启特定服务
ssh bpmax4090@************* 'sudo docker exec promax-platform supervisorctl restart nestjs-server'
ssh bpmax4090@************* 'sudo docker exec promax-platform supervisorctl restart python-mining-service'
ssh bpmax4090@************* 'sudo docker exec promax-platform supervisorctl restart nuxt-frontend'
ssh bpmax4090@************* 'sudo docker exec promax-platform supervisorctl restart nginx'
```

## 更新部署

### 1. 代码更新后重新部署

```bash
# 挂载式部署（推荐）
./deploy-volume-mount.sh
```

### 2. 更新流程说明

挂载式部署的更新流程：
1. **代码同步**: 自动同步本地代码到远程服务器
2. **远程构建**: 在远程服务器的Docker容器中重新构建代码
3. **容器重启**: 停止现有容器，启动新容器
4. **服务验证**: 自动检查服务启动状态

### 3. 仅更新运行时环境

如果需要更新基础运行时环境（Node.js、Python等）：

```bash
# 重建运行时镜像
./rebuild-runtime.sh

# 然后重新部署
./deploy-volume-mount.sh
```

### 4. 快速重启服务

如果只需要重启服务而不重新构建：

```bash
# 重启整个容器
ssh bpmax4090@************* 'sudo docker restart promax-platform'

# 或重启特定服务
ssh bpmax4090@************* 'sudo docker exec promax-platform supervisorctl restart nestjs-server'
```

## 性能监控

### 1. 系统资源监控

```bash
# 查看容器资源使用
ssh bpmax4090@************* 'sudo docker stats promax-platform'

# 进入容器查看详细信息
ssh bpmax4090@************* 'sudo docker exec -it promax-platform bash'
# 在容器内执行
htop
df -h
free -h
```

### 2. 服务监控

```bash
# 查看各服务状态
ssh bpmax4090@************* 'sudo docker exec promax-platform supervisorctl status'

# 查看端口监听
ssh bpmax4090@************* 'sudo docker exec promax-platform netstat -tlnp'

# 查看服务进程
ssh bpmax4090@************* 'sudo docker exec promax-platform ps aux'
```

### 3. 日志监控

```bash
# 实时监控所有日志
ssh bpmax4090@************* 'sudo docker logs -f promax-platform'

# 监控特定服务日志
ssh bpmax4090@************* 'tail -f ~/promax-data/supervisor-logs/nestjs-server-stdout.log'
ssh bpmax4090@************* 'tail -f ~/promax-data/supervisor-logs/python-mining-service-stdout.log'
ssh bpmax4090@************* 'tail -f ~/promax-data/supervisor-logs/nuxt-frontend-stdout.log'
```

## 安全注意事项

1. **数据库密码**: 已在环境变量中配置，避免硬编码
2. **网络访问**: 容器内服务仅监听必要端口
3. **文件权限**: 上传目录权限已正确设置
4. **日志管理**: 日志文件自动轮转，避免磁盘空间耗尽
5. **SSH访问**: 使用免密登录，避免密码泄露
6. **容器隔离**: 服务运行在容器内，与主机系统隔离

## 备份策略

### 1. 数据备份

```bash
# 备份数据目录
ssh bpmax4090@************* 'tar czf ~/promax-data-backup-$(date +%Y%m%d).tar.gz -C ~ promax-data'

# 下载备份到本地
scp bpmax4090@*************:~/promax-data-backup-*.tar.gz ./backups/

# 恢复数据（在远程服务器上）
ssh bpmax4090@************* 'tar xzf ~/promax-data-backup-*.tar.gz -C ~'
```

### 2. 代码备份

```bash
# 备份代码目录
ssh bpmax4090@************* 'tar czf ~/promax-code-backup-$(date +%Y%m%d).tar.gz -C ~ promax-code'

# 下载代码备份
scp bpmax4090@*************:~/promax-code-backup-*.tar.gz ./backups/
```

### 3. 镜像备份

```bash
# 导出运行时镜像
ssh bpmax4090@************* 'sudo docker save promax-runtime:latest | gzip > ~/promax-runtime-backup.tar.gz'

# 下载镜像备份
scp bpmax4090@*************:~/promax-runtime-backup.tar.gz ./backups/

# 导入镜像（在目标服务器上）
gunzip -c promax-runtime-backup.tar.gz | sudo docker load
```

## 常见问题解决

### 1. 容器启动失败

```bash
# 检查环境变量配置
source scripts/load-env.sh

# 查看容器启动日志
ssh bpmax4090@************* 'sudo docker logs promax-platform'

# 检查端口占用
ssh bpmax4090@************* 'sudo netstat -tlnp | grep -E "(3100|3101|3102)"'
```

### 2. 服务无法访问

```bash
# 检查服务状态
ssh bpmax4090@************* 'sudo docker exec promax-platform supervisorctl status'

# 检查网络连接
curl -I http://*************:3100/health

# 检查防火墙设置
ssh bpmax4090@************* 'sudo ufw status'
```

### 3. 构建失败

```bash
# 重建运行时镜像
./rebuild-runtime.sh

# 清理Docker缓存
ssh bpmax4090@************* 'sudo docker system prune -f'

# 重新部署
./deploy-volume-mount.sh
```

### 4. 性能问题

```bash
# 检查资源使用
ssh bpmax4090@************* 'sudo docker stats promax-platform'

# 调整配置参数
vim config/production.env
# 修改 MAX_WORKERS, MAX_MEMORY_USAGE_PERCENT 等参数

# 重新部署
./deploy-volume-mount.sh
```

## 技术支持

如遇到问题，请按以下步骤排查：

1. 查看本文档的故障排查部分
2. 检查容器日志：`ssh bpmax4090@************* 'sudo docker logs promax-platform'`
3. 检查服务健康状态：`curl http://*************:3100/health`
4. 查看Supervisor日志：`ssh bpmax4090@************* 'tail -f ~/promax-data/supervisor-logs/*.log'`
5. 联系技术支持团队

## 附录

### 相关文档
- [Python挖掘服务部署文档](python-mining-service/DEPLOYMENT.md)
- [GPU部署指南](python-mining-service/GPU_DEPLOYMENT_GUIDE.md)
- [故障排查指南](python-mining-service/TROUBLESHOOTING.md)

### 脚本文件说明
- `deploy-volume-mount.sh`: 主要部署脚本（挂载式部署）
- `rebuild-runtime.sh`: 运行时镜像重建脚本
- `scripts/load-env.sh`: 环境变量加载脚本
- `config/production.env`: 生产环境配置文件
