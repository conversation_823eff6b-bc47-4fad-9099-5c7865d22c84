# 开发指南

## 环境要求
- Node.js >= 18.0.0
- Yarn >= 1.22.0
- PostgreSQL >= 13.0
- Redis >= 6.0

## 开发环境搭建

### 1. 克隆项目
```bash
git clone <repository-url>
cd bpmax-pro-mined
```

### 2. 安装依赖
```bash
# 前端依赖
cd client
yarn install

# 后端依赖
cd ../server
yarn install
```

### 3. 环境配置
```bash
# 复制环境变量文件
cp server/.env.example server/.env
cp client/.env.example client/.env
```

### 4. 数据库设置
```bash
# 创建数据库
createdb promined_dev

# 运行迁移
cd server
yarn migration:run
```

### 5. 启动开发服务器
```bash
# 启动后端 (端口 3000)
cd server
yarn start:dev

# 启动前端 (端口 3001)
cd client
yarn dev
```

## 开发规范

### 代码风格
- 使用 ESLint 和 Prettier
- 遵循 TypeScript 严格模式
- 使用语义化命名

### Git 工作流
1. 从 develop 分支创建功能分支
2. 完成开发后提交 PR
3. 代码审查通过后合并

### 测试要求
- 单元测试覆盖率 > 80%
- 集成测试覆盖核心功能
- E2E 测试覆盖主要用户流程

## 调试技巧
- 使用 VS Code 调试配置
- 利用浏览器开发者工具
- 查看应用日志和错误信息
