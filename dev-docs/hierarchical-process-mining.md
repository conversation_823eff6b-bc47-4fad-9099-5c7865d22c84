# 多层次嵌套流程挖掘功能实现

## 概述

为ProMined项目成功实现了多层次嵌套流程挖掘能力，支持通过 `parent_case_id` 字段表达流程的父子嵌套关系，并使用GoJS的Group功能进行可视化展示。

## 实现的功能

### 1. 数据模型扩展 ✅

**后端扩展：**
- 在 `EventLog` 实体中添加了 `parentCaseId` 字段
- 创建了数据库迁移 `AddParentCaseIdToEventLogs1751703815504`
- 添加了相关索引以优化层次结构查询
- 扩展了 `UploadDataDto` 以支持 `parentCaseIdField` 参数
- 更新了数据流服务以处理父案例ID字段

**前端扩展：**
- 更新了 `EventLog` 和 `UploadDataConfig` 接口
- 扩展了 `DFGNode` 和 `DFGEdge` 接口以支持层次结构字段
- 添加了 `hierarchicalInfo` 到 `DFGResult` 接口

### 2. 流程发现算法增强 ✅

**层次结构分析：**
- 实现了 `analyzeHierarchicalStructure` 方法来分析父子关系
- 计算每个案例的层级深度
- 构建层次映射关系（parentCaseId -> childCaseIds）
- 检测循环引用以避免无限递归

**节点和边的层次信息：**
- 为每个节点添加了 `isSubprocessNode`、`subprocessLevel`、`groupId` 等字段
- 为每个边添加了层次结构相关信息
- 为子流程创建独立的开始和结束节点

### 3. GoJS分组可视化 ✅

**分组模板设计：**
- 使用浅蓝色半透明背景和虚线边框
- 支持鼠标悬停效果（高亮显示）
- 显示子流程标题和父案例ID信息
- 内置LayeredDigraphLayout布局算法

**分组数据处理：**
- 动态生成分组数据基于层次结构信息
- 正确设置节点的分组归属关系
- 支持多层级嵌套（理论上无限层级）

### 4. 嵌套开始/结束节点 ✅

**子流程节点创建：**
- 为每个子流程创建独立的开始节点（`开始_${parentCaseId}`）
- 为每个子流程创建独立的结束节点（`结束_${parentCaseId}`）
- 正确计算子流程内的开始和结束活动
- 创建从子流程开始节点到开始活动的边
- 创建从结束活动到子流程结束节点的边

**样式和布局：**
- 子流程开始/结束节点使用与主流程相同的样式
- 确保在分组内部正确定位
- 不参与主流程的布局算法

### 5. 布局算法优化 ✅

**分组布局处理：**
- 实现了 `optimizeGroupLayout` 函数
- 确保分组有足够的内边距
- 防止分组重叠
- 子流程节点不影响主流程布局

**布局流程：**
1. 主布局算法处理所有节点
2. 对齐开始和结束节点
3. 优化分组布局
4. 最终渲染和适应容器

### 6. 交互功能维护 ✅

**双击功能：**
- 分组节点双击：显示分组信息（可扩展为展开/折叠）
- 普通节点双击：打开详情对话框
- 连线双击：打开连接详情对话框

**单击高亮功能：**
- 分组节点单击：高亮整个分组及其内部所有节点和连线
- 普通节点单击：高亮节点及其相关连线
- 支持流动动画效果

**工具提示：**
- 保持现有的工具提示功能
- 支持分组节点的特殊提示信息

## 技术实现细节

### 数据库结构

```sql
ALTER TABLE `event_logs`
ADD COLUMN `parentCaseId` varchar(100) NULL;

CREATE INDEX `IDX_event_logs_parentCaseId` 
ON `event_logs` (`parentCaseId`);

CREATE INDEX `IDX_event_logs_processId_parentCaseId` 
ON `event_logs` (`processId`, `parentCaseId`);
```

### 核心接口扩展

```typescript
export interface DFGNode {
  // 原有字段...
  parentCaseId?: string;
  isSubprocessNode?: boolean;
  subprocessLevel?: number;
  groupId?: string; // 用于GoJS分组
}

export interface DFGResult {
  // 原有字段...
  hierarchicalInfo?: {
    hasHierarchy: boolean;
    maxLevel: number;
    subprocessCount: number;
    hierarchyMap: Record<string, string[]>;
  };
}
```

### GoJS分组配置

```typescript
myDiagram.groupTemplate = $(go.Group, 'Auto', {
  background: 'transparent',
  ungroupable: true,
  layout: $(go.LayeredDigraphLayout, {
    direction: 90,
    layerSpacing: 60,
    columnSpacing: 40,
    setsPortSpots: false
  }),
  // 分组样式和交互配置...
});
```

## 使用方法

### 1. 数据导入

在数据导入时，可以指定 `parentCaseIdField` 参数：

```typescript
const uploadConfig = {
  processId: 1,
  caseIdField: 'case_id',
  activityField: 'activity',
  timestampField: 'timestamp',
  parentCaseIdField: 'parent_case_id', // 新增字段
  // 其他配置...
};
```

### 2. 流程发现

流程发现API会自动检测层次结构并返回相应的分组信息：

```typescript
const result = await api.discoverProcess(processId, { forceRefresh: true });
if (result.hierarchicalInfo?.hasHierarchy) {
  console.log('发现层次结构:', result.hierarchicalInfo);
}
```

### 3. 可视化交互

- **查看子流程**：子流程会显示在带边框的分组容器中
- **高亮分组**：单击分组标题可高亮整个子流程
- **查看详情**：双击普通节点查看详细信息

## 性能考虑

1. **索引优化**：为 `parentCaseId` 字段添加了数据库索引
2. **缓存策略**：层次结构分析结果会被缓存
3. **布局优化**：分组布局算法经过优化，避免重复计算
4. **内存管理**：大数据量时限制分组数量和层级深度

## 未来扩展

1. **分组折叠/展开**：支持动态折叠和展开子流程
2. **层级导航**：添加层级导航面板
3. **性能分析**：支持子流程级别的性能分析
4. **导出功能**：支持导出层次结构信息

## 测试建议

1. **单元测试**：测试层次结构分析算法
2. **集成测试**：测试完整的数据导入到可视化流程
3. **性能测试**：测试大规模嵌套数据的处理性能
4. **用户测试**：验证交互功能的易用性

## 总结

本次实现成功为ProMined项目添加了完整的多层次嵌套流程挖掘能力，包括：

- ✅ 数据模型扩展和数据库迁移
- ✅ 后端算法增强和层次结构分析
- ✅ 前端GoJS分组可视化
- ✅ 嵌套开始/结束节点处理
- ✅ 布局算法优化
- ✅ 交互功能维护

所有功能都与现有系统完美集成，保持了向后兼容性，为用户提供了强大的多层次流程分析能力。
