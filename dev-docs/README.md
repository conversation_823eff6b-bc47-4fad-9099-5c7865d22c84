# ProMax 开发文档

ProMax 是一个基于流程挖掘的业务流程分析套件。

## 项目结构

```
bpmax-pro-mined/
├── client/          # Nuxt3 前端应用
├── server/          # NestJS 后端应用
└── dev-docs/        # 开发文档
```

## 技术栈

### 前端 (client)
- **框架**: Nuxt 3
- **包管理器**: Yarn
- **UI库**: Nuxt UI (基于 Tailwind CSS)
- **图标**: Nuxt Icon
- **图片处理**: Nuxt Image
- **字体**: Nuxt Fonts
- **代码规范**: ESLint

### 后端 (server)
- **框架**: NestJS
- **包管理器**: Yarn
- **语言**: TypeScript
- **测试**: Jest

## 快速开始

### 前端开发
```bash
cd client
yarn dev
```

### 后端开发
```bash
cd server
yarn start:dev
```

## 开发规范

### 代码提交规范
- 使用语义化提交信息
- 每次提交前运行 lint 检查

### 分支管理
- main: 主分支
- develop: 开发分支
- feature/*: 功能分支
- hotfix/*: 热修复分支

## 文档目录

- [API 文档](./api/)
- [数据库设计](./database/)
- [部署文档](./deployment/)
- [开发指南](./development/)
- [架构设计](./architecture/)
