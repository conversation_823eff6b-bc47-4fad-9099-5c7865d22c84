# API 文档

## 概述
ProMax 后端 API 文档

## 基础信息
- 基础URL: `http://localhost:3000`
- 认证方式: JWT Token
- 数据格式: JSON

## API 端点

### 认证相关
- `POST /auth/login` - 用户登录
- `POST /auth/register` - 用户注册
- `POST /auth/refresh` - 刷新令牌

### 流程挖掘相关
- `GET /processes` - 获取流程列表
- `POST /processes` - 创建新流程
- `GET /processes/:id` - 获取流程详情
- `PUT /processes/:id` - 更新流程
- `DELETE /processes/:id` - 删除流程

### 数据分析相关
- `POST /analysis/upload` - 上传数据文件
- `GET /analysis/results/:id` - 获取分析结果
- `POST /analysis/visualize` - 生成可视化图表

## 错误码
- 200: 成功
- 400: 请求参数错误
- 401: 未授权
- 403: 禁止访问
- 404: 资源不存在
- 500: 服务器内部错误
