# 架构设计

## 系统架构

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Nuxt3)   │    │  后端 (NestJS)   │    │   数据库层       │
│                 │    │                 │    │                 │
│ - Vue 3         │◄──►│ - RESTful API   │◄──►│ - PostgreSQL    │
│ - Nuxt UI       │    │ - GraphQL       │    │ - Redis         │
│ - TypeScript    │    │ - TypeScript    │    │ - Min<PERSON>         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 前端架构
- **框架**: Nuxt 3 (Vue 3 + SSR)
- **状态管理**: Pinia
- **UI组件**: Nuxt UI + Tailwind CSS
- **路由**: Nuxt Router (基于文件系统)
- **数据获取**: $fetch / useFetch

### 后端架构
- **框架**: NestJS
- **架构模式**: 模块化 + 依赖注入
- **数据访问**: TypeORM
- **认证**: JWT + Passport
- **API文档**: Swagger

### 数据层架构
- **主数据库**: PostgreSQL (关系型数据)
- **缓存层**: Redis (会话、缓存)
- **文件存储**: Min<PERSON> (文件、图片)

## 核心模块

### 前端模块
```
client/
├── components/     # 可复用组件
├── pages/         # 页面组件
├── layouts/       # 布局组件
├── middleware/    # 中间件
├── plugins/       # 插件
├── stores/        # Pinia 状态管理
├── utils/         # 工具函数
└── types/         # TypeScript 类型定义
```

### 后端模块
```
server/src/
├── auth/          # 认证模块
├── users/         # 用户管理
├── processes/     # 流程管理
├── analysis/      # 数据分析
├── common/        # 公共模块
├── database/      # 数据库配置
└── config/        # 配置管理
```

## 流程可视化格式

### 主要可视化格式
- **DFG (Directly-Follows Graph)**: 主要格式，类似 Celonis 风格
  - 显示活动间的直接跟随关系
  - 包含频率和性能信息
  - 简洁直观，适合业务用户
- **BPMN 2.0**: 标准业务流程建模格式
  - 符合国际标准
  - 适合流程文档化
  - 支持复杂流程结构

## 设计原则
1. **单一职责**: 每个模块只负责一个功能
2. **开闭原则**: 对扩展开放，对修改关闭
3. **依赖倒置**: 依赖抽象而非具体实现
4. **接口隔离**: 使用小而专一的接口

## 安全考虑
- JWT Token 认证
- CORS 跨域保护
- SQL 注入防护
- XSS 攻击防护
- 数据加密存储
