# DFG流程耗时维度可视化功能

## 功能概述

在ProMined项目的DFG（Directly-Follows Graph）图表中成功增加了流程耗时维度的可视化表达，为用户提供了更全面的流程分析能力。

## 实现的功能

### 1. 耗时数据计算（后端）

**文件**: `server/src/analysis/process-mining.service.ts`

- **扩展数据结构**: 为`DFGNode`和`DFGEdge`接口添加了`avgDuration`、`minDuration`、`maxDuration`字段
- **活动耗时计算**: 实现了`calculateActivityDurations`方法，计算每个活动的平均、最小、最大耗时
- **转换耗时计算**: 实现了`calculateTransitionDurations`方法，计算活动间转换的耗时统计
- **数据集成**: 在`discoverProcess`方法中集成耗时计算，确保DFG结果包含完整的耗时信息

### 2. 前端可视化增强

**文件**: `client/pages/analysis/[processId]/discover.vue`

#### 2.1 维度切换控制
- 添加了频率/耗时维度切换按钮
- 用户可以在两种可视化模式间无缝切换
- 实时更新图表样式和标签显示

#### 2.2 科学映射算法
- **百分位数映射**: 使用P25、P50、P75、P90百分位数进行数据分层
- **对数缩放支持**: 对于高度偏斜的耗时数据自动应用对数变换
- **平方根尺寸映射**: 使用平方根函数使视觉差异更加明显但不过于极端

#### 2.3 颜色方案设计
- **频率维度**: 绿色系（深绿→浅绿，表示高频→低频）
- **耗时维度**: 红绿渐变（红色→绿色，表示高耗时→低耗时）
- **连接线颜色**: 根据维度动态调整，确保视觉一致性

#### 2.4 交互功能
- **悬浮提示**: 显示活动/转换的耗时信息
- **详情弹窗**: 点击节点或连接线显示完整的耗时统计
- **动态标签**: 根据当前维度显示频率或耗时信息

### 3. 用户体验优化

#### 3.1 图表说明更新
- 根据当前维度动态显示相应的说明文字
- 提供清晰的颜色和尺寸映射解释

#### 3.2 专业性保证
- 遵循流程挖掘领域的可视化标准
- 使用科学的数据映射算法
- 确保大数据量下的性能表现

## 技术特点

### 1. 数据驱动设计
- 所有视觉元素基于实际数据分布动态调整
- 自动检测数据特征并选择合适的映射策略

### 2. 响应式布局
- 支持不同屏幕尺寸的自适应显示
- 维度切换控制与现有UI风格一致

### 3. 性能优化
- 高效的数据处理算法
- 智能缓存策略
- 平滑的动画过渡

## 使用方法

1. **访问流程发现页面**: 导航到`/analysis/{processId}/discover`
2. **执行流程发现**: 点击"开始流程发现"按钮
3. **切换维度**: 使用图表头部的"频率"/"耗时"切换按钮
4. **查看详情**: 
   - 鼠标悬停查看快速信息
   - 点击节点/连接线查看详细统计
5. **图表操作**: 支持缩放、平移等标准操作

## 数据格式

### 节点数据结构
```typescript
interface DFGNode {
  id: string
  label: string
  frequency: number
  avgDuration?: number    // 平均耗时（毫秒）
  minDuration?: number    // 最小耗时（毫秒）
  maxDuration?: number    // 最大耗时（毫秒）
}
```

### 连接线数据结构
```typescript
interface DFGEdge {
  source: string
  target: string
  frequency: number
  avgDuration?: number    // 平均转换耗时（毫秒）
  minDuration?: number    // 最小转换耗时（毫秒）
  maxDuration?: number    // 最大转换耗时（毫秒）
}
```

## 测试验证

功能已通过以下方式验证：
- 后端API正确计算耗时数据
- 前端正确渲染两种维度的可视化
- 维度切换功能正常工作
- 交互功能响应正确
- 在不同数据规模下性能良好

## 未来扩展

1. **更多耗时指标**: 可添加中位数、标准差等统计指标
2. **时间范围过滤**: 支持按时间段分析耗时变化
3. **耗时预警**: 基于历史数据设置耗时阈值预警
4. **导出功能**: 支持导出耗时分析报告

## 总结

本次实现成功为ProMined项目的DFG可视化添加了专业的耗时维度分析能力，提升了流程挖掘分析的深度和实用性。通过科学的数据映射算法和直观的交互设计，用户可以更好地理解流程中的时间瓶颈和效率问题。
