# 部署文档

## 部署架构

### 生产环境
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Nginx     │    │  应用服务器   │    │   数据库     │
│  (反向代理)   │◄──►│             │◄──►│             │
│             │    │ - Frontend  │    │ - PostgreSQL│
│             │    │ - Backend   │    │ - Redis     │
└─────────────┘    └─────────────┘    └─────────────┘
```

### Docker 部署

#### 1. 构建镜像
```bash
# 构建前端镜像
cd client
docker build -t promined-frontend .

# 构建后端镜像
cd ../server
docker build -t promined-backend .
```

#### 2. Docker Compose
```yaml
version: '3.8'
services:
  frontend:
    image: promined-frontend
    ports:
      - "3001:3000"
    environment:
      - NUXT_API_BASE_URL=http://backend:3000
    depends_on:
      - backend

  backend:
    image: promined-backend
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=************************************/promined
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=promined
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### Kubernetes 部署

#### 1. 命名空间
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: promined
```

#### 2. 配置映射
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: promined-config
  namespace: promined
data:
  DATABASE_HOST: postgres-service
  REDIS_HOST: redis-service
```

#### 3. 部署清单
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: promined-backend
  namespace: promined
spec:
  replicas: 3
  selector:
    matchLabels:
      app: promined-backend
  template:
    metadata:
      labels:
        app: promined-backend
    spec:
      containers:
      - name: backend
        image: promined-backend:latest
        ports:
        - containerPort: 3000
        envFrom:
        - configMapRef:
            name: promined-config
```

### CI/CD 流程

#### GitHub Actions
```yaml
name: Deploy to Production
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Build and Deploy
      run: |
        docker build -t promined-app .
        docker push registry/promined-app:latest
        kubectl apply -f k8s/
```

### 监控和日志
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack (Elasticsearch + Logstash + Kibana)
- **错误追踪**: Sentry
- **性能监控**: New Relic

### 备份策略
- 数据库每日自动备份
- 文件存储定期同步
- 配置文件版本控制
