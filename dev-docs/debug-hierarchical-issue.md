# 调试层次结构问题

## 问题描述

从用户提供的数据和JSON结果来看，虽然原始数据中有 `parent_case_id` 字段，但在流程发现结果中：

1. 所有节点的 `subprocessLevel` 都是 0
2. 所有节点的 `isSubprocessNode` 都是 false  
3. 没有 `hierarchicalInfo` 字段
4. 但在 `businessFields` 中确实有 `parent_case_id` 数据

## 可能的原因

### 1. 字段映射问题
- 用户在字段映射时可能没有将 `parent_case_id` 字段映射为 "父案例ID"
- 或者字段映射功能还没有包含父案例ID选项

### 2. 数据类型问题
- 数据中的 `parent_case_id` 可能是数字类型
- 但我们的代码期望字符串类型

### 3. 数据处理问题
- `parentCaseId` 字段可能没有被正确保存到数据库
- 或者在查询时没有被正确读取

## 调试步骤

### 1. 检查数据库中的数据
```sql
SELECT case_id, parent_case_id, activity FROM event_logs WHERE process_id = 21 LIMIT 10;
```

### 2. 检查字段映射配置
- 确认用户在上传数据时是否映射了父案例ID字段

### 3. 检查层次结构分析日志
- 查看控制台输出，确认是否有层次结构分析的调试信息

## 解决方案

### 1. 立即解决方案
- 在前端字段映射界面添加父案例ID选项 ✅ (已完成)
- 用户重新上传数据并正确映射字段

### 2. 数据修复方案
- 如果数据已经在数据库中，可以通过SQL更新 `parentCaseId` 字段
- 或者提供数据修复工具

### 3. 代码改进
- 增强数据类型处理，支持数字到字符串的转换
- 改进错误处理和日志记录
