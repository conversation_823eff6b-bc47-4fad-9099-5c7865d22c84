# 数据库设计

## 概述
ProMax 数据库设计文档

## 数据库选择
- **主数据库**: PostgreSQL
- **缓存**: Redis
- **文件存储**: MinIO/AWS S3

## 核心表结构

### 用户表 (users)
```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 流程表 (processes)
```sql
CREATE TABLE processes (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  user_id INTEGER REFERENCES users(id),
  status VARCHAR(20) DEFAULT 'draft',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 事件日志表 (event_logs)
```sql
CREATE TABLE event_logs (
  id SERIAL PRIMARY KEY,
  process_id INTEGER REFERENCES processes(id),
  case_id VARCHAR(100) NOT NULL,
  activity VARCHAR(100) NOT NULL,
  timestamp TIMESTAMP NOT NULL,
  resource VARCHAR(100),
  attributes JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 分析结果表 (analysis_results)
```sql
CREATE TABLE analysis_results (
  id SERIAL PRIMARY KEY,
  process_id INTEGER REFERENCES processes(id),
  analysis_type VARCHAR(50) NOT NULL,
  result_data JSONB NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 索引策略
- 用户表: username, email
- 流程表: user_id, status
- 事件日志表: process_id, case_id, timestamp
- 分析结果表: process_id, analysis_type

## 数据迁移
使用 TypeORM 进行数据库迁移管理
