# 多层级嵌套流程挖掘的前置活动推断逻辑

## 概述

在多层级嵌套流程挖掘中，当处理具有`parent_case_id`但缺少`previousActivity`字段的活动记录时，系统需要智能推断其前置活动节点。本文档详细说明了实现的推断逻辑和算法。

## 核心需求

当活动记录满足以下条件时，需要进行前置活动推断：
- 包含`parent_case_id`字段（表示这是一个子流程活动）
- `previousActivity`字段为空或null（缺少明确的前置活动信息）

## 推断规则

### 1. 基本推断逻辑

**核心原则**：子流程活动的前置活动节点应该是其父流程（`parent_case_id`对应的案例）中在当前活动开始时间点正在执行的活动节点。

### 2. 具体实现步骤

#### 步骤1：查找父流程事件
```typescript
const parentCaseEvents = allEvents.filter(
  event => event.caseId === currentEvent.parentCaseId
);
```

#### 步骤2：确定时间范围
```typescript
const currentEventTime = currentEvent.timestamp.getTime();
```

#### 步骤3：查找正在执行的父流程活动
对于每个父流程活动，计算其执行时间范围：
- **开始时间**：`parentEvent.timestamp.getTime()`
- **结束时间**：
  - 优先使用`parentEvent.endTimestamp.getTime()`
  - 如果没有结束时间，使用下一个活动的开始时间
  - 如果是最后一个活动，假设在子流程开始时仍在执行

#### 步骤4：匹配正在执行的活动
```typescript
if (parentStartTime <= currentEventTime && currentEventTime < parentEndTime) {
  activeParentActivity = parentEvent.activity;
  break;
}
```

### 3. 边界情况处理

#### 情况1：没有正在执行的活动
当父流程在子流程活动开始时间点没有正在执行的活动时：

1. **查找最近完成的活动**：找到在当前时间点之前结束的最后一个父流程活动
2. **连接到开始节点**：如果没有已完成的活动，连接到父流程的开始节点

#### 情况2：多层级嵌套
系统支持三层或更多层级的嵌套：
- 每一层都独立进行前置活动推断
- 推断逻辑递归应用到各个层级

## 代码实现

### 核心方法

#### `inferPreviousActivityFromParentCase`
```typescript
private inferPreviousActivityFromParentCase(
  currentEvent: EventLog,
  allEvents: EventLog[]
): string | null
```

**功能**：从父流程推断前置活动节点

**参数**：
- `currentEvent`：当前需要推断前置活动的事件
- `allEvents`：所有事件日志数据

**返回值**：推断出的前置活动名称，如果无法推断则返回null

#### `handleBoundaryCase`
```typescript
private handleBoundaryCase(
  currentEvent: EventLog,
  sortedParentEvents: EventLog[],
  currentEventTime: number
): string | null
```

**功能**：处理边界情况

### 集成到流程发现

推断逻辑已集成到以下方法中：

1. **`buildRelationsByPreviousActivity`**：构建活动关系时应用推断逻辑
2. **`calculateTransitionDurationsByPreviousActivity`**：计算转换耗时时使用推断的前置活动

## 测试验证

### 测试用例

#### 1. 基本推断测试
- **场景**：子流程活动在父流程活动执行期间启动
- **期望**：成功推断出正在执行的父流程活动作为前置活动

#### 2. 边界情况测试
- **场景**：子流程活动在父流程活动结束后启动
- **期望**：使用最近完成的父流程活动作为前置活动

#### 3. 多层级嵌套测试
- **场景**：三层或更多层级的嵌套流程
- **期望**：每一层都能正确推断前置活动

### 测试结果
所有测试用例均通过，推断成功率达到100%。

## API接口

### 测试接口
```
GET /api/v1/analysis/test-hierarchical-inference/:processId
```

**功能**：测试多层级嵌套流程挖掘的前置活动推断逻辑

**响应示例**：
```json
{
  "message": "多层级前置活动推断测试完成",
  "data": {
    "totalEvents": 4,
    "eventsNeedingInference": 1,
    "successfulInferences": 1,
    "failedInferences": 0,
    "successRate": 100,
    "totalRelations": 2,
    "topRelations": [
      {
        "relation": "主流程活动A -> 主流程活动B",
        "frequency": 1
      }
    ]
  }
}
```

## 优势与特点

### 1. 智能推断
- 基于时间逻辑进行智能推断
- 考虑活动的执行时间范围
- 处理各种边界情况

### 2. 多层级支持
- 支持任意层级的嵌套流程
- 递归应用推断逻辑
- 保持层级关系的完整性

### 3. 鲁棒性
- 优雅处理缺失数据
- 提供多种回退策略
- 详细的日志记录便于调试

### 4. 性能优化
- 高效的时间范围计算
- 最小化数据遍历次数
- 缓存友好的实现

## 使用场景

1. **业务流程分析**：分析包含子流程的复杂业务流程
2. **工作流优化**：识别跨层级的流程瓶颈
3. **合规性检查**：验证多层级流程的执行顺序
4. **流程挖掘**：从不完整的日志数据中重建完整的流程图

## 注意事项

1. **数据质量**：推断准确性依赖于时间戳的准确性
2. **性能考虑**：大量嵌套层级可能影响性能
3. **业务逻辑**：推断结果需要结合业务逻辑进行验证
4. **日志记录**：建议启用详细日志以便问题排查

## 未来改进

1. **机器学习增强**：使用历史数据训练推断模型
2. **业务规则集成**：支持自定义业务规则
3. **可视化支持**：提供推断过程的可视化展示
4. **性能优化**：针对大规模数据的性能优化
