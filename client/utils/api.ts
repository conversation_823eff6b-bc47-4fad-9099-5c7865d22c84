import type {
  User,
  LoginCredentials,
  RegisterData,
  AuthResponse,
  Process,
  CreateProcessData,
  ProcessStatistics,
  DataStatistics,
  DFGResult,
  VariantResult,
  PerformanceResult,
  AnalysisResult,
  AnalysisType,
  UploadDataConfig,
  DataValidationResult,
  BpmnModel,
  BpmnModelValidation,
  ConformanceResult,
  ConformanceCheckRequest,
  ProcessDiscoveryOptions,
  PerformanceAnalysisOptions,
  ProcessCompareOptions,
  ProcessCompareResult,
  NodeDetailInfo,
  EdgeDetailInfo,
  ConformanceStatistics,
  ProcessingStatus,
  DatabaseConnection,
  CreateDatabaseConnectionDto,
  UpdateDatabaseConnectionDto,
  TestConnectionDto,
  TestConnectionResponse,
  ExecuteQueryDto,
  QueryResult,
  PaginationMeta
} from '~/types'

class ApiClient {
  private baseURL: string | null = null
  private token: string | null = null

  constructor() {
    // 延迟初始化，在第一次使用时获取配置
    // 从localStorage获取token，检查是否过期
    if (import.meta.client) {
      this.loadTokenFromStorage()
    }
  }

  private loadTokenFromStorage() {
    try {
      const token = localStorage.getItem('auth_token_content')
      const tokenExpiry = localStorage.getItem('auth_token_expiry')

      console.log('加载存储的认证信息:', {
        hasToken: !!token,
        hasExpiry: !!tokenExpiry,
        token: token ? `${token.substring(0, 10)}...` : null,
        expiry: tokenExpiry
      })

      // 检查数据一致性
      if ((token && !tokenExpiry) || (!token && tokenExpiry)) {
        console.warn('认证数据不一致，清除所有存储:', { hasToken: !!token, hasExpiry: !!tokenExpiry })
        this.clearToken()
        return
      }

      if (token && tokenExpiry) {
        const expiryTime = parseInt(tokenExpiry)
        const currentTime = Date.now()

        // 如果token未过期，则使用它
        if (currentTime < expiryTime) {
          this.token = token
          console.log('Token有效，已加载')
        } else {
          // token已过期，清除存储
          console.log('Token已过期，清除存储')
          this.clearToken()
        }
      } else {
        console.log('未找到存储的认证信息')
      }
    } catch (error) {
      console.error('加载认证信息时出错:', error)
      this.clearToken()
    }
  }

  private getBaseURL(): string {
    if (!this.baseURL) {
      const config = useRuntimeConfig()
      this.baseURL = config.public.apiBase.replace(/\/$/, '')
    }
    return this.baseURL
  }

  private getHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
    }

    return headers
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.getBaseURL()}/api/v1${endpoint}`
    
    const response = await fetch(url, {
      ...options,
      headers: {
        ...this.getHeaders(),
        ...options.headers,
      },
    })

    if (!response.ok) {
      // 401未授权时自动跳转到登录页
      if (response.status === 401 && typeof window !== 'undefined') {
        // 清除token，跳转登录页
        this.clearToken()
        // Nuxt3 navigateTo
        if (typeof navigateTo === 'function') {
          navigateTo('/auth/login')
        } else {
          window.location.href = '/auth/login'
        }
      }
      const error = await response.json().catch(() => ({ message: 'Network error' }))
      throw new Error(error.message || `HTTP ${response.status}`)
    }

    return response.json()
  }

  setToken(token: string) {
    this.token = token
    if (import.meta.client) {
      // 设置30天的过期时间
      const expiryTime = Date.now() + (30 * 24 * 60 * 60 * 1000) // 30天
      localStorage.setItem('auth_token_content', token)
      localStorage.setItem('auth_token_expiry', expiryTime.toString())
    }
  }

  clearToken() {
    console.log('清除认证信息')
    this.token = null
    if (import.meta.client) {
      try {
        // 检查清除前的状态
        const beforeToken = localStorage.getItem('auth_token_content')
        const beforeExpiry = localStorage.getItem('auth_token_expiry')
        console.log('清除前的存储状态:', { hasToken: !!beforeToken, hasExpiry: !!beforeExpiry })

        localStorage.removeItem('auth_token_content')
        localStorage.removeItem('auth_token_expiry')

        // 验证清除结果
        const afterToken = localStorage.getItem('auth_token_content')
        const afterExpiry = localStorage.getItem('auth_token_expiry')
        console.log('清除后的存储状态:', { hasToken: !!afterToken, hasExpiry: !!afterExpiry })

        if (afterToken || afterExpiry) {
          console.error('警告：认证信息未完全清除!', { afterToken: !!afterToken, afterExpiry: !!afterExpiry })
        }
      } catch (error) {
        console.error('清除认证信息时出错:', error)
      }
    }
  }

  // 认证相关API
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    })
    
    this.setToken(response.access_token)
    return response
  }

  async register(data: RegisterData): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(data),
    })
    
    this.setToken(response.access_token)
    return response
  }

  async getProfile(): Promise<User> {
    return this.request<User>('/auth/profile')
  }

  // 用户相关API
  async getUsers(): Promise<User[]> {
    return this.request<User[]>('/users')
  }

  async getUser(id: number): Promise<User> {
    return this.request<User>(`/users/${id}`)
  }

  async updateUser(id: number, data: Partial<User>): Promise<User> {
    return this.request<User>(`/users/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    })
  }

  // 流程相关API
  async getProcesses(): Promise<Process[]> {
    return this.request<Process[]>('/processes')
  }

  async getProcess(id: number): Promise<Process> {
    return this.request<Process>(`/processes/${id}`)
  }

  async createProcess(data: CreateProcessData): Promise<Process> {
    return this.request<Process>('/processes', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async updateProcess(id: number, data: Partial<CreateProcessData>): Promise<Process> {
    return this.request<Process>(`/processes/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    })
  }

  async deleteProcess(id: number): Promise<void> {
    await this.request(`/processes/${id}`, {
      method: 'DELETE',
    })
  }

  async getProcessStatistics(): Promise<ProcessStatistics> {
    return this.request<ProcessStatistics>('/processes/statistics')
  }

  // 数据分析相关API
  async uploadData(file: File, config: UploadDataConfig): Promise<any> {
    const formData = new FormData()
    formData.append('file', file)
    Object.entries(config).forEach(([key, value]) => {
      formData.append(key, value.toString())
    })

    const response = await fetch(`${this.getBaseURL()}/api/v1/analysis/upload`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.token}`,
      },
      body: formData,
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Upload failed' }))
      throw new Error(error.message || `HTTP ${response.status}`)
    }

    return response.json()
  }

  async validateData(file: File, config: UploadDataConfig): Promise<{ validation: DataValidationResult }> {
    const formData = new FormData()
    formData.append('file', file)
    Object.entries(config).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        formData.append(key, value.toString())
      }
    })

    const response = await fetch(`${this.getBaseURL()}/api/v1/analysis/validate`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.token}`,
      },
      body: formData,
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Validation failed' }))
      throw new Error(error.message || `HTTP ${response.status}`)
    }

    return response.json()
  }

  // 获取文件预览数据
  async getFilePreview(file: File): Promise<{ columns: string[], preview: any[], totalRows: number }> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await fetch(`${this.getBaseURL()}/api/v1/analysis/preview`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.token}`,
      },
      body: formData,
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Preview failed' }))
      throw new Error(error.message || `HTTP ${response.status}`)
    }

    return response.json()
  }

  // 开始多步骤流程发现（文件上传模式）
  async startDiscoveryWizard(processId: number, file: File, config: UploadDataConfig): Promise<{ discoveryId: string }> {
    const formData = new FormData()
    formData.append('file', file)
    Object.entries(config).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        formData.append(key, value.toString())
      }
    })

    const response = await fetch(`${this.getBaseURL()}/api/v1/analysis/discovery-wizard/${processId}`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.token}`,
      },
      body: formData,
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Discovery wizard failed' }))
      throw new Error(error.message || `HTTP ${response.status}`)
    }

    return response.json()
  }

  // 开始多步骤流程发现（数据库查询模式）
  async startDiscoveryWizardWithData(processId: number, queryData: { columns: string[], data: any[], totalRows: number }, config: UploadDataConfig): Promise<{ discoveryId: string }> {
    const requestBody = {
      queryData,
      config
    }

    const response = await fetch(`${this.getBaseURL()}/api/v1/analysis/discovery-wizard/${processId}/database`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.token}`,
      },
      body: JSON.stringify(requestBody),
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Discovery wizard with database data failed' }))
      throw new Error(error.message || `HTTP ${response.status}`)
    }

    return response.json()
  }

  // 获取流程发现状态
  async getDiscoveryStatus(discoveryId: string): Promise<ProcessingStatus> {
    return this.request<ProcessingStatus>(`/analysis/discovery-status/${discoveryId}`)
  }

  // 取消流程发现
  async cancelDiscovery(discoveryId: string): Promise<{ success: boolean; message: string; discoveryId: string }> {
    return this.request<{ success: boolean; message: string; discoveryId: string }>(`/analysis/cancel-discovery/${discoveryId}`, {
      method: 'DELETE',
    })
  }

  async getDataStatistics(processId: number): Promise<DataStatistics> {
    return this.request<DataStatistics>(`/analysis/statistics/${processId}`)
  }

  // 流程挖掘相关API
  async discoverProcess(processId: number, options: ProcessDiscoveryOptions = {}): Promise<DFGResult> {
    // 为了向后兼容，如果传入的是boolean，转换为options对象
    if (typeof options === 'boolean') {
      options = { forceRefresh: options }
    }

    // 如果有配置参数，使用POST body，否则使用查询参数（向后兼容）
    if (options.requiredActivities && options.requiredActivities.length > 0) {
      return this.request<DFGResult>(`/analysis/discover/${processId}`, {
        method: 'POST',
        body: JSON.stringify(options),
      })
    } else {
      // 向后兼容：只有forceRefresh时使用查询参数
      const params = options.forceRefresh ? '?forceRefresh=true' : ''
      return this.request<DFGResult>(`/analysis/discover/${processId}${params}`, {
        method: 'POST',
      })
    }
  }

  async analyzeVariants(processId: number): Promise<VariantResult> {
    return this.request<VariantResult>(`/analysis/variants/${processId}`, {
      method: 'POST',
    })
  }

  async analyzePerformance(processId: number, options: PerformanceAnalysisOptions | boolean = {}): Promise<PerformanceResult> {
    // 为了向后兼容，如果传入的是boolean，转换为options对象
    if (typeof options === 'boolean') {
      options = { forceRefresh: options }
    }

    // 如果有配置参数，使用POST body，否则使用查询参数（向后兼容）
    if (options.requiredActivities && options.requiredActivities.length > 0) {
      return this.request<PerformanceResult>(`/analysis/performance/${processId}`, {
        method: 'POST',
        body: JSON.stringify(options),
      })
    } else {
      // 向后兼容：只有forceRefresh时使用查询参数
      const params = options.forceRefresh ? '?forceRefresh=true' : ''
      return this.request<PerformanceResult>(`/analysis/performance/${processId}${params}`, {
        method: 'POST',
      })
    }
  }

  async compareProcess(processId: number, options: ProcessCompareOptions): Promise<ProcessCompareResult> {
    return this.request<ProcessCompareResult>(`/analysis/compare/${processId}`, {
      method: 'POST',
      body: JSON.stringify(options),
    })
  }

  async getAnalysisResult(processId: number, analysisType: AnalysisType): Promise<any> {
    return this.request<any>(`/analysis/results/${processId}/${analysisType}`)
  }

  async discoverSubprocess(processId: number, options: any): Promise<any> {
    return this.request(`/analysis/subprocess-discovery/${processId}`, {
      method: 'POST',
      body: JSON.stringify(options)
    })
  }

  // DFG详细信息相关API
  async getNodeDetailInfo(processId: number, nodeId: string): Promise<NodeDetailInfo> {
    return this.request<NodeDetailInfo>(`/analysis/node-detail/${processId}/${encodeURIComponent(nodeId)}`)
  }

  async getEdgeDetailInfo(processId: number, sourceId: string, targetId: string): Promise<EdgeDetailInfo> {
    return this.request<EdgeDetailInfo>(`/analysis/edge-detail/${processId}/${encodeURIComponent(sourceId)}/${encodeURIComponent(targetId)}`)
  }

  // 缓存相关API
  async getCacheStatus(processId: number): Promise<{
    totalCached: number
    byType: Record<AnalysisType, boolean>
  }> {
    return this.request(`/analysis/cache/status/${processId}`)
  }

  // 获取指定时间范围内的数据计数
  async getDataCountByTimeRange(processId: number, startTime: string, endTime: string): Promise<{
    totalEvents: number
    uniqueCases: number
    uniqueActivities: number
    uniqueResources: number
  }> {
    return this.request(`/analysis/data-count/${processId}?startTime=${encodeURIComponent(startTime)}&endTime=${encodeURIComponent(endTime)}`)
  }

  // 获取指定周期的详细数据
  async getPeriodDetails(processId: number, period: string, params: {
    granularity?: string
    requiredActivities?: string[]
  } = {}): Promise<{
    period: string
    rankings: {
      activity: {
        improvements: Array<{
          name: string
          change: number
          currentValue: number
          previousValue: number
        }>
        declines: Array<{
          name: string
          change: number
          currentValue: number
          previousValue: number
        }>
      }
      resource: {
        improvements: Array<{
          name: string
          change: number
          currentValue: number
          previousValue: number
        }>
        declines: Array<{
          name: string
          change: number
          currentValue: number
          previousValue: number
        }>
      }
    }
    activityDurations: Record<string, Array<{ name: string; value: number }>>
    resourceWaitTimes: Record<string, {
      legend: string[]
      data: number[][]
      periods: string[]
    }>
  }> {
    const queryParams = new URLSearchParams()
    queryParams.append('period', period)
    if (params.granularity) queryParams.append('granularity', params.granularity)

    const queryString = queryParams.toString()
    const url = `/analysis/period-details/${processId}?${queryString}`

    const requestBody: any = {}
    if (params.requiredActivities && params.requiredActivities.length > 0) {
      requestBody.requiredActivities = params.requiredActivities
    }

    return this.request(url, {
      method: 'POST',
      body: Object.keys(requestBody).length > 0 ? JSON.stringify(requestBody) : undefined,
    })
  }

  async clearProcessCache(processId: number): Promise<{ message: string }> {
    return this.request(`/analysis/cache/${processId}`, {
      method: 'DELETE',
    })
  }

  async clearAnalysisCache(processId: number, analysisType: AnalysisType): Promise<{ message: string }> {
    return this.request(`/analysis/cache/${processId}/${analysisType}`, {
      method: 'DELETE',
    })
  }

  async getDataSourceHash(processId: number): Promise<{
    processId: number
    hash: string | null
  }> {
    return this.request(`/analysis/cache/hash/${processId}`)
  }

  // 符合性检查相关API

  // BPMN模型管理
  async createBpmnModel(data: {
    name: string
    description?: string
    bpmnXml: string
    processId: number
    modelType: 'reference' | 'discovered' | 'normative'
    status?: 'draft' | 'active' | 'archived'
  }): Promise<BpmnModel> {
    return this.request('/conformance/models', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  async getBpmnModels(processId?: number): Promise<BpmnModel[]> {
    const params = processId ? `?processId=${processId}` : ''
    return this.request(`/conformance/models${params}`)
  }

  async getBpmnModel(id: number): Promise<BpmnModel> {
    return this.request(`/conformance/models/${id}`)
  }

  async updateBpmnModel(id: number, data: {
    name?: string
    description?: string
    bpmnXml?: string
  }): Promise<BpmnModel> {
    return this.request(`/conformance/models/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  async deleteBpmnModel(id: number): Promise<void> {
    return this.request(`/conformance/models/${id}`, {
      method: 'DELETE'
    })
  }

  async validateBpmnModel(id: number): Promise<BpmnModelValidation> {
    return this.request(`/conformance/models/${id}/validation`)
  }

  async activateBpmnModel(id: number): Promise<BpmnModel> {
    return this.request(`/conformance/models/${id}/activate`, {
      method: 'POST'
    })
  }

  async archiveBpmnModel(id: number): Promise<BpmnModel> {
    return this.request(`/conformance/models/${id}/archive`, {
      method: 'POST'
    })
  }

  async createBpmnModelFromDiscovery(processId: number, data?: {
    name?: string
    description?: string
  }): Promise<{
    success: boolean
    message: string
    model: BpmnModel
  }> {
    return this.request(`/conformance/models/from-discovery/${processId}`, {
      method: 'POST',
      body: JSON.stringify(data || {})
    })
  }

  // 符合性检查执行
  async performConformanceCheck(data: ConformanceCheckRequest): Promise<ConformanceResult> {
    return this.request('/conformance/check', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  // 重新分析特定的符合性检查结果
  async reanalyzeConformanceResult(resultId: number, parameters?: Record<string, any>): Promise<ConformanceResult> {
    return this.request(`/conformance/reanalyze/${resultId}`, {
      method: 'POST',
      body: JSON.stringify({ parameters })
    })
  }

  // 符合性检查结果获取
  async getConformanceResultsPaged(params: {
    page?: number
    pageSize?: number
    search?: string
    processId?: number | null
    status?: string
    level?: string
    sortBy?: 'createdAt' | 'conformanceScore'
    sortOrder?: 'asc' | 'desc'
  }): Promise<{ data: ConformanceResult[]; meta: PaginationMeta }> {
    const q = new URLSearchParams()
    if (params?.page) q.set('page', String(params.page))
    if (params?.pageSize) q.set('pageSize', String(params.pageSize))
    if (params?.search) q.set('search', params.search)
    if (params?.processId) q.set('processId', String(params.processId))
    if (params?.status) q.set('status', params.status)
    if (params?.level) q.set('level', params.level)
    if (params?.sortBy) q.set('sortBy', params.sortBy)
    if (params?.sortOrder) q.set('sortOrder', params.sortOrder)
    const query = q.toString() ? `?${q.toString()}` : ''
    return this.request(`/conformance/results${query}`)
  }

  async getConformanceResultById(id: number): Promise<ConformanceResult | null> {
    return this.request(`/conformance/results_detail/${id}`)
  }

  async getConformanceResult(processId: number, bpmnModelId: number): Promise<ConformanceResult | null> {
    return this.request(`/conformance/results/${processId}/${bpmnModelId}`)
  }

  async getConformanceResults(processId: number): Promise<ConformanceResult[]> {
    return this.request(`/conformance/results/${processId}`)
  }

  async deleteConformanceResult(id: number): Promise<void> {
    return this.request(`/conformance/results/${id}`, {
      method: 'DELETE'
    })
  }

  // 符合性检查统计和分析
  async getConformanceStatistics(processId: number, period?: string): Promise<ConformanceStatistics> {
    const params = period ? `?period=${period}` : ''
    return this.request(`/conformance/statistics/${processId}${params}`)
  }

  // 获取全局符合性检查统计
  async getGlobalConformanceStatistics(): Promise<{
    totalModels: number
    totalChecks: number
    averageScore: number
    recentChecks: number
  }> {
    return this.request('/conformance/statistics/global')
  }

  // 获取最近的符合性检查结果
  async getRecentConformanceResults(limit: number = 5): Promise<ConformanceResult[]> {
    return this.request(`/conformance/results_recent?limit=${limit}`)
  }

  // 批量符合性检查
  async batchConformanceCheck(data: {
    processId: number
    bpmnModelIds: number[]
    parameters?: Record<string, any>
    forceRefresh?: boolean
  }): Promise<{
    message: string
    results: Array<{
      bpmnModelId: number
      resultId?: number
      status: string
      error?: string
    }>
    totalRequests: number
    successfulRequests: number
  }> {
    return this.request('/conformance/batch', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  // ===== 数据库连接管理 =====

  // 获取所有数据库连接
  async getDatabaseConnections(): Promise<DatabaseConnection[]> {
    return this.request('/database-connections')
  }

  // 根据ID获取数据库连接
  async getDatabaseConnection(id: number): Promise<DatabaseConnection> {
    return this.request(`/database-connections/${id}`)
  }

  // 创建数据库连接
  async createDatabaseConnection(data: CreateDatabaseConnectionDto): Promise<DatabaseConnection> {
    return this.request('/database-connections', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  // 更新数据库连接
  async updateDatabaseConnection(id: number, data: UpdateDatabaseConnectionDto): Promise<DatabaseConnection> {
    return this.request(`/database-connections/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(data)
    })
  }

  // 删除数据库连接
  async deleteDatabaseConnection(id: number): Promise<{ message: string }> {
    return this.request(`/database-connections/${id}`, {
      method: 'DELETE'
    })
  }

  // 测试数据库连接
  async testDatabaseConnection(data: TestConnectionDto): Promise<TestConnectionResponse> {
    return this.request('/database-connections/test', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  // 测试已保存的数据库连接
  async testSavedDatabaseConnection(id: number): Promise<TestConnectionResponse> {
    return this.request(`/database-connections/${id}/test`, {
      method: 'POST'
    })
  }

  // 执行SQL查询
  async executeQuery(data: ExecuteQueryDto): Promise<QueryResult> {
    return this.request('/database-connections/execute-query', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  // 获取数据库表列表
  async getDatabaseTables(connectionId: number): Promise<string[]> {
    return this.request(`/database-connections/${connectionId}/tables`)
  }

  // 获取表结构信息
  async getTableStructure(connectionId: number, tableName: string): Promise<any[]> {
    return this.request(`/database-connections/${connectionId}/tables/${tableName}/structure`)
  }

}

// 创建单例实例
export const api = new ApiClient()

// 导出便捷函数
export const useApi = () => api
