interface VirtualNode {
  key: string
  name: string
  category: string
  position?: { x: number, y: number }
}

interface VirtualLink {
  key: string
  from: string
  to: string
  name?: string
}

/**
 * 向BPMN XML中添加虚拟节点和连线
 */
export function addVirtualElementsToBpmnXml(
  bpmnXml: string,
  nodeNames: string[],
  linkObjects: {from: string, to: string}[]
): string {
  try {
    // 解析XML
    const parser = new DOMParser()
    const xmlDoc = parser.parseFromString(bpmnXml, 'text/xml')

    // 检查解析错误
    const parseError = xmlDoc.querySelector('parsererror')
    if (parseError) {
      throw new Error('BPMN XML解析失败: ' + parseError.textContent)
    }

    // 获取process元素
    const processElement = xmlDoc.querySelector('bpmn\\:process, process')
    if (!processElement) {
      throw new Error('未找到BPMN process元素')
    }

    // 创建节点key到ID的映射
    const nodeKeyToIdMap = new Map<string, string>()

    // 收集现有的所有ID，确保生成的ID唯一
    const existingIds = new Set<string>()
    collectExistingIds(xmlDoc, existingIds)

    // 先收集现有节点的映射
    collectExistingNodeMappings(xmlDoc, nodeKeyToIdMap)

    // 添加虚拟节点并更新映射
    nodeNames.forEach(nodeName => {
      const nodeElement = createBpmnNodeElement(xmlDoc, { name: nodeName, category: 'Task' }, existingIds)
      processElement.appendChild(nodeElement)

      // 更新映射
      const nodeId = nodeElement.getAttribute('id')!
      nodeKeyToIdMap.set(nodeName, nodeId)

      existingIds.add(nodeId)
    })

    // 添加虚拟连线，使用正确的节点ID引用
    linkObjects.forEach(linkObj => {
      const linkElement = createBpmnLinkElement(xmlDoc, linkObj, nodeKeyToIdMap, existingIds)
      if (linkElement) {
        processElement.appendChild(linkElement)
        const linkId = linkElement.getAttribute('id')!
        const sourceRef = linkElement.getAttribute('sourceRef')!
        const targetRef = linkElement.getAttribute('targetRef')!

        // 更新源节点的outgoing引用
        const sourceNode = xmlDoc.querySelector(`*[id="${sourceRef}"]`)
        if (sourceNode) {
          const outgoingElement = xmlDoc.createElement('bpmn:outgoing')
          outgoingElement.textContent = linkId
          sourceNode.appendChild(outgoingElement)
        }

        // 更新目标节点的incoming引用
        const targetNode = xmlDoc.querySelector(`*[id="${targetRef}"]`)
        if (targetNode) {
          const incomingElement = xmlDoc.createElement('bpmn:incoming')
          incomingElement.textContent = linkId
          targetNode.appendChild(incomingElement)
        }

        existingIds.add(linkId)
      } else {
        console.warn(`连线 ${linkObj.from} -> ${linkObj.to} 添加失败`)
      }
    })

    // 序列化XML
    const serializer = new XMLSerializer()
    return serializer.serializeToString(xmlDoc)

  } catch (error) {
    console.error('修改BPMN XML失败:', error)
    throw error
  }
}

/**
 * 收集现有的所有ID
 */
function collectExistingIds(xmlDoc: Document, existingIds: Set<string>) {
  const allElements = xmlDoc.querySelectorAll('*[id]')
  allElements.forEach(element => {
    const id = element.getAttribute('id')
    if (id) {
      existingIds.add(id)
    }
  })
}

/**
 * 创建BPMN节点元素
 */
function createBpmnNodeElement(xmlDoc: Document, nodeData: { name: string, category?: string }, existingIds: Set<string>): Element {
  // 使用节点名称作为ID，与现有模型保持一致
  let nodeId = nodeData.name

  // 如果ID已存在，添加数字后缀
  let counter = 1
  while (existingIds.has(nodeId)) {
    nodeId = `${nodeData.name}_${counter}`
    counter++
  }

  // 根据节点类别创建不同类型的元素
  let elementName = 'bpmn:task'
  const category = nodeData.category || 'task'
  switch (category.toLowerCase()) {
    case 'startevent':
      elementName = 'bpmn:startEvent'
      break
    case 'endevent':
      elementName = 'bpmn:endEvent'
      break
    case 'task':
    case 'activity':
      elementName = 'bpmn:task'
      break
    case 'gateway':
      elementName = 'bpmn:exclusiveGateway'
      break
    default:
      elementName = 'bpmn:task'
  }

  const nodeElement = xmlDoc.createElement(elementName)
  nodeElement.setAttribute('id', nodeId)
  nodeElement.setAttribute('name', nodeData.name)

  return nodeElement
}

/**
 * 创建BPMN连线元素
 */
function createBpmnLinkElement(
  xmlDoc: Document,
  linkData: { from: string, to: string },
  nodeKeyToIdMap: Map<string, string>,
  existingIds: Set<string>
): Element | null {
  // 查找源节点和目标节点的实际ID
  let fromId: string | undefined = nodeKeyToIdMap.get(linkData.from)
  let toId: string | undefined = nodeKeyToIdMap.get(linkData.to)

  // 如果直接查找失败，尝试其他方式
  if (!fromId) {
    const foundFromId = findNodeIdByName(xmlDoc, linkData.from)
    if (foundFromId) {
      fromId = foundFromId
    } else {
      // 如果还是找不到，尝试查找包含该名称的key
      for (const [key, id] of nodeKeyToIdMap.entries()) {
        if (key.includes(linkData.from) || linkData.from.includes(key)) {
          fromId = id
          break
        }
      }
    }
  }

  if (!toId) {
    const foundToId = findNodeIdByName(xmlDoc, linkData.to)
    if (foundToId) {
      toId = foundToId
    } else {
      // 如果还是找不到，尝试查找包含该名称的key
      for (const [key, id] of nodeKeyToIdMap.entries()) {
        if (key.includes(linkData.to) || linkData.to.includes(key)) {
          toId = id
          break
        }
      }
    }
  }

  if (!fromId || !toId) {
    return null
  }

  // 生成与现有模型一致的连线ID格式：Flow_源节点_to_目标节点_数字
  let linkId = `Flow_${fromId}_to_${toId}`

  // 查找现有连线的最大数字后缀
  let maxSuffix = -1
  existingIds.forEach(id => {
    if (id.startsWith(`Flow_${fromId}_to_${toId}_`)) {
      const suffix = parseInt(id.split('_').pop() || '0')
      if (!isNaN(suffix) && suffix > maxSuffix) {
        maxSuffix = suffix
      }
    }
  })

  // 使用下一个可用的数字后缀
  linkId = `${linkId}_${maxSuffix + 1}`

  const linkElement = xmlDoc.createElement('bpmn:sequenceFlow')
  linkElement.setAttribute('id', linkId)
  linkElement.setAttribute('sourceRef', fromId)
  linkElement.setAttribute('targetRef', toId)

  // 可以添加连线名称
  const linkName = `${linkData.from} → ${linkData.to}`
  linkElement.setAttribute('name', linkName)

  return linkElement
}

/**
 * 收集现有节点的映射关系
 */
function collectExistingNodeMappings(xmlDoc: Document, nodeKeyToIdMap: Map<string, string>) {
  // 查找所有BPMN元素
  const elements = xmlDoc.querySelectorAll(
    'bpmn\\:task, bpmn\\:startEvent, bpmn\\:endEvent, bpmn\\:exclusiveGateway, bpmn\\:parallelGateway, ' +
    'task, startEvent, endEvent, exclusiveGateway, parallelGateway'
  )

  elements.forEach(element => {
    const id = element.getAttribute('id')
    const name = element.getAttribute('name')

    if (id && name) {
      // 使用名称作为key进行映射
      nodeKeyToIdMap.set(name, id)
      // 也使用ID作为key进行映射
      nodeKeyToIdMap.set(id, id)
    }
  })
}

/**
 * 根据节点名称查找节点ID
 */
function findNodeIdByName(xmlDoc: Document, nodeName: string): string | null {
  // 查找所有BPMN元素
  const elements = xmlDoc.querySelectorAll(
    'bpmn\\:task, bpmn\\:startEvent, bpmn\\:endEvent, bpmn\\:exclusiveGateway, bpmn\\:parallelGateway, ' +
    'task, startEvent, endEvent, exclusiveGateway, parallelGateway'
  )

  for (const element of elements) {
    const name = element.getAttribute('name')
    const id = element.getAttribute('id')

    if (name === nodeName && id) {
      return id
    }
  }

  return null
}



/**
 * 从虚拟元素数据中提取节点和连线信息
 */
export function extractVirtualElementsFromDiagram(
  selectedNodes: Set<string>,
  selectedLinks: Set<string>,
  diagram: { findNodeForKey: (key: string) => any, findLinkForKey: (key: string) => any } | null
): { nodes: VirtualNode[], links: VirtualLink[] } {
  const nodes: VirtualNode[] = []
  const links: VirtualLink[] = []
  const nodeKeyToNameMap = new Map<string, string>()

  // 首先收集所有节点的key到名称的映射（包括虚拟和非虚拟节点）
  if (diagram) {
    try {
      const allNodes = (diagram as any).nodes
      if (allNodes && typeof allNodes.each === 'function') {
        allNodes.each((node: any) => {
          if (node.data && node.data.key && node.data.name) {
            nodeKeyToNameMap.set(node.data.key, node.data.name)
          }
        })
      }
    } catch (error) {
      console.warn('无法遍历图表节点:', error)
    }
  }

  // 提取选中的虚拟节点
  selectedNodes.forEach(nodeKey => {
    const node = diagram?.findNodeForKey(nodeKey)
    if (node && node.data.isVirtual) {
      nodes.push({
        key: nodeKey,
        name: node.data.name || nodeKey,
        category: node.data.category || 'Task',
        position: node.location ? { x: node.location.x, y: node.location.y } : undefined
      })
    }
  })
  
  // 提取选中的虚拟连线
  selectedLinks.forEach(linkKey => {
    const link = diagram?.findLinkForKey(linkKey)
    if (link && link.data.isVirtual) {
      // 获取源节点和目标节点的名称
      const fromNode = diagram?.findNodeForKey(link.data.from)
      const toNode = diagram?.findNodeForKey(link.data.to)

      // 优先使用节点名称，如果没有名称则使用key
      let fromName = link.data.from
      let toName = link.data.to

      if (fromNode?.data.name) {
        fromName = fromNode.data.name
      }

      if (toNode?.data.name) {
        toName = toNode.data.name
      }

      // 使用映射表将key转换为名称
      if (nodeKeyToNameMap.has(link.data.from)) {
        fromName = nodeKeyToNameMap.get(link.data.from)!
      }
      if (nodeKeyToNameMap.has(link.data.to)) {
        toName = nodeKeyToNameMap.get(link.data.to)!
      }

      links.push({
        key: linkKey,
        from: fromName,
        to: toName,
        name: link.data.name || `${fromName} → ${toName}`
      })
    }
  })

  return { nodes, links }
}

/**
 * 验证BPMN XML格式
 */
export function validateBpmnXml(bpmnXml: string): { isValid: boolean, errors: string[] } {
  const errors: string[] = []

  try {
    // 基本XML格式检查
    const parser = new DOMParser()
    const xmlDoc = parser.parseFromString(bpmnXml, 'text/xml')

    // 检查XML解析错误
    const parseError = xmlDoc.querySelector('parsererror')
    if (parseError) {
      errors.push('XML格式错误: ' + parseError.textContent)
      return { isValid: false, errors }
    }

    // BPMN结构验证
    const rootElement = xmlDoc.documentElement
    if (!rootElement || rootElement.tagName !== 'bpmn:definitions') {
      errors.push('缺少有效的BPMN根元素 (bpmn:definitions)')
    }

    // 检查必需的命名空间
    const requiredNamespaces = [
      'http://www.omg.org/spec/BPMN/20100524/MODEL'
    ]

    requiredNamespaces.forEach(ns => {
      if (!bpmnXml.includes(ns)) {
        errors.push(`缺少必需的BPMN命名空间: ${ns}`)
      }
    })

    // 检查process元素
    const processElements = xmlDoc.querySelectorAll('bpmn\\:process, process')
    if (processElements.length === 0) {
      errors.push('BPMN模型中缺少process元素')
    } else {
      // 验证process元素的属性
      processElements.forEach((process, index) => {
        const processId = process.getAttribute('id')
        if (!processId) {
          errors.push(`第${index + 1}个process元素缺少id属性`)
        }
      })
    }

    // 验证节点和连线的完整性
    const allNodes = xmlDoc.querySelectorAll(
      'bpmn\\:startEvent, bpmn\\:endEvent, bpmn\\:task, bpmn\\:userTask, bpmn\\:serviceTask, ' +
      'bpmn\\:exclusiveGateway, bpmn\\:parallelGateway, bpmn\\:inclusiveGateway, ' +
      'startEvent, endEvent, task, userTask, serviceTask, exclusiveGateway, parallelGateway, inclusiveGateway'
    )

    const allLinks = xmlDoc.querySelectorAll('bpmn\\:sequenceFlow, sequenceFlow')

    // 检查是否有基本的流程元素
    if (allNodes.length === 0) {
      errors.push('BPMN模型中没有找到任何流程节点')
    }

    // 验证ID唯一性
    const allElements = xmlDoc.querySelectorAll('*[id]')
    const ids = new Set<string>()
    const duplicateIds: string[] = []

    allElements.forEach(element => {
      const id = element.getAttribute('id')
      if (id) {
        if (ids.has(id)) {
          duplicateIds.push(id)
        } else {
          ids.add(id)
        }
      }
    })

    if (duplicateIds.length > 0) {
      errors.push(`发现重复的元素ID: ${duplicateIds.join(', ')}`)
    }

    // 验证连线引用的完整性
    allLinks.forEach((link, index) => {
      const sourceRef = link.getAttribute('sourceRef')
      const targetRef = link.getAttribute('targetRef')
      const linkId = link.getAttribute('id')

      if (!sourceRef) {
        errors.push(`连线${linkId || `(第${index + 1}个)`}缺少sourceRef属性`)
      } else if (!xmlDoc.querySelector(`*[id="${sourceRef}"]`)) {
        errors.push(`连线${linkId || `(第${index + 1}个)`}引用的源节点"${sourceRef}"不存在`)
      }

      if (!targetRef) {
        errors.push(`连线${linkId || `(第${index + 1}个)`}缺少targetRef属性`)
      } else if (!xmlDoc.querySelector(`*[id="${targetRef}"]`)) {
        errors.push(`连线${linkId || `(第${index + 1}个)`}引用的目标节点"${targetRef}"不存在`)
      }
    })

    // 验证节点的incoming/outgoing引用
    allNodes.forEach(node => {
      const nodeId = node.getAttribute('id')
      const incomingElements = node.querySelectorAll('bpmn\\:incoming, incoming')
      const outgoingElements = node.querySelectorAll('bpmn\\:outgoing, outgoing')

      // 检查incoming引用的连线是否存在
      incomingElements.forEach(incoming => {
        const flowId = incoming.textContent?.trim()
        if (flowId && !xmlDoc.querySelector(`bpmn\\:sequenceFlow[id="${flowId}"], sequenceFlow[id="${flowId}"]`)) {
          errors.push(`节点"${nodeId}"引用的incoming连线"${flowId}"不存在`)
        }
      })

      // 检查outgoing引用的连线是否存在
      outgoingElements.forEach(outgoing => {
        const flowId = outgoing.textContent?.trim()
        if (flowId && !xmlDoc.querySelector(`bpmn\\:sequenceFlow[id="${flowId}"], sequenceFlow[id="${flowId}"]`)) {
          errors.push(`节点"${nodeId}"引用的outgoing连线"${flowId}"不存在`)
        }
      })
    })

    // 检查基本的流程逻辑
    const startEvents = xmlDoc.querySelectorAll('bpmn\\:startEvent, startEvent')
    const endEvents = xmlDoc.querySelectorAll('bpmn\\:endEvent, endEvent')

    if (startEvents.length === 0) {
      errors.push('BPMN模型中缺少开始事件')
    }

    if (endEvents.length === 0) {
      errors.push('BPMN模型中缺少结束事件')
    }

    // 检查是否有孤立的节点（没有连线的节点）
    const connectedNodeIds = new Set<string>()
    allLinks.forEach(link => {
      const sourceRef = link.getAttribute('sourceRef')
      const targetRef = link.getAttribute('targetRef')
      if (sourceRef) connectedNodeIds.add(sourceRef)
      if (targetRef) connectedNodeIds.add(targetRef)
    })

    const isolatedNodes: string[] = []
    allNodes.forEach(node => {
      const nodeId = node.getAttribute('id')
      if (nodeId && !connectedNodeIds.has(nodeId)) {
        const isStartEvent = node.tagName.includes('startEvent')
        const isEndEvent = node.tagName.includes('endEvent')
        if (!isStartEvent && !isEndEvent) {
          isolatedNodes.push(nodeId)
        }
      }
    })

    if (isolatedNodes.length > 0) {
      errors.push(`发现孤立的节点（没有连线）: ${isolatedNodes.join(', ')}`)
    }

  } catch (error) {
    errors.push('BPMN XML验证时发生错误: ' + (error as Error).message)
  }

  return { isValid: errors.length === 0, errors }
}
