/**
 * GoJS 安全工具函数
 * 用于防止生产环境中的 measuredBounds NaN 错误
 */

export interface SafeNodeData {
  key: string
  label: string
  frequency: number
  avgDuration: number
  minDuration: number
  maxDuration: number
  currentValue: number
  dimension: string
  nodeStyle: SafeNodeStyle
  isStartNode: boolean
  isEndNode: boolean
  group?: string
  isSubprocessNode: boolean
  subprocessLevel: number
  parentCaseId?: string
  color: string
  borderColor: string
}

export interface SafeLinkData {
  from: string
  to: string
  frequency: number
  avgDuration: number
  minDuration: number
  maxDuration: number
  currentValue: number
  dimension: string
  linkStyle: SafeLinkStyle
}

export interface SafeNodeStyle {
  fill: string
  stroke: string
  strokeWidth: number
  textColor: string
  width: number
  height: number
}

export interface SafeLinkStyle {
  stroke: string
  strokeWidth: number
  arrowScale: number
}

/**
 * 验证数值是否安全（非NaN、非Infinity、非null、非undefined）
 */
export function isSafeNumber(value: any): value is number {
  return typeof value === 'number' && Number.isFinite(value)
}

/**
 * 获取安全的数值，如果不安全则返回默认值
 */
export function getSafeNumber(value: any, defaultValue: number = 0): number {
  return isSafeNumber(value) ? value : defaultValue
}

/**
 * 获取安全的正数值，确保大于0
 */
export function getSafePositiveNumber(value: any, defaultValue: number = 1): number {
  const safeValue = getSafeNumber(value, defaultValue)
  return Math.max(safeValue, 0.001) // 确保至少是一个很小的正数
}

/**
 * 验证和清理节点数据
 */
export function sanitizeNodeData(node: any): SafeNodeData | null {
  if (!node || typeof node !== 'object') {
    console.warn('Invalid node data:', node)
    return null
  }

  const nodeId = String(node.id || node.key || '')
  if (!nodeId) {
    console.warn('Node missing id/key:', node)
    return null
  }

  // 清理数值字段
  const safeFrequency = getSafeNumber(node.frequency, 0)
  const safeAvgDuration = getSafeNumber(node.avgDuration, 0)
  const safeMinDuration = getSafeNumber(node.minDuration, 0)
  const safeMaxDuration = getSafeNumber(node.maxDuration, 0)

  // 验证节点样式
  const nodeStyle = node.nodeStyle || {}
  const safeNodeStyle: SafeNodeStyle = {
    fill: String(nodeStyle.fill || '#4CAF50'),
    stroke: String(nodeStyle.stroke || '#388E3C'),
    strokeWidth: getSafePositiveNumber(nodeStyle.strokeWidth, 2),
    textColor: String(nodeStyle.textColor || '#FFFFFF'),
    width: getSafePositiveNumber(nodeStyle.width, 80),
    height: getSafePositiveNumber(nodeStyle.height, 40)
  }

  // 确保尺寸在合理范围内
  safeNodeStyle.width = Math.max(60, Math.min(300, safeNodeStyle.width))
  safeNodeStyle.height = Math.max(30, Math.min(150, safeNodeStyle.height))
  safeNodeStyle.strokeWidth = Math.max(1, Math.min(10, safeNodeStyle.strokeWidth))

  return {
    key: nodeId,
    label: String(node.label || nodeId),
    frequency: safeFrequency,
    avgDuration: safeAvgDuration,
    minDuration: safeMinDuration,
    maxDuration: safeMaxDuration,
    currentValue: getSafeNumber(node.currentValue, 0),
    dimension: String(node.dimension || 'frequency'),
    nodeStyle: safeNodeStyle,
    isStartNode: Boolean(node.isStartNode),
    isEndNode: Boolean(node.isEndNode),
    group: node.group ? String(node.group) : undefined,
    isSubprocessNode: Boolean(node.isSubprocessNode),
    subprocessLevel: getSafeNumber(node.subprocessLevel, 0),
    parentCaseId: node.parentCaseId ? String(node.parentCaseId) : undefined,
    color: safeNodeStyle.fill,
    borderColor: safeNodeStyle.stroke
  }
}

/**
 * 验证和清理连接线数据
 */
export function sanitizeLinkData(edge: any): SafeLinkData | null {
  if (!edge || typeof edge !== 'object') {
    console.warn('Invalid edge data:', edge)
    return null
  }

  const from = String(edge.from || edge.source || '')
  const to = String(edge.to || edge.target || '')
  
  if (!from || !to) {
    console.warn('Edge missing from/to:', edge)
    return null
  }

  // 清理数值字段
  const safeFrequency = getSafeNumber(edge.frequency, 0)
  const safeAvgDuration = getSafeNumber(edge.avgDuration, 0)
  const safeMinDuration = getSafeNumber(edge.minDuration, 0)
  const safeMaxDuration = getSafeNumber(edge.maxDuration, 0)

  // 验证连接线样式
  const linkStyle = edge.linkStyle || {}
  const safeLinkStyle: SafeLinkStyle = {
    stroke: String(linkStyle.stroke || '#2196F3'),
    strokeWidth: getSafePositiveNumber(linkStyle.strokeWidth, 2),
    arrowScale: getSafePositiveNumber(linkStyle.arrowScale, 1.0)
  }

  // 确保样式在合理范围内
  safeLinkStyle.strokeWidth = Math.max(1, Math.min(20, safeLinkStyle.strokeWidth))
  safeLinkStyle.arrowScale = Math.max(0.5, Math.min(5.0, safeLinkStyle.arrowScale))

  return {
    from,
    to,
    frequency: safeFrequency,
    avgDuration: safeAvgDuration,
    minDuration: safeMinDuration,
    maxDuration: safeMaxDuration,
    currentValue: getSafeNumber(edge.currentValue, 0),
    dimension: String(edge.dimension || 'frequency'),
    linkStyle: safeLinkStyle
  }
}

/**
 * 验证整个发现结果数据
 */
export function validateDiscoveryResult(data: any): boolean {
  if (!data || typeof data !== 'object') {
    console.error('Invalid discovery result: not an object')
    return false
  }

  // 验证节点数组
  if (!Array.isArray(data.nodes)) {
    console.error('Invalid discovery result: nodes is not an array')
    return false
  }

  // 验证边数组
  if (!Array.isArray(data.edges)) {
    console.error('Invalid discovery result: edges is not an array')
    return false
  }

  // 验证每个节点
  for (let i = 0; i < data.nodes.length; i++) {
    const node = data.nodes[i]
    if (!sanitizeNodeData(node)) {
      console.error(`Invalid node at index ${i}:`, node)
      return false
    }
  }

  // 验证每个边
  for (let i = 0; i < data.edges.length; i++) {
    const edge = data.edges[i]
    if (!sanitizeLinkData(edge)) {
      console.error(`Invalid edge at index ${i}:`, edge)
      return false
    }
  }

  return true
}

/**
 * 创建安全的空数据结构
 */
export function createSafeEmptyData() {
  return {
    nodes: [],
    edges: [],
    statistics: {
      totalCases: 0,
      totalActivities: 0,
      avgCaseDuration: 0
    },
    timestamp: new Date().toISOString()
  }
}

/**
 * GoJS 错误处理包装器
 */
export function safeGoJSOperation<T>(operation: () => T, fallback: T, operationName: string = 'GoJS operation'): T {
  try {
    return operation()
  } catch (error) {
    console.error(`❌ ${operationName} failed:`, error)
    if (error instanceof Error && error.message.includes('measuredBounds')) {
      console.error('🔥 Detected GoJS measuredBounds error - this is likely due to NaN values in data')
    }
    return fallback
  }
}
