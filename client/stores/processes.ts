import { defineStore } from 'pinia'
import type { Process, CreateProcessData, ProcessStatistics } from '~/types'
import { useApi } from '~/utils/api'

export const useProcessesStore = defineStore('processes', () => {
  const api = useApi()
  
  // 状态
  const processes = ref<Process[]>([])
  const currentProcess = ref<Process | null>(null)
  const statistics = ref<ProcessStatistics | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const processesByStatus = computed(() => {
    return {
      draft: processes.value.filter(p => p.status === 'draft'),
      active: processes.value.filter(p => p.status === 'active'),
      completed: processes.value.filter(p => p.status === 'completed'),
      archived: processes.value.filter(p => p.status === 'archived'),
    }
  })

  // 获取流程列表
  const fetchProcesses = async () => {
    isLoading.value = true
    error.value = null
    
    try {
      const data = await api.getProcesses()
      processes.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取流程列表失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 获取单个流程
  const fetchProcess = async (id: number) => {
    isLoading.value = true
    error.value = null
    
    try {
      const data = await api.getProcess(id)
      currentProcess.value = data
      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取流程详情失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 创建流程
  const createProcess = async (data: CreateProcessData) => {
    isLoading.value = true
    error.value = null
    
    try {
      const newProcess = await api.createProcess(data)
      processes.value.unshift(newProcess)
      return newProcess
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建流程失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 更新流程
  const updateProcess = async (id: number, data: Partial<CreateProcessData>) => {
    isLoading.value = true
    error.value = null
    
    try {
      const updatedProcess = await api.updateProcess(id, data)
      
      // 更新列表中的流程
      const index = processes.value.findIndex(p => p.id === id)
      if (index !== -1) {
        processes.value[index] = updatedProcess
      }
      
      // 更新当前流程
      if (currentProcess.value?.id === id) {
        currentProcess.value = updatedProcess
      }
      
      return updatedProcess
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新流程失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 删除流程
  const deleteProcess = async (id: number) => {
    isLoading.value = true
    error.value = null
    
    try {
      await api.deleteProcess(id)
      
      // 从列表中移除
      processes.value = processes.value.filter(p => p.id !== id)
      
      // 清除当前流程
      if (currentProcess.value?.id === id) {
        currentProcess.value = null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除流程失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 获取统计信息
  const fetchStatistics = async () => {
    try {
      const data = await api.getProcessStatistics()
      statistics.value = data
      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取统计信息失败'
      throw err
    }
  }

  // 根据ID查找流程
  const getProcessById = (id: number) => {
    return processes.value.find(p => p.id === id)
  }

  // 清除错误
  const clearError = () => {
    error.value = null
  }

  // 清除当前流程
  const clearCurrentProcess = () => {
    currentProcess.value = null
  }

  return {
    // 状态
    processes: readonly(processes),
    currentProcess: readonly(currentProcess),
    statistics: readonly(statistics),
    isLoading: readonly(isLoading),
    error: readonly(error),
    
    // 计算属性
    processesByStatus,
    
    // 方法
    fetchProcesses,
    fetchProcess,
    createProcess,
    updateProcess,
    deleteProcess,
    fetchStatistics,
    getProcessById,
    clearError,
    clearCurrentProcess
  }
})
