import { defineStore } from 'pinia'
import type { User, LoginCredentials, RegisterData } from '~/types'
import { useApi } from '~/utils/api'

export const useAuthStore = defineStore('auth', () => {
  const api = useApi()
  
  // 状态
  const user = ref<User | null>(null)
  const isAuthenticated = computed(() => !!user.value)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 初始化认证状态
  const initAuth = async () => {
    if (import.meta.client) {
      console.log('初始化认证状态...')
      try {
        const token = localStorage.getItem('auth_token_content')
        const tokenExpiry = localStorage.getItem('auth_token_expiry')

        console.log('存储状态检查:', {
          hasToken: !!token,
          hasExpiry: !!tokenExpiry,
          tokenLength: token?.length || 0,
          expiry: tokenExpiry
        })

        // 检查数据一致性
        if ((token && !tokenExpiry) || (!token && tokenExpiry)) {
          console.warn('认证数据不一致，清除所有存储:', { hasToken: !!token, hasExpiry: !!tokenExpiry })
          api.clearToken()
          return
        }

        if (token && tokenExpiry) {
          console.log('找到存储的token和过期时间')
          const expiryTime = parseInt(tokenExpiry)
          const currentTime = Date.now()

          // 检查token是否过期
          if (currentTime < expiryTime) {
            console.log('Token未过期，尝试获取用户信息')
            try {
              const userData = await api.getProfile()
              user.value = userData
              console.log('成功获取用户信息')
            } catch (err) {
              console.warn('Token无效，清除本地存储:', err)
              api.clearToken()
            }
          } else {
            console.log('Token已过期，清除本地存储')
            api.clearToken()
          }
        } else {
          console.log('未找到存储的认证信息')
        }
      } catch (error) {
        console.error('初始化认证状态时出错:', error)
        api.clearToken()
      }
    }
  }

  // 登录
  const login = async (credentials: LoginCredentials) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await api.login(credentials)
      user.value = response.user
      
      // 导航到仪表板
      await navigateTo('/dashboard')
      
      return response
    } catch (err) {
      error.value = err instanceof Error ? err.message : '登录失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (data: RegisterData) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await api.register(data)
      user.value = response.user
      
      // 导航到仪表板
      await navigateTo('/dashboard')
      
      return response
    } catch (err) {
      error.value = err instanceof Error ? err.message : '注册失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    user.value = null
    api.clearToken()
    
    // 导航到登录页
    await navigateTo('/auth/login')
  }

  // 更新用户信息
  const updateProfile = async (data: Partial<User>) => {
    if (!user.value) return
    
    isLoading.value = true
    error.value = null
    
    try {
      const updatedUser = await api.updateUser(user.value.id, data)
      user.value = updatedUser
      return updatedUser
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 清除错误
  const clearError = () => {
    error.value = null
  }

  // 检查token剩余时间
  const getTokenRemainingTime = () => {
    if (!import.meta.client) return 0

    const tokenExpiry = localStorage.getItem('auth_token_expiry')
    if (!tokenExpiry) return 0

    const expiryTime = parseInt(tokenExpiry)
    const currentTime = Date.now()
    const remainingTime = expiryTime - currentTime

    return Math.max(0, remainingTime)
  }

  // 检查token是否即将过期（剩余时间少于7天）
  const isTokenExpiringSoon = () => {
    const remainingTime = getTokenRemainingTime()
    const sevenDaysInMs = 7 * 24 * 60 * 60 * 1000
    return remainingTime > 0 && remainingTime < sevenDaysInMs
  }

  return {
    // 状态
    user: readonly(user),
    isAuthenticated,
    isLoading: readonly(isLoading),
    error: readonly(error),

    // 方法
    initAuth,
    login,
    register,
    logout,
    updateProfile,
    clearError,
    getTokenRemainingTime,
    isTokenExpiringSoon
  }
})
