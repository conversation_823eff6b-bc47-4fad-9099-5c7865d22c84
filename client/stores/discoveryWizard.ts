import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ProcessDiscoveryStep } from '~/types'
import type {
  UploadDataConfig,
  ProcessingStatus,
  DFGResult,
  QueryResult
} from '~/types'

// 文件预览数据类型
interface PreviewData {
  columns: string[]
  data: any[]
  totalRows: number
}

export const useDiscoveryWizardStore = defineStore('discoveryWizard', () => {
  // 基础状态
  const step = ref<ProcessDiscoveryStep>(ProcessDiscoveryStep.UPLOAD)
  const processId = ref<number>(0)
  const source = ref<string>('upload')

  // 文件相关状态
  const file = ref<File | null>(null)
  const preview = ref<PreviewData | null>(null)
  const timeOverrides = ref<Record<string, boolean>>({})

  // 映射相关状态
  const mapping = ref<Record<string, string> | null>(null)
  const clearData = ref<boolean>(false)

  // 处理状态
  const config = ref<UploadDataConfig | null>(null)
  const status = ref<ProcessingStatus | null>(null)
  const result = ref<DFGResult | null>(null)

  // SQL查询相关状态
  const queryResult = ref<QueryResult | null>(null)
  const query = ref<string>('')
  
  // 计算属性
  const isFileUploaded = computed(() => file.value !== null)
  const hasPreview = computed(() => preview.value !== null)
  const hasMapping = computed(() => mapping.value !== null)
  const isProcessing = computed(() => status.value !== null)
  
  // 获取当前被识别为时间列的列表
  const timeColumns = computed(() => {
    if (!preview.value || !preview.value.columns) return []

    return preview.value.columns.filter(column => {
      // 检查是否有用户覆盖设置
      if (Object.prototype.hasOwnProperty.call(timeOverrides.value, column)) {
        return timeOverrides.value[column]
      }

      // 如果没有用户覆盖，使用自动检测逻辑
      return isAutoDetectedTimeColumn(column)
    })
  })
  
  // 自动检测是否为时间列
  const isAutoDetectedTimeColumn = (columnName: string): boolean => {
    // 检查列名是否包含时间相关关键词
    const timeKeywords = ['time', 'date', 'timestamp', '时间', '日期', '时刻', 'created', 'updated', 'start', 'end', 'finish']
    const lowerColumnName = columnName.toLowerCase()
    const hasTimeKeyword = timeKeywords.some(keyword => lowerColumnName.includes(keyword))
    
    // 检查该列的数据是否看起来像时间
    if (preview.value && preview.value.data.length > 0) {
      const sampleValues = preview.value.data.slice(0, 3).map(row => row[columnName])
      const timeValueCount = sampleValues.filter(value => {
        if (!value) return false

        // 检查是否为时间格式字符串
        const timePatterns = [
          /^\d{4}[-/]\d{1,2}[-/]\d{1,2}\s+\d{1,2}:\d{1,2}:\d{1,2}$/, // 标准格式
          /^\d{1,2}[-/]\d{1,2}[-/]\d{4}\s+\d{1,2}:\d{1,2}:\d{1,2}$/, // 美式格式
          /^\d{4}[-/]\d{1,2}[-/]\d{1,2}$/, // 仅日期
          /^\d{1,2}[-/]\d{1,2}[-/]\d{4}$/, // 美式仅日期
        ]

        const isTimeString = timePatterns.some(pattern => pattern.test(String(value)))

        // 检查是否为数字且可能是Excel日期序列号或时间戳
        const numValue = Number(value)
        const isExcelDate = !isNaN(numValue) && numValue > 1 && numValue < 2958466
        const isTimestamp = !isNaN(numValue) && numValue > 946684800 && numValue < 4102444800 // 2000年到2100年

        return isTimeString || isExcelDate || isTimestamp
      }).length

      // 如果超过一半的样本值看起来像时间，则认为是时间列
      const isTimeData = timeValueCount >= Math.ceil(sampleValues.length / 2)
      return hasTimeKeyword || isTimeData
    }

    return hasTimeKeyword
  }
  
  // 文件相关
  const setFile = (newFile: File, previewData?: PreviewData) => {
    file.value = newFile
    if (previewData) {
      preview.value = previewData
    }
  }

  const setPreview = (previewData: PreviewData) => {
    preview.value = previewData
  }

  const updateTimeOverrides = (overrides: Record<string, boolean>) => {
    timeOverrides.value = { ...overrides }
  }

  const clearFile = () => {
    file.value = null
    preview.value = null
    timeOverrides.value = {}
  }
  
  // 映射相关
  const updateMapping = (config: Record<string, string>) => {
    mapping.value = { ...config }
  }

  const setClearData = (clear: boolean) => {
    clearData.value = clear
  }

  const clearMapping = () => {
    mapping.value = null
    clearData.value = false
  }
  
  // 处理相关
  const setConfig = (newConfig: UploadDataConfig) => {
    config.value = newConfig
  }

  const setStatus = (newStatus: ProcessingStatus | null) => {
    status.value = newStatus
  }

  const setResult = (newResult: DFGResult | null) => {
    result.value = newResult
  }
  
  // SQL查询相关
  const setQuery = (result: QueryResult, queryText: string) => {
    queryResult.value = result
    query.value = queryText

    // 将查询结果转换为文件预览数据格式
    preview.value = {
      columns: result.columns,
      data: result.data,
      totalRows: result.totalRows
    }
  }
  
  // 步骤导航
  const setStep = (newStep: ProcessDiscoveryStep) => {
    step.value = newStep
  }

  const nextStep = (steps: any[]) => {
    const currentIndex = steps.findIndex(s => s.key === step.value)
    if (currentIndex < steps.length - 1) {
      step.value = steps[currentIndex + 1].key as ProcessDiscoveryStep
    }
  }

  const prevStep = (steps: any[]) => {
    const currentIndex = steps.findIndex(s => s.key === step.value)
    if (currentIndex > 0) {
      step.value = steps[currentIndex - 1].key as ProcessDiscoveryStep
    }
  }
  
  // 初始化和重置
  const init = (id: number, sourceType: string = 'upload') => {
    reset()

    processId.value = id
    source.value = sourceType
    step.value = sourceType === 'database' ? ProcessDiscoveryStep.UPLOAD : ProcessDiscoveryStep.UPLOAD
  }

  const reset = () => {
    step.value = ProcessDiscoveryStep.UPLOAD
    processId.value = 0
    source.value = 'upload'

    file.value = null
    preview.value = null
    timeOverrides.value = {}

    mapping.value = null
    clearData.value = false

    config.value = null
    status.value = null
    result.value = null

    queryResult.value = null
    query.value = ''
  }
  
  return {
    // State
    step,
    processId,
    source,
    file,
    preview,
    timeOverrides,
    mapping,
    clearData,
    config,
    status,
    result,
    queryResult,
    query,

    // Computed
    isFileUploaded,
    hasPreview,
    hasMapping,
    isProcessing,
    timeColumns,

    // Actions
    setFile,
    setPreview,
    updateTimeOverrides,
    clearFile,
    updateMapping,
    setClearData,
    clearMapping,
    setConfig,
    setStatus,
    setResult,
    setQuery,
    setStep,
    nextStep,
    prevStep,
    init,
    reset,
    isAutoDetectedTimeColumn,
  }
})
