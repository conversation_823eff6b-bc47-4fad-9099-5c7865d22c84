// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },

  // 关闭服务端渲染，启用SPA模式
  ssr: false,

  modules: [
    '@nuxt/eslint',
    '@element-plus/nuxt',
    '@pinia/nuxt',
    '@vueuse/nuxt',
    '@nuxtjs/color-mode'
  ],

  // 应用配置
  app: {
    head: {
      title: 'ProMax - 流程挖掘平台',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: 'ProMax 专业流程挖掘平台，帮助企业发现、分析和优化业务流程' }
      ]
    }
  },

  // 运行时配置
  runtimeConfig: {
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE || 'http://localhost:3003',
    }
  },

  // CSS配置
  css: ['~/assets/scss/main.scss'],

  // Vite 配置
  vite: {
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler'
        }
      }
    },
    optimizeDeps: {
      include: ['echarts', 'vue-echarts']
    },
    define: {
      // 编译时替换的全局常量
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
      __ENVIRONMENT__: JSON.stringify(process.env.NODE_ENV || 'development'),
      __API_BASE__: JSON.stringify(process.env.NUXT_PUBLIC_API_BASE || 'http://localhost:3003'),
      // 自定义替换
      __COMPANY_NAME__: JSON.stringify('ProMax'),
      __PRODUCT_NAME__: JSON.stringify('流程挖掘平台')
    }
  },

  // 构建配置
  build: {
    transpile: ['vue-echarts', 'echarts']
  },

  // Nitro 配置
  nitro: {
    replace: {
      // 在最终构建的 JS 中替换字符串
      '7eba17a4ca3b1a8346': ''
    }
  },

  // 颜色模式配置
  colorMode: {
    preference: 'system',
    fallback: 'light',
    hid: 'nuxt-color-mode-script',
    globalName: '__NUXT_COLOR_MODE__',
    componentName: 'ColorScheme',
    classPrefix: '',
    classSuffix: '',
    storageKey: 'nuxt-color-mode'
  },

  // Element Plus 配置
  elementPlus: {
    importStyle: 'scss',
    themes: ['dark']
  },

  // 开发服务器配置
  devServer: {
    port: process.env.PORT ? parseInt(process.env.PORT) : 3000,
    host: process.env.HOST || '0.0.0.0'
  }
})