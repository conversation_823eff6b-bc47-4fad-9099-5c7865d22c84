import { ref, nextTick } from 'vue'

export interface ContextMenuState {
  visible: boolean
  x: number
  y: number
  type: 'node' | 'link' | ''
  target: Record<string, unknown> | null
}

export function useContextMenu() {
  // 上下文菜单状态
  const contextMenu = ref<ContextMenuState>({
    visible: false,
    x: 0,
    y: 0,
    type: '',
    target: null
  })
  
  // 鼠标位置跟踪
  const mousePosition = ref({ x: 0, y: 0 })
  
  // 跟踪鼠标位置
  const trackMousePosition = (event: Event) => {
    const mouseEvent = event as MouseEvent
    mousePosition.value = {
      x: mouseEvent.clientX,
      y: mouseEvent.clientY
    }
  }
  
  // 显示上下文菜单
  const showContextMenu = (type: 'node' | 'link', target: Record<string, unknown>) => {
    hideContextMenu()
    
    // 使用最后记录的鼠标位置
    let x = mousePosition.value.x || 200
    let y = mousePosition.value.y || 200
    
    // 确保菜单不会超出视窗边界
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    const menuWidth = 160
    const menuHeight = 200
    
    if (x + menuWidth > viewportWidth) {
      x = viewportWidth - menuWidth - 10
    }
    if (y + menuHeight > viewportHeight) {
      y = viewportHeight - menuHeight - 10
    }
    if (x < 0) x = 10
    if (y < 0) y = 10
    
    contextMenu.value = {
      visible: true,
      x,
      y,
      type,
      target
    }
    
    // 延迟添加全局点击监听器，避免立即触发
    nextTick(() => {
      setTimeout(() => {
        document.addEventListener('click', hideContextMenu, { once: true })
      }, 100)
    })
  }
  
  // 隐藏上下文菜单
  const hideContextMenu = () => {
    contextMenu.value.visible = false
    contextMenu.value.x = 0
    contextMenu.value.y = 0
    contextMenu.value.type = ''
    contextMenu.value.target = null
    document.removeEventListener('click', hideContextMenu)
  }
  
  // 初始化鼠标位置跟踪
  const initMouseTracking = () => {
    nextTick(() => {
      const selectors = [
        '.diagram-container',
        '.dfg-container',
        '.gojs-diagram',
        '#dfgDiagram'
      ]

      let diagramContainer: Element | null = null
      for (const selector of selectors) {
        diagramContainer = document.querySelector(selector)
        if (diagramContainer) {
          break
        }
      }

      if (diagramContainer) {
        diagramContainer.addEventListener('mousemove', trackMousePosition)
      } else {
        console.warn('未找到图表容器，鼠标位置跟踪可能不准确')
      }
    })
  }

  // 清理鼠标位置跟踪
  const cleanupMouseTracking = () => {
    const selectors = [
      '.diagram-container',
      '.dfg-container',
      '.gojs-diagram',
      '#dfgDiagram'
    ]

    for (const selector of selectors) {
      const diagramContainer = document.querySelector(selector)
      if (diagramContainer) {
        diagramContainer.removeEventListener('mousemove', trackMousePosition)
      }
    }
  }
  
  return {
    contextMenu,
    mousePosition,
    showContextMenu,
    hideContextMenu,
    trackMousePosition,
    initMouseTracking,
    cleanupMouseTracking
  }
}
