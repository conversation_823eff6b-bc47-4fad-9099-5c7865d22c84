import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useApi } from '~/utils/api'
import type { DFGResult } from '~/types'

export function useDiscoveryData() {
  const route = useRoute()
  const api = useApi()

  // 响应式状态
  const isLoading = ref(true)
  const isSaving = ref(false)
  const discoveryResult = ref<DFGResult | null>(null)

  /**
   * 加载流程发现结果
   * @param forceRefresh 是否强制刷新数据
   */
  const loadDiscoveryResult = async (forceRefresh = false) => {
    const processId = parseInt(route.params.processId as string)
    if (!processId || isNaN(processId)) {
      ElMessage.error('无效的流程ID参数')
      return
    }

    try {
      isLoading.value = true
      console.log(`开始加载流程 ${processId} 的发现结果...`)

      // 调用流程发现API
      const dfgResult = await api.discoverProcess(processId, { forceRefresh })

      if (!dfgResult) {
        ElMessage.error('该流程暂无流程发现结果')
        return
      }

      // 处理结果数据，确保包含时间戳
      const processedResult = {
        ...dfgResult,
        timestamp: dfgResult?.timestamp || new Date().toISOString()
      }

      discoveryResult.value = processedResult
      console.log('流程发现结果加载成功:', processedResult)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '加载流程发现结果失败'
      console.error(error)
      ElMessage.error(errorMessage)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 保存流程模型
   * @param modelData DFG数据的JSON字符串
   * @param options 保存选项
   */
  const saveBpmnModel = async (
    modelData: string,
    options?: {
      name?: string
      description?: string
      modelType?: string
      status?: 'draft' | 'active' | 'archived'
    }
  ): Promise<{ success: boolean; modelId?: number }> => {
    const processId = parseInt(route.params.processId as string)
    if (!processId || isNaN(processId)) {
      ElMessage.error('无效的流程ID参数')
      return { success: false }
    }

    try {
      isSaving.value = true
      console.log('开始保存流程模型...')

      // 生成默认模型名和描述
      const defaultName = `流程发现编辑模型_${new Date().toLocaleString()}`
      const defaultDescription = '基于流程发现结果编辑的流程模型，用于符合性检查'

      // 调用API创建流程模型（实际保存DFG JSON数据）
      const model = await api.createBpmnModel({
        name: options?.name || defaultName,
        description: options?.description || defaultDescription,
        bpmnXml: modelData,
        processId: processId,
        modelType: (options?.modelType as 'discovered' | 'reference' | 'normative') || 'discovered',
        status: options?.status
      })

      if (model && model.id) {
        console.log('流程模型保存成功，模型ID:', model.id)
        ElMessage.success('流程模型保存成功')
        return { success: true, modelId: model.id }
      } else {
        throw new Error('保存失败：未返回有效的模型数据')
      }

    } catch (error) {
      console.error('保存流程模型失败:', error)
      ElMessage.error('保存流程模型失败')
      return { success: false }
    } finally {
      isSaving.value = false
    }
  }

  return {
    isLoading,
    isSaving,
    discoveryResult,

    loadDiscoveryResult,
    saveBpmnModel
  }
}
