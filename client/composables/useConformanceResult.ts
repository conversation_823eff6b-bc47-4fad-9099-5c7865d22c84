import { ref, computed, watch } from 'vue';
import type { ConformanceResult, BpmnModel } from '~/types';
import { ConformanceStatus } from '~/types';
import { api } from '~/utils/api';
import { ElMessage } from 'element-plus';

export interface DeviationDetail {
  caseId: string;
  description: string;
  severity: string;
  activity?: string;
  expectedActivity?: string;
  timestamp?: Date | string;
}

export interface DeviationItem {
  id: number;
  type: string;
  activity: string;
  count: number;
  percentage: number;
  deviations: DeviationDetail[];
}

export interface VariantEvent {
  id: number;
  name: string;
  hasDeviation: boolean;
  deviationType?: string;
  time?: string;
  timestamp?: string;
  duration?: number;
  resource?: string;
}

export interface Deviation {
  id: string;
  caseId: string;
  activity: string; // 活动名称
  type: string; // 偏差类型
  description: string;
  position?: number; // 在轨迹中的位置
}

export interface ProcessVariant {
  id: number;
  trace: string[];
  alignedTrace: string[];
  cases: number;
  isConforming: boolean;
  deviationCount: number;
  conformanceScore: number;
  events: VariantEvent[];
  caseIds: string[]; // 属于该变体的案例ID列表
  deviations: Deviation[]; // 该变体的偏差信息
}

export function useConformanceResult(resultId: string) {
  // 响应式数据
  const result = ref<ConformanceResult | null>(null);
  const bpmnModel = ref<BpmnModel | null>(null);
  const isLoading = ref(true);

  // 计算属性
  const processTitle = computed(() => {
    return result.value?.description || "流程符合性分析";
  });

  const conformanceRate = computed(() => {
    if (!result.value?.conformanceScore) return 0;
    const score = ensureNumberScore(result.value.conformanceScore);
    return Math.round(score * 100 * 100) / 100;
  });

  const conformanceRateText = computed(() => {
    return result.value?.description || "流程事件活动";
  });

  // 偏差类型转换函数
  const getDeviationTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      MISSING_ACTIVITY: "缺失活动",
      EXTRA_ACTIVITY: "额外活动",
      WRONG_ORDER: "顺序错误",
      SKIPPED_ACTIVITY: "跳过活动",
      REPEATED_ACTIVITY: "重复活动",
      TIMING_VIOLATION: "时间违规",
    };
    return typeMap[type.toUpperCase()] || type;
  };

  // 从真实数据获取偏差并按环节和类型分组
  const deviations = computed(() => {
    if (result.value?.deviations && result.value.deviations.length > 0) {
      // 按环节和类型分组
      const groupedDeviations = new Map();
      const totalDeviationsCount = result.value.deviations.length;

      result.value.deviations.forEach((deviation) => {
        const activity =
          deviation.activity || deviation.description || "未知活动";
        const type = deviation.type.toUpperCase();
        const key = `${activity}-${type}`;

        if (!groupedDeviations.has(key)) {
          groupedDeviations.set(key, {
            activity,
            type,
            count: 0,
            deviations: [],
          });
        }

        const group = groupedDeviations.get(key);
        group.count++;
        group.deviations.push(deviation);
      });

      // 转换为数组格式并计算百分比
      return Array.from(groupedDeviations.values()).map((group, index) => {
        const percentage =
          Math.round((group.count / totalDeviationsCount) * 100 * 100) / 100;
        return {
          id: index + 1,
          type: group.type,
          activity: group.activity,
          count: group.count,
          percentage: percentage,
          deviations: group.deviations,
        };
      });
    }

    return [];
  });

  const totalDeviations = computed(() => {
    return result.value?.deviations?.length || deviations.value.length;
  });

  const variantsCache = ref<ProcessVariant[] | null>(null);

  const processVariants = computed(() => {
    // 如果有缓存，直接返回
    if (variantsCache.value) {
      return variantsCache.value;
    }

    // 从caseAnalysis中提取变体信息，按trace分组
    if (!result.value?.caseAnalysis) return [];

    const startTime = performance.now();
    const caseAnalysisLength = result.value.caseAnalysis.length;
    console.log(`开始计算变体数据，案例数量: ${caseAnalysisLength}`);

    // 预处理偏差数据，建立索引
    const deviationsByCase = new Map<string, any[]>();
    if (result.value?.deviations) {
      result.value.deviations.forEach(deviation => {
        if (!deviationsByCase.has(deviation.caseId)) {
          deviationsByCase.set(deviation.caseId, []);
        }
        deviationsByCase.get(deviation.caseId)!.push(deviation);
      });
    }

    const variantMap = new Map();
    const processedCount = { value: 0 };

    // 批量处理案例数据
    result.value.caseAnalysis.forEach((caseData) => {
      const traceKey = caseData.trace.join(" -> ");

      if (!variantMap.has(traceKey)) {
        variantMap.set(traceKey, {
          id: variantMap.size + 1,
          trace: caseData.trace,
          alignedTrace: caseData.alignedTrace,
          cases: 0,
          isConforming: caseData.isConforming,
          deviationCount: caseData.deviationCount,
          conformanceScore: caseData.conformanceScore,
          caseIds: [], // 存储属于该变体的所有案例ID
          deviations: [], // 存储该变体的偏差信息
          deviationSet: new Set<string>(), // 用于快速去重
        });
      }

      const variant = variantMap.get(traceKey);
      variant.cases++;

      // 添加案例ID
      if (caseData.caseId) {
        variant.caseIds.push(caseData.caseId);
      }

      // 使用预建索引快速获取偏差
      if (!caseData.isConforming && deviationsByCase.has(caseData.caseId)) {
        const caseDeviations = deviationsByCase.get(caseData.caseId)!;

        // 使用Set进行快速去重
        caseDeviations.forEach(deviation => {
          const deviationKey = `${deviation.activity}-${deviation.type.toUpperCase()}`;

          if (!variant.deviationSet.has(deviationKey)) {
            variant.deviationSet.add(deviationKey);
            variant.deviations.push({
              id: deviationKey,
              caseId: '', // 不需要具体案例ID
              activity: deviation.activity,
              type: deviation.type.toUpperCase(),
              description: deviation.description
            });
          }
        });
      }

      processedCount.value++;

      // 每处理1000个案例输出一次进度
      if (processedCount.value % 1000 === 0) {
        console.log(`变体计算进度: ${processedCount.value}/${caseAnalysisLength} (${((processedCount.value / caseAnalysisLength) * 100).toFixed(1)}%)`);
      }
    });

    // 清理临时数据，释放内存
    variantMap.forEach((variant) => {
      delete variant.deviationSet; // 删除临时的Set对象
    });

    const variants = Array.from(variantMap.values()).sort((a, b) => b.cases - a.cases);

    const endTime = performance.now();
    const duration = endTime - startTime;
    console.log(`变体数据计算完成，耗时: ${duration.toFixed(2)}ms，生成 ${variants.length} 个变体`);

    // 缓存结果
    variantsCache.value = variants;

    if (duration > 2000) {
      console.warn(`变体数据计算耗时过长: ${duration.toFixed(2)}ms，建议优化`);
    }

    return variants;
  });

  const totalVariants = computed(() => {
    return processVariants.value.length;
  });

  const displayedVariants = computed(() => {
    // 根据数据量动态调整显示数量
    const totalVariants = processVariants.value.length;

    if (totalVariants <= 50) {
      return totalVariants; // 小数据量全部显示
    } else if (totalVariants <= 200) {
      return Math.min(totalVariants, 100); // 中等数据量显示100个
    } else {
      return Math.min(totalVariants, 50); // 大数据量只显示50个
    }
  });

  // 清除缓存的函数
  const clearVariantsCache = () => {
    variantsCache.value = null;
    console.log('变体缓存已清除');
  };

  // 监听结果数据变化，自动清除缓存
  watch(() => result.value, () => {
    clearVariantsCache();
  }, { deep: false });

  // 辅助函数
  const ensureNumberScore = (score: number | string | undefined): number => {
    if (score === undefined || score === null) return 0;
    const numScore = typeof score === "string" ? parseFloat(score) : score;
    return isNaN(numScore) ? 0 : numScore;
  };

  // 加载数据
  const loadData = async () => {
    try {
      isLoading.value = true;

      // 加载数据，如果正在处理中则轮询等待完成
      const data = await loadDataWithPolling();
      result.value = data as ConformanceResult;

      // 如果有BPMN模型ID，加载模型数据
      if (data && data.bpmnModelId) {
        try {
          const modelData = await api.getBpmnModel(data.bpmnModelId);
          bpmnModel.value = modelData as BpmnModel;
        } catch (modelError) {
          console.warn("Failed to load BPMN model:", modelError);
        }
      }
    } catch (error) {
      console.error("Failed to load conformance result:", error);
      ElMessage.error("加载符合性检查结果失败");
    } finally {
      isLoading.value = false;
    }
  };

  // 轮询加载数据，等待符合性检查完成
  const loadDataWithPolling = async (): Promise<ConformanceResult | null> => {
    const maxAttempts = 30; // 最多轮询30次
    const pollInterval = 2000; // 每2秒轮询一次
    let attempts = 0;

    while (attempts < maxAttempts) {
      try {
        const data = await api.getConformanceResultById(parseInt(resultId));

        if (!data) {
          throw new Error('符合性检查结果不存在');
        }

        // 检查状态
        if (data.status === ConformanceStatus.COMPLETED) {
          return data;
        } else if (data.status === ConformanceStatus.FAILED) {
          throw new Error('符合性检查失败');
        } else if (data.status === ConformanceStatus.PROCESSING) {
          console.log(`符合性检查正在进行中... (${attempts + 1}/${maxAttempts})`);
          // 设置处理中的结果，让UI能够显示处理状态
          result.value = data;
        }

        // 如果不是最后一次尝试，等待后继续
        if (attempts < maxAttempts - 1) {
          await new Promise(resolve => setTimeout(resolve, pollInterval));
        }

        attempts++;
      } catch (error) {
        if (attempts === 0) {
          // 第一次尝试失败，可能是网络问题，继续尝试
          console.warn('首次加载失败，继续尝试...', error);
          await new Promise(resolve => setTimeout(resolve, pollInterval));
          attempts++;
          continue;
        } else {
          // 后续尝试失败，抛出错误
          throw error;
        }
      }
    }

    // 轮询超时，返回最后一次获取的数据
    console.warn('轮询超时，返回当前数据');
    const finalData = await api.getConformanceResultById(parseInt(resultId));
    return finalData;
  };

  return {
    // 响应式数据
    result,
    bpmnModel,
    isLoading,
    
    // 计算属性
    processTitle,
    conformanceRate,
    conformanceRateText,
    deviations,
    totalDeviations,
    processVariants,
    totalVariants,
    displayedVariants,
    
    // 方法
    loadData,
    getDeviationTypeText,
    clearVariantsCache,
  };
}
