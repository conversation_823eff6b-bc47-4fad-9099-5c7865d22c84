import { ElMessage, ElMessageBox } from 'element-plus'
import type * as go from 'gojs'
import type { Ref, ComputedRef } from 'vue'

// 定义选中元素的类型
interface SelectedElement {
  id?: string
  key?: string
  name?: string
  label?: string
  frequency?: number
  isStartNode?: boolean
  isEndNode?: boolean
  [key: string]: unknown
}

// 定义上下文菜单目标的类型
interface ContextMenuTarget {
  key?: string
  from?: string
  to?: string
  source?: string
  target?: string
  name?: string
  label?: string
  frequency?: number
  [key: string]: unknown
}

// 定义图表引用的类型
interface DiagramRef {
  diagram?: go.Diagram
  [key: string]: unknown
}

export interface DfgOperationContext {
  diagramRef: Ref<DiagramRef | null>
  selectedElement: Ref<SelectedElement | null>
  contextMenuTarget: Ref<ContextMenuTarget | null> | ComputedRef<ContextMenuTarget | null>
  onOperationComplete: () => void
  updateConnectivity?: () => void
  refreshData?: () => void
  // 撤销重做支持
  undoRedo?: {
    recordAddNode: (description: string, beforeState: any, afterState: any) => void
    recordDeleteNode: (description: string, beforeState: any, afterState: any) => void
    recordAddLink: (description: string, beforeState: any, afterState: any) => void
    recordDeleteLink: (description: string, beforeState: any, afterState: any) => void
    recordBatchOperation: (description: string, beforeState: any, afterState: any) => void
    getCurrentState: () => { nodes: any[], links: any[] }
  }
}

export function useDfgOperations(context: DfgOperationContext) {

  // 获取图表实例
  const getDiagram = (): go.Diagram | null => {
    return context.diagramRef.value?.diagram || null
  }

  // 获取流程中所有可用的活动名称
  const getAvailableActivityNames = (): string[] => {
    const diagram = getDiagram()
    if (!diagram) return []

    const activityNames = new Set<string>()

    // 从当前DFG图表中收集所有节点名称
    diagram.nodes.each((node: go.Node) => {
      const name = node.data.name || node.data.label
      if (name && !node.data.isStartNode && !node.data.isEndNode) {
        activityNames.add(name)
      }
    })

    return Array.from(activityNames)
  }

  // 让用户选择活动名称
  const selectActivityName = async (): Promise<string | null> => {
    const availableNames = getAvailableActivityNames()
    if (availableNames.length === 0) {
      return '新活动'
    }

    try {
      const { value } = await ElMessageBox.prompt(
        `<div style="margin-bottom: 10px;">请选择或输入活动名称：</div>
         <div style="margin-bottom: 10px; font-size: 12px; color: #666;">
           可选活动：${availableNames.slice(0, 5).join('、')}${availableNames.length > 5 ? '等' : ''}
         </div>`,
        '选择活动',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputType: 'text',
          inputValue: availableNames[0],
          dangerouslyUseHTMLString: true,
          inputValidator: (value: string) => {
            if (!value || value.trim().length === 0) {
              return '活动名称不能为空'
            }
            if (value.trim().length > 50) {
              return '活动名称不能超过50个字符'
            }
            return true
          },
          inputPlaceholder: `例如：${availableNames.slice(0, 3).join('、')}`
        }
      )

      return value?.trim() || availableNames[0]
    } catch {
      // 用户取消
      return null
    }
  }
  
  // 执行事务操作
  const executeTransaction = (
    name: string,
    operation: (diagram: go.Diagram, model: go.GraphLinksModel) => void
  ): boolean => {
    const diagram = getDiagram()
    if (!diagram) {
      ElMessage.error('图表未初始化')
      return false
    }

    try {
      diagram.startTransaction(name)
      const model = diagram.model as go.GraphLinksModel
      operation(diagram, model)
      diagram.commitTransaction(name)
      
      // 操作完成后更新连通性状态
      if (context.updateConnectivity) {
        context.updateConnectivity()
      }
      
      // 刷新数据
      if (context.refreshData) {
        context.refreshData()
      }
      
      return true
    } catch (error) {
      console.error(`${name} 操作失败:`, error)
      ElMessage.error(`${name}失败`)
      return false
    }
  }

  // 节点操作：添加并行路径
  const addParallelPath = async () => {
    const selectedElement = context.selectedElement.value
    const contextMenuTarget = context.contextMenuTarget.value

    if (!selectedElement || !contextMenuTarget) {
      context.onOperationComplete()
      return
    }

    // 让用户选择活动名称
    const activityName = await selectActivityName()
    if (!activityName) {
      context.onOperationComplete()
      return
    }

    // 记录操作前状态
    const beforeState = context.undoRedo?.getCurrentState()

    const success = executeTransaction('add parallel path', (diagram, model) => {
      const currentNodeKey = selectedElement.id || selectedElement.key || 'unknown'
      const parallelNodeKey = `parallel-${currentNodeKey}-${Date.now()}`

      // 创建并行路径节点数据
      const parallelNodeData = {
        key: parallelNodeKey,
        id: parallelNodeKey,
        name: activityName,
        label: activityName,
        frequency: 1, // 默认频率
        text: activityName, // GoJS显示文本
        loc: '0 0',
        // 添加默认样式
        nodeStyle: {
          fill: '#2196F3',
          stroke: '#1976D2',
          strokeWidth: 2,
          textColor: '#FFFFFF',
          width: 80,
          height: 40
        }
      }
      
      model.addNodeData(parallelNodeData)
      
      // 找到当前节点的输入和输出连接
      const inputLinks: go.ObjectData[] = []
      const outputLinks: go.ObjectData[] = []
      
      diagram.links.each((link: go.Link) => {
        const linkFrom = link.data.from || link.data.source
        const linkTo = link.data.to || link.data.target
        
        if (linkTo === currentNodeKey) {
          inputLinks.push(link.data)
        }
        if (linkFrom === currentNodeKey) {
          outputLinks.push(link.data)
        }
      })
      
      // 为并行路径创建连接
      if (inputLinks.length > 0 && outputLinks.length > 0) {
        const inputNode = inputLinks[0].from || inputLinks[0].source
        const outputNode = outputLinks[0].to || outputLinks[0].target
        
        // 创建DFG格式的连接（使用source/target作为主要字段）
        model.addLinkData({
          key: `link-${inputNode}-${parallelNodeKey}`,
          source: inputNode,
          target: parallelNodeKey,
          frequency: 1,
          // 为兼容性添加from/to字段
          from: inputNode,
          to: parallelNodeKey,
          // 添加默认样式
          linkStyle: {
            stroke: '#2196F3',
            strokeWidth: 2,
            arrowScale: 1.0
          },
          label: '1',
          showLabel: true
        })

        model.addLinkData({
          key: `link-${parallelNodeKey}-${outputNode}`,
          source: parallelNodeKey,
          target: outputNode,
          frequency: 1,
          // 为兼容性添加from/to字段
          from: parallelNodeKey,
          to: outputNode,
          // 添加默认样式
          linkStyle: {
            stroke: '#2196F3',
            strokeWidth: 2,
            arrowScale: 1.0
          },
          label: '1',
          showLabel: true
        })
      }
    })

    if (success) {
      // 记录操作后状态
      const afterState = context.undoRedo?.getCurrentState()

      // 记录撤销重做历史
      if (beforeState && afterState && context.undoRedo) {
        context.undoRedo.recordBatchOperation(
          `添加并行路径: ${activityName}`,
          beforeState,
          afterState
        )
      }

      ElMessage.success('并行路径添加成功')
    }

    context.onOperationComplete()
  }

  // 节点操作：添加自循环
  const addSelfLoop = () => {
    const selectedElement = context.selectedElement.value
    const contextMenuTarget = context.contextMenuTarget.value

    if (!selectedElement || !contextMenuTarget) {
      context.onOperationComplete()
      return
    }

    // 记录操作前状态
    const beforeState = context.undoRedo?.getCurrentState()

    const success = executeTransaction('add self loop', (_diagram, model) => {
      const nodeKey = selectedElement.id || selectedElement.key || 'unknown'
      const selfLoopKey = `self-loop-${nodeKey}-${Date.now()}`

      // 创建DFG格式的自循环连接（使用source/target作为主要字段）
      const selfLoopData = {
        key: selfLoopKey,
        source: nodeKey,
        target: nodeKey,
        frequency: 1, // 默认频率
        isSelfLoop: true,
        // 为兼容性添加from/to字段
        from: nodeKey,
        to: nodeKey,
        // 添加默认样式
        linkStyle: {
          stroke: '#FF9800',
          strokeWidth: 2,
          arrowScale: 1.0
        },
        label: '1',
        showLabel: true
      }

      model.addLinkData(selfLoopData)
    })

    if (success) {
      // 记录操作后状态
      const afterState = context.undoRedo?.getCurrentState()

      // 记录撤销重做历史
      if (beforeState && afterState && context.undoRedo) {
        const nodeName = selectedElement.name || selectedElement.label || '节点'
        context.undoRedo.recordAddLink(
          `添加自循环: ${nodeName}`,
          beforeState,
          afterState
        )
      }

      ElMessage.success('自循环添加成功')
    }

    context.onOperationComplete()
  }

  // 节点操作：删除元素 - 适应DFG结构
  const deleteElement = async () => {
    const selectedElement = context.selectedElement.value
    const contextMenuTarget = context.contextMenuTarget.value

    if (!selectedElement || !contextMenuTarget) {
      context.onOperationComplete()
      return
    }

    // 检查是否是开始或结束节点
    const isStartOrEnd = selectedElement.isStartNode || selectedElement.isEndNode
    if (isStartOrEnd) {
      ElMessage.warning('不能删除开始或结束节点，这会导致流程不连通')
      context.onOperationComplete()
      return
    }

    try {
      await ElMessageBox.confirm(
        `确定要删除活动 "${selectedElement.name || selectedElement.label || '未知活动'}" 吗？`,
        '确认删除',
        {
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )

      // 记录操作前状态
      const beforeState = context.undoRedo?.getCurrentState()

      const success = executeTransaction('delete element', (diagram, model) => {
        const nodeKey = selectedElement.id || selectedElement.key || 'unknown'

        // 删除与该节点相关的所有连接线
        const linksToRemove: go.ObjectData[] = []
        diagram.links.each((link: go.Link) => {
          const linkFrom = link.data.from || link.data.source
          const linkTo = link.data.to || link.data.target

          if (linkFrom === nodeKey || linkTo === nodeKey) {
            linksToRemove.push(link.data)
          }
        })

        linksToRemove.forEach(linkData => {
          model.removeLinkData(linkData)
        })

        // 删除节点
        const nodeData = diagram.findNodeForKey(nodeKey as go.Key)?.data
        if (nodeData) {
          model.removeNodeData(nodeData)
        }
      })

      if (success) {
        // 记录操作后状态
        const afterState = context.undoRedo?.getCurrentState()

        // 记录撤销重做历史
        if (beforeState && afterState && context.undoRedo) {
          const nodeName = selectedElement.name || selectedElement.label || '活动'
          context.undoRedo.recordDeleteNode(
            `删除活动: ${nodeName}`,
            beforeState,
            afterState
          )
        }

        context.selectedElement.value = null
        ElMessage.success('活动删除成功')
      }

    } catch {
      // 用户取消删除
    }

    context.onOperationComplete()
  }

  // 连接线操作：插入活动 - 适应DFG结构
  const insertActivity = async () => {
    const selectedElement = context.selectedElement.value
    const contextMenuTarget = context.contextMenuTarget.value

    if (!selectedElement || !contextMenuTarget) {
      context.onOperationComplete()
      return
    }

    // 让用户选择活动名称
    const activityName = await selectActivityName()
    if (!activityName) {
      context.onOperationComplete()
      return
    }

    // 记录操作前状态
    const beforeState = context.undoRedo?.getCurrentState()

    const success = executeTransaction('insert activity', (diagram, model) => {
      const linkData = contextMenuTarget as ContextMenuTarget
      const fromNodeKey = (linkData.from || linkData.source) as string
      const toNodeKey = (linkData.to || linkData.target) as string
      const activityNodeKey = `activity-${Date.now()}`

      // 精确找到被右键点击的连接线（通过key匹配）
      let originalLinkData: go.ObjectData | null = null

      // 优先通过key精确匹配
      if (linkData.key) {
        diagram.links.each((link: go.Link) => {
          if (link.data.key === linkData.key) {
            originalLinkData = link.data
            return false // 停止迭代
          }
          return true
        })
      }

      // 如果通过key没找到，再通过from/to匹配（但只取第一个）
      if (!originalLinkData) {
        diagram.links.each((link: go.Link) => {
          const linkFrom = link.data.from || link.data.source
          const linkTo = link.data.to || link.data.target

          if (linkFrom === fromNodeKey && linkTo === toNodeKey) {
            originalLinkData = link.data
            return false // 停止迭代，只取第一个匹配的
          }
          return true
        })
      }

      if (!originalLinkData) {
        throw new Error('未找到原连接线')
      }

      // 创建DFG格式的活动节点
      const newNodeData = {
        key: activityNodeKey,
        id: activityNodeKey,
        name: activityName,
        label: activityName,
        frequency: 1, // 默认频率
        text: activityName, // GoJS显示文本
        loc: '0 0',
        // 添加默认样式
        nodeStyle: {
          fill: '#2196F3',
          stroke: '#1976D2',
          strokeWidth: 2,
          textColor: '#FFFFFF',
          width: 80,
          height: 40
        }
      }

      model.addNodeData(newNodeData)

      // 删除原连接线（只删除被点击的那一条）
      model.removeLinkData(originalLinkData)
      console.log(`删除原连接线: ${(originalLinkData as any)?.key || 'unknown'}`)

      // 获取原连接线的属性（使用类型断言）
      const originalLink = originalLinkData as any
      const originalFrequency = originalLink?.frequency || 1
      const originalLinkStyle = originalLink?.linkStyle || {
        stroke: '#2196F3',
        strokeWidth: 2,
        arrowScale: 1.0
      }

      // 创建第一条新连接线：从原起点到新插入的活动节点
      const firstLinkKey = `link-${fromNodeKey}-${activityNodeKey}`
      const firstLinkData = {
        key: firstLinkKey,
        source: fromNodeKey,
        target: activityNodeKey,
        frequency: originalFrequency, // 继承原连接线的频次
        // 为兼容性添加from/to字段
        from: fromNodeKey,
        to: activityNodeKey,
        // 继承原连接线的样式
        linkStyle: originalLinkStyle,
        label: String(originalFrequency),
        showLabel: true
      }

      model.addLinkData(firstLinkData)
      console.log(`创建第一条连接线: ${firstLinkKey}`)

      // 创建第二条新连接线：从新插入的活动节点到原终点
      const secondLinkKey = `link-${activityNodeKey}-${toNodeKey}`
      const secondLinkData = {
        key: secondLinkKey,
        source: activityNodeKey,
        target: toNodeKey,
        frequency: originalFrequency, // 继承原连接线的频次
        // 为兼容性添加from/to字段
        from: activityNodeKey,
        to: toNodeKey,
        // 继承原连接线的样式
        linkStyle: originalLinkStyle,
        label: String(originalFrequency),
        showLabel: true
      }

      model.addLinkData(secondLinkData)
      console.log(`创建第二条连接线: ${secondLinkKey}`)
    })

    if (success) {
      // 记录操作后状态
      const afterState = context.undoRedo?.getCurrentState()

      // 记录撤销重做历史
      if (beforeState && afterState && context.undoRedo) {
        context.undoRedo.recordBatchOperation(
          `插入活动: ${activityName}`,
          beforeState,
          afterState
        )
      }

      ElMessage.success('活动插入成功')
    }

    context.onOperationComplete()
  }

  // 连接线操作：删除连接
  const deleteConnection = async () => {
    const selectedElement = context.selectedElement.value
    const contextMenuTarget = context.contextMenuTarget.value

    if (!selectedElement || !contextMenuTarget) {
      context.onOperationComplete()
      return
    }

    // 构建连接描述信息
    const fromNode = contextMenuTarget.from || contextMenuTarget.source || '未知'
    const toNode = contextMenuTarget.to || contextMenuTarget.target || '未知'
    const connectionName = selectedElement.name || selectedElement.label || `${fromNode} -> ${toNode}`

    try {
      await ElMessageBox.confirm(
        `确定要删除连接 "${connectionName}" 吗？删除后可能导致流程不连通。`,
        '确认删除',
        {
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )

      // 记录操作前状态
      const beforeState = context.undoRedo?.getCurrentState()

      const success = executeTransaction('delete connection', (diagram, model) => {
        const linkData = contextMenuTarget as ContextMenuTarget
        let deletedLink: go.ObjectData | null = null

        // 尝试通过key查找连接
        if (linkData.key) {
          const link = diagram.findLinkForKey(linkData.key as go.Key)
          if (link) {
            deletedLink = link.data
            model.removeLinkData(link.data)
          }
        }

        // 如果没有key，尝试通过from/to或source/target查找连接
        if (!deletedLink) {
          const fromKey = linkData.from || linkData.source
          const toKey = linkData.to || linkData.target

          if (fromKey && toKey) {
            diagram.links.each((link: go.Link) => {
              const linkFrom = link.data.from || link.data.source
              const linkTo = link.data.to || link.data.target

              if (linkFrom === fromKey && linkTo === toKey) {
                deletedLink = link.data
                model.removeLinkData(link.data)
                return false // 停止迭代
              }
              return true // 继续迭代
            })
          }
        }

        // 最后尝试直接删除传入的数据
        if (!deletedLink) {
          deletedLink = linkData as go.ObjectData
          model.removeLinkData(linkData)
        }
      })

      if (success) {
        // 记录操作后状态
        const afterState = context.undoRedo?.getCurrentState()

        // 记录撤销重做历史
        if (beforeState && afterState && context.undoRedo) {
          context.undoRedo.recordDeleteLink(
            `删除连接: ${connectionName}`,
            beforeState,
            afterState
          )
        }

        context.selectedElement.value = null
        ElMessage.success('连接删除成功')
      }

    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除连接时发生错误:', error)
        ElMessage.error('删除连接失败')
      }
    }

    context.onOperationComplete()
  }

  return {
    addParallelPath,
    addSelfLoop,
    deleteElement,
    insertActivity,
    deleteConnection
  }
}
