import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type * as go from 'gojs'

// 操作类型定义
export interface UndoRedoOperation {
  id: string
  type: 'ADD_NODE' | 'DELETE_NODE' | 'ADD_LINK' | 'DELETE_LINK' | 'MODIFY_NODE' | 'MODIFY_LINK' | 'BATCH'
  description: string
  timestamp: number
  data: {
    // 前置状态（用于撤销）
    before: {
      nodes?: any[]
      links?: any[]
      modifiedNode?: any
      modifiedLink?: any
    }
    // 后置状态（用于重做）
    after: {
      nodes?: any[]
      links?: any[]
      modifiedNode?: any
      modifiedLink?: any
    }
  }
}

// 图表引用接口
interface DiagramRef {
  diagram?: go.Diagram
  [key: string]: unknown
}

export interface UndoRedoContext {
  diagramRef: () => DiagramRef | null
  onStateChange?: () => void
  maxHistorySize?: number
}

/**
 * 撤销重做功能
 */
export function useUndoRedo(context: UndoRedoContext) {
  // 操作历史栈
  const operationHistory = ref<UndoRedoOperation[]>([])
  const currentIndex = ref(-1)
  const maxHistorySize = context.maxHistorySize || 50

  // 计算属性
  const canUndo = computed(() => currentIndex.value >= 0)
  const canRedo = computed(() => currentIndex.value < operationHistory.value.length - 1)
  const historySize = computed(() => operationHistory.value.length)

  // 获取图表实例
  const getDiagram = (): go.Diagram | null => {
    const diagramRef = context.diagramRef()
    return diagramRef?.diagram || null
  }

  // 获取当前图表状态
  const getCurrentState = () => {
    const diagram = getDiagram()
    if (!diagram) return { nodes: [], links: [] }

    const model = diagram.model as go.GraphLinksModel
    return {
      nodes: model.nodeDataArray.map(node => ({ ...node })),
      links: model.linkDataArray.map(link => ({ ...link }))
    }
  }

  // 应用状态到图表
  const applyState = (state: { nodes: any[], links: any[] }) => {
    const diagram = getDiagram()
    if (!diagram) return

    diagram.startTransaction('apply state')
    try {
      const model = diagram.model as go.GraphLinksModel
      
      // 更新节点数据
      model.nodeDataArray = state.nodes.map(node => ({ ...node }))
      
      // 更新连接数据
      model.linkDataArray = state.links.map(link => ({ ...link }))
      
      diagram.commitTransaction('apply state')
      
      // 触发状态变化回调
      if (context.onStateChange) {
        context.onStateChange()
      }
      
    } catch (error) {
      console.error('应用状态失败:', error)
      diagram.rollbackTransaction()
    }
  }

  // 记录操作
  const recordOperation = (operation: Omit<UndoRedoOperation, 'id' | 'timestamp'>) => {
    // 生成操作ID
    const operationId = `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const fullOperation: UndoRedoOperation = {
      ...operation,
      id: operationId,
      timestamp: Date.now()
    }

    // 如果当前不在历史末尾，删除后续历史
    if (currentIndex.value < operationHistory.value.length - 1) {
      operationHistory.value = operationHistory.value.slice(0, currentIndex.value + 1)
    }

    // 添加新操作
    operationHistory.value.push(fullOperation)
    currentIndex.value = operationHistory.value.length - 1

    // 限制历史大小
    if (operationHistory.value.length > maxHistorySize) {
      operationHistory.value = operationHistory.value.slice(-maxHistorySize)
      currentIndex.value = operationHistory.value.length - 1
    }

    console.log(`记录操作: ${operation.description}`, fullOperation)
  }

  // 撤销操作
  const undo = () => {
    if (!canUndo.value) {
      ElMessage.warning('没有可撤销的操作')
      return false
    }

    const operation = operationHistory.value[currentIndex.value]
    console.log(`撤销操作: ${operation.description}`)

    try {
      // 应用前置状态
      if (operation.data.before.nodes && operation.data.before.links) {
        applyState({
          nodes: operation.data.before.nodes,
          links: operation.data.before.links
        })
      }

      currentIndex.value--
      ElMessage.success(`已撤销: ${operation.description}`)
      return true

    } catch (error) {
      console.error('撤销操作失败:', error)
      ElMessage.error('撤销操作失败')
      return false
    }
  }

  // 重做操作
  const redo = () => {
    if (!canRedo.value) {
      ElMessage.warning('没有可重做的操作')
      return false
    }

    currentIndex.value++
    const operation = operationHistory.value[currentIndex.value]
    console.log(`重做操作: ${operation.description}`)

    try {
      // 应用后置状态
      if (operation.data.after.nodes && operation.data.after.links) {
        applyState({
          nodes: operation.data.after.nodes,
          links: operation.data.after.links
        })
      }

      ElMessage.success(`已重做: ${operation.description}`)
      return true

    } catch (error) {
      console.error('重做操作失败:', error)
      ElMessage.error('重做操作失败')
      return false
    }
  }

  // 清空历史
  const clearHistory = () => {
    operationHistory.value = []
    currentIndex.value = -1
    console.log('操作历史已清空')
  }

  // 获取操作历史（用于调试）
  const getHistory = () => {
    return {
      operations: operationHistory.value,
      currentIndex: currentIndex.value,
      canUndo: canUndo.value,
      canRedo: canRedo.value
    }
  }

  // 记录节点添加操作
  const recordAddNode = (description: string, beforeState: any, afterState: any) => {
    recordOperation({
      type: 'ADD_NODE',
      description,
      data: {
        before: beforeState,
        after: afterState
      }
    })
  }

  // 记录节点删除操作
  const recordDeleteNode = (description: string, beforeState: any, afterState: any) => {
    recordOperation({
      type: 'DELETE_NODE',
      description,
      data: {
        before: beforeState,
        after: afterState
      }
    })
  }

  // 记录连接添加操作
  const recordAddLink = (description: string, beforeState: any, afterState: any) => {
    recordOperation({
      type: 'ADD_LINK',
      description,
      data: {
        before: beforeState,
        after: afterState
      }
    })
  }

  // 记录连接删除操作
  const recordDeleteLink = (description: string, beforeState: any, afterState: any) => {
    recordOperation({
      type: 'DELETE_LINK',
      description,
      data: {
        before: beforeState,
        after: afterState
      }
    })
  }

  // 记录批量操作
  const recordBatchOperation = (description: string, beforeState: any, afterState: any) => {
    recordOperation({
      type: 'BATCH',
      description,
      data: {
        before: beforeState,
        after: afterState
      }
    })
  }

  return {
    canUndo,
    canRedo,
    historySize,
    
    undo,
    redo,
    clearHistory,
    getCurrentState,
    
    recordOperation,
    recordAddNode,
    recordDeleteNode,
    recordAddLink,
    recordDeleteLink,
    recordBatchOperation,
    
    getHistory
  }
}
