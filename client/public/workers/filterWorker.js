// Web Worker for processing DFG filtering algorithms
// This worker handles computationally intensive filtering operations to avoid blocking the main thread

// 图遍历算法：从指定节点开始进行深度优先搜索
const dfsTraversal = (startNodeId, adjacencyMap, visited = new Set()) => {
  const reachableNodes = new Set()
  const stack = [startNodeId]
  
  while (stack.length > 0) {
    const currentNode = stack.pop()
    
    if (visited.has(currentNode)) continue
    
    visited.add(currentNode)
    reachableNodes.add(currentNode)
    
    const neighbors = adjacencyMap.get(currentNode) || []
    for (const neighbor of neighbors) {
      if (!visited.has(neighbor)) {
        stack.push(neighbor)
      }
    }
  }
  
  return reachableNodes
}

// 构建邻接表（正向图：从源到目标）
const buildForwardAdjacencyMap = (edges) => {
  const adjacencyMap = new Map()
  
  edges.forEach((edge) => {
    const fromId = edge.from || edge.source
    const toId = edge.to || edge.target
    
    if (!adjacencyMap.has(fromId)) {
      adjacencyMap.set(fromId, [])
    }
    adjacencyMap.get(fromId).push(toId)
  })
  
  return adjacencyMap
}

// 构建邻接表（反向图：从目标到源）
const buildBackwardAdjacencyMap = (edges) => {
  const adjacencyMap = new Map()
  
  edges.forEach((edge) => {
    const fromId = edge.from || edge.source
    const toId = edge.to || edge.target
    
    if (!adjacencyMap.has(toId)) {
      adjacencyMap.set(toId, [])
    }
    adjacencyMap.get(toId).push(fromId)
  })
  
  return adjacencyMap
}

// 检查条件是否有值
const hasConditionValue = (condition) => {
  switch (condition.type) {
    case 'resource':
      return condition.data.resources && condition.data.resources.length > 0
    case 'duration':
      return condition.data.min !== null || condition.data.max !== null
    case 'businessField':
      return condition.data.field !== '' && (
        condition.data.value !== '' ||
        (condition.data.operator === 'range' && condition.data.rangeMin !== '' && condition.data.rangeMax !== '')
      )
    case 'pathway':
      return condition.data.activity !== '' && 
             condition.data.frequency && 
             condition.data.frequency[0] >= 1 && 
             condition.data.frequency[1] >= condition.data.frequency[0]
    default:
      return false
  }
}

// 应用单个筛选条件
const applyCondition = (nodes, edges, condition) => {
  console.log('Worker: Applying condition:', condition)

  let filteredNodes = [...nodes]
  let filteredEdges = [...edges]

  switch (condition.type) {
    case 'resource':
      if (condition.data.resources?.length > 0) {
        filteredNodes = filteredNodes.filter((node) => {
          const resourceFields = ['resource', 'performer', 'executor', 'assignee', 'user', 'actor']
          const nodeResource = resourceFields.find(field => node[field])
          if (nodeResource) {
            return condition.data.resources.includes(node[nodeResource])
          }
          // Align with main thread: keep nodes without resource info
          return true
        })
      }
      break

    case 'duration':
      if (condition.data.min !== null || condition.data.max !== null) {
        // Convert to ms and filter both nodes and edges like main thread
        const toMs = (val, unit) => unit === 'hours' ? val * 3600000 : unit === 'days' ? val * 86400000 : val * 60000
        const minDuration = toMs(condition.data.min || 0, condition.data.unit)
        const maxDuration = condition.data.max != null ? toMs(condition.data.max, condition.data.unit) : Infinity

        filteredNodes = filteredNodes.filter((node) => {
          if(node.id === '开始' || node.id === '结束'){
            return true
          }

          const duration = node.avgDuration || 0
          return duration >= minDuration && duration <= maxDuration
        })

        filteredEdges = filteredEdges.filter((edge) => {
          if (condition.data.ignoreBoundaryEdges) {
            const src = edge.source || edge.from
            const dst = edge.target || edge.to
            const isStartLike = (id) => !!id && (id === '开始' || id === 'Start' || String(id).startsWith('开始') || String(id).startsWith('Start') || (edge.fromIsStartNode === true))
            const isEndLike = (id) => !!id && (id === '结束' || id === 'End' || String(id).startsWith('结束') || String(id).startsWith('End') || (edge.toIsEndNode === true))
            if (isStartLike(src) || isEndLike(dst)) return true
          }
          const duration = edge.avgDuration || 0
          return duration >= minDuration && duration <= maxDuration
        })
      }
      break

    case 'businessField':
      if (condition.data.field && (condition.data.value || (condition.data.operator === 'range' && condition.data.rangeMin && condition.data.rangeMax))) {
        filteredNodes = filteredNodes.filter((node) => {
          const fieldValue = node[condition.data.field]
          if (fieldValue == null) return false

          switch (condition.data.operator) {
            case 'contains':
              return String(fieldValue).toLowerCase().includes(String(condition.data.value).toLowerCase())
            case 'equals':
              return String(fieldValue) === String(condition.data.value)
            case 'gt':
              return Number(fieldValue) > Number(condition.data.value)
            case 'lt':
              return Number(fieldValue) < Number(condition.data.value)
            case 'range': {
              const min = Number(condition.data.rangeMin)
              const max = Number(condition.data.rangeMax)
              const val = Number(fieldValue)
              return val >= min && val <= max
            }
            default:
              return true
          }
        })
      }
      break

    case 'pathway':
      if (condition.data.activity) {
        const targetActivity = condition.data.activity
        const minFrequency = condition.data.frequency[0]
        const maxFrequency = condition.data.frequency[1]
        
        // console.log('Worker: Applying pathway filter:', {
        //   targetActivity,
        //   minFrequency,
        //   maxFrequency,
        //   type: condition.data.type,
        //   originalEdgeCount: filteredEdges.length
        // })

        if (targetActivity === 'ALL_NODES') {
          // 全部活动模式：对所有边进行筛选
          // const beforeCount = filteredEdges.length
          filteredEdges = filteredEdges.filter((edge) => {
            const frequency = edge.frequency || 0
            const passes = frequency >= minFrequency && frequency <= maxFrequency
            // if (!passes) {
            //   // const sourceId = edge.source || edge.from
            //   // const targetId = edge.target || edge.to
            //   // console.log(`Worker: Filtering out edge ${sourceId} -> ${targetId} (frequency: ${frequency})`)
            // }
            return passes
          })
          // console.log(`Worker: ALL_NODES filter: ${beforeCount} -> ${filteredEdges.length} edges`)
        } else {
          // 特定活动模式：筛选与指定活动相关的边
          const beforeCount = filteredEdges.length
          if (condition.data.type === 'predecessor') {
            // 前置活动筛选：筛选指向目标活动的边
            filteredEdges = filteredEdges.filter((edge) => {
              const targetId = edge.target || edge.to
              if (targetId === targetActivity) {
                const frequency = edge.frequency || 0
                const passes = frequency >= minFrequency && frequency <= maxFrequency
                // if (!passes) {
                //   const sourceId = edge.source || edge.from
                //   console.log(`Worker: Filtering out predecessor edge ${sourceId} -> ${targetActivity} (frequency: ${frequency})`)
                // }
                return passes
              }
              return true // 保留与目标活动无关的边
            })
          } else {
            // 后续活动筛选：筛选从目标活动出发的边
            filteredEdges = filteredEdges.filter((edge) => {
              const sourceId = edge.source || edge.from
              if (sourceId === targetActivity) {
                const frequency = edge.frequency || 0
                const passes = frequency >= minFrequency && frequency <= maxFrequency
                // if (!passes) {
                //   const targetId = edge.target || edge.to
                //   console.log(`Worker: Filtering out successor edge ${targetActivity} -> ${targetId} (frequency: ${frequency})`)
                // }
                return passes
              }
              return true // 保留与目标活动无关的边
            })
          }
          // console.log(`Worker: Specific activity filter (${condition.data.type}): ${beforeCount} -> ${filteredEdges.length} edges`)
        }
      }
      break

    default:
      console.warn('Worker: Unknown condition type:', condition.type)
  }

  return { nodes: filteredNodes, edges: filteredEdges }
}

// 主要的筛选处理函数
const processFilters = (originalData, filters) => {
  // console.log('Worker: Starting filter processing')
  
  // 发送进度更新
  self.postMessage({
    type: 'FILTER_PROGRESS',
    payload: { stage: 'initializing', progress: 0 }
  })

  if (!filters || !originalData || !filters.groups || filters.groups.length === 0) {
    // console.log('Worker: No filters to apply, returning original data')
    return originalData
  }

  let filteredNodes = [...(originalData.nodes || [])]
  let filteredEdges = [...(originalData.edges || [])]

  // console.log('Worker: Initial counts - Nodes:', filteredNodes.length, 'Edges:', filteredEdges.length)

  // 发送进度更新
  self.postMessage({
    type: 'FILTER_PROGRESS',
    payload: { stage: 'applying_conditions', progress: 10 }
  })

  // 应用筛选组
  filters.groups.forEach((group, groupIndex) => {
    // console.log(`Worker: Processing group ${groupIndex}:`, group)

    const enabledConditions = group.conditions.filter((condition) => condition.enabled)
    if (enabledConditions.length === 0) return

    // 根据组内逻辑处理条件
    if (group.logic === 'AND') {
      // AND 逻辑：所有条件都必须满足
      enabledConditions.forEach((condition) => {
        const result = applyCondition(filteredNodes, filteredEdges, condition)
        filteredNodes = result.nodes
        filteredEdges = result.edges
      })
    } else {
      // OR 逻辑：任一条件满足即可
      const orResults = []
      enabledConditions.forEach((condition) => {
        const result = applyCondition(filteredNodes, filteredEdges, condition)
        orResults.push(result)
      })

      // 合并OR结果
      if (orResults.length > 0) {
        const allNodes = new Set()
        const allEdges = new Set()

        orResults.forEach(result => {
          result.nodes.forEach((node) => allNodes.add(JSON.stringify(node)))
          result.edges.forEach((edge) => allEdges.add(JSON.stringify(edge)))
        })

        filteredNodes = Array.from(allNodes).map(nodeStr => JSON.parse(nodeStr))
        filteredEdges = Array.from(allEdges).map(edgeStr => JSON.parse(edgeStr))
      }
    }
  })

  // 发送进度更新
  self.postMessage({
    type: 'FILTER_PROGRESS',
    payload: { stage: 'cleaning_edges', progress: 30 }
  })

  // 确保边的节点都存在于筛选后的节点中
  const nodeIds = new Set(filteredNodes.map((node) => node.id || node.label))
  const originalEdgeCount = filteredEdges.length
  filteredEdges = filteredEdges.filter((edge) => {
    const fromId = edge.from || edge.source
    const toId = edge.to || edge.target
    const isValid = nodeIds.has(fromId) && nodeIds.has(toId)
    // if (!isValid) {
    //   console.log(`Worker: Removing edge ${fromId} -> ${toId} because nodes not found`)
    // }
    return isValid
  })

  // console.log(`Worker: Edge cleanup: ${originalEdgeCount} -> ${filteredEdges.length} edges`)

  // 继续处理其他算法...
  return processAdvancedFiltering(filteredNodes, filteredEdges, originalData)
}

// 处理高级筛选算法（孤立节点、单向节点、连通性分析）
const processAdvancedFiltering = (filteredNodes, filteredEdges, originalData) => {
  // 发送进度更新
  self.postMessage({
    type: 'FILTER_PROGRESS',
    payload: { stage: 'removing_isolated_nodes', progress: 40 }
  })

  // 移除没有连线的孤立节点（保留开始和结束节点，包括子流程的开始和结束节点）
  const connectedNodeIds = new Set()
  filteredEdges.forEach((edge) => {
    const fromId = edge.from || edge.source
    const toId = edge.to || edge.target
    connectedNodeIds.add(fromId)
    connectedNodeIds.add(toId)
  })

  const originalNodeCount = filteredNodes.length
  filteredNodes = filteredNodes.filter((node) => {
    const nodeId = node.id || node.label

    // 保留所有开始和结束节点，包括子流程的开始和结束节点
    if (node.isStartNode || node.isEndNode ||
        nodeId === '开始' || nodeId === '结束' ||
        nodeId.startsWith('开始_') || nodeId.startsWith('结束_')) {
      return true
    }

    // 保留有连线的节点
    const isConnected = connectedNodeIds.has(nodeId)
    // if (!isConnected) {
    //   console.log(`Worker: Removing isolated node: ${nodeId}`)
    // }
    return isConnected
  })

  // console.log(`Worker: Node cleanup: ${originalNodeCount} -> ${filteredNodes.length} nodes (removed isolated nodes)`)

  // 发送进度更新
  self.postMessage({
    type: 'FILTER_PROGRESS',
    payload: { stage: 'removing_unidirectional_nodes', progress: 50 }
  })

  // 迭代式移除只有单向连接的节点（保留开始和结束节点）
  // console.log('Worker: === ITERATIVE UNIDIRECTIONAL NODE REMOVAL ===')
  let iterationCount = 0
  const totalRemovedNodes = []

  while (true) {
    iterationCount++
    // console.log(`Worker: --- Iteration ${iterationCount} ---`)

    const nodeConnectionInfo = new Map()

    // 初始化所有节点的连接信息
    filteredNodes.forEach((node) => {
      const nodeId = node.id || node.label
      nodeConnectionInfo.set(nodeId, { incoming: 0, outgoing: 0 })
    })

    // 统计每个节点的入边和出边数量
    filteredEdges.forEach((edge) => {
      const fromId = edge.from || edge.source
      const toId = edge.to || edge.target

      const fromInfo = nodeConnectionInfo.get(fromId)
      const toInfo = nodeConnectionInfo.get(toId)

      if (fromInfo) fromInfo.outgoing++
      if (toInfo) toInfo.incoming++
    })

    // 识别本轮要移除的单向连接节点
    const currentIterationRemovedNodes = []
    const beforeCount = filteredNodes.length

    filteredNodes = filteredNodes.filter((node) => {
      const nodeId = node.id || node.label

      // 保留所有开始和结束节点，包括子流程的开始和结束节点，即使它们只有单向连接
      if (node.isStartNode || node.isEndNode ||
          nodeId === '开始' || nodeId === '结束' ||
          nodeId.startsWith('开始_') || nodeId.startsWith('结束_')) {
        return true
      }

      const connectionInfo = nodeConnectionInfo.get(nodeId)
      if (!connectionInfo) return false

      // 检查是否为单向连接节点（只有入边或只有出边，但不是两者都有）
      const hasIncoming = connectionInfo.incoming > 0
      const hasOutgoing = connectionInfo.outgoing > 0
      const isBidirectional = hasIncoming && hasOutgoing

      // 只移除那些只有单向连接的节点（死胡同节点）
      if (!isBidirectional && (hasIncoming || hasOutgoing)) {
        currentIterationRemovedNodes.push(nodeId)
        // const direction = hasIncoming ? 'only incoming' : 'only outgoing'
        // console.log(`Worker: Removing unidirectional node: ${nodeId} (${direction}, incoming: ${connectionInfo.incoming}, outgoing: ${connectionInfo.outgoing})`)
        return false
      }

      return true
    })

    // 如果本轮没有移除任何节点，则停止迭代
    if (currentIterationRemovedNodes.length === 0) {
      // console.log(`Worker: No more unidirectional nodes found. Stopping after ${iterationCount} iterations.`)
      break
    }

    // 移除与本轮移除节点相关的边
    const removedNodeSet = new Set(currentIterationRemovedNodes)
    const beforeEdgeCount = filteredEdges.length

    filteredEdges = filteredEdges.filter((edge) => {
      const fromId = edge.from || edge.source
      const toId = edge.to || edge.target

      const shouldRemove = removedNodeSet.has(fromId) || removedNodeSet.has(toId)
      if (shouldRemove) {
        console.log(`Worker: Removing edge connected to unidirectional node: ${fromId} -> ${toId}`)
      }
      return !shouldRemove
    })

    totalRemovedNodes.push(...currentIterationRemovedNodes)
    console.log(`Worker: Iteration ${iterationCount} results: ${beforeCount} -> ${filteredNodes.length} nodes (removed ${currentIterationRemovedNodes.length})`)
    console.log(`Worker: Edges: ${beforeEdgeCount} -> ${filteredEdges.length}`)

    // 防止无限循环
    if (iterationCount > 10) {
      console.warn('Worker: Maximum iterations reached, stopping unidirectional node removal')
      break
    }
  }

  // 发送进度更新
  self.postMessage({
    type: 'FILTER_PROGRESS',
    payload: { stage: 'connectivity_analysis', progress: 70 }
  })

  // 移除与开始节点和结束节点不连通的孤立子图
  // console.log('Worker: === CONNECTIVITY ANALYSIS AND ISOLATED SUBGRAPH REMOVAL ===')

  // 找到开始节点和结束节点（包括子流程的开始和结束节点）
  const startNodes = filteredNodes.filter((node) => {
    const nodeId = node.id || node.label
    return node.isStartNode || nodeId === '开始' || nodeId.startsWith('开始_')
  })
  const endNodes = filteredNodes.filter((node) => {
    const nodeId = node.id || node.label
    return node.isEndNode || nodeId === '结束' || nodeId.startsWith('结束_')
  })

  // console.log('Worker: Start nodes found:', startNodes.map((n) => n.id || n.label))
  // console.log('Worker: End nodes found:', endNodes.map((n) => n.id || n.label))

  if (startNodes.length === 0 || endNodes.length === 0) {
    console.warn('Worker: No start or end nodes found, skipping connectivity analysis')
  } else {
    // 构建正向和反向邻接表
    const forwardAdjacencyMap = buildForwardAdjacencyMap(filteredEdges)
    const backwardAdjacencyMap = buildBackwardAdjacencyMap(filteredEdges)

    // 从开始节点进行正向遍历，找到所有可达节点
    const reachableFromStart = new Set()
    startNodes.forEach((startNode) => {
      const startNodeId = startNode.id || startNode.label
      const reachable = dfsTraversal(startNodeId, forwardAdjacencyMap)
      reachable.forEach(nodeId => reachableFromStart.add(nodeId))
    })

    // console.log('Worker: Nodes reachable from start:', Array.from(reachableFromStart))

    // 从结束节点进行反向遍历，找到所有可以到达结束节点的节点
    const canReachEnd = new Set()
    endNodes.forEach((endNode) => {
      const endNodeId = endNode.id || endNode.label
      const reachable = dfsTraversal(endNodeId, backwardAdjacencyMap)
      reachable.forEach(nodeId => canReachEnd.add(nodeId))
    })

    // console.log('Worker: Nodes that can reach end:', Array.from(canReachEnd))

    // 找到在主流程路径上的节点（既可以从开始节点到达，又可以到达结束节点）
    const mainPathNodes = new Set()
    reachableFromStart.forEach(nodeId => {
      if (canReachEnd.has(nodeId)) {
        mainPathNodes.add(nodeId)
      }
    })

    // console.log('Worker: Main path nodes (reachable from start AND can reach end):', Array.from(mainPathNodes))

    // 识别孤立节点（不在主流程路径上的节点）
    const isolatedNodes = []
    const beforeConnectivityCount = filteredNodes.length

    filteredNodes = filteredNodes.filter((node) => {
      const nodeId = node.id || node.label

      if (mainPathNodes.has(nodeId)) {
        return true
      } else {
        isolatedNodes.push(nodeId)
        // console.log(`Worker: Removing isolated node: ${nodeId} (not in main path)`)
        return false
      }
    })

    // 移除与孤立节点相关的边
    const isolatedNodeSet = new Set(isolatedNodes)
    const beforeIsolatedEdgeCleanup = filteredEdges.length

    filteredEdges = filteredEdges.filter((edge) => {
      const fromId = edge.from || edge.source
      const toId = edge.to || edge.target

      const shouldRemove = isolatedNodeSet.has(fromId) || isolatedNodeSet.has(toId)
      if (shouldRemove) {
        // console.log(`Worker: Removing edge connected to isolated node: ${fromId} -> ${toId}`)
      }
      return !shouldRemove
    })

    // console.log(`Worker: Connectivity cleanup: ${beforeConnectivityCount} -> ${filteredNodes.length} nodes (removed ${isolatedNodes.length} isolated nodes)`)
    // console.log(`Worker: Edge cleanup after connectivity removal: ${beforeIsolatedEdgeCleanup} -> ${filteredEdges.length} edges`)
  }

  // 发送进度更新
  self.postMessage({
    type: 'FILTER_PROGRESS',
    payload: { stage: 'finalizing', progress: 90 }
  })

  // 计算筛选后的统计信息
  const activityNodes = filteredNodes.filter((node) =>
    !node.isStartNode && !node.isEndNode &&
    node.id !== '开始' && node.id !== '结束'
  )

  const result = {
    ...originalData,
    nodes: filteredNodes,
    edges: filteredEdges,
    statistics: {
      ...originalData.statistics,
      totalCases: originalData.statistics?.totalCases || 0, // 保持原始案例数
      totalActivities: activityNodes.length, // 只计算实际的活动节点数
      filteredActivities: activityNodes.length, // 新增：筛选后的活动数
      filteredEdges: filteredEdges.length // 新增：筛选后的边数
    }
  }

  // console.log('Worker: Filter processing complete')
  // console.log('Worker: Final counts - Nodes:', filteredNodes.length, 'Edges:', filteredEdges.length)

  return result
}

// Worker消息处理
self.onmessage = (event) => {
  const { type, payload } = event.data

  try {
    switch (type) {
      case 'APPLY_FILTERS':
        const result = processFilters(payload.originalData, payload.filters)
        
        self.postMessage({
          type: 'FILTER_COMPLETE',
          payload: result
        })
        break

      default:
        console.warn('Worker: Unknown message type:', type)
    }
  } catch (error) {
    console.error('Worker: Error processing filters:', error)
    self.postMessage({
      type: 'FILTER_ERROR',
      payload: { error: error.message }
    })
  }
}

console.log('Worker: Filter worker initialized')
