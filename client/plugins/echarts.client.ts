import { use } from 'echarts/core'
import {
  <PERSON>vas<PERSON>enderer
} from 'echarts/renderers'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON>atter<PERSON>hart,
  BoxplotChart,
  HeatmapChart,
  RadarChart
} from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  ToolboxComponent
} from 'echarts/components'
import VChart from 'vue-echarts'

use([
  CanvasRenderer,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  Line<PERSON>hart,
  Scatter<PERSON>hart,
  BoxplotChart,
  HeatmapChart,
  RadarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  ToolboxComponent
])

export default defineNuxtPlugin((nuxtApp) => {
  // 全局注册 VChart 组件
  nuxtApp.vueApp.component('<PERSON><PERSON>', <PERSON><PERSON>)
})
