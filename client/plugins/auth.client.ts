export default defineNuxtPlugin(async () => {
  const authStore = useAuthStore()

  // 添加存储监听器
  if (import.meta.client) {
    // 监听 localStorage 变化
    window.addEventListener('storage', (e) => {
      console.log('Storage 事件触发:', {
        key: e.key,
        oldValue: e.oldValue,
        newValue: e.newValue,
        url: e.url
      })

      if (e.key === 'auth_token_content' || e.key === 'auth_token_expiry') {
        console.log('认证相关存储发生变化:', {
          key: e.key,
          oldValue: e.oldValue ? `${e.oldValue.substring(0, 10)}...` : null,
          newValue: e.newValue ? `${e.newValue.substring(0, 10)}...` : null
        })
      }
    })

    // 定期检查存储一致性
    setInterval(() => {
      const token = localStorage.getItem('auth_token_content')
      const tokenExpiry = localStorage.getItem('auth_token_expiry')

      if ((token && !tokenExpiry) || (!token && tokenExpiry)) {
        console.warn('检测到认证数据不一致:', { hasToken: !!token, hasExpiry: !!tokenExpiry })
      }
    }, 5000) // 每5秒检查一次
  }

  // 初始化认证状态
  await authStore.initAuth()
})
