import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'

export default defineNuxtPlugin(() => {
  // 创建带有默认配置的消息函数
  const message = (options: any) => {
    if (typeof options === 'string') {
      return ElMessage({
        message: options,
        duration: 3000,
        showClose: true,
      })
    }
    return ElMessage({
      duration: 3000,
      showClose: true,
      ...options,
    })
  }

  // 添加类型方法
  message.success = (options: any) => {
    if (typeof options === 'string') {
      return ElMessage.success({
        message: options,
        duration: 3000,
        showClose: true,
      })
    }
    return ElMessage.success({
      duration: 3000,
      showClose: true,
      ...options,
    })
  }

  message.warning = (options: any) => {
    if (typeof options === 'string') {
      return ElMessage.warning({
        message: options,
        duration: 3000,
        showClose: true,
      })
    }
    return ElMessage.warning({
      duration: 3000,
      showClose: true,
      ...options,
    })
  }

  message.info = (options: any) => {
    if (typeof options === 'string') {
      return ElMessage.info({
        message: options,
        duration: 3000,
        showClose: true,
      })
    }
    return ElMessage.info({
      duration: 3000,
      showClose: true,
      ...options,
    })
  }

  message.error = (options: any) => {
    if (typeof options === 'string') {
      return ElMessage.error({
        message: options,
        duration: 3000,
        showClose: true,
      })
    }
    return ElMessage.error({
      duration: 3000,
      showClose: true,
      ...options,
    })
  }

  // 创建带有默认配置的通知函数
  const notification = (options: any) => {
    return ElNotification({
      duration: 4000,
      showClose: true,
      ...options,
    })
  }

  // 添加类型方法
  notification.success = (options: any) => {
    return ElNotification.success({
      duration: 4000,
      showClose: true,
      ...options,
    })
  }

  notification.warning = (options: any) => {
    return ElNotification.warning({
      duration: 4000,
      showClose: true,
      ...options,
    })
  }

  notification.info = (options: any) => {
    return ElNotification.info({
      duration: 4000,
      showClose: true,
      ...options,
    })
  }

  notification.error = (options: any) => {
    return ElNotification.error({
      duration: 4000,
      showClose: true,
      ...options,
    })
  }

  // 设置全局样式变量（仅在客户端）
  if (process.client) {
    const root = document.documentElement

    // 设置 Element Plus 主题色
    root.style.setProperty('--el-color-primary', '#2563eb')
    root.style.setProperty('--el-color-primary-light-3', '#60a5fa')
    root.style.setProperty('--el-color-primary-light-5', '#93c5fd')
    root.style.setProperty('--el-color-primary-light-7', '#bfdbfe')
    root.style.setProperty('--el-color-primary-light-8', '#dbeafe')
    root.style.setProperty('--el-color-primary-light-9', '#eff6ff')
    root.style.setProperty('--el-color-primary-dark-2', '#1d4ed8')

    // 设置边框圆角
    root.style.setProperty('--el-border-radius-base', '6px')
    root.style.setProperty('--el-border-radius-small', '4px')
    root.style.setProperty('--el-border-radius-round', '20px')
    root.style.setProperty('--el-border-radius-circle', '100%')

    // 设置字体
    root.style.setProperty('--el-font-family', "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif")
  }

  return {
    provide: {
      message,
      messageBox: ElMessageBox,
      notification
    }
  }
})
