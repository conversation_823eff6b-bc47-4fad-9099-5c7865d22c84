{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "start": "node .output/server/index.mjs", "postinstall": "nuxt prepare"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@element-plus/nuxt": "^1.1.3", "@nuxt/eslint": "1.4.1", "@nuxtjs/color-mode": "^3.5.2", "@pinia/nuxt": "^0.11.1", "@vueuse/core": "^13.4.0", "@vueuse/nuxt": "^13.4.0", "echarts": "^5.6.0", "element-plus": "^2.10.2", "eslint": "^9.0.0", "gojs": "^3.0.23", "nuxt": "^3.17.5", "pinia": "^3.0.3", "typescript": "^5.6.3", "vue": "^3.5.16", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1", "zod": "^3.25.67"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "devDependencies": {"sass": "^1.89.2", "sass-embedded": "^1.89.2"}}