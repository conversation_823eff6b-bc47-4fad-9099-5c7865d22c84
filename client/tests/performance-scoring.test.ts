import { describe, it, expect } from 'vitest'

// 模拟性能评分函数（从组件中提取）
const calculateExecutionScore = (durationHours: number): number => {
  if (durationHours <= 0.5) return 100      // 30分钟以内：优秀
  if (durationHours <= 2) return 85         // 2小时以内：良好
  if (durationHours <= 8) return 70         // 8小时以内：一般
  if (durationHours <= 24) return 50        // 24小时以内：较差
  if (durationHours <= 72) return 30        // 3天以内：差
  return 10                                  // 超过3天：很差
}

const calculateWaitingScore = (waitingHours: number): number => {
  if (waitingHours === 0) return 100        // 无等待：优秀
  if (waitingHours <= 1) return 90          // 1小时以内：优秀
  if (waitingHours <= 4) return 75          // 4小时以内：良好
  if (waitingHours <= 24) return 55         // 24小时以内：一般
  if (waitingHours <= 72) return 35         // 3天以内：较差
  return 15                                  // 超过3天：很差
}

const calculatePerformanceScore = (activity: { avgDuration: number; avgWaitingTime: number }) => {
  const avgDuration = activity.avgDuration || 0
  const avgWaitingTime = activity.avgWaitingTime || 0
  
  const durationHours = avgDuration / (1000 * 60 * 60)
  const waitingHours = avgWaitingTime / (1000 * 60 * 60)
  
  const executionScore = calculateExecutionScore(durationHours)
  const waitingScore = calculateWaitingScore(waitingHours)
  
  // 综合评分：执行时间权重60%，等待时间权重40%
  const compositeScore = executionScore * 0.6 + waitingScore * 0.4
  
  if (compositeScore >= 85) return 'excellent'
  if (compositeScore >= 70) return 'good'
  if (compositeScore >= 50) return 'average'
  return 'poor'
}

describe('性能评分算法', () => {
  describe('执行时间评分', () => {
    it('应该为快速执行给出高分', () => {
      expect(calculateExecutionScore(0.25)).toBe(100) // 15分钟
      expect(calculateExecutionScore(0.5)).toBe(100)  // 30分钟
    })

    it('应该为中等执行时间给出中等分数', () => {
      expect(calculateExecutionScore(1)).toBe(85)     // 1小时
      expect(calculateExecutionScore(4)).toBe(70)     // 4小时
    })

    it('应该为长时间执行给出低分', () => {
      expect(calculateExecutionScore(12)).toBe(50)    // 12小时
      expect(calculateExecutionScore(48)).toBe(30)    // 2天
      expect(calculateExecutionScore(96)).toBe(10)    // 4天
    })
  })

  describe('等待时间评分', () => {
    it('应该为无等待给出满分', () => {
      expect(calculateWaitingScore(0)).toBe(100)
    })

    it('应该为短等待时间给出高分', () => {
      expect(calculateWaitingScore(0.5)).toBe(90)     // 30分钟
      expect(calculateWaitingScore(1)).toBe(90)       // 1小时
    })

    it('应该为长等待时间给出低分', () => {
      expect(calculateWaitingScore(12)).toBe(55)      // 12小时
      expect(calculateWaitingScore(48)).toBe(35)      // 2天
      expect(calculateWaitingScore(96)).toBe(15)      // 4天
    })
  })

  describe('综合性能评分', () => {
    it('应该正确评估优秀性能', () => {
      // 快速执行 + 无等待
      const excellentActivity = {
        avgDuration: 30 * 60 * 1000,    // 30分钟
        avgWaitingTime: 0               // 无等待
      }
      expect(calculatePerformanceScore(excellentActivity)).toBe('excellent')
    })

    it('应该正确评估良好性能', () => {
      // 中等执行 + 短等待
      const goodActivity = {
        avgDuration: 2 * 60 * 60 * 1000,    // 2小时
        avgWaitingTime: 1 * 60 * 60 * 1000  // 1小时等待
      }
      expect(calculatePerformanceScore(goodActivity)).toBe('good')
    })

    it('应该正确评估一般性能', () => {
      // 较长执行 + 中等等待
      const averageActivity = {
        avgDuration: 8 * 60 * 60 * 1000,    // 8小时
        avgWaitingTime: 4 * 60 * 60 * 1000  // 4小时等待
      }
      expect(calculatePerformanceScore(averageActivity)).toBe('average')
    })

    it('应该正确评估较差性能', () => {
      // 长时间执行 + 长时间等待
      const poorActivity = {
        avgDuration: 24 * 60 * 60 * 1000,   // 24小时
        avgWaitingTime: 24 * 60 * 60 * 1000 // 24小时等待
      }
      expect(calculatePerformanceScore(poorActivity)).toBe('poor')
    })

    it('应该正确处理只有执行时间的情况', () => {
      const executionOnlyActivity = {
        avgDuration: 1 * 60 * 60 * 1000,    // 1小时
        avgWaitingTime: 0                   // 无等待
      }
      expect(calculatePerformanceScore(executionOnlyActivity)).toBe('excellent')
    })

    it('应该正确处理只有等待时间的情况', () => {
      const waitingOnlyActivity = {
        avgDuration: 0,                     // 无执行时间
        avgWaitingTime: 12 * 60 * 60 * 1000 // 12小时等待
      }
      expect(calculatePerformanceScore(waitingOnlyActivity)).toBe('average')
    })
  })

  describe('权重测试', () => {
    it('执行时间应该有更高的权重', () => {
      // 测试执行时间权重60%，等待时间权重40%
      const activity1 = {
        avgDuration: 24 * 60 * 60 * 1000,   // 24小时执行（50分）
        avgWaitingTime: 0                   // 无等待（100分）
      }
      // 综合分数：50 * 0.6 + 100 * 0.4 = 70
      expect(calculatePerformanceScore(activity1)).toBe('good')

      const activity2 = {
        avgDuration: 0,                     // 无执行时间（100分）
        avgWaitingTime: 24 * 60 * 60 * 1000 // 24小时等待（55分）
      }
      // 综合分数：100 * 0.6 + 55 * 0.4 = 82
      expect(calculatePerformanceScore(activity2)).toBe('good')
    })
  })
})
