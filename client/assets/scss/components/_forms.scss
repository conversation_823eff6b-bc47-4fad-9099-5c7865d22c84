// ProMax 表单组件样式
@use '../utils/variables' as *;
@use '../utils/mixins' as *;
@use 'sass:map';

.form {
  &__group {
    margin-bottom: spacing(4);
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  &__label {
    display: block;
    font-size: font-size(sm);
    font-weight: map.get($font-weight, medium);
    color: theme-color(gray, 700);
    margin-bottom: spacing(2);
    
    .dark & {
      color: theme-color(gray, 300);
    }
    
    &--required::after {
      content: ' *';
      color: theme-color(error, 500);
    }
  }
  
  &__input {
    @include input();
    
    &--error {
      border-color: theme-color(error, 500);
      
      &:focus {
        border-color: theme-color(error, 500);
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
      }
    }
    
    &--success {
      border-color: theme-color(success, 500);
      
      &:focus {
        border-color: theme-color(success, 500);
        box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
      }
    }
  }
  
  &__textarea {
    @include input();
    min-height: 100px;
    resize: vertical;
  }
  
  &__select {
    @include input();
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right spacing(2) center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: spacing(10);
    appearance: none;
  }
  
  &__help {
    font-size: font-size(sm);
    color: theme-color(gray, 600);
    margin-top: spacing(1);
    
    .dark & {
      color: theme-color(gray, 400);
    }
  }
  
  &__error {
    font-size: font-size(sm);
    color: theme-color(error, 600);
    margin-top: spacing(1);
    
    .dark & {
      color: theme-color(error, 400);
    }
  }
  
  &__success {
    font-size: font-size(sm);
    color: theme-color(success, 600);
    margin-top: spacing(1);
    
    .dark & {
      color: theme-color(success, 400);
    }
  }
}

// 复选框和单选框
.checkbox,
.radio {
  display: flex;
  align-items: flex-start;
  gap: spacing(2);
  
  &__input {
    width: 16px;
    height: 16px;
    margin: 0;
    background-color: white;
    cursor: pointer;
    
    &:checked {
      background-color: theme-color(primary, 600);
      border-color: theme-color(primary, 600);
    }
    
    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .dark & {
      background-color: theme-color(gray, 800);
      border-color: theme-color(gray, 600);
      
      &:checked {
        background-color: theme-color(primary, 600);
        border-color: theme-color(primary, 600);
      }
    }
  }
  
  &__label {
    font-size: font-size(sm);
    color: theme-color(gray, 700);
    cursor: pointer;
    line-height: 1.25;
    
    .dark & {
      color: theme-color(gray, 300);
    }
  }
}

.checkbox {
  &__input {
    border-radius: map.get($border-radius, sm);
  }
}

.radio {
  &__input {
    border-radius: 50%;
  }
}

// 开关
.switch {
  display: flex;
  align-items: center;
  gap: spacing(2);
  
  &__input {
    position: relative;
    width: 44px;
    height: 24px;
    background-color: theme-color(gray, 200);
    border-radius: map.get($border-radius, full);
    border: none;
    cursor: pointer;
    transition: map.get($transition, all);
    
    &::before {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      width: 20px;
      height: 20px;
      background-color: white;
      border-radius: 50%;
      transition: map.get($transition, transform);
    }
    
    &:checked {
      background-color: theme-color(primary, 600);
      
      &::before {
        transform: translateX(20px);
      }
    }
    
    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .dark & {
      background-color: theme-color(gray, 700);
      
      &::before {
        background-color: theme-color(gray, 300);
      }
      
      &:checked {
        background-color: theme-color(primary, 600);
        
        &::before {
          background-color: white;
        }
      }
    }
  }
  
  &__label {
    font-size: font-size(sm);
    color: theme-color(gray, 700);
    cursor: pointer;
    
    .dark & {
      color: theme-color(gray, 300);
    }
  }
}

// 文件上传
.file-upload {
  position: relative;
  display: inline-block;
  
  &__input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
  
  &__button {
    @include button($variant: outline);
    position: relative;
    overflow: hidden;
  }
  
  &__dropzone {
    border: 2px dashed theme-color(gray, 300);
    border-radius: map.get($border-radius, lg);
    padding: spacing(8);
    text-align: center;
    transition: map.get($transition, colors);
    
    &:hover,
    &--dragover {
      border-color: theme-color(primary, 500);
      background-color: theme-color(primary, 50);
    }
    
    .dark & {
      border-color: theme-color(gray, 600);
      
      &:hover,
      &--dragover {
        border-color: theme-color(primary, 400);
        background-color: theme-color(primary, 900);
      }
    }
  }
  
  &__icon {
    width: 48px;
    height: 48px;
    margin: 0 auto spacing(4);
    color: theme-color(gray, 400);
    
    .dark & {
      color: theme-color(gray, 500);
    }
  }
  
  &__text {
    color: theme-color(gray, 600);
    margin-bottom: spacing(2);
    
    .dark & {
      color: theme-color(gray, 400);
    }
  }
  
  &__hint {
    font-size: font-size(sm);
    color: theme-color(gray, 500);
    
    .dark & {
      color: theme-color(gray, 500);
    }
  }
}

// 表单布局
.form-grid {
  display: grid;
  gap: spacing(4);
  
  &--2 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  &--3 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  @include respond-to(md) {
    gap: spacing(6);
  }
}

.form-row {
  display: flex;
  gap: spacing(4);
  
  .form__group {
    flex: 1;
  }
  
  @include respond-to-max(md) {
    flex-direction: column;
    gap: spacing(2);
  }
}
