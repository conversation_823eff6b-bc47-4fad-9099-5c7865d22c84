// ProMax 按钮组件样式
@use '../utils/variables' as *;
@use '../utils/mixins' as *;
@use 'sass:map';

.btn {
  @include button();
  
  // 按钮尺寸变体
  &--sm {
    @include button($size: sm);
  }
  
  &--lg {
    @include button($size: lg);
  }
  
  // 按钮样式变体
  &--primary {
    @include button($variant: primary);
  }
  
  &--secondary {
    @include button($variant: secondary);
  }
  
  &--outline {
    @include button($variant: outline);
  }
  
  &--success {
    background-color: theme-color(success, 600);
    color: white;

    &:hover {
      background-color: theme-color(success, 700);
    }

    &:focus {
      box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.5);
    }
  }

  &--warning {
    background-color: theme-color(warning, 600);
    color: white;

    &:hover {
      background-color: theme-color(warning, 700);
    }

    &:focus {
      box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.5);
    }
  }

  &--error {
    background-color: theme-color(error, 600);
    color: white;

    &:hover {
      background-color: theme-color(error, 700);
    }

    &:focus {
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.5);
    }
  }
  
  // 按钮状态
  &--loading {
    position: relative;
    color: transparent;
    
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 16px;
      height: 16px;
      margin: -8px 0 0 -8px;
      border: 2px solid currentColor;
      border-radius: 50%;
      border-top-color: transparent;
      animation: spin 1s linear infinite;
    }
  }
  
  &:disabled,
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  // 按钮组
  &-group {
    display: inline-flex;
    
    .btn {
      border-radius: 0;
      
      &:first-child {
        border-top-left-radius: map.get($border-radius, md);
        border-bottom-left-radius: map.get($border-radius, md);
      }
      
      &:last-child {
        border-top-right-radius: map.get($border-radius, md);
        border-bottom-right-radius: map.get($border-radius, md);
      }
      
      &:not(:first-child) {
        margin-left: -1px;
      }
    }
  }
  
  // 图标按钮
  &--icon {
    padding: spacing(2);
    
    &.btn--sm {
      padding: spacing(1);
    }
    
    &.btn--lg {
      padding: spacing(3);
    }
  }
  
  // 全宽按钮
  &--full {
    width: 100%;
  }
}

// 浮动操作按钮
.fab {
  position: fixed;
  bottom: spacing(6);
  right: spacing(6);
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-color: theme-color(primary, 600);
  color: white;
  border: none;
  cursor: pointer;
  box-shadow: shadow(lg);
  transition: map.get($transition, all);
  z-index: map.get($z-index, 50);
  
  @include center-flex();
  
  &:hover {
    background-color: theme-color(primary, 700);
    box-shadow: shadow(xl);
    transform: scale(1.05);
  }
  
  &:focus {
    outline: none;
    box-shadow: shadow(xl), 0 0 0 3px rgba(59, 130, 246, 0.5);
  }
  
  @include respond-to(md) {
    bottom: spacing(8);
    right: spacing(8);
  }
}

// 按钮动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
