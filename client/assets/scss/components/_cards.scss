// ProMax 卡片组件样式
@use '../utils/variables' as *;
@use '../utils/mixins' as *;
@use 'sass:map';

.card {
  @include card();
  
  // 卡片变体
  &--sm {
    @include card($padding: 4);
  }
  
  &--lg {
    @include card($padding: 8);
  }
  
  &--shadow-sm {
    @include card($shadow: sm);
  }
  
  &--shadow-lg {
    @include card($shadow: lg);
  }
  
  &--shadow-xl {
    @include card($shadow: xl);
  }
  
  &--no-shadow {
    @include card($shadow: none);
    border: 1px solid theme-color(gray, 200);
    
    .dark & {
      border-color: theme-color(gray, 700);
    }
  }
  
  // 卡片头部
  &__header {
    padding: spacing(4) spacing(6) spacing(3);
    border-bottom: 1px solid theme-color(gray, 200);
    background-color: theme-color(gray, 50);
    border-radius: map.get($border-radius, lg) map.get($border-radius, lg) 0 0;
    
    .dark & {
      background-color: theme-color(gray, 800);
      border-bottom-color: theme-color(gray, 700);
    }
    
    &:first-child {
      margin-top: -(spacing(6));
      margin-left: -(spacing(6));
      margin-right: -(spacing(6));
      margin-bottom: spacing(6);
    }
  }
  
  // 卡片标题
  &__title {
    font-size: font-size(lg);
    font-weight: map.get($font-weight, semibold);
    color: theme-color(gray, 900);
    margin: 0;
    
    .dark & {
      color: theme-color(gray, 100);
    }
  }
  
  // 卡片副标题
  &__subtitle {
    font-size: font-size(sm);
    color: theme-color(gray, 600);
    margin: spacing(1) 0 0;
    
    .dark & {
      color: theme-color(gray, 400);
    }
  }
  
  // 卡片内容
  &__body {
    padding: 0;
    
    &:not(:first-child) {
      padding-top: spacing(6);
    }
    
    &:not(:last-child) {
      padding-bottom: spacing(6);
    }
  }
  
  // 卡片底部
  &__footer {
    padding: spacing(3) spacing(6) spacing(4);
    border-top: 1px solid theme-color(gray, 200);
    background-color: theme-color(gray, 50);
    border-radius: 0 0 map.get($border-radius, lg) map.get($border-radius, lg);
    
    .dark & {
      background-color: theme-color(gray, 800);
      border-top-color: theme-color(gray, 700);
    }
    
    &:last-child {
      margin-bottom: -(spacing(6));
      margin-left: -(spacing(6));
      margin-right: -(spacing(6));
      margin-top: spacing(6);
    }
  }
  
  // 卡片操作区域
  &__actions {
    display: flex;
    gap: spacing(2);
    align-items: center;
    
    &--right {
      justify-content: flex-end;
    }
    
    &--center {
      justify-content: center;
    }
    
    &--between {
      justify-content: space-between;
    }
  }
  
  // 可点击卡片
  &--clickable {
    cursor: pointer;
    transition: map.get($transition, all);
    
    &:hover {
      box-shadow: shadow(md);
      transform: translateY(-2px);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  // 卡片状态
  &--success {
    border-left: 4px solid theme-color(success, 500);
  }
  
  &--warning {
    border-left: 4px solid theme-color(warning, 500);
  }
  
  &--error {
    border-left: 4px solid theme-color(error, 500);
  }
  
  &--info {
    border-left: 4px solid theme-color(info, 500);
  }
}

// 卡片网格
.card-grid {
  display: grid;
  gap: spacing(6);
  
  &--1 {
    grid-template-columns: 1fr;
  }
  
  &--2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
  
  &--3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  &--4 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  @include respond-to(md) {
    gap: spacing(8);
  }
}

// 统计卡片
.stat-card {
  @include card();
  text-align: center;
  
  &__icon {
    width: 48px;
    height: 48px;
    margin: 0 auto spacing(4);
    border-radius: 50%;
    @include center-flex();
    
    &--primary {
      background-color: theme-color(primary, 100);
      color: theme-color(primary, 600);
      
      .dark & {
        background-color: theme-color(primary, 900);
        color: theme-color(primary, 400);
      }
    }
    
    &--success {
      background-color: theme-color(success, 100);
      color: theme-color(success, 600);
      
      .dark & {
        background-color: theme-color(success, 900);
        color: theme-color(success, 400);
      }
    }
    
    &--warning {
      background-color: theme-color(warning, 100);
      color: theme-color(warning, 600);
      
      .dark & {
        background-color: theme-color(warning, 900);
        color: theme-color(warning, 400);
      }
    }
    
    &--error {
      background-color: theme-color(error, 100);
      color: theme-color(error, 600);
      
      .dark & {
        background-color: theme-color(error, 900);
        color: theme-color(error, 400);
      }
    }
  }
  
  &__value {
    font-size: font-size(3xl);
    font-weight: map.get($font-weight, bold);
    color: theme-color(gray, 900);
    margin-bottom: spacing(2);
    
    .dark & {
      color: theme-color(gray, 100);
    }
  }
  
  &__label {
    font-size: font-size(sm);
    color: theme-color(gray, 600);
    
    .dark & {
      color: theme-color(gray, 400);
    }
  }
  
  &__change {
    font-size: font-size(sm);
    font-weight: map.get($font-weight, medium);
    margin-top: spacing(2);
    
    &--positive {
      color: theme-color(success, 600);
    }
    
    &--negative {
      color: theme-color(error, 600);
    }
    
    &--neutral {
      color: theme-color(gray, 600);
    }
  }
}
