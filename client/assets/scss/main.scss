// ProMax 主样式文件

// 1. 导入变量和混合器
@use 'utils/variables' as *;
@use 'utils/mixins' as *;
@use 'sass:map';

// 2. 导入基础样式
@use 'base/reset';
@use 'base/scrollbar';
@use 'base/animations';

// 3. 导入组件样式
@use 'components/buttons';
@use 'components/cards';
@use 'components/forms';

// 4. 导入页面样式
@use 'pages/conformance';
@use 'pages/compare';

// 5. 导入工具类
@use 'utils/utilities';

// 6. Element Plus 样式覆盖
.el-card {
  box-shadow: shadow(sm);
  border: 1px solid theme-color(gray, 200);

  .dark & {
    border-color: theme-color(gray, 700);
    background-color: theme-color(gray, 800);
  }

  .el-card__header {
    background-color: theme-color(gray, 50);
    border-bottom: 1px solid theme-color(gray, 200);

    .dark & {
      background-color: theme-color(gray, 800);
      border-bottom-color: theme-color(gray, 700);
    }
  }

  .el-card__body {
    background-color: white;

    .dark & {
      background-color: theme-color(gray, 900);
    }
  }
}

.el-button {
  transition: map.get($transition, all);

  &.el-button--primary {
    background-color: theme-color(primary, 600);
    border-color: theme-color(primary, 600);

    &:hover {
      background-color: theme-color(primary, 700);
      border-color: theme-color(primary, 700);
    }
  }
}

.el-input {
  .el-input__wrapper {
    transition: map.get($transition, all);
    border-radius: map.get($border-radius, md);

    &:hover {
      border-color: theme-color(gray, 400);
    }

    &.is-focus {
      border-color: theme-color(primary, 500);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

// 7. 自定义样式

// 流程图样式
.process-diagram {
  background-color: white;
  border-radius: map.get($border-radius, lg);
  box-shadow: shadow(md);

  .dark & {
    background-color: theme-color(gray, 800);
  }
}

// 数据表格样式
.data-table {
  border-collapse: collapse;
  width: 100%;
  
  th,
  td {
    padding: spacing(3);
    text-align: left;
    border-bottom: 1px solid theme-color(gray, 200);
    
    .dark & {
      border-bottom-color: theme-color(gray, 700);
    }
  }
  
  th {
    font-weight: map.get($font-weight, semibold);
    color: theme-color(gray, 700);
    background-color: theme-color(gray, 50);
    
    .dark & {
      color: theme-color(gray, 300);
      background-color: theme-color(gray, 800);
    }
  }
  
  tbody tr {
    &:hover {
      background-color: theme-color(gray, 50);
      
      .dark & {
        background-color: theme-color(gray, 800);
      }
    }
  }
}

// 状态指示器
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: spacing(2);
  
  &--draft {
    background-color: theme-color(gray, 400);
  }
  
  &--active {
    background-color: theme-color(success, 500);
  }
  
  &--completed {
    background-color: theme-color(primary, 500);
  }
  
  &--archived {
    background-color: theme-color(warning, 500);
  }
  
  &--error {
    background-color: theme-color(error, 500);
  }
}

// 徽章样式
.badge {
  display: inline-flex;
  align-items: center;
  padding: spacing(1) spacing(2);
  font-size: font-size(xs);
  font-weight: map.get($font-weight, medium);
  border-radius: map.get($border-radius, full);
  
  &--primary {
    background-color: theme-color(primary, 100);
    color: theme-color(primary, 800);

    .dark & {
      background-color: theme-color(primary, 900);
      color: theme-color(primary, 200);
    }
  }

  &--success {
    background-color: theme-color(success, 100);
    color: theme-color(success, 800);

    .dark & {
      background-color: theme-color(success, 900);
      color: theme-color(success, 200);
    }
  }

  &--warning {
    background-color: theme-color(warning, 100);
    color: theme-color(warning, 800);

    .dark & {
      background-color: theme-color(warning, 900);
      color: theme-color(warning, 200);
    }
  }

  &--error {
    background-color: theme-color(error, 100);
    color: theme-color(error, 800);

    .dark & {
      background-color: theme-color(error, 900);
      color: theme-color(error, 200);
    }
  }

  &--gray {
    background-color: theme-color(gray, 100);
    color: theme-color(gray, 800);

    .dark & {
      background-color: theme-color(gray, 800);
      color: theme-color(gray, 200);
    }
  }
}

// 分隔线
.divider {
  height: 1px;
  background-color: theme-color(gray, 200);
  border: none;
  margin: spacing(4) 0;

  .dark & {
    background-color: theme-color(gray, 700);
  }

  &--vertical {
    width: 1px;
    height: auto;
    margin: 0 spacing(4);
  }
}

// 工具提示
.tooltip {
  position: relative;

  &__content {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: spacing(2) spacing(3);
    background-color: theme-color(gray, 900);
    color: white;
    font-size: font-size(sm);
    border-radius: map.get($border-radius, md);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: map.get($transition, all);
    z-index: map.get($z-index, tooltip);

    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 4px solid transparent;
      border-top-color: theme-color(gray, 900);
    }

    .dark & {
      background-color: theme-color(gray, 100);
      color: theme-color(gray, 900);

      &::after {
        border-top-color: theme-color(gray, 100);
      }
    }
  }

  &:hover &__content {
    opacity: 1;
    visibility: visible;
  }
}

// 响应式隐藏类
@include respond-to-max(sm) {
  .hidden-sm { display: none !important; }
}

@include respond-to-max(md) {
  .hidden-md { display: none !important; }
}

@include respond-to-max(lg) {
  .hidden-lg { display: none !important; }
}

@include respond-to-max(xl) {
  .hidden-xl { display: none !important; }
}

@include respond-to(sm) {
  .visible-sm { display: block !important; }
}

@include respond-to(md) {
  .visible-md { display: block !important; }
}

@include respond-to(lg) {
  .visible-lg { display: block !important; }
}

@include respond-to(xl) {
  .visible-xl { display: block !important; }
}
