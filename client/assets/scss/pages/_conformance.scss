// 符合性检查页面专用样式
@use '../utils/variables' as *;
@use '../utils/mixins' as *;
@use 'sass:map';

// 符合性检查通用样式
.conformance {
  // 页面容器
  &-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 2rem 1rem;

    .dark & {
      background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    }

    @include respond-to(md) {
      padding: 2rem 2rem;
    }
  }

  // 页面标题区域
  &-header {
    max-width: 1400px;
    margin: 0 auto 2rem auto;
    animation: fadeInUp 0.6s ease-out;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 2rem;

      @include respond-to-max(md) {
        flex-direction: column;
        align-items: stretch;
      }
    }

    .header-title {
      display: flex;
      align-items: center;
      gap: 1rem;

      .back-button {
        color: theme-color(gray, 600);
        transition: map.get($transition, all);
        border-radius: 50%;
        width: 40px;
        height: 40px;

        &:hover {
          color: theme-color(primary, 600);
          background-color: rgba(59, 130, 246, 0.1);
          transform: translateX(-2px);
        }

        .dark & {
          color: theme-color(gray, 400);

          &:hover {
            color: theme-color(primary, 400);
          }
        }
      }

      .title {
        font-size: font-size(3xl);
        font-weight: map.get($font-weight, bold);
        color: theme-color(gray, 900);
        margin: 0 0 spacing(1) 0;
        background: linear-gradient(135deg, theme-color(primary, 600), theme-color(primary, 800));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;

        .dark & {
          background: linear-gradient(135deg, theme-color(primary, 400), theme-color(primary, 200));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
      }

      .description {
        font-size: font-size(base);
        color: theme-color(gray, 600);
        margin: 0;

        .dark & {
          color: theme-color(gray, 400);
        }
      }
    }

    .header-actions {
      display: flex;
      gap: 0.5rem;
      flex-shrink: 0;

      @include respond-to-max(md) {
        width: 100%;
        justify-content: flex-end;
      }
    }
  }

  // 内容区域
  &-content {
    max-width: 1400px;
    margin: 0 auto;
    animation: fadeInUp 0.6s ease-out 0.1s both;

    &.grid {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }
  }

  // 统计卡片网格
  &-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;

    .stat-card {
      @include card(md, lg, 6);
      transition: map.get($transition, all);

      &:hover {
        transform: translateY(-2px);
        box-shadow: shadow(lg);
      }

      .stat-content {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .stat-icon {
        width: 3rem;
        height: 3rem;
        border-radius: map.get($border-radius, lg);
        @include center-flex;
        font-size: 1.5rem;

        &--primary {
          background-color: theme-color(primary, 100);
          color: theme-color(primary, 600);

          .dark & {
            background-color: theme-color(primary, 900);
            color: theme-color(primary, 400);
          }
        }

        &--success {
          background-color: theme-color(success, 100);
          color: theme-color(success, 600);

          .dark & {
            background-color: theme-color(success, 900);
            color: theme-color(success, 400);
          }
        }

        &--warning {
          background-color: theme-color(warning, 100);
          color: theme-color(warning, 600);

          .dark & {
            background-color: theme-color(warning, 900);
            color: theme-color(warning, 400);
          }
        }

        &--info {
          background-color: theme-color(info, 100);
          color: theme-color(info, 600);

          .dark & {
            background-color: theme-color(info, 900);
            color: theme-color(info, 400);
          }
        }
      }

      .stat-info {
        .stat-value {
          font-size: font-size(2xl);
          font-weight: map.get($font-weight, bold);
          color: theme-color(gray, 900);
          line-height: 1.2;

          .dark & {
            color: theme-color(gray, 100);
          }
        }

        .stat-label {
          font-size: font-size(sm);
          color: theme-color(gray, 600);
          margin-top: spacing(1);

          .dark & {
            color: theme-color(gray, 400);
          }
        }
      }
    }
  }

  // 快速操作区域
  &-actions {
    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1rem;

      .action-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1.5rem;
        border: 1px solid theme-color(gray, 200);
        border-radius: map.get($border-radius, lg);
        background-color: white;
        cursor: pointer;
        transition: map.get($transition, all);

        &:hover {
          border-color: theme-color(primary, 300);
          box-shadow: shadow(sm);
          transform: translateY(-1px);
        }

        .dark & {
          background-color: theme-color(gray, 800);
          border-color: theme-color(gray, 700);

          &:hover {
            border-color: theme-color(primary, 600);
          }
        }

        .action-icon {
          width: 3rem;
          height: 3rem;
          border-radius: map.get($border-radius, lg);
          background-color: theme-color(primary, 100);
          color: theme-color(primary, 600);
          @include center-flex;
          font-size: 1.5rem;
          flex-shrink: 0;

          .dark & {
            background-color: theme-color(primary, 900);
            color: theme-color(primary, 400);
          }
        }

        .action-content {
          flex: 1;

          .action-title {
            font-size: font-size(base);
            font-weight: map.get($font-weight, semibold);
            color: theme-color(gray, 900);
            margin: 0 0 0.25rem 0;

            .dark & {
              color: theme-color(gray, 100);
            }
          }

          .action-description {
            font-size: font-size(sm);
            color: theme-color(gray, 600);
            margin: 0;

            .dark & {
              color: theme-color(gray, 400);
            }
          }
        }

        .action-arrow {
          color: theme-color(gray, 400);
          font-size: 1.25rem;
          transition: map.get($transition, all);

          .dark & {
            color: theme-color(gray, 600);
          }
        }

        &:hover .action-arrow {
          color: theme-color(primary, 600);
          transform: translateX(2px);

          .dark & {
            color: theme-color(primary, 400);
          }
        }
      }
    }
  }

  // 符合性等级标签样式
  &-level {
    &--excellent {
      background-color: theme-color(success, 100);
      color: theme-color(success, 800);
      border-color: theme-color(success, 200);

      .dark & {
        background-color: theme-color(success, 900);
        color: theme-color(success, 200);
        border-color: theme-color(success, 700);
      }
    }

    &--good {
      background-color: theme-color(success, 100);
      color: theme-color(success, 700);
      border-color: theme-color(success, 200);

      .dark & {
        background-color: theme-color(success, 900);
        color: theme-color(success, 300);
        border-color: theme-color(success, 700);
      }
    }

    &--fair {
      background-color: theme-color(warning, 100);
      color: theme-color(warning, 800);
      border-color: theme-color(warning, 200);

      .dark & {
        background-color: theme-color(warning, 900);
        color: theme-color(warning, 200);
        border-color: theme-color(warning, 700);
      }
    }

    &--poor {
      background-color: theme-color(error, 100);
      color: theme-color(error, 800);
      border-color: theme-color(error, 200);

      .dark & {
        background-color: theme-color(error, 900);
        color: theme-color(error, 200);
        border-color: theme-color(error, 700);
      }
    }
  }

  // 偏差类型标签样式
  &-deviation {
    &--missing_activity {
      background-color: theme-color(error, 100);
      color: theme-color(error, 700);

      .dark & {
        background-color: theme-color(error, 900);
        color: theme-color(error, 300);
      }
    }

    &--extra_activity {
      background-color: theme-color(warning, 100);
      color: theme-color(warning, 700);

      .dark & {
        background-color: theme-color(warning, 900);
        color: theme-color(warning, 300);
      }
    }

    &--wrong_order {
      background-color: theme-color(info, 100);
      color: theme-color(info, 700);

      .dark & {
        background-color: theme-color(info, 900);
        color: theme-color(info, 300);
      }
    }

    &--skipped_activity {
      background-color: theme-color(purple, 100);
      color: theme-color(purple, 700);

      .dark & {
        background-color: theme-color(purple, 900);
        color: theme-color(purple, 300);
      }
    }

    &--repeated_activity {
      background-color: theme-color(pink, 100);
      color: theme-color(pink, 700);

      .dark & {
        background-color: theme-color(pink, 900);
        color: theme-color(pink, 300);
      }
    }

    &--timing_violation {
      background-color: theme-color(orange, 100);
      color: theme-color(orange, 700);

      .dark & {
        background-color: theme-color(orange, 900);
        color: theme-color(orange, 300);
      }
    }
  }
}

// 动画定义
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 响应式断点
@include respond-to-max(sm) {
  .conformance {
    &-page {
      padding: 1rem 0.5rem;
    }

    &-header {
      .header-title .title {
        font-size: font-size(2xl);
      }
    }

    &-stats {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    &-actions .actions-grid {
      grid-template-columns: 1fr;
    }
  }
}
