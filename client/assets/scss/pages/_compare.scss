// 流程比对分析页面样式

@use 'sass:color';
@use '../utils/variables' as *;
@use '../utils/mixins' as *;

.compare-page {
  // 基础页面样式
  min-height: 1200px;
  position: relative;
  display: flex;
  flex-direction: column;
  background: theme-color(gray, 50);

  .dark & {
    background: theme-color(gray, 900);
  }

  // 页面头部
  .page-header {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    padding: 1rem 2rem;
    flex-shrink: 0;
    position: relative;
    z-index: 100;

    .dark & {
      background: rgba(31, 41, 55, 0.98);
      border-bottom-color: rgba(255, 255, 255, 0.1);
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
    }
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1920px;
    margin: 0 auto;
  }

  .header-left {
    .page-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0 0 0.25rem 0;
      color: theme-color(gray, 900);

      .dark & {
        color: theme-color(gray, 100);
      }
    }

    .breadcrumb {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.875rem;
      color: theme-color(gray, 600);

      .dark & {
        color: theme-color(gray, 400);
      }

      .el-icon {
        font-size: 0.75rem;
      }
    }
  }

  // 导航标签
  .nav-tabs {
    display: flex;
    gap: 0.5rem;
  }

  .nav-tab {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    color: theme-color(gray, 600);
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.04);
      color: theme-color(gray, 900);
    }

    &--active {
      background: theme-color(primary, 600);
      color: white;

      &:hover {
        background: theme-color(primary, 600);
        color: white;
      }
    }

    .dark & {
      color: theme-color(gray, 400);

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: theme-color(gray, 100);
      }

      &--active {
        background: theme-color(primary, 600);
        color: white;
      }
    }
  }

  // 加载状态
  .loading-container {
    flex: 1;
    padding: 2rem;
    max-width: 1920px;
    margin: 0 auto;
    width: 100%;
  }

  // 主布局
  .compare-results {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .main-layout {
    flex:1;
    display: flex;
    height: 100%;
    max-width: 1920px;
    margin: 0 auto;
  }

  // 筛选器面板
  .filter-panel-container {
    display: flex;
    flex-shrink: 0;
    position: relative;
  }

  .filter-panel {
    width: 350px;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(0, 0, 0, 0.06);
    padding: 1rem;
    overflow-y: auto;
    transition: all 0.3s ease;
    position: relative;

    &.collapsed {
      width: 0;
      padding: 0;
      overflow: hidden;
    }

    .dark & {
      background: rgba(31, 41, 55, 0.98);
      border-right-color: rgba(255, 255, 255, 0.1);
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      width: 1px;
      background: linear-gradient(180deg, transparent, rgba(0, 0, 0, 0.1), transparent);

      .dark & {
        background: linear-gradient(180deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      }
    }
  }

  .filter-toggle-button {
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background: theme-color(primary, 600);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
    z-index: 10;

    &:hover {
      transform: translateY(-50%) scale(1.1);
    }
  }

  // 内容区域
  .content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.3s ease;

    &.full-width {
      margin-left: 0;
    }
  }

  // 时间控制区域 - 优化为更紧凑整齐的布局
  .time-control-section {
    flex-shrink: 0;
    padding: 0.5rem 1rem;

    .time-control-card {
      border-radius: 8px;
      border: 1px solid rgba(0, 0, 0, 0.06);
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);

      .dark & {
        border-color: rgba(255, 255, 255, 0.1);
        background: rgba(31, 41, 55, 0.98);
      }
    }

    .time-control-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 0.75rem 1rem;
    }

    .time-control-item {
      flex: 1;

      .control-header {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        margin-bottom: 0.5rem;

        .control-icon {
          color: theme-color(primary, 600);
          font-size: 0.875rem;
        }

        .control-title {
          font-size: 0.8125rem;
          font-weight: 600;
          color: theme-color(gray, 800);

          .dark & {
            color: theme-color(gray, 200);
          }
        }
      }
    }

    .time-control-divider {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      background: theme-color(primary, 50);
      border-radius: 50%;
      color: theme-color(primary, 600);
      flex-shrink: 0;

      .dark & {
        background: theme-color(primary, 900);
        color: theme-color(primary, 400);
      }

      .el-icon {
        font-size: 0.75rem;
      }
    }
  }

  // 比对布局
  .compare-layout {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    padding: 1rem;
    overflow: hidden;
    position: relative;
    min-height: 700px; // 确保有足够的高度

    // 添加分割线
    &::after {
      content: '';
      position: absolute;
      top: 1rem;
      bottom: 1rem;
      left: 50%;
      width: 1px;
      background: linear-gradient(180deg, transparent, rgba(0, 0, 0, 0.1), transparent);
      transform: translateX(-50%);
      z-index: 1;
    }

    .dark &::after {
      background: linear-gradient(180deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    }
  }

  .dfg-section {
    display: flex;
    flex-direction: column;
    overflow: hidden;

    &.left-dfg {
      .dfg-title {
        color: theme-color(success, 600);
      }
    }

    &.right-dfg {
      .dfg-title {
        color: theme-color(warning, 600);
      }
    }
  }

  // DFG 卡片
  .dfg-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.06);
    box-shadow: none;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: none;
      // box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
      // transform: translateY(-2px);
    }

    .el-card__body {
      flex: 1 !important;
      display: flex !important;
      flex-direction: column !important;
      overflow: hidden !important;
      padding: 0 !important;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      z-index: 1;
      border-radius: 16px 16px 0 0;
    }

    .left-dfg &::before {
      background: linear-gradient(90deg, theme-color(success, 600), theme-color(success, 400));
    }

    .right-dfg &::before {
      background: linear-gradient(90deg, theme-color(warning, 600), theme-color(warning, 400));
    }

    .el-card__header {
      background: rgba(255, 255, 255, 0.98);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      padding: 1.5rem;

      .dark & {
        background: rgba(31, 41, 55, 0.98);
        border-bottom-color: rgba(255, 255, 255, 0.1);
      }
    }
  }

  .dfg-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;

    .dfg-title-section {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      flex: 1;

      .dfg-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        flex-shrink: 0;

        &.base-indicator {
          background: theme-color(success, 600);
          box-shadow: 0 0 0 3px rgba(theme-color(success, 600), 0.2);
        }

        &.compare-indicator {
          background: theme-color(warning, 600);
          box-shadow: 0 0 0 3px rgba(theme-color(warning, 600), 0.2);
        }
      }

      .dfg-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0;
        color: theme-color(gray, 800);

        .dark & {
          color: theme-color(gray, 200);
        }
      }

      .time-tag {
        font-size: 0.75rem;
        font-weight: 500;
        border-radius: 12px;
        padding: 0.25rem 0.75rem;
      }
    }

    .dfg-stats {
      display: flex;
      align-items: center;
      gap: 1rem;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.875rem;
        color: theme-color(gray, 600);

        .dark & {
          color: theme-color(gray, 400);
        }

        .el-icon {
          font-size: 1rem;
          color: theme-color(primary, 600);
        }
      }
    }
  }

  .dfg-content {
    flex: 1;
    position: relative;
    overflow: visible; // 允许图表内容溢出，确保交互正常
    height: 100%; // 确保内容区域填满容器
  }

  // DFG 容器
  .dfg-container {
    width: 100%;
    height: 100%;
    border-radius: 12px;
    overflow: visible; // 允许图表内容溢出，确保交互正常
    position: relative;
    background: rgba(248, 250, 252, 0.5);
    border: 1px solid rgba(0, 0, 0, 0.04);
    min-height: 600px; // 增加最小高度，确保图表有足够空间

    .dark & {
      background: rgba(15, 23, 42, 0.5);
      border-color: rgba(255, 255, 255, 0.08);
    }

    // 添加网格背景
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image:
        linear-gradient(rgba(0, 0, 0, 0.02) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 0, 0, 0.02) 1px, transparent 1px);
      background-size: 20px 20px;
      pointer-events: none;
      z-index: 0;

      .dark & {
        background-image:
          linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
          linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
      }
    }

    // 确保 GoJS 画布能够正常交互
    canvas {
      cursor: grab;

      &:active {
        cursor: grabbing;
      }
    }

    // GoJS 图表容器样式
    .go-diagram {
      width: 100% !important;
      height: 100% !important;
      position: relative;
      z-index: 1;
    }
  }

  .dfg-placeholder {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: theme-color(gray, 500);
    z-index: 1;

    .placeholder-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
      opacity: 0.5;
    }

    .placeholder-text {
      font-size: 1rem;
      margin: 0;
      opacity: 0.7;
    }

    .dark & {
      color: theme-color(gray, 400);
    }
  }

  .dfg-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;

    .dark & {
      background: rgba(31, 41, 55, 0.95);
    }

    .el-loading-spinner {
      .circular {
        stroke: theme-color(primary, 600);
      }
    }
  }

  // 控制面板 - 去掉卡片样式，更紧凑的布局
  .control-panel {
    flex-shrink: 0;
    padding: 0 1rem;

    .control-content {
      display: flex;
      align-items: center;
      gap: 2rem;
      padding: 0.75rem 1rem;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 8px;
      border: 1px solid rgba(0, 0, 0, 0.06);
      position: relative;

      .dark & {
        background: rgba(31, 41, 55, 0.95);
        border-color: rgba(255, 255, 255, 0.1);
      }

      // 添加装饰性渐变
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, theme-color(primary, 600), theme-color(success, 600), theme-color(warning, 600));
        border-radius: 8px 8px 0 0;
      }
    }

    .control-group {
      display: flex;
      align-items: center;
      gap: 1rem;

      .control-label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.75rem;
        font-weight: 500;
        color: theme-color(gray, 700);
        white-space: nowrap;

        .dark & {
          color: theme-color(gray, 300);
        }

        .el-icon {
          color: theme-color(primary, 600);
          font-size: 0.875rem;
        }
      }
    }

    .action-controls {
      display: flex;
      gap: 0.75rem;
      align-items: center;

      .el-button {
        .el-icon {
          margin-right: 0.25rem;
        }
      }
    }

    .status-info {
      display: flex;
      gap: 1.5rem;
      align-items: center;

      .status-item {
        display: flex;
        align-items: center;
        gap: 0.25rem;

        .status-label {
          font-size: 0.75rem;
          color: theme-color(gray, 600);

          .dark & {
            color: theme-color(gray, 400);
          }
        }

        .status-value {
          font-size: 0.75rem;
          font-weight: 600;

          &.status-increase {
            color: theme-color(success, 600);
          }

          &.status-decrease {
            color: theme-color(error, 600);
          }

          &.status-same {
            color: theme-color(info, 600);
          }
        }
      }
    }
  }

  .dimension-controls,
  .display-controls,
  .action-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .control-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: theme-color(gray, 600);
    white-space: nowrap;

    .dark & {
      color: theme-color(gray, 400);
    }
  }

  // 空状态
  .empty-state {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;

    .empty-description {
      text-align: center;
      max-width: 400px;

      h3 {
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 12px 0;
      }

      p {
        font-size: 14px;
        color: #6b7280;
        margin: 0 0 16px 0;
        line-height: 1.5;
      }

      .steps-list {
        text-align: left;
        font-size: 14px;
        color: #4b5563;
        margin: 0;
        padding-left: 20px;

        li {
          margin-bottom: 8px;
          line-height: 1.4;
        }
      }
    }
  }

  // 维度和显示控制增强
  .dimension-controls,
  .display-controls {
    position: relative;
    padding: 0.75rem 1rem;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.04);

    .dark & {
      background: rgba(255, 255, 255, 0.03);
      border-color: rgba(255, 255, 255, 0.08);
    }

    .control-label {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, theme-color(primary, 600), transparent);
        opacity: 0.3;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1400px) {
    .filter-panel {
      width: 300px;
    }

    .control-panel .control-content {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;

      .control-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
      }

      .action-controls {
        flex-direction: column;
        align-items: stretch;
      }

      .status-info {
        flex-direction: column;
        gap: 0.5rem;
      }
    }
  }

  @media (max-width: 1200px) {
    .compare-layout {
      grid-template-columns: 1fr;
      grid-template-rows: 1fr 1fr;
    }

    .time-control-content {
      flex-direction: column;
      gap: 0.5rem;
      padding: 0.5rem;

      .time-control-item {
        padding: 0.375rem;

        &:first-child::after {
          display: none;
        }

        // 在垂直布局时添加底部分隔线
        &:first-child::before {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0.375rem;
          right: 0.375rem;
          height: 1px;
          background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.08), transparent);

          .dark & {
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
          }
        }
      }

      .time-control-divider {
        position: relative;
        transform: rotate(90deg);
        width: 18px;
        height: 18px;
        align-self: center;
        margin: 0.125rem 0;
      }
    }

    .control-panel .control-content {
      flex-direction: column;
      gap: 1rem;
      padding: 1rem;
    }
  }

  @media (max-width: 768px) {
    .header-content {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;
    }

    .nav-tabs {
      align-self: stretch;
      justify-content: space-between;
    }

    .filter-panel {
      width: 280px;
    }

    .time-control-section {
      padding: 0.25rem 0.375rem;

      .time-control-content {
        padding: 0.375rem;
        gap: 0.375rem;
        flex-direction: column;

        .time-control-item {
          padding: 0.375rem;

          &:first-child::after {
            display: none;
          }

          &:first-child::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0.375rem;
            right: 0.375rem;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.08), transparent);

            .dark & {
              background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            }
          }

          .control-header {
            margin-bottom: 0.25rem;

            .control-title {
              font-size: 0.6875rem;
            }
          }
        }

        .time-control-divider {
          position: relative;
          transform: rotate(90deg);
          width: 16px;
          height: 16px;
          align-self: center;
          margin: 0.125rem 0;
        }
      }
    }

    .control-panel {
      padding: 0 0.5rem 0.5rem 0.5rem;

      .control-content {
        padding: 0.75rem;
        gap: 0.75rem;
      }
    }

    .control-panel .control-content {
      .control-group {
        gap: 0.5rem;
      }

      .action-controls {
        gap: 0.5rem;
      }

      .status-info {
        gap: 0.5rem;
      }
    }

    .dfg-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;

      .dfg-stats {
        align-self: stretch;
        justify-content: space-between;
      }
    }
  }

  // 操作按钮增强
  .action-controls {
    .el-button {
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
      }

      &:hover::before {
        left: 100%;
      }
    }
  }

  // 响应式增强（合并到上面的响应式样式中）
  @media (max-width: 1200px) {
    .compare-layout {
      &::after {
        display: none;
      }
    }

    .dfg-section {
      &.left-dfg::after {
        content: '';
        position: absolute;
        bottom: -0.5rem;
        left: 1rem;
        right: 1rem;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
        z-index: 1;

        .dark & {
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        }
      }
    }
  }

  // 动画增强
  .dfg-card,
  .control-panel {
    animation: slideInUp 0.6s ease-out;
  }

  .filter-panel {
    animation: slideInLeft 0.6s ease-out;
  }
}

// 动画定义
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 比对分析特有的颜色主题
.compare-theme {
  --base-color: #{theme-color(success, 600)};
  --compare-color: #{theme-color(warning, 600)};
  --neutral-color: #{theme-color(info, 600)};
  
  .base-indicator {
    color: var(--base-color);
    
    &::before {
      content: '●';
      margin-right: 0.25rem;
    }
  }
  
  .compare-indicator {
    color: var(--compare-color);
    
    &::before {
      content: '●';
      margin-right: 0.25rem;
    }
  }
  
  .neutral-indicator {
    color: var(--neutral-color);
    
    &::before {
      content: '●';
      margin-right: 0.25rem;
    }
  }
}
