// 页面容器
.discover-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;

  .dark & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @media (min-width: 768px) {
    padding: 2rem 2rem;
  }
}

// 页面标题
.page-header {
  margin: 0 auto 2rem auto;
  animation: fadeInDown 0.6s ease-out;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;

    @media (max-width: 1024px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
  }

  .header-left {
    flex: 1;
  }

  .header-title {
    display: flex;
    align-items: flex-start;
    gap: 1rem;

    .back-button {
      color: #64748b;
      font-size: 1.2rem;
      margin-top: 0.25rem;

      &:hover {
        background: rgba(59, 130, 246, 0.1);
        color: #3b82f6;
      }

      .dark & {
        color: #94a3b8;

        &:hover {
          background: rgba(96, 165, 250, 0.1);
          color: #60a5fa;
        }
      }
    }

    .title-section {
      flex: 1;

      .title {
        font-size: 2.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0 0 0.5rem 0;
        letter-spacing: -0.025em;

        @media (max-width: 768px) {
          font-size: 2rem;
        }

        .dark & {
          background: linear-gradient(135deg, #60a5fa, #a78bfa);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .description {
        color: #64748b;
        font-size: 1.125rem;
        font-weight: 500;
        margin: 0;
        opacity: 0.8;

        .dark & {
          color: #94a3b8;
        }
      }
    }
  }

  .header-tabs {
    flex-shrink: 0;

    @media (max-width: 1024px) {
      width: 100%;
    }

    .tabs-container {
      display: flex;
      gap: 0.5rem;
      background: rgba(255, 255, 255, 0.95);
      padding: 0.5rem;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);

      .dark & {
        background: rgba(30, 41, 59, 0.95);
        border: 1px solid rgba(71, 85, 105, 0.3);
      }

      @media (max-width: 1024px) {
        justify-content: center;
      }

      .tab-button {
        border-radius: 8px;
        font-weight: 600;
        padding: 8px 16px;
        transition: all 0.2s ease;
        border: none;
        font-size: 0.875rem;

        &.el-button--primary {
          background: linear-gradient(135deg, #3b82f6, #8b5cf6);
          color: white;
          box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
          }

          .dark & {
            background: linear-gradient(135deg, #60a5fa, #a78bfa);
          }
        }

        &.el-button--default {
          background: transparent;
          color: #6b7280;

          &:hover {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
          }

          .dark & {
            color: #9ca3af;

            &:hover {
              background: rgba(96, 165, 250, 0.1);
              color: #60a5fa;
            }
          }
        }
      }
    }
  }
}



// 控制面板
.control-panel {
  // max-width: 1400px;
  margin: 0 auto 1.5rem auto;
  animation: slideInLeft 0.5s ease-out 0.2s both;

  :deep(.el-card) {
    border-radius: 16px;
    border: none;
    box-shadow: none;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(0px);
      box-shadow: none;
    }

    .dark & {
      background: rgba(30, 41, 59, 0.95);
      border: 1px solid rgba(71, 85, 105, 0.3);
    }
  }

  .control-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;

    @media (min-width: 640px) {
      flex-direction: row;
      align-items: center;
    }

    .control-actions {
      display: flex;
      align-items: center;
      gap: 1rem;

      :deep(.el-button) {
        border-radius: 12px;
        font-weight: 600;
        padding: 10px 20px;
        transition: all 0.2s ease;

        &.el-button--primary {
          background: linear-gradient(135deg, #3b82f6, #8b5cf6);
          border: none;
          box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
          }

          .dark & {
            background: linear-gradient(135deg, #60a5fa, #a78bfa);
          }
        }

        &.el-button--warning {
          background: linear-gradient(135deg, #f59e0b, #f97316);
          border: none;
          box-shadow: 0 4px 14px 0 rgba(245, 158, 11, 0.3);

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px 0 rgba(245, 158, 11, 0.4);
          }
        }

        &:not(.el-button--primary):not(.el-button--warning) {
          border: 2px solid #e5e7eb;
          color: #6b7280;

          &:hover {
            border-color: #3b82f6;
            color: #3b82f6;
            transform: translateY(-1px);
          }

          .dark & {
            border-color: #4b5563;
            color: #9ca3af;

            &:hover {
              border-color: #60a5fa;
              color: #60a5fa;
            }
          }
        }
      }
    }

    .result-info {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .last-updated {
      font-size: 0.875rem;
      color: #6b7280;
      font-weight: 500;

      .dark & {
        color: #9ca3af;
      }
    }

    .cache-indicator {
      :deep(.el-tag) {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        border-radius: 8px;
        font-weight: 500;
      }
    }
  }
}

// 加载状态
.loading-state {
  text-align: center;
  padding: 4rem 1rem;
  // max-width: 1400px;
  margin: 0 auto;

  :deep(.el-skeleton) {
    .el-skeleton__item {
      background: rgba(226, 232, 240, 0.5);
      border-radius: 8px;

      .dark & {
        background: rgba(75, 85, 99, 0.5);
      }
    }
  }
}

.analyzing-state {
  text-align: center;
  padding: 4rem 1rem;
  // max-width: 1400px;
  margin: 0 auto;

  .analyzing-text {
    color: #64748b;
    font-size: 1.125rem;
    font-weight: 500;
    margin: 0;

    .dark & {
      color: #94a3b8;
    }
  }
}

// 加载动画
.loading-spinner {
  width: 2.5rem;
  height: 2.5rem;
  border: 3px solid rgba(59, 130, 246, 0.2);
  border-top-color: #3b82f6;
  border-radius: 50%;
  margin: 0 auto 1.5rem;
  animation: spin 1s linear infinite;

  .dark & {
    border-color: rgba(96, 165, 250, 0.2);
    border-top-color: #60a5fa;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 发现结果 - Grid全屏布局
.discovery-results {
  height: calc(100vh - 50px);
  min-height: 1200px;
  overflow: hidden;
  // max-width: 1400px;
  margin: 0 auto;
  animation: slideInUp 0.5s ease-out 0.4s both;
}

// Grid布局容器
.grid-layout {
  display: grid;
  grid-template-columns: 3fr 1fr 1fr;
  grid-template-rows: 1fr 2fr 2fr 2fr;
  gap: 1rem;
  height: 100%;
  grid-template-areas:
    "dfg statistics statistics"
    "dfg activity-pie frequency-pie"
    "dfg bar-chart bar-chart"
    "dfg table table";
  transition: all 0.3s ease;

  // 右侧面板区域
  .right-panel-area {
    display: contents;
  }

  // 右侧面板折叠状态
  &.right-collapsed {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
    grid-template-areas: "dfg";

    .statistics-section,
    .activity-pie-section,
    .frequency-pie-section,
    .pie-section,
    .bar-chart-section,
    .table-section {
      display: none;
    }
  }

  @media (max-width: 1400px) {
    grid-template-columns: 1fr 280px 280px;

    &.right-collapsed {
      grid-template-columns: 1fr;
      grid-template-areas: "dfg";
    }
  }

  @media (max-width: 1200px) {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr 1fr 1fr;
    grid-template-areas:
      "dfg statistics"
      "dfg activity-pie"
      "dfg frequency-pie"
      "dfg bar-chart"
      "dfg table";

    &.right-collapsed {
      grid-template-columns: 1fr;
      grid-template-rows: 1fr;
      grid-template-areas: "dfg";
    }
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    grid-template-rows: 300px 140px 200px 200px 250px 300px;
    grid-template-areas:
      "dfg"
      "statistics"
      "activity-pie"
      "frequency-pie"
      "bar-chart"
      "table";
    height: auto;
    gap: 1.5rem;

    &.right-collapsed {
      grid-template-rows: 300px;
      grid-template-areas: "dfg";
    }
  }

  // 统一卡片样式
  :deep(.el-card) {
    border-radius: 16px;
    border: none;
    box-shadow: none;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(0px);
      box-shadow: none;
    }

    .dark & {
      background: rgba(30, 41, 59, 0.95);
      border: 1px solid rgba(71, 85, 105, 0.3);
    }

    .el-card__header {
      border-bottom: 1px solid rgba(226, 232, 240, 0.5);

      .dark & {
        border-bottom-color: rgba(75, 85, 99, 0.5);
      }
    }
  }
}

// DFG区域
.dfg-section {
  grid-area: dfg;
  position: relative;
  overflow: visible;
  display: flex;
  flex-direction: column;
  min-height: 0;
  height: 100%;

  .dfg-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;

    :deep(.el-card__header) {
      flex-shrink: 0;
      padding: 1rem;
      border-bottom: 1px solid #e5e7eb;
    }

    :deep(.el-card__body) {
      flex: 1;
      padding: 0;
      display: flex;
      flex-direction: column;
      min-height: 0;
      overflow: hidden;
    }
  }
}

// 统计信息区域
.statistics-section {
  grid-area: statistics;
  position: relative;
  overflow: hidden;

  .statistics-card {
    height: 100%;

    :deep(.el-card__body) {
      height: calc(100% - 60px);
      overflow: hidden;
    }
  }
}

// 活动分布饼图区域
.activity-pie-section {
  grid-area: activity-pie;
  position: relative;
  overflow: hidden;
}

.pie-section {
  grid-area: activity-pie;
  position: relative;
  overflow: hidden;

  @media (min-width: 1201px) {
    grid-column: 2 / 4;
    grid-row: 2 / 3;
  }

  @media (max-width: 1200px) and (min-width: 769px) {
    grid-column: 2 / 3;
    grid-row: 2 / 4;
  }

  @media (max-width: 768px) {
    grid-column: 1 / 2;
    grid-row: 3 / 5;
  }

  .chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.5rem;
  }

  .chart-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
}


// 频率分布饼图区域
.frequency-pie-section {
  grid-area: frequency-pie;
  position: relative;
  overflow: hidden;
}

// 柱状图区域
.bar-chart-section {
  grid-area: bar-chart;
  position: relative;
  overflow: hidden;
}

// 表格区域
.table-section {
  grid-area: table;
  position: relative;
  overflow: hidden;
}



// 图表卡片 - Grid布局适配
.chart-card {
  height: 100%;
  display: flex;
  flex-direction: column;

  :deep(.el-card__header) {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
    flex-shrink: 0;
  }

  :deep(.el-card__body) {
    flex: 1;
    padding: 1rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .chart-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #111827;
    margin: 0;

    .dark & {
      color: #ffffff;
    }
  }

  .chart-container {
    flex: 1;
    overflow: hidden;
    min-height: 0;

    .chart {
      width: 100%;
      height: 100%;
    }

    .chart-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #6b7280;
      font-size: 0.875rem;
    }
  }

  // 表格卡片特殊处理
  &:has(.el-table) {
    .chart-container {
      overflow: visible;
    }

    :deep(.el-table) {
      height: 100%;
    }

    :deep(.el-table__body-wrapper) {
      max-height: calc(100% - 40px);
      overflow-y: auto;
    }
  }
}

// 统计信息
.statistics-title,
.frequency-title,
.dfg-title,
.activities-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;

  .dark & {
    color: #ffffff;
  }
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
  height: 100%;

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
}

.statistic-item {
  text-align: center;
  padding: 0.5rem;
  background: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 0;

  .statistic-value {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    line-height: 1.2;

    @media (max-width: 1400px) {
      font-size: 1.125rem;
    }

    @media (max-width: 768px) {
      font-size: 1.5rem;
    }

    &--blue {
      color: #2563eb;
    }

    &--green {
      color: #16a34a;
    }

    &--purple {
      color: #9333ea;
    }

    &--orange {
      color: #ea580c;
    }
  }

  .statistic-label {
    font-size: 0.75rem;
    color: #6b7280;
    line-height: 1.2;

    @media (max-width: 1400px) {
      font-size: 0.7rem;
    }

    @media (max-width: 768px) {
      font-size: 0.875rem;
    }
  }
}

// DFG 可视化
.dfg-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 80px;

  .dark & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    border-bottom-color: #374151;
  }

  @media (max-width: 768px) {
    padding: 1rem;
    min-height: 70px;
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .dfg-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;

    @media (max-width: 768px) {
      justify-content: center;
      gap: 0.75rem;
    }

    .dimension-controls {
      margin-right: 0.5rem;

      :deep(.el-radio-group) {
        .el-radio-button {
          .el-radio-button__inner {
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
            border: 1px solid #d1d5db;
            background: #ffffff;
            color: #374151;
            min-width: 60px;
            text-align: center;

            &:hover {
              border-color: #3b82f6;
              color: #3b82f6;
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
            }

            .dark & {
              background: #374151;
              border-color: #4b5563;
              color: #d1d5db;

              &:hover {
                border-color: #60a5fa;
                color: #60a5fa;
              }
            }
          }

          &.is-active .el-radio-button__inner {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            border-color: #3b82f6;
            color: #ffffff;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            transform: translateY(-1px);

            .dark & {
              background: linear-gradient(135deg, #60a5fa, #a78bfa);
              border-color: #60a5fa;
            }
          }

          &:first-child .el-radio-button__inner {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            border-right: none;
          }

          &:last-child .el-radio-button__inner {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
          }
        }
      }
    }

    .control-divider {
      width: 2px;
      height: 32px;
      background: linear-gradient(to bottom, #e5e7eb, #d1d5db);
      margin: 0 1rem;
      border-radius: 1px;

      .dark & {
        background: linear-gradient(to bottom, #4b5563, #374151);
      }
    }

    :deep(.el-button) {
      font-size: 0.875rem;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.2s ease;
      border: 1px solid #d1d5db;
      background: #ffffff;
      color: #374151;
      margin: 0 0.25rem;
      min-width: 80px;

      &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border-color: #3b82f6;
        color: #3b82f6;
      }

      &:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }

      .dark & {
        background: #374151;
        border-color: #4b5563;
        color: #d1d5db;

        &:hover:not(:disabled) {
          border-color: #60a5fa;
          color: #60a5fa;
        }
      }

      .el-icon {
        margin-right: 0.375rem;
        font-size: 1rem;
      }
    }

    .zoom-indicator {
      display: flex;
      align-items: center;
      padding: 0.5rem 1rem;
      background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 0.875rem;
      font-weight: 600;
      color: #374151;
      min-width: 60px;
      justify-content: center;
      margin-left: 0.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .dark & {
        background: linear-gradient(135deg, #374151, #1f2937);
        border-color: #4b5563;
        color: #d1d5db;
      }
    }
  }
}

.dfg-container {
  flex: 1;
  min-height: 400px;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;

  @media (max-width: 1200px) {
    min-height: 350px;
  }

  @media (max-width: 768px) {
    min-height: 300px;
  }

  .dfg-canvas {
    width: 100%;
    height: 100%;
    flex: 1;
    background: #f9fafb;
  }

  .dfg-empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6b7280;

    .dfg-empty-content {
      text-align: center;

      .dfg-empty-title {
        font-size: 1.125rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
      }

      .dfg-empty-text {
        font-size: 0.875rem;
      }
    }
  }

  .dfg-loading-overlay {
    position: absolute;
    inset: 0;
    background: rgba(255, 255, 255, 0.75);
    display: flex;
    align-items: center;
    justify-content: center;

    .dfg-loading-content {
      text-align: center;

      .dfg-loading-text {
        color: #6b7280;
        margin: 0;
      }
    }
  }
}

.dfg-info {
  flex-shrink: 0;
  padding: 0.75rem 1rem;
  background: #eff6ff;
  border-top: 1px solid #e5e7eb;
  cursor: pointer;

  &:hover {
    background: rgba(59, 130, 246, 0.06);

    .dfg-info-arrow {
      transform: translateX(2px);
    }
  }

  .dfg-info-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;

    .dfg-info-icon {
      color: #3b82f6;
      font-size: 1rem;
      flex-shrink: 0;
    }

    .dfg-info-text {
      font-size: 0.875rem;
      color: #1d4ed8;
      flex: 1;

      .dfg-info-title {
        font-weight: 500;
        margin: 0;
        font-size: 0.875rem;
      }
    }

    .dfg-info-arrow {
      color: #6b7280;
      font-size: 0.875rem;
      flex-shrink: 0;
      transition: transform 0.2s ease;
    }
  }
}

// 图表说明浮层样式
.chart-legend-content {
  .chart-legend-list {
    list-style: disc;
    list-style-position: inside;
    margin: 0;
    padding: 0;
    font-size: 0.875rem;
    line-height: 1.5;

    li {
      margin-bottom: 0.5rem;
      color: #374151;

      &:last-child {
        margin-bottom: 0;
      }

      strong {
        color: #1f2937;
        font-weight: 600;
      }
    }
  }
}

// 确保图表说明浮层覆盖整个页面
:deep(.el-dialog__wrapper) {
  z-index: 3000 !important;
}

:deep(.el-overlay) {
  z-index: 2999 !important;
}

// 活动网格
.activities-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;

  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.activity-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  border-radius: 0.25rem;

  &--start {
    background: #f0fdf4;
  }

  &--end {
    background: #fef2f2;
  }

  .activity-name {
    font-size: 0.875rem;
    font-weight: 500;
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 3rem 0;

  .empty-icon {
    width: 4rem;
    height: 4rem;
    color: #9ca3af;
    margin: 0 auto 1rem;
  }

  .empty-title {
    font-size: 1.125rem;
    font-weight: 500;
    color: #111827;
    margin: 0 0 0.5rem 0;

    .dark & {
      color: #ffffff;
    }
  }

  .empty-description {
    color: #6b7280;
    margin: 0 0 1.5rem 0;

    .dark & {
      color: #9ca3af;
    }
  }
}

// GoJS 图表容器样式
:deep(.go-diagram) {
  width: 100%;
  height: 100%;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

// 新的左右并排布局样式
.main-layout {
  display: flex;
  gap: spacing(4);
  height: 100%; // 减去页面头部和控制面板的高度
  min-height: 600px;
  position: relative;
}

.filter-panel-container {
  position: relative;
  flex-shrink: 0;
  height: 100%;
  min-height: 500px;
}

.filter-panel {
  width: 350px;
  transition: all 0.3s ease;
  margin-right: 0.5rem;
  height: 100%;
  :deep(.global-filter) {
    width: 350px !important;
  }

  &.collapsed {
    width: 0;
    overflow: hidden;
  }

  .sidebar-filter {
    height: 100%;
    width: 100%;
  }
}

.filter-toggle-button {
  position: absolute;
  top: 50%;
  right: -30px;
  transform: translateY(-50%);
  width: 32px;
  height: 64px;
  background: #409eff;
  border-radius: 0 12px 12px 0;
  display: flex !important;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  opacity: 1;
  visibility: visible;

  &:hover {
    background: #337ecc;
    transform: translateY(-50%) translateX(3px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }

  .el-icon {
    color: white;
    font-size: 18px;
    font-weight: bold;
  }

  .dark & {
    background: #337ecc;
    border-color: #374151;

    &:hover {
      background: #2b6cb0;
    }
  }
}

.content-area {
  flex: 1;
  position: relative;
  transition: all 0.3s ease;
  height: 100%;

  &.full-width {
    margin-left: 0;
  }
}

// 动画定义
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// DFG高亮效果样式
.dfg-canvas {
  // 确保图表容器支持交互
  cursor: default;

  // 高亮时的视觉反馈
  &.highlight-mode {
    .go-diagram {
      transition: all 0.3s ease;
    }
  }
}

// 右侧面板折叠按钮
.dfg-section .right-toggle-button {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  width: 32px;
  height: 64px;
  background: #409eff;
  border-radius: 12px 0 0 12px;
  display: flex !important;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1001;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  opacity: 1;
  visibility: visible;

  &:hover {
    background: #337ecc;
    transform: translateY(-50%) translateX(-3px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }

  .el-icon {
    color: white;
    font-size: 18px;
    font-weight: bold;
  }

  .dark & {
    background: #337ecc;
    border-color: #374151;

    &:hover {
      background: #2b6cb0;
    }
  }

  @media (max-width: 768px) {
    display: none;
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}