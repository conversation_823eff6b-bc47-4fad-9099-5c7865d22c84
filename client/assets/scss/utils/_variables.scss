// ProMax 设计系统变量

// 颜色系统
$colors: (
  // 主色调
  primary: (
    50: #eff6ff,
    100: #dbeafe,
    200: #bfdbfe,
    300: #93c5fd,
    400: #60a5fa,
    500: #3b82f6,
    600: #2563eb,
    700: #1d4ed8,
    800: #1e40af,
    900: #1e3a8a,
  ),

  // 灰色调
  gray: (
    50: #f9fafb,
    100: #f3f4f6,
    200: #e5e7eb,
    300: #d1d5db,
    400: #9ca3af,
    500: #6b7280,
    600: #4b5563,
    700: #374151,
    800: #1f2937,
    900: #111827,
  ),

  // 成功色
  success: (
    50: #f0fdf4,
    100: #dcfce7,
    200: #bbf7d0,
    300: #86efac,
    400: #4ade80,
    500: #22c55e,
    600: #16a34a,
    700: #15803d,
    800: #166534,
    900: #14532d,
  ),

  // 警告色
  warning: (
    50: #fffbeb,
    100: #fef3c7,
    200: #fde68a,
    300: #fcd34d,
    400: #fbbf24,
    500: #f59e0b,
    600: #d97706,
    700: #b45309,
    800: #92400e,
    900: #78350f,
  ),

  // 错误色
  error: (
    50: #fef2f2,
    100: #fee2e2,
    200: #fecaca,
    300: #fca5a5,
    400: #f87171,
    500: #ef4444,
    600: #dc2626,
    700: #b91c1c,
    800: #991b1b,
    900: #7f1d1d,
  ),

  // 信息色
  info: (
    50: #f0f9ff,
    100: #e0f2fe,
    200: #bae6fd,
    300: #7dd3fc,
    400: #38bdf8,
    500: #0ea5e9,
    600: #0284c7,
    700: #0369a1,
    800: #075985,
    900: #0c4a6e,
  ),

  // 橙色
  orange: (
    50: #fff7ed,
    100: #ffedd5,
    200: #fed7aa,
    300: #fdba74,
    400: #fb923c,
    500: #f97316,
    600: #ea580c,
    700: #c2410c,
    800: #9a3412,
    900: #7c2d12,
  ),

  // 紫色
  purple: (
    50: #faf5ff,
    100: #f3e8ff,
    200: #e9d5ff,
    300: #d8b4fe,
    400: #c084fc,
    500: #a855f7,
    600: #9333ea,
    700: #7e22ce,
    800: #6b21a8,
    900: #581c87,
  ),

  // 粉色
  pink: (
    50: #fdf2f8,
    100: #fce7f3,
    200: #fbcfe8,
    300: #f9a8d4,
    400: #f472b6,
    500: #ec4899,
    600: #db2777,
    700: #be185d,
    800: #9d174d,
    900: #831843,
  ),
);

// 间距系统
$spacing: (
  0: 0,
  1: 0.25rem,   // 4px
  2: 0.5rem,    // 8px
  3: 0.75rem,   // 12px
  4: 1rem,      // 16px
  5: 1.25rem,   // 20px
  6: 1.5rem,    // 24px
  8: 2rem,      // 32px
  10: 2.5rem,   // 40px
  12: 3rem,     // 48px
  16: 4rem,     // 64px
  20: 5rem,     // 80px
  24: 6rem,     // 96px
  32: 8rem,     // 128px
  40: 10rem,    // 160px
  48: 12rem,    // 192px
  56: 14rem,    // 224px
  64: 16rem,    // 256px
);

// 字体系统
$font-family: (
  sans: ('Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif),
  mono: ('Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace),
);

$font-size: (
  xs: 0.75rem,    // 12px
  sm: 0.875rem,   // 14px
  base: 1rem,     // 16px
  md: 1rem,       // 16px (alias of base)
  lg: 1.125rem,   // 18px
  xl: 1.25rem,    // 20px
  2xl: 1.5rem,    // 24px
  3xl: 1.875rem,  // 30px
  4xl: 2.25rem,   // 36px
  5xl: 3rem,      // 48px
  6xl: 3.75rem,   // 60px
);

$font-weight: (
  thin: 100,
  light: 300,
  normal: 400,
  medium: 500,
  semibold: 600,
  bold: 700,
  extrabold: 800,
  black: 900,
);

$line-height: (
  none: 1,
  tight: 1.25,
  snug: 1.375,
  normal: 1.5,
  relaxed: 1.625,
  loose: 2,
);

// 边框半径
$border-radius: (
  none: 0,
  sm: 0.125rem,   // 2px
  base: 0.25rem,  // 4px
  md: 0.375rem,   // 6px
  lg: 0.5rem,     // 8px
  xl: 0.75rem,    // 12px
  2xl: 1rem,      // 16px
  3xl: 1.5rem,    // 24px
  full: 9999px,
);

// 阴影
$box-shadow: (
  sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05),
  base: 0 1px 3px 0 rgba(0, 0, 0, 0.1),
  md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
  lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
  xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
  2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
  inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06),
  none: none,
);

// 断点
$breakpoints: (
  sm: 640px,
  md: 768px,
  lg: 1024px,
  xl: 1280px,
  2xl: 1536px,
);

// Z-index 层级
$z-index: (
  auto: auto,
  0: 0,
  10: 10,
  20: 20,
  30: 30,
  40: 40,
  50: 50,
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modal-backdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
);

// 过渡动画
$transition: (
  none: none,
  all: all 150ms cubic-bezier(0.4, 0, 0.2, 1),
  default: all 150ms cubic-bezier(0.4, 0, 0.2, 1),
  colors: color 150ms cubic-bezier(0.4, 0, 0.2, 1),
  opacity: opacity 150ms cubic-bezier(0.4, 0, 0.2, 1),
  shadow: box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1),
  transform: transform 150ms cubic-bezier(0.4, 0, 0.2, 1),
);

// 动画持续时间
$duration: (
  75: 75ms,
  100: 100ms,
  150: 150ms,
  200: 200ms,
  300: 300ms,
  500: 500ms,
  700: 700ms,
  1000: 1000ms,
);
