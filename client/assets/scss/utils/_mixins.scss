// ProMax Sass 混合器和函数
@use 'variables' as *;
@use 'sass:map';


// 获取颜色值的函数
@function theme-color($name, $shade: 500) {
  @if map.has-key($colors, $name) {
    $color-map: map.get($colors, $name);
    @if map.has-key($color-map, $shade) {
      @return map.get($color-map, $shade);
    } @else {
      @warn "Shade `#{$shade}` not found in color `#{$name}`.";
      @return null;
    }
  } @else {
    @warn "Color `#{$name}` not found.";
    @return null;
  }
}

// 获取间距值的函数
@function spacing($size) {
  @if map.has-key($spacing, $size) {
    @return map.get($spacing, $size);
  } @else {
    @warn "Spacing size `#{$size}` not found.";
    @return null;
  }
}

// 获取字体大小的函数
@function font-size($size) {
  @if map.has-key($font-size, $size) {
    @return map.get($font-size, $size);
  } @else {
    @warn "Font size `#{$size}` not found.";
    @return null;
  }
}

// 获取阴影的函数
@function shadow($size) {
  @if map.has-key($box-shadow, $size) {
    @return map.get($box-shadow, $size);
  } @else {
    @warn "Shadow size `#{$size}` not found.";
    @return null;
  }
}

// 响应式断点混合器
@mixin respond-to($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    @media (min-width: map.get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Breakpoint `#{$breakpoint}` not found.";
  }
}

// 响应式断点混合器（最大宽度）
@mixin respond-to-max($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    @media (max-width: calc(map.get($breakpoints, $breakpoint) - 1px)) {
      @content;
    }
  } @else {
    @warn "Breakpoint `#{$breakpoint}` not found.";
  }
}

// 卡片样式混合器
@mixin card($shadow: base, $radius: lg, $padding: 6) {
  background-color: white;
  border-radius: map.get($border-radius, $radius);
  box-shadow: map.get($box-shadow, $shadow);
  padding: map.get($spacing, $padding);

  .dark & {
    background-color: theme-color(gray, 800);
    border: 1px solid theme-color(gray, 700);
  }
}

// 按钮样式混合器
@mixin button($variant: primary, $size: base) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: map.get($border-radius, md);
  font-weight: map.get($font-weight, medium);
  cursor: pointer;
  transition: map.get($transition, all);
  text-decoration: none;

  @if $size == sm {
    padding: spacing(2) spacing(3);
    font-size: font-size(sm);
  } @else if $size == lg {
    padding: spacing(3) spacing(6);
    font-size: font-size(lg);
  } @else {
    padding: spacing(2) spacing(4);
    font-size: font-size(base);
  }

  @if $variant == primary {
    background-color: theme-color(primary, 600);
    color: white;

    &:hover {
      background-color: theme-color(primary, 700);
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
    }

    &:disabled {
      background-color: theme-color(gray, 300);
      cursor: not-allowed;
    }
  } @else if $variant == secondary {
    background-color: theme-color(gray, 200);
    color: theme-color(gray, 900);

    &:hover {
      background-color: theme-color(gray, 300);
    }

    .dark & {
      background-color: theme-color(gray, 700);
      color: theme-color(gray, 100);

      &:hover {
        background-color: theme-color(gray, 600);
      }
    }
  } @else if $variant == outline {
    background-color: transparent;
    color: theme-color(primary, 600);
    border: 1px solid theme-color(primary, 600);

    &:hover {
      background-color: theme-color(primary, 50);
    }

    .dark & {
      color: theme-color(primary, 400);
      border-color: theme-color(primary, 400);

      &:hover {
        background-color: theme-color(primary, 900);
      }
    }
  }
}

// 输入框样式混合器
@mixin input() {
}

// 文本截断混合器
@mixin text-truncate() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-truncate-lines($lines: 2) {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $lines;
}

// 居中混合器
@mixin center-flex() {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin center-absolute() {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 清除浮动混合器
@mixin clearfix() {
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}

// 隐藏文本混合器
@mixin sr-only() {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// 容器混合器
@mixin container($max-width: xl) {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: spacing(4);
  padding-right: spacing(4);

  @if $max-width == sm {
    max-width: map.get($breakpoints, sm);
  } @else if $max-width == md {
    max-width: map.get($breakpoints, md);
  } @else if $max-width == lg {
    max-width: map.get($breakpoints, lg);
  } @else if $max-width == xl {
    max-width: map.get($breakpoints, xl);
  } @else if $max-width == 2xl {
    max-width: map.get($breakpoints, 2xl);
  }

  @include respond-to(sm) {
    padding-left: spacing(6);
    padding-right: spacing(6);
  }

  @include respond-to(lg) {
    padding-left: spacing(8);
    padding-right: spacing(8);
  }
}
