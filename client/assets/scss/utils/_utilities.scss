// ProMax 工具类样式
@use 'variables' as *;
@use 'mixins' as *;
@use 'sass:map';

// 容器
.container {
  @include container();

  &--sm {
    @include container($max-width: sm);
  }

  &--md {
    @include container($max-width: md);
  }

  &--lg {
    @include container($max-width: lg);
  }

  &--xl {
    @include container($max-width: xl);
  }

  &--2xl {
    @include container($max-width: 2xl);
  }
}

// 响应式容器（兼容旧的 Tailwind 类名）
.container-responsive {
  @include container();
}

// 文本对齐
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

// 文本截断
.text-truncate {
  @include text-truncate();
}

.text-truncate-2 {
  @include text-truncate-lines(2);
}

.text-truncate-3 {
  @include text-truncate-lines(3);
}

// 字体大小
.text-xs { font-size: font-size(xs); }
.text-sm { font-size: font-size(sm); }
.text-base { font-size: font-size(base); }
.text-lg { font-size: font-size(lg); }
.text-xl { font-size: font-size(xl); }
.text-2xl { font-size: font-size(2xl); }
.text-3xl { font-size: font-size(3xl); }
.text-4xl { font-size: font-size(4xl); }
.text-5xl { font-size: font-size(5xl); }
.text-6xl { font-size: font-size(6xl); }

// 字体粗细
.font-thin { font-weight: map.get($font-weight, thin); }
.font-light { font-weight: map.get($font-weight, light); }
.font-normal { font-weight: map.get($font-weight, normal); }
.font-medium { font-weight: map.get($font-weight, medium); }
.font-semibold { font-weight: map.get($font-weight, semibold); }
.font-bold { font-weight: map.get($font-weight, bold); }
.font-extrabold { font-weight: map.get($font-weight, extrabold); }
.font-black { font-weight: map.get($font-weight, black); }

// 行高
.leading-none { line-height: map.get($line-height, none); }
.leading-tight { line-height: map.get($line-height, tight); }
.leading-snug { line-height: map.get($line-height, snug); }
.leading-normal { line-height: map.get($line-height, normal); }
.leading-relaxed { line-height: map.get($line-height, relaxed); }
.leading-loose { line-height: map.get($line-height, loose); }

// 文本颜色
.text-primary { color: theme-color(primary, 600); }
.text-success { color: theme-color(success, 600); }
.text-warning { color: theme-color(warning, 600); }
.text-error { color: theme-color(error, 600); }
.text-info { color: theme-color(info, 600); }
.text-gray { color: theme-color(gray, 600); }
.text-muted { color: theme-color(gray, 500); }

// 背景颜色
.bg-primary { background-color: theme-color(primary, 600); }
.bg-success { background-color: theme-color(success, 600); }
.bg-warning { background-color: theme-color(warning, 600); }
.bg-error { background-color: theme-color(error, 600); }
.bg-info { background-color: theme-color(info, 600); }
.bg-gray { background-color: theme-color(gray, 100); }
.bg-white { background-color: white; }
.bg-transparent { background-color: transparent; }

// 边距 - Margin
@each $size, $value in $spacing {
  .m-#{$size} { margin: $value; }
  .mt-#{$size} { margin-top: $value; }
  .mr-#{$size} { margin-right: $value; }
  .mb-#{$size} { margin-bottom: $value; }
  .ml-#{$size} { margin-left: $value; }
  .mx-#{$size} { margin-left: $value; margin-right: $value; }
  .my-#{$size} { margin-top: $value; margin-bottom: $value; }
}

// 内边距 - Padding
@each $size, $value in $spacing {
  .p-#{$size} { padding: $value; }
  .pt-#{$size} { padding-top: $value; }
  .pr-#{$size} { padding-right: $value; }
  .pb-#{$size} { padding-bottom: $value; }
  .pl-#{$size} { padding-left: $value; }
  .px-#{$size} { padding-left: $value; padding-right: $value; }
  .py-#{$size} { padding-top: $value; padding-bottom: $value; }
}

// 显示
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }

// Flexbox
.flex-row { flex-direction: row; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-none { flex: none; }

// Justify Content
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

// Align Items
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

// Gap
@each $size, $value in $spacing {
  .gap-#{$size} { gap: $value; }
  .gap-x-#{$size} { column-gap: $value; }
  .gap-y-#{$size} { row-gap: $value; }
}

// 宽度
.w-auto { width: auto; }
.w-full { width: 100%; }
.w-screen { width: 100vw; }
.w-fit { width: fit-content; }

// 高度
.h-auto { height: auto; }
.h-full { height: 100%; }
.h-screen { height: 100vh; }
.h-fit { height: fit-content; }

// 位置
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }
.static { position: static; }

// 溢出
.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }

// 圆角
.rounded-none { border-radius: map.get($border-radius, none); }
.rounded-sm { border-radius: map.get($border-radius, sm); }
.rounded { border-radius: map.get($border-radius, base); }
.rounded-md { border-radius: map.get($border-radius, md); }
.rounded-lg { border-radius: map.get($border-radius, lg); }
.rounded-xl { border-radius: map.get($border-radius, xl); }
.rounded-2xl { border-radius: map.get($border-radius, 2xl); }
.rounded-3xl { border-radius: map.get($border-radius, 3xl); }
.rounded-full { border-radius: map.get($border-radius, full); }

// 阴影
.shadow-none { box-shadow: map.get($box-shadow, none); }
.shadow-sm { box-shadow: map.get($box-shadow, sm); }
.shadow { box-shadow: map.get($box-shadow, base); }
.shadow-md { box-shadow: map.get($box-shadow, md); }
.shadow-lg { box-shadow: map.get($box-shadow, lg); }
.shadow-xl { box-shadow: map.get($box-shadow, xl); }
.shadow-2xl { box-shadow: map.get($box-shadow, 2xl); }
.shadow-inner { box-shadow: map.get($box-shadow, inner); }

// 透明度
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

// 过渡
.transition-none { transition: map.get($transition, none); }
.transition-all { transition: map.get($transition, all); }
.transition-colors { transition: map.get($transition, colors); }
.transition-opacity { transition: map.get($transition, opacity); }
.transition-shadow { transition: map.get($transition, shadow); }
.transition-transform { transition: map.get($transition, transform); }

// 变换
.transform { transform: translateZ(0); }
.scale-0 { transform: scale(0); }
.scale-50 { transform: scale(0.5); }
.scale-75 { transform: scale(0.75); }
.scale-90 { transform: scale(0.9); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }
.scale-125 { transform: scale(1.25); }
.scale-150 { transform: scale(1.5); }

// 光标
.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-help { cursor: help; }
.cursor-not-allowed { cursor: not-allowed; }

// 用户选择
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }
.select-auto { user-select: auto; }

// 指针事件
.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

// 屏幕阅读器
.sr-only {
  @include sr-only();
}
