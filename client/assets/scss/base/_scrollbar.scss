// ProMax 自定义滚动条样式
@use '../utils/variables' as *;
@use '../utils/mixins' as *;
@use 'sass:map';

// Webkit 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: theme-color(gray, 100);
  border-radius: map.get($border-radius, full);
  
  .dark & {
    background-color: theme-color(gray, 800);
  }
}

::-webkit-scrollbar-thumb {
  background-color: theme-color(gray, 300);
  border-radius: map.get($border-radius, full);
  transition: map.get($transition, colors);
  
  &:hover {
    background-color: theme-color(gray, 400);
  }
  
  .dark & {
    background-color: theme-color(gray, 600);
    
    &:hover {
      background-color: theme-color(gray, 500);
    }
  }
}

::-webkit-scrollbar-corner {
  background-color: theme-color(gray, 100);
  
  .dark & {
    background-color: theme-color(gray, 800);
  }
}

// Firefox 滚动条样式
* {
  scrollbar-width: thin;
  scrollbar-color: theme-color(gray, 300) theme-color(gray, 100);
  
  .dark & {
    scrollbar-color: theme-color(gray, 600) theme-color(gray, 800);
  }
}

// 自定义滚动条类
.scrollbar-thin {
  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
}

.scrollbar-none {
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}
