// ProMax CSS Reset
@use '../utils/variables' as *;
@use '../utils/mixins' as *;
@use 'sass:map';

// 基础重置
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

// HTML 和 Body
html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: map.get($font-family, sans);
  font-size: font-size(base);
  line-height: map.get($line-height, normal);
  color: theme-color(gray, 900);
  background-color: theme-color(gray, 50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  .dark & {
    color: theme-color(gray, 100);
    background-color: theme-color(gray, 900);
  }
}

// 标题
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: map.get($font-weight, semibold);
  line-height: map.get($line-height, tight);
  color: inherit;
}

h1 {
  font-size: font-size(3xl);
  
  @include respond-to(md) {
    font-size: font-size(4xl);
  }
}

h2 {
  font-size: font-size(2xl);
  
  @include respond-to(md) {
    font-size: font-size(3xl);
  }
}

h3 {
  font-size: font-size(xl);
  
  @include respond-to(md) {
    font-size: font-size(2xl);
  }
}

h4 {
  font-size: font-size(lg);
  
  @include respond-to(md) {
    font-size: font-size(xl);
  }
}

h5 {
  font-size: font-size(base);
  
  @include respond-to(md) {
    font-size: font-size(lg);
  }
}

h6 {
  font-size: font-size(sm);
  
  @include respond-to(md) {
    font-size: font-size(base);
  }
}

// 段落
p {
  margin: 0;
  line-height: map.get($line-height, relaxed);
}

// 链接
a {
  color: theme-color(primary, 600);
  text-decoration: none;
  transition: map.get($transition, colors);

  &:hover {
    color: theme-color(primary, 700);
    text-decoration: underline;
  }

  &:focus {
    outline: 2px solid theme-color(primary, 500);
    outline-offset: 2px;
  }

  .dark & {
    color: theme-color(primary, 400);

    &:hover {
      color: theme-color(primary, 300);
    }
  }
}

// 列表
ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

// 图片
img {
  max-width: 100%;
  height: auto;
  border-style: none;
}

// 表格
table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
}

th, td {
  text-align: left;
  vertical-align: top;
  padding: spacing(3);
  border-bottom: 1px solid theme-color(gray, 200);

  .dark & {
    border-bottom-color: theme-color(gray, 700);
  }
}

th {
  font-weight: map.get($font-weight, semibold);
  color: theme-color(gray, 700);
  background-color: theme-color(gray, 50);

  .dark & {
    color: theme-color(gray, 300);
    background-color: theme-color(gray, 800);
  }
}

// 表单元素
button, input, optgroup, select, textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
}

button, input {
  overflow: visible;
}

button, select {
  text-transform: none;
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
  appearance: button;
  cursor: pointer;
}

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="search"],
input[type="tel"],
input[type="url"],
select,
textarea {
  @include input();
}

// 代码
code, kbd, samp, pre {
  font-family: map.get($font-family, mono);
  font-size: font-size(sm);
}

code {
  padding: spacing(1) spacing(2);
  background-color: theme-color(gray, 100);
  border-radius: map.get($border-radius, sm);

  .dark & {
    background-color: theme-color(gray, 800);
  }
}

pre {
  padding: spacing(4);
  background-color: theme-color(gray, 100);
  border-radius: map.get($border-radius, md);
  overflow-x: auto;

  .dark & {
    background-color: theme-color(gray, 800);
  }

  code {
    padding: 0;
    background-color: transparent;
  }
}

// 引用
blockquote {
  margin: 0;
  padding: spacing(4);
  border-left: 4px solid theme-color(gray, 300);
  background-color: theme-color(gray, 50);
  font-style: italic;

  .dark & {
    border-left-color: theme-color(gray, 600);
    background-color: theme-color(gray, 800);
  }
}

// 水平线
hr {
  height: 0;
  overflow: visible;
  border: 0;
  border-top: 1px solid theme-color(gray, 200);
  margin: spacing(6) 0;

  .dark & {
    border-top-color: theme-color(gray, 700);
  }
}

// 隐藏元素
[hidden] {
  display: none;
}

// 屏幕阅读器专用
.sr-only {
  @include sr-only();
}
