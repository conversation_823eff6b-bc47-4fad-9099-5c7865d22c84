<template>
  <div class="performance-trends">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-state">
      <div class="loading-spinner"/>
      <p class="loading-text">正在加载趋势数据...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="isError" class="error-state">
      <el-result icon="warning" title="加载趋势数据失败" :sub-title="errorMessage || '请稍后重试'">
        <template #extra>
          <el-button type="primary" @click="loadTrendsData()">重试</el-button>
        </template>
      </el-result>
    </div>

    <!-- 主要内容 -->
    <div v-else class="trends-content">
      <!-- 控制面板 -->
      <el-card class="control-card">
        <div class="control-header">
          <div class="header-left">
            <h3 class="trends-title">性能趋势分析</h3>
          </div>
          <div class="header-right">
            <div class="control-group">
              <!-- 时间范围选择器 -->
              <div class="time-range-selector">
                <div class="time-range-label">
                  <el-icon class="time-icon"><Clock /></el-icon>
                  <span>时间筛选</span>
                </div>
                <el-date-picker
                  v-model="timeRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  size="default"
                  class="time-picker"
                  :disabled-date="disabledDate"
                  clearable
                  @change="handleTimeRangeChange"
                />
              </div>

              <!-- 时间粒度选择器 -->
              <div class="granularity-tabs">
                <div
                  v-for="option in granularityOptions"
                  :key="option.value"
                  :class="['granularity-tab', { active: activeGranularity === option.value }]"
                  @click="selectGranularity(option.value)"
                >
                  {{ option.label }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 性能分析整体模块 -->
      <el-card class="performance-analysis-container">
        <!-- 趋势图表 -->
        <div class="trend-chart-section">
          <div class="section-header">
            <div class="header-left">
              <div class="title-with-description">
                <h4 class="section-title">趋势对比</h4>
                <p class="chart-description">不同时间周期内各项性能指标的变化趋势, 点击图表中的任意点可查看对应周期内的性能排行榜、活动耗时占比和资源等待时间堆叠图</p>
              </div>
            </div>
            <div class="header-right">
              <el-checkbox-group v-model="selectedMetrics" size="small" class="metric-checkbox-group">
                <el-checkbox value="avgDuration">平均持续时间</el-checkbox>
                <el-checkbox value="avgWaitTime">平均等待时间</el-checkbox>
                <el-checkbox value="caseCount">案例数量</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div ref="trendChartContainer" class="chart-container" style="width: 100%; height: 380px;">
            <ClientOnly>
              <VChart
                v-if="trendChartOption && chartReady"
                ref="trendChart"
                :option="trendChartOption"
                class="trend-chart"
                :style="{ width: '100%', height: '100%' }"
                autoresize
                @click="handleChartClick"
              />
              <template #fallback>
                <div class="chart-placeholder">
                  <el-icon class="placeholder-icon"><TrendCharts /></el-icon>
                  <p class="placeholder-text">图表加载中...</p>
                </div>
              </template>
            </ClientOnly>
            <div v-if="!trendChartOption" class="chart-placeholder">
              <el-icon class="placeholder-icon"><TrendCharts /></el-icon>
              <p class="placeholder-text">暂无趋势数据</p>
            </div>
          </div>
        </div>

        <!-- 排行榜 -->
        <div class="ranking-section">
          <div class="section-header">
            <div class="header-left">
              <div class="title-with-description">
                <h4 class="section-title">
                  性能排行榜
                  <span v-if="previousPeriodRange && currentPeriodRange" class="ranking-time-badges">
                    <span class="time-badge previous">
                      <span class="badge-label">上期</span>
                      {{ previousPeriodRange }}
                    </span>
                    <span class="time-badge current">
                      <span class="badge-label">本期</span>
                      {{ currentPeriodRange }}
                    </span>
                  </span>
                </h4>
                <p class="chart-description">耗时变化最显著的活动或资源, 点击项目可查看具体耗时数据</p>
              </div>
            </div>
            <div class="header-right">
              <el-radio-group v-model="rankingDimension" size="small" class="dimension-selector">
                <el-radio-button value="activity">
                  <el-icon><TrendCharts /></el-icon>
                  活动
                </el-radio-button>
                <el-radio-button value="resource">
                  <el-icon><TrendCharts /></el-icon>
                  资源
                </el-radio-button>
              </el-radio-group>
            </div>
          </div>

        <el-row :gutter="24" class="ranking-content">
          <el-col :xs="24" :lg="12">
            <div class="ranking-panel improvement-panel">
              <div class="panel-header">
                <div class="panel-title">
                  <el-icon class="title-icon improvement-icon"><Top /></el-icon>
                  <span>性能提升 TOP5</span>
                </div>
              </div>

              <div class="ranking-list">
                <div
                  v-for="(item, index) in improvementRanking.slice(0, 5)"
                  :key="item.name"
                  class="ranking-item improvement-item"
                  @click="showDetailDialog(item, 'improvement')"
                >
                  <div class="item-rank" :class="`rank-${index + 1}`">
                    {{ index + 1 }}
                  </div>
                  <div class="item-content">
                    <div class="item-name">{{ item.name }}</div>
                  </div>
                  <div class="item-change improvement-change">
                    <div class="change-value">{{ formatPercentage(Math.abs(item.change)) }}</div>
                  </div>
                </div>

                <div v-if="improvementRanking.length === 0" class="empty-state">
                  <el-icon class="empty-icon"><TrendCharts /></el-icon>
                  <p class="empty-text">暂无提升数据</p>
                </div>
              </div>
            </div>
          </el-col>

          <el-col :xs="24" :lg="12">
            <div class="ranking-panel decline-panel">
              <div class="panel-header">
                <div class="panel-title">
                  <el-icon class="title-icon decline-icon"><Bottom /></el-icon>
                  <span>性能下降 TOP5</span>
                </div>
              </div>

              <div class="ranking-list">
                <div
                  v-for="(item, index) in declineRanking.slice(0, 5)"
                  :key="item.name"
                  class="ranking-item decline-item"
                  @click="showDetailDialog(item, 'decline')"
                >
                  <div class="item-rank" :class="`rank-${index + 1}`">
                    {{ index + 1 }}
                  </div>
                  <div class="item-content">
                    <div class="item-name">{{ item.name }}</div>
                  </div>
                  <div class="item-change decline-change">
                    <div class="change-value">{{ formatPercentage(Math.abs(item.change)) }}</div>
                  </div>
                </div>

                <div v-if="declineRanking.length === 0" class="empty-state">
                  <el-icon class="empty-icon"><TrendCharts /></el-icon>
                  <p class="empty-text">暂无下降数据</p>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        </div>

        <!-- 图表区域 -->
        <div class="charts-section">
          <el-row :gutter="24" class="charts-row">
        <el-col :xs="24" :md="12">
          <div class="chart-card">
            <div class="section-header">
              <div class="header-left">
                <div class="title-with-description">
                  <h4 class="section-title">
                    活动耗时占比
                    <span v-if="pieChartTimeRange" class="title-time-badge">{{ pieChartTimeRange }}</span>
                  </h4>
                  <p class="chart-description">各个活动耗时在总耗时中的占比情况</p>
                </div>
              </div>
              <div class="header-right">
                <el-radio-group v-model="piePeriod" size="small" class="period-selector">
                  <el-radio-button value="current">
                    <el-icon><TrendCharts /></el-icon>
                    本期
                  </el-radio-button>
                  <el-radio-button value="previous">
                    <el-icon><TrendCharts /></el-icon>
                    上期
                  </el-radio-button>
                </el-radio-group>
              </div>
            </div>
            <div ref="pieChartContainer" class="chart-wrapper">
              <ClientOnly>
                <VChart
                  v-if="pieChartOption && chartReady && pieContainerReady"
                  :option="pieChartOption"
                  class="chart"
                  autoresize
                />
                <template #fallback>
                  <div class="chart-placeholder">图表加载中...</div>
                </template>
              </ClientOnly>
              <div v-if="!pieChartOption" class="chart-placeholder">暂无数据</div>
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :md="12">
          <div class="chart-card">
            <div class="section-header">
              <div class="header-left">
                <div class="title-with-description">
                  <h4 class="section-title">资源等待时间堆叠图</h4>
                  <p class="chart-description">不同资源的等待时间分布, 各颜色段表示不同资源的耗费的时间</p>
                </div>
              </div>
            </div>
            <div ref="stackBarChartContainer" class="chart-wrapper">
              <ClientOnly>
                <VChart
                  v-if="stackBarChartOption?.series && stackBarChartOption?.series.length && chartReady && stackContainerReady"
                  :option="stackBarChartOption"
                  class="chart"
                  autoresize
                />
                <template #fallback>
                  <div class="chart-placeholder">图表加载中...</div>
                </template>
              </ClientOnly>
              <div v-if="!stackBarChartOption?.series.length" class="chart-placeholder">暂无数据</div>
            </div>
          </div>
          </el-col>
        </el-row>
        </div>
      </el-card>
    </div>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      width="480px"
      :show-close="false"
      center
      class="performance-detail-dialog"
    >
      <template #header>
        <div class="dialog-header">
          <h3 class="dialog-title">{{ selectedItem?.name }}耗时</h3>
          <el-button
            type="text"
            class="close-btn"
            @click="detailDialogVisible = false"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </template>

      <div v-if="selectedItem" class="detail-content">
        <!-- 变化指标 -->
        <div class="change-metric">
          <div class="metric-label">变化幅度</div>
          <div class="metric-value" :class="selectedItemType">
            <el-icon class="trend-icon">
              <Top v-if="selectedItemType === 'improvement'" />
              <Bottom v-else />
            </el-icon>
            {{ formatPercentage(selectedItem.change) }}
          </div>
        </div>

        <!-- 数据对比 -->
        <div class="data-comparison">
          <div class="comparison-item">
            <div class="item-label">{{ currentPeriodRange }}</div>
            <div class="item-value">
              {{ formatDuration(selectedItem.currentValue * 1000 * 60 * 60) }}
            </div>
          </div>
          <div class="comparison-item">
            <div class="item-label">{{ previousPeriodRange }}</div>
            <div class="item-value">
              {{ formatDuration(selectedItem.previousValue * 1000 * 60 * 60) }}
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick, defineProps } from 'vue'
import {
  TrendCharts,
  Top,
  Bottom,
  Clock,
  Close
} from '@element-plus/icons-vue'
import { useApi } from '~/utils/api'
import { useRoute } from 'vue-router'
import type { PerformanceTrends } from '~/types'

type MetricKey = 'avgDuration' | 'avgWaitTime' | 'caseCount'

interface TrendItem {
  name: string
  description: string
  change: number
  currentValue: number
  previousValue: number
}

interface Props {
  requiredActivities?: string[]
  trends?: PerformanceTrends | null
}

const props = withDefaults(defineProps<Props>(), {
  requiredActivities: () => [],
  trends: null,
})

const route = useRoute()
const processId = parseInt(route.params.processId as string)
const api = useApi()

// 基础状态
const isLoading = ref(false)
const isError = ref(false)
const errorMessage = ref('')
const activeGranularity = ref('week')
const timeRange = ref<string[]>([])

// 时间粒度选项
const granularityOptions = [
  { label: '年', value: 'year' },
  { label: '月', value: 'month' },
  { label: '周', value: 'week' },
  { label: '日', value: 'day' }
]

// 禁用未来日期
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now()
}

// 格式化日期
const formatDate = (date: Date): string => {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const formatPercentage = (value: number) => {
  if (value === 0) return '0%'
  return `${value.toFixed(1)}%`
}

// 图表和排行榜配置
const selectedMetrics = ref<MetricKey[]>(['avgDuration', 'avgWaitTime', 'caseCount'])
const rankingDimension = ref<'activity' | 'resource'>('activity')
const piePeriod = ref<'current' | 'previous'>('current')

// 数据状态
const trendsData = ref<any>(null)
const periodDetails = ref<any>(null)
const selectedPeriod = ref<string>('')
const lastLoadedPeriod = ref<string>('') // 记录最后加载的周期，避免重复请求
const overallTrend = ref({
  avgDurationChange: 0,
  avgWaitTimeChange: 0,
  totalCases: 0
})

// 图表渲染状态
const chartReady = ref(false)
const trendChart = ref<any>(null)
const trendChartContainer = ref<HTMLElement | null>(null)
const pieChartContainer = ref<HTMLElement | null>(null)
const stackBarChartContainer = ref<HTMLElement | null>(null)
const pieContainerReady = ref(false)
const stackContainerReady = ref(false)

// 等待容器尺寸就绪再渲染图表，避免 ECharts DOM 尺寸为 0 报错
const showChartsWhenReady = async () => {
  await nextTick()

  const maxWaitMs = 3000
  const intervalMs = 60
  const start = Date.now()

  const isElReady = (el: HTMLElement | null) => !!el && el.clientWidth > 0 && el.clientHeight > 0

  // 等待三个容器（若存在）都有非 0 宽高
  while (
    (!isElReady(trendChartContainer.value)) ||
    (pieChartContainer.value && !isElReady(pieChartContainer.value)) ||
    (stackBarChartContainer.value && !isElReady(stackBarChartContainer.value))
  ) {
    if (Date.now() - start > maxWaitMs) break
    await new Promise((r) => setTimeout(r, intervalMs))
  }

  const readyNow =
    isElReady(trendChartContainer.value) &&
    (!pieChartContainer.value || isElReady(pieChartContainer.value)) &&
    (!stackBarChartContainer.value || isElReady(stackBarChartContainer.value))

  // 更新子图容器就绪标识
  pieContainerReady.value = !pieChartContainer.value ? true : isElReady(pieChartContainer.value)
  stackContainerReady.value = !stackBarChartContainer.value ? true : isElReady(stackBarChartContainer.value)

  if (!readyNow) {
    return
  }

  if (!chartReady.value) {
    chartReady.value = true
  }
  await nextTick()
  setTimeout(() => {
    setupChartAreaClick()
    try { trendChart.value?.chart?.resize?.() } catch {}
  }, 0)
}

let containerObserver: ResizeObserver | null = null
const setupContainerObserver = () => {
  // SSR/旧环境下无 ResizeObserver
  if (typeof window === 'undefined' || typeof (window as any).ResizeObserver === 'undefined') return
  if (containerObserver) return
  containerObserver = new ResizeObserver(() => {
    showChartsWhenReady()
  })
  if (trendChartContainer.value) containerObserver.observe(trendChartContainer.value)
  if (pieChartContainer.value) containerObserver.observe(pieChartContainer.value)
  if (stackBarChartContainer.value) containerObserver.observe(stackBarChartContainer.value)
}

onBeforeUnmount(() => {
  try { containerObserver?.disconnect() } catch {}
  containerObserver = null
})

// 详情对话框
const detailDialogVisible = ref(false)
const selectedItem = ref<any>(null)
const selectedItemType = ref<'improvement' | 'decline'>('improvement')

// 当前周期时间范围
const currentPeriodRange = computed(() => {
  if (selectedPeriod.value && trendsData.value?.timeSeries) {
    const timeSeries = trendsData.value.timeSeries
    const currentIndex = timeSeries.findIndex(
      (item: any) => item.period === selectedPeriod.value
    )

    if (currentIndex >= 0) {
      const currentPeriod = timeSeries[currentIndex]
      return formatPeriodRange(currentPeriod)
    }
  }
  return '本期'
})

// 上期时间范围
const previousPeriodRange = computed(() => {
  if (selectedPeriod.value && trendsData.value?.timeSeries) {
    const timeSeries = trendsData.value.timeSeries
    const currentIndex = timeSeries.findIndex(
      (item: any) => item.period === selectedPeriod.value
    )

    if (currentIndex > 0) {
      const previousPeriod = timeSeries[currentIndex - 1]
      return formatPeriodRange(previousPeriod)
    }
  }
  return '上期'
})

// 饼图时间范围显示
const pieChartTimeRange = computed(() => {
  if (selectedPeriod.value && trendsData.value?.timeSeries) {
    const timeSeries = trendsData.value.timeSeries
    const currentIndex = timeSeries.findIndex(
      (item: any) => item.period === selectedPeriod.value
    )

    if (currentIndex >= 0) {
      if (piePeriod.value === 'current') {
        const currentPeriod = timeSeries[currentIndex]
        return formatPeriodRange(currentPeriod)
      } else if (piePeriod.value === 'previous' && currentIndex > 0) {
        const previousPeriod = timeSeries[currentIndex - 1]
        return formatPeriodRange(previousPeriod)
      }
    }
  }
  return ''
})

// 格式化周期范围的辅助函数
const formatPeriodRange = (item: any) => {
  if (!item.start || !item.end) return item.period

  const startDate = new Date(item.start)
  const endDate = new Date(item.end)

  if (activeGranularity.value === 'day') {
    return formatDate(startDate)
  } else if (activeGranularity.value === 'week') {
    return `${formatDate(startDate)} - ${formatDate(endDate)}`
  } else if (activeGranularity.value === 'month') {
    return `${startDate.getFullYear()}年${startDate.getMonth() + 1}月`
  } else if (activeGranularity.value === 'year') {
    return `${startDate.getFullYear()}年`
  } else {
    return `${formatDate(startDate)} - ${formatDate(endDate)}`
  }
}

// 排行榜数据
const improvementRanking = computed(() => {
  if (!trendsData.value?.rankings) {
    return []
  }
  const rankings = trendsData.value.rankings[rankingDimension.value]
  return rankings?.improvements || []
})

const declineRanking = computed(() => {
  if (!trendsData.value?.rankings) return []
  const rankings = trendsData.value.rankings[rankingDimension.value]
  return rankings?.declines || []
})

// 趋势图表配置
const trendChartOption = computed(() => {
  if (!trendsData.value?.timeSeries) return null

  const timeSeries = trendsData.value.timeSeries
  // X轴显示具体的时间标签
  const xAxisData = timeSeries.map((item: any) => {
    if (item.start) {
      const startDate = new Date(item.start)
      const endDate = new Date(item.end)

      if (activeGranularity.value === 'week') {
        // 周粒度保留年份信息，确保数据清晰
        const startYear = startDate.getFullYear()
        const startMonth = startDate.getMonth() + 1
        const startDay = startDate.getDate()
        const endYear = endDate.getFullYear()
        const endMonth = endDate.getMonth() + 1
        const endDay = endDate.getDate()

        return `${startYear}/${startMonth}/${startDay}\n${endYear}/${endMonth}/${endDay}`
      } else if (activeGranularity.value === 'month') {
        return `${startDate.getFullYear()}年${startDate.getMonth() + 1}月`
      } else if (activeGranularity.value === 'day') {
        return formatDate(startDate)
      } else if (activeGranularity.value === 'year') {
        return `${startDate.getFullYear()}年`
      }
    }
    return item.period
  })

  // 创建详细时间信息用于tooltip
  const detailedTimeInfo = timeSeries.map((item: any) => {
    if (item.start && item.end) {
      const startDate = new Date(item.start)
      const endDate = new Date(item.end)

      if (activeGranularity.value === 'day') {
        return formatDate(startDate)
      } else if (activeGranularity.value === 'week') {
        return `${formatDate(startDate)} - ${formatDate(endDate)}`
      } else if (activeGranularity.value === 'month') {
        return `${startDate.getFullYear()}年${startDate.getMonth() + 1}月`
      } else if (activeGranularity.value === 'year') {
        return `${startDate.getFullYear()}年`
      }
    }
    return item.period
  })

  const series = selectedMetrics.value.map(metricKey => {
    const metricConfig = getMetricConfig(metricKey)
    const data = timeSeries.map((item: any) => item[metricKey])

    return {
      name: metricConfig.name,
      type: 'line',
      data,
      smooth: true,
      lineStyle: { width: 3, color: metricConfig.color },
      itemStyle: { color: metricConfig.color },
      areaStyle: metricKey !== 'caseCount' ? {
        color: metricConfig.color + '22'
      } : undefined,
      symbol: 'circle',
      symbolSize: 8,
      yAxisIndex: metricConfig.yAxisIndex || 0,
      // 增大点击区域
      triggerLineEvent: true,
      emphasis: {
        focus: 'series',
        lineStyle: {
          width: 5,
          color: metricConfig.color
        },
        itemStyle: {
          color: metricConfig.color
        },
        symbolSize: 12
      }
    }
  })

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        snap: true
      },
      formatter: (params: any) => {
        const dataIndex = params[0].dataIndex
        const detailedTime = detailedTimeInfo[dataIndex] || params[0].axisValue

        // 只显示详细时间信息，不重复显示X轴标签
        let result = `<div style="font-weight: bold; margin-bottom: 8px; color: #333;">${detailedTime}</div>`

        params.forEach((param: any) => {
          const unit = param.seriesName.includes('时间') ? '小时' :
                      param.seriesName.includes('数量') ? '个' : ''
          result += `${param.marker}${param.seriesName}: <strong>${param.value}${unit}</strong><br/>`
        })
        result += '<br/><span style="color: #666; font-size: 12px;">💡 点击查看该周期详情</span>'
        return result
      }
    },
    legend: {
      data: selectedMetrics.value.map(key => getMetricConfig(key).name),
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: activeGranularity.value === 'week' ? '18%' : '8%', // 周粒度需要更多底部空间来显示年份
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: {
        rotate: activeGranularity.value === 'week' ? 0 : 45, // 周粒度不旋转，其他旋转45度
        fontSize: 11,
        margin: 15, // 增加标签与轴线的距离
        lineHeight: 14, // 设置行高
        rich: {
          // 为多行文本设置样式
          a: {
            fontSize: 11,
            lineHeight: 14
          }
        }
      },
      triggerEvent: true, // 启用 X 轴点击事件
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(150,150,150,0.3)'
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '时间(小时)',
        nameTextStyle: { fontSize: 12 },
        min: 0
      },
      {
        type: 'value',
        name: '案例数量',
        nameTextStyle: { fontSize: 12 },
        min: 0
      }
    ],
    series
  }
})

// 饼图数据和配置
const pieChartOption = computed(() => {
  if (!trendsData.value?.activityDurations) {
    return null
  }

  const activityDurations = trendsData.value.activityDurations
  let pieData = []

  // 根据选择的周期获取数据
  if (piePeriod.value === 'current') {
    pieData = activityDurations.current || []
  } else if (piePeriod.value === 'previous') {
    pieData = activityDurations.previous || []
  }

  // 如果没有数据，返回null
  if (!pieData || pieData.length === 0) {
    return null
  }

  return {
    tooltip: {
      trigger: 'item',
      formatter: function(params: any) {
        const percentage = typeof params.percent === 'number' ? params.percent.toFixed(2) : params.percent
        const value = typeof params.value === 'number' ? params.value.toFixed(2) : params.value
        return `${params.seriesName} <br/>${params.name}: ${value} 小时 (${percentage}%)`
      },
      confine: true,
      position: function (point: any, params: any, dom: any, rect: any, size: any) {
        // 确保tooltip不会超出容器边界
        const x = point[0]
        const y = point[1]
        const viewWidth = size.viewSize[0]
        const viewHeight = size.viewSize[1]
        const boxWidth = size.contentSize[0]
        const boxHeight = size.contentSize[1]

        let posX = x + 10
        let posY = y + 10

        // 防止右侧溢出
        if (posX + boxWidth > viewWidth) {
          posX = x - boxWidth - 10
        }

        // 防止底部溢出
        if (posY + boxHeight > viewHeight) {
          posY = y - boxHeight - 10
        }

        // 防止左侧溢出
        if (posX < 0) {
          posX = 10
        }

        // 防止顶部溢出
        if (posY < 0) {
          posY = 10
        }

        return [posX, posY]
      }
    },
    legend: {
      bottom: 5,
      left: 'center',
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '活动耗时占比',
        type: 'pie',
        radius: ['35%', '60%'],
        center: ['50%', '40%'],
        avoidLabelOverlap: true,
        label: {
          show: true,
          formatter: function(params: any) {
            const percentage = typeof params.percent === 'number' ? params.percent.toFixed(2) : params.percent
            return `${params.name}: ${percentage}%`
          },
          fontSize: 12,
          position: 'outside',
          distanceToLabelLine: 5
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          smooth: 0.2
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        data: pieData,
      },
    ],
  }
})

// 堆叠柱状图数据和配置
const stackBarChartOption = computed(() => {
  if (!trendsData.value?.resourceWaitTimes) {
    return null
  }

  const resourceWaitTimes = trendsData.value.resourceWaitTimes
  const barData = resourceWaitTimes.current

  if (!barData) {
    return null
  }

  // 使用具体的时间范围作为X轴标签
  let periods = ['上期', '本期'] // 默认值

  if (selectedPeriod.value && trendsData.value?.timeSeries) {
    const timeSeries = trendsData.value.timeSeries
    const currentIndex = timeSeries.findIndex(
      (item: any) => item.period === selectedPeriod.value
    )

    if (currentIndex >= 0) {
      const currentPeriod = timeSeries[currentIndex]
      const previousPeriod = currentIndex > 0 ? timeSeries[currentIndex - 1] : null

      const currentRange = formatPeriodRange(currentPeriod)

      if (previousPeriod) {
        const previousRange = formatPeriodRange(previousPeriod)
        periods = [previousRange, currentRange]
      } else {
        periods = ['--', currentRange]
      }
    }
  }

  const series = barData.series || []

  // 构建堆叠图的系列数据
  let seriesData: any[] = []
  let finalLegend: string[] = []

  // 使用series数据结构
  const reversedSeries = [...series].reverse()
  seriesData = reversedSeries.map((seriesItem: any) => ({
    name: seriesItem.name,
    type: 'bar',
    stack: 'total', // 堆叠标识
    data: seriesItem.data || [],
    emphasis: {
      focus: 'series'
    }
  }))

  finalLegend = series.map((seriesItem: any) => seriesItem.name)

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      confine: true,
      formatter: function(params: any) {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          const value = typeof param.value === 'number' ? param.value.toFixed(2) : param.value
          result += `${param.marker}${param.seriesName}: ${value} 小时<br/>`
        })
        return result
      },
      position: function (point: any, params: any, dom: any, rect: any, size: any) {
        // 确保tooltip不会超出容器边界
        const x = point[0]
        const y = point[1]
        const viewWidth = size.viewSize[0]
        const viewHeight = size.viewSize[1]
        const boxWidth = size.contentSize[0]
        const boxHeight = size.contentSize[1]

        let posX = x + 10
        let posY = y - boxHeight - 10

        // 防止右侧溢出
        if (posX + boxWidth > viewWidth) {
          posX = x - boxWidth - 10
        }

        // 防止顶部溢出
        if (posY < 0) {
          posY = y + 10
        }

        // 防止左侧溢出
        if (posX < 0) {
          posX = 10
        }

        // 防止底部溢出
        if (posY + boxHeight > viewHeight) {
          posY = viewHeight - boxHeight - 10
        }

        return [posX, posY]
      }
    },
    legend: {
      data: finalLegend,
      bottom: 5,
      left: 'center',
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '8%',
      right: '4%',
      bottom: '20%',
      top: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: periods,
      axisLabel: {
        fontSize: 12,
        rotate: 0
      }
    },
    yAxis: {
      type: 'value',
      name: '等待时间(小时)',
      nameLocation: 'middle',
      nameGap: 50,
      nameTextStyle: {
        fontSize: 12,
        color: '#666',
        fontWeight: 'normal'
      },
      axisLabel: {
        fontSize: 12,
        formatter: function(value: number) {
          return value.toFixed(2)
        }
      }
    },
    series: seriesData,
  }
})

const getMetricConfig = (metricKey: MetricKey) => {
  const configs = {
    avgDuration: {
      name: '平均持续时间',
      color: '#3b82f6',
      yAxisIndex: 0
    },
    avgWaitTime: {
      name: '平均等待时间',
      color: '#f59e0b',
      yAxisIndex: 0
    },
    caseCount: {
      name: '案例数量',
      color: '#8b5cf6',
      yAxisIndex: 1
    }
  }
  return configs[metricKey]
}

const formatDuration = (milliseconds: number) => {
  if (!milliseconds || isNaN(milliseconds)) return '0分钟'

  const hours = milliseconds / (1000 * 60 * 60)
  if (hours < 1) {
    const minutes = Math.round(hours * 60)
    return `${minutes}分钟`
  } else if (hours < 24) {
    return `${hours.toFixed(1)}小时`
  } else {
    const days = Math.floor(hours / 24)
    const remainingHours = Math.round(hours % 24)
    return `${days}天${remainingHours}小时`
  }
}

const loadTrendsData = async (autoLoadPeriodDetails = true) => {
  try {
    isLoading.value = true
    isError.value = false

    if (!props.trends) {
      errorMessage.value = ''
      trendsData.value = null
      selectedPeriod.value = ''
      periodDetails.value = null
      return
    }

    const buildViewResult = (trendsObj: any) => {
      const gran = activeGranularity.value
      const granData = trendsObj?.granularities?.[gran]
      if (!granData) throw new Error(`No data available for granularity: ${gran}`)

      let ts = [...granData.timeSeries]
      if (timeRange.value && timeRange.value.length === 2) {
        const [startTime, endTime] = timeRange.value
        const start = new Date(startTime)
        const end = new Date(endTime)
        ts = ts.filter((item: any) => {
          const itemStart = new Date(item.start)
          const itemEnd = new Date(item.end)
          return itemStart >= start && itemEnd <= end
        })
      }

      return {
        overall: { ...granData.overall, throughputChange: 0 },
        timeSeries: ts.map((item: any) => ({ ...item, throughput: item.caseCount })),
        rankings: granData.rankings,
        activityDurations: trendsObj.activityDurations || null,
        resourceWaitTimes: trendsObj.resourceWaitTimes || null,
      }
    }

    const trends = props.trends as any
    const viewResult = buildViewResult(trends)

    trendsData.value = viewResult
    updateOverallTrend(viewResult)

    if (viewResult.timeSeries && viewResult.timeSeries.length > 0) {
      const lastPeriod = viewResult.timeSeries[viewResult.timeSeries.length - 1].period
      selectedPeriod.value = lastPeriod
      if (autoLoadPeriodDetails) {
        await handlePeriodClick(lastPeriod)
      }
    } else {
      selectedPeriod.value = ''
      periodDetails.value = null
    }

    await nextTick()
    await showChartsWhenReady()
  } catch (error: unknown) {
    console.error('加载趋势数据失败:', error)
    const msg = error instanceof Error ? error.message : '加载趋势数据失败'
    errorMessage.value = msg
    isError.value = true
    trendsData.value = null
    chartReady.value = false
  } finally {
    isLoading.value = false
    await nextTick()
    await showChartsWhenReady()
  }
}

const selectGranularity = (value: string) => {
  activeGranularity.value = value
  loadTrendsData(true) // 粒度变化时需要重新加载周期详情
}

const handleTimeRangeChange = (value: string[]) => {
  timeRange.value = value
  loadTrendsData(true) // 时间范围变化时需要重新加载周期详情
}

// 设置图表区域点击监听
const setupChartAreaClick = () => {
  nextTick(() => {
    if (trendChart.value) {
      const chart = trendChart.value.chart
      if (chart) {
        // 监听整个图表区域的点击事件
        chart.getZr().on('click', (event: any) => {
          // 获取点击位置
          const pointInPixel = [event.offsetX, event.offsetY]

          try {
            // 转换为数据坐标
            const pointInGrid = chart.convertFromPixel('grid', pointInPixel)

            if (pointInGrid && trendsData.value?.timeSeries) {
              const dataIndex = Math.round(pointInGrid[0])

              if (dataIndex >= 0 && dataIndex < trendsData.value.timeSeries.length) {
                const period = trendsData.value.timeSeries[dataIndex]?.period
                if (period) {
                  handlePeriodClick(period)
                }
              }
            }
          } catch (error) {
            console.log(error instanceof Error ? error.message : error)
          }
        })
      }
    }
  })
}

// 处理图表点击事件
const handleChartClick = (params: any, instance: any) => {
  let dataIndex = -1

  // 处理不同类型的点击事件
  if (params.componentType === 'series') {
    // 点击数据点或线条
    dataIndex = params.dataIndex
  } else if (params.componentType === 'xAxis') {
    // 点击 X 轴标签
    dataIndex = params.value
  } else if (params.componentType === undefined && params.event) {
    // 点击图表区域，通过坐标计算对应的数据索引
    const pointInPixel = [params.event.offsetX, params.event.offsetY]
    const pointInGrid = instance.convertFromPixel('grid', pointInPixel)

    if (pointInGrid && trendsData.value?.timeSeries) {
      dataIndex = Math.round(pointInGrid[0])
    }
  }

  if (dataIndex >= 0 && trendsData.value?.timeSeries && dataIndex < trendsData.value.timeSeries.length) {
    const period = trendsData.value.timeSeries[dataIndex]?.period
    if (period) {
      handlePeriodClick(period)
    }
  }
}

// 处理周期点击事件
const handlePeriodClick = async (period: string) => {
  try {
    selectedPeriod.value = period

    // 如果是同一个周期且参数没有变化，避免重复请求
    const currentParams = JSON.stringify({
      granularity: activeGranularity.value,
      requiredActivities: props.requiredActivities
    })

    if (lastLoadedPeriod.value === period + currentParams) {
      return
    }

    const params: any = {
      granularity: activeGranularity.value
    }

    // 添加活动节点过滤参数
    if (props.requiredActivities && props.requiredActivities.length > 0) {
      params.requiredActivities = props.requiredActivities
    }

    const result = await api.getPeriodDetails(processId, period, params)

    periodDetails.value = result
    lastLoadedPeriod.value = period + currentParams // 记录已加载的周期和参数

    // 更新排行榜和图表数据
    updatePeriodData(result)

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : '加载周期详情失败'
    ElMessage.error(errorMessage)
  }
}

// 更新周期相关的数据
const updatePeriodData = (data: any) => {
  // 更新活动耗时占比数据
  if (data.activityDurations) {
    trendsData.value.activityDurations = data.activityDurations
  }

  // 更新资源等待时间数据
  if (data.resourceWaitTimes) {
    trendsData.value.resourceWaitTimes = data.resourceWaitTimes
  }

  // 更新排行榜数据
  if (data.rankings) {
    trendsData.value.rankings = data.rankings
  }
}

const updateOverallTrend = (data: {
  overall: {
    avgDurationChange: number
    avgWaitTimeChange: number
    totalCases: number
  }
}) => {
  if (!data.overall) return

  overallTrend.value = {
    avgDurationChange: data.overall.avgDurationChange || 0,
    avgWaitTimeChange: data.overall.avgWaitTimeChange || 0,
    totalCases: data.overall.totalCases || 0
  }
}

const showDetailDialog = (item: TrendItem, type: 'improvement' | 'decline') => {
  selectedItem.value = item
  selectedItemType.value = type
  detailDialogVisible.value = true
}

watch(() => props.requiredActivities, (newVal, oldVal) => {
  // 只有在组件已挂载且值真正发生变化时才重新加载数据
  if (oldVal !== undefined && JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
    loadTrendsData(true)
  }
}, { deep: true })

watch(() => props.trends, async (val) => {
  if (val) {
    await loadTrendsData(true)
  }
})

onMounted(async () => {
  await loadTrendsData()
  setupContainerObserver()
})
</script>

<style lang="scss" scoped>
.performance-trends {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 1rem;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
  }
}

:deep(.el-card) {
  border-radius: 16px;
  border: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;

  &:last-child {
    margin-bottom: 0;
  }
}

// 性能分析整体容器样式
.performance-analysis-container {
  :deep(.el-card__body) {
    padding: 0;
  }

  .trend-chart-section,
  .ranking-section,
  .charts-section {
    padding: 1.5rem;
    border-bottom: 1px solid #f3f4f6;

    &:last-child {
      border-bottom: none;
    }

    :global(.dark) & {
      border-bottom-color: #374151;
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    gap: 1rem;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;
    }

    .header-left {
      flex: 1;
    }

    .header-right {
      flex-shrink: 0;
    }
  }

  .section-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #111827;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.6rem;

    :global(.dark) & {
      color: #ffffff;
    }
  }

  .chart-card {
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    padding: 1.5rem;

    :global(.dark) & {
      background: #1f2937;
      border-color: #374151;
    }

    .section-header {
      margin-bottom: 1rem;
    }

    .section-title {
      font-size: 1.125rem;
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
}

.card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.6rem;

  :global(.dark) & {
    color: #ffffff;
  }
}

.title-with-description {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  .chart-description {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
    line-height: 1.4;
    font-weight: 400;

    :global(.dark) & {
      color: #9ca3af;
    }
  }

  .time-range-info {
    font-size: 0.8rem;
    color: #059669;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-weight: 500;

    .time-icon {
      font-size: 0.875rem;
    }

    :global(.dark) & {
      color: #10b981;
    }
  }

  .ranking-time-badges {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: 0.75rem;

    .time-badge {
      display: inline-flex;
      align-items: baseline;
      justify-content: center;
      font-size: 0.65rem;
      font-weight: 500;
      padding: 0.25rem 0.6rem;
      border-radius: 12px;
      line-height: 1.2;
      white-space: nowrap;
      flex-shrink: 0;
      gap: 0.3rem;

      .badge-label {
        font-size: 0.6rem;
        font-weight: 600;
        opacity: 0.8;
        line-height: 1.2;
      }

      &.previous {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        color: #92400e;
        border: 1px solid #f59e0b;
      }

      &.current {
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        color: #1e40af;
        border: 1px solid #3b82f6;
      }
    }

    :global(.dark) & {
      .time-badge {
        &.previous {
          background: linear-gradient(135deg, #92400e 0%, #b45309 100%);
          color: #fde68a;
          border-color: #d97706;
        }

        &.current {
          background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
          color: #bfdbfe;
          border-color: #3b82f6;
        }
      }
    }
  }

  .title-time-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e40af;
    font-size: 0.7rem;
    font-weight: 500;
    padding: 0.25rem 0.6rem;
    border-radius: 12px;
    border: 1px solid #93c5fd;
    line-height: 1;
    white-space: nowrap;
    flex-shrink: 0;

    :global(.dark) & {
      background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
      color: #bfdbfe;
      border-color: #3b82f6;
    }
  }
}

.control-header {
  @extend .card-header;
}

.trends-title {
  @extend .card-title;
  font-size: 1.5rem;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
}

.time-range-selector {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1rem;
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(59, 130, 246, 0.08);
    border-color: rgba(59, 130, 246, 0.3);
  }

  :global(.dark) & {
    background: rgba(96, 165, 250, 0.05);
    border-color: rgba(96, 165, 250, 0.2);

    &:hover {
      background: rgba(96, 165, 250, 0.08);
      border-color: rgba(96, 165, 250, 0.3);
    }
  }

  .time-range-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #3b82f6;
    white-space: nowrap;

    :global(.dark) & {
      color: #60a5fa;
    }

    .time-icon {
      font-size: 1rem;
    }
  }

  .time-picker {
    min-width: 320px;
    flex: 1;

    @media (max-width: 768px) {
      min-width: 280px;
    }

    @media (max-width: 480px) {
      min-width: 100%;
    }
  }

  :deep(.el-date-editor) {
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    background: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    &:hover {
      border-color: #3b82f6;
      box-shadow: 0 4px 8px rgba(59, 130, 246, 0.15);
    }

    &.is-active,
    &:focus-within {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .el-input__wrapper {
      padding: 8px 12px;
    }

    .el-input__inner {
      font-size: 0.875rem;
      color: #374151;
    }

    .el-range-separator {
      color: #6b7280;
      font-weight: 500;
    }

    :global(.dark) & {
      border-color: #4b5563;
      background: #374151;

      &:hover {
        border-color: #60a5fa;
        box-shadow: 0 4px 8px rgba(96, 165, 250, 0.15);
      }

      &.is-active,
      &:focus-within {
        border-color: #60a5fa;
        box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
      }

      .el-input__inner {
        color: #e5e7eb;
        background: transparent;
      }

      .el-range-separator {
        color: #9ca3af;
      }
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;

    .time-range-label {
      justify-content: center;
    }
  }
}

.granularity-tabs {
  display: flex;
  background: #f3f4f6;
  border-radius: 12px;
  padding: 0.25rem;
  gap: 0.25rem;

  :global(.dark) & {
    background: #374151;
  }

  .granularity-tab {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;

    &:hover {
      color: #3b82f6;
      background: rgba(59, 130, 246, 0.1);
    }

    &.active {
      color: #ffffff;
      background: #3b82f6;
      box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
    }

    :global(.dark) & {
      color: #9ca3af;

      &:hover {
        color: #60a5fa;
        background: rgba(96, 165, 250, 0.1);
      }

      &.active {
        color: #ffffff;
        background: #3b82f6;
      }
    }
  }
}

.ranking-section-header {
  @extend .card-header;
}

.section-title {
  @extend .card-title;
}

.dimension-selector {
  :deep(.el-radio-button) {
    font-size: 0.875rem;

    .el-icon {
      margin-right: 0.25rem;
    }
  }
}

.ranking-content {
  .ranking-panel {
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    height: 100%;

    :global(.dark) & {
      background: #1f2937;
      border-color: #374151;
    }

    .panel-header {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #f3f4f6;

      :global(.dark) & {
        border-bottom-color: #374151;
      }

      .panel-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        font-weight: 600;
        color: #374151;

        :global(.dark) & {
          color: #d1d5db;
        }

        .title-icon {
          font-size: 1rem;

          &.improvement-icon {
            color: #10b981;
          }

          &.decline-icon {
            color: #ef4444;
          }
        }
      }
    }

    .ranking-list {
      .ranking-item {
        display: flex;
        align-items: center;
        padding: 0.875rem 1.25rem;
        border-bottom: 1px solid #f3f4f6;
        cursor: pointer;
        transition: background-color 0.15s ease;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background: #f8fafc;

          :global(.dark) & {
            background: #111827;
          }
        }

        :global(.dark) & {
          border-bottom-color: #374151;
        }

        .item-rank {
          width: 1.5rem;
          height: 1.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.875rem;
          font-weight: 600;
          margin-right: 1rem;
          flex-shrink: 0;
          color: #6b7280;

          :global(.dark) & {
            color: #9ca3af;
          }
        }

        .item-content {
          flex: 1;
          min-width: 0;

          .item-name {
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            :global(.dark) & {
              color: #e5e7eb;
            }
          }
        }

        .item-change {
          text-align: right;
          flex-shrink: 0;

          .change-value {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.125rem;
          }

          &.improvement-change {
            .change-value {
              color: #059669;
            }

            .change-label {
              color: #6b7280;
            }
          }

          &.decline-change {
            .change-value {
              color: #dc2626;
            }

            .change-label {
              color: #6b7280;
            }
          }
        }
      }

      .empty-state {
        padding: 2rem 1.5rem;
        text-align: center;
        color: #6b7280;

        .empty-icon {
          font-size: 2rem;
          margin-bottom: 0.5rem;
          opacity: 0.5;
        }

        .empty-text {
          font-size: 0.875rem;
          margin: 0;
        }
      }
    }
  }
}

.chart-header {
  @extend .card-header;
}

.chart-title {
  @extend .card-title;
}

.chart-wrapper {
  width: 100%;
  height: 380px;
  display: flex;
  align-items: center;
  justify-content: center;

  .chart {
    width: 100%;
    height: 100%;
  }
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 0.875rem;
  gap: 0.5rem;

  .placeholder-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  .placeholder-text {
    font-size: 0.875rem;
    margin: 0;
  }
}

.trend-chart-card {
  .chart-container {
    position: relative;
    width: 100%;
    height: 380px;

    .trend-chart {
      width: 100%;
      height: 100%;
    }
  }
}

.period-selector {
  @extend .dimension-selector;
}

.metric-checkbox-group {
  :deep(.el-checkbox) {
    margin-right: 1rem;
    font-size: 0.875rem;
  }
}

.charts-row {
  margin-top: 0;

  .el-col {
    margin-bottom: 1.5rem;

    @media (max-width: 768px) {
      margin-bottom: 1rem;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// ===== 详情对话框样式 =====
:deep(.performance-detail-dialog) {
  .el-dialog__header {
    padding: 0;
    border-bottom: 1px solid #e5e7eb;
  }

  .el-dialog__body {
    padding: 2rem;
  }

  .el-dialog__footer {
    padding: 1rem 2rem;
    border-top: 1px solid #e5e7eb;
  }

  :global(.dark) & {
    .el-dialog__header {
      border-bottom-color: #374151;
    }

    .el-dialog__footer {
      border-top-color: #374151;
    }
  }
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;

  .dialog-title {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;

    :global(.dark) & {
      color: #f9fafb;
    }
  }

  .close-btn {
    width: 2rem;
    height: 2rem;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;

    &:hover {
      background: #f3f4f6;
      color: #374151;
    }

    :global(.dark) & {
      color: #9ca3af;

      &:hover {
        background: #374151;
        color: #d1d5db;
      }
    }
  }
}

.detail-content {
  .change-metric {
    text-align: center;
    margin-bottom: 2rem;

    .metric-label {
      font-size: 0.875rem;
      color: #6b7280;
      margin-bottom: 0.75rem;

      :global(.dark) & {
        color: #9ca3af;
      }
    }

    .metric-value {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      font-size: 1.75rem;
      font-weight: 700;

      .trend-icon {
        font-size: 1.25rem;
      }

      &.improvement {
        color: #059669;
      }

      &.decline {
        color: #dc2626;
      }
    }
  }

  .data-comparison {
    .comparison-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 0;
      border-bottom: 1px solid #f3f4f6;

      &:last-child {
        border-bottom: none;
      }

      :global(.dark) & {
        border-bottom-color: #374151;
      }

      .item-label {
        font-size: 0.875rem;
        color: #6b7280;
        font-weight: 500;

        :global(.dark) & {
          color: #9ca3af;
        }
      }

      .item-value {
        font-size: 1rem;
        font-weight: 600;
        color: #111827;

        :global(.dark) & {
          color: #f9fafb;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .performance-trends {
    padding: 0.5rem;
  }

  .ranking-content {
    .ranking-panel {
      .ranking-list {
        .ranking-item {
          padding: 0.75rem 1rem;

          .item-rank {
            width: 1.5rem;
            height: 1.5rem;
            font-size: 0.625rem;
            margin-right: 0.75rem;
          }

          .item-content {
            .item-name {
              font-size: 0.8125rem;
            }
          }

          .item-change {
            .change-value {
              font-size: 0.875rem;
            }

            .change-label {
              font-size: 0.6875rem;
            }
          }
        }
      }
    }
  }

  .chart-container {
    height: 320px !important;
  }

  .chart-wrapper {
    height: 340px !important;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>