<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="handleClose"
    :title="dialogTitle"
    width="90%"
    :before-close="handleClose"
    class="dfg-detail-dialog"
    destroy-on-close
  >
    <div v-if="loading" class="loading-container">
      <el-icon class="loading-icon">
        <Loading />
      </el-icon>
      <p class="loading-text">加载详细信息中...</p>
    </div>

    <div v-else-if="error" class="error-container">
      <el-icon class="error-icon">
        <Warning />
      </el-icon>
      <p class="error-text">{{ error }}</p>
      <el-button @click="retryLoad" type="primary">重试</el-button>
    </div>

    <div v-else-if="nodeDetail" class="detail-content">
      <!-- 节点详细信息 -->
      <div class="detail-header">
        <h3 class="detail-title">{{ nodeDetail.label }}</h3>
        <div class="detail-subtitle">活动详细分析</div>
      </div>

      <div class="detail-sections">
        <!-- 基础统计 -->
        <div class="section basic-stats">
          <h4 class="section-title">基础统计</h4>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ nodeDetail.basicStats.frequency }}</div>
              <div class="stat-label">执行次数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ formatDuration(nodeDetail.basicStats.avgDuration) }}</div>
              <div class="stat-label">平均耗时</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ formatDuration(nodeDetail.basicStats.minDuration) }}</div>
              <div class="stat-label">最短耗时</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ formatDuration(nodeDetail.basicStats.maxDuration) }}</div>
              <div class="stat-label">最长耗时</div>
            </div>
          </div>
        </div>

        <!-- 时间分布图表 -->
        <div class="section chart-section">
          <h4 class="section-title">时间分布</h4>
          <div class="chart-container">
            <ClientOnly>
              <v-chart :option="timeDistributionOption" class="chart" />
              <template #fallback>
                <div class="chart-loading">图表加载中...</div>
              </template>
            </ClientOnly>
          </div>
        </div>

        <!-- 路径分析 -->
        <div class="section path-analysis">
          <h4 class="section-title">路径分析</h4>
          <div class="path-grid">
            <div class="path-item">
              <h5>前置活动</h5>
              <div class="path-list">
                <div
                  v-for="pred in nodeDetail.pathAnalysis.predecessors.slice(0, 5)"
                  :key="pred.activity"
                  class="path-entry"
                >
                  <span class="activity-name">{{ pred.activity }}</span>
                  <span class="activity-percentage">{{ pred.percentage.toFixed(1) }}%</span>
                </div>
              </div>
            </div>
            <div class="path-item">
              <h5>后续活动</h5>
              <div class="path-list">
                <div
                  v-for="succ in nodeDetail.pathAnalysis.successors.slice(0, 5)"
                  :key="succ.activity"
                  class="path-entry"
                >
                  <span class="activity-name">{{ succ.activity }}</span>
                  <span class="activity-percentage">{{ succ.percentage.toFixed(1) }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 时间模式 -->
        <div class="section time-patterns">
          <h4 class="section-title">时间模式</h4>
          <div class="patterns-grid">
            <div class="pattern-item">
              <h5>小时分布</h5>
              <div class="chart-container small">
                <ClientOnly>
                  <v-chart :option="hourlyDistributionOption" class="chart" />
                </ClientOnly>
              </div>
            </div>
            <div class="pattern-item">
              <h5>星期分布</h5>
              <div class="chart-container small">
                <ClientOnly>
                  <v-chart :option="weeklyDistributionOption" class="chart" />
                </ClientOnly>
              </div>
            </div>
          </div>
        </div>

        <!-- 异常检测 -->
        <div v-if="nodeDetail.anomalies.length > 0" class="section anomalies">
          <h4 class="section-title">异常检测</h4>
          <div class="anomaly-list">
            <div
              v-for="anomaly in nodeDetail.anomalies.slice(0, 10)"
              :key="anomaly.caseId"
              class="anomaly-item"
              :class="`severity-${anomaly.severity}`"
            >
              <div class="anomaly-case">案例: {{ anomaly.caseId }}</div>
              <div class="anomaly-duration">耗时: {{ formatDuration(anomaly.duration) }}</div>
              <div class="anomaly-reason">{{ anomaly.reason }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="edgeDetail" class="detail-content">
      <!-- 连接详细信息 -->
      <div class="detail-header">
        <h3 class="detail-title">{{ edgeDetail.source }} → {{ edgeDetail.target }}</h3>
        <div class="detail-subtitle">连接详细分析</div>
      </div>

      <div class="detail-sections">
        <!-- 基础统计 -->
        <div class="section basic-stats">
          <h4 class="section-title">基础统计</h4>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ edgeDetail.basicStats.frequency }}</div>
              <div class="stat-label">执行次数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ edgeDetail.basicStats.percentage.toFixed(1) }}%</div>
              <div class="stat-label">占比</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ formatDuration(edgeDetail.basicStats.avgDuration) }}</div>
              <div class="stat-label">平均转换耗时</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ formatDuration(edgeDetail.basicStats.medianDuration) }}</div>
              <div class="stat-label">中位数耗时</div>
            </div>
          </div>
        </div>

        <!-- 时间分布图表 -->
        <div class="section chart-section">
          <h4 class="section-title">转换时间分布</h4>
          <div class="chart-container">
            <ClientOnly>
              <v-chart :option="edgeTimeDistributionOption" class="chart" />
              <template #fallback>
                <div class="chart-loading">图表加载中...</div>
              </template>
            </ClientOnly>
          </div>
        </div>

        <!-- 趋势分析 -->
        <div class="section trend-analysis">
          <h4 class="section-title">趋势分析</h4>
          <div class="trend-info">
            <div class="trend-indicator" :class="`trend-${edgeDetail.trendAnalysis.trend}`">
              <span class="trend-label">趋势:</span>
              <span class="trend-value">{{ getTrendText(edgeDetail.trendAnalysis.trend) }}</span>
            </div>
          </div>
          <div class="chart-container">
            <ClientOnly>
              <v-chart :option="trendAnalysisOption" class="chart" />
            </ClientOnly>
          </div>
        </div>

        <!-- 效率对比 -->
        <div v-if="edgeDetail.efficiencyAnalysis.comparedToOtherPaths.length > 0" class="section efficiency">
          <h4 class="section-title">效率对比</h4>
          <div class="efficiency-list">
            <div
              v-for="comparison in edgeDetail.efficiencyAnalysis.comparedToOtherPaths.slice(0, 5)"
              :key="comparison.alternativePath"
              class="efficiency-item"
            >
              <div class="path-name">{{ comparison.alternativePath }}</div>
              <div class="comparison-stats">
                <span class="freq-diff" :class="comparison.frequencyDiff > 0 ? 'positive' : 'negative'">
                  频次差异: {{ comparison.frequencyDiff > 0 ? '+' : '' }}{{ comparison.frequencyDiff }}
                </span>
                <span class="duration-diff" :class="comparison.durationDiff < 0 ? 'positive' : 'negative'">
                  耗时差异: {{ comparison.durationDiff > 0 ? '+' : '' }}{{ formatDuration(Math.abs(comparison.durationDiff)) }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 案例详情 -->
        <div class="section case-details">
          <h4 class="section-title">相关案例详情 ({{ edgeDetail.caseDetails.length }})</h4>
          <div class="case-list">
            <div class="case-list-header">
              <div class="case-header-item">案例ID</div>
              <div class="case-header-item">开始时间</div>
              <div class="case-header-item">结束时间</div>
              <div class="case-header-item">转换耗时</div>
            </div>
            <div class="case-list-body">
              <div
                v-for="caseDetail in edgeDetail.caseDetails.slice(0, 20)"
                :key="caseDetail.caseId"
                class="case-item"
              >
                <div class="case-data-item case-id">{{ caseDetail.caseId }}</div>
                <div class="case-data-item">{{ formatDateTime(caseDetail.sourceTime) }}</div>
                <div class="case-data-item">{{ formatDateTime(caseDetail.targetTime) }}</div>
                <div class="case-data-item duration">{{ formatDuration(caseDetail.duration) }}</div>
              </div>
            </div>
            <div v-if="edgeDetail.caseDetails.length > 20" class="case-list-footer">
              <span class="more-cases-hint">显示前20条，共{{ edgeDetail.caseDetails.length }}条案例</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="exportData">导出数据</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { Loading, Warning } from '@element-plus/icons-vue'
import { useApi } from '~/utils/api'
import type { NodeDetailInfo, EdgeDetailInfo } from '~/types'

// 动态导入VChart以避免SSR问题
const VChart = defineAsyncComponent(() => import('vue-echarts'))

interface Props {
  visible: boolean
  processId: number
  nodeId?: string
  sourceId?: string
  targetId?: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const error = ref('')
const nodeDetail = ref<NodeDetailInfo | null>(null)
const edgeDetail = ref<EdgeDetailInfo | null>(null)

// API实例
const api = useApi()

// 计算属性
const dialogTitle = computed(() => {
  if (nodeDetail.value) {
    return `活动详情 - ${nodeDetail.value.label}`
  }
  if (edgeDetail.value) {
    return `连接详情 - ${edgeDetail.value.source} → ${edgeDetail.value.target}`
  }
  return '详细信息'
})

// 监听visible变化，加载数据
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    loadDetailData()
  } else {
    // 清理数据
    nodeDetail.value = null
    edgeDetail.value = null
    error.value = ''
  }
})

// 加载详细数据
const loadDetailData = async () => {
  loading.value = true
  error.value = ''

  try {
    if (props.nodeId) {
      // 加载节点详细信息
      try {
        nodeDetail.value = await api.getNodeDetailInfo(props.processId, props.nodeId)
      } catch (err: any) {
        // 如果API调用失败，使用模拟数据进行演示
        console.warn('API调用失败，使用模拟数据:', err.message)
        nodeDetail.value = createMockNodeDetail(props.nodeId)
      }
    } else if (props.sourceId && props.targetId) {
      // 加载连接详细信息
      try {
        edgeDetail.value = await api.getEdgeDetailInfo(props.processId, props.sourceId, props.targetId)
      } catch (err: any) {
        // 如果API调用失败，使用模拟数据进行演示
        console.warn('API调用失败，使用模拟数据:', err.message)
        edgeDetail.value = createMockEdgeDetail(props.sourceId, props.targetId)
      }
    }
  } catch (err: any) {
    error.value = err.message || '加载详细信息失败'
  } finally {
    loading.value = false
  }
}

// 创建模拟节点数据
const createMockNodeDetail = (nodeId: string) => {
  // 生成模拟的持续时间数据（毫秒）
  const baseDuration = 3600000 // 1小时基准
  const mockDurations = generateMockDurations(156, baseDuration, 0.4) // 156个样本，40%变异系数

  // 动态生成时间分布
  const timeDistribution = generateTimeDistribution(mockDurations)

  return {
    id: nodeId,
    label: nodeId,
    basicStats: {
      frequency: 156,
      avgDuration: mockDurations.reduce((sum, d) => sum + d, 0) / mockDurations.length,
      minDuration: Math.min(...mockDurations),
      maxDuration: Math.max(...mockDurations),
      medianDuration: mockDurations.sort((a, b) => a - b)[Math.floor(mockDurations.length / 2)],
      stdDeviation: calculateStandardDeviation(mockDurations)
    },
    timeDistribution,
    resourceAnalysis: {
      resources: [
        { resource: '系统自动', frequency: 89, avgDuration: 3200000, efficiency: 0.92 },
        { resource: '人工处理', frequency: 67, avgDuration: 4100000, efficiency: 0.78 }
      ]
    },
    pathAnalysis: {
      predecessors: [
        { activity: '申请提交', frequency: 89, percentage: 57.1 },
        { activity: '初步审核', frequency: 45, percentage: 28.8 },
        { activity: '补充材料', frequency: 22, percentage: 14.1 }
      ],
      successors: [
        { activity: '审核通过', frequency: 112, percentage: 71.8 },
        { activity: '需要补充', frequency: 32, percentage: 20.5 },
        { activity: '审核拒绝', frequency: 12, percentage: 7.7 }
      ]
    },
    timePatterns: {
      hourlyDistribution: Array.from({ length: 24 }, (_, i) => ({
        hour: i,
        count: Math.floor(Math.random() * 20) + (i >= 9 && i <= 17 ? 10 : 2)
      })),
      weeklyDistribution: Array.from({ length: 7 }, (_, i) => ({
        dayOfWeek: i,
        count: Math.floor(Math.random() * 30) + (i >= 1 && i <= 5 ? 20 : 5)
      }))
    },
    anomalies: [
      { caseId: 'CASE_001', duration: 14400000, reason: '执行时间异常长', severity: 'high' as const },
      { caseId: 'CASE_045', duration: 900000, reason: '执行时间异常短', severity: 'medium' as const },
      { caseId: 'CASE_089', duration: 12600000, reason: '执行时间异常长', severity: 'high' as const }
    ]
  }
}

// 创建模拟连接数据
const createMockEdgeDetail = (sourceId: string, targetId: string) => {
  // 生成模拟的转换时间数据（毫秒）
  const baseDuration = 1800000 // 30分钟基准
  const mockDurations = generateMockDurations(89, baseDuration, 0.6) // 89个样本，60%变异系数

  // 动态生成时间分布
  const timeDistribution = generateTimeDistribution(mockDurations)

  return {
    source: sourceId,
    target: targetId,
    basicStats: {
      frequency: 89,
      avgDuration: mockDurations.reduce((sum, d) => sum + d, 0) / mockDurations.length,
      minDuration: Math.min(...mockDurations),
      maxDuration: Math.max(...mockDurations),
      medianDuration: mockDurations.sort((a, b) => a - b)[Math.floor(mockDurations.length / 2)],
      percentage: 15.2
    },
    timeDistribution,
    trendAnalysis: {
      timeSeriesData: [
        { period: '2024-01', frequency: 23, avgDuration: 1900000 },
        { period: '2024-02', frequency: 28, avgDuration: 1750000 },
        { period: '2024-03', frequency: 31, avgDuration: 1650000 },
        { period: '2024-04', frequency: 35, avgDuration: 1600000 },
        { period: '2024-05', frequency: 38, avgDuration: 1550000 },
        { period: '2024-06', frequency: 42, avgDuration: 1500000 }
      ],
      trend: 'increasing' as const
    },
    efficiencyAnalysis: {
      comparedToOtherPaths: [
        { alternativePath: `${sourceId} → 其他路径A`, frequencyDiff: 15, durationDiff: -300000 },
        { alternativePath: `${sourceId} → 其他路径B`, frequencyDiff: -8, durationDiff: 600000 }
      ]
    },
    contextAnalysis: {
      commonPredecessors: [
        { activity: '前置活动A', frequency: 45 },
        { activity: '前置活动B', frequency: 32 }
      ],
      commonSuccessors: [
        { activity: '后续活动A', frequency: 52 },
        { activity: '后续活动B', frequency: 28 }
      ]
    },
    caseDetails: generateMockCaseDetails(89, mockDurations)
  }
}

// 生成模拟持续时间数据
const generateMockDurations = (count: number, baseDuration: number, variationCoeff: number): number[] => {
  const durations: number[] = []

  for (let i = 0; i < count; i++) {
    // 使用对数正态分布生成更真实的时间数据
    const randomFactor = Math.exp((Math.random() - 0.5) * variationCoeff * 2)
    const duration = Math.max(baseDuration * randomFactor, baseDuration * 0.1) // 最小值为基准的10%
    durations.push(Math.round(duration))
  }

  return durations.sort((a, b) => a - b)
}

// 生成模拟案例详情数据
const generateMockCaseDetails = (count: number, durations: number[]) => {
  const caseDetails = []
  const baseTime = new Date('2024-01-01T09:00:00Z').getTime()

  for (let i = 0; i < count; i++) {
    const caseId = `CASE_${String(i + 1).padStart(4, '0')}`
    const sourceTime = new Date(baseTime + i * 86400000 + Math.random() * 28800000) // 随机分布在工作时间内
    const duration = durations[i] || durations[Math.floor(Math.random() * durations.length)]
    const targetTime = new Date(sourceTime.getTime() + duration)

    caseDetails.push({
      caseId,
      sourceTime,
      targetTime,
      duration
    })
  }

  return caseDetails.sort((a, b) => a.sourceTime.getTime() - b.sourceTime.getTime())
}

// 计算标准差
const calculateStandardDeviation = (values: number[]): number => {
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
  return Math.sqrt(variance)
}

// 生成智能时间分布
const generateTimeDistribution = (durations: number[]) => {
  if (durations.length === 0) {
    return {
      intervals: [],
      quartiles: { q1: 0, q2: 0, q3: 0 }
    }
  }

  const min = Math.min(...durations)
  const max = Math.max(...durations)
  const range = max - min

  // 根据数据范围选择合适的区间数量
  let intervalCount = 8
  if (durations.length < 20) {
    intervalCount = 5
  } else if (durations.length > 100) {
    intervalCount = 12
  }

  // 计算合适的区间大小
  const rawStep = range / intervalCount
  const step = roundToNiceNumber(rawStep)

  // 调整起始点到合适的整数
  const adjustedMin = Math.floor(min / step) * step
  const adjustedMax = Math.ceil(max / step) * step

  const intervals: Array<{ range: string; count: number; percentage: number }> = []

  for (let start = adjustedMin; start < adjustedMax; start += step) {
    const end = start + step
    const count = durations.filter(d => d >= start && d < end).length

    // 只包含有数据的区间
    if (count > 0 || intervals.length === 0) {
      intervals.push({
        range: `${formatDuration(start)} - ${formatDuration(end)}`,
        count,
        percentage: (count / durations.length) * 100,
      })
    }
  }

  // 计算四分位数
  const sortedDurations = [...durations].sort((a, b) => a - b)
  const quartiles = {
    q1: sortedDurations[Math.floor(sortedDurations.length * 0.25)],
    q2: sortedDurations[Math.floor(sortedDurations.length * 0.5)],
    q3: sortedDurations[Math.floor(sortedDurations.length * 0.75)],
  }

  return {
    intervals,
    quartiles
  }
}

// 将数字四舍五入到合适的"好看"数字
const roundToNiceNumber = (value: number): number => {
  const magnitude = Math.pow(10, Math.floor(Math.log10(value)))
  const normalized = value / magnitude

  let nice: number
  if (normalized <= 1) {
    nice = 1
  } else if (normalized <= 2) {
    nice = 2
  } else if (normalized <= 5) {
    nice = 5
  } else {
    nice = 10
  }

  return nice * magnitude
}

// 重试加载
const retryLoad = () => {
  loadDetailData()
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 格式化持续时间 - 统一格式化逻辑，与图表标签保持一致
const formatDuration = (milliseconds: number): string => {
  if (!milliseconds || milliseconds <= 0) {
    return '0秒'
  }

  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) {
    return `${days}天${hours % 24}小时`
  } else if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟`
  } else if (seconds > 0) {
    return `${seconds}秒`
  } else {
    return '<1秒'
  }
}

// 格式化日期时间
const formatDateTime = (dateTime: Date | string): string => {
  const date = new Date(dateTime)
  if (isNaN(date.getTime())) {
    return '无效时间'
  }

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 获取趋势文本
const getTrendText = (trend: string): string => {
  switch (trend) {
    case 'increasing': return '上升'
    case 'decreasing': return '下降'
    case 'stable': return '稳定'
    default: return '未知'
  }
}

// 导出数据
const exportData = () => {
  const data = nodeDetail.value || edgeDetail.value
  if (data) {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `dfg-detail-${Date.now()}.json`
    a.click()
    URL.revokeObjectURL(url)
  }
}

// ECharts图表配置
const timeDistributionOption = computed(() => {
  if (!nodeDetail.value?.timeDistribution.intervals) return null

  return {
    title: {
      text: '执行时间分布',
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const data = params[0]
        return `${data.name}<br/>数量: ${data.value}<br/>占比: ${nodeDetail.value?.timeDistribution.intervals[data.dataIndex]?.percentage.toFixed(1)}%`
      }
    },
    xAxis: {
      type: 'category',
      data: nodeDetail.value.timeDistribution.intervals.map(item => item.range),
      axisLabel: {
        rotate: 45,
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      name: '数量'
    },
    series: [{
      type: 'bar',
      data: nodeDetail.value.timeDistribution.intervals.map(item => item.count),
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#3b82f6' },
            { offset: 1, color: '#1d4ed8' }
          ]
        }
      },
      barWidth: '60%'
    }]
  }
})

const hourlyDistributionOption = computed(() => {
  if (!nodeDetail.value?.timePatterns.hourlyDistribution) return null

  return {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const data = params[0]
        return `${data.name}:00<br/>执行次数: ${data.value}`
      }
    },
    xAxis: {
      type: 'category',
      data: nodeDetail.value.timePatterns.hourlyDistribution.map(item => item.hour),
      axisLabel: {
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      name: '次数'
    },
    series: [{
      type: 'line',
      data: nodeDetail.value.timePatterns.hourlyDistribution.map(item => item.count),
      smooth: true,
      lineStyle: {
        color: '#10b981'
      },
      itemStyle: {
        color: '#10b981'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(16, 185, 129, 0.3)' },
            { offset: 1, color: 'rgba(16, 185, 129, 0.1)' }
          ]
        }
      }
    }]
  }
})

const weeklyDistributionOption = computed(() => {
  if (!nodeDetail.value?.timePatterns.weeklyDistribution) return null

  const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

  return {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const data = params[0]
        return `${weekDays[data.dataIndex]}<br/>执行次数: ${data.value}`
      }
    },
    xAxis: {
      type: 'category',
      data: weekDays,
      axisLabel: {
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      name: '次数'
    },
    series: [{
      type: 'bar',
      data: nodeDetail.value.timePatterns.weeklyDistribution.map(item => item.count),
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#f59e0b' },
            { offset: 1, color: '#d97706' }
          ]
        }
      },
      barWidth: '50%'
    }]
  }
})

const edgeTimeDistributionOption = computed(() => {
  if (!edgeDetail.value?.timeDistribution.intervals) return null

  return {
    title: {
      text: '转换时间分布',
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const data = params[0]
        return `${data.name}<br/>数量: ${data.value}<br/>占比: ${edgeDetail.value?.timeDistribution.intervals[data.dataIndex]?.percentage.toFixed(1)}%`
      }
    },
    xAxis: {
      type: 'category',
      data: edgeDetail.value.timeDistribution.intervals.map(item => item.range),
      axisLabel: {
        rotate: 45,
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      name: '数量'
    },
    series: [{
      type: 'bar',
      data: edgeDetail.value.timeDistribution.intervals.map(item => item.count),
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#8b5cf6' },
            { offset: 1, color: '#7c3aed' }
          ]
        }
      },
      barWidth: '60%'
    }]
  }
})

const trendAnalysisOption = computed(() => {
  if (!edgeDetail.value?.trendAnalysis.timeSeriesData) return null

  return {
    title: {
      text: '频次趋势',
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const data = params[0]
        const seriesData = edgeDetail.value?.trendAnalysis.timeSeriesData[data.dataIndex]
        return `${data.name}<br/>频次: ${data.value}<br/>平均耗时: ${formatDuration(seriesData?.avgDuration || 0)}`
      }
    },
    xAxis: {
      type: 'category',
      data: edgeDetail.value.trendAnalysis.timeSeriesData.map(item => item.period),
      axisLabel: {
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      name: '频次'
    },
    series: [{
      type: 'line',
      data: edgeDetail.value.trendAnalysis.timeSeriesData.map(item => item.frequency),
      smooth: true,
      lineStyle: {
        color: '#ef4444',
        width: 2
      },
      itemStyle: {
        color: '#ef4444'
      },
      symbol: 'circle',
      symbolSize: 6
    }]
  }
})
</script>

<style lang="scss" scoped>
.dfg-detail-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(226, 232, 240, 0.5);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    .dark & {
      background: rgba(31, 41, 55, 0.95);
      border-color: rgba(75, 85, 99, 0.5);
    }
  }

  :deep(.el-dialog__header) {
    padding: 24px 24px 0;
    border-bottom: none;

    .el-dialog__title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;

      .dark & {
        color: #f9fafb;
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(156, 163, 175, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(156, 163, 175, 0.3);
      border-radius: 3px;

      &:hover {
        background: rgba(156, 163, 175, 0.5);
      }
    }
  }

  :deep(.el-dialog__footer) {
    padding: 16px 24px 24px;
    border-top: 1px solid rgba(226, 232, 240, 0.5);

    .dark & {
      border-top-color: rgba(75, 85, 99, 0.5);
    }
  }
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;

  .loading-icon,
  .error-icon {
    font-size: 3rem;
    margin-bottom: 16px;
    color: #6b7280;

    .dark & {
      color: #9ca3af;
    }
  }

  .loading-icon {
    animation: spin 1s linear infinite;
  }

  .loading-text,
  .error-text {
    font-size: 1rem;
    color: #6b7280;
    margin-bottom: 16px;

    .dark & {
      color: #9ca3af;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.detail-content {
  .detail-header {
    margin-bottom: 32px;
    text-align: center;

    .detail-title {
      font-size: 1.5rem;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 8px;

      .dark & {
        color: #f9fafb;
      }
    }

    .detail-subtitle {
      font-size: 0.875rem;
      color: #6b7280;

      .dark & {
        color: #9ca3af;
      }
    }
  }

  .detail-sections {
    display: flex;
    flex-direction: column;
    gap: 32px;
  }

  .section {
    background: rgba(248, 250, 252, 0.8);
    border-radius: 12px;
    padding: 24px;
    border: 1px solid rgba(226, 232, 240, 0.5);

    .dark & {
      background: rgba(55, 65, 81, 0.8);
      border-color: rgba(75, 85, 99, 0.5);
    }

    .section-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: #374151;
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 2px solid rgba(59, 130, 246, 0.2);

      .dark & {
        color: #e5e7eb;
      }
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;

    .stat-item {
      text-align: center;
      padding: 20px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 8px;
      border: 1px solid rgba(226, 232, 240, 0.3);

      .dark & {
        background: rgba(31, 41, 55, 0.8);
        border-color: rgba(75, 85, 99, 0.3);
      }

      .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #3b82f6;
        margin-bottom: 8px;

        .dark & {
          color: #60a5fa;
        }
      }

      .stat-label {
        font-size: 0.875rem;
        color: #6b7280;

        .dark & {
          color: #9ca3af;
        }
      }
    }
  }

  .chart-container {
    height: 300px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    border: 1px solid rgba(226, 232, 240, 0.3);
    overflow: hidden;

    .dark & {
      background: rgba(31, 41, 55, 0.8);
      border-color: rgba(75, 85, 99, 0.3);
    }

    &.small {
      height: 200px;
    }

    .chart {
      width: 100%;
      height: 100%;
    }

    .chart-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #6b7280;
      font-size: 0.875rem;

      .dark & {
        color: #9ca3af;
      }
    }
  }

  .path-analysis {
    .path-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }

    .path-item {
      h5 {
        font-size: 1rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 16px;

        .dark & {
          color: #e5e7eb;
        }
      }

      .path-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .path-entry {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 6px;
        border: 1px solid rgba(226, 232, 240, 0.3);

        .dark & {
          background: rgba(31, 41, 55, 0.6);
          border-color: rgba(75, 85, 99, 0.3);
        }

        .activity-name {
          font-size: 0.875rem;
          color: #374151;
          flex: 1;

          .dark & {
            color: #e5e7eb;
          }
        }

        .activity-percentage {
          font-size: 0.875rem;
          font-weight: 600;
          color: #3b82f6;

          .dark & {
            color: #60a5fa;
          }
        }
      }
    }
  }

  .time-patterns {
    .patterns-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }

    .pattern-item {
      h5 {
        font-size: 1rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 16px;
        text-align: center;

        .dark & {
          color: #e5e7eb;
        }
      }
    }
  }

  .anomalies {
    .anomaly-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
      max-height: 300px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(156, 163, 175, 0.1);
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(156, 163, 175, 0.3);
        border-radius: 2px;
      }
    }

    .anomaly-item {
      padding: 16px;
      border-radius: 8px;
      border-left: 4px solid;

      &.severity-low {
        background: rgba(34, 197, 94, 0.1);
        border-left-color: #22c55e;

        .dark & {
          background: rgba(34, 197, 94, 0.2);
        }
      }

      &.severity-medium {
        background: rgba(245, 158, 11, 0.1);
        border-left-color: #f59e0b;

        .dark & {
          background: rgba(245, 158, 11, 0.2);
        }
      }

      &.severity-high {
        background: rgba(239, 68, 68, 0.1);
        border-left-color: #ef4444;

        .dark & {
          background: rgba(239, 68, 68, 0.2);
        }
      }

      .anomaly-case {
        font-size: 0.875rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 4px;

        .dark & {
          color: #e5e7eb;
        }
      }

      .anomaly-duration {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 4px;

        .dark & {
          color: #9ca3af;
        }
      }

      .anomaly-reason {
        font-size: 0.875rem;
        color: #6b7280;

        .dark & {
          color: #9ca3af;
        }
      }
    }
  }

  .trend-analysis {
    .trend-info {
      margin-bottom: 20px;
      text-align: center;
    }

    .trend-indicator {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 600;

      &.trend-increasing {
        background: rgba(34, 197, 94, 0.1);
        color: #22c55e;

        .dark & {
          background: rgba(34, 197, 94, 0.2);
        }
      }

      &.trend-decreasing {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;

        .dark & {
          background: rgba(239, 68, 68, 0.2);
        }
      }

      &.trend-stable {
        background: rgba(156, 163, 175, 0.1);
        color: #6b7280;

        .dark & {
          background: rgba(156, 163, 175, 0.2);
          color: #9ca3af;
        }
      }
    }
  }

  .efficiency {
    .efficiency-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .efficiency-item {
      padding: 16px;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 8px;
      border: 1px solid rgba(226, 232, 240, 0.3);

      .dark & {
        background: rgba(31, 41, 55, 0.6);
        border-color: rgba(75, 85, 99, 0.3);
      }

      .path-name {
        font-size: 0.875rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 8px;

        .dark & {
          color: #e5e7eb;
        }
      }

      .comparison-stats {
        display: flex;
        gap: 16px;
        font-size: 0.875rem;

        .freq-diff,
        .duration-diff {
          &.positive {
            color: #22c55e;
          }

          &.negative {
            color: #ef4444;
          }
        }
      }
    }
  }

  .case-details {
    .case-list {
      background: rgba(255, 255, 255, 0.6);
      border-radius: 8px;
      border: 1px solid rgba(226, 232, 240, 0.3);
      overflow: hidden;

      .dark & {
        background: rgba(31, 41, 55, 0.6);
        border-color: rgba(75, 85, 99, 0.3);
      }
    }

    .case-list-header {
      display: grid;
      grid-template-columns: 2fr 2fr 2fr 1.5fr;
      gap: 16px;
      padding: 12px 16px;
      background: rgba(249, 250, 251, 0.8);
      border-bottom: 1px solid rgba(226, 232, 240, 0.5);
      font-weight: 600;
      font-size: 0.875rem;
      color: #374151;

      .dark & {
        background: rgba(17, 24, 39, 0.8);
        border-bottom-color: rgba(75, 85, 99, 0.5);
        color: #e5e7eb;
      }
    }

    .case-list-body {
      max-height: 400px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(156, 163, 175, 0.1);
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(156, 163, 175, 0.3);
        border-radius: 3px;
      }
    }

    .case-item {
      display: grid;
      grid-template-columns: 2fr 2fr 2fr 1.5fr;
      gap: 16px;
      padding: 12px 16px;
      border-bottom: 1px solid rgba(226, 232, 240, 0.3);
      font-size: 0.875rem;
      transition: background-color 0.2s;

      &:hover {
        background: rgba(249, 250, 251, 0.5);

        .dark & {
          background: rgba(17, 24, 39, 0.5);
        }
      }

      &:last-child {
        border-bottom: none;
      }

      .dark & {
        border-bottom-color: rgba(75, 85, 99, 0.3);
      }
    }

    .case-data-item {
      color: #6b7280;
      word-break: break-all;

      .dark & {
        color: #9ca3af;
      }

      &.case-id {
        font-weight: 500;
        color: #374151;

        .dark & {
          color: #e5e7eb;
        }
      }

      &.duration {
        font-weight: 500;
        color: #059669;

        .dark & {
          color: #10b981;
        }
      }
    }

    .case-list-footer {
      padding: 12px 16px;
      background: rgba(249, 250, 251, 0.5);
      border-top: 1px solid rgba(226, 232, 240, 0.3);
      text-align: center;

      .dark & {
        background: rgba(17, 24, 39, 0.5);
        border-top-color: rgba(75, 85, 99, 0.3);
      }

      .more-cases-hint {
        font-size: 0.875rem;
        color: #6b7280;

        .dark & {
          color: #9ca3af;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 响应式设计
@media (max-width: 1024px) {
  .dfg-detail-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 2.5vh auto;
    }
  }
}

@media (max-width: 768px) {
  .dfg-detail-dialog {
    :deep(.el-dialog) {
      width: 100% !important;
      height: 100vh !important;
      margin: 0;
      border-radius: 0;
    }

    :deep(.el-dialog__body) {
      max-height: calc(100vh - 140px);
    }
  }

  .detail-content {
    .stats-grid {
      grid-template-columns: 1fr;
    }

    .chart-container {
      height: 250px;

      &.small {
        height: 180px;
      }
    }
  }
}
</style>
