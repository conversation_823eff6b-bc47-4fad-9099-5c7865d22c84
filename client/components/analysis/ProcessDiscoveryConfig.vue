<template>
  <el-dialog
    v-model="dialogVisible"
    title="流程发现配置"
    width="600px"
    :show-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    :modal="true"
    :append-to-body="true"
    :z-index="3000"
    class="config-dialog"
  >
    <div class="config-content">
      <el-form :model="configForm" label-width="0" class="config-form">
        <div class="form-section">
          <div class="section-header">
            <h4 class="section-title">
              <el-icon class="section-icon"><Filter /></el-icon>
              活动节点过滤
            </h4>
            <div class="section-description">
              <p class="description-text">
                选择必须包含的活动节点，只有包含这些活动节点的案例链路才会参与流程发现计算
              </p>
              <p class="description-note">
                多个活动节点是"或"的关系，即案例中包含任一活动节点即可
              </p>
            </div>
          </div>

          <div class="activity-select-container">
            <el-select
              v-model="configForm.requiredActivities"
              multiple
              filterable
              placeholder="请选择必须包含的活动节点（可选）"
              size="large"
              :loading="loadingActivities"
              clearable
              :teleported="true"
              popper-class="config-select-dropdown"
              :popper-options="{ strategy: 'fixed' }"
              class="activity-select"
            >
              <el-option
                v-for="activity in availableActivities"
                :key="activity"
                :label="activity"
                :value="activity"
              />
            </el-select>

            <div v-if="configForm.requiredActivities && configForm.requiredActivities.length > 0" class="selected-activities">
              <div class="selected-header">
                <el-icon class="selected-icon"><Check /></el-icon>
                <span class="selected-title">已选择 {{ configForm.requiredActivities?.length || 0 }} 个活动节点</span>
              </div>
              <div class="selected-tags">
                <el-tag
                  v-for="activity in configForm.requiredActivities"
                  :key="activity"
                  type="primary"
                  size="default"
                  closable
                  effect="light"
                  @close="removeActivity(activity)"
                  class="activity-tag"
                >
                  {{ activity }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>

        <div class="form-section">
          <div class="section-header">
            <h4 class="section-title">
              <el-icon class="section-icon"><Refresh /></el-icon>
              缓存设置
            </h4>
          </div>

          <div class="switch-container">
            <div class="switch-item">
              <div class="switch-content">
                <div class="switch-label">
                  <span class="label-text">强制刷新缓存</span>
                  <span class="label-description">启用后将忽略缓存，重新计算流程发现结果</span>
                </div>
                <el-switch
                  v-model="configForm.forceRefresh"
                  size="large"
                  active-color="#10b981"
                  inactive-color="#e5e7eb"
                  :disabled="props.readOnly"
                  class="refresh-switch"
                />
              </div>
            </div>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="applying">
          应用配置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Filter, Check, Refresh } from '@element-plus/icons-vue'
import { useApi } from '~/utils/api'
import type { ProcessDiscoveryOptions } from '~/types'

interface Props {
  visible: boolean
  processId: number
  currentConfig?: ProcessDiscoveryOptions
  readOnly?: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm', config: ProcessDiscoveryOptions): void
}

const props = withDefaults(defineProps<Props>(), {
  currentConfig: () => ({ requiredActivities: [], forceRefresh: true }),
  readOnly: false
})

const emit = defineEmits<Emits>()

// 响应式数据
const api = useApi()
const loadingActivities = ref(false)
const applying = ref(false)
const availableActivities = ref<string[]>([])

// 配置表单
const configForm = reactive<ProcessDiscoveryOptions>({
  requiredActivities: [],
  forceRefresh: true
})

// 计算属性处理v-model
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
})

// 监听visible变化，重置表单
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    resetForm()
    loadAvailableActivities()
  }
})

// 监听当前配置变化
watch(() => props.currentConfig, (newConfig) => {
  if (newConfig) {
    Object.assign(configForm, {
      requiredActivities: [...(newConfig.requiredActivities || [])],
      forceRefresh: newConfig.forceRefresh ?? true
    })
  }
}, { immediate: true, deep: true })

// 重置表单
const resetForm = () => {
  Object.assign(configForm, {
    requiredActivities: [...(props.currentConfig?.requiredActivities || [])],
    forceRefresh: props.currentConfig?.forceRefresh ?? true
  })
}

// 加载可用的活动节点
const loadAvailableActivities = async () => {
  if (!props.processId) return
  
  loadingActivities.value = true
  try {
    // 这里需要获取所有活动节点，暂时使用空数组
    // 实际应该从统计数据或者专门的接口获取
    availableActivities.value = []
    
    // 如果统计数据中没有活动列表，尝试从现有的流程发现结果中获取
    try {
      const discoveryResult = await api.discoverProcess(props.processId, { forceRefresh: false })
      if (discoveryResult && discoveryResult.nodes) {
        const activities = discoveryResult.nodes
          .filter(node => node.id !== '开始' && node.id !== '结束')
          .map(node => node.label || node.id)
          .filter(Boolean)
        availableActivities.value = [...new Set(activities)].sort()
      }
    } catch (error) {
      console.warn('Failed to load activities from discovery result:', error)
    }
  } catch (error) {
    console.error('Failed to load available activities:', error)
    ElMessage.error('加载活动节点失败')
  } finally {
    loadingActivities.value = false
  }
}

// 移除活动节点
const removeActivity = (activity: string) => {
  const index = configForm.requiredActivities?.indexOf(activity)
  if (index !== undefined && index > -1) {
    configForm.requiredActivities?.splice(index, 1)
  }
}

// 处理取消
const handleCancel = () => {
  emit('update:visible', false)
}

// 处理重置
const handleReset = () => {
  resetForm()
  ElMessage.success('配置已重置')
}

// 处理确认
const handleConfirm = () => {
  applying.value = true
  
  try {
    const config: ProcessDiscoveryOptions = {
      requiredActivities: [...(configForm.requiredActivities || [])],
      forceRefresh: props.readOnly ? false : configForm.forceRefresh
    }

    emit('confirm', config)
    emit('update:visible', false)

    ElMessage.success('配置已应用')
  } catch (error) {
    console.error('Failed to apply config:', error)
    ElMessage.error('应用配置失败')
  } finally {
    applying.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  if (props.visible) {
    loadAvailableActivities()
  }
})
</script>

<style lang="scss" scoped>
.config-dialog {
  :deep(.el-dialog__body) {
    padding: 24px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  }

  :deep(.el-dialog__header) {
    padding: 24px 24px 0;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
    }
  }

  :deep(.el-dialog__footer) {
    padding: 20px 24px 24px;
    border-top: 1px solid #e2e8f0;
    background: #ffffff;
  }
}

.config-content {
  .config-form {
    .form-section {
      background: #ffffff;
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border: 1px solid #e2e8f0;
      transition: all 0.2s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-color: #cbd5e1;
      }

      &:last-child {
        margin-bottom: 0;
      }

      .section-header {
        margin-bottom: 16px;

        .section-title {
          display: flex;
          align-items: center;
          margin: 0 0 12px 0;
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;

          .section-icon {
            margin-right: 8px;
            color: #3b82f6;
            font-size: 18px;
          }
        }

        .section-description {
          .description-text {
            margin: 0 0 6px 0;
            color: #64748b;
            font-size: 14px;
            line-height: 1.5;
          }

          .description-note {
            margin: 0;
            color: #94a3b8;
            font-size: 13px;
            font-style: italic;
          }
        }
      }

      .activity-select-container {
        .activity-select {
          :deep(.el-select__wrapper) {
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            transition: all 0.2s ease;

            &:hover {
              border-color: #cbd5e1;
            }

            &.is-focused {
              border-color: #3b82f6;
              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }
          }
        }

        .selected-activities {
          margin-top: 16px;
          padding: 16px;
          background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
          border-radius: 8px;
          border: 1px solid #bae6fd;

          .selected-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            .selected-icon {
              color: #0ea5e9;
              margin-right: 6px;
              font-size: 16px;
            }

            .selected-title {
              font-size: 14px;
              color: #0c4a6e;
              font-weight: 500;
            }
          }

          .selected-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            .activity-tag {
              border-radius: 6px;
              font-weight: 500;
              transition: all 0.2s ease;

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
              }
            }
          }
        }
      }

      .switch-container {
        .switch-item {
          .switch-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
            border-radius: 8px;
            border: 1px solid #bbf7d0;

            .switch-label {
              flex: 1;

              .label-text {
                display: block;
                font-size: 15px;
                font-weight: 500;
                color: #166534;
                margin-bottom: 4px;
              }

              .label-description {
                display: block;
                font-size: 13px;
                color: #16a34a;
                line-height: 1.4;
              }
            }

            .refresh-switch {
              margin-left: 16px;

              :deep(.el-switch__core) {
                border-width: 2px;
              }
            }
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .el-button {
    border-radius: 8px;
    font-weight: 500;
    padding: 10px 20px;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
    }
  }
}

:global(.config-select-dropdown) {
  max-height: 300px;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  z-index: 3100 !important; /* 确保下拉框在对话框之上 */
}

:global(.dark) {
  .config-dialog {
    :deep(.el-dialog__body) {
      background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    }

    :deep(.el-dialog__header) {
      border-bottom-color: #475569;

      .el-dialog__title {
        color: #f1f5f9;
      }
    }

    :deep(.el-dialog__footer) {
      background: #1e293b;
      border-top-color: #475569;
    }
  }

  .config-content {
    .config-form {
      .form-section {
        background: #334155;
        border-color: #475569;

        &:hover {
          border-color: #64748b;
        }

        .section-header {
          .section-title {
            color: #f1f5f9;
          }

          .section-description {
            .description-text {
              color: #cbd5e1;
            }

            .description-note {
              color: #94a3b8;
            }
          }
        }

        .activity-select-container {
          .selected-activities {
            background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
            border-color: #3b82f6;

            .selected-header {
              .selected-icon {
                color: #60a5fa;
              }

              .selected-title {
                color: #dbeafe;
              }
            }
          }
        }

        .switch-container {
          .switch-item {
            .switch-content {
              background: linear-gradient(135deg, #166534 0%, #15803d 100%);
              border-color: #22c55e;

              .switch-label {
                .label-text {
                  color: #dcfce7;
                }

                .label-description {
                  color: #bbf7d0;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
