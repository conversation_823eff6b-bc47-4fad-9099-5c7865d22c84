<template>
  <div class="global-filter sidebar-layout" :class="{ 'has-filters': hasActiveFilters }">
    <div class="filter-header">
      <div class="filter-title">
        <el-icon class="filter-icon">
          <Filter />
        </el-icon>
        <h3>筛选器</h3>
      </div>
      <div class="filter-actions">
        <el-button
          size="small"
          :icon="RefreshLeft"
          :disabled="!hasActiveFilters"
          circle
          title="重置"
          @click="resetFilters"
        />
        <el-button
          size="small"
          type="primary"
          :icon="Collection"
          :disabled="!hasActiveFilters"
          circle
          title="保存"
          @click="saveFilters"
        />
      </div>
    </div>

    <div
class="filter-content"
         :class="{ 'collapsed': !isExpanded }"
    >
      <!-- 筛选结果统计 - 移到最上面 -->
      <div v-if="hasActiveFilters" class="filter-summary">
        <div class="summary-content">
          <el-icon class="summary-icon"><InfoFilled /></el-icon>
          <div class="summary-text">
            <span class="summary-count">
              <strong>{{ filteredCount }}</strong> 条记录
            </span>
            <span v-if="totalCount > 0" class="summary-percentage">
              ({{ Math.round((filteredCount / totalCount) * 100) }}%)
            </span>
          </div>
        </div>
        <div class="active-filters">
          <el-tag
            v-for="tag in activeFilterTags"
            :key="tag.key"
            :type="tag.type"
            size="small"
            closable
            class="filter-tag"
            @close="removeFilter(tag.key)"
          >
            {{ tag.label }}
          </el-tag>
        </div>
      </div>

      <!-- 全局逻辑选择 -->
      <div class="global-logic-section">
        <div class="logic-label">
          <el-icon><Connection /></el-icon>
          <span>组间逻辑</span>
        </div>
        <el-radio-group v-model="filters.globalLogic" size="small" @change="emitFilterChange">
          <el-radio-button value="AND">且</el-radio-button>
          <el-radio-button value="OR">或</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 筛选组列表 -->
      <div class="filter-groups">
        <div
          v-for="(group, groupIndex) in filters.groups"
          :key="group.id"
          class="filter-group-container"
        >
          <!-- 组头部 -->
          <div class="group-header">
            <div class="group-info">
              <div class="group-title">
                <el-icon class="group-icon">
                  <component :is="getGroupIcon(group.type)" />
                </el-icon>
                <span class="group-name">{{ getGroupTypeName(group.type) }}</span>
                <el-tag
                  size="small"
                  :type="group.logic === 'AND' ? 'primary' : 'warning'"
                  class="logic-tag"
                >
                  {{ group.logic }}
                </el-tag>
                <span class="condition-count">
                  {{ group.conditions.filter(c => c.enabled).length }}/{{ group.conditions.length }}
                </span>
              </div>
            </div>
            <div class="group-actions">
              <el-dropdown trigger="click" @command="(cmd) => handleGroupAction(cmd, group.id, groupIndex)">
                <el-button size="small" :icon="ArrowDown" circle />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="add-condition">
                      <el-icon><Plus /></el-icon>
                      添加条件
                    </el-dropdown-item>
                    <el-dropdown-item command="toggle-logic">
                      <el-icon><Connection /></el-icon>
                      切换逻辑 ({{ group.logic === 'AND' ? '改为OR' : '改为AND' }})
                    </el-dropdown-item>
                    <el-dropdown-item command="remove-group" divided>
                      <el-icon><Delete /></el-icon>
                      删除组
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <!-- 条件列表 -->
          <div class="conditions-list">
            <div
              v-for="(condition, conditionIndex) in group.conditions"
              :key="condition.id"
              class="condition-item"
              :class="{ 'disabled': !condition.enabled }"
            >
              <div class="condition-header">
                <span class="condition-index">{{ conditionIndex + 1 }}.</span>
                <el-switch
                  v-model="condition.enabled"
                  size="small"
                  class="condition-toggle"
                  @change="emitFilterChange"
                />
                <span class="condition-type-label">{{ getConditionTypeLabel(condition.type) }}</span>
                <el-button
                  size="small"
                  type="danger"
                  :icon="Delete"
                  class="remove-condition-btn"
                  circle
                  @click="removeCondition(group.id, conditionIndex)"
                />
              </div>
              <div class="condition-content">
                <!-- 资源筛选条件 -->
                <template v-if="condition.type === 'resource'">
                  <el-select
                    v-model="condition.data.resources"
                    multiple
                    filterable
                    clearable
                    placeholder="选择执行者/角色"
                    class="condition-input"
                    :loading="loadingResources"
                    @change="emitFilterChange"
                    @clear="() => { condition.data.resources = []; emitFilterChange() }"
                  >
                    <el-option
                      v-for="resource in availableResources"
                      :key="resource.value"
                      :label="resource.label"
                      :value="resource.value"
                    >
                      <div class="resource-option">
                        <span class="resource-name">{{ resource.label }}</span>
                        <span class="resource-count">({{ resource.count }})</span>
                      </div>
                    </el-option>
                  </el-select>
                </template>

                <!-- 耗时筛选条件 -->
                <template v-else-if="condition.type === 'duration'">
                  <div class="duration-filter">
                    <div class="duration-inputs">
                      <el-input-number
                        v-model="condition.data.min"
                        :min="0"
                        :max="getDurationMaxValue(condition)"
                        placeholder="最小值"
                        size="small"
                        class="duration-input"
                        @change="emitFilterChange"
                      />
                      <span class="range-separator">至</span>
                      <el-input-number
                        v-model="condition.data.max"
                        :min="getDurationMinValue(condition)"
                        placeholder="最大值"
                        size="small"
                        class="duration-input"
                        @change="emitFilterChange"
                      />
                    </div>
                    <el-select
                      v-model="condition.data.unit"
                      size="small"
                      class="duration-unit"
                      @change="emitFilterChange"
                    >
                      <el-option label="分钟" value="minutes" />
                      <el-option label="小时" value="hours" />
                      <el-option label="天" value="days" />
                    </el-select>
                    <el-checkbox
                      v-model="condition.data.ignoreBoundaryEdges"
                      size="small"
                      @change="emitFilterChange"
                    >忽略边界路径（开始→*，*→结束）</el-checkbox>
                  </div>
                </template>

                <!-- 业务字段筛选条件 -->
                <template v-else-if="condition.type === 'businessField'">
                  <div class="business-filter">
                    <div class="business-field-row">
                      <el-select
                        v-model="condition.data.field"
                        placeholder="字段"
                        size="small"
                        class="field-select"
                        filterable
                        @change="onBusinessFieldChange(condition)"
                      >
                        <el-option
                          v-for="field in availableBusinessFields"
                          :key="field.name"
                          :label="field.label"
                          :value="field.name"
                        />
                      </el-select>
                      <el-select
                        v-model="condition.data.operator"
                        size="small"
                        class="operator-select"
                        @change="emitFilterChange"
                      >
                        <el-option label="包含" value="contains" />
                        <el-option label="等于" value="equals" />
                        <el-option label="在列表中" value="in" />
                        <el-option label="大于" value="gt" />
                        <el-option label="小于" value="lt" />
                        <el-option label="范围" value="range" />
                      </el-select>
                    </div>
                    <div v-if="condition.data.operator === 'in'" class="business-value-row">
                      <el-select
                        v-model="condition.data.values"
                        multiple
                        filterable
                        placeholder="选择值"
                        size="small"
                        class="value-select"
                        :loading="loadingFieldValues"
                        @change="emitFilterChange"
                      >
                        <el-option
                          v-for="value in getFieldValues(condition.data.field)"
                          :key="value"
                          :label="value"
                          :value="value"
                        />
                      </el-select>
                    </div>
                    <div v-else-if="condition.data.operator !== 'range'" class="business-value-row">
                      <el-input
                        v-model="condition.data.value"
                        placeholder="筛选值"
                        size="small"
                        class="value-input"
                        @input="emitFilterChange"
                      />
                    </div>
                    <div v-else class="business-value-row">
                      <div class="range-inputs">
                        <el-input
                          v-model="condition.data.rangeMin"
                          placeholder="最小值"
                          size="small"
                          class="range-input"
                          @input="emitFilterChange"
                        />
                        <span class="range-separator">至</span>
                        <el-input
                          v-model="condition.data.rangeMax"
                          placeholder="最大值"
                          size="small"
                          class="range-input"
                          @input="emitFilterChange"
                        />
                      </div>
                    </div>
                  </div>
                </template>

                <!-- 路径筛选条件 -->
                <template v-else-if="condition.type === 'pathway'">
                  <div class="pathway-filter">
                    <div class="pathway-selects">
                      <el-select
                        v-model="condition.data.activity"
                        placeholder="活动"
                        size="small"
                        class="activity-select"
                        filterable
                        @change="emitFilterChange"
                      >
                        <!-- 特殊的全部活动选项 -->
                        <el-option
                          label="🌐 全部活动"
                          value="ALL_NODES"
                          class="all-nodes-option"
                        >
                          <div class="special-option">
                            <span class="option-icon">🌐</span>
                            <span class="option-text">全部活动</span>
                            <span class="option-desc">（应用于所有节点）</span>
                          </div>
                        </el-option>
                        <el-option
                          value="__divider__"
                          disabled
                          label="── 具体活动 ──"
                        >
                          <div class="option-divider">── 具体活动 ──</div>
                        </el-option>
                        <el-option
                          v-for="activity in availableActivities"
                          :key="activity.value"
                          :label="activity.label"
                          :value="activity.value"
                        />
                      </el-select>
                      <el-select
                        v-model="condition.data.type"
                        size="small"
                        class="pathway-type"
                        @change="emitFilterChange"
                      >
                        <el-option label="前置" value="predecessor" />
                        <el-option label="后续" value="successor" />
                      </el-select>
                    </div>
                    <div class="frequency-range">
                      <!-- <div class="frequency-header">
                        <span class="frequency-label">
                          {{ condition.data.activity === 'ALL_NODES' ? '全局频次范围' : '频次范围' }}
                        </span>
                        <div class="frequency-values">
                          <span>{{ condition.data.frequency[0] }}</span>
                          <span>-</span>
                          <span>{{ condition.data.frequency[1] }}</span>
                        </div>
                      </div> -->
                      <div v-if="condition.data.activity === 'ALL_NODES'" class="global-filter-hint">
                        <el-icon><InfoFilled /></el-icon>
                        <span>将对所有路径的频次进行筛选，只显示频次在指定范围内的连接</span>
                      </div>
                      <div v-else-if="condition.data.activity" class="activity-filter-hint">
                        <el-icon><InfoFilled /></el-icon>
                        <span>筛选与"{{ getActivityLabel(condition.data.activity) }}"相关的{{ condition.data.type === 'predecessor' ? '前置' : '后续' }}路径</span>
                      </div>
                      <div class="frequency-inputs">
                        <div v-if="condition.data.frequency && Array.isArray(condition.data.frequency)" class="input-row">
                          <el-input-number
                            v-model="condition.data.frequency[0]"
                            :min="1"
                            :max="getFrequencyMaxValue(condition)"
                            placeholder="最小频次"
                            size="small"
                            class="frequency-input"
                            @change="emitFilterChange"
                          />
                          <span class="range-separator">至</span>
                          <el-input-number
                            v-model="condition.data.frequency[1]"
                            :min="getFrequencyMinValue(condition)"
                            :max="getMaxFrequencyForCondition(condition)"
                            placeholder="最大频次"
                            size="small"
                            class="frequency-input"
                            @change="emitFilterChange"
                          />
                        </div>
                        <div v-else class="frequency-loading">
                          <span>加载频次数据中...</span>
                        </div>
                      </div>
                      <el-slider
                        v-model="condition.data.frequency"
                        range
                        :min="1"
                        :max="getMaxFrequencyForCondition(condition)"
                        :step="1"
                        class="frequency-slider"
                        @change="emitFilterChange"
                      />
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>

        </div>

        <!-- 添加新筛选组按钮 -->
        <div class="add-group-section">
          <el-dropdown trigger="click" @command="addGroup">
            <el-button type="primary" :icon="Plus" size="small" style="width: 100%;">
              添加筛选组
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="resource">
                  <el-icon><User /></el-icon>
                  资源筛选
                </el-dropdown-item>
                <el-dropdown-item command="duration">
                  <el-icon><Timer /></el-icon>
                  耗时筛选
                </el-dropdown-item>
                <el-dropdown-item command="businessField">
                  <el-icon><Document /></el-icon>
                  业务字段筛选
                </el-dropdown-item>
                <el-dropdown-item command="pathway">
                  <el-icon><Share /></el-icon>
                  路径筛选
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>


      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Filter, RefreshLeft, Collection, User, Timer, Document, Share, InfoFilled, ArrowDown, Plus, Delete, Connection } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 定义组件属性
interface Props {
  processId: number
  discoveryData?: any // 流程发现数据
  visible?: boolean
}

// 定义事件
interface Emits {
  (e: 'filter-change', filters: FilterState): void
  (e: 'reset'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  discoveryData: null
})

const emit = defineEmits<Emits>()

// 单个筛选条件接口
interface FilterCondition {
  id: string
  type: 'resource' | 'duration' | 'businessField' | 'pathway'
  enabled: boolean
  data: any
}

// 筛选器组
interface FilterGroup {
  id: string
  type: 'resource' | 'duration' | 'businessField' | 'pathway'
  logic: 'AND' | 'OR'
  conditions: FilterCondition[]
}

// 筛选器状态接口
interface FilterState {
  globalLogic: 'AND' | 'OR'
  groups: FilterGroup[]
}

// 响应式数据
const isExpanded = ref(true)
const loadingResources = ref(false)
const loadingFieldValues = ref(false)
const filteredCount = ref(0)
const totalCount = ref(0)

// 筛选器状态
const filters = ref<FilterState>({
  globalLogic: 'AND',
  groups: []
})

// 可用选项数据
const availableResources = ref<Array<{
  label: string
  value: string
  count: number
}>>([])

const availableBusinessFields = ref<Array<{
  name: string
  label: string
  type: 'string' | 'number' | 'date'
}>>([])

const availableActivities = ref<Array<{
  label: string
  value: string
}>>([])

// 计算属性
const hasActiveFilters = computed(() => {
  return filters.value.groups.some(group =>
    group.conditions.some(condition =>
      condition.enabled && hasConditionValue(condition)
    )
  )
})

const activeFilterTags = computed(() => {
  const tags: Array<{
    key: string
    label: string
    type: 'primary' | 'success' | 'warning' | 'info'
  }> = []

  filters.value.groups.forEach((group, groupIndex) => {
    const activeConditions = group.conditions.filter(condition =>
      condition.enabled && hasConditionValue(condition)
    )

    if (activeConditions.length > 0) {
      tags.push({
        key: `group-${groupIndex}`,
        label: `${getGroupTypeName(group.type)}: ${activeConditions.length} 个条件 (${group.logic})`,
        type: getGroupTagType(group.type)
      })
    }
  })

  return tags
})

const resetFilters = () => {
  filters.value = {
    globalLogic: 'AND',
    groups: []
  }
  emit('reset')
  ElMessage.success('筛选条件已重置')
}

const saveFilters = () => {
  // 保存筛选条件到本地存储
  const filterKey = `process_filters_${props.processId}`
  localStorage.setItem(filterKey, JSON.stringify(filters.value))
  ElMessage.success('筛选条件已保存')
}

const loadSavedFilters = () => {
  // 从本地存储加载筛选条件
  const filterKey = `process_filters_${props.processId}`
  const saved = localStorage.getItem(filterKey)
  if (saved) {
    try {
      const savedFilters = JSON.parse(saved)
      filters.value = { ...filters.value, ...savedFilters }

      // 检查是否有活跃的筛选器，如果有则触发筛选事件
      const hasActiveFilters = filters.value.groups.some(group =>
        group.conditions.some(condition =>
          condition.enabled && hasConditionValue(condition)
        )
      )

      if (hasActiveFilters) {
        console.log('GlobalFilter: Found active saved filters, emitting filter change')
        // 延迟触发事件，确保父组件已经准备好接收
        nextTick(() => {
          emitFilterChange()
        })
      }
    } catch (error) {
      console.warn('Failed to load saved filters:', error)
    }
  }
}

const removeFilter = (key: string) => {
  if (key.startsWith('group-')) {
    const groupIndex = parseInt(key.split('-')[1])
    filters.value.groups.splice(groupIndex, 1)
    emitFilterChange()
  }
}

const emitFilterChange = () => {
  console.log('GlobalFilter: emitting filter change', JSON.stringify(filters.value, null, 2))
  emit('filter-change', filters.value)
}

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 检查条件是否有值
const hasConditionValue = (condition: FilterCondition): boolean => {
  switch (condition.type) {
    case 'resource':
      return condition.data.resources && condition.data.resources.length > 0
    case 'duration':
      return condition.data.min !== null || condition.data.max !== null
    case 'businessField':
      return condition.data.field !== '' && (
        condition.data.value !== '' ||
        (condition.data.operator === 'in' && condition.data.values && condition.data.values.length > 0) ||
        (condition.data.operator === 'range' && condition.data.rangeMin !== '' && condition.data.rangeMax !== '')
      )
    case 'pathway':
      return condition.data.activity !== '' &&
             condition.data.frequency &&
             condition.data.frequency[0] >= 1 &&
             condition.data.frequency[1] >= condition.data.frequency[0]
    default:
      return false
  }
}

// 获取组类型名称
const getGroupTypeName = (type: string): string => {
  const typeNames = {
    resource: '资源筛选',
    duration: '耗时筛选',
    businessField: '业务字段筛选',
    pathway: '路径筛选'
  }
  return typeNames[type as keyof typeof typeNames] || type
}

// 获取条件类型标签
const getConditionTypeLabel = (type: string): string => {
  const typeLabels = {
    resource: '执行者',
    duration: '耗时',
    businessField: '字段',
    pathway: '路径'
  }
  return typeLabels[type as keyof typeof typeLabels] || type
}

// 获取组图标
const getGroupIcon = (type: string) => {
  const icons = {
    resource: User,
    duration: Timer,
    businessField: Document,
    pathway: Share
  }
  return icons[type as keyof typeof icons] || User
}

// 获取组标签类型
const getGroupTagType = (type: string): 'primary' | 'success' | 'warning' | 'info' => {
  const tagTypes = {
    resource: 'primary',
    duration: 'success',
    businessField: 'warning',
    pathway: 'info'
  }
  return tagTypes[type as keyof typeof tagTypes] || 'primary'
}

// 添加筛选组
const addGroup = (type: string) => {
  const newGroup: FilterGroup = {
    id: generateId(),
    type: type as any,
    logic: 'AND',
    conditions: []
  }

  // 先添加组到列表中
  filters.value.groups.push(newGroup)

  // 然后添加一个默认条件
  addConditionToGroup(newGroup.id)

  emitFilterChange()
}

// 移除筛选组
const removeGroup = (groupIndex: number) => {
  filters.value.groups.splice(groupIndex, 1)
  emitFilterChange()
}

// 处理组操作
const handleGroupAction = (command: string, groupId: string, groupIndex: number) => {
  switch (command) {
    case 'add-condition':
      addConditionToGroup(groupId)
      break
    case 'toggle-logic':
      const group = filters.value.groups.find(g => g.id === groupId)
      if (group) {
        group.logic = group.logic === 'AND' ? 'OR' : 'AND'
        emitFilterChange()
      }
      break
    case 'remove-group':
      removeGroup(groupIndex)
      break
  }
}

// 添加条件到组
const addConditionToGroup = (groupId: string) => {
  const group = filters.value.groups.find(g => g.id === groupId)
  if (!group) return

  const newCondition: FilterCondition = {
    id: generateId(),
    type: group.type,
    enabled: true,
    data: getDefaultConditionData(group.type)
  }

  group.conditions.push(newCondition)
  emitFilterChange()
}

// 移除条件
const removeCondition = (groupId: string, conditionIndex: number) => {
  const group = filters.value.groups.find(g => g.id === groupId)
  if (!group) return

  group.conditions.splice(conditionIndex, 1)

  // 如果组中没有条件了，删除整个组
  if (group.conditions.length === 0) {
    const groupIndex = filters.value.groups.findIndex(g => g.id === groupId)
    if (groupIndex !== -1) {
      filters.value.groups.splice(groupIndex, 1)
    }
  }

  emitFilterChange()
}

// 获取默认条件数据
const getDefaultConditionData = (type: string): any => {
  switch (type) {
    case 'resource':
      return { resources: [] }
    case 'duration':
      return { min: null, max: null, unit: 'minutes', ignoreBoundaryEdges: true }
    case 'businessField':
      return { field: '', operator: 'contains', value: '', values: [], rangeMin: '', rangeMax: '' }
    case 'pathway': {
      const maxFreq = getMaxFrequencyFromData()
      return { activity: '', type: 'predecessor', frequency: [1, maxFreq] }
    }
    default:
      return {}
  }
}

// 业务字段变化处理
const onBusinessFieldChange = (condition: FilterCondition) => {
  // 重置值相关的字段
  condition.data.value = ''
  condition.data.values = []
  condition.data.rangeMin = ''
  condition.data.rangeMax = ''

  // 触发筛选变化
  emitFilterChange()
}

// 获取字段的可选值
const getFieldValues = (fieldName: string): any[] => {
  if (!fieldName || !props.discoveryData) return []

  const values = new Set<any>()

  // 从节点的businessFields中提取字段值
  if (props.discoveryData.nodes && Array.isArray(props.discoveryData.nodes)) {
    props.discoveryData.nodes.forEach((node: any) => {
      if (node.businessFields && node.businessFields[fieldName]) {
        const fieldInfo = node.businessFields[fieldName]
        if (fieldInfo.uniqueValues && Array.isArray(fieldInfo.uniqueValues)) {
          fieldInfo.uniqueValues.forEach((value: any) => {
            if (value !== null && value !== undefined) {
              values.add(value)
            }
          })
        }
      }
    })
  }

  return Array.from(values).slice(0, 100) // 限制数量
}

// 格式化字段标签
const formatFieldLabel = (fieldName: string): string => {
  // 常见字段的中文映射
  const fieldMappings: Record<string, string> = {
    'department': '部门',
    'priority': '优先级',
    'amount': '金额',
    'customer_type': '客户类型',
    'region': '地区',
    'status': '状态',
    'category': '类别',
    'type': '类型',
    'level': '级别',
    'cost': '成本',
    'revenue': '收入',
    'profit': '利润',
    'duration': '持续时间',
    'start_time': '开始时间',
    'end_time': '结束时间',
    'created_at': '创建时间',
    'updated_at': '更新时间',
    'created_by': '创建者',
    'updated_by': '更新者',
    'owner': '负责人',
    'manager': '管理者',
    'team': '团队',
    'project': '项目',
    'product': '产品',
    'service': '服务',
    'location': '位置',
    'address': '地址',
    'phone': '电话',
    'email': '邮箱',
    'website': '网站',
    'description': '描述',
    'notes': '备注',
    'comments': '评论'
  }

  // 如果有直接映射，使用映射
  if (fieldMappings[fieldName.toLowerCase()]) {
    return fieldMappings[fieldName.toLowerCase()]
  }

  // 否则进行格式化处理
  return fieldName
    .replace(/_/g, ' ') // 下划线替换为空格
    .replace(/([a-z])([A-Z])/g, '$1 $2') // 驼峰命名转换
    .replace(/\b\w/g, l => l.toUpperCase()) // 首字母大写
    .trim()
}

// 获取条件的最大频次值
const getMaxFrequencyForCondition = (condition: any): number => {
  if (!props.discoveryData || !props.discoveryData.edges) return 100

  const edges = props.discoveryData.edges
  if (!edges || edges.length === 0) return 100

  // 获取所有边的频次值
  const frequencies = edges.map((edge: any) => edge.frequency || 0)
  const maxFrequency = Math.max(...frequencies)

  return Math.max(maxFrequency, 100) // 至少返回100作为默认最大值
}

// 获取耗时筛选的安全最大值
const getDurationMaxValue = (condition: any): number => {
  const maxValue = condition.data.max
  const minValue = condition.data.min

  // 如果没有设置最大值，返回默认值
  if (maxValue === null || maxValue === undefined) {
    return 999999
  }

  // 如果最小值大于最大值，返回最小值加1，确保max > min
  if (minValue !== null && minValue !== undefined && minValue >= maxValue) {
    return Math.max(minValue + 1, 999999)
  }

  return maxValue
}

// 获取耗时筛选的安全最小值
const getDurationMinValue = (condition: any): number => {
  const minValue = condition.data.min
  const maxValue = condition.data.max

  // 如果没有设置最小值，返回0
  if (minValue === null || minValue === undefined) {
    return 0
  }

  // 如果最大值小于最小值，返回0，确保min < max
  if (maxValue !== null && maxValue !== undefined && maxValue <= minValue) {
    return 0
  }

  return Math.max(minValue, 0)
}

// 获取频次筛选的安全最大值
const getFrequencyMaxValue = (condition: any): number => {
  if (!condition.data.frequency || !Array.isArray(condition.data.frequency)) {
    return getMaxFrequencyForCondition(condition)
  }

  const maxValue = condition.data.frequency[1]
  const minValue = condition.data.frequency[0]

  // 如果最小值大于最大值，返回最小值加1，确保max > min
  if (minValue >= maxValue) {
    return Math.max(minValue + 1, getMaxFrequencyForCondition(condition))
  }

  return maxValue
}

// 获取频次筛选的安全最小值
const getFrequencyMinValue = (condition: any): number => {
  if (!condition.data.frequency || !Array.isArray(condition.data.frequency)) {
    return 1
  }

  const minValue = condition.data.frequency[0]
  const maxValue = condition.data.frequency[1]

  // 如果最大值小于最小值，返回1，确保min < max
  if (maxValue <= minValue) {
    return 1
  }

  return Math.max(minValue, 1)
}

// 获取活动标签
const getActivityLabel = (activityValue: string): string => {
  if (activityValue === 'ALL_NODES') return '全部活动'

  const activity = availableActivities.value.find(a => a.value === activityValue)
  return activity ? activity.label : activityValue
}

// 从数据中获取最大频次值
const getMaxFrequencyFromData = (): number => {
  if (!props.discoveryData || !props.discoveryData.edges) return 100

  const edges = props.discoveryData.edges
  if (!edges || edges.length === 0) return 100

  const frequencies = edges.map((edge: any) => edge.frequency || 0)
  const maxFrequency = Math.max(...frequencies)

  return Math.max(maxFrequency, 100)
}

// 从流程数据中提取筛选选项
const extractFilterOptions = (data: any) => {
  if (!data) return

  try {
    loadingResources.value = true

    // 提取可用资源（从节点数据中）
    const resourceSet = new Set<string>()
    const resourceCounts = new Map<string, number>()

    // 从节点数据中提取资源
    if (data.nodes && Array.isArray(data.nodes)) {
      data.nodes.forEach((node: any) => {
        // 优先使用节点的主要资源字段
        if (node.resource && typeof node.resource === 'string') {
          resourceSet.add(node.resource)
          resourceCounts.set(node.resource, (resourceCounts.get(node.resource) || 0) + (node.frequency || 1))
        }

        // 如果节点有详细的资源信息，也提取这些资源
        if (node.resources && Array.isArray(node.resources)) {
          node.resources.forEach((resourceInfo: any) => {
            if (resourceInfo.resource && typeof resourceInfo.resource === 'string') {
              resourceSet.add(resourceInfo.resource)
              resourceCounts.set(resourceInfo.resource, (resourceCounts.get(resourceInfo.resource) || 0) + (resourceInfo.frequency || 1))
            }
          })
        }

        // 兼容旧的资源字段格式
        const legacyResourceFields = ['performer', 'executor', 'assignee', 'user', 'actor']
        legacyResourceFields.forEach(field => {
          if (node[field] && typeof node[field] === 'string') {
            resourceSet.add(node[field])
            resourceCounts.set(node[field], (resourceCounts.get(node[field]) || 0) + (node.frequency || 1))
          }
        })
      })
    }

    // 从边数据中提取资源（作为补充）
    if (data.edges && Array.isArray(data.edges)) {
      data.edges.forEach((edge: any) => {
        if (edge.resource) {
          resourceSet.add(edge.resource)
          resourceCounts.set(edge.resource, (resourceCounts.get(edge.resource) || 0) + (edge.frequency || 1))
        }
      })
    }

    // 设置资源数据
    availableResources.value = Array.from(resourceSet).map(resource => ({
      label: resource,
      value: resource,
      count: resourceCounts.get(resource) || 0
    })).sort((a, b) => b.count - a.count) // 按频率排序

    // 提取活动列表（从节点数据中）
    if (data.nodes && Array.isArray(data.nodes)) {
      const activities = data.nodes.map((node: any) => ({
        label: node.label || node.id || node.name,
        value: node.id || node.label || node.name
      })).filter(activity => activity.label && activity.value)

      availableActivities.value = activities
    } else {
      // 默认活动列表
      availableActivities.value = [
        { label: '需求分析', value: 'requirement_analysis' },
        { label: '方案设计', value: 'solution_design' },
        { label: '开发实现', value: 'development' },
        { label: '测试验证', value: 'testing' },
        { label: '部署上线', value: 'deployment' }
      ]
    }

    // 提取业务字段（从节点的businessFields中获取）
    const businessFields = new Set<string>()
    const fieldTypes = new Map<string, 'string' | 'number' | 'date'>()
    const fieldValues = new Map<string, Set<any>>()

    if (data.nodes && Array.isArray(data.nodes) && data.nodes.length > 0) {
      // 从节点的businessFields中提取业务字段信息
      data.nodes.forEach((node: any) => {
        if (node.businessFields && typeof node.businessFields === 'object') {
          Object.keys(node.businessFields).forEach(field => {
            businessFields.add(field)

            const fieldInfo = node.businessFields[field]
            if (fieldInfo && fieldInfo.uniqueValues && Array.isArray(fieldInfo.uniqueValues)) {
              // 初始化字段值集合
              if (!fieldValues.has(field)) {
                fieldValues.set(field, new Set())
              }

              // 添加唯一值到集合中
              fieldInfo.uniqueValues.forEach((value: any) => {
                fieldValues.get(field)!.add(value)
              })

              // 推断字段类型
              if (fieldInfo.uniqueValues.length > 0) {
                const firstValue = fieldInfo.uniqueValues[0]
                if (typeof firstValue === 'number') {
                  fieldTypes.set(field, 'number')
                } else if (firstValue instanceof Date ||
                           (typeof firstValue === 'string' && !isNaN(Date.parse(firstValue)) && firstValue.includes('-'))) {
                  fieldTypes.set(field, 'date')
                } else {
                  fieldTypes.set(field, 'string')
                }
              }
            }
          })
        }
      })

      // 如果没有从businessFields中获取到数据，回退到旧的方法
      if (businessFields.size === 0) {
        const systemFields = [
          'id', 'label', 'frequency', 'avgDuration', 'minDuration', 'maxDuration',
          'nodeStyle', 'resource', 'performer', 'executor', 'assignee', 'user', 'actor',
          'key', 'color', 'borderColor', 'currentValue', 'dimension', 'businessFields', 'resources'
        ]

        // 收集所有节点的字段
        const allFields = new Set<string>()
        data.nodes.forEach((node: any) => {
          Object.keys(node).forEach(key => {
            if (!systemFields.includes(key) && node[key] !== null && node[key] !== undefined) {
              allFields.add(key)
            }
          })
        })

        // 分析字段类型
        allFields.forEach(field => {
          businessFields.add(field)

          // 从多个节点中推断字段类型
          const values = data.nodes
            .map((node: any) => node[field])
            .filter((value: any) => value !== null && value !== undefined)

          if (values.length > 0) {
            const firstValue = values[0]
            if (typeof firstValue === 'number') {
              fieldTypes.set(field, 'number')
            } else if (firstValue instanceof Date ||
                       (typeof firstValue === 'string' && !isNaN(Date.parse(firstValue)) && firstValue.includes('-'))) {
              fieldTypes.set(field, 'date')
            } else {
              fieldTypes.set(field, 'string')
            }
          }
        })
      }
    }

    if (businessFields.size === 0) {
      // 默认业务字段
      availableBusinessFields.value = [
        { name: 'department', label: '部门', type: 'string' },
        { name: 'priority', label: '优先级', type: 'string' },
        { name: 'amount', label: '金额', type: 'number' },
        { name: 'customer_type', label: '客户类型', type: 'string' },
        { name: 'region', label: '地区', type: 'string' }
      ]
    } else {
      availableBusinessFields.value = Array.from(businessFields).map(field => ({
        name: field,
        label: formatFieldLabel(field),
        type: fieldTypes.get(field) || 'string'
      })).sort((a, b) => a.label.localeCompare(b.label)) // 按标签排序
    }

    // 设置统计数据
    totalCount.value = data.statistics?.totalCases || data.nodes?.length || 1000
    filteredCount.value = totalCount.value

  } catch (error) {
    console.error('Failed to extract filter options:', error)
    // 出错时清空数据
    availableResources.value = []
    availableBusinessFields.value = []
    availableActivities.value = []
    totalCount.value = 0
    filteredCount.value = 0
  } finally {
    loadingResources.value = false
  }
}

// 监听筛选条件变化
watch(filters, () => {
  // 筛选后的记录数将由父组件传递或通过实际筛选计算
  // 这里不再使用模拟数据
}, { deep: true })

// 当摘要（hasActiveFilters）出现/消失时，强制重定位下拉弹层，避免位置错乱
watch(hasActiveFilters, () => {
  nextTick(() => {
    try {
      window.dispatchEvent(new Event('resize'))
    } catch (e) {
      // ignore
    }
  })
})

// 监听流程数据变化
watch(() => props.discoveryData, (newData) => {
  if (newData) {
    extractFilterOptions(newData)

    // 重新初始化pathway条件的frequency数组
    const maxFreq = getMaxFrequencyFromData()
    filters.value.groups.forEach(group => {
      if (group.type === 'pathway') {
        group.conditions.forEach(condition => {
          if (!condition.data.frequency || !Array.isArray(condition.data.frequency)) {
            condition.data.frequency = [1, maxFreq]
          } else {
            // 更新最大值，但保持用户设置的当前值
            condition.data.frequency[1] = Math.min(condition.data.frequency[1], maxFreq)
          }
        })
      }
    })
  }
}, { immediate: true, deep: true })

// 生命周期
onMounted(() => {
  if (props.discoveryData) {
    extractFilterOptions(props.discoveryData)
  }
  loadSavedFilters()
})

// 暴露方法给父组件
defineExpose({
  resetFilters,
  getFilters: () => filters.value,
  setFilters: (newFilters: Partial<FilterState>) => {
    filters.value = { ...filters.value, ...newFilters }
  }
})
</script>

<style scoped lang="scss">
@use '~/assets/scss/utils/variables' as *;
@use '~/assets/scss/utils/mixins' as *;

.global-filter {
  height: 100%;
  display: flex;
  flex-direction: column;

  &.sidebar-layout {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    border: none;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    :global(.dark) & {
      background: rgba(31, 41, 55, 0.98);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }

  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: spacing(4) spacing(4);
    background: linear-gradient(135deg, rgba(theme-color(primary, 50), 0.6), rgba(theme-color(primary, 100), 0.4));
    border-bottom: none;
    flex-shrink: 0;

    :global(.dark) & {
      background: linear-gradient(135deg, rgba(55, 65, 81, 0.6), rgba(75, 85, 99, 0.4));
    }

    .filter-title {
      display: flex;
      align-items: center;
      gap: spacing(2);

      .filter-icon {
        color: theme-color(primary, 600);
        font-size: font-size(md);
      }

      h3 {
        margin: 0;
        font-size: font-size(md);
        font-weight: 600;
        background: linear-gradient(135deg, theme-color(primary, 600), theme-color(primary, 800));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    .filter-actions {
      display: flex;
      align-items: center;
      gap: spacing(1);
    }
  }

  .filter-content {
    flex: 1;
    overflow-y: auto;
    padding: spacing(4);
    display: flex;
    flex-direction: column;
    gap: spacing(4);

    &.collapsed {
      display: none;
    }
  }

  .filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: spacing(6);
    margin-bottom: spacing(6);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: spacing(4);
    }
  }

  .filter-group {
    .filter-label {
      display: flex;
      align-items: center;
      gap: spacing(2);
      margin-bottom: spacing(3);
      font-weight: 500;
      color: theme-color(gray, 700);

      :global(.dark) & {
        color: theme-color(gray, 300);
      }

      .el-icon {
        color: theme-color(primary, 600);
      }
    }

    .filter-select {
      width: 100%;
    }

    .duration-filter {
      display: flex;
      align-items: center;
      gap: spacing(2);
      flex-wrap: wrap;

      .duration-input {
        flex: 1;
        min-width: 80px;
      }

      .duration-separator {
        color: theme-color(gray, 500);
        font-weight: 500;
        font-size: 12px;
        margin: 0 spacing(1);
      }

      .duration-unit {
        width: 80px;
      }
    }

    .business-filter {
      display: flex;
      flex-direction: column;
      gap: spacing(2);

      .field-select,
      .operator-select {
        width: 100%;
      }

      .value-input {
        width: 100%;
      }

      .range-inputs {
        display: flex;
        align-items: center;
        gap: spacing(2);

        .range-input {
          flex: 1;
        }

        span {
          color: theme-color(gray, 500);
        }
      }
    }

    .pathway-filter {
      display: flex;
      flex-direction: column;
      gap: spacing(3);

      .activity-select,
      .pathway-type {
        width: 100%;
      }

      .frequency-range {
        .frequency-label {
          display: block;
          margin-bottom: spacing(2);
          font-size: font-size(xs);
          color: theme-color(gray, 600);

          :global(.dark) & {
            color: theme-color(gray, 400);
          }
        }

        .frequency-slider {
          margin-bottom: spacing(2);
        }

        .frequency-values {
          display: flex;
          justify-content: space-between;
          font-size: font-size(xs);
          color: theme-color(gray, 500);
        }

        .frequency-inputs {
          margin-bottom: spacing(3);

          .input-row {
            display: flex;
            align-items: center;
            gap: spacing(2);

            .frequency-input {
              flex: 1;
            }

            .range-separator {
              color: theme-color(gray, 500);
              font-weight: 500;
              font-size: 12px;
              margin: 0 spacing(1);
            }

            .frequency-loading {
              padding: spacing(2);
              text-align: center;
              color: theme-color(gray, 500);
              font-size: font-size(sm);
              background-color: theme-color(gray, 50);
              border-radius: border-radius(sm);
            }
          }
        }
      }
    }
  }

  .filter-summary {
    background: linear-gradient(135deg, rgba(theme-color(success, 50), 0.6), rgba(theme-color(success, 100), 0.3));
    border: none;
    border-radius: 12px;
    padding: spacing(4);

    :global(.dark) & {
      background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.08));
    }

    .summary-content {
      display: flex;
      align-items: center;
      gap: spacing(2);
      margin-bottom: spacing(2);

      .summary-icon {
        color: theme-color(success, 600);
        font-size: 16px;
      }

      .summary-text {
        display: flex;
        align-items: center;
        gap: spacing(1);
        font-size: 13px;
        color: theme-color(gray, 700);

        :global(.dark) & {
          color: theme-color(gray, 300);
        }

        .summary-count {
          font-weight: 600;
          color: theme-color(success, 700);
        }

        .summary-percentage {
          font-size: 12px;
          color: theme-color(gray, 600);
        }
      }
    }

    .active-filters {
      display: flex;
      flex-wrap: wrap;
      gap: spacing(1);

      .filter-tag {
        font-size: 11px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
        }
      }
    }
  }
}

// Element Plus 组件样式覆盖
:deep(.el-select) {
  .el-input__wrapper {
    border-radius: 16px;
    transition: all 0.3s ease;

    &:hover {
      border-color: theme-color(primary, 400);
    }

    &.is-focus {
      border-color: theme-color(primary, 500);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

:deep(.el-input-number) {
  .el-input__wrapper {
    border-radius: 16px;
  }
}

:deep(.el-slider) {
  .el-slider__runway {
    background-color: theme-color(gray, 200);

    :global(.dark) & {
      background-color: theme-color(gray, 700);
    }
  }

  .el-slider__bar {
    background-color: theme-color(primary, 500);
  }

  .el-slider__button {
    border-color: theme-color(primary, 500);
  }
}

// 资源选项样式
.resource-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .resource-name {
    flex: 1;
  }

  .resource-count {
    font-size: font-size(xs);
    color: theme-color(gray, 500);
  }
}

// 新的筛选器样式
.global-logic-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: spacing(3) spacing(4);
  background: linear-gradient(135deg, rgba(theme-color(primary, 50), 0.4), rgba(theme-color(primary, 100), 0.2));
  border-radius: 12px;
  border: none;

  :global(.dark) & {
    background: linear-gradient(135deg, rgba(55, 65, 81, 0.4), rgba(75, 85, 99, 0.2));
  }

  .logic-label {
    display: flex;
    align-items: center;
    gap: spacing(2);
    font-weight: 500;
    color: theme-color(primary, 700);
    font-size: 14px;

    :global(.dark) & {
      color: theme-color(primary, 300);
    }
  }
}

.filter-groups {
  display: flex;
  flex-direction: column;
  gap: spacing(4);
}

.filter-group-container {
  border: none;
  border-radius: 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
  }

  :global(.dark) & {
    background: linear-gradient(135deg, rgba(31, 41, 55, 0.9), rgba(55, 65, 81, 0.8));

    &:hover {
      background: linear-gradient(135deg, rgba(31, 41, 55, 0.95), rgba(55, 65, 81, 0.9));
    }
  }
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: spacing(3) spacing(4);
  background: linear-gradient(135deg, rgba(theme-color(gray, 50), 0.6), rgba(theme-color(gray, 100), 0.3));
  border-bottom: none;

  :global(.dark) & {
    background: linear-gradient(135deg, rgba(55, 65, 81, 0.6), rgba(75, 85, 99, 0.3));
  }
}

.group-info {
  flex: 1;
}

.group-title {
  display: flex;
  align-items: center;
  gap: spacing(2);

  .group-icon {
    color: theme-color(primary, 600);
    font-size: 16px;
  }

  .group-name {
    font-weight: 500;
    color: theme-color(gray, 800);
    font-size: 14px;

    :global(.dark) & {
      color: theme-color(gray, 200);
    }
  }

  .logic-tag {
    margin-left: spacing(1);
  }

  .condition-count {
    margin-left: spacing(2);
    font-size: 12px;
    color: theme-color(gray, 600);
    background: rgba(theme-color(gray, 100), 0.8);
    padding: 2px 6px;
    border-radius: 4px;

    :global(.dark) & {
      color: theme-color(gray, 400);
      background: rgba(theme-color(gray, 700), 0.8);
    }
  }
}

.condition-count {
  font-size: font-size(xs);
  color: theme-color(gray, 500);
}

.group-actions {
  display: flex;
  align-items: center;
  gap: spacing(3);

  .logic-selector {
    margin-right: spacing(2);
  }

  .add-condition-btn {
    font-size: font-size(xs);
  }

  .remove-group-btn {
    width: 28px;
    height: 28px;
  }
}

.conditions-list {
  padding: spacing(3);
  display: flex;
  flex-direction: column;
  gap: spacing(3);
}

.condition-item {
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
  transition: all 0.3s ease;

  &.disabled {
    opacity: 0.6;
    background: linear-gradient(135deg, rgba(theme-color(gray, 100), 0.6), rgba(theme-color(gray, 200), 0.4));
  }

  :global(.dark) & {
    background: linear-gradient(135deg, rgba(31, 41, 55, 0.95), rgba(55, 65, 81, 0.9));

    &.disabled {
      background: linear-gradient(135deg, rgba(55, 65, 81, 0.6), rgba(75, 85, 99, 0.4));
    }
  }
}

.condition-header {
  display: flex;
  align-items: center;
  gap: spacing(2);
  padding: spacing(2) spacing(3);
  background: linear-gradient(135deg, rgba(theme-color(gray, 50), 0.4), rgba(theme-color(gray, 100), 0.2));
  border-bottom: none;
  border-radius: 12px 12px 0 0;

  .condition-index {
    font-size: 12px;
    font-weight: 600;
    color: theme-color(primary, 700);
    min-width: 16px;
  }

  .condition-type-label {
    font-size: 12px;
    color: theme-color(gray, 600);
    background: linear-gradient(135deg, rgba(theme-color(gray, 100), 0.8), rgba(theme-color(gray, 200), 0.6));
    padding: 4px 8px;
    border-radius: 8px;
    border: none;

    :global(.dark) & {
      color: theme-color(gray, 400);
      background: linear-gradient(135deg, rgba(theme-color(gray, 700), 0.8), rgba(theme-color(gray, 600), 0.6));
    }
  }

  .condition-toggle {
    margin-left: auto;
  }

  .remove-condition-btn {
    width: 20px;
    height: 20px;
  }

  :global(.dark) & {
    background: linear-gradient(135deg, rgba(55, 65, 81, 0.4), rgba(75, 85, 99, 0.2));

    .condition-index {
      color: theme-color(primary, 300);
    }
  }
}

.condition-content {
  padding: spacing(3);
}

// 条件类型特定样式
.duration-filter {
  display: flex;
  align-items: center;
  gap: spacing(2);
  flex-wrap: wrap;
  width: 100%;

  .duration-inputs {
    display: flex;
    align-items: center;
    gap: spacing(2);
    flex: 1;
    min-width: 200px;

    .duration-input {
      flex: 1;
      min-width: 120px;
    }

    .range-separator {
      color: theme-color(gray, 500);
      font-size: 12px;
      margin: 0 spacing(1);
    }
  }

  .duration-unit {
    width: 80px;
  }
}

.business-filter {
  display: flex;
  flex-direction: column;
  gap: spacing(2);

  .business-field-row {
    display: flex;
    gap: spacing(2);

    .field-select {
      flex: 2;
      min-width: 120px;
    }

    .operator-select {
      flex: 1;
      min-width: 80px;
    }
  }

  .business-value-row {
    .value-input {
      width: 100%;
    }

    .range-inputs {
      display: flex;
      align-items: center;
      gap: spacing(2);

      .range-input {
        flex: 1;
        min-width: 80px;
      }

      .range-separator {
        color: theme-color(gray, 500);
        font-size: 12px;
      }
    }
  }
}

.pathway-filter {
  display: flex;
  flex-direction: column;
  gap: spacing(2);

  .pathway-selects {
    display: flex;
    gap: spacing(2);

    .activity-select {
      flex: 2;
      min-width: 140px;
    }

    .pathway-type {
      flex: 1;
      min-width: 80px;
    }
  }

  .percentage-range {
    .frequency-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: spacing(2);

      .frequency-label {
        
        font-size: font-size(xs);
        font-weight: 500;
        color: theme-color(gray, 700);

        :global(.dark) & {
          color: theme-color(gray, 300);
        }
      }

      .frequency-values {
        display: flex;
        font-size: font-size(xs);
        align-items: center;
        gap: spacing(1);
        color: theme-color(primary, 600);
        font-weight: 500;
      }
    }

    .frequency-slider {
      margin: spacing(2) 0;
    }
  }
}

.add-group-section {
  padding-top: spacing(4);
  border-top: none;
  position: relative;
  display: flex;
  justify-content: center;

  /* 仅当存在筛选条件时显示上边分隔线 */
  .has-filters &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 1px;
    background: linear-gradient(90deg, transparent, theme-color(gray, 300), transparent);

    :global(.dark) & {
      background: linear-gradient(90deg, transparent, theme-color(gray, 600), transparent);
    }
  }
}

// 特殊选项样式
.special-option {
  display: flex;
  align-items: center;
  gap: spacing(2);

  .option-icon {
    font-size: font-size(md);
  }

  .option-text {
    font-weight: 500;
    color: theme-color(primary, 600);
  }

  .option-desc {
    font-size: font-size(xs);
    color: theme-color(gray, 500);
    font-style: italic;
  }
}

.option-divider {
  text-align: center;
  color: theme-color(gray, 400);
  font-size: font-size(xs);
  padding: spacing(1) 0;
}

.global-filter-hint,
.activity-filter-hint {
  display: flex;
  align-items: center;
  gap: spacing(2);
  margin-top: spacing(2);
  margin-bottom: spacing(2);
  padding: spacing(2) spacing(3);
  background: rgba(theme-color(info, 50), 0.8);
  border: 1px solid theme-color(info, 200);
  border-radius: 4px;
  font-size: font-size(xs);
  color: theme-color(info, 700);

  :global(.dark) & {
    background: rgba(theme-color(info, 900), 0.3);
    border-color: theme-color(info, 700);
    color: theme-color(info, 300);
  }
}

.activity-filter-hint {
  background: rgba(theme-color(success, 50), 0.8);
  border-color: theme-color(success, 200);
  color: theme-color(success, 700);

  :global(.dark) & {
    background: rgba(theme-color(success, 900), 0.3);
    border-color: theme-color(success, 700);
    color: theme-color(success, 300);
  }
}

// 动画
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
