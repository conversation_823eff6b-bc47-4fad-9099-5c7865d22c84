<template>
  <div class="offset-days-control">
    <div class="control-header">
      <el-icon class="control-icon"><Timer /></el-icon>
      <span class="control-label">偏移天数</span>
      <el-tooltip content="相对于基准时间段向前偏移的天数" placement="top">
        <el-icon class="help-icon"><InfoFilled /></el-icon>
      </el-tooltip>
    </div>
    
    <div class="control-content">
      <!-- 主控制区域 -->
      <div class="main-controls">
        <div class="input-section">
          <el-input-number
            v-model="offsetValue"
            :min="1"
            :max="maxOffset"
            :step="1"
            size="small"
            class="offset-input"
            @change="onOffsetChange"
          />
          <span class="unit-label">天</span>
        </div>

        <!-- 快速设置 -->
        <div class="quick-settings">
          <el-button-group size="small">
            <el-button @click="setQuickOffset(1)">1天</el-button>
            <el-button @click="setQuickOffset(7)">1周</el-button>
            <el-button @click="setQuickOffset(30)">1月</el-button>
            <el-button @click="setQuickOffset(90)">3月</el-button>
          </el-button-group>
          <el-button
            size="small"
            type="primary"
            text
            :disabled="!baseRange"
            @click="setAutoOffset"
          >
            自动设置
          </el-button>
        </div>
      </div>

      <!-- 时间范围显示 -->
      <div v-if="calculatedRange" class="range-display">
        <div class="range-item">
          <span class="range-label">对比时间段：</span>
          <span class="range-value">
            {{ formatDate(calculatedRange.start) }} 至 {{ formatDate(calculatedRange.end) }}
          </span>
        </div>
        <div class="range-item">
          <span class="range-label">时间跨度：</span>
          <span class="range-value">{{ formatDuration(calculatedRange.duration) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Timer, InfoFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 组件属性
interface Props {
  modelValue?: number
  baseRange?: [Date, Date] | null
  disabled?: boolean
  maxDays?: number
}

// 组件事件
interface Emits {
  (e: 'update:modelValue' | 'change', value: number): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: 7,
  baseRange: null,
  disabled: false,
  maxDays: 365
})

const emit = defineEmits<Emits>()

// 响应式数据
const offsetValue = ref(props.modelValue)

// 计算属性
const maxOffset = computed(() => {
  if (props.baseRange) {
    // 基于基准时间范围计算最大偏移天数
    const duration = props.baseRange[1].getTime() - props.baseRange[0].getTime()
    const baseDays = Math.ceil(duration / (1000 * 60 * 60 * 24))
    return Math.min(props.maxDays, baseDays * 10) // 最大为基准时间段的10倍
  }
  return props.maxDays
})



const calculatedRange = computed(() => {
  if (!props.baseRange || !offsetValue.value) return null
  
  const baseDuration = props.baseRange[1].getTime() - props.baseRange[0].getTime()
  const offsetMs = offsetValue.value * 24 * 60 * 60 * 1000
  
  const start = new Date(props.baseRange[0].getTime() - offsetMs)
  const end = new Date(props.baseRange[1].getTime() - offsetMs)
  
  return {
    start,
    end,
    duration: baseDuration
  }
})

// 方法

const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDuration = (duration: number) => {
  const days = Math.floor(duration / (1000 * 60 * 60 * 24))
  const hours = Math.floor((duration % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  
  if (days > 0) {
    return `${days}天${hours}小时`
  } else {
    return `${hours}小时`
  }
}

const setQuickOffset = (days: number) => {
  if (days <= maxOffset.value) {
    offsetValue.value = days
    onOffsetChange(days)
  } else {
    ElMessage.warning(`偏移天数不能超过${maxOffset.value}天`)
  }
}

const setAutoOffset = () => {
  if (!props.baseRange) return
  
  // 自动设置为基准时间段的长度
  const baseDuration = props.baseRange[1].getTime() - props.baseRange[0].getTime()
  const baseDays = Math.ceil(baseDuration / (1000 * 60 * 60 * 24))
  
  const autoOffset = Math.min(baseDays, maxOffset.value)
  offsetValue.value = autoOffset
  onOffsetChange(autoOffset)
  
  ElMessage.success(`已自动设置偏移天数为${autoOffset}天`)
}

const onOffsetChange = (value: number | number[] | undefined) => {
  const numValue = Array.isArray(value) ? value[0] : value
  if (typeof numValue === 'number') {
    emit('update:modelValue', numValue)
    emit('change', numValue)
  }
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  offsetValue.value = newValue
}, { immediate: true })

// 监听基准时间范围变化，自动调整偏移天数
watch(() => props.baseRange, (newRange) => {
  if (newRange && offsetValue.value > maxOffset.value) {
    offsetValue.value = Math.min(offsetValue.value, maxOffset.value)
    onOffsetChange(offsetValue.value)
  }
})
</script>

<style scoped lang="scss">
@use '~/assets/scss/utils/variables' as *;
@use '~/assets/scss/utils/mixins' as *;

.offset-days-control {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 6px;
  padding: 0.375rem;
  border: 1px solid rgba(0, 0, 0, 0.06);

  .dark & {
    background: rgba(31, 41, 55, 0.98);
    border-color: rgba(255, 255, 255, 0.1);
  }
}

.control-header {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  margin-bottom: 0.375rem;

  .control-icon {
    color: theme-color(warning, 500);
    font-size: 0.75rem;
  }

  .control-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: theme-color(gray, 900);

    .dark & {
      color: theme-color(gray, 100);
    }
  }

  .help-icon {
    color: theme-color(gray, 500);
    font-size: 0.6875rem;
    cursor: help;

    &:hover {
      color: theme-color(gray, 600);
    }

    .dark & {
      color: theme-color(gray, 400);

      &:hover {
        color: theme-color(gray, 300);
      }
    }
  }
}

.control-content {
  .main-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    margin-bottom: 0.375rem;

    .input-section {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      flex-shrink: 0;

      .offset-input {
        width: 170px;
      }

      .unit-label {
        font-size: 0.6875rem;
        color: theme-color(gray, 500);
        font-weight: 500;

        .dark & {
          color: theme-color(gray, 400);
        }
      }
    }

    .quick-settings {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      flex: 1;
      justify-content: flex-end;
    }
  }
}

.range-display {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.25rem 0.375rem;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
  font-size: 0.625rem;
  margin-top: 0.375rem;

  :global(.dark) & {
    background: rgba(255, 255, 255, 0.03);
  }
}

.range-item {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .range-label {
    color: theme-color(gray, 600);
    font-size: 0.625rem;

    :global(.dark) & {
      color: theme-color(gray, 400);
    }
  }

  .range-value {
    color: theme-color(gray, 900);
    font-weight: 500;
    text-align: right;
    font-size: 0.625rem;

    :global(.dark) & {
      color: theme-color(gray, 100);
    }
  }
}



// 响应式设计
@media (max-width: 768px) {
  .offset-days-control {
    padding: 0.25rem;
  }

  .control-header {
    margin-bottom: 0.25rem;
    gap: 0.25rem;

    .control-icon {
      font-size: 0.6875rem;
    }

    .control-label {
      font-size: 0.6875rem;
    }

    .help-icon {
      font-size: 0.625rem;
    }
  }

  .main-controls {
    flex-direction: column;
    gap: 0.25rem;
    align-items: stretch;
    margin-bottom: 0.25rem;

    .input-section {
      justify-content: center;
      gap: 0.125rem;

      .offset-input {
        width: 60px;
      }

      .unit-label {
        font-size: 0.625rem;
      }
    }

    .quick-settings {
      justify-content: center;
      gap: 0.125rem;
    }
  }

  .range-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.125rem;

    .range-label,
    .range-value {
      font-size: 0.5625rem;
    }
  }

  .range-display {
    margin-top: 0.25rem;
    padding: 0.125rem 0.25rem;
    font-size: 0.5625rem;
  }
}
</style>
