import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElDatePicker, ElButton, ElButtonGroup, ElIcon } from 'element-plus'
import TimeRangeFilter from '../TimeRangeFilter.vue'

// Mock Element Plus components
vi.mock('element-plus', () => ({
  ElDatePicker: {
    name: 'ElDatePicker',
    template: '<div class="el-date-picker"><slot /></div>',
    props: ['modelValue', 'type', 'rangeSeparator', 'startPlaceholder', 'endPlaceholder', 'format', 'valueFormat', 'size', 'disabledDate', 'shortcuts'],
    emits: ['update:modelValue', 'change']
  },
  ElButton: {
    name: 'ElButton',
    template: '<button class="el-button"><slot /></button>',
    props: ['size', 'type', 'text', 'disabled']
  },
  ElButtonGroup: {
    name: 'ElButtonGroup',
    template: '<div class="el-button-group"><slot /></div>',
    props: ['size']
  },
  ElIcon: {
    name: 'ElIcon',
    template: '<i class="el-icon"><slot /></i>'
  }
}))

// Mock icons
vi.mock('@element-plus/icons-vue', () => ({
  Clock: {
    name: 'Clock',
    template: '<svg class="clock-icon"></svg>'
  }
}))

describe('TimeRangeFilter', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(TimeRangeFilter, {
      global: {
        components: {
          ElDatePicker,
          ElButton,
          ElButtonGroup,
          ElIcon
        }
      }
    })
  })

  it('renders correctly', () => {
    expect(wrapper.find('.time-range-filter').exists()).toBe(true)
    expect(wrapper.find('.filter-header').exists()).toBe(true)
    expect(wrapper.find('.filter-content').exists()).toBe(true)
  })

  it('displays filter label correctly', () => {
    const filterLabel = wrapper.find('.filter-label')
    expect(filterLabel.exists()).toBe(true)
    expect(filterLabel.text()).toBe('时间范围')
  })

  it('renders date picker with correct props', () => {
    const datePicker = wrapper.findComponent({ name: 'ElDatePicker' })
    expect(datePicker.exists()).toBe(true)
    expect(datePicker.props('type')).toBe('datetimerange')
    expect(datePicker.props('rangeSeparator')).toBe('至')
    expect(datePicker.props('startPlaceholder')).toBe('开始时间')
    expect(datePicker.props('endPlaceholder')).toBe('结束时间')
  })

  it('renders quick action buttons', () => {
    const quickActions = wrapper.find('.quick-actions')
    expect(quickActions.exists()).toBe(true)
    
    const buttons = quickActions.findAllComponents({ name: 'ElButton' })
    expect(buttons.length).toBeGreaterThan(0)
    
    // Check for specific quick action buttons
    const buttonTexts = buttons.map((btn: any) => btn.text())
    expect(buttonTexts).toContain('今天')
    expect(buttonTexts).toContain('昨天')
    expect(buttonTexts).toContain('本周')
    expect(buttonTexts).toContain('本月')
  })

  it('emits update:modelValue when date range changes', async () => {
    const testRange = [
      new Date('2024-01-01T00:00:00'),
      new Date('2024-01-07T23:59:59')
    ]
    
    await wrapper.setProps({ modelValue: testRange })
    
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
  })

  it('shows range info when date range is selected', async () => {
    const testRange = [
      '2024-01-01 00:00:00',
      '2024-01-07 23:59:59'
    ]
    
    await wrapper.setData({ dateRange: testRange })
    
    const rangeInfo = wrapper.find('.range-info')
    expect(rangeInfo.exists()).toBe(true)
    
    const infoItems = rangeInfo.findAll('.info-item')
    expect(infoItems.length).toBeGreaterThanOrEqual(2)
  })

  it('calculates duration correctly', async () => {
    const component = wrapper.vm
    
    // Set a test date range
    await wrapper.setData({
      dateRange: ['2024-01-01 00:00:00', '2024-01-02 00:00:00']
    })
    
    const duration = component.getDuration()
    expect(duration).toBe(24 * 60 * 60 * 1000) // 1 day in milliseconds
  })

  it('formats duration correctly', () => {
    const component = wrapper.vm
    
    // Test different durations
    expect(component.formatDuration(60 * 1000)).toBe('1分钟')
    expect(component.formatDuration(60 * 60 * 1000)).toBe('1小时0分钟')
    expect(component.formatDuration(24 * 60 * 60 * 1000)).toBe('1天0小时')
    expect(component.formatDuration(25 * 60 * 60 * 1000)).toBe('1天1小时')
  })

  it('handles quick range selection', async () => {
    const component = wrapper.vm
    
    // Test setting today's range
    component.setQuickRange('today')
    
    expect(wrapper.emitted('change')).toBeTruthy()
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
  })

  it('validates time range correctly', async () => {
    const component = wrapper.vm
    
    // Mock ElMessage.warning
    const mockWarning = vi.fn()
    vi.stubGlobal('ElMessage', { warning: mockWarning })
    
    // Test invalid range (start time after end time)
    const invalidRange = ['2024-01-02 00:00:00', '2024-01-01 00:00:00']
    component.onDateRangeChange(invalidRange)
    
    expect(mockWarning).toHaveBeenCalledWith('开始时间必须早于结束时间')
  })

  it('clears range correctly', async () => {
    // Set initial range
    await wrapper.setData({
      dateRange: ['2024-01-01 00:00:00', '2024-01-07 23:59:59']
    })
    
    const component = wrapper.vm
    component.clearRange()
    
    expect(component.dateRange).toBeNull()
    expect(wrapper.emitted('change')).toBeTruthy()
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
  })

  it('disables future dates', () => {
    const component = wrapper.vm
    const futureDate = new Date(Date.now() + 24 * 60 * 60 * 1000) // Tomorrow
    const pastDate = new Date(Date.now() - 24 * 60 * 60 * 1000) // Yesterday
    
    expect(component.disabledDate(futureDate)).toBe(true)
    expect(component.disabledDate(pastDate)).toBe(false)
  })

  it('estimates data count based on duration', async () => {
    const component = wrapper.vm
    
    // Set a 7-day range
    const range = [
      '2024-01-01 00:00:00',
      '2024-01-08 00:00:00'
    ]
    
    component.onDateRangeChange(range)
    
    expect(component.estimatedDataCount).toBeGreaterThan(0)
  })

  it('handles external modelValue changes', async () => {
    const testRange = [
      new Date('2024-01-01T00:00:00'),
      new Date('2024-01-07T23:59:59')
    ]
    
    await wrapper.setProps({ modelValue: testRange })
    
    expect(wrapper.vm.dateRange).toBeTruthy()
    expect(wrapper.vm.dateRange[0]).toContain('2024-01-01')
    expect(wrapper.vm.dateRange[1]).toContain('2024-01-07')
  })

  it('handles null modelValue', async () => {
    await wrapper.setProps({ modelValue: null })
    
    expect(wrapper.vm.dateRange).toBeNull()
  })
})
