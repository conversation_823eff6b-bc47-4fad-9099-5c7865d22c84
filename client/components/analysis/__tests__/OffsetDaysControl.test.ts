import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElInputNumber, ElSlider, ElButton, ElButtonGroup, ElIcon, ElTooltip } from 'element-plus'
import OffsetDaysControl from '../OffsetDaysControl.vue'

// Mock Element Plus components
vi.mock('element-plus', () => ({
  ElInputNumber: {
    name: 'ElInputNumber',
    template: '<input class="el-input-number" type="number" />',
    props: ['modelValue', 'min', 'max', 'step', 'size'],
    emits: ['update:modelValue', 'change']
  },
  ElSlider: {
    name: 'ElSlider',
    template: '<div class="el-slider"></div>',
    props: ['modelValue', 'min', 'max', 'step', 'marks', 'showTooltip', 'formatTooltip'],
    emits: ['update:modelValue', 'change']
  },
  ElButton: {
    name: 'ElButton',
    template: '<button class="el-button"><slot /></button>',
    props: ['size', 'type', 'text', 'disabled']
  },
  ElButtonGroup: {
    name: 'ElButtonGroup',
    template: '<div class="el-button-group"><slot /></div>',
    props: ['size']
  },
  ElIcon: {
    name: 'ElIcon',
    template: '<i class="el-icon"><slot /></i>'
  },
  ElTooltip: {
    name: 'ElTooltip',
    template: '<div class="el-tooltip"><slot /></div>',
    props: ['content', 'placement']
  }
}))

// Mock icons
vi.mock('@element-plus/icons-vue', () => ({
  Timer: {
    name: 'Timer',
    template: '<svg class="timer-icon"></svg>'
  },
  InfoFilled: {
    name: 'InfoFilled',
    template: '<svg class="info-icon"></svg>'
  }
}))

// Mock ElMessage
const mockElMessage = {
  warning: vi.fn(),
  success: vi.fn()
}
vi.stubGlobal('ElMessage', mockElMessage)

describe('OffsetDaysControl', () => {
  let wrapper: any

  beforeEach(() => {
    vi.clearAllMocks()
    wrapper = mount(OffsetDaysControl, {
      props: {
        modelValue: 7,
        baseRange: [
          new Date('2024-01-01T00:00:00'),
          new Date('2024-01-07T23:59:59')
        ]
      },
      global: {
        components: {
          ElInputNumber,
          ElSlider,
          ElButton,
          ElButtonGroup,
          ElIcon,
          ElTooltip
        }
      }
    })
  })

  it('renders correctly', () => {
    expect(wrapper.find('.offset-days-control').exists()).toBe(true)
    expect(wrapper.find('.control-header').exists()).toBe(true)
    expect(wrapper.find('.control-content').exists()).toBe(true)
  })

  it('displays control label correctly', () => {
    const controlLabel = wrapper.find('.control-label')
    expect(controlLabel.exists()).toBe(true)
    expect(controlLabel.text()).toBe('偏移天数')
  })

  it('renders input number with correct props', () => {
    const inputNumber = wrapper.findComponent({ name: 'ElInputNumber' })
    expect(inputNumber.exists()).toBe(true)
    expect(inputNumber.props('min')).toBe(1)
    expect(inputNumber.props('step')).toBe(1)
  })

  it('renders slider with correct props', () => {
    const slider = wrapper.findComponent({ name: 'ElSlider' })
    expect(slider.exists()).toBe(true)
    expect(slider.props('min')).toBe(1)
    expect(slider.props('step')).toBe(1)
  })

  it('calculates max offset correctly based on base range', () => {
    const component = wrapper.vm
    expect(component.maxOffset).toBeGreaterThan(0)
    
    // Test with different base ranges
    const longRange = [
      new Date('2024-01-01T00:00:00'),
      new Date('2024-01-31T23:59:59')
    ]
    wrapper.setProps({ baseRange: longRange })
    
    expect(component.maxOffset).toBeGreaterThan(30)
  })

  it('generates slider marks correctly', () => {
    const component = wrapper.vm
    const marks = component.sliderMarks
    
    expect(typeof marks).toBe('object')
    expect(Object.keys(marks).length).toBeGreaterThan(0)
    
    // Check that marks contain day labels
    const markValues = Object.values(marks)
    expect(markValues.some((mark: any) => mark.includes('天'))).toBe(true)
  })

  it('calculates range correctly', () => {
    const component = wrapper.vm
    const calculatedRange = component.calculatedRange
    
    expect(calculatedRange).toBeTruthy()
    expect(calculatedRange.start).toBeInstanceOf(Date)
    expect(calculatedRange.end).toBeInstanceOf(Date)
    expect(calculatedRange.duration).toBeGreaterThan(0)
    
    // Verify that the calculated range is offset correctly
    const baseStart = new Date('2024-01-01T00:00:00')
    const expectedStart = new Date(baseStart.getTime() - 7 * 24 * 60 * 60 * 1000)
    expect(calculatedRange.start.getTime()).toBe(expectedStart.getTime())
  })

  it('formats tooltip correctly', () => {
    const component = wrapper.vm
    expect(component.formatTooltip(7)).toBe('7天前')
    expect(component.formatTooltip(1)).toBe('1天前')
    expect(component.formatTooltip(30)).toBe('30天前')
  })

  it('formats date correctly', () => {
    const component = wrapper.vm
    const testDate = new Date('2024-01-01T12:30:00')
    const formatted = component.formatDate(testDate)
    
    expect(formatted).toContain('2024')
    expect(formatted).toContain('01')
    expect(formatted).toContain('12:30')
  })

  it('formats duration correctly', () => {
    const component = wrapper.vm
    
    const oneDayMs = 24 * 60 * 60 * 1000
    const oneHourMs = 60 * 60 * 1000
    
    expect(component.formatDuration(oneDayMs)).toBe('1天0小时')
    expect(component.formatDuration(oneDayMs + 2 * oneHourMs)).toBe('1天2小时')
    expect(component.formatDuration(5 * oneHourMs)).toBe('5小时')
  })

  it('handles quick offset selection', async () => {
    const component = wrapper.vm
    
    component.setQuickOffset(1)
    expect(component.offsetValue).toBe(1)
    expect(wrapper.emitted('change')).toBeTruthy()
    
    component.setQuickOffset(7)
    expect(component.offsetValue).toBe(7)
    
    component.setQuickOffset(30)
    expect(component.offsetValue).toBe(30)
  })

  it('prevents setting offset beyond max limit', async () => {
    const component = wrapper.vm
    const maxOffset = component.maxOffset
    
    component.setQuickOffset(maxOffset + 100)
    
    expect(mockElMessage.warning).toHaveBeenCalledWith(
      expect.stringContaining(`偏移天数不能超过${maxOffset}天`)
    )
  })

  it('handles auto offset setting', async () => {
    const component = wrapper.vm
    
    component.setAutoOffset()
    
    expect(component.offsetValue).toBeGreaterThan(0)
    expect(mockElMessage.success).toHaveBeenCalledWith(
      expect.stringContaining('已自动设置偏移天数为')
    )
  })

  it('emits change events correctly', async () => {
    const component = wrapper.vm
    
    component.onOffsetChange(14)
    
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
    expect(wrapper.emitted('change')).toBeTruthy()
    expect(wrapper.emitted('change')[0]).toEqual([14])
  })

  it('watches external modelValue changes', async () => {
    await wrapper.setProps({ modelValue: 14 })
    
    expect(wrapper.vm.offsetValue).toBe(14)
  })

  it('adjusts offset when base range changes', async () => {
    const component = wrapper.vm
    const originalOffset = component.offsetValue
    
    // Set a very small base range that would make current offset invalid
    const smallRange = [
      new Date('2024-01-01T00:00:00'),
      new Date('2024-01-01T01:00:00') // 1 hour range
    ]
    
    await wrapper.setProps({ baseRange: smallRange })
    
    // The offset should be adjusted to fit within the new max limit
    expect(component.offsetValue).toBeLessThanOrEqual(component.maxOffset)
  })

  it('handles undefined offset change', () => {
    const component = wrapper.vm
    
    component.onOffsetChange(undefined)
    
    // Should not emit events for undefined values
    expect(wrapper.emitted('update:modelValue')).toBeFalsy()
    expect(wrapper.emitted('change')).toBeFalsy()
  })

  it('displays range information when calculated range exists', async () => {
    const rangeDisplay = wrapper.find('.range-display')
    expect(rangeDisplay.exists()).toBe(true)
    
    const rangeItems = rangeDisplay.findAll('.range-item')
    expect(rangeItems.length).toBeGreaterThanOrEqual(2)
    
    // Check for time range and duration display
    const rangeTexts = rangeItems.map((item: any) => item.text())
    expect(rangeTexts.some((text: string) => text.includes('对比时间段'))).toBe(true)
    expect(rangeTexts.some((text: string) => text.includes('时间跨度'))).toBe(true)
  })

  it('renders quick setting buttons', () => {
    const quickSettings = wrapper.find('.quick-settings')
    expect(quickSettings.exists()).toBe(true)
    
    const buttons = quickSettings.findAllComponents({ name: 'ElButton' })
    expect(buttons.length).toBeGreaterThan(0)
    
    const buttonTexts = buttons.map((btn: any) => btn.text())
    expect(buttonTexts).toContain('1天')
    expect(buttonTexts).toContain('1周')
    expect(buttonTexts).toContain('1月')
    expect(buttonTexts).toContain('3月')
    expect(buttonTexts).toContain('自动设置')
  })
})
