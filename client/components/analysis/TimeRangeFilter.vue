<template>
  <div class="time-range-filter">
    <div class="filter-header">
      <el-icon class="filter-icon"><Clock /></el-icon>
      <span class="filter-label">时间范围</span>
    </div>
    
    <div class="filter-content">
      <div class="main-controls">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm:ss"
          size="small"
          class="date-picker"
          :disabled-date="disabledDate"
          :shortcuts="shortcuts"
          @change="onDateRangeChange"
        />

        <div class="quick-actions">
          <el-button-group size="small">
            <el-button @click="setQuickRange('today')">今天</el-button>
            <el-button @click="setQuickRange('yesterday')">昨天</el-button>
            <el-button @click="setQuickRange('week')">本周</el-button>
            <el-button @click="setQuickRange('month')">本月</el-button>
          </el-button-group>
          <el-button
            size="small"
            type="danger"
            text
            @click="clearRange"
            :disabled="!dateRange"
          >
            清除
          </el-button>
        </div>
      </div>

      <div v-if="dateRange && dateRange.length === 2" class="range-info">
        <div class="info-item">
          <span class="info-label">时间跨度：</span>
          <span class="info-value">{{ formatDuration(getDuration()) }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">数据量：</span>
          <span class="info-value">
            <template v-if="isLoadingCount">
              <el-icon class="is-loading"><Loading /></el-icon>
              加载中...
            </template>
            <template v-else>
              {{ estimatedDataCount }} 条记录
            </template>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Clock, Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { api } from '~/utils/api'

// 组件属性
interface Props {
  modelValue?: [Date, Date] | null
  processId?: number
  disabled?: boolean
  placeholder?: string
}

// 组件事件
interface Emits {
  (e: 'update:modelValue', value: [Date, Date] | null): void
  (e: 'change', value: [Date, Date] | null): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  processId: undefined,
  disabled: false,
  placeholder: '选择时间范围'
})

const emit = defineEmits<Emits>()

// 响应式数据
const dateRange = ref<[string, string] | null>(null)
const estimatedDataCount = ref(0)
const isLoadingCount = ref(false)

// 快捷选项
const shortcuts = [
  {
    text: '最近1小时',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000)
      return [start, end]
    }
  },
  {
    text: '最近6小时',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 6)
      return [start, end]
    }
  },
  {
    text: '最近1天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24)
      return [start, end]
    }
  },
  {
    text: '最近3天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 3)
      return [start, end]
    }
  },
  {
    text: '最近7天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近30天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  }
]

// 计算属性
const getDuration = () => {
  if (!dateRange.value || dateRange.value.length !== 2) return 0
  const start = new Date(dateRange.value[0])
  const end = new Date(dateRange.value[1])
  return end.getTime() - start.getTime()
}

// 方法
const disabledDate = (time: Date) => {
  // 禁用未来的日期
  return time.getTime() > Date.now()
}

const formatDuration = (duration: number) => {
  const days = Math.floor(duration / (1000 * 60 * 60 * 24))
  const hours = Math.floor((duration % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))
  
  if (days > 0) {
    return `${days}天${hours}小时`
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

const setQuickRange = (type: string) => {
  const now = new Date()
  let start: Date
  let end: Date = new Date(now)
  
  switch (type) {
    case 'today':
      start = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      break
    case 'yesterday':
      start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1)
      end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      break
    case 'week': {
      const dayOfWeek = now.getDay()
      start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek)
      break
    }
    case 'month':
      start = new Date(now.getFullYear(), now.getMonth(), 1)
      break
    default:
      return
  }
  
  const startStr = start.toISOString().slice(0, 19).replace('T', ' ')
  const endStr = end.toISOString().slice(0, 19).replace('T', ' ')
  dateRange.value = [startStr, endStr]
  onDateRangeChange(dateRange.value)
}

const clearRange = () => {
  dateRange.value = null
  onDateRangeChange(null)
}

const onDateRangeChange = async (value: [string, string] | null) => {
  let dateValue: [Date, Date] | null = null

  if (value && value.length === 2) {
    dateValue = [new Date(value[0]), new Date(value[1])]

    // 验证时间范围
    if (dateValue[0] >= dateValue[1]) {
      ElMessage.warning('开始时间必须早于结束时间')
      return
    }

    // 获取真实数据量
    if (props.processId) {
      isLoadingCount.value = true
      try {
        const result = await api.getDataCountByTimeRange(
          props.processId,
          value[0],
          value[1]
        )
        estimatedDataCount.value = result.totalEvents
      } catch (error) {
        console.error('获取数据计数失败:', error)
        // 如果API调用失败，回退到估算
        const duration = dateValue[1].getTime() - dateValue[0].getTime()
        const days = duration / (1000 * 60 * 60 * 24)
        estimatedDataCount.value = Math.round(days * 1000) // 假设每天1000条记录
        ElMessage.warning('无法获取准确数据量，显示估算值')
      } finally {
        isLoadingCount.value = false
      }
    } else {
      // 没有processId时使用估算
      const duration = dateValue[1].getTime() - dateValue[0].getTime()
      const days = duration / (1000 * 60 * 60 * 24)
      estimatedDataCount.value = Math.round(days * 1000) // 假设每天1000条记录
    }
  } else {
    estimatedDataCount.value = 0
  }

  emit('update:modelValue', dateValue)
  emit('change', dateValue)
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && newValue.length === 2) {
    const startStr = newValue[0].toISOString().slice(0, 19).replace('T', ' ')
    const endStr = newValue[1].toISOString().slice(0, 19).replace('T', ' ')
    dateRange.value = [startStr, endStr]
  } else {
    dateRange.value = null
  }
}, { immediate: true })
</script>

<style scoped lang="scss">
@use '~/assets/scss/utils/variables' as *;
@use '~/assets/scss/utils/mixins' as *;

.time-range-filter {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 6px;
  padding: 0.375rem;
  border: 1px solid rgba(0, 0, 0, 0.06);

  .dark & {
    background: rgba(31, 41, 55, 0.98);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .filter-content {
    .main-controls {
      display: flex;
      align-items: center;
      gap: 0.375rem;
      margin-bottom: 0.375rem;

      .date-picker {
        flex: 1;
        min-width: 0;
      }

      .quick-actions {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        flex-shrink: 0;
      }
    }

    .range-info {
      display: flex;
      gap: 0.75rem;
      padding: 0.25rem 0.375rem;
      background: rgba(0, 0, 0, 0.02);
      border-radius: 4px;
      font-size: 0.6875rem;

      .dark & {
        background: rgba(255, 255, 255, 0.03);
      }

      .info-item {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        flex: 1;

        .info-label {
          font-size: 0.6875rem;
          color: theme-color(gray, 600);
          white-space: nowrap;

          .dark & {
            color: theme-color(gray, 400);
          }
        }

        .info-value {
          font-size: 0.6875rem;
          font-weight: 500;
          color: theme-color(primary, 600);
          text-align: right;
          flex: 1;
        }
      }
    }
  }
}

.filter-header {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  margin-bottom: 0.375rem;

  .filter-icon {
    color: theme-color(primary, 600);
    font-size: 0.75rem;
  }

  .filter-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: theme-color(gray, 900);

    .dark & {
      color: theme-color(gray, 100);
    }
  }
}

.filter-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.date-picker {
  width: 100%;
}

.quick-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.375rem;
}

// 响应式设计
@media (max-width: 768px) {
  .time-range-filter {
    padding: 0.25rem;
  }

  .filter-header {
    margin-bottom: 0.25rem;

    .filter-icon {
      font-size: 0.6875rem;
    }

    .filter-label {
      font-size: 0.6875rem;
    }
  }

  .filter-content {
    .main-controls {
      flex-direction: column;
      gap: 0.25rem;
      align-items: stretch;
      margin-bottom: 0.25rem;

      .quick-actions {
        justify-content: center;
        gap: 0.125rem;
      }
    }

    .range-info {
      padding: 0.125rem 0.25rem;
      gap: 0.5rem;
      font-size: 0.625rem;

      .info-item {
        .info-label,
        .info-value {
          font-size: 0.5625rem;
        }
      }
    }
  }
}
</style>
