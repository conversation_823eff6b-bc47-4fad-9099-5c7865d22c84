<template>
  <div v-if="visible" class="filter-loading-overlay">
    <div class="filter-loading-content">
      <div class="filter-loading-spinner">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
      </div>
      
      <div class="filter-loading-text">
        <h3 class="filter-loading-title">{{ currentStage.title }}</h3>
        <p class="filter-loading-description">{{ currentStage.description }}</p>
      </div>
      
      <div class="filter-loading-progress">
        <el-progress 
          :percentage="progress" 
          :stroke-width="8"
          :show-text="true"
          status="primary"
          class="progress-bar"
        />
        <div class="progress-details">
          <span class="progress-stage">{{ currentStage.stage }}</span>
          <span class="progress-percent">{{ progress }}%</span>
        </div>
      </div>
      
      <div class="filter-loading-stages">
        <div 
          v-for="stage in stages" 
          :key="stage.key"
          class="stage-item"
          :class="{
            'stage-completed': isStageCompleted(stage.key),
            'stage-current': currentStageKey === stage.key,
            'stage-pending': !isStageCompleted(stage.key) && currentStageKey !== stage.key
          }"
        >
          <div class="stage-icon">
            <el-icon v-if="isStageCompleted(stage.key)">
              <Check />
            </el-icon>
            <el-icon v-else-if="currentStageKey === stage.key" class="is-loading">
              <Loading />
            </el-icon>
            <div v-else class="stage-dot"></div>
          </div>
          <span class="stage-label">{{ stage.label }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Loading, Check } from '@element-plus/icons-vue'

interface Props {
  visible: boolean
  progress: number
  stage: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  progress: 0,
  stage: 'initializing'
})

const stages = [
  { key: 'initializing', label: '初始化', title: '正在初始化筛选器', description: '准备筛选算法和数据结构' },
  { key: 'applying_conditions', label: '应用条件', title: '应用筛选条件', description: '根据设定的条件筛选节点和边' },
  { key: 'cleaning_edges', label: '清理边', title: '清理无效边', description: '移除指向不存在节点的边' },
  { key: 'removing_isolated_nodes', label: '移除孤立节点', title: '移除孤立节点', description: '移除没有任何连接的节点' },
  { key: 'removing_unidirectional_nodes', label: '移除单向节点', title: '移除单向连接节点', description: '移除只有单向连接的死胡同节点' },
  { key: 'connectivity_analysis', label: '连通性分析', title: '分析流程连通性', description: '识别和移除与主流程不连通的孤立子图' },
  { key: 'finalizing', label: '完成处理', title: '完成筛选处理', description: '生成最终的筛选结果和统计信息' }
]

const currentStageKey = computed(() => props.stage)

const currentStage = computed(() => {
  return stages.find(s => s.key === currentStageKey.value) || stages[0]
})

const isStageCompleted = (stageKey: string) => {
  const currentIndex = stages.findIndex(s => s.key === currentStageKey.value)
  const stageIndex = stages.findIndex(s => s.key === stageKey)
  return stageIndex < currentIndex || (stageIndex === currentIndex && props.progress === 100)
}
</script>

<style lang="scss" scoped>
@use '@/assets/scss/utils/variables' as *;
@use '@/assets/scss/utils/mixins' as *;
@use 'sass:map';

.filter-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
}

.filter-loading-content {
  background: white;
  border-radius: map.get($border-radius, lg);
  padding: spacing(8);
  max-width: 500px;
  width: 90%;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  text-align: center;
  animation: slideUp 0.4s ease-out;

  :global(.dark) & {
    background: theme-color(gray, 800);
    color: theme-color(gray, 100);
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

.filter-loading-spinner {
  margin-bottom: spacing(6);

  .el-icon {
    font-size: 48px;
    color: theme-color(primary, 500);
  }
}

.filter-loading-text {
  margin-bottom: spacing(6);

  .filter-loading-title {
    font-size: font-size(xl);
    font-weight: map.get($font-weight, semibold);
    color: theme-color(gray, 900);
    margin: 0 0 spacing(2) 0;

    :global(.dark) & {
      color: theme-color(gray, 100);
    }
  }

  .filter-loading-description {
    font-size: font-size(sm);
    color: theme-color(gray, 600);
    margin: 0;

    :global(.dark) & {
      color: theme-color(gray, 400);
    }
  }
}

.filter-loading-progress {
  margin-bottom: spacing(6);

  .progress-bar {
    margin-bottom: spacing(3);
  }

  .progress-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: font-size(sm);

    .progress-stage {
      color: theme-color(gray, 600);
      font-weight: map.get($font-weight, medium);

      :global(.dark) & {
        color: theme-color(gray, 400);
      }
    }

    .progress-percent {
      color: theme-color(primary, 600);
      font-weight: map.get($font-weight, semibold);

      :global(.dark) & {
        color: theme-color(primary, 400);
      }
    }
  }
}

.filter-loading-stages {
  display: flex;
  flex-direction: column;
  gap: spacing(3);
  text-align: left;

  .stage-item {
    display: flex;
    align-items: center;
    gap: spacing(3);
    padding: spacing(2) spacing(3);
    border-radius: map.get($border-radius, md);
    transition: all 0.3s ease;

    &.stage-completed {
      background: rgba(theme-color(success, 50), 0.8);
      color: theme-color(success, 700);

      :global(.dark) & {
        background: rgba(theme-color(success, 900), 0.3);
        color: theme-color(success, 300);
      }

      .stage-icon .el-icon {
        color: theme-color(success, 500);
      }
    }

    &.stage-current {
      background: rgba(theme-color(primary, 50), 0.8);
      color: theme-color(primary, 700);

      :global(.dark) & {
        background: rgba(theme-color(primary, 900), 0.3);
        color: theme-color(primary, 300);
      }

      .stage-icon .el-icon {
        color: theme-color(primary, 500);
      }
    }

    &.stage-pending {
      color: theme-color(gray, 500);

      :global(.dark) & {
        color: theme-color(gray, 500);
      }

      .stage-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: theme-color(gray, 300);

        :global(.dark) & {
          background: theme-color(gray, 600);
        }
      }
    }
  }

  .stage-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;

    .el-icon {
      font-size: 16px;
    }
  }

  .stage-label {
    font-size: font-size(sm);
    font-weight: map.get($font-weight, medium);
  }
}
</style>
