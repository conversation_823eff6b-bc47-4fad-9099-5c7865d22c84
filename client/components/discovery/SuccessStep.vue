<template>
  <el-card class="success-step-card">
    <template #header>
      <div class="step-header">
        <div class="success-icon">
          <el-icon :size="48" color="#67c23a">
            <CircleCheck />
          </el-icon>
        </div>
        <h3 class="step-title">流程发现完成！</h3>
        <p class="step-description">您的流程数据已成功分析，可以查看详细结果</p>
      </div>
    </template>

    <div class="success-content">
      <!-- 流程统计概览 -->
      <div class="statistics-overview">
        <h4 class="section-title">流程概览</h4>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon :size="24" color="#667eea">
                <DataBoard />
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ discoveryResult?.statistics?.totalCases || 0 }}</div>
              <div class="stat-label">流程案例</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <el-icon :size="24" color="#e6a23c">
                <Operation />
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ discoveryResult?.statistics?.totalActivities || 0 }}</div>
              <div class="stat-label">活动数量</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <el-icon :size="24" color="#f56c6c">
                <Timer />
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatDuration(discoveryResult?.statistics?.avgCaseDuration) }}</div>
              <div class="stat-label">平均持续时间</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <el-icon :size="24" color="#67c23a">
                <Connection />
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ discoveryResult?.edges.length || 0 }}</div>
              <div class="stat-label">流程连接</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 流程特征 -->
      <div class="process-features">
        <h4 class="section-title">流程特征</h4>
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-header">
              <el-icon color="#67c23a">
                <Right />
              </el-icon>
              <span class="feature-title">开始活动</span>
            </div>
            <div class="feature-content">
              <el-tag
                v-for="activity in discoveryResult?.statistics?.startActivities"
                :key="activity"
                type="success"
                size="small"
                class="activity-tag"
              >
                {{ activity }}
              </el-tag>
            </div>
          </div>

          <div class="feature-item">
            <div class="feature-header">
              <el-icon color="#f56c6c">
                <SwitchButton />
              </el-icon>
              <span class="feature-title">结束活动</span>
            </div>
            <div class="feature-content">
              <el-tag
                v-for="activity in discoveryResult?.statistics?.endActivities"
                :key="activity"
                type="danger"
                size="small"
                class="activity-tag"
              >
                {{ activity }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 流程复杂度指标 -->
      <div class="complexity-metrics">
        <h4 class="section-title">流程复杂度</h4>
        <div class="metrics-container">
          <div class="metric-item">
            <div class="metric-label">节点密度</div>
            <el-progress
              :percentage="calculateNodeDensity()"
              :stroke-width="8"
              :show-text="false"
              class="metric-progress"
            />
            <div class="metric-value">{{ calculateNodeDensity() }}%</div>
          </div>

          <div class="metric-item">
            <div class="metric-label">连接复杂度</div>
            <el-progress
              :percentage="calculateConnectivityComplexity()"
              :stroke-width="8"
              :show-text="false"
              class="metric-progress"
              color="#e6a23c"
            />
            <div class="metric-value">{{ calculateConnectivityComplexity() }}%</div>
          </div>

          <div class="metric-item">
            <div class="metric-label">变体多样性</div>
            <el-progress
              :percentage="calculateVariantDiversity()"
              :stroke-width="8"
              :show-text="false"
              class="metric-progress"
              color="#f56c6c"
            />
            <div class="metric-value">{{ calculateVariantDiversity() }}%</div>
          </div>
        </div>
      </div>

      <!-- 下一步建议 -->
      <div class="next-steps">
        <h4 class="section-title">建议的下一步操作</h4>
        <div class="suggestions-list">
          <div class="suggestion-item" @click="navigateToDiscovery">
            <el-icon color="#667eea">
              <View />
            </el-icon>
            <span>查看详细的流程发现结果和DFG图</span>
          </div>
          <div class="suggestion-item" @click="navigateToPerformance">
            <el-icon color="#e6a23c">
              <TrendCharts />
            </el-icon>
            <span>进行性能分析，识别流程瓶颈</span>
          </div>
          <div class="suggestion-item" @click="navigateToConformance">
            <el-icon color="#f56c6c">
              <DocumentChecked />
            </el-icon>
            <span>执行符合性检查，对比标准流程</span>
          </div>
          <div class="suggestion-item" @click="navigateToSubprocess">
            <el-icon color="#67c23a">
              <Share />
            </el-icon>
            <span>探索子流程发现，深入分析流程片段</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <div class="step-actions">
        <el-button @click="handleRestart" size="large">
          <el-icon class="mr-2">
            <Refresh />
          </el-icon>
          重新开始
        </el-button>
        <el-button
          type="primary"
          @click="handleViewDetails"
          size="large"
        >
          查看详细结果
          <el-icon class="ml-2">
            <ArrowRight />
          </el-icon>
        </el-button>
      </div>
    </template>
  </el-card>
</template>

<script setup lang="ts">
import {
  CircleCheck,
  DataBoard,
  Operation,
  Timer,
  Connection,
  Right,
  SwitchButton,
  Share,
  View,
  TrendCharts,
  DocumentChecked,
  Refresh,
  ArrowRight
} from '@element-plus/icons-vue'
import type { DFGResult } from '~/types'

// Props
interface Props {
  processId: number
  discoveryResult: DFGResult | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  viewDetails: []
  restart: []
}>()

// 调试信息
console.log('SuccessStep - discoveryResult:', props.discoveryResult)
console.log('SuccessStep - statistics:', props.discoveryResult?.statistics)

// 检查数据完整性
if (props.discoveryResult && !props.discoveryResult.statistics) {
  console.error('SuccessStep - Missing statistics in discoveryResult!')
}
if (props.discoveryResult?.statistics && typeof props.discoveryResult.statistics.totalCases === 'undefined') {
  console.error('SuccessStep - Missing totalCases in statistics!')
}

// 方法
const formatDuration = (milliseconds?: number): string => {
  if (!milliseconds) return '0分钟'
  
  const minutes = Math.floor(milliseconds / 60000)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) return `${days}天${hours % 24}小时`
  if (hours > 0) return `${hours}小时${minutes % 60}分钟`
  return `${minutes}分钟`
}

const calculateNodeDensity = (): number => {
  if (!props.discoveryResult) return 0
  const nodes = props.discoveryResult.nodes.length
  const maxNodes = 20 // 假设最大节点数为20
  return Math.min(100, Math.round((nodes / maxNodes) * 100))
}

const calculateConnectivityComplexity = (): number => {
  if (!props.discoveryResult) return 0
  const edges = props.discoveryResult.edges.length
  const nodes = props.discoveryResult.nodes.length
  if (nodes <= 1) return 0
  
  const maxPossibleEdges = nodes * (nodes - 1)
  const complexity = (edges / maxPossibleEdges) * 100
  return Math.min(100, Math.round(complexity))
}

const calculateVariantDiversity = (): number => {
  if (!props.discoveryResult?.statistics) return 0
  // 基于案例数量和活动数量的简单多样性计算
  const cases = props.discoveryResult.statistics.totalCases
  const activities = props.discoveryResult.statistics.totalActivities

  if (cases === 0 || activities === 0) return 0

  const diversity = Math.min(100, (activities / cases) * 100)
  return Math.round(diversity)
}

const handleViewDetails = () => {
  emit('viewDetails')
}

const handleRestart = () => {
  emit('restart')
}

const navigateToDiscovery = () => {
  navigateTo(`/analysis/${props.processId}/discover`)
}

const navigateToPerformance = () => {
  navigateTo(`/analysis/${props.processId}/performance`)
}

const navigateToConformance = () => {
  navigateTo(`/analysis/${props.processId}/conformance`)
}

const navigateToSubprocess = () => {
  navigateTo(`/analysis/${props.processId}/subprocess`)
}
</script>

<style lang="scss" scoped>
.success-step-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  :deep(.el-card__header) {
    background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
    color: white;
    border-radius: 16px 16px 0 0;
  }

  .step-header {
    text-align: center;

    .success-icon {
      margin-bottom: 1rem;
      i {
        color: white;
      }
    }

    .step-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0 0 0.5rem 0;
    }

    .step-description {
      margin: 0;
      opacity: 0.9;
    }
  }

  .success-content {
    padding: 2rem 0;

    .section-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: #333;
      margin: 0 0 1rem 0;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #f0f0f0;
    }

    .statistics-overview {
      margin-bottom: 2rem;

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;

        .stat-card {
          display: flex;
          align-items: center;
          padding: 1.5rem;
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          border-radius: 12px;
          border: 1px solid #e9ecef;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
          }

          .stat-icon {
            margin-right: 1rem;
          }

          .stat-content {
            .stat-value {
              font-size: 1.5rem;
              font-weight: 600;
              color: #333;
              margin-bottom: 0.25rem;
            }

            .stat-label {
              font-size: 0.875rem;
              color: #666;
            }
          }
        }
      }
    }

    .process-features {
      margin-bottom: 2rem;

      .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;

        .feature-item {
          padding: 1rem;
          background: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #e9ecef;

          .feature-header {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;

            .feature-title {
              margin-left: 0.5rem;
              font-weight: 500;
              color: #333;
            }
          }

          .feature-content {
            .activity-tag {
              margin-right: 0.5rem;
              margin-bottom: 0.5rem;
            }
          }
        }
      }
    }

    .complexity-metrics {
      margin-bottom: 2rem;

      .metrics-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;

        .metric-item {
          padding: 1rem;
          background: #f8f9fa;
          border-radius: 8px;
          text-align: center;

          .metric-label {
            font-size: 0.875rem;
            color: #666;
            margin-bottom: 0.5rem;
          }

          .metric-progress {
            margin-bottom: 0.5rem;
          }

          .metric-value {
            font-weight: 600;
            color: #333;
          }
        }
      }
    }

    .next-steps {
      .suggestions-list {
        .suggestion-item {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 0.875rem 1rem;
          margin-bottom: 0.5rem;
          background: #fafafa;
          border: 1px solid #eee;
          border-radius: 10px;
          cursor: pointer;
          transition: background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease, transform 0.15s ease;

          &:hover {
            background: #f5f7fa;
            border-color: #e4e7ed;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
            transform: translateY(-1px);
          }

          &:active {
            transform: translateY(0);
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.06);
          }

          .el-icon {
            transition: opacity 0.2s ease;
          }

          span {
            color: #303133;
            font-weight: 500;
            transition: color 0.2s ease;
          }

          &:hover span { color: #1f2328; }
        }
      }
    }
  }

  :deep(.el-card__footer) {
    background: #f8f9fa;
    border-radius: 0 0 16px 16px;

    .step-actions {
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
