<template>
  <el-card class="upload-step-card">
    <template #header>
      <div class="step-header">
        <h3 class="step-title">上传事件日志文件</h3>
        <p class="step-description">请选择包含流程事件数据的CSV或Excel文件</p>
      </div>
    </template>

    <div class="upload-content">
      <!-- 文件上传区域 -->
      <div class="upload-section">
        <el-upload
          v-show="!selectedFile"
          ref="uploadRef"
          class="file-uploader"
          drag
          :auto-upload="false"
          :limit="1"
          accept=".csv,.xlsx,.xls"
          :disabled="isLoadingPreview"
          :file-list="fileList"
          :show-file-list="false"
          @change="handleFileChange"
          @remove="handleFileRemove"
        >
          <div class="upload-area">
            <el-icon class="upload-icon" :size="48">
              <Upload />
            </el-icon>
            <div class="upload-text">
              <p class="primary-text">将文件拖拽到此处，或点击选择文件</p>
              <p class="secondary-text">支持 CSV、Excel 格式，文件大小不超过 100MB</p>
            </div>
          </div>
        </el-upload>

        <!-- 文件信息显示 -->
        <div v-if="selectedFile" class="file-info">
          <div class="file-card">
            <div class="file-icon">
              <el-icon :size="24">
                <Document />
              </el-icon>
            </div>
            <div class="file-details">
              <div class="file-name">{{ selectedFile.name }}</div>
              <div class="file-meta">
                <span class="file-size">{{ formatFileSize(selectedFile.size) }}</span>
                <span class="file-type">{{ getFileType(selectedFile.name) }}</span>
              </div>
            </div>
            <div class="file-actions">
              <el-button
                type="danger"
                :icon="Delete"
                circle
                size="small"
                :disabled="isLoadingPreview"
                @click="removeFile"
              />
            </div>
          </div>
        </div>

        <!-- 文件内容预览 -->
        <div v-if="selectedFile && filePreview" class="file-preview">
          <div class="preview-header">
            <div class="preview-header-main">
              <h4 class="preview-title">文件内容预览</h4>
              <span class="preview-notice">仅显示前 {{ Math.min(filePreview.data.length, 10) }} 行数据用于预览。请在下一步中配置字段映射。</span>
            </div>
            <div class="preview-stats">
              <span class="preview-rows">共 {{ filePreview.totalRows }} 行数据</span>
              <span class="preview-columns">{{ filePreview.columns.length }} 列</span>
            </div>
          </div>

          <div class="preview-table-container">
            <el-table
              ref="previewTableRef"
              :data="filePreview.data"
              class="preview-table custom-header-table"
              max-height="400"
              stripe
              border
              show-header
            >
              <el-table-column
                v-for="column in filePreview.columns"
                :key="column"
                :prop="column"
                :min-width="120"
                show-overflow-tooltip
              >
                <template #header>
                  <div class="column-header">
                    <span class="column-name">{{ column }}</span>
                    <div class="time-field-controls">
                      <!-- 时间标签 -->
                      <el-tag
                        v-if="isTimeColumn(column)"
                        type="info"
                        size="small"
                        class="time-tag"
                      >
                        日期格式
                      </el-tag>

                      <!-- 控制按钮 -->
                      <div v-if="isAutoDetectedTimeColumn(column) || isTimeColumn(column)" class="time-control-buttons">
                        <el-button
                          v-if="isTimeColumn(column)"
                          type="danger"
                          size="small"
                          :icon="Close"
                          circle
                          @click.stop="toggleTimeFieldRecognition(column)"
                          class="time-control-btn"
                          title="取消时间识别"
                        />
                        <el-button
                          v-else
                          type="success"
                          size="small"
                          :icon="Check"
                          circle
                          @click.stop="toggleTimeFieldRecognition(column)"
                          class="time-control-btn"
                          title="启用时间识别"
                        />
                      </div>
                    </div>
                  </div>
                </template>
                <template #default="{ row }">
                  <div class="cell-content">
                    <span
                      :class="{ 'time-value': isTimeColumn(column) }"
                    >
                      {{ formatCellValue(row[column], column) }}
                    </span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="preview-footer">
            <div v-if="hasTimeColumns" class="time-format-notice">
              <el-alert
                title="时间格式处理"
                type="warning"
                :closable="false"
                show-icon
              >
                <template #default>
                  <div class="time-notice-content">
                    <p>系统已自动识别并处理时间字段：</p>
                    <ul class="time-format-list">
                      <li>Excel日期序列号已转换为标准时间格式</li>
                      <li>Unix时间戳（秒/毫秒）已转换为可读时间</li>
                      <li>各种日期时间字符串已标准化处理</li>
                      <li>支持格式：YYYY-MM-DD HH:mm:ss、MM/DD/YYYY HH:mm:ss 等</li>
                    </ul>
                  </div>
                </template>
              </el-alert>
            </div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="selectedFile && isLoadingPreview" class="loading-preview">
          <el-skeleton :rows="5" animated />
          <p class="loading-text">正在解析文件内容...</p>
        </div>


      </div>

      <!-- 文件要求说明 -->
      <div class="requirements-section">
        <h4 class="requirements-title">文件格式要求</h4>
        <div class="requirements-list">
          <div class="requirement-item">
            <el-icon class="requirement-icon" color="#67c23a">
              <CircleCheck />
            </el-icon>
            <span>文件必须包含案例ID、活动名称和时间戳字段</span>
          </div>
          <div class="requirement-item">
            <el-icon class="requirement-icon" color="#67c23a">
              <CircleCheck />
            </el-icon>
            <span>支持CSV（逗号分隔）和Excel格式</span>
          </div>
          <div class="requirement-item">
            <el-icon class="requirement-icon" color="#67c23a">
              <CircleCheck />
            </el-icon>
            <span>建议包含资源字段以获得更详细的分析</span>
          </div>
          <div class="requirement-item">
            <el-icon class="requirement-icon" color="#e6a23c">
              <Warning />
            </el-icon>
            <span>文件大小不超过100MB，行数建议在100万行以内</span>
          </div>
        </div>
      </div>

      <!-- 示例数据格式 -->
      <div class="example-section">
        <h4 class="example-title">示例数据格式</h4>
        <div class="example-table">
          <el-table :data="exampleData" size="small" border>
            <el-table-column prop="case_id" label="案例ID" width="100" />
            <el-table-column prop="activity" label="活动名称" width="150" />
            <el-table-column prop="timestamp" label="时间戳" width="180" />
            <el-table-column prop="resource" label="资源" width="100" />
            <el-table-column prop="cost" label="成本" width="80" />
          </el-table>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <div class="step-actions">
        <el-button
          type="primary"
          :disabled="!selectedFile || isLoadingPreview || !hasTimeColumns"
          @click="handleNext"
          size="large"
        >
          下一步：配置字段映射
          <el-icon class="ml-2">
            <ArrowRight />
          </el-icon>
        </el-button>
      </div>
    </template>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Upload, Document, Delete, CircleCheck, ArrowRight, Close, Check, Warning } from '@element-plus/icons-vue'
import type { UploadFile } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useDiscoveryWizardStore } from '~/stores/discoveryWizard'

// Emits
const emit = defineEmits<{
  timeFieldOverridesChanged: [overrides: Record<string, boolean>]
  next: []
}>()

const wizardStore = useDiscoveryWizardStore()

// 响应式数据
const uploadRef = ref()
const previewTableRef = ref()
const selectedFile = ref<File | null>(null)
const fileList = ref<UploadFile[]>([])
const filePreview = ref<{
  columns: string[]
  data: Record<string, unknown>[]
  totalRows: number
} | null>(null)
const isLoadingPreview = ref(false)

// 时间字段用户控制状态
const userTimeFieldOverrides = ref<Record<string, boolean>>({}) // true: 用户启用, false: 用户禁用

// 初始化文件状态
const initeFileState = () => {
  if (wizardStore.file) {
    selectedFile.value = wizardStore.file
    // 创建一个模拟的UploadFile对象
    const mockUploadFile: UploadFile = {
      name: wizardStore.file.name,
      size: wizardStore.file.size,
      raw: wizardStore.file as any,
      status: 'success',
      uid: Date.now()
    }
    fileList.value = [mockUploadFile]
  }

  if (wizardStore.preview) {
    filePreview.value = {
      columns: wizardStore.preview.columns,
      data: wizardStore.preview.data as Record<string, unknown>[],
      totalRows: wizardStore.preview.totalRows
    }
  }

  // 恢复时间字段手动配置
  if (wizardStore.timeOverrides) {
    userTimeFieldOverrides.value = { ...wizardStore.timeOverrides }
  }
}

// 计算属性
const hasTimeColumns = computed(() => {
  return wizardStore.timeColumns.length > 0
})

// 示例数据
const exampleData: Record<string, string | number>[] = [
  {
    case_id: '100126',
    activity: '财务会议安排确认 - 负责人',
    timestamp: '2025/5/17 17:51:11',
    resource: '995098661',
    cost: 100
  },
  {
    case_id: '100126',
    activity: '财务会议安排确认 - 负责人',
    timestamp: '2025/5/23 10:25:05',
    resource: '995098661',
    cost: 150
  },
  {
    case_id: '102234',
    activity: '品牌管理系统 - 品牌分析',
    timestamp: '2025/5/22 17:28:55',
    resource: '995020928',
    cost: 200
  }
]

// 方法
const handleFileChange = async (file: UploadFile) => {
  if (file.raw) {
    if (selectedFile.value && selectedFile.value.name !== file.raw.name) {
      wizardStore.clearMapping()
      wizardStore.updateTimeOverrides({})
    }

    selectedFile.value = file.raw
    fileList.value = [file]

    // 加载文件预览
    await loadFilePreview(file.raw)
  }
}

const loadFilePreview = async (file: File) => {
  isLoadingPreview.value = true
  filePreview.value = null

  try {
    const api = useApi()
    const result = await api.getFilePreview(file)

    const previewData = {
      columns: result.columns,
      data: result.preview.slice(0, 10), // 只显示前10行
      totalRows: result.totalRows
    }

    filePreview.value = previewData
    // 更新store
    wizardStore.setFile(file, previewData)
  } catch (error) {
    // 清空文件
    selectedFile.value = null;
    fileList.value = [];
    filePreview.value = null;
    ElMessage.error(
      error instanceof Error
        ? error.message
        : "加载文件预览失败，请检查文件格式或联系管理员"
    );
  } finally {
    isLoadingPreview.value = false
  }
}

const handleFileRemove = () => {
  if (isLoadingPreview.value) {
    ElMessage.warning('正在解析文件，请稍候完成后再删除')
    return
  }
  selectedFile.value = null
  fileList.value = []
  filePreview.value = null
  userTimeFieldOverrides.value = {} // 重置用户控制
  // 清空store
  wizardStore.clearFile()
  wizardStore.updateTimeOverrides({})
}

const removeFile = () => {
  if (isLoadingPreview.value) {
    ElMessage.warning('正在解析文件，请稍候完成后再删除')
    return
  }
  selectedFile.value = null
  fileList.value = []
  filePreview.value = null
  userTimeFieldOverrides.value = {} // 重置用户控制
  uploadRef.value?.clearFiles()
  // 清空store
  wizardStore.clearFile()
  wizardStore.updateTimeOverrides({})
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getFileType = (filename: string): string => {
  const ext = filename.split('.').pop()?.toLowerCase()
  switch (ext) {
    case 'csv': return 'CSV文件'
    case 'xlsx': return 'Excel文件'
    case 'xls': return 'Excel文件'
    default: return '未知格式'
  }
}

// 自动检测是否为时间列
const isAutoDetectedTimeColumn = (columnName: string): boolean => {
  return wizardStore.isAutoDetectedTimeColumn(columnName)
}

// 最终判断是否为时间列
const isTimeColumn = (columnName: string): boolean => {
  return wizardStore.timeColumns.includes(columnName)
}

// 切换时间字段识别状态
const toggleTimeFieldRecognition = (columnName: string) => {
  const isCurrentlyTimeColumn = isTimeColumn(columnName)

  if (isCurrentlyTimeColumn) {
    // 当前是时间列，用户要禁用它
    userTimeFieldOverrides.value[columnName] = false
  } else {
    // 当前不是时间列，用户要启用它
    userTimeFieldOverrides.value[columnName] = true
  }

  // 触发时间字段配置变化事件并更新store
  emit('timeFieldOverridesChanged', userTimeFieldOverrides.value)
  wizardStore.updateTimeOverrides(userTimeFieldOverrides.value)
}

const formatCellValue = (value: unknown, columnName: string): string => {
  if (!value) return ''

  // 如果是时间列，尝试格式化显示
  if (isTimeColumn(columnName)) {
    // 如果是数字，可能是Excel日期序列号或时间戳
    const numValue = Number(value)
    if (!isNaN(numValue)) {
      // Excel日期序列号
      if (numValue > 1 && numValue < 2958466) {
        try {
          const excelDate = new Date((numValue - 25569) * 86400 * 1000)
          if (excelDate.getFullYear() >= 1900 && excelDate.getFullYear() <= 2100) {
            return excelDate.toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            })
          }
        } catch {
          // 继续尝试其他格式
        }
      }

      // Unix时间戳
      if (numValue > 946684800 && numValue < 4102444800) {
        try {
          let date: Date
          if (numValue < 10000000000) { // 秒时间戳
            date = new Date(numValue * 1000)
          } else { // 毫秒时间戳
            date = new Date(numValue)
          }

          if (date.getFullYear() >= 1970 && date.getFullYear() <= 2100) {
            return date.toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            })
          }
        } catch {
          // 继续尝试其他格式
        }
      }
    }

    // 如果是字符串，尝试解析为日期
    if (typeof value === 'string') {
      try {
        const date = new Date(value)
        if (!isNaN(date.getTime())) {
          return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          })
        }
      } catch {
        // 保持原值
      }
    }
  }

  return String(value)
}

const handleNext = () => {
  if (selectedFile.value) {
    emit('next')
  }
}

// 监听store变化
watch(() => wizardStore.file, () => {
  initeFileState()
}, { immediate: true })

watch(() => wizardStore.preview, () => {
  initeFileState()
}, { immediate: true, deep: true })

// 监听时间字段配置变化
watch(() => wizardStore.timeOverrides, (newOverrides) => {
  if (newOverrides) {
    userTimeFieldOverrides.value = { ...newOverrides }
  }
}, { immediate: true, deep: true })

// 组件挂载时初始化
onMounted(() => {
  initeFileState()
})
</script>

<style lang="scss" scoped>
// 全局样式覆盖Element Plus表头
.upload-step-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  :deep(.el-card__header) {
    border-radius: 16px 16px 0 0;
  }

  .step-header {
    text-align: center;

    .step-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0 0 0.5rem 0;
    }

    .step-description {
      margin: 0;
      opacity: 0.9;
    }
  }

  .upload-content {
    padding: 2rem 0;

    .upload-section {
      margin-bottom: 2rem;

      .file-uploader {
        :deep(.el-upload-dragger) {
          border: 2px dashed #d9d9d9;
          border-radius: 12px;
          background: #fafafa;
          transition: all 0.3s ease;

          &:hover {
            border-color: #667eea;
            background: #f0f2ff;
          }
        }

        .upload-area {
          padding: 2rem;
          text-align: center;

          .upload-icon {
            color: #667eea;
            margin-bottom: 1rem;
          }

          .upload-text {
            .primary-text {
              font-size: 1.1rem;
              color: #333;
              margin: 0 0 0.5rem 0;
            }

            .secondary-text {
              color: #666;
              margin: 0;
            }
          }
        }
      }

      .file-info {
        margin-top: 1rem;

        .file-card {
          display: flex;
          align-items: center;
          padding: 1rem;
          background: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #e9ecef;

          .file-icon {
            color: #667eea;
            margin-right: 1rem;
          }

          .file-details {
            flex: 1;

            .file-name {
              font-weight: 500;
              color: #333;
              margin-bottom: 0.25rem;
            }

            .file-meta {
              font-size: 0.875rem;
              color: #666;

              span {
                margin-right: 1rem;
              }
            }
          }
        }
      }
    }

    .requirements-section,
    .example-section {
      margin-bottom: 2rem;

      .requirements-title,
      .example-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin: 0 0 1rem 0;
      }

      .requirements-list {
        .requirement-item {
          display: flex;
          align-items: center;
          margin-bottom: 0.75rem;

          .requirement-icon {
            margin-right: 0.5rem;
          }
        }
      }

      .example-table {
        :deep(.el-table) {
          border-radius: 8px;
          overflow: hidden;
        }
      }
    }
  }

  :deep(.el-card__footer) {
    background: #f8f9fa;
    border-radius: 0 0 16px 16px;

    .step-actions {
      text-align: center;
    }
  }

  .file-preview {
    margin-top: 24px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 24px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    .preview-header {
      margin-bottom: 16px;
      padding-bottom: 16px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      .preview-header-main {
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex: 1;

        .preview-title {
          margin: 0 0 12px 0;
          font-size: 18px;
          font-weight: 600;
          color: #2c3e50;
        }

        .preview-notice {
          display: block;
          color: #666;
          font-style: italic;
          font-size: 13px;
          line-height: 1.4;
          margin-bottom: 0;
        }
      }

      .preview-stats {
        display: flex;
        gap: 12px;
        align-items: center;
        height: auto;
        .preview-rows,
        .preview-columns {
          padding: 4px 12px;
          background: rgba(64, 158, 255, 0.1);
          border-radius: 12px;
          color: #409eff;
          font-weight: 500;
          font-size: 13px;
        }
      }
    }

    .preview-table-container {
      margin-bottom: 16px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .preview-table {
        border-radius: 8px;
        overflow: hidden;
      }
    }

    .preview-footer {
      margin-top: 16px;

      :deep(.el-alert) {
        border-radius: 8px;
        border: none;
        background: rgba(144, 202, 249, 0.1);
      }
    }
  }

  .loading-preview {
    margin-top: 24px;
    padding: 24px;
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);

    .loading-text {
      margin-top: 16px;
      color: #666;
      font-size: 14px;
    }
  }

  // 时间列相关样式
  .column-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;

    .column-name {
      font-weight: 600;
      flex-shrink: 0;
    }

  .time-field-controls {
      display: flex;
      align-items: center;
      gap: 4px;
      flex-wrap: wrap;

      .time-tag,
      .format-tag,
      .confidence-tag {
        font-size: 9px;
        padding: 1px 4px;
        border: none;
        border-radius: 3px;
        font-weight: 500;
        line-height: 1.2;

        &.el-tag--warning {
          background: rgba(255, 193, 7, 0.9);
          color: #856404;
        }

        &.el-tag--success {
          background: rgba(103, 194, 58, 0.9);
          color: #529b2e;
        }

        &.el-tag--info {
          background: rgba(64, 158, 255, 0.9);
          color: #ffffff;
        }

        &.el-tag--primary {
          background: rgba(102, 126, 234, 0.9);
          color: #ffffff;
        }

        &.el-tag--danger {
          background: rgba(245, 108, 108, 0.9);
          color: #ffffff;
        }
      }

      .error-tooltip {
        display: flex;
        align-items: center;
      }

      .time-control-buttons {
        display: flex;
        gap: 4px;

        .time-control-btn {
          width: 18px;
          height: 18px;
          padding: 0;
          min-height: 18px;

          :deep(.el-icon) {
            font-size: 12px;
          }

          &.el-button--danger {
            background: rgba(245, 108, 108, 0.1);
            border-color: rgba(245, 108, 108, 0.3);
            color: #f56c6c;

            &:hover {
              background: rgba(245, 108, 108, 0.2);
              border-color: #f56c6c;
            }
          }

          &.el-button--success {
            background: rgba(103, 194, 58, 0.1);
            border-color: rgba(103, 194, 58, 0.3);
            color: #67c23a;

            &:hover {
              background: rgba(103, 194, 58, 0.2);
              border-color: #67c23a;
            }
          }
        }
      }
    }
  }

  .cell-content {
    .time-value {
      color: #fa8c16;
      font-weight: 500;
      background: rgba(255, 193, 7, 0.1);
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 13px;
    }
  }

  .time-format-notice {
    margin-top: 16px;

    .time-notice-content {
      p {
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .time-format-list {
        margin: 8px 0 0 0;
        padding-left: 20px;

        li {
          margin: 4px 0;
          font-size: 13px;
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }
  }
}
</style>
