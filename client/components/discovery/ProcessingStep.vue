<template>
  <el-card class="processing-step-card">
    <template #header>
      <div class="step-header">
        <h3 class="step-title">正在处理数据</h3>
        <p class="step-description">请稍候，我们正在分析您的流程数据</p>
      </div>
    </template>

    <div class="processing-content">
      <!-- 总体进度 -->
      <div class="overall-progress">
        <div class="progress-info">
          <h4 class="progress-title">处理进度</h4>
          <div class="progress-meta">
            <span class="progress-text">{{ overallProgress }}%</span>
            <span v-if="estimatedTime" class="time-info">
              预计剩余时间: {{ estimatedTime }}
            </span>
          </div>
        </div>
        <el-progress
          :percentage="overallProgress"
          :status="progressStatus"
          :stroke-width="12"
          class="main-progress"
        />
      </div>

      <!-- 处理步骤 -->
      <div class="processing-steps">
        <h4 class="steps-title">处理步骤</h4>
        <div class="steps-list">
          <div
            v-for="step in processingSteps"
            :key="step.id"
            class="step-item"
            :class="{
              'active': step.status === 'processing',
              'completed': step.status === 'completed',
              'failed': step.status === 'failed'
            }"
          >
            <div class="step-icon">
              <el-icon v-if="step.status === 'completed'" color="#67c23a">
                <CircleCheck />
              </el-icon>
              <el-icon v-else-if="step.status === 'failed'" color="#f56c6c">
                <CircleClose />
              </el-icon>
              <el-icon v-else-if="step.status === 'processing'" class="rotating">
                <Loading />
              </el-icon>
              <el-icon v-else color="#d9d9d9">
                <Clock />
              </el-icon>
            </div>

            <div class="step-content">
              <div class="step-header-info">
                <span class="step-name">{{ step.name }}</span>
                <span v-if="step.endTime" class="step-time">
                  {{ formatDuration(step.startTime, step.endTime) }}
                </span>
              </div>
              <div class="step-description">{{ step.description }}</div>

              <!-- 步骤进度条 -->
              <el-progress
                v-if="step.status === 'processing'"
                :percentage="step.progress"
                :show-text="false"
                :stroke-width="4"
                class="step-progress"
              />

              <!-- 错误信息 -->
              <div v-if="step.status === 'failed' && (step.error || step.validationErrors)" class="step-error">
                <el-alert
                  type="error"
                  :closable="false"
                  :show-icon="false"
                  class="error-alert"
                >
                  <template #default>
                    <div v-if="step.validationErrors && step.validationErrors.length > 0" class="validation-errors">
                      <div class="error-summary">
                        <div class="summary-left">
                          <el-icon class="warn-icon"><CircleClose /></el-icon>
                          <span class="summary-text">共发现 {{ step.validationErrors.length }} 个验证错误</span>
                        </div>
                        <div class="summary-right">
                          <el-button
                            v-if="step.validationErrors.length > 3"
                            link
                            size="small"
                            class="toggle-button"
                            @click="toggleErrorExpansion(step.id)"
                          >
                            {{ isErrorExpanded(step.id) ? '收起' : '展开' }}
                            <el-icon>
                              <ArrowDown v-if="!isErrorExpanded(step.id)" />
                              <ArrowUp v-else />
                            </el-icon>
                          </el-button>
                        </div>
                      </div>
                      <div
                        class="error-list"
                        :class="{
                          'collapsed': !isErrorExpanded(step.id) && step.validationErrors.length > 3,
                          'expanded': isErrorExpanded(step.id) || step.validationErrors.length <= 3
                        }"
                        tabindex="0"
                        role="region"
                        aria-label="验证错误列表"
                      >
                        <ul>
                          <li
                            v-for="(error, index) in getDisplayedErrors(step)"
                            :key="index"
                            class="error-item"
                          >
                            {{ error }}
                          </li>
                        </ul>
                        <div
                          v-if="!isErrorExpanded(step.id) && step.validationErrors.length > 3"
                          class="gradient-mask"
                          aria-hidden="true"
                        />
                      </div>
                    </div>
                    <div v-else class="single-error">
                      {{ step.error || '处理失败' }}
                    </div>
                  </template>
                </el-alert>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实时日志 -->
      <div class="processing-logs">
        <div class="logs-card">
          <div class="logs-summary">
            <div class="summary-left">
              <el-icon class="log-icon"><Clock /></el-icon>
              <span class="summary-text">处理日志</span>
            </div>
            <div class="summary-right">
              <el-button
                link
                class="toggle-button"
                size="small"
                @click="showLogs = !showLogs"
              >
                {{ showLogs ? '收起' : '展开' }}
                <el-icon>
                  <ArrowDown v-if="!showLogs" />
                  <ArrowUp v-else />
                </el-icon>
              </el-button>
            </div>
          </div>
          <div
            class="logs-list"
            :class="{ 'collapsed': !showLogs, 'expanded': showLogs }"
            tabindex="0"
            role="region"
            aria-label="处理日志列表"
          >
            <div
              v-for="(log, index) in aggregatedLogs"
              :key="index"
              class="log-entry"
              :class="log.level"
            >
              <span class="level-dot" aria-hidden="true" />
              <span class="log-time">{{ formatTime(log.lastAt) }}</span>
              <span class="log-message">{{ log.message }}</span>
              <span v-if="log.count > 1" class="log-count">×{{ log.count }}</span>
            </div>
            <div v-if="!showLogs && aggregatedLogs.length > 0" class="gradient-mask" aria-hidden="true" />
          </div>
        </div>
      </div>

      <!-- 统计信息 -->
      <div v-if="currentStats" class="processing-stats">
        <h4 class="stats-title">处理统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ currentStats.totalRows ?? '-' }}</div>
            <div class="stat-label">总行数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ currentStats.uniqueCases ?? '-' }}</div>
            <div class="stat-label">案例数量</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ currentStats.uniqueActivities ?? '-' }}</div>
            <div class="stat-label">活动数量</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <div class="step-actions">
        <el-button
          v-if="processingStatus?.status === 'failed'"
          type="danger"
          size="large"
          @click="handleRetry"
        >
          重试
        </el-button>
        <el-button
          v-if="processingStatus?.status === 'processing'"
          :loading="isCancelling"
          size="large"
          @click="handleCancel"
        >
          取消处理
        </el-button>
        <div v-if="processingStatus?.status === 'completed'" class="success-message">
          <el-icon color="#67c23a" :size="20">
            <CircleCheck />
          </el-icon>
          <span>数据处理完成！正在跳转到结果页面...</span>
        </div>
        <div v-if="processingStatus?.status === 'cancelled'" class="cancelled-message">
          <el-icon color="#e6a23c" :size="20">
            <CircleClose />
          </el-icon>
          <span>处理已取消</span>
        </div>
      </div>
    </template>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from 'vue'
import { CircleCheck, CircleClose, Loading, Clock, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import type { ProcessingStatus, ProcessingStep, DFGResult } from '~/types'

// Props
interface Props {
  processId: number
  processingStatus: ProcessingStatus | null
  totalRows?: number
}

const props = withDefaults(defineProps<Props>(), {
  totalRows: 0
})

// Emits
const emit = defineEmits<{
  completed: [result: DFGResult]
  failed: [error: string]
  cancelled: [reason: string]
  retry: []
}>()

// 响应式数据
const showLogs = ref(false)

type LogLevel = 'info' | 'warning' | 'error'
interface AggLog {
  key: string
  level: LogLevel
  message: string
  firstAt: Date
  lastAt: Date
  count: number
}

const CAP_LOGS = 200
const logMap = ref<Map<string, AggLog>>(new Map())
const logOrder = ref<string[]>([])
const aggregatedLogs = computed<AggLog[]>(() =>
  logOrder.value
    .map((k) => logMap.value.get(k)!)
    .filter((v): v is AggLog => !!v)
)

interface ProcessingStatsVM {
  totalRows?: number
  uniqueCases?: number
  uniqueActivities?: number
}
const currentStats = ref<ProcessingStatsVM | null>(null)
const expandedErrors = ref<Set<string>>(new Set())
const pollingInterval = ref<NodeJS.Timeout | null>(null)

const isCancelling = ref(false) // 添加取消加载状态

// 默认处理步骤
const defaultSteps: ProcessingStep[] = [
  {
    id: 'transform',
    name: '数据转换',
    description: '转换数据格式并验证字段',
    status: 'pending',
    progress: 0
  },
  {
    id: 'model',
    name: '创建数据模型',
    description: '构建流程数据模型',
    status: 'pending',
    progress: 0
  },
  {
    id: 'load',
    name: '加载流程数据',
    description: '将数据加载到流程模型中',
    status: 'pending',
    progress: 0
  },
  {
    id: 'discover',
    name: '流程发现',
    description: '分析流程并生成DFG图',
    status: 'pending',
    progress: 0
  }
]

const processingSteps = ref<ProcessingStep[]>([...defaultSteps])

// 根据ProcessingStatus生成步骤
const generateSteps = (status: ProcessingStatus): ProcessingStep[] => {
  const steps: ProcessingStep[] = [
    {
      id: 'parse',
      name: '解析文件',
      description: '解析上传的文件，提取数据结构',
      status: 'pending',
      progress: 0
    },
    {
      id: 'validate',
      name: '验证数据',
      description: '验证数据完整性和字段映射',
      status: 'pending',
      progress: 0
    },
    {
      id: 'save',
      name: '保存数据',
      description: '保存事件日志到数据库',
      status: 'pending',
      progress: 0
    },
    {
      id: 'discover',
      name: '流程发现',
      description: '执行流程挖掘算法，生成DFG图',
      status: 'pending',
      progress: 0
    }
  ]

  const stepIds = ['parse', 'validate', 'save', 'discover']
  const currentStepIndex = stepIds.indexOf(status.currentStepId || status.phase)
  const failedStepIndex = status.failedStepId ? stepIds.indexOf(status.failedStepId) : -1

  steps.forEach((step, index) => {
    if (status.status === 'failed' && failedStepIndex >= 0) {
      // 失败状态处理
      if (index < failedStepIndex) {
        step.status = 'completed'
        step.progress = 100
      } else if (index === failedStepIndex) {
        step.status = 'failed'
        step.progress = 0
        step.error = status.phaseError || status.error
        step.validationErrors = status.validationErrors
      }
    } else if (status.status === 'completed') {
      // 完成状态
      step.status = 'completed'
      step.progress = 100
    } else if (status.status === 'cancelled') {
      // 取消状态
      if (index < currentStepIndex) {
        step.status = 'completed'
        step.progress = 100
      } else if (index === currentStepIndex) {
        step.status = 'pending'
        step.progress = 0
      }
    } else {
      // 处理中状态
      if (index < currentStepIndex) {
        step.status = 'completed'
        step.progress = 100
      } else if (index === currentStepIndex) {
        step.status = 'processing'
        // 根据总进度计算当前步骤进度
        const stepProgress = ((status.progress % 25) * 4)
        step.progress = Math.min(100, Math.max(0, stepProgress))
      }
    }
  })

  return steps
}

// 计算属性
const overallProgress = computed(() => {
  if (!props.processingStatus) return 0
  return props.processingStatus.progress || 0
})

const progressStatus = computed(() => {
  if (!props.processingStatus) return undefined
  switch (props.processingStatus.status) {
    case 'completed': return 'success'
    case 'failed': return 'exception'
    case 'cancelled': return 'warning'
    default: return undefined
  }
})

const estimatedTime = computed(() => {
  // 根据当前进度估算剩余时间
  if (overallProgress.value === 0) return null
  if (overallProgress.value >= 100) return null

  const remainingProgress = 100 - overallProgress.value
  const estimatedMinutes = Math.ceil(remainingProgress / 10) // 简单估算

  if (estimatedMinutes < 1) return '不到1分钟'
  if (estimatedMinutes === 1) return '约1分钟'
  return `约${estimatedMinutes}分钟`
})

// 方法
const formatDuration = (startTime?: string, endTime?: string): string => {
  if (!startTime || !endTime) return ''
  const start = new Date(startTime)
  const end = new Date(endTime)
  const duration = end.getTime() - start.getTime()
  const seconds = Math.floor(duration / 1000)

  if (seconds < 60) return `${seconds}秒`
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}分${remainingSeconds}秒`
}

const formatTime = (timestamp: Date): string => {
  return timestamp.toLocaleTimeString()
}

// 错误展开相关方法
const toggleErrorExpansion = (stepId: string) => {
  if (expandedErrors.value.has(stepId)) {
    expandedErrors.value.delete(stepId)
  } else {
    expandedErrors.value.add(stepId)
  }
}

const isErrorExpanded = (stepId: string): boolean => {
  return expandedErrors.value.has(stepId)
}


const getDisplayedErrors = (step: ProcessingStep): string[] => {
  if (!step.validationErrors) return []
  return step.validationErrors
}

function addLog(level: LogLevel, message: string) {
  const key = `${level}|${message}`
  const now = new Date()
  const hit = logMap.value.get(key)
  if (hit) {
    hit.count += 1
    hit.lastAt = now
  } else {
    logMap.value.set(key, { key, level, message, firstAt: now, lastAt: now, count: 1 })
    logOrder.value.unshift(key)
    if (logOrder.value.length > CAP_LOGS) {
      const evict = logOrder.value.pop()
      if (evict) logMap.value.delete(evict)
    }
  }
}

// 添加处理日志
const addProcessingLog = (message: string, status: string) => {
  if (!message) return

  let level: 'info' | 'warning' | 'error' = 'info'
  if (status === 'failed') {
    level = 'error'
  } else if (status === 'cancelled') {
    level = 'warning'
  }

  addLog(level, message)
}

const logStatusDetails = (status: ProcessingStatus) => {
  if (status.status === 'failed') {
    // 验证错误优先：逐条输出（仅前5条）
    if (status.validationErrors && status.validationErrors.length > 0) {
      const maxShow = 5
      status.validationErrors.slice(0, maxShow).forEach((e, i) => addLog('error', `验证错误${i + 1}: ${e}`))
      if (status.validationErrors.length > maxShow) {
        addLog('error', `... 还有 ${status.validationErrors.length - maxShow} 条验证错误`)
      }
    } else if (status.error || status.phaseError) {
      addLog('error', status.phaseError || status.error!)
    }
  }
}

// 更新统计信息
const updateStats = (status: ProcessingStatus) => {
  if (!status) return

  const stats = status.result?.statistics || {}

  currentStats.value = {
    totalRows: stats.totalRows ?? props.totalRows ?? undefined,
    uniqueCases: stats.totalCases ?? undefined,
    uniqueActivities: stats.totalActivities ?? undefined,
  }
}

const stopPolling = () => {
  if (pollingInterval.value) {
    clearInterval(pollingInterval.value)
    pollingInterval.value = null
  }
}

const handleRetry = () => {
  // 重置状态并重新开始
  processingSteps.value = [...defaultSteps]
  logMap.value.clear();
  logOrder.value = [];
  currentStats.value = null
  // 触发重试事件，让父组件处理
  emit('retry')
}

const handleCancel = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消当前处理任务吗？此操作不可撤销。',
      '确认取消',
      {
        confirmButtonText: '取消任务',
        cancelButtonText: '继续处理',
        type: 'warning',
      }
    )

    const discoveryId = props.processingStatus?.discoveryId

    if (discoveryId) {
      try {
        isCancelling.value = true // 开始加载状态
        console.log('Calling cancel API for discoveryId:', discoveryId)
        const api = useApi()

        const result = await api.cancelDiscovery(discoveryId)
        console.log('Cancel API result:', result)

        stopPolling()

        // 触发取消事件
        emit('cancelled', '用户取消了处理任务')

        ElMessage.success('任务已取消')
      } catch (error) {
        console.error('取消任务失败:', error)
        ElMessage.error('取消任务失败，请重试')
      } finally {
        isCancelling.value = false // 结束加载状态
      }
    } else {
      // 如果没有ID，只停止轮询
      console.warn('No discoveryId found, only stopping polling')
      stopPolling()
      emit('cancelled', '用户取消了处理任务')
      ElMessage.warning('处理已取消')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消操作失败:', error)
    }
    isCancelling.value = false // 确保在出错时也结束加载状态
  }
}

// 监听 processingStatus 变化，更新步骤数据
watch(
  () => props.processingStatus,
  (newStatus) => {
    if (newStatus) {
      // 根据新的字段生成步骤状态
      processingSteps.value = generateSteps(newStatus)

      // 添加处理日志（阶段信息简述）
      addProcessingLog(newStatus.currentTask, newStatus.status)
      // 针对失败/验证错误，补充更具体原因
      logStatusDetails(newStatus)

      // 更新统计信息
      updateStats(newStatus)
    }
  },
  { immediate: true, deep: true }
)

onUnmounted(() => {
  stopPolling()
})
</script>

<style lang="scss" scoped>
.processing-step-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  :deep(.el-card__header) {
    // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    // color: white;
    border-radius: 16px 16px 0 0;
  }

  .step-header {
    text-align: center;

    .step-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0 0 0.5rem 0;
    }

    .step-description {
      margin: 0;
      opacity: 0.9;
    }
  }

  .processing-content {
    padding: 2rem 0;

    .overall-progress {
      margin-bottom: 2rem;

      .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        .progress-title {
          font-size: 1.1rem;
          font-weight: 600;
          color: #333;
          margin: 0;
        }

        .progress-meta {
          display: flex;
          align-items: center;
          gap: 1rem;

          .progress-text {
            font-size: 1.2rem;
            font-weight: 600;
            color: #667eea;
          }

          .time-info {
            font-size: 0.875rem;
            color: #666;
          }
        }
      }

      .main-progress {
        :deep(.el-progress-bar__outer) {
          border-radius: 8px;
        }
      }
    }

    .processing-steps {
      margin-bottom: 2rem;

      .steps-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin: 0 0 1rem 0;
      }

      .steps-list {
        .step-item {
          display: flex;
          align-items: flex-start;
          padding: 1rem;
          margin-bottom: 0.5rem;
          border-radius: 8px;
          transition: all 0.3s ease;

          &.active {
            background: #f0f2ff;
            border: 1px solid #667eea;
          }

          &.completed {
            background: #f0f9ff;
          }

          &.failed {
            background: #fef2f2;
          }

          .step-icon {
            margin-right: 1rem;
            margin-top: 0.25rem;

            .rotating {
              animation: rotate 1s linear infinite;
            }
          }

          .step-content {
            flex: 1;
            width: 100%;

            .step-header-info {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 0.25rem;

              .step-name {
                font-weight: 500;
                color: #333;
              }

              .step-time {
                font-size: 0.875rem;
                color: #666;
              }
            }

            .step-description {
              color: #666;
              font-size: 0.875rem;
              margin-bottom: 0.5rem;
            }

            .step-progress {
              margin-top: 0.5rem;
            }

            .step-error {
              margin-top: 0.5rem;
              width: 100%;

              :deep(.error-alert) {
                width: 100%;
                display: block;
                align-items: stretch;
                --el-alert-padding: 0;
              }
              :deep(.error-alert .el-alert__content) {
                width: 100%;
                display: block;
                flex: 1;
                padding: 0;
              }

              .validation-errors {
                background: #fff7f7;
                border: 1px solid #fde2e2;
                border-radius: 8px;
                padding: 10px 12px;

                .error-summary {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  margin-bottom: 8px;
                  padding-bottom: 8px;
                  border-bottom: 1px dashed #f5c2c2;

                  .summary-left {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .warn-icon {
                      color: #f56c6c;
                      font-size: 16px;
                    }

                    .summary-text {
                      font-size: 13px;
                      color: #a73a3a;
                      font-weight: 500;
                    }
                  }

                  .summary-right {
                    .toggle-button {
                      padding: 0;
                      font-size: 12px;
                      color: #a73a3a;

                      .el-icon {
                        margin-left: 4px;
                        font-size: 12px;
                      }
                    }
                  }
                }

                .error-list {
                  position: relative;
                  transition: all 0.2s ease;
                  border-radius: 6px;

                  &.collapsed {
                    display: block;
                    width: 100%;
                    max-height: 140px;
                    overflow-y: auto;
                  }

                  &.expanded {
                    display: block;
                    width: 100%;
                    max-height: 320px;
                    overflow-y: auto;
                  }

                  &::-webkit-scrollbar {
                    width: 6px;
                    height: 6px;
                  }
                  &::-webkit-scrollbar-track {
                    background: transparent;
                    border-radius: 8px;
                  }
                  &::-webkit-scrollbar-thumb {
                    background: rgba(245, 107, 107, 0.35);
                    border-radius: 8px;
                  }
                  &::-webkit-scrollbar-thumb:hover {
                    background: rgba(245, 107, 107, 0.55);
                  }
                  scrollbar-width: thin;
                  scrollbar-color: rgba(245,107,107,0.35) transparent;

                  ul {
                    margin: 0;
                    padding-left: 1.25rem;

                    .error-item {
                      margin: 8px 0;
                      padding-left: 4px;
                      font-size: 13px;
                      line-height: 1.5;
                      color: #7a2e2e;
                      word-break: break-word;
                      border-left: 2px solid #fde2e2;
                    }
                  }

                  .gradient-mask {
                    pointer-events: none;
                    position: sticky;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 24px;
                    background: linear-gradient(to bottom, rgba(255,247,247,0), rgba(255,247,247,1));
                  }
                }
              }
            }
          }
        }
      }
    }

    .processing-logs {
      margin-bottom: 2rem;

      .logs-card {
        background: #f8f9fa;
        border: 1px solid #e6ebf1;
        border-radius: 8px;
        padding: 10px 12px;
      }

      .logs-summary {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        padding-bottom: 8px;
        border-bottom: 1px dashed #e6ebf1;

        .summary-left {
          display: flex;
          align-items: center;
          gap: 8px;

          .log-icon { color: #409EFF; font-size: 16px; }
          .summary-text { font-size: 13px; color: #34495e; font-weight: 500; }
        }

        .summary-right {
          .toggle-button { padding: 0; font-size: 12px; color: #409EFF; }
        }
      }

      .logs-list {
        position: relative;
        transition: all 0.2s ease;
        border-radius: 6px;

        &.collapsed { max-height: 140px; overflow-y: auto; }
        &.expanded  { max-height: 300px; overflow-y: auto; }

        background: #f8f9fa;
        padding: 0.75rem 1rem;
        border: 1px solid #eef0f3;

        &::-webkit-scrollbar { width: 6px; height: 6px; }
        &::-webkit-scrollbar-track { background: transparent; }
        &::-webkit-scrollbar-thumb { background: rgba(144,147,153,0.35); border-radius: 8px; }
        &::-webkit-scrollbar-thumb:hover { background: rgba(144,147,153,0.55); }
        scrollbar-width: thin;
        scrollbar-color: rgba(144,147,153,0.35) transparent;

        .log-entry {
          position: relative;
          display: grid;
          grid-template-columns: 10px 90px 1fr auto;
          align-items: center;
          gap: 8px 12px;
          padding: 6px 0;
          border-bottom: 1px dashed #e6ebf1;
          font-size: 13px;

          &:last-child { border-bottom: none; }

          &.error { color: #f56c6c; }
          &.warning { color: #e6a23c; }
          &.info { color: #606266; }

          .level-dot {
            display: inline-block;
            width: 8px; height: 8px;
            border-radius: 50%;
            background: #909399;
          }
          &.error .level-dot { background: #f56c6c; }
          &.warning .level-dot { background: #e6a23c; }
          &.info .level-dot { background: #909399; }

          .log-time {
            color: #999;
            font-variant-numeric: tabular-nums;
            white-space: nowrap;
          }

          .log-message { flex: 1; color: #444; }
          .log-count {
            justify-self: end;
            background: #eef2ff;
            color: #3a5df0;
            border: 1px solid #d5dbff;
            border-radius: 999px;
            padding: 0 8px;
            min-width: 22px;
            height: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            line-height: 1;
            font-weight: 600;
            white-space: nowrap;
          }
        }
      }
    }

    .processing-stats {
      .stats-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin: 0 0 1rem 0;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;

        .stat-item {
          text-align: center;
          padding: 1rem;
          background: #f8f9fa;
          border-radius: 8px;

          .stat-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 0.25rem;
          }

          .stat-label {
            font-size: 0.875rem;
            color: #666;
          }
        }
      }
    }
  }

  :deep(.el-card__footer) {
    background: #f8f9fa;
    border-radius: 0 0 16px 16px;

    .step-actions {
      text-align: center;

      .success-message {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        color: #67c23a;
        font-weight: 500;
      }

      .cancelled-message {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        color: #e6a23c;
        font-weight: 500;
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
