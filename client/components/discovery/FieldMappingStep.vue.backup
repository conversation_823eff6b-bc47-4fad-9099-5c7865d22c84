<template>
  <el-card class="field-mapping-card">
    <template #header>
      <div class="step-header">
        <h3 class="step-title">配置字段映射</h3>
        <p class="step-description">请在表头的下拉菜单中选择对应的字段类型</p>
      </div>
    </template>

    <div class="mapping-content">
      <!-- 配置说明 -->
      <div class="config-info">
        <el-alert
          title="配置说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="config-instructions">
              <p>• 在每列的表头下拉菜单中选择对应的字段类型</p>
              <p>• <strong>案例ID</strong> 和 <strong>活动名称</strong> 为必填字段</p>
              <p>• 其他字段为可选，可根据需要进行配置</p>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 数据预览和字段映射 -->
      <div class="preview-mapping-section">
        <div class="section-header">
          <h4 class="section-title">数据预览与字段映射</h4>
          <div class="data-info">
            <span class="data-rows">共 {{ totalRows }} 行数据</span>
            <span class="data-columns">{{ availableColumns.length }} 列</span>
          </div>
        </div>

        <div class="mapping-table-container" v-loading="isLoadingPreview">
          <el-table
            v-if="previewData.length > 0"
            :data="previewData"
            class="mapping-table"
            border
            max-height="500"
            stripe
          >
            <el-table-column
              v-for="column in availableColumns"
              :key="column"
              :prop="column"
              :min-width="150"
              show-overflow-tooltip
            >
              <template #header>
                <div class="column-header">
                  <div class="column-name">{{ column }}</div>
                  <el-select
                    v-model="mappingConfig[column]"
                    placeholder="选择字段类型"
                    size="small"
                    class="field-type-select"
                    @change="handleFieldMappingChange"
                  >
                    <el-option
                      label="未映射"
                      value=""
                      class="unmapped-option"
                    />
                    <el-option-group label="必填字段">
                      <el-option
                        label="案例ID"
                        value="caseId"
                        :disabled="isFieldMapped('caseId', column)"
                      />
                      <el-option
                        label="活动名称"
                        value="activity"
                        :disabled="isFieldMapped('activity', column)"
                      />
                    </el-option-group>
                    <el-option-group label="可选字段">
                      <el-option
                        label="时间戳"
                        value="timestamp"
                        :disabled="isFieldMapped('timestamp', column)"
                      />
                      <el-option
                        label="资源"
                        value="resource"
                        :disabled="isFieldMapped('resource', column)"
                      />
                      <el-option
                        label="成本"
                        value="cost"
                        :disabled="isFieldMapped('cost', column)"
                      />
                      <el-option
                        label="前置活动"
                        value="previousActivity"
                        :disabled="isFieldMapped('previousActivity', column)"
                      />
                      <el-option
                        label="活动结束时间"
                        value="endTime"
                        :disabled="isFieldMapped('endTime', column)"
                      />
                    </el-option-group>
                  </el-select>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <el-empty v-else description="正在加载数据预览..." />
        </div>
      </div>

      <!-- 字段映射配置 -->
      <div class="mapping-section">
        <h4 class="section-title">字段映射配置</h4>
        
        <el-form
          ref="formRef"
          :model="mappingConfig"
          :rules="mappingRules"
          label-width="140px"
          class="mapping-form"
        >
          <!-- 必填字段 -->
          <div class="required-fields">
            <h5 class="field-group-title">必填字段</h5>
            
            <el-form-item label="案例ID字段" prop="caseIdField" required>
              <el-select
                v-model="mappingConfig.caseIdField"
                placeholder="选择案例ID字段"
                class="field-select"
              >
                <el-option
                  v-for="column in availableColumns"
                  :key="column"
                  :label="column"
                  :value="column"
                />
              </el-select>
              <div class="field-hint">用于标识不同的流程实例</div>
            </el-form-item>

            <el-form-item label="活动字段" prop="activityField" required>
              <el-select
                v-model="mappingConfig.activityField"
                placeholder="选择活动字段"
                class="field-select"
              >
                <el-option
                  v-for="column in availableColumns"
                  :key="column"
                  :label="column"
                  :value="column"
                />
              </el-select>
              <div class="field-hint">流程中的活动或任务名称</div>
            </el-form-item>

            <el-form-item label="时间戳字段" prop="timestampField" required>
              <el-select
                v-model="mappingConfig.timestampField"
                placeholder="选择时间戳字段"
                class="field-select"
              >
                <el-option
                  v-for="column in availableColumns"
                  :key="column"
                  :label="column"
                  :value="column"
                />
              </el-select>
              <div class="field-hint">活动发生的时间</div>
            </el-form-item>
          </div>

          <!-- 可选字段 -->
          <div class="optional-fields">
            <h5 class="field-group-title">可选字段</h5>
            
            <el-form-item label="资源字段" prop="resourceField">
              <el-select
                v-model="mappingConfig.resourceField"
                placeholder="选择资源字段（可选）"
                class="field-select"
                clearable
              >
                <el-option
                  v-for="column in availableColumns"
                  :key="column"
                  :label="column"
                  :value="column"
                />
              </el-select>
              <div class="field-hint">执行活动的人员或系统</div>
            </el-form-item>

            <el-form-item label="成本字段" prop="costField">
              <el-select
                v-model="mappingConfig.costField"
                placeholder="选择成本字段（可选）"
                class="field-select"
                clearable
              >
                <el-option
                  v-for="column in availableColumns"
                  :key="column"
                  :label="column"
                  :value="column"
                />
              </el-select>
              <div class="field-hint">活动的成本或费用</div>
            </el-form-item>

            <!-- 扩展字段 -->
            <el-collapse class="advanced-fields">
              <el-collapse-item title="高级字段配置" name="advanced">
                <el-form-item label="活动ID字段" prop="activityIdField">
                  <el-select
                    v-model="mappingConfig.activityIdField"
                    placeholder="选择活动ID字段（可选）"
                    class="field-select"
                    clearable
                  >
                    <el-option
                      v-for="column in availableColumns"
                      :key="column"
                      :label="column"
                      :value="column"
                    />
                  </el-select>
                  <div class="field-hint">活动的唯一标识符</div>
                </el-form-item>

                <el-form-item label="前置活动字段" prop="previousActivityField">
                  <el-select
                    v-model="mappingConfig.previousActivityField"
                    placeholder="选择前置活动字段（可选）"
                    class="field-select"
                    clearable
                  >
                    <el-option
                      v-for="column in availableColumns"
                      :key="column"
                      :label="column"
                      :value="column"
                    />
                  </el-select>
                  <div class="field-hint">当前活动的前置活动</div>
                </el-form-item>

                <el-form-item label="结束时间字段" prop="endTimestampField">
                  <el-select
                    v-model="mappingConfig.endTimestampField"
                    placeholder="选择结束时间字段（可选）"
                    class="field-select"
                    clearable
                  >
                    <el-option
                      v-for="column in availableColumns"
                      :key="column"
                      :label="column"
                      :value="column"
                    />
                  </el-select>
                  <div class="field-hint">活动的结束时间</div>
                </el-form-item>
              </el-collapse-item>
            </el-collapse>

            <!-- 时间格式配置 -->
            <div class="time-format-config" v-if="hasTimeFields">
              <h5 class="field-group-title">时间格式配置</h5>

              <el-form-item
                v-if="mappingConfig.timestampField"
                :label="`${mappingConfig.timestampField} 格式`"
                prop="timestampFormat"
              >
                <el-select
                  v-model="mappingConfig.timestampFormat"
                  placeholder="选择时间格式"
                  class="field-select"
                >
                  <el-option
                    v-for="format in timeFormats"
                    :key="format.value"
                    :label="format.label"
                    :value="format.value"
                  >
                    <div class="format-option">
                      <div class="format-pattern">{{ format.label }}</div>
                      <div class="format-example">示例: {{ format.example }}</div>
                    </div>
                  </el-option>
                </el-select>
                <div class="field-hint">指定时间戳字段的格式</div>
              </el-form-item>

              <el-form-item
                v-if="mappingConfig.endTimestampField"
                :label="`${mappingConfig.endTimestampField} 格式`"
                prop="endTimestampFormat"
              >
                <el-select
                  v-model="mappingConfig.endTimestampFormat"
                  placeholder="选择时间格式"
                  class="field-select"
                >
                  <el-option
                    v-for="format in timeFormats"
                    :key="format.value"
                    :label="format.label"
                    :value="format.value"
                  >
                    <div class="format-option">
                      <div class="format-pattern">{{ format.label }}</div>
                      <div class="format-example">示例: {{ format.example }}</div>
                    </div>
                  </el-option>
                </el-select>
                <div class="field-hint">指定结束时间字段的格式</div>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>

      <!-- 映射预览 -->
      <div v-if="hasValidMapping" class="mapping-preview">
        <h4 class="section-title">映射预览</h4>
        <div class="preview-cards">
          <div class="mapping-card" v-for="(value, key) in validMappings" :key="key">
            <div class="mapping-label">{{ getMappingLabel(key) }}</div>
            <div class="mapping-value">{{ value }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <div class="step-actions">
        <el-button @click="handlePrevious" size="large">
          <el-icon class="mr-2">
            <ArrowLeft />
          </el-icon>
          上一步
        </el-button>
        <el-button
          type="primary"
          :disabled="!isFormValid"
          @click="handleNext"
          size="large"
        >
          开始流程发现
          <el-icon class="ml-2">
            <ArrowRight />
          </el-icon>
        </el-button>
      </div>
    </template>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import type { UploadDataConfig } from '~/types'
import { useApi } from '~/utils/api'

// Props
interface Props {
  processId: number
  file: File | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  configUpdated: [config: UploadDataConfig]
  previous: []
  next: []
}>()

// 响应式数据
const formRef = ref()
const isLoadingPreview = ref(false)
const previewData = ref<any[]>([])
const availableColumns = ref<string[]>([])

const mappingConfig = ref<Partial<UploadDataConfig>>({
  processId: props.processId,
  caseIdField: '',
  activityField: '',
  timestampField: '',
  resourceField: '',
  costField: '',
  activityIdField: '',
  previousActivityField: '',
  endTimestampField: '',
  timestampFormat: 'YYYY/MM/DD HH:mm:ss',
  endTimestampFormat: 'YYYY/MM/DD HH:mm:ss'
})

// 时间格式选项
const timeFormats = [
  {
    value: 'YYYY/MM/DD HH:mm:ss',
    label: 'YYYY/MM/DD HH:mm:ss',
    example: '2025/05/17 17:51:11'
  },
  {
    value: 'YYYY-MM-DD HH:mm:ss',
    label: 'YYYY-MM-DD HH:mm:ss',
    example: '2025-05-17 17:51:11'
  },
  {
    value: 'DD/MM/YYYY HH:mm:ss',
    label: 'DD/MM/YYYY HH:mm:ss',
    example: '17/05/2025 17:51:11'
  },
  {
    value: 'MM/DD/YYYY HH:mm:ss',
    label: 'MM/DD/YYYY HH:mm:ss',
    example: '05/17/2025 17:51:11'
  },
  {
    value: 'YYYY/MM/DD HH:mm',
    label: 'YYYY/MM/DD HH:mm',
    example: '2025/05/17 17:51'
  },
  {
    value: 'YYYY-MM-DD HH:mm',
    label: 'YYYY-MM-DD HH:mm',
    example: '2025-05-17 17:51'
  },
  {
    value: 'YYYY/MM/DD',
    label: 'YYYY/MM/DD',
    example: '2025/05/17'
  },
  {
    value: 'YYYY-MM-DD',
    label: 'YYYY-MM-DD',
    example: '2025-05-17'
  },
  {
    value: 'DD/MM/YYYY',
    label: 'DD/MM/YYYY',
    example: '17/05/2025'
  },
  {
    value: 'MM/DD/YYYY',
    label: 'MM/DD/YYYY',
    example: '05/17/2025'
  },
  {
    value: 'ISO8601',
    label: 'ISO 8601',
    example: '2025-05-17T17:51:11.000Z'
  },
  {
    value: 'timestamp',
    label: 'Unix 时间戳',
    example: '1716825071'
  }
]

// 表单验证规则
const mappingRules = {
  caseIdField: [
    { required: true, message: '请选择案例ID字段', trigger: 'change' }
  ],
  activityField: [
    { required: true, message: '请选择活动字段', trigger: 'change' }
  ],
  timestampField: [
    { required: true, message: '请选择时间戳字段', trigger: 'change' }
  ]
}

// 计算属性
const hasValidMapping = computed(() => {
  return mappingConfig.value.caseIdField && 
         mappingConfig.value.activityField && 
         mappingConfig.value.timestampField
})

const validMappings = computed(() => {
  const mappings: Record<string, string> = {}
  Object.entries(mappingConfig.value).forEach(([key, value]) => {
    if (value && key !== 'processId') {
      mappings[key] = value as string
    }
  })
  return mappings
})

const isFormValid = computed(() => {
  return hasValidMapping.value
})

const hasTimeFields = computed(() => {
  return mappingConfig.value.timestampField || mappingConfig.value.endTimestampField
})

// 方法
const getMappingLabel = (key: string): string => {
  const labels: Record<string, string> = {
    caseIdField: '案例ID',
    activityField: '活动',
    timestampField: '时间戳',
    resourceField: '资源',
    costField: '成本',
    activityIdField: '活动ID',
    previousActivityField: '前置活动',
    endTimestampField: '结束时间'
  }
  return labels[key] || key
}

const loadPreview = async () => {
  if (!props.file) return

  isLoadingPreview.value = true
  try {
    const api = useApi()
    const result = await api.getFilePreview(props.file)

    availableColumns.value = result.columns
    previewData.value = result.preview

    // 自动映射常见字段名
    autoMapFields()
  } catch (error) {
    console.error('加载预览失败:', error)
    ElMessage.error('加载数据预览失败')

    // 降级到模拟数据
    availableColumns.value = ['case_id', 'activity', 'timestamp', 'resource', 'cost', 'activity_id']
    previewData.value = [
      {
        case_id: '100126',
        activity: '财务会议安排确认',
        timestamp: '2025/5/17 17:51:11',
        resource: '995098661',
        cost: 100,
        activity_id: 'ACT001'
      },
      {
        case_id: '100126',
        activity: '财务会议安排确认',
        timestamp: '2025/5/23 10:25:05',
        resource: '995098661',
        cost: 150,
        activity_id: 'ACT002'
      }
    ]
    autoMapFields()
  } finally {
    isLoadingPreview.value = false
  }
}

const autoMapFields = () => {
  const columnLower = availableColumns.value.map(col => col.toLowerCase())
  
  // 自动映射案例ID字段
  const caseIdCandidates = ['case_id', 'caseid', 'case', 'id']
  for (const candidate of caseIdCandidates) {
    const index = columnLower.indexOf(candidate)
    if (index !== -1) {
      mappingConfig.value.caseIdField = availableColumns.value[index]
      break
    }
  }

  // 自动映射活动字段
  const activityCandidates = ['activity', 'task', 'event', 'action']
  for (const candidate of activityCandidates) {
    const index = columnLower.indexOf(candidate)
    if (index !== -1) {
      mappingConfig.value.activityField = availableColumns.value[index]
      break
    }
  }

  // 自动映射时间戳字段
  const timestampCandidates = ['timestamp', 'time', 'date', 'datetime']
  for (const candidate of timestampCandidates) {
    const index = columnLower.indexOf(candidate)
    if (index !== -1) {
      mappingConfig.value.timestampField = availableColumns.value[index]
      break
    }
  }

  // 自动映射资源字段
  const resourceCandidates = ['resource', 'user', 'person', 'actor']
  for (const candidate of resourceCandidates) {
    const index = columnLower.indexOf(candidate)
    if (index !== -1) {
      mappingConfig.value.resourceField = availableColumns.value[index]
      break
    }
  }

  // 自动映射成本字段
  const costCandidates = ['cost', 'price', 'amount', 'fee']
  for (const candidate of costCandidates) {
    const index = columnLower.indexOf(candidate)
    if (index !== -1) {
      mappingConfig.value.costField = availableColumns.value[index]
      break
    }
  }
}

const handlePrevious = () => {
  emit('previous')
}

const handleNext = async () => {
  try {
    await formRef.value?.validate()
    const config = mappingConfig.value as UploadDataConfig
    emit('configUpdated', config)
    emit('next')
  } catch (error) {
    ElMessage.error('请完成必填字段的配置')
  }
}

// 监听配置变化
watch(mappingConfig, (newConfig) => {
  if (hasValidMapping.value) {
    emit('configUpdated', newConfig as UploadDataConfig)
  }
}, { deep: true })

// 生命周期
onMounted(() => {
  loadPreview()
})
</script>

<style lang="scss" scoped>
.field-mapping-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  :deep(.el-card__header) {
    // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    // color: white;
    border-radius: 16px 16px 0 0;
  }

  .step-header {
    text-align: center;

    .step-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0 0 0.5rem 0;
    }

    .step-description {
      margin: 0;
      opacity: 0.9;
    }
  }

  .mapping-content {
    padding: 2rem 0;

    .section-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: #333;
      margin: 0 0 1rem 0;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #f0f0f0;
    }

    .preview-section {
      margin-bottom: 2rem;

      .preview-table {
        :deep(.el-table) {
          border-radius: 8px;
          overflow: hidden;
        }
      }
    }

    .mapping-section {
      margin-bottom: 2rem;

      .field-group-title {
        font-size: 1rem;
        font-weight: 500;
        color: #666;
        margin: 0 0 1rem 0;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e9ecef;
      }

      .mapping-form {
        .field-select {
          width: 100%;
        }

        .field-hint {
          font-size: 0.875rem;
          color: #666;
          margin-top: 0.25rem;
        }

        .required-fields {
          margin-bottom: 2rem;
        }

        .optional-fields {
          .advanced-fields {
            margin-top: 1rem;

            :deep(.el-collapse-item__header) {
              background: #f8f9fa;
              border-radius: 8px;
              padding: 0 1rem;
            }
          }

          .time-format-config {
            margin-top: 2rem;

            .format-option {
              .format-pattern {
                font-weight: 500;
                color: #333;
              }

              .format-example {
                font-size: 0.875rem;
                color: #666;
                margin-top: 0.25rem;
              }
            }
          }
        }
      }
    }

    .mapping-preview {
      .preview-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;

        .mapping-card {
          padding: 1rem;
          background: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #e9ecef;

          .mapping-label {
            font-size: 0.875rem;
            color: #666;
            margin-bottom: 0.25rem;
          }

          .mapping-value {
            font-weight: 500;
            color: #333;
          }
        }
      }
    }
  }

  :deep(.el-card__footer) {
    background: #f8f9fa;
    border-radius: 0 0 16px 16px;

    .step-actions {
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
