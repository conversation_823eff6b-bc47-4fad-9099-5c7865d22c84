<template>
  <el-card class="field-mapping-card">
    <template #header>
      <div class="step-header">
        <h3 class="step-title">配置字段映射</h3>
        <p class="step-description">请在表头的下拉菜单中选择对应的字段类型</p>
      </div>
    </template>

    <div class="mapping-content">
      <!-- 配置说明 -->
      <div class="config-info">
        <el-alert
          title="配置说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="config-instructions">
              <p>• 在每列的表头下拉菜单中选择对应的字段类型</p>
              <p>• <strong>案例ID</strong>、<strong>活动名称</strong> 和 <strong>时间戳</strong> 为必填字段</p>
              <p>• <strong>父案例ID</strong> 字段用于多层次嵌套流程挖掘（可选）</p>
              <p>• 其他字段为可选，可根据需要进行配置</p>
              <p>• 需要为时间格式的字段才可配置为时间戳和活动结束时间的映射</p>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 数据预览和字段映射 -->
      <div class="preview-mapping-section">
        <div class="section-header">
          <h4 class="section-title">数据预览与字段映射</h4>
          <div class="data-info">
            <span class="data-rows">共 {{ totalRows }} 行数据</span>
            <span class="data-columns">{{ availableColumns.length }} 列</span>
          </div>
        </div>

        <div v-loading="isLoadingPreview" class="mapping-table-container">
          <el-table
            v-if="previewData.length > 0"
            :data="previewData"
            class="mapping-table"
            border
            max-height="500"
            stripe
          >
            <el-table-column
              v-for="column in availableColumns"
              :key="column"
              :prop="column"
              :min-width="150"
              show-overflow-tooltip
            >
              <template #header>
                <div class="column-header">
                  <div class="column-name">{{ column }}</div>
                  <el-select
                    v-model="mappingConfig[column]"
                    placeholder="选择字段类型"
                    size="small"
                    class="field-type-select"
                    clearable
                    :popper-class="'mapping-select-popper'"
                    @change="(val) => handleFieldMappingChange(column, val)"
                    @clear="() => handleFieldMappingClear(column)"
                  >
                    <el-option-group label="必填字段">
                      <el-option
                        label="案例ID"
                        value="caseId"
                        :disabled="isFieldMapped('caseId', column)"
                      />
                      <el-option
                        label="活动名称"
                        value="activity"
                        :disabled="isFieldMapped('activity', column)"
                      />
                      <el-option
                        label="时间戳"
                        value="timestamp"
                        :disabled="isFieldMapped('timestamp', column) || !isTimeColumn(column)"
                      />
                    </el-option-group>
                    <el-option-group label="可选字段">
                      <el-option
                        label="资源"
                        value="resource"
                        :disabled="isFieldMapped('resource', column)"
                      />
                      <el-option
                        label="成本"
                        value="cost"
                        :disabled="isFieldMapped('cost', column)"
                      />
                      <el-option
                        label="前置活动"
                        value="previousActivity"
                        :disabled="isFieldMapped('previousActivity', column)"
                      />
                      <el-option
                        label="活动结束时间"
                        value="endTime"
                        :disabled="isFieldMapped('endTime', column) || !isTimeColumn(column)"
                      />
                      <el-option
                        label="父案例ID"
                        value="parentCaseId"
                        :disabled="isFieldMapped('parentCaseId', column)"
                      />
                    </el-option-group>
                  </el-select>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <el-empty v-else description="正在加载数据预览..." />
        </div>
      </div>

      <!-- 清空数据选项 -->
      <div class="clear-data-option">
        <el-checkbox
          v-model="clearExistingData"
          class="clear-data-checkbox"
          @change="handleClearDataChange"
        >
          <span class="checkbox-text">清空之前的数据（将删除该流程下所有已存在的事件日志）</span>
        </el-checkbox>
        <div class="warning-notice">
          <el-icon class="warning-icon">
            <Warning />
          </el-icon>
          <span class="warning-text">这是一个危险操作，删除后无法恢复，请谨慎选择</span>
        </div>
      </div>

      <!-- 映射状态 -->
      <div class="mapping-status">
        <div class="status-header">
          <h4 class="section-title">映射状态</h4>
          <el-button
            type="primary"
            size="small"
            :disabled="!hasMappings"
            @click="clearAllMappings"
          >
            清空所有映射
          </el-button>
        </div>

        <div class="status-grid">
          <div class="status-item required" :class="{ mapped: getMappedColumn('caseId') }">
            <div class="status-label">案例ID</div>
            <div class="status-value">
              {{ getMappedColumn('caseId') || '未映射' }}
            </div>
            <div class="status-indicator">
              <el-icon v-if="getMappedColumn('caseId')" color="#67c23a">
                <Check />
              </el-icon>
              <el-icon v-else color="#f56c6c">
                <Close />
              </el-icon>
              <button
                v-if="getMappedColumn('caseId')"
                class="remove-btn"
                type="button"
                aria-label="移除映射"
                @click.stop="removeMapping('caseId')"
              >
                <el-icon class="delete-icon"><Delete /></el-icon>
              </button>
            </div>
          </div>

          <div class="status-item required" :class="{ mapped: getMappedColumn('activity') }">
            <div class="status-label">活动名称</div>
            <div class="status-value">
              {{ getMappedColumn('activity') || '未映射' }}
            </div>
            <div class="status-indicator">
              <el-icon v-if="getMappedColumn('activity')" color="#67c23a">
                <Check />
              </el-icon>
              <el-icon v-else color="#f56c6c">
                <Close />
              </el-icon>
              <button
                v-if="getMappedColumn('activity')"
                class="remove-btn"
                type="button"
                aria-label="移除映射"
                @click.stop="removeMapping('activity')"
              >
                <el-icon class="delete-icon"><Delete /></el-icon>
              </button>
            </div>
          </div>

          <div class="status-item required" :class="{ mapped: getMappedColumn('timestamp') }">
            <div class="status-label">时间戳</div>
            <div class="status-value">
              {{ getMappedColumn('timestamp') || '未映射' }}
            </div>
            <div class="status-indicator">
              <el-icon v-if="getMappedColumn('timestamp')" color="#67c23a">
                <Check />
              </el-icon>
              <el-icon v-else color="#f56c6c">
                <Close />
              </el-icon>
              <button
                v-if="getMappedColumn('timestamp')"
                class="remove-btn"
                type="button"
                aria-label="移除映射"
                @click.stop="removeMapping('timestamp')"
              >
                <el-icon class="delete-icon"><Delete /></el-icon>
              </button>
            </div>
          </div>

          <div class="status-item optional" :class="{ mapped: getMappedColumn('resource') }">
            <div class="status-label">资源</div>
            <div class="status-value">
              {{ getMappedColumn('resource') || '未映射' }}
            </div>
            <div class="status-indicator">
              <el-icon v-if="getMappedColumn('resource')" color="#67c23a">
                <Check />
              </el-icon>
              <el-icon v-else color="#e6a23c">
                <Minus />
              </el-icon>
              <button
                v-if="getMappedColumn('resource')"
                class="remove-btn"
                type="button"
                aria-label="移除映射"
                @click.stop="removeMapping('resource')"
              >
                <el-icon class="delete-icon"><Delete /></el-icon>
              </button>
            </div>
          </div>

          <div class="status-item optional" :class="{ mapped: getMappedColumn('cost') }">
            <div class="status-label">成本</div>
            <div class="status-value">
              {{ getMappedColumn('cost') || '未映射' }}
            </div>
            <div class="status-indicator">
              <el-icon v-if="getMappedColumn('cost')" color="#67c23a">
                <Check />
              </el-icon>
              <el-icon v-else color="#e6a23c">
                <Minus />
              </el-icon>
              <button
                v-if="getMappedColumn('cost')"
                class="remove-btn"
                type="button"
                aria-label="移除映射"
                @click.stop="removeMapping('cost')"
              >
                <el-icon class="delete-icon"><Delete /></el-icon>
              </button>
            </div>
          </div>

          <div class="status-item optional" :class="{ mapped: getMappedColumn('previousActivity') }">
            <div class="status-label">前置活动</div>
            <div class="status-value">
              {{ getMappedColumn('previousActivity') || '未映射' }}
            </div>
            <div class="status-indicator">
              <el-icon v-if="getMappedColumn('previousActivity')" color="#67c23a">
                <Check />
              </el-icon>
              <el-icon v-else color="#e6a23c">
                <Minus />
              </el-icon>
              <button
                v-if="getMappedColumn('previousActivity')"
                class="remove-btn"
                type="button"
                aria-label="移除映射"
                @click.stop="removeMapping('previousActivity')"
              >
                <el-icon class="delete-icon"><Delete /></el-icon>
              </button>
            </div>
          </div>

          <div class="status-item optional" :class="{ mapped: getMappedColumn('endTime') }">
            <div class="status-label">活动结束时间</div>
            <div class="status-value">
              {{ getMappedColumn('endTime') || '未映射' }}
            </div>
            <div class="status-indicator">
              <el-icon v-if="getMappedColumn('endTime')" color="#67c23a">
                <Check />
              </el-icon>
              <el-icon v-else color="#e6a23c">
                <Minus />
              </el-icon>
              <button
                v-if="getMappedColumn('endTime')"
                class="remove-btn"
                type="button"
                aria-label="移除映射"
                @click.stop="removeMapping('endTime')"
              >
                <el-icon class="delete-icon"><Delete /></el-icon>
              </button>
            </div>
          </div>

          <div class="status-item optional" :class="{ mapped: getMappedColumn('parentCaseId') }">
            <div class="status-label">父案例ID</div>
            <div class="status-value">
              {{ getMappedColumn('parentCaseId') || '未映射' }}
            </div>
            <div class="status-indicator">
              <el-icon v-if="getMappedColumn('parentCaseId')" color="#67c23a">
                <Check />
              </el-icon>
              <el-icon v-else color="#e6a23c">
                <Minus />
              </el-icon>
              <button
                v-if="getMappedColumn('parentCaseId')"
                class="remove-btn"
                type="button"
                aria-label="移除映射"
                @click.stop="removeMapping('parentCaseId')"
              >
                <el-icon class="delete-icon"><Delete /></el-icon>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 验证结果 -->
      <div v-if="validationMessage" class="validation-section">
        <el-alert
          :title="validationMessage.title"
          :type="validationMessage.type"
          :closable="false"
          show-icon
        >
          <template #default>
            <div v-html="validationMessage.content"/>
          </template>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <div class="step-actions">
        <el-button @click="$emit('prev')">上一步</el-button>
        <el-button
          type="primary"
          :disabled="!isValidMapping"
          :loading="isValidating"
          @click="handleNext"
        >
          下一步
        </el-button>
      </div>
    </template>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Close, Minus, Warning, Delete } from '@element-plus/icons-vue'

// Props
interface Props {
  file?: File
  previewData?: any[]
  availableColumns?: string[]
  totalRows?: number
  savedMapping?: Record<string, string> | null
  savedClearData?: boolean
  timeColumns?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  previewData: () => [],
  availableColumns: () => [],
  totalRows: 0,
  savedMapping: null,
  savedClearData: false,
  timeColumns: () => []
})

// Emits
const emit = defineEmits<{
  prev: []
  next: [mapping: Record<string, string | boolean>]
  mappingChange: [mapping: Record<string, string | boolean>]
  clearDataChanged: [clearData: boolean]
}>()

// 响应式数据
const isLoadingPreview = ref(false)
const isValidating = ref(false)
const mappingConfig = ref<Record<string, string>>({})
const clearExistingData = ref(false)
const validationMessage = ref<{
  title: string
  type: 'success' | 'warning' | 'error' | 'info'
  content: string
} | null>(null)

const removeMapping = (fieldType: string) => {
  const column = getMappedColumn(fieldType)
  if (!column) return
  // 清除该字段类型的映射
  mappingConfig.value[column] = ''
  validateMapping()
  emit('mappingChange', buildMappingResult())
  ElMessage.success('已移除映射')
}

// 初始化映射配置
const initMappingConfig = () => {
  if (props.savedMapping) {
    // 从保存的映射中恢复字段映射
    const savedConfig: Record<string, string> = {}

    props.availableColumns.forEach(column => {
      savedConfig[column] = ''
    })

    for (const [key, column] of Object.entries(props.savedMapping)) {
      if (key.endsWith('Field') && typeof column === 'string' && props.availableColumns.includes(column)) {
        const fieldType = key.replace('Field', '')
        savedConfig[column] = fieldType
      }
    }

    mappingConfig.value = savedConfig
  } else {
    // 初始化空的映射配置
    const config: Record<string, string> = {}
    props.availableColumns.forEach(column => {
      config[column] = ''
    })
    mappingConfig.value = config
  }

  // 恢复清空数据选项
  clearExistingData.value = props.savedClearData || false
}

// 计算属性
const hasMappings = computed(() => {
  return Object.values(mappingConfig.value).some(value => typeof value === 'string' && value.length > 0)
})

const isValidMapping = computed(() => {
  return getMappedColumn('caseId') && getMappedColumn('activity') && getMappedColumn('timestamp')
})

// 方法
const getMappedColumn = (fieldType: string): string => {
  for (const [column, type] of Object.entries(mappingConfig.value)) {
    if (type === fieldType) {
      return column
    }
  }
  return ''
}

const isFieldMapped = (fieldType: string, currentColumn: string): boolean => {
  const mappedColumn = getMappedColumn(fieldType)
  return mappedColumn !== '' && mappedColumn !== currentColumn
}

const isTimeColumn = (column: string): boolean => {
  return props.timeColumns.includes(column)
}

const handleFieldMappingChange = (column: string, val: any) => {
  // 空值/清空统一视为未映射
  mappingConfig.value[column] = typeof val === 'string' && val ? val : ''
  validateMapping()
  emit('mappingChange', buildMappingResult())
}

const handleFieldMappingClear = (column: string) => {
  mappingConfig.value[column] = ''
  validateMapping()
  emit('mappingChange', buildMappingResult())
}

const clearAllMappings = () => {
  mappingConfig.value = {}
  props.availableColumns.forEach(column => {
    mappingConfig.value[column] = ''
  })
  validateMapping()
  emit('mappingChange', buildMappingResult())
}

const handleClearDataChange = (value: boolean | string | number) => {
  const boolValue = Boolean(value)
  emit('clearDataChanged', boolValue)
}

const validateMapping = () => {
  const caseIdMapped = getMappedColumn('caseId')
  const activityMapped = getMappedColumn('activity')
  const timestampMapped = getMappedColumn('timestamp')

  if (!caseIdMapped && !activityMapped && !timestampMapped) {
    validationMessage.value = {
      title: '请配置必填字段',
      type: 'warning',
      content: '请至少配置 <strong>案例ID</strong>、<strong>活动名称</strong> 和 <strong>时间戳</strong> 字段的映射。'
    }
  } else if (!caseIdMapped) {
    validationMessage.value = {
      title: '缺少案例ID字段',
      type: 'error',
      content: '请配置 <strong>案例ID</strong> 字段的映射，这是进行流程挖掘的必要字段。'
    }
  } else if (!activityMapped) {
    validationMessage.value = {
      title: '缺少活动名称字段',
      type: 'error',
      content: '请配置 <strong>活动名称</strong> 字段的映射，这是进行流程挖掘的必要字段。'
    }
  } else if (!timestampMapped) {
    validationMessage.value = {
      title: '缺少时间戳字段',
      type: 'error',
      content: '请配置 <strong>时间戳</strong> 字段的映射，这是进行流程挖掘的必要字段。'
    }
  } else {
    const optionalCount = [
      getMappedColumn('resource'),
      getMappedColumn('cost'),
      getMappedColumn('previousActivity'),
      getMappedColumn('endTime'),
      getMappedColumn('parentCaseId')
    ].filter(Boolean).length

    validationMessage.value = {
      title: '字段映射配置完成',
      type: 'success',
      content: `已配置 <strong>3</strong> 个必填字段和 <strong>${optionalCount}</strong> 个可选字段。点击下一步继续。`
    }
  }
}

const buildMappingResult = () => {
  const result: Record<string, string | boolean> = {
    clearExistingData: clearExistingData.value
  }

  for (const [column, fieldType] of Object.entries(mappingConfig.value)) {
    if (fieldType) {
      result[fieldType + 'Field'] = column
    }
  }

  return result
}

const handleNext = () => {
  if (!isValidMapping.value) {
    ElMessage.error('请先配置必填字段的映射')
    return
  }

  isValidating.value = true

  setTimeout(() => {
    isValidating.value = false
    emit('next', buildMappingResult())
  }, 500)
}

// 初始化
onMounted(() => {
  initMappingConfig()
  validateMapping()
})

// 监听数据变化
watch(() => props.availableColumns, (newColumns) => {
  if (newColumns.length > 0) {
    initMappingConfig()
    validateMapping()
  }
}, { immediate: true })

// 监听保存的映射配置变化
watch(() => props.savedMapping, (newMapping, oldMapping) => {
  // 只有在映射真正改变时才重新初始化
  if (props.availableColumns.length > 0 && newMapping !== oldMapping) {
    initMappingConfig()
    validateMapping()
  }
}, { deep: true })

// 监听保存的清空数据选项变化
watch(() => props.savedClearData, (newValue) => {
  clearExistingData.value = newValue || false
})
</script>

<style lang="scss" scoped>
.field-mapping-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  :deep(.el-card__header) {
    border-radius: 16px 16px 0 0;
    padding: 24px;

    .step-header {
      text-align: center;

      .step-title {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
      }

      .step-description {
        margin: 0;
        font-size: 16px;
        opacity: 0.9;
      }
    }
  }

  :deep(.el-card__body) {
    padding: 24px;
  }

  :deep(.el-card__footer) {
    background: #f8f9fa;
    border-radius: 0 0 16px 16px;
    padding: 16px 24px;

    .step-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

.mapping-content {
  .config-info {
    margin-bottom: 24px;

    :deep(.el-alert) {
      border-radius: 12px;
      border: none;
      background: rgba(144, 202, 249, 0.1);

      .config-instructions {
        p {
          margin: 4px 0;
          line-height: 1.6;
        }
      }
    }
  }

  .preview-mapping-section {
    margin-bottom: 24px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 2px solid rgba(102, 126, 234, 0.1);

      .section-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
      }

      .data-info {
        display: flex;
        gap: 16px;
        font-size: 14px;

        .data-rows,
        .data-columns {
          padding: 4px 12px;
          background: rgba(64, 158, 255, 0.1);
          border-radius: 12px;
          color: #409eff;
          font-weight: 500;
        }
      }
    }

    .mapping-table-container {
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

      .mapping-table {
        :deep(.el-table__header) {
          background: #f8f9fa;

          th {
            background: transparent !important;
            border-bottom: 2px solid #e9ecef;
            padding: 0;

            .cell {
              padding: 0;
            }
          }
        }

        :deep(.el-table__body) {
          tr:nth-child(even) {
            background: rgba(64, 158, 255, 0.03);
          }

          td {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
          }
        }

        .column-header {
          padding: 12px 8px;
          display: flex;
          flex-direction: column;
          gap: 8px;

          .column-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
            text-align: center;
          }

          .field-type-select {
            width: 100%;

            :deep(.el-input__wrapper) {
              border-radius: 8px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
          }
        }
      }
    }
  }

  .mapping-status {
    margin-bottom: 24px;

    .status-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 2px solid rgba(102, 126, 234, 0.1);

      .section-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
      }
    }

    .status-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;

      .status-item {
        padding: 16px;
        border-radius: 12px;
        border: 2px solid #e9ecef;
        background: white;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 12px;

        &.required {
          border-color: #f56c6c;
          background: rgba(245, 108, 108, 0.05);

          &.mapped {
            border-color: #67c23a;
            background: rgba(103, 194, 58, 0.05);
          }
        }

        &.optional {
          border-color: #e6a23c;
          background: rgba(230, 162, 60, 0.05);

          &.mapped {
            border-color: #67c23a;
            background: rgba(103, 194, 58, 0.05);
          }
        }

        .status-label {
          font-weight: 600;
          color: #2c3e50;
          flex: 1;
          display: flex;
          align-items: center;
        }

        .status-value {
          font-size: 14px;
          color: #666;
          flex: 2;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .status-indicator {
          position: relative;
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;

          .el-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            width: 20px;
            height: 20px;
            transition: opacity .15s ease-in-out;
          }

          .remove-btn {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: transparent;
            border: none;
            padding: 0;
            cursor: pointer;
            opacity: 0;
            transform: scale(0.9);
            transition: opacity .15s ease-in-out, transform .15s ease-in-out;
            color: #f56c6c;

            .delete-icon {
              font-size: 18px;
            }

            &:hover {
              color: #d93025;
            }

            &:focus-visible {
              outline: 2px solid #f56c6c;
              border-radius: 50%;
            }
          }
        }

        &.mapped:hover {
          .status-indicator {
            .remove-btn {
              opacity: 1;
              transform: scale(1);
            }
            .el-icon:not(.delete-icon) {
              opacity: 0;
            }
          }
        }
      }
    }
  }

  .clear-data-option {
    margin-bottom: 24px;
    padding: 20px;
    background: rgba(255, 243, 224, 0.8);
    border: 1px solid #ffd591;
    border-radius: 12px;
    backdrop-filter: blur(10px);

    .clear-data-checkbox {
      margin-bottom: 12px;

      .checkbox-text {
        font-size: 15px;
        font-weight: 500;
        color: #d46b08;
      }

      :deep(.el-checkbox__label) {
        color: #d46b08 !important;
        font-weight: 500;
      }

      :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
        background-color: #fa8c16;
        border-color: #fa8c16;
      }

      :deep(.el-checkbox__inner:hover) {
        border-color: #fa8c16;
      }
    }

    .warning-notice {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background: rgba(255, 77, 79, 0.1);
      border: 1px solid rgba(255, 77, 79, 0.2);
      border-radius: 8px;

      .warning-icon {
        color: #ff4d4f;
        font-size: 16px;
        flex-shrink: 0;
      }

      .warning-text {
        font-size: 13px;
        color: #ff4d4f;
        line-height: 1.4;
      }
    }
  }

  .validation-section {
    :deep(.el-alert) {
      border-radius: 12px;
      border: none;
    }
  }
}
</style>
