<template>
  <div class="sql-editor">
    <el-card class="editor-card">
      <template #header>
        <div class="editor-header">
          <h3 class="editor-title">SQL查询编辑器</h3>
          <div class="editor-actions">
            <el-select
              v-model="selectedConnectionId"
              placeholder="选择数据库连接"
              style="width: 200px; margin-right: 1rem;"
              @change="handleConnectionChange"
            >
              <el-option
                v-for="connection in availableConnections"
                :key="connection.id"
                :label="connection.name"
                :value="connection.id"
                :disabled="!connection.isAvailable"
              >
                <div class="connection-option">
                  <span class="connection-name">{{ connection.name }}</span>
                  <el-tag
                    :type="connection.isAvailable ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ connection.isAvailable ? '已连接' : '未连接' }}
                  </el-tag>
                </div>
              </el-option>
            </el-select>
            
            <el-button
              type="primary"
              :icon="VideoPlay"
              @click="executeQuery"
              :disabled="!selectedConnectionId || !sqlQuery.trim()"
              :loading="executing"
            >
              执行查询
            </el-button>
          </div>
        </div>
      </template>

      <div class="editor-content">
        <!-- SQL编辑区域 -->
        <div class="sql-input-section">
          <div class="input-header">
            <span class="section-title">SQL语句</span>
            <div class="input-actions">
              <el-button
                size="small"
                :icon="Document"
                @click="showTableHelper = !showTableHelper"
              >
                表结构助手
              </el-button>
              <el-button
                size="small"
                :icon="Delete"
                @click="clearQuery"
              >
                清空
              </el-button>
            </div>
          </div>
          
          <el-input
            v-model="sqlQuery"
            type="textarea"
            :rows="8"
            placeholder="请输入SQL查询语句，例如：SELECT * FROM users LIMIT 100"
            class="sql-textarea"
          />
          
          <div class="query-info">
            <div class="info-left">
              <span class="info-item">
                <el-icon><Clock /></el-icon>
                查询限制: {{ queryLimit }} 行
              </span>
              <span class="info-tip">
                系统会自动添加LIMIT限制，无需手动添加
              </span>
            </div>
            <el-input-number
              v-model="queryLimit"
              :min="1"
              :max="10000"
              size="small"
              style="width: 120px;"
            />
          </div>
        </div>

        <!-- 表结构助手 -->
        <div v-if="showTableHelper" class="table-helper">
          <div class="helper-header">
            <span class="section-title">表结构助手</span>
            <el-button
              size="small"
              :icon="Refresh"
              @click="loadTables"
              :loading="loadingTables"
            >
              刷新
            </el-button>
          </div>

          <div class="helper-tip">
            <el-icon><Document /></el-icon>
            <span>点击表名可快速插入到SQL编辑器中</span>
          </div>
          
          <div class="tables-list">
            <div v-if="loadingTables" class="loading-state">
              <el-skeleton :rows="3" animated />
            </div>
            
            <div v-else-if="tables.length === 0" class="empty-state">
              <p>暂无表信息</p>
            </div>
            
            <div v-else class="tables-grid">
              <div
                v-for="table in tables"
                :key="table"
                class="table-item"
                @click="insertTableName(table)"
              >
                <el-icon><Grid /></el-icon>
                <span>{{ table }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 查询结果 -->
        <div v-if="queryResult" class="result-section">
          <div class="result-header">
            <span class="section-title">查询结果</span>
            <div class="result-info">
              <span class="info-item">
                <el-icon><DataLine /></el-icon>
                {{ queryResult.totalRows }} 行
              </span>
              <span class="info-item">
                <el-icon><Clock /></el-icon>
                {{ queryResult.executionTime }}ms
              </span>
              <el-button
                v-if="queryResult.success"
                size="small"
                type="primary"
                @click="useQueryResult"
              >
                使用此数据
              </el-button>
            </div>
          </div>

          <div v-if="!queryResult.success" class="error-message">
            <el-alert
              :title="queryResult.error"
              type="error"
              :closable="false"
              show-icon
            />
          </div>

          <div v-else class="result-table">
            <el-table
              :data="queryResult.data"
              border
              stripe
              max-height="400"
              style="width: 100%"
            >
              <el-table-column
                v-for="column in queryResult.columns"
                :key="column"
                :prop="column"
                :label="column"
                :min-width="120"
                show-overflow-tooltip
              />
            </el-table>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  VideoPlay,
  Document,
  Delete,
  Clock,
  Refresh,
  Grid,
  DataLine
} from '@element-plus/icons-vue'
import {
  DatabaseType
} from '~/types'
import type {
  DatabaseConnection,
  ExecuteQueryDto,
  QueryResult
} from '~/types'
import { useApi } from '~/utils/api'

// Props
interface Props {
  processId?: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  queryResult: [result: QueryResult, query: string]
}>()

// 响应式数据
const api = useApi()
const selectedConnectionId = ref<number | null>(null)
const sqlQuery = ref('')
const queryLimit = ref(1000)
const executing = ref(false)
const loadingTables = ref(false)
const showTableHelper = ref(false)
const availableConnections = ref<DatabaseConnection[]>([])
const tables = ref<string[]>([])
const queryResult = ref<QueryResult | null>(null)

// 计算属性
const selectedConnection = computed(() => {
  return availableConnections.value.find(conn => conn.id === selectedConnectionId.value)
})

// 方法
const loadConnections = async () => {
  try {
    availableConnections.value = await api.getDatabaseConnections()
    // 自动选择第一个可用的连接
    const firstAvailable = availableConnections.value.find(conn => conn.isAvailable)
    if (firstAvailable) {
      selectedConnectionId.value = firstAvailable.id
      await loadTables()
    }
  } catch (error) {
    ElMessage.error('加载数据库连接失败')
    console.error('加载数据库连接失败:', error)
  }
}

const handleConnectionChange = async () => {
  if (selectedConnectionId.value) {
    await loadTables()
  }
}

const loadTables = async () => {
  if (!selectedConnectionId.value) return
  
  loadingTables.value = true
  try {
    tables.value = await api.getDatabaseTables(selectedConnectionId.value)
  } catch (error) {
    ElMessage.error('加载表列表失败')
    console.error('加载表列表失败:', error)
  } finally {
    loadingTables.value = false
  }
}

const insertTableName = (tableName: string) => {
  const textarea = document.querySelector('.sql-textarea textarea') as HTMLTextAreaElement
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const text = sqlQuery.value
    sqlQuery.value = text.substring(0, start) + tableName + text.substring(end)
    
    // 重新设置光标位置
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(start + tableName.length, start + tableName.length)
    }, 0)
  }
}

const executeQuery = async () => {
  if (!selectedConnectionId.value || !sqlQuery.value.trim()) {
    ElMessage.warning('请选择数据库连接并输入SQL语句')
    return
  }

  executing.value = true
  try {
    // 预处理SQL查询
    let processedQuery = sqlQuery.value.trim()

    // 移除末尾的分号（如果存在），因为后端会自动添加LIMIT
    if (processedQuery.endsWith(';')) {
      processedQuery = processedQuery.slice(0, -1).trim()
    }

    const executeDto: ExecuteQueryDto = {
      connectionId: selectedConnectionId.value,
      query: processedQuery,
      limit: queryLimit.value
    }

    queryResult.value = await api.executeQuery(executeDto)

    if (queryResult.value.success) {
      ElMessage.success(`查询执行成功，返回 ${queryResult.value.totalRows} 行数据`)

      // 验证查询结果的完整性
      if (!queryResult.value.columns || !Array.isArray(queryResult.value.columns)) {
        console.warn('查询结果缺少列信息')
        queryResult.value.columns = []
      }

      if (!queryResult.value.data || !Array.isArray(queryResult.value.data)) {
        console.warn('查询结果缺少数据')
        queryResult.value.data = []
      }
    } else {
      ElMessage.error(`查询执行失败: ${queryResult.value.error}`)
    }
  } catch (error) {
    ElMessage.error('查询执行失败')
    console.error('查询执行失败:', error)
  } finally {
    executing.value = false
  }
}

const clearQuery = () => {
  sqlQuery.value = ''
  queryResult.value = null
}

const useQueryResult = () => {
  if (queryResult.value && queryResult.value.success) {
    emit('queryResult', queryResult.value, sqlQuery.value)
    ElMessage.success('查询结果已应用到流程发现')
  }
}

// 生命周期
onMounted(() => {
  loadConnections()
})
</script>

<style lang="scss" scoped>
.sql-editor {
  .editor-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: none;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    :global(.dark) & {
      background: rgba(30, 41, 59, 0.95);
      border: 1px solid rgba(71, 85, 105, 0.3);
    }
  }

  .editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .editor-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1e293b;
      margin: 0;

      :global(.dark) & {
        color: #f1f5f9;
      }
    }

    .editor-actions {
      display: flex;
      align-items: center;

      @media (max-width: 768px) {
        width: 100%;
        justify-content: space-between;
      }
    }
  }

  .editor-content {
    .section-title {
      font-weight: 600;
      color: #374151;
      font-size: 0.875rem;

      :global(.dark) & {
        color: #d1d5db;
      }
    }

    .sql-input-section {
      margin-bottom: 1.5rem;

      .input-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;

        .input-actions {
          display: flex;
          gap: 0.5rem;
        }
      }

      .query-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0.75rem;
        padding: 0.75rem;
        background: rgba(59, 130, 246, 0.05);
        border-radius: 8px;

        :global(.dark) & {
          background: rgba(96, 165, 250, 0.1);
        }

        .info-left {
          display: flex;
          flex-direction: column;
          gap: 0.25rem;
        }

        .info-item {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          color: #64748b;
          font-size: 0.875rem;
          font-weight: 500;

          :global(.dark) & {
            color: #94a3b8;
          }
        }

        .info-tip {
          font-size: 0.75rem;
          color: #9ca3af;
          font-style: italic;

          :global(.dark) & {
            color: #6b7280;
          }
        }
      }
    }

    .table-helper {
      margin-bottom: 1.5rem;
      padding: 1.5rem;
      background: rgba(248, 250, 252, 0.9);
      border-radius: 12px;
      border: 1px solid rgba(226, 232, 240, 0.6);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      :global(.dark) & {
        background: rgba(15, 23, 42, 0.9);
        border-color: rgba(71, 85, 105, 0.4);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      }

      .helper-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.25rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid rgba(226, 232, 240, 0.5);

        :global(.dark) & {
          border-bottom-color: rgba(71, 85, 105, 0.3);
        }

        .section-title {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-weight: 600;
          color: #374151;
          font-size: 1rem;

          :global(.dark) & {
            color: #d1d5db;
          }

          &::before {
            content: '';
            width: 4px;
            height: 16px;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            border-radius: 2px;
          }
        }

        .helper-tip {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem 1rem;
          background: rgba(59, 130, 246, 0.05);
          border: 1px solid rgba(59, 130, 246, 0.1);
          border-radius: 8px;
          margin-bottom: 1rem;
          font-size: 0.875rem;
          color: #3b82f6;

          :global(.dark) & {
            background: rgba(96, 165, 250, 0.1);
            border-color: rgba(96, 165, 250, 0.2);
            color: #60a5fa;
          }

          .el-icon {
            font-size: 1rem;
          }
        }
      }

      .tables-list {
        .loading-state,
        .empty-state {
          text-align: center;
          padding: 3rem 2rem;
          color: #64748b;

          :global(.dark) & {
            color: #94a3b8;
          }
        }

        .loading-state {
          background: rgba(59, 130, 246, 0.02);
          border: 1px dashed rgba(59, 130, 246, 0.2);
          border-radius: 8px;

          :global(.dark) & {
            background: rgba(96, 165, 250, 0.05);
            border-color: rgba(96, 165, 250, 0.2);
          }
        }

        .empty-state {
          background: rgba(107, 114, 128, 0.02);
          border: 1px dashed rgba(107, 114, 128, 0.2);
          border-radius: 8px;

          p {
            margin: 0;
            font-size: 0.875rem;
            font-weight: 500;
          }

          :global(.dark) & {
            background: rgba(107, 114, 128, 0.05);
            border-color: rgba(107, 114, 128, 0.2);
          }
        }

        .tables-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
          gap: 0.75rem;

          @media (max-width: 768px) {
            grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
            gap: 0.5rem;
          }

          .table-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(226, 232, 240, 0.6);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            min-height: 44px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

            &:hover {
              background: rgba(59, 130, 246, 0.1);
              border-color: rgba(59, 130, 246, 0.4);
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
            }

            :global(.dark) & {
              background: rgba(30, 41, 59, 0.9);
              border-color: rgba(71, 85, 105, 0.4);
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);

              &:hover {
                background: rgba(96, 165, 250, 0.1);
                border-color: rgba(96, 165, 250, 0.4);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.15);
              }
            }

            .el-icon {
              flex-shrink: 0;
              color: #3b82f6;
              font-size: 1rem;

              :global(.dark) & {
                color: #60a5fa;
              }
            }

            span {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              color: #374151;

              :global(.dark) & {
                color: #d1d5db;
              }
            }
          }
        }
      }
    }

    .result-section {
      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        @media (max-width: 768px) {
          flex-direction: column;
          align-items: flex-start;
          gap: 0.75rem;
        }

        .result-info {
          display: flex;
          align-items: center;
          gap: 1rem;

          .info-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #64748b;
            font-size: 0.875rem;

            :global(.dark) & {
              color: #94a3b8;
            }
          }
        }
      }

      .error-message {
        margin-bottom: 1rem;
      }

      .result-table {
        border-radius: 8px;
        overflow: hidden;
      }
    }
  }
}

.connection-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .connection-name {
    flex: 1;
  }
}

// SQL编辑器样式优化
:deep(.sql-textarea) {
  .el-textarea__inner {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 8px;
    border: 1px solid #d1d5db;
    transition: all 0.2s ease;

    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    :global(.dark) & {
      background: rgba(15, 23, 42, 0.8);
      border-color: #374151;
      color: #f1f5f9;

      &:focus {
        border-color: #60a5fa;
        box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
      }
    }
  }
}

// 表格样式优化
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;

  .el-table__header {
    background: rgba(248, 250, 252, 0.8);

    :global(.dark) & {
      background: rgba(15, 23, 42, 0.8);
    }
  }

  .el-table__row {
    &:hover {
      background: rgba(59, 130, 246, 0.05);

      :global(.dark) & {
        background: rgba(96, 165, 250, 0.1);
      }
    }
  }
}
</style>
