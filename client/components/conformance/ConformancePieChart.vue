<template>
  <div ref="chartRef" class="conformance-pie-chart"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import type { ConformanceResult } from '~/types'

interface Props {
  data: ConformanceResult
  height?: string
  theme?: 'light' | 'dark'
}

const props = withDefaults(defineProps<Props>(), {
  height: '300px',
  theme: 'light'
})

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 监听主题变化
watch(() => props.theme, (newTheme) => {
  if (chartInstance) {
    chartInstance.dispose()
    initChart()
  }
})

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value, props.theme)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance || !props.data) return

  const conformingRate = (props.data.conformingCases / props.data.totalCases) * 100
  const deviatingRate = (props.data.deviatingCases / props.data.totalCases) * 100

  const option = {
    title: {
      text: '案例符合性分布',
      left: 'center',
      textStyle: {
        color: props.theme === 'dark' ? '#e5e7eb' : '#374151',
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.name}: ${params.value} 个案例 (${params.percent}%)`
      }
    },
    legend: {
      orient: 'horizontal',
      bottom: '5%',
      left: 'center',
      itemGap: 20,
      textStyle: {
        color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
        fontSize: 12
      }
    },
    series: [
      {
        name: '案例分布',
        type: 'pie',
        radius: ['35%', '55%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 8,
          borderColor: props.theme === 'dark' ? '#1f2937' : '#ffffff',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}\n{c} 个\n({d}%)',
          color: props.theme === 'dark' ? '#e5e7eb' : '#374151',
          fontSize: 12,
          distanceToLabelLine: 5
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          lineStyle: {
            color: props.theme === 'dark' ? '#6b7280' : '#9ca3af'
          }
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        data: [
          {
            value: props.data.conformingCases,
            name: '符合案例',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#10b981' },
                { offset: 1, color: '#059669' }
              ])
            }
          },
          {
            value: props.data.deviatingCases,
            name: '偏差案例',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#f59e0b' },
                { offset: 1, color: '#d97706' }
              ])
            }
          }
        ]
      }
    ]
  }

  chartInstance.setOption(option, true)
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })

  // 监听窗口大小变化
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped lang="scss">
.conformance-pie-chart {
  width: 100%;
  height: v-bind(height);
  min-height: 300px;
}
</style>
