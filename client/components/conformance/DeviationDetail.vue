<template>
  <div class="deviation-detail-view">
    <div class="detail-header">
      <div class="header-content">
        <el-button
          :icon="ArrowLeft"
          link
          class="back-btn"
          @click="$emit('back')"
        />
        <div class="header-text">
          <div class="deviation-type-text">{{ getDeviationTypeText(deviation.type) }}</div>
          <h2 class="activity-name">{{ deviation.activity }}</h2>
        </div>
      </div>
    </div>

    <div class="detail-content">
      <!-- 相关案例列表 -->
      <div class="related-cases-section">
        <div class="section-header">
          <h4 class="section-title">
            案例({{ aggregatedCases.length }})
          </h4>
        </div>

        <div class="cases-list">
          <div
            v-for="aggregatedCase in aggregatedCases"
            :key="aggregatedCase.caseId"
            class="case-item"
            :class="{ 'highlighted': aggregatedCase.caseId === currentHighlightedCase }"
            @click="handleCaseClick(aggregatedCase.caseId)"
          >
            <div class="case-info">
              <div class="case-header-row">
                <div class="case-id">案例 {{ aggregatedCase.caseId }}</div>
                <div class="case-badges">
                  <span class="case-severity" :class="getSeverityClass(aggregatedCase.highestSeverity)">
                    {{ getSeverityText(aggregatedCase.highestSeverity) }}
                  </span>
                </div>
              </div>

              <!-- 简化的描述信息 -->
              <div class="case-description">
                {{ getAggregatedDescription(aggregatedCase) }}
              </div>

              <!-- 元数据信息 -->
              <div class="case-meta">
                <span v-if="aggregatedCase.activities.length > 0" class="case-activity">
                  活动: {{ aggregatedCase.activities.join(', ') }}
                </span>
                <span v-if="aggregatedCase.expectedActivities.length > 0" class="case-expected">
                  期望: {{ aggregatedCase.expectedActivities.join(', ') }}
                </span>
                <span v-if="aggregatedCase.timestamps.length > 0" class="case-time">
                  {{ formatTimestamp(aggregatedCase.timestamps[0]) }}
                  <span v-if="aggregatedCase.timestamps.length > 1">
                    等 {{ aggregatedCase.timestamps.length }} 次
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { ArrowLeft } from "@element-plus/icons-vue";

interface DeviationDetail {
  caseId: string;
  description: string;
  severity: string;
  activity?: string;
  expectedActivity?: string;
  timestamp?: Date | string;
}

interface DeviationItem {
  id: number;
  type: string;
  activity: string;
  count: number;
  percentage: number;
  deviations: DeviationDetail[];
}

interface AggregatedCase {
  caseId: string;
  deviations: DeviationDetail[];
  highestSeverity: string;
  deviationCount: number;
  activities: string[];
  expectedActivities: string[];
  timestamps: (Date | string)[];
}

interface Props {
  deviation: DeviationItem;
  getDeviationTypeText: (type: string) => string;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  back: [];
  'case-selected': [activityName: string, caseIds: string[], deviationType: string];
}>();

const currentHighlightedCase = ref<string>('');

// 组件挂载时，设置第一个案例为高亮状态
onMounted(() => {
  if (aggregatedCases.value.length > 0) {
    currentHighlightedCase.value = aggregatedCases.value[0].caseId;
  }
});

// 处理案例点击事件
const handleCaseClick = (caseId: string) => {
  // 更新当前高亮的案例
  currentHighlightedCase.value = caseId;

  // 触发高亮事件
  emit('case-selected', props.deviation.activity, [caseId], props.deviation.type);
};

// 聚合同一案例的偏差
const aggregatedCases = computed(() => {
  const caseMap = new Map<string, AggregatedCase>();

  props.deviation.deviations.forEach(dev => {
    if (!caseMap.has(dev.caseId)) {
      caseMap.set(dev.caseId, {
        caseId: dev.caseId,
        deviations: [],
        highestSeverity: 'low',
        deviationCount: 0,
        activities: [],
        expectedActivities: [],
        timestamps: []
      });
    }

    const aggregated = caseMap.get(dev.caseId)!;
    aggregated.deviations.push(dev);
    aggregated.deviationCount++;

    // 收集活动信息
    if (dev.activity && !aggregated.activities.includes(dev.activity)) {
      aggregated.activities.push(dev.activity);
    }
    if (dev.expectedActivity && !aggregated.expectedActivities.includes(dev.expectedActivity)) {
      aggregated.expectedActivities.push(dev.expectedActivity);
    }
    if (dev.timestamp) {
      aggregated.timestamps.push(dev.timestamp);
    }

    // 确定最高严重程度
    const severityOrder = { low: 1, medium: 2, high: 3 };
    const currentSeverity = severityOrder[dev.severity as keyof typeof severityOrder] || 1;
    const highestSeverity = severityOrder[aggregated.highestSeverity as keyof typeof severityOrder] || 1;

    if (currentSeverity > highestSeverity) {
      aggregated.highestSeverity = dev.severity;
    }
  });

  return Array.from(caseMap.values()).sort((a, b) => a.caseId.localeCompare(b.caseId));
});

const getSeverityClass = (severity: string) => {
  return `severity-${severity}`;
};

const getSeverityText = (severity: string) => {
  const severityMap: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高'
  };
  return severityMap[severity] || severity;
};

const formatTimestamp = (timestamp: Date | string | undefined) => {
  if (!timestamp) return '无';
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN');
};

const getAggregatedDescription = (aggregatedCase: AggregatedCase) => {
  // 获取所有唯一的描述
  const descriptions = aggregatedCase.deviations
    .map(d => d.description)
    .filter(Boolean);
  const uniqueDescriptions = [...new Set(descriptions)];

  // 如果只有一种描述，直接返回
  if (uniqueDescriptions.length === 1) {
    return uniqueDescriptions[0];
  }

  // 如果有多种描述，返回第一个并标注总数
  return uniqueDescriptions.length > 0
    ? `${uniqueDescriptions[0]}${uniqueDescriptions.length > 1 ? ` 等${uniqueDescriptions.length}种问题` : ''}`
    : '偏差详情';
};
</script>

<style scoped lang="scss">
.deviation-detail-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .detail-header {
    padding-bottom: 16px;
    background: white;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;

      .back-btn {
        color: #6c757d;
        font-size: 20px;
        padding: 8px;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          color: #495057;
          background-color: #f8f9fa;
        }
      }

      .header-text {
        .deviation-type-text {
          font-size: 14px;
          color: #6c757d;
          margin-bottom: 4px;
          font-weight: 400;
          line-height: 1.2;
        }

        .activity-name {
          font-size: 24px;
          font-weight: 700;
          color: #212529;
          margin: 0;
          line-height: 1.2;
        }
      }
    }
  }

  .detail-content {
    flex: 1;
    overflow-y: auto;

    .related-cases-section {
      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;

        .section-title {
          font-size: 18px;
          font-weight: 600;
          color: #212529;
          margin: 0;
        }
      }

      .cases-list {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .case-item {
          background: white;
          border: 1px solid #e1e5e9;
          border-radius: 8px;
          padding: 16px;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: space-between;
          position: relative;
          cursor: pointer;

          &:hover {
            border-color: #1976d2;
            box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15);
            transform: translateY(-1px);
          }

          &.highlighted {
            border-color: #1976d2;
            background-color: #f3f8ff;
            box-shadow: 0 2px 12px rgba(25, 118, 210, 0.2);

            .case-id {
              color: #1976d2;
              font-weight: 600;
            }

            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 0;
              bottom: 0;
              width: 4px;
              background-color: #1976d2;
              border-radius: 4px 0 0 4px;
            }
          }

          .case-info {
            flex: 1;

            .case-header-row {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 12px;

              .case-id {
                font-size: 14px;
                font-weight: 600;
                color: #212529;
              }

              .case-badges {
                display: flex;
                align-items: center;
                gap: 8px;

                .case-severity {
                  font-size: 11px;
                  padding: 4px 8px;
                  border-radius: 4px;
                  font-weight: 600;
                  text-transform: uppercase;

                  &.severity-low {
                    background: #d4edda;
                    color: #155724;
                  }

                  &.severity-medium {
                    background: #fff3cd;
                    color: #856404;
                  }

                  &.severity-high {
                    background: #f8d7da;
                    color: #721c24;
                  }
                }
              }
            }

            .case-description {
              font-size: 13px;
              color: #6c757d;
              margin-bottom: 12px;
              line-height: 1.4;
            }

            .case-meta {
              display: flex;
              align-items: center;
              gap: 12px;
              flex-wrap: wrap;

              .case-activity,
              .case-expected {
                font-size: 12px;
                color: #495057;
                background: #f8f9fa;
                padding: 4px 8px;
                border-radius: 4px;
              }

              .case-time {
                font-size: 12px;
                color: #6c757d;
              }
            }
          }
        }
      }
    }
  }
}
</style>
