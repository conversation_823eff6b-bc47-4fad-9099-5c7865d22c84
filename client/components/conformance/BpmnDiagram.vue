<template>
  <div class="bpmn-diagram-container">
    <div ref="diagramRef" class="bpmn-diagram"></div>
    <div v-if="isLoading" class="diagram-loading">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <span>加载BPMN模型中...</span>
    </div>

    <div
      v-if="showVirtualElementsHint"
      class="virtual-elements-hint"
    >
      <el-icon class="hint-icon"><InfoFilled /></el-icon>
      <span class="hint-text">点击虚线或虚线边框的节点，可将其添加到标准模型中</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as go from 'gojs'
import { Loading, InfoFilled } from '@element-plus/icons-vue'
import type { BpmnModel, ConformanceDeviation, ConformanceResult } from '~/types'

interface Props {
  bpmnXml?: string
  model?: BpmnModel
  deviations?: ConformanceDeviation[]
  result?: ConformanceResult
  height?: string
  theme?: 'light' | 'dark'
  readonly?: boolean
  enableDragging?: boolean
  showGrid?: boolean
  currentView?: 'deviations' | 'variants'
}

const props = withDefaults(defineProps<Props>(), {
  height: '500px',
  theme: 'light',
  readonly: true,
  enableDragging: true,
  showGrid: true
})

// 定义节点和连接线数据的类型
interface NodeData {
  key: string
  name: string
  category: string
  activityType?: string
  deviation?: ConformanceDeviation | {
    type: string
    severity: 'low' | 'medium' | 'high'
  } | null
  [key: string]: unknown
}

interface LinkData {
  key?: string
  from: string
  to: string
  name?: string
  isLoop?: boolean
  isWrongOrder?: boolean
  isSkip?: boolean
  deviation?: ConformanceDeviation | {
    type: string
    severity: 'low' | 'medium' | 'high'
  } | null
  [key: string]: unknown
}

const emit = defineEmits<{
  'node-click': [nodeData: NodeData, event?: MouseEvent]
  'link-click': [linkData: LinkData, event?: MouseEvent]
  'zoom-changed': [zoomLevel: number]
  'undo-redo-state-changed': [state: { canUndo: boolean, canRedo: boolean }]
  'virtual-elements-selected': [selectedElements: { nodes: Set<string>, links: Set<{from: string, to: string}> }]
}>()

const diagramRef = ref<HTMLElement>()
const isLoading = ref(true)
let diagram: go.Diagram | null = null

const selectedVirtualNodes = ref<Set<string>>(new Set()) // 存储节点名称
const selectedVirtualLinks = ref<Set<{from: string, to: string}>>(new Set()) // 存储连线对象

// 虚拟元素存在状态
const hasVirtualElements = ref(false)

// 检查是否显示虚拟元素提醒
const showVirtualElementsHint = computed(() => {
  return props.currentView === 'variants' && hasVirtualElements.value
})

// 检查并更新虚拟元素状态
const checkVirtualElements = () => {
  if (!diagram) {
    hasVirtualElements.value = false
    return
  }

  let found = false

  // 检查虚拟节点
  diagram.nodes.each((node: go.Node) => {
    if (node.data.isVirtual) {
      found = true
      return false
    }
  })

  // 如果没找到虚拟节点，检查虚拟连线
  if (!found) {
    diagram.links.each((link: go.Link) => {
      if (link.data.isVirtual) {
        found = true
        return false
      }
    })
  }

  hasVirtualElements.value = found
}

// 虚拟节点选中处理
const handleVirtualNodeSelection = (nodeKey: string, isMultiSelect: boolean) => {
  if (!diagram) return

  // 获取节点信息
  const node = diagram.findNodeForKey(nodeKey)
  if (!node || !node.data.isVirtual) return

  const nodeName = node.data.name || nodeKey

  const isCurrentNodeSelected = selectedVirtualNodes.value.has(nodeName)

  // 多选模式：按住Ctrl键时保持其他选择
  if (!isMultiSelect) {
    // 单选模式：清除所有其他选择
    selectedVirtualNodes.value.clear()
    selectedVirtualLinks.value.clear()
  }

  if (isCurrentNodeSelected) {
    // 如果之前已选中，则取消选中（不添加回去）
    selectedVirtualNodes.value.delete(nodeName)
  } else {
    // 如果之前未选中，则添加到选中列表
    selectedVirtualNodes.value.add(nodeName)
  }

  // 更新视觉样式
  updateVirtualElementStyles()

  // 通知父组件选中状态变化
  emit('virtual-elements-selected', {
    nodes: new Set(selectedVirtualNodes.value),
    links: new Set(selectedVirtualLinks.value)
  })
}

// 虚拟连线选中处理（通过连线对象）
const handleVirtualLinkSelectionWithLink = (link: go.Link, isMultiSelect: boolean) => {
  if (!diagram) {
    return
  }

  if (!link || !link.data.isVirtual) {
    return
  }

  // 获取源节点和目标节点的名称
  const fromNode = diagram.findNodeForKey(link.data.from)
  const toNode = diagram.findNodeForKey(link.data.to)

  const fromName = fromNode?.data.name || link.data.from
  const toName = toNode?.data.name || link.data.to

  const linkObj = { from: fromName, to: toName }

  // 检查是否已选中（需要在清空之前检查）
  const isSelected = Array.from(selectedVirtualLinks.value).some(
    item => item.from === linkObj.from && item.to === linkObj.to
  )

  // 多选模式：按住Ctrl键时保持其他选择
  if (!isMultiSelect) {
    // 单选模式：清除所有其他选择
    selectedVirtualNodes.value.clear()
    selectedVirtualLinks.value.clear()
  }
  if (isSelected) {
    // 如果已选中，则取消选中
    selectedVirtualLinks.value.forEach(item => {
      if (item.from === linkObj.from && item.to === linkObj.to) {
        selectedVirtualLinks.value.delete(item)
      }
    })
  } else {
    // 如果未选中，则添加到选中列表
    selectedVirtualLinks.value.add(linkObj)
  }

  // 更新视觉样式
  updateVirtualElementStyles()

  // 通知父组件选中状态变化
  emit('virtual-elements-selected', {
    nodes: new Set(selectedVirtualNodes.value),
    links: new Set(selectedVirtualLinks.value)
  })
}

// 更新虚拟元素的视觉样式
const updateVirtualElementStyles = () => {
  if (!diagram) return

  // 更新虚拟节点样式
  diagram.nodes.each((node: go.Node) => {
    if (node.data.isVirtual) {
      const shape = node.findObject('SHAPE') as go.Shape
      if (shape) {
        const nodeName = node.data.name || node.data.key
        const isSelected = selectedVirtualNodes.value.has(nodeName)
        if (isSelected) {
          // 选中状态：蓝色边框，更粗的线条
          shape.stroke = '#1a73e8'
          shape.strokeWidth = 4
          shape.fill = '#e8f0fe'
        } else {
          // 未选中状态：恢复原始样式
          shape.stroke = '#ef4444'
          shape.strokeWidth = 2
          shape.fill = '#fecaca'
        }
      }
    }
  })

  // 更新虚拟连线样式
  diagram.links.each((link: go.Link) => {
    if (link.data.isVirtual) {
      const shape = link.findObject('LINKSHAPE') as go.Shape
      const arrowShape = link.findObject('ARROWSHAPE') as go.Shape

      if (shape) {
        // 获取连线的源节点和目标节点名称
        const fromNode = diagram?.findNodeForKey(link.data.from)
        const toNode = diagram?.findNodeForKey(link.data.to)
        const fromName = fromNode?.data.name || link.data.from
        const toName = toNode?.data.name || link.data.to

        // 检查是否选中
        const isSelected = Array.from(selectedVirtualLinks.value).some(
          item => item.from === fromName && item.to === toName
        )

        if (isSelected) {
          // 选中状态：蓝色，更粗的线条
          shape.stroke = '#1a73e8'
          shape.strokeWidth = 4
          if (arrowShape) {
            arrowShape.stroke = '#1a73e8'
            arrowShape.fill = '#1a73e8'
          }
        } else {
          // 未选中状态：恢复原始样式
          shape.stroke = '#ef4444'
          shape.strokeWidth = 2
          if (arrowShape) {
            arrowShape.stroke = '#ef4444'
            arrowShape.fill = '#ef4444'
          }
        }
      }
    }
  })
}

// 清除虚拟元素选中状态
const clearVirtualElementSelection = () => {
  selectedVirtualNodes.value.clear()
  selectedVirtualLinks.value.clear()
  updateVirtualElementStyles()

  // 通知父组件选中状态变化
  emit('virtual-elements-selected', {
    nodes: new Set(),
    links: new Set()
  })
}

// 删除单个虚拟节点
const removeVirtualNode = (nodeKey: string) => {
  selectedVirtualNodes.value.delete(nodeKey)
  updateVirtualElementStyles()

  // 通知父组件选中状态变化
  emit('virtual-elements-selected', {
    nodes: selectedVirtualNodes.value,
    links: selectedVirtualLinks.value
  })
}

// 删除单个虚拟连线
const removeVirtualLink = (linkObj: { from: string; to: string }) => {
  // 找到并删除匹配的连线
  selectedVirtualLinks.value.forEach(item => {
    if (item.from === linkObj.from && item.to === linkObj.to) {
      selectedVirtualLinks.value.delete(item)
    }
  })
  updateVirtualElementStyles()

  // 通知父组件选中状态变化
  emit('virtual-elements-selected', {
    nodes: selectedVirtualNodes.value,
    links: selectedVirtualLinks.value
  })
}

// 全选虚拟元素
const selectAllVirtualElements = () => {
  if (!diagram) return

  // 选中所有虚拟节点
  diagram.nodes.each((node: go.Node) => {
    if (node.data.isVirtual) {
      const nodeName = node.data.name || node.data.key
      selectedVirtualNodes.value.add(nodeName)
    }
  })

  // 选中所有虚拟连线
  diagram.links.each((link: go.Link) => {
    if (link.data.isVirtual) {
      const fromNode = diagram?.findNodeForKey(link.data.from)
      const toNode = diagram?.findNodeForKey(link.data.to)
      const fromName = fromNode?.data.name || link.data.from
      const toName = toNode?.data.name || link.data.to

      // 检查是否已经存在相同的连线
      const linkExists = Array.from(selectedVirtualLinks.value).some(
        item => item.from === fromName && item.to === toName
      )

      if (!linkExists) {
        selectedVirtualLinks.value.add({ from: fromName, to: toName })
      }
    }
  })

  // 更新视觉样式
  updateVirtualElementStyles()

  // 通知父组件选中状态变化
  emit('virtual-elements-selected', {
    nodes: new Set(selectedVirtualNodes.value),
    links: new Set(selectedVirtualLinks.value)
  })
}

// 监听主题变化
watch(() => props.theme, () => {
  if (diagram) {
    updateTheme()
  }
})

// 监听数据变化
watch([() => props.bpmnXml, () => props.model, () => props.deviations], () => {
  if (diagram) {
    loadDiagram()
  }
}, { deep: true, immediate: true })

const initDiagram = () => {
  if (!diagramRef.value) return

  // 设置 GoJS 许可证密钥
  if (!go.Diagram.licenseKey) {
    go.Diagram.licenseKey = "2bf843e7b36758c511895a25406c7efb0bab2d67ce864df3595012a0ed587a04249fb87b50d7d8c986aa4df9182ec98ed8976121931c0338e737d48f45e0d5f1b63124e5061841dbf4052691c9fb38b1ff7971fbddbc68a2d2"
  }

  // 初始化GoJS图表
  diagram = new go.Diagram(diagramRef.value, {
    'undoManager.isEnabled': true,
    'undoManager.maxHistoryLength': 50, // 设置最大撤销历史长度
    'toolManager.mouseWheelBehavior': go.WheelMode.Zoom,
    'allowMove': props.enableDragging && !props.readonly,
    'allowCopy': false,
    'allowDelete': false,
    'allowSelect': true,
    'allowZoom': true,
    'allowHorizontalScroll': true,
    'allowVerticalScroll': true,
    'initialContentAlignment': go.Spot.Center,
    'layout': new go.LayeredDigraphLayout({
      direction: 90,
      layerSpacing: 50,
      columnSpacing: 30,
      setsPortSpots: false
    })
  })

  // 配置拖动和平移工具
  if (diagram.toolManager) {
    // 启用平移工具（鼠标拖动背景）
    diagram.toolManager.panningTool.isEnabled = true

    // 启用节点拖动工具
    if (diagram.toolManager.draggingTool) {
      diagram.toolManager.draggingTool.isEnabled = props.enableDragging && !props.readonly
      diagram.toolManager.draggingTool.isGridSnapEnabled = props.showGrid
      diagram.toolManager.draggingTool.gridSnapCellSize = new go.Size(10, 10)
      // 拖动时显示网格
      diagram.toolManager.draggingTool.isGridSnapRealtime = props.showGrid
    }

    // 配置选择工具
    if (diagram.toolManager.dragSelectingTool) {
      diagram.toolManager.dragSelectingTool.isEnabled = props.enableDragging && !props.readonly
    }
  }

  // 显示网格（可选）
  diagram.grid.visible = props.showGrid
  diagram.grid.gridCellSize = new go.Size(10, 10)

  // 定义节点模板
  diagram.nodeTemplateMap.add('Activity',
    new go.Node('Auto')
      .bind('location', 'loc', go.Point.parse)
      .bind('cursor', 'isVirtual', (isVirtual) => isVirtual ? 'pointer' : 'default')
      .add(
        new go.Shape('RoundedRectangle', {
          name: 'SHAPE',
          fill: 'white',
          stroke: '#3b82f6',
          strokeWidth: 2,
          minSize: new go.Size(80, 40)
        })
          .bind('fill', 'deviation', (deviation) => {
            if (!deviation) return props.theme === 'dark' ? '#374151' : 'white'
            switch (deviation.severity) {
              case 'high': return '#fecaca'
              case 'medium': return '#fed7aa'
              case 'low': return '#fef3c7'
              default: return props.theme === 'dark' ? '#374151' : 'white'
            }
          })
          .bind('stroke', 'deviation', (deviation) => {
            if (!deviation) return '#3b82f6'
            switch (deviation.severity) {
              case 'high': return '#ef4444'
              case 'medium': return '#f59e0b'
              case 'low': return '#eab308'
              default: return '#3b82f6'
            }
          }),
        new go.TextBlock({
          margin: 8,
          font: '12px sans-serif',
          stroke: props.theme === 'dark' ? '#e5e7eb' : '#374151',
          textAlign: 'center',
          overflow: go.TextBlock.OverflowEllipsis,
          maxSize: new go.Size(120, NaN)
        })
          .bind('text', 'name')
      )
  )

  // 定义开始事件模板
  diagram.nodeTemplateMap.add('StartEvent',
    new go.Node('Auto')
      .bind('location', 'loc', go.Point.parse)
      .add(
        new go.Shape('Circle', {
          name: 'SHAPE',
          fill: '#10b981',
          stroke: '#059669',
          strokeWidth: 2,
          width: 30,
          height: 30
        }),
        new go.TextBlock('开始', {
          font: '10px sans-serif',
          stroke: 'white',
          textAlign: 'center'
        })
      )
  )

  // 定义结束事件模板
  diagram.nodeTemplateMap.add('EndEvent',
    new go.Node('Auto')
      .bind('location', 'loc', go.Point.parse)
      .add(
        new go.Shape('Circle', {
          name: 'SHAPE',
          fill: '#ef4444',
          stroke: '#dc2626',
          strokeWidth: 3,
          width: 30,
          height: 30
        }),
        new go.TextBlock('结束', {
          font: '10px sans-serif',
          stroke: 'white',
          textAlign: 'center'
        })
      )
  )

  // 定义连接线模板
  diagram.linkTemplate = new go.Link({
    routing: go.Routing.AvoidsNodes,
    curve: go.Curve.JumpOver,
    corner: 5,
    toShortLength: 4,
    relinkableFrom: false,
    relinkableTo: false,
    reshapable: false,
    selectable: true,
    pickable: true
  })
    .bind('cursor', 'isVirtual', (isVirtual) => isVirtual ? 'pointer' : 'default')
    .bind('routing', '', (data) => {
      if (data.isLoop) return go.Routing.Normal
      if (data.isWrongOrder) return go.Routing.Orthogonal
      if (data.isSkip) return go.Routing.AvoidsNodes
      return go.Routing.AvoidsNodes
    })
    .bind('curve', '', (data) => {
      if (data.isLoop) return go.Curve.Bezier
      if (data.isWrongOrder) return go.Curve.JumpGap
      if (data.isSkip) return go.Curve.Bezier
      return go.Curve.JumpOver
    })
    .bind('curviness', '', (data) => {
      // 自循环连接线的曲率
      if (data.isLoop) return 40
      if (data.isWrongOrder) return 20
      if (data.isSkip) return 30
      return 0
    })
    .bind('fromSpot', '', (data) => {
      // 自循环从节点右侧开始
      if (data.isLoop) return go.Spot.Right
      if (data.isWrongOrder) return go.Spot.Bottom
      if (data.isSkip) return go.Spot.Right
      return go.Spot.Default
    })
    .bind('toSpot', '', (data) => {
      // 自循环回到节点顶部
      if (data.isLoop) return go.Spot.Top
      if (data.isWrongOrder) return go.Spot.Top
      if (data.isSkip) return go.Spot.Left
      return go.Spot.Default
    })
    .bind('fromEndSegmentLength', '', (data) => {
      if (data.isLoop) return 20
      if (data.isWrongOrder) return 15
      if (data.isSkip) return 10
      return 4
    })
    .bind('toEndSegmentLength', '', (data) => {
      if (data.isLoop) return 20
      if (data.isWrongOrder) return 15
      if (data.isSkip) return 10
      return 4
    })
    .add(
      new go.Shape({
        name: 'LINKSHAPE',
        stroke: props.theme === 'dark' ? '#6b7280' : '#9ca3af',
        strokeWidth: 2
      })
        .bind('stroke', 'deviation', (deviation) => {
          if (!deviation) return props.theme === 'dark' ? '#6b7280' : '#9ca3af'
          switch (deviation.severity) {
            case 'high': return '#ef4444'
            case 'medium': return '#f59e0b'
            case 'low': return '#eab308'
            default: return props.theme === 'dark' ? '#6b7280' : '#9ca3af'
          }
        })
        .bind('strokeWidth', 'deviation', (deviation) => deviation ? 3 : 2)
        .bind('strokeDashArray', '', (data) => {
          if (data.isWrongOrder) return [8, 4]
          if (data.isSkip) return [6, 6]
          return null
        }),
      new go.Shape({
        name: 'ARROWSHAPE',
        toArrow: 'Standard',
        stroke: props.theme === 'dark' ? '#6b7280' : '#9ca3af',
        fill: props.theme === 'dark' ? '#6b7280' : '#9ca3af'
      })
        .bind('stroke', 'deviation', (deviation) => {
          if (!deviation) return props.theme === 'dark' ? '#6b7280' : '#9ca3af'
          switch (deviation.severity) {
            case 'high': return '#ef4444'
            case 'medium': return '#f59e0b'
            case 'low': return '#eab308'
            default: return props.theme === 'dark' ? '#6b7280' : '#9ca3af'
          }
        })
        .bind('fill', 'deviation', (deviation) => {
          if (!deviation) return props.theme === 'dark' ? '#6b7280' : '#9ca3af'
          switch (deviation.severity) {
            case 'high': return '#ef4444'
            case 'medium': return '#f59e0b'
            case 'low': return '#eab308'
            default: return props.theme === 'dark' ? '#6b7280' : '#9ca3af'
          }
        }),

    )

  // 创建一个自定义的鼠标事件对象，包含正确的坐标信息
  const createMouseEventFromGoJS = (inputEvent: go.InputEvent | null): MouseEvent | undefined => {
    if (!inputEvent) return undefined

    const originalEvent = inputEvent.event as MouseEvent
    const viewPoint = inputEvent.viewPoint

    // 如果原始事件存在且有正确的坐标，直接使用
    if (originalEvent && originalEvent.clientX !== undefined && originalEvent.clientY !== undefined) {
      return originalEvent
    }

    // 如果没有原始事件或坐标不正确，尝试从GoJS坐标转换
    const diagramDiv = diagram?.div
    if (!diagramDiv || !viewPoint) {
      return originalEvent
    }

    const rect = diagramDiv.getBoundingClientRect()
    const clientX = rect.left + viewPoint.x
    const clientY = rect.top + viewPoint.y

    // 创建一个新的鼠标事件对象，包含正确的客户端坐标
    const customEvent = new MouseEvent(originalEvent?.type || 'click', {
      bubbles: true,
      cancelable: true,
      clientX: clientX,
      clientY: clientY,
      button: originalEvent?.button || 0,
      buttons: originalEvent?.buttons || 1,
      ctrlKey: originalEvent?.ctrlKey || false,
      shiftKey: originalEvent?.shiftKey || false,
      altKey: originalEvent?.altKey || false,
      metaKey: originalEvent?.metaKey || false
    })

    return customEvent
  }

  // 添加点击事件
  diagram.addDiagramListener('ObjectSingleClicked', (e) => {
    const part = e.subject.part
    const inputEvent = e.diagram.lastInput
    const mouseEvent = createMouseEventFromGoJS(inputEvent)

    if (part instanceof go.Node) {
      // 检查是否为虚拟节点且在变体视图中
      if (part.data.isVirtual && props.currentView === 'variants') {
        handleVirtualNodeSelection(part.data.key, mouseEvent?.ctrlKey || false)
      } else {
        emit('node-click', part.data, mouseEvent)
      }
    } else if (part instanceof go.Link) {
      if (part.data.isVirtual && props.currentView === 'variants') {
        handleVirtualLinkSelectionWithLink(part as go.Link, mouseEvent?.ctrlKey || false)
      } else {
        emit('link-click', part.data, mouseEvent)
      }
    }
  })

  // 添加右键点击事件
  diagram.addDiagramListener('ObjectContextClicked', (e) => {
    const part = e.subject.part
    const inputEvent = e.diagram.lastInput
    const mouseEvent = createMouseEventFromGoJS(inputEvent)

    // 阻止默认的右键菜单
    if (mouseEvent) {
      mouseEvent.preventDefault()
      mouseEvent.stopPropagation()
    }

    if (part instanceof go.Node) {
      // 检查是否为虚拟节点且在变体视图中
      if (part.data.isVirtual && props.currentView === 'variants') {
        handleVirtualNodeSelection(part.data.key, mouseEvent?.ctrlKey || false)
      } else {
        emit('node-click', part.data, mouseEvent)
      }
    } else if (part instanceof go.Link) {
      // 检查是否为虚拟连线且在变体视图中
      if (part.data.isVirtual && props.currentView === 'variants') {
        handleVirtualLinkSelectionWithLink(part as go.Link, mouseEvent?.ctrlKey || false)
      } else {
        emit('link-click', part.data, mouseEvent)
      }
    }
  })

  // 添加缩放变化监听器
  diagram.addDiagramListener('ViewportBoundsChanged', (_e) => {
    // 当视口边界改变时（包括缩放），通知父组件
    if (diagram) {
      const currentZoom = Math.round(diagram.scale * 100)
      emit('zoom-changed', currentZoom)
    }
  })

  // 添加撤销管理器状态变化监听器
  diagram.addDiagramListener('ChangedSelection', (_e) => {
    // 当选择改变时，也更新撤销/重做状态
    if (diagram && diagram.undoManager) {
      emit('undo-redo-state-changed', {
        canUndo: diagram.undoManager.canUndo(),
        canRedo: diagram.undoManager.canRedo()
      })
    }
  })

  // 监听模型变化以更新撤销/重做状态
  diagram.model.addChangedListener((_e) => {
    // 当模型发生变化时，更新撤销/重做状态
    if (diagram && diagram.undoManager) {
      emit('undo-redo-state-changed', {
        canUndo: diagram.undoManager.canUndo(),
        canRedo: diagram.undoManager.canRedo()
      })
    }
  })

  // 配置选择框功能
  if (diagram.toolManager.dragSelectingTool) {
    diagram.toolManager.dragSelectingTool.isEnabled = !props.readonly
  }
}

const updateTheme = () => {
  if (!diagram) return
  
  // 更新背景色
  diagram.div!.style.backgroundColor = props.theme === 'dark' ? '#1f2937' : '#ffffff'
  
  // 重新渲染
  diagram.requestUpdate()
}

const loadDiagram = async () => {
  if (!diagram) return

  try {
    isLoading.value = true

    let nodeDataArray: NodeData[] = []
    let linkDataArray: LinkData[] = []

    // 优先使用bpmnXml，如果存在的话
    if (props.bpmnXml) {
      console.log('Using bpmnXml prop for rendering')
      const diagramData = await buildDiagramFromBpmnXml(props.bpmnXml)
      nodeDataArray = diagramData.nodes
      linkDataArray = diagramData.links
    } else if (props.model?.bpmnXml) {
      console.log('Using model.bpmnXml for rendering')
      const diagramData = await buildDiagramFromBpmnXml(props.model.bpmnXml)
      nodeDataArray = diagramData.nodes
      linkDataArray = diagramData.links
    } else if (props.model && props.model.modelData) {
      console.log('Using model.modelData for rendering')
      const diagramData = buildDiagramFromModelData(props.model.modelData)
      nodeDataArray = diagramData.nodes
      linkDataArray = diagramData.links
    } else if (props.model && props.model.activities && props.model.activities.length > 0) {
      console.log('Using model.activities for rendering')
      // 使用简化的活动列表
      nodeDataArray = [
        { key: 'start', category: 'StartEvent', name: '开始' },
        ...props.model.activities.map((activity, index) => ({
          key: `activity_${index}`,
          category: 'Activity',
          name: activity,
          deviation: findDeviationForActivity(activity)
        })),
        { key: 'end', category: 'EndEvent', name: '结束' }
      ]

      linkDataArray = [
        { from: 'start', to: 'activity_0' },
        ...props.model.activities.slice(0, -1).map((_, index) => ({
          from: `activity_${index}`,
          to: `activity_${index + 1}`
        })),
        { from: `activity_${props.model.activities.length - 1}`, to: 'end' }
      ]
    } else {
      console.log('Using default diagram structure')
      // 默认显示一个简单的流程
      nodeDataArray = [
        { key: 'start', category: 'StartEvent', name: '开始' },
        { key: 'end', category: 'EndEvent', name: '结束' }
      ]

      linkDataArray = [
        { from: 'start', to: 'end' }
      ]
    }

    // 设置模型数据
    diagram.model = new go.GraphLinksModel(nodeDataArray, linkDataArray)

    // 延迟一下再进行布局，确保DOM已经渲染
    await nextTick()

    // 自动布局
    diagram.layoutDiagram(true)

    // 适应内容大小
    setTimeout(() => {
      if (diagram) {
        diagram.zoomToFit()
      }
    }, 100)

  } catch (error) {
    console.error('Failed to load BPMN diagram:', error)
  } finally {
    isLoading.value = false
  }
}

const findDeviationForActivity = (activityName: string) => {
  if (!props.deviations) return null
  return props.deviations.find(d => d.activity === activityName) || null
}

// 从BPMN XML构建图表数据
const buildDiagramFromBpmnXml = async (bpmnXml: string) => {
  const nodes: NodeData[] = []
  const links: LinkData[] = []

  try {
    console.log('Building diagram from BPMN XML')

    // 解析XML
    const parser = new DOMParser()
    const xmlDoc = parser.parseFromString(bpmnXml, 'text/xml')

    // 检查解析错误
    const parseError = xmlDoc.querySelector('parsererror')
    if (parseError) {
      throw new Error('Invalid XML format')
    }

    // 查找所有BPMN元素，使用更全面的选择器
    const tasks = xmlDoc.querySelectorAll('bpmn\\:task, bpmn2\\:task, task, bpmn\\:userTask, bpmn2\\:userTask, userTask, bpmn\\:serviceTask, bpmn2\\:serviceTask, serviceTask, bpmn\\:scriptTask, bpmn2\\:scriptTask, scriptTask, bpmn\\:manualTask, bpmn2\\:manualTask, manualTask')
    const startEvents = xmlDoc.querySelectorAll('bpmn\\:startEvent, bpmn2\\:startEvent, startEvent')
    const endEvents = xmlDoc.querySelectorAll('bpmn\\:endEvent, bpmn2\\:endEvent, endEvent')
    const sequenceFlows = xmlDoc.querySelectorAll('bpmn\\:sequenceFlow, bpmn2\\:sequenceFlow, sequenceFlow')

    // 如果没有找到任务，尝试从sequenceFlow中推断节点
    if (tasks.length === 0 && sequenceFlows.length > 0) {
      const nodeIds = new Set<string>()

      sequenceFlows.forEach(flow => {
        const sourceRef = flow.getAttribute('sourceRef')
        const targetRef = flow.getAttribute('targetRef')
        if (sourceRef) nodeIds.add(sourceRef)
        if (targetRef) nodeIds.add(targetRef)
      })

      // 为每个推断的节点创建任务节点（除了已知的开始和结束事件）
      nodeIds.forEach(nodeId => {
        if (!nodeId.includes('StartEvent') && !nodeId.includes('EndEvent')) {
          // 尝试从XML中找到对应的元素来获取名称
          const element = xmlDoc.querySelector(`[id="${nodeId}"]`)
          const name = element?.getAttribute('name') || nodeId.replace('Task_', '任务 ')

          nodes.push({
            key: nodeId,
            category: 'Activity',
            name: name,
            deviation: findDeviationForActivity(name)
          })
        }
      })
    } else {
      // 添加任务节点
      tasks.forEach((task, index) => {
        const id = task.getAttribute('id') || `Task_${index + 1}`
        const name = task.getAttribute('name') || `任务 ${index + 1}`
        nodes.push({
          key: id,
          category: 'Activity',
          name: name,
          deviation: findDeviationForActivity(name)
        })
      })
    }

    // 添加开始事件
    startEvents.forEach((startEvent, index) => {
      const id = startEvent.getAttribute('id') || `StartEvent_${index + 1}`
      const name = startEvent.getAttribute('name') || '开始'
      nodes.push({
        key: id,
        category: 'StartEvent',
        name: name
      })
    })

    // 添加结束事件
    endEvents.forEach((endEvent, index) => {
      const id = endEvent.getAttribute('id') || `EndEvent_${index + 1}`
      const name = endEvent.getAttribute('name') || '结束'
      nodes.push({
        key: id,
        category: 'EndEvent',
        name: name
      })
    })

    // 添加连接线
    sequenceFlows.forEach((flow, index) => {
      const id = flow.getAttribute('id') || `Flow_${index + 1}`
      const sourceRef = flow.getAttribute('sourceRef')
      const targetRef = flow.getAttribute('targetRef')

      if (sourceRef && targetRef) {
        links.push({
          key: id,
          from: sourceRef,
          to: targetRef
        })
      }
    })

    // 如果没有找到任何元素，创建默认结构
    if (nodes.length === 0) {
      nodes.push(
        { key: 'StartEvent_1', category: 'StartEvent', name: '开始' },
        { key: 'EndEvent_1', category: 'EndEvent', name: '结束' }
      )
      links.push({ from: 'StartEvent_1', to: 'EndEvent_1' })
    }

    return { nodes, links }

  } catch (error) {
    console.error('Error building diagram from BPMN XML:', error)
    // 返回默认结构
    return {
      nodes: [
        { key: 'StartEvent_1', category: 'StartEvent', name: '开始' },
        { key: 'EndEvent_1', category: 'EndEvent', name: '结束' }
      ],
      links: [
        { from: 'StartEvent_1', to: 'EndEvent_1' }
      ]
    }
  }
}

// 定义模型数据类型
interface ModelData {
  activities?: string[]
  paths?: Array<{
    from: string
    to: string
    [key: string]: unknown
  }>
  [key: string]: unknown
}

// 从模型数据构建图表数据
const buildDiagramFromModelData = (modelData: ModelData) => {
  const nodes: NodeData[] = []
  const links: LinkData[] = []

  try {
    // 添加开始事件
    nodes.push({
      key: 'StartEvent_1',
      category: 'StartEvent',
      name: '开始'
    })

    // 添加结束事件
    nodes.push({
      key: 'EndEvent_1',
      category: 'EndEvent',
      name: '结束'
    })

    // 添加活动节点
    if (modelData.activities && Array.isArray(modelData.activities)) {
      modelData.activities.forEach((activity: string, index: number) => {
        nodes.push({
          key: `Task_${index + 1}`,
          category: 'Activity',
          name: activity,
          deviation: findDeviationForActivity(activity)
        })
      })
    }

    // 从paths构建连接关系
    if (modelData.paths && Array.isArray(modelData.paths)) {
      modelData.paths.forEach((path, index: number) => {
        if (path.from && path.to) {
          links.push({
            key: `Flow_${index}`,
            from: path.from,
            to: path.to
          })
        }
      })
    } else {
      // 如果没有paths，创建简单的线性连接
      if (modelData.activities && modelData.activities.length > 0) {
        // 开始 -> 第一个活动
        links.push({
          from: 'StartEvent_1',
          to: 'Task_1'
        })

        // 活动之间的连接
        for (let i = 0; i < modelData.activities.length - 1; i++) {
          links.push({
            from: `Task_${i + 1}`,
            to: `Task_${i + 2}`
          })
        }

        // 最后一个活动 -> 结束
        links.push({
          from: `Task_${modelData.activities.length}`,
          to: 'EndEvent_1'
        })
      } else {
        // 只有开始和结束
        links.push({
          from: 'StartEvent_1',
          to: 'EndEvent_1'
        })
      }
    }

    return { nodes, links }

  } catch (error) {
    console.log(error)
    // 返回默认结构
    return {
      nodes: [
        { key: 'start', category: 'StartEvent', name: '开始' },
        { key: 'end', category: 'EndEvent', name: '结束' }
      ],
      links: [
        { from: 'start', to: 'end' }
      ]
    }
  }
}

// 类型定义
interface ActivityOccurrence {
  activity: string
  index: number
}

interface HighlightData {
  deviationNodes: Set<string>
  deviationLinks: Set<string>
  relatedNodes: Set<string>
  relatedLinks: Set<string>
  standardTrace: string[]
}

interface CaseAnalysis {
  caseId: string
  trace: string[]
  alignedTrace: string[]
  isConforming: boolean
}

// 高亮状态管理
const highlightedNodes = ref<Set<string>>(new Set())
const highlightedLinks = ref<Set<string>>(new Set())
const isHighlightMode = ref(false)

// 根据偏差活动高亮相关节点和连接线
const highlightDeviationCases = (activityName: string, caseIds: string[], deviationType?: string) => {
  if (!diagram || !props.result?.caseAnalysis) return
  // 打印案例轨迹信息
  printCaseTraces(caseIds)

  // 清除之前的高亮
  clearHighlight()

  // 找到目标活动节点
  const targetNodeKey = findNodeKeyByActivity(activityName)

  // 对于额外活动和跳过活动，节点可能不存在或不需要存在
  if (!targetNodeKey && deviationType !== 'EXTRA_ACTIVITY' && deviationType !== 'SKIPPED_ACTIVITY') {
    return
  }

  // 初始化高亮数据
  const highlightData = initializeHighlightData(targetNodeKey || '')

  // 对于跳过活动，不需要将被跳过的节点添加到相关节点中
  if (deviationType === 'SKIPPED_ACTIVITY' && targetNodeKey) {
    highlightData.relatedNodes.delete(targetNodeKey)
  }

  // 根据偏差类型选择处理器
  const handler = deviationType ? getDeviationHandler(deviationType) : null

  if (handler) {
    // 使用专门的处理器
    caseIds.forEach(caseId => {
      const caseAnalysis = findCaseAnalysis(caseId)
      if (!caseAnalysis) return

      // 确保轨迹包含开始和结束节点
      caseAnalysis.trace = ensureTraceWithStartEnd(caseAnalysis.trace)

      const activityOccurrences = findActivityOccurrences(caseAnalysis.trace, activityName)

      // 对于跳过活动，即使活动不在轨迹中也要处理
      if (activityOccurrences.length === 0 && deviationType !== 'SKIPPED_ACTIVITY') return

      handler(highlightData, caseAnalysis, activityOccurrences, activityName, targetNodeKey || '')
    })
  } else {
    // 默认处理器
    caseIds.forEach(caseId => {
      const caseAnalysis = findCaseAnalysis(caseId)
      if (!caseAnalysis) return

      caseAnalysis.trace = ensureTraceWithStartEnd(caseAnalysis.trace)

      const activityOccurrences = findActivityOccurrences(caseAnalysis.trace, activityName)
      if (activityOccurrences.length === 0) return

      handleDefaultDeviation(highlightData, caseAnalysis, activityOccurrences, activityName)
    })
  }

  // 应用高亮效果
  applyHighlightResults(highlightData)
}

// 打印案例轨迹
const printCaseTraces = (caseIds: string[]) => {
  caseIds.forEach(caseId => {
    const caseAnalysis = findCaseAnalysis(caseId)
    if (caseAnalysis) {
      console.log(`案例 ${caseId} 的流程轨迹:`, caseAnalysis.trace)
    }
  })
}

// 确保轨迹包含开始和结束节点
const ensureTraceWithStartEnd = (originalTrace: string[]): string[] => {
  if (!originalTrace || originalTrace.length === 0) {
    return []
  }

  const trace = [...originalTrace]

  const isStartNode = trace[0] === '开始'
  const isEndNode = trace[trace.length - 1] === '结束'

  if (!isStartNode) {
    trace.unshift('开始')
  }

  if (!isEndNode) {
    trace.push('结束')
  }

  return trace
}

// 查找案例分析数据
const findCaseAnalysis = (caseId: string): CaseAnalysis | undefined => {
  return props.result?.caseAnalysis?.find((c: CaseAnalysis) => c.caseId === caseId)
}

// 查找活动出现位置
const findActivityOccurrences = (trace: string[], activityName: string): ActivityOccurrence[] => {
  return trace.map((activity, index) => ({ activity, index }))
    .filter(item => item.activity === activityName)
}

// 初始化高亮数据
const initializeHighlightData = (targetNodeKey: string): HighlightData => {
  return {
    deviationNodes: new Set<string>(),
    deviationLinks: new Set<string>(),
    relatedNodes: new Set<string>([targetNodeKey]),
    relatedLinks: new Set<string>(),
    standardTrace: props.model?.activities || []
  }
}

// 重复活动偏差处理器
const handleRepeatedActivity = (
  highlightData: HighlightData,
  caseAnalysis: CaseAnalysis,
  activityOccurrences: ActivityOccurrence[],
  activityName: string,
  targetNodeKey: string
) => {
  if (activityOccurrences.length > 1) {
    // 标记节点为偏差
    highlightData.deviationNodes.add(targetNodeKey)

    // 创建回环连接线
    const loopLinkKey = createLoopConnectionKey(targetNodeKey)
    highlightData.relatedLinks.add(loopLinkKey)
    highlightData.deviationLinks.add(loopLinkKey)
  }
}

// 缺失活动偏差处理器
const handleMissingActivity = (
  highlightData: HighlightData,
  caseAnalysis: CaseAnalysis,
  activityOccurrences: ActivityOccurrence[],
  activityName: string,
  targetNodeKey: string
) => {
  // 缺失活动：标记为偏差节点（虚线显示）
  highlightData.deviationNodes.add(targetNodeKey)

  // 分析前后连接，显示缺失的上下文
  const standardTrace = highlightData.standardTrace
  const activityIndex = standardTrace.indexOf(activityName)

  if (activityIndex > 0) {
    const prevActivity = standardTrace[activityIndex - 1]
    const prevNodeKey = findNodeKeyByActivity(prevActivity)
    if (prevNodeKey) {
      // 前一个节点是已有的，使用实线
      highlightData.relatedNodes.add(prevNodeKey)

      // 到缺失节点的连接是偏差连接（虚线）
      addDeviationConnection(highlightData, prevActivity, activityName)
    }
  }

  if (activityIndex < standardTrace.length - 1) {
    const nextActivity = standardTrace[activityIndex + 1]
    const nextNodeKey = findNodeKeyByActivity(nextActivity)
    if (nextNodeKey) {
      // 后一个节点是已有的，使用实线
      highlightData.relatedNodes.add(nextNodeKey)

      // 从缺失节点的连接是偏差连接（虚线）
      addDeviationConnection(highlightData, activityName, nextActivity)
    }
  }
}

// 额外活动偏差处理器
const handleExtraActivity = (
  highlightData: HighlightData,
  caseAnalysis: CaseAnalysis,
  activityOccurrences: ActivityOccurrence[],
  activityName: string,
  targetNodeKey: string
) => {
  // 如果额外活动节点不存在，需要创建虚拟节点
  if (!targetNodeKey || targetNodeKey === '') {
    activityOccurrences.forEach((occurrence) => {
      ensureExtraActivityChain(caseAnalysis.trace, occurrence.index)
    })

    // 查找已创建的虚拟节点，不再重复创建
    let virtualNodeKey: string | null = null
    const possibleVirtualKey = `virtual-extra-${activityName}`
    diagram?.nodes.each((node: go.Node) => {
      if (node.data.key && node.data.key.startsWith(possibleVirtualKey)) {
        virtualNodeKey = node.data.key
        console.log(`找到已创建的虚拟节点: ${activityName}, key: ${virtualNodeKey}`)
        return false
      }
    })

    if (virtualNodeKey) {
      highlightData.deviationNodes.add(virtualNodeKey)
      highlightData.relatedNodes.add(virtualNodeKey)
    }

    // 分析前后连接关系，高亮相关的已有节点和虚拟连接线
    activityOccurrences.forEach((occurrence) => {
      const activityIndex = occurrence.index
      const trace = caseAnalysis.trace

      if (activityIndex > 0) {
        const prevActivity = trace[activityIndex - 1]
        let prevNodeKey = findNodeKeyByActivity(prevActivity)

        // 如果前置节点不存在，检查是否有虚拟节点
        if (!prevNodeKey && virtualNodeKey) {
          // 查找可能存在的前置虚拟节点
          const possiblePrevVirtualKey = `virtual-extra-${prevActivity}`
          diagram?.nodes.each((node: go.Node) => {
            if (node.data.key && node.data.key.startsWith(possiblePrevVirtualKey)) {
              prevNodeKey = node.data.key
              return false
            }
          })

          if (!prevNodeKey) {
            const virtualNodes: string[] = []
            diagram?.nodes.each((node: go.Node) => {
              if (node.data.isVirtual) {
                virtualNodes.push(`${node.data.name}(${node.data.key})`)
              }
            })
          }
        }

        if (prevNodeKey && virtualNodeKey) {
          highlightData.relatedNodes.add(prevNodeKey)
          if (prevNodeKey.startsWith('virtual-extra-')) {
            highlightData.deviationNodes.add(prevNodeKey)
          }

          // 添加虚拟连接线到高亮数据
          const prevLinkKey = `virtual-link-${prevNodeKey}-${virtualNodeKey}`
          highlightData.relatedLinks.add(prevLinkKey)
          highlightData.deviationLinks.add(prevLinkKey) // 虚线显示
        }
      }

      // 高亮后一个节点（已有节点或新创建的虚拟节点）
      if (activityIndex < trace.length - 1) {
        const nextActivity = trace[activityIndex + 1]
        let nextNodeKey = findNodeKeyByActivity(nextActivity)

        if (!nextNodeKey && virtualNodeKey) {
          const possibleNextVirtualKey = `virtual-extra-${nextActivity}`
          diagram?.nodes.each((node: go.Node) => {
            if (node.data.key && node.data.key.startsWith(possibleNextVirtualKey)) {
              nextNodeKey = node.data.key
              console.log(`找到后续虚拟节点: ${nextActivity}, key: ${nextNodeKey}`)
              return false
            }
          })

          if (!nextNodeKey) {
            setTimeout(() => {
              diagram?.nodes.each((node: go.Node) => {
                if (node.data.key && node.data.key.startsWith(possibleNextVirtualKey)) {
                  nextNodeKey = node.data.key

                  // 补充添加到高亮数据
                  if (nextNodeKey && virtualNodeKey) {
                    highlightData.relatedNodes.add(nextNodeKey)
                    highlightData.deviationNodes.add(nextNodeKey)
                    const nextLinkKey = `virtual-link-${virtualNodeKey}-${nextNodeKey}`
                    highlightData.relatedLinks.add(nextLinkKey)
                    highlightData.deviationLinks.add(nextLinkKey)
                  }
                  return false
                }
              })
            }, 100)
          }
        }

        if (nextNodeKey && virtualNodeKey) {
          highlightData.relatedNodes.add(nextNodeKey)
          // 如果是虚拟节点，也标记为偏差节点
          if (nextNodeKey.startsWith('virtual-extra-')) {
            highlightData.deviationNodes.add(nextNodeKey)
          }

          // 添加虚拟连接线到高亮数据
          const nextLinkKey = `virtual-link-${virtualNodeKey}-${nextNodeKey}`
          highlightData.relatedLinks.add(nextLinkKey)
          highlightData.deviationLinks.add(nextLinkKey) // 虚线显示
        }
      }
    })
  } else {
    // 如果额外活动节点存在（不太可能，但保留原逻辑）
    highlightData.deviationNodes.add(targetNodeKey)

    // 分析实际的前后连接关系
    activityOccurrences.forEach((occurrence) => {
      const activityIndex = occurrence.index
      const trace = caseAnalysis.trace

      if (activityIndex > 0) {
        const prevActivity = trace[activityIndex - 1]
        const prevNodeKey = findNodeKeyByActivity(prevActivity)
        if (prevNodeKey) {
          highlightData.relatedNodes.add(prevNodeKey)
          addDeviationConnection(highlightData, prevActivity, activityName)
        }
      }

      if (activityIndex < trace.length - 1) {
        const nextActivity = trace[activityIndex + 1]
        const nextNodeKey = findNodeKeyByActivity(nextActivity)
        if (nextNodeKey) {
          highlightData.relatedNodes.add(nextNodeKey)
          addDeviationConnection(highlightData, activityName, nextActivity)
        }
      }
    })
  }
}

// 创建虚拟的额外活动节点和连接线
const createVirtualExtraNode = (activityName: string, activityOccurrences: ActivityOccurrence[], trace: string[]): string | null => {
  if (!diagram || activityOccurrences.length === 0) return null

  const occurrence = activityOccurrences[0] // 取第一个出现位置
  const activityIndex = occurrence.index

  // 找到前后节点
  let prevNodeKey = ''
  let nextNodeKey = ''
  let prevActivity = ''
  let nextActivity = ''

  if (activityIndex > 0) {
    prevActivity = trace[activityIndex - 1]
    prevNodeKey = findNodeKeyByActivity(prevActivity) || ''
  }

  if (activityIndex < trace.length - 1) {
    nextActivity = trace[activityIndex + 1]
    nextNodeKey = findNodeKeyByActivity(nextActivity) || ''
  }

  // 计算虚拟节点的位置
  const nodePosition = { x: 300, y: 200 } // 默认位置

  if (prevNodeKey && nextNodeKey) {
    // 如果前后节点都存在，放在中间位置
    const prevNode = diagram.findNodeForKey(prevNodeKey)
    const nextNode = diagram.findNodeForKey(nextNodeKey)

    if (prevNode && nextNode) {
      // 计算中点位置
      nodePosition.x = (prevNode.location.x + nextNode.location.x) / 2
      nodePosition.y = (prevNode.location.y + nextNode.location.y) / 2

      // 如果前后节点在同一水平线上，向下偏移
      if (Math.abs(prevNode.location.y - nextNode.location.y) < 20) {
        nodePosition.y += 80
      }
    }
  } else if (prevNodeKey) {
    // 只有前节点，放在其右下方
    const prevNode = diagram.findNodeForKey(prevNodeKey)
    if (prevNode) {
      nodePosition.x = prevNode.location.x + 200
      nodePosition.y = prevNode.location.y + 80
    }
  } else if (nextNodeKey) {
    // 只有后节点，放在其左下方
    const nextNode = diagram.findNodeForKey(nextNodeKey)
    if (nextNode) {
      nodePosition.x = nextNode.location.x - 200
      nodePosition.y = nextNode.location.y + 80
    }
  }

  // 创建虚拟节点的键
  const virtualNodeKey = `virtual-extra-${activityName}-${Date.now()}`

  // 开始事务
  diagram.startTransaction('add virtual extra node and links')

  // 创建虚拟节点数据
  const virtualNodeData = {
    key: virtualNodeKey,
    name: activityName,
    category: 'Task',
    isVirtual: true,
    loc: `${nodePosition.x} ${nodePosition.y}`,
    deviation: {
      type: 'EXTRA_ACTIVITY',
      severity: 'high'
    }
  }

  // 添加虚拟节点到图表
  diagram.model.addNodeData(virtualNodeData)

  // 设置节点位置
  const virtualNode = diagram.findNodeForKey(virtualNodeKey)
  if (virtualNode) {
    virtualNode.location = new go.Point(nodePosition.x, nodePosition.y)
  }

  // 创建连接线
  const model = diagram.model as go.GraphLinksModel

  // 创建从前一个节点到虚拟节点的连接线
  if (prevNodeKey) {
    const prevLinkKey = `virtual-link-${prevNodeKey}-${virtualNodeKey}`
    const prevLinkData = {
      key: prevLinkKey,
      from: prevNodeKey,
      to: virtualNodeKey,
      isVirtual: true,
      deviation: {
        type: 'EXTRA_ACTIVITY',
        severity: 'high'
      }
    }
    model.addLinkData(prevLinkData)
  }

  // 创建从虚拟节点到后一个节点的连接线
  if (nextNodeKey) {
    const nextLinkKey = `virtual-link-${virtualNodeKey}-${nextNodeKey}`
    const nextLinkData = {
      key: nextLinkKey,
      from: virtualNodeKey,
      to: nextNodeKey,
      isVirtual: true,
      deviation: {
        type: 'EXTRA_ACTIVITY',
        severity: 'high'
      }
    }
    model.addLinkData(nextLinkData)
  } else if (nextActivity) {
    const nextX = virtualNode ? virtualNode.location.x + 200 : nodePosition.x + 200
    const nextY = virtualNode ? virtualNode.location.y : nodePosition.y
    const nextVirtualNodeKey = createVirtualExtraNodeChain(activityIndex + 1, trace, nextX, nextY)

    if (nextVirtualNodeKey) {
      const nextLinkKey = `virtual-link-${virtualNodeKey}-${nextVirtualNodeKey}`
      const nextLinkData = {
        key: nextLinkKey,
        from: virtualNodeKey,
        to: nextVirtualNodeKey,
        isVirtual: true,
        deviation: {
          type: 'EXTRA_ACTIVITY',
          severity: 'high'
        }
      }
      model.addLinkData(nextLinkData)
    }
  }

  diagram.commitTransaction('add virtual extra node and links')

  return virtualNodeKey
}

const ensureExtraActivityChain = (trace: string[], currentIndex: number) => {
  if (!diagram) return

  let startIndex = currentIndex
  while (startIndex > 0) {
    const activity = trace[startIndex - 1]
    const nodeKey = findStandardNodeKeyByActivity(activity)
    if (nodeKey) {
      break
    }
    startIndex--
  }

  let endIndex = currentIndex
  while (endIndex < trace.length - 1) {
    const activity = trace[endIndex + 1]
    const nodeKey = findStandardNodeKeyByActivity(activity)
    if (nodeKey) {
      endIndex++
      break
    }
    endIndex++
  }

  for (let i = startIndex; i <= endIndex; i++) {
    const activity = trace[i]
    const existingNodeKey = findNodeKeyByActivity(activity)

    if (!existingNodeKey) {
      const occurrence = { activity, index: i }
      createVirtualExtraNode(activity, [occurrence], trace)
    }
  }
}

const ensureWrongOrderChain = (trace: string[], fromIndex: number, toIndex: number) => {
  if (!diagram) return

  // 向前扩展，找到第一个在标准流程中的活动
  let startIndex = fromIndex
  while (startIndex > 0) {
    const activity = trace[startIndex - 1]
    const nodeKey = findStandardNodeKeyByActivity(activity)
    if (nodeKey) {
      break
    }
    startIndex--
  }

  // 向后扩展，找到第一个在标准流程中的活动
  let endIndex = toIndex
  while (endIndex < trace.length - 1) {
    const activity = trace[endIndex + 1]
    const nodeKey = findStandardNodeKeyByActivity(activity)
    if (nodeKey) {
      endIndex++
      break
    }
    endIndex++
  }

  for (let i = startIndex; i <= endIndex; i++) {
    const activity = trace[i]
    const existingNodeKey = findNodeKeyByActivity(activity)

    if (!existingNodeKey) {
      const occurrence = { activity, index: i }
      createVirtualExtraNode(activity, [occurrence], trace)
    }
  }
}

// 递归创建虚拟额外活动节点链条
const createVirtualExtraNodeChain = (startActivityIndex: number, trace: string[], startX: number, startY: number): string | null => {
  if (!diagram || startActivityIndex >= trace.length) return null

  const currentActivity = trace[startActivityIndex]

  const existingNodeKey = findStandardNodeKeyByActivity(currentActivity)
  if (existingNodeKey) {
    return existingNodeKey
  }

  // 创建当前虚拟节点
  const virtualNodeKey = `virtual-extra-${currentActivity}-${startActivityIndex}-${Date.now()}`
  const virtualNodeData = {
    key: virtualNodeKey,
    name: currentActivity,
    category: 'Task',
    isVirtual: true,
    deviation: {
      type: 'EXTRA_ACTIVITY',
      severity: 'high'
    }
  }

  // 添加虚拟节点到图表
  const model = diagram.model as go.GraphLinksModel
  model.addNodeData(virtualNodeData)

  // 设置节点位置
  const virtualNode = diagram.findNodeForKey(virtualNodeKey)
  if (virtualNode) {
    virtualNode.location = new go.Point(startX, startY)
  }

  // 递归创建下一个节点
  if (startActivityIndex + 1 < trace.length) {
    const nextNodeKey = createVirtualExtraNodeChain(startActivityIndex + 1, trace, startX + 200, startY)

    if (nextNodeKey && nextNodeKey !== virtualNodeKey) { // 避免自循环
      // 创建连接线到下一个节点
      const linkKey = `virtual-link-${virtualNodeKey}-${nextNodeKey}`
      const linkData = {
        key: linkKey,
        from: virtualNodeKey,
        to: nextNodeKey,
        isVirtual: true,
        deviation: {
          type: 'EXTRA_ACTIVITY',
          severity: 'high'
        }
      }
      model.addLinkData(linkData)
    }
  }

  return virtualNodeKey
}

// 为轨迹对比创建虚拟节点
const createVirtualNodeForTrace = (activityName: string, activityIndex: number, trace: string[]): string | null => {
  if (!diagram) return null

  // 生成稳定的虚拟节点键，避免重复创建
  const virtualNodeKey = `virtual-trace-${activityName.replace(/[^a-zA-Z0-9]/g, '_')}`

  // 检查是否已经存在该虚拟节点
  const existingNode = diagram.findNodeForKey(virtualNodeKey)
  if (existingNode) {
    return virtualNodeKey
  }

  // 找到前后节点用于定位
  let prevNodeKey = ''
  let nextNodeKey = ''

  if (activityIndex > 0) {
    const prevActivity = trace[activityIndex - 1]
    prevNodeKey = findNodeKeyByActivity(prevActivity) || ''
  }

  if (activityIndex < trace.length - 1) {
    const nextActivity = trace[activityIndex + 1]
    nextNodeKey = findNodeKeyByActivity(nextActivity) || ''
  }

  // 计算虚拟节点的位置
  let nodePosition = { x: 300, y: 200 } // 默认位置

  if (prevNodeKey && nextNodeKey) {
    // 如果前后节点都存在，放在中间位置
    const prevNode = diagram.findNodeForKey(prevNodeKey)
    const nextNode = diagram.findNodeForKey(nextNodeKey)

    if (prevNode && nextNode) {
      const prevPos = prevNode.location
      const nextPos = nextNode.location
      nodePosition = {
        x: (prevPos.x + nextPos.x) / 2,
        y: (prevPos.y + nextPos.y) / 2 + 60 // 稍微偏移避免重叠
      }
    }
  } else if (prevNodeKey) {
    // 只有前节点，放在其右侧
    const prevNode = diagram.findNodeForKey(prevNodeKey)
    if (prevNode) {
      const prevPos = prevNode.location
      nodePosition = {
        x: prevPos.x + 150,
        y: prevPos.y + 60
      }
    }
  } else if (nextNodeKey) {
    // 只有后节点，放在其左侧
    const nextNode = diagram.findNodeForKey(nextNodeKey)
    if (nextNode) {
      const nextPos = nextNode.location
      nodePosition = {
        x: nextPos.x - 150,
        y: nextPos.y + 60
      }
    }
  }

  // 创建虚拟节点
  const model = diagram.model as go.GraphLinksModel

  const virtualNodeData = {
    key: virtualNodeKey,
    name: activityName,
    text: activityName,
    category: 'Activity',
    isVirtual: true,
    loc: `${nodePosition.x} ${nodePosition.y}`
  }

  model.addNodeData(virtualNodeData)

  // 创建虚拟连接线（避免重复）
  if (prevNodeKey) {
    const prevLinkKey = `virtual-trace-link-${prevNodeKey}-${virtualNodeKey}`
    const existingPrevLink = diagram.findLinkForKey(prevLinkKey)
    if (!existingPrevLink) {
      const prevLinkData = {
        key: prevLinkKey,
        from: prevNodeKey,
        to: virtualNodeKey,
        isVirtual: true
      }
      model.addLinkData(prevLinkData)
    }
  }

  if (nextNodeKey) {
    const nextLinkKey = `virtual-trace-link-${virtualNodeKey}-${nextNodeKey}`
    const existingNextLink = diagram.findLinkForKey(nextLinkKey)
    if (!existingNextLink) {
      const nextLinkData = {
        key: nextLinkKey,
        from: virtualNodeKey,
        to: nextNodeKey,
        isVirtual: true
      }
      model.addLinkData(nextLinkData)
    }
  }

  return virtualNodeKey
}

const extractBpmnPaths = (): Map<string, Set<string>> => {
  const paths = new Map<string, Set<string>>()

  if (!diagram) {
    return paths
  }

  // 遍历所有连接线，构建活动之间的直接路径关系
  diagram.links.each((link: go.Link) => {
    // 只处理原始的BPMN连接，不包括动态创建的连接
    if (link.data.isVirtual || link.data.isErrorConnection || link.data.isCorrectPath) {
      return
    }

    const fromNode = link.fromNode
    const toNode = link.toNode

    if (fromNode && toNode) {
      const fromActivity = fromNode.data.name || fromNode.data.text
      const toActivity = toNode.data.name || toNode.data.text

      if (fromActivity && toActivity) {
        if (!paths.has(fromActivity)) {
          paths.set(fromActivity, new Set())
        }
        paths.get(fromActivity)!.add(toActivity)
      }
    }
  })

  return paths
}

const findIndirectPath = (
  fromActivity: string,
  toActivity: string,
  modelPaths: Map<string, Set<string>>,
  visited: Set<string>,
  maxDepth: number = 5
): boolean => {
  if (maxDepth <= 0 || visited.has(fromActivity)) {
    return false
  }

  visited.add(fromActivity)

  const directTargets = modelPaths.get(fromActivity)
  if (!directTargets) {
    return false
  }

  // 检查直接连接
  if (directTargets.has(toActivity)) {
    return true
  }

  // 递归检查间接连接
  for (const intermediate of directTargets) {
    if (findIndirectPath(intermediate, toActivity, modelPaths, new Set(visited), maxDepth - 1)) {
      return true
    }
  }

  return false
}

// 检查两个活动之间的连接是否在BPMN模型中有效
const isValidBpmnConnection = (
  fromActivity: string,
  toActivity: string,
  modelPaths: Map<string, Set<string>>
): boolean => {
  // 检查直接连接 - 只有直接连接才算有效
  const directConnection = modelPaths.get(fromActivity)?.has(toActivity)
  if (directConnection) {
    return true
  }

  // 如果两个活动都不在模型中，认为连接无效
  const fromInModel = Array.from(modelPaths.keys()).includes(fromActivity) ||
                     Array.from(modelPaths.values()).some(targets => targets.has(fromActivity))
  const toInModel = Array.from(modelPaths.keys()).includes(toActivity) ||
                   Array.from(modelPaths.values()).some(targets => targets.has(toActivity))

  if (!fromInModel || !toInModel) {
    return false
  }

  // 检查间接路径是否存在
  const hasIndirectPath = findIndirectPath(fromActivity, toActivity, modelPaths, new Set(), 3)
  if (hasIndirectPath) {
    return false
  }

  // 检查是否是反向连接
  const reverseConnection = modelPaths.get(toActivity)?.has(fromActivity)
  const hasReverseIndirectPath = findIndirectPath(toActivity, fromActivity, modelPaths, new Set(), 3)

  if (reverseConnection || hasReverseIndirectPath) {
    return false
  }

  return false
}

const findSkippedActivities = (
  fromActivity: string,
  toActivity: string,
  standardTrace: string[]
): string[] => {
  const fromIndex = standardTrace.indexOf(fromActivity)
  const toIndex = standardTrace.indexOf(toActivity)

  if (fromIndex === -1 || toIndex === -1 || toIndex <= fromIndex + 1) {
    return []
  }

  return standardTrace.slice(fromIndex + 1, toIndex)
}

// 顺序错误偏差处理器
const handleWrongOrder = (
  highlightData: HighlightData,
  caseAnalysis: CaseAnalysis,
  activityOccurrences: ActivityOccurrence[],
  activityName: string,
  targetNodeKey: string
) => {
  const trace = caseAnalysis.trace
  const modelPaths = extractBpmnPaths()
  const standardTrace = highlightData.standardTrace

  // 用于去重的连接集合
  const processedConnections = new Set<string>()

  // 分析每个活动出现的位置
  activityOccurrences.forEach((occurrence) => {
    const activityIndex = occurrence.index

    // 只分析与前一个活动的连接
    if (activityIndex > 0) {
      const prevActivity = trace[activityIndex - 1]
      const connectionKey = `${prevActivity} → ${activityName}`

      if (processedConnections.has(connectionKey)) {
        return
      }

      const prevNodeKey = findNodeKeyByActivity(prevActivity)

      // 检查连接是否违反BPMN模型
      if (!isValidBpmnConnection(prevActivity, activityName, modelPaths)) {
        // 标记连接为已处理
        processedConnections.add(connectionKey)

        // 处理错误连接
        handleInvalidConnection(
          highlightData,
          prevActivity,
          activityName,
          prevNodeKey,
          targetNodeKey,
          standardTrace,
          trace,
          activityIndex - 1
        )
      }
    }
  })

  // 标记当前节点为相关节点
  if (targetNodeKey) {
    highlightData.relatedNodes.add(targetNodeKey)
  }
}

// 处理无效连接的核心逻辑
const handleInvalidConnection = (
  highlightData: HighlightData,
  fromActivity: string,
  toActivity: string,
  fromNodeKey: string | null,
  toNodeKey: string | null,
  standardTrace: string[],
  actualTrace: string[],
  fromIndex: number
) => {
  // 确保整个错误链条的连通性
  ensureWrongOrderChain(actualTrace, fromIndex, fromIndex + 1)

  // 查找已创建的虚拟节点或使用现有节点
  let sourceNodeKey = fromNodeKey
  if (!sourceNodeKey) {
    const possibleVirtualKey = `virtual-extra-${fromActivity}`
    diagram?.nodes.each((node: go.Node) => {
      if (node.data.key && node.data.key.startsWith(possibleVirtualKey)) {
        sourceNodeKey = node.data.key
        return false
      }
    })

    if (sourceNodeKey) {
      highlightData.relatedNodes.add(sourceNodeKey)
      highlightData.deviationNodes.add(sourceNodeKey)
    }
  } else {
    highlightData.relatedNodes.add(sourceNodeKey)
  }

  // 处理目标节点
  let targetNodeKey = toNodeKey
  if (!targetNodeKey) {
    // 查找可能已创建的虚拟节点
    const possibleVirtualKey = `virtual-extra-${toActivity}`
    diagram?.nodes.each((node: go.Node) => {
      if (node.data.key && node.data.key.startsWith(possibleVirtualKey)) {
        targetNodeKey = node.data.key
        return false
      }
    })

    if (targetNodeKey) {
      highlightData.relatedNodes.add(targetNodeKey)
      highlightData.deviationNodes.add(targetNodeKey)
    }
  } else {
    highlightData.relatedNodes.add(targetNodeKey)
  }

  // 检查是否已存在连接，避免重复创建
  if (sourceNodeKey && targetNodeKey) {
    // 检查是否已经存在连接
    let existingLinkKey: string | null = null

    // 查找现有连接
    diagram?.links.each((link: go.Link) => {
      if (link.fromNode?.data.key === sourceNodeKey && link.toNode?.data.key === targetNodeKey) {
        existingLinkKey = link.data.key
        return false
      }
    })

    if (existingLinkKey) {
      // 使用现有连接
      highlightData.relatedLinks.add(existingLinkKey)
      highlightData.deviationLinks.add(existingLinkKey)
    } else {
      // 创建新的错误连接
      const errorLinkKey = createErrorConnection(sourceNodeKey, targetNodeKey, fromActivity, toActivity)
      if (errorLinkKey) {
        highlightData.relatedLinks.add(errorLinkKey)
        highlightData.deviationLinks.add(errorLinkKey)
      }
    }
  }

  // 检查并显示被跳过的活动和应有连接
  const skippedActivities = findSkippedActivities(fromActivity, toActivity, standardTrace)
  if (skippedActivities.length > 0) {
    // 高亮被跳过的活动
    skippedActivities.forEach(skippedActivity => {
      const skippedNodeKey = findNodeKeyByActivity(skippedActivity)
      if (skippedNodeKey) {
        highlightData.relatedNodes.add(skippedNodeKey)
        highlightData.deviationNodes.add(skippedNodeKey)
      }
    })

    // 创建应有连接路径
    if (sourceNodeKey && targetNodeKey) {
      createCorrectPathConnections(
        highlightData,
        fromActivity,
        toActivity,
        skippedActivities,
        sourceNodeKey,
        targetNodeKey
      )
    }
  }
}

// 跳过活动偏差处理器
const handleSkippedActivity = (
  highlightData: HighlightData,
  caseAnalysis: CaseAnalysis,
  activityOccurrences: ActivityOccurrence[],
  activityName: string,
  _targetNodeKey: string
) => {
  // 跳过活动：不高亮被跳过的节点，而是创建跳过连接线
  const standardTrace = highlightData.standardTrace
  const actualTrace = caseAnalysis.trace

  // 分析实际轨迹，找到跳过的连接
  for (let i = 0; i < actualTrace.length - 1; i++) {
    const currentActivity = actualTrace[i]
    const nextActivity = actualTrace[i + 1]

    // 获取这两个活动在标准流程中的位置
    const currentStandardIndex = standardTrace.indexOf(currentActivity)
    const nextStandardIndex = standardTrace.indexOf(nextActivity)

    // 如果两个活动在标准流程中不相邻，说明中间跳过了活动
    if (currentStandardIndex !== -1 && nextStandardIndex !== -1 &&
        nextStandardIndex - currentStandardIndex > 1) {

      // 检查跳过的活动中是否包含当前测试的活动
      const skippedActivities = standardTrace.slice(currentStandardIndex + 1, nextStandardIndex)

      if (skippedActivities.includes(activityName)) {
        // 高亮实际执行的前后节点
        const currentNodeKey = findNodeKeyByActivity(currentActivity)
        const nextNodeKey = findNodeKeyByActivity(nextActivity)

        if (currentNodeKey && nextNodeKey) {
          highlightData.relatedNodes.add(currentNodeKey)
          highlightData.relatedNodes.add(nextNodeKey)

          // 创建跳过连接线：显示实际执行的路径
          const skipLinkKey = createSkipConnection(
            currentNodeKey,
            nextNodeKey,
            currentActivity,
            nextActivity,
            skippedActivities.join(',')
          )

          if (skipLinkKey) {
            highlightData.relatedLinks.add(skipLinkKey)
            highlightData.deviationLinks.add(skipLinkKey)
          } else {
            console.error(`跳过连接线创建失败`)
          }
        } else {
          console.error(`无法找到节点键: ${currentActivity} -> ${currentNodeKey}, ${nextActivity} -> ${nextNodeKey}`)
        }
      }
    }
  }

  // 高亮实际轨迹中的所有相关节点
  actualTrace.forEach(activity => {
    const nodeKey = findNodeKeyByActivity(activity)
    if (nodeKey) {
      highlightData.relatedNodes.add(nodeKey)
    }
  })
}

// 时间违规偏差处理器
const handleTimingViolation = (
  highlightData: HighlightData,
  caseAnalysis: CaseAnalysis,
  activityOccurrences: ActivityOccurrence[],
  activityName: string,
  targetNodeKey: string
) => {
  // 时间违规：节点存在但时间有问题，使用实线显示节点
  highlightData.relatedNodes.add(targetNodeKey)

  // 分析时间相关的连接
  activityOccurrences.forEach((occurrence) => {
    const activityIndex = occurrence.index
    analyzeConnections(highlightData, caseAnalysis.trace, activityIndex, activityName)
  })
}

// 默认偏差处理器
const handleDefaultDeviation = (
  highlightData: HighlightData,
  caseAnalysis: CaseAnalysis,
  activityOccurrences: ActivityOccurrence[],
  activityName: string
) => {
  // 默认处理：分析所有连接关系
  activityOccurrences.forEach((occurrence) => {
    const activityIndex = occurrence.index

    // 检查当前活动是否偏差
    if (!caseAnalysis.isConforming) {
      const isActivityDeviation = !highlightData.standardTrace.includes(activityName) ||
          (caseAnalysis.alignedTrace[activityIndex] && activityName !== caseAnalysis.alignedTrace[activityIndex])

      if (isActivityDeviation) {
        const nodeKey = findNodeKeyByActivity(activityName)
        if (nodeKey) {
          highlightData.deviationNodes.add(nodeKey)
        }
      }
    }

    // 分析前后连接关系
    analyzeConnections(highlightData, caseAnalysis.trace, activityIndex, activityName)
  })
}

// 偏差类型处理器映射
const getDeviationHandler = (deviationType: string) => {
  const handlers = {
    REPEATED_ACTIVITY: handleRepeatedActivity,
    MISSING_ACTIVITY: handleMissingActivity,
    EXTRA_ACTIVITY: handleExtraActivity,
    WRONG_ORDER: handleWrongOrder,
    SKIPPED_ACTIVITY: handleSkippedActivity,
    TIMING_VIOLATION: handleTimingViolation,
  } as const

  return handlers[deviationType as keyof typeof handlers] || null
}

// 分析连接关系
const analyzeConnections = (highlightData: HighlightData, trace: string[], activityIndex: number, activityName: string) => {
  // 分析前向连接
  if (activityIndex > 0) {
    const prevActivity = trace[activityIndex - 1]
    // 使用默认逻辑：检查是否为偏差连接
    addConnectionIfExists(highlightData, prevActivity, activityName)
  }

  // 分析后向连接
  if (activityIndex < trace.length - 1) {
    const nextActivity = trace[activityIndex + 1]
    // 使用默认逻辑：检查是否为偏差连接
    addConnectionIfExists(highlightData, activityName, nextActivity)
  }
}

// 添加连接关系
const addConnectionIfExists = (
  highlightData: HighlightData,
  fromActivity: string,
  toActivity: string,
  forceDeviation: boolean = false
) => {
  const fromNodeKey = findNodeKeyByActivity(fromActivity)
  const toNodeKey = findNodeKeyByActivity(toActivity)

  if (fromNodeKey && toNodeKey) {
    highlightData.relatedNodes.add(fromNodeKey)
    highlightData.relatedNodes.add(toNodeKey)

    const linkKey = findLinkKeyByActivities(fromActivity, toActivity)
    if (linkKey) {
      highlightData.relatedLinks.add(linkKey)

      if (forceDeviation) {
        // 强制标记为偏差连接（虚线）
        highlightData.deviationLinks.add(linkKey)
      } else {
        // 检查连接线是否偏差
        const fromIndex = highlightData.standardTrace.indexOf(fromActivity)
        const toIndex = highlightData.standardTrace.indexOf(toActivity)
        const isLinkDeviation = fromIndex === -1 || toIndex === -1 || Math.abs(toIndex - fromIndex) !== 1

        if (isLinkDeviation) {
          highlightData.deviationLinks.add(linkKey)
        }
      }
    }
  }
}

// 添加偏差连接关系
const addDeviationConnection = (highlightData: HighlightData, fromActivity: string, toActivity: string) => {
  const fromNodeKey = findNodeKeyByActivity(fromActivity)
  const toNodeKey = findNodeKeyByActivity(toActivity)

  if (fromNodeKey && toNodeKey) {
    highlightData.relatedNodes.add(fromNodeKey)
    highlightData.relatedNodes.add(toNodeKey)

    const linkKey = findLinkKeyByActivities(fromActivity, toActivity)
    if (linkKey) {
      highlightData.relatedLinks.add(linkKey)
      highlightData.deviationLinks.add(linkKey)
    }
  }
}

// 应用高亮结果
const applyHighlightResults = (highlightData: HighlightData) => {
  highlightedNodes.value = highlightData.deviationNodes
  highlightedLinks.value = highlightData.deviationLinks
  isHighlightMode.value = true

  applyRelatedHighlight(
    highlightData.relatedNodes,
    highlightData.relatedLinks,
    highlightData.deviationNodes,
    highlightData.deviationLinks
  )
}

// 查找活动对应的节点键
const findNodeKeyByActivity = (activityName: string): string | null => {
  let nodeKey: string | null = null
  diagram?.nodes.each((node: go.Node) => {
    if (node.data.name === activityName) {
      nodeKey = node.data.key
      return false
    }
  })
  return nodeKey
}

// 查找标准流程中活动对应的节点键）
const findStandardNodeKeyByActivity = (activityName: string): string | null => {
  let nodeKey: string | null = null
  diagram?.nodes.each((node: go.Node) => {
    // 只查找非虚拟节点
    if (node.data.name === activityName && !node.data.isVirtual) {
      nodeKey = node.data.key
      return false
    }
  })
  return nodeKey
}

// 查找两个活动之间的连接线键
const findLinkKeyByActivities = (fromActivity: string, toActivity: string): string | null => {
  const fromNodeKey = findNodeKeyByActivity(fromActivity)
  const toNodeKey = findNodeKeyByActivity(toActivity)

  if (!fromNodeKey || !toNodeKey) return null

  let linkKey: string | null = null
  diagram?.links.each((link: go.Link) => {
    if (link.data.from === fromNodeKey && link.data.to === toNodeKey) {
      linkKey = link.data.key || `${link.data.from}-${link.data.to}`
      return false
    }
  })
  return linkKey
}

// 创建回环连接线键并添加到图表
const createLoopConnectionKey = (nodeKey: string): string => {
  const loopLinkKey = `${nodeKey}-loop`

  // 检查是否已存在回环连接线
  let existingLoop = false
  diagram?.links.each((link: go.Link) => {
    if (link.data.key === loopLinkKey) {
      existingLoop = true
      return false
    }
  })

  if (!existingLoop && diagram) {
    // 创建回环连接线数据
    const loopLinkData = {
      key: loopLinkKey,
      from: nodeKey,
      to: nodeKey,
      isLoop: true,
      deviation: {
        type: 'REPEATED_ACTIVITY',
        severity: 'medium'
      }
    }

    // 添加到图表模型
    diagram.startTransaction('add loop link')
    const model = diagram.model as go.GraphLinksModel
    model.addLinkData(loopLinkData)
    diagram.commitTransaction('add loop link')
  }

  return loopLinkKey
}

// 创建错误连接线
const createErrorConnection = (fromNodeKey: string, toNodeKey: string, fromActivity: string, toActivity: string): string | null => {
  if (!diagram) return null

  const errorLinkKey = `error-connection-${fromNodeKey}-${toNodeKey}`

  // 检查连接是否已存在
  const existingLink = diagram.findLinkForKey(errorLinkKey)
  if (existingLink) {
    return errorLinkKey
  }

  // 确保在事务中执行
  diagram.startTransaction('create error connection')

  // 创建错误连接数据
  const linkData = {
    key: errorLinkKey,
    from: fromNodeKey,
    to: toNodeKey,
    isErrorConnection: true,
    fromActivity,
    toActivity,
    category: 'ErrorConnection'
  }

  // 添加到图表
  const model = diagram.model as go.GraphLinksModel
  model.addLinkData(linkData)

  diagram.commitTransaction('create error connection')

  return errorLinkKey
}

// 创建应有连接路径（灰色虚线）
const createCorrectPathConnections = (
  highlightData: HighlightData,
  fromActivity: string,
  toActivity: string,
  skippedActivities: string[],
  sourceNodeKey: string,
  targetNodeKey: string
) => {
  if (!diagram) return

  // 构建完整的正确路径
  const fullPath = [fromActivity, ...skippedActivities, toActivity]
  const fullNodePath = [sourceNodeKey]

  // 获取跳过活动的节点键
  skippedActivities.forEach(activity => {
    const nodeKey = findNodeKeyByActivity(activity)
    if (nodeKey) {
      fullNodePath.push(nodeKey)
    }
  })
  fullNodePath.push(targetNodeKey)

  // 创建路径中每一段的应有连接
  for (let i = 0; i < fullNodePath.length - 1; i++) {
    const fromNodeKey = fullNodePath[i]
    const toNodeKey = fullNodePath[i + 1]
    const fromAct = fullPath[i]
    const toAct = fullPath[i + 1]

    const correctLinkKey = createCorrectPathConnection(fromNodeKey, toNodeKey, fromAct, toAct)
    if (correctLinkKey) {
      highlightData.relatedLinks.add(correctLinkKey)
    }
  }
}

// 创建单个应有连接
const createCorrectPathConnection = (fromNodeKey: string, toNodeKey: string, fromActivity: string, toActivity: string): string | null => {
  if (!diagram) return null

  const correctLinkKey = `correct-path-${fromNodeKey}-${toNodeKey}`

  // 检查连接是否已存在
  const existingLink = diagram.findLinkForKey(correctLinkKey)
  if (existingLink) {
    console.log(`应有连接已存在: ${fromActivity} → ${toActivity}`)
    return correctLinkKey
  }

  // 创建应有连接数据
  const linkData = {
    key: correctLinkKey,
    from: fromNodeKey,
    to: toNodeKey,
    isCorrectPath: true,
    fromActivity,
    toActivity,
    category: 'CorrectPath'
  }

  // 添加到图表
  const model = diagram.model as go.GraphLinksModel
  model.addLinkData(linkData)

  console.log(`创建应有连接: ${fromActivity} → ${toActivity}, key: ${correctLinkKey}`)
  return correctLinkKey
}

// 创建跳过活动连接线并添加到图表
const createSkipConnection = (fromNodeKey: string, toNodeKey: string, fromActivity: string, toActivity: string, skippedActivity: string): string | null => {
  if (!diagram) return null

  const skipLinkKey = `skip-${fromNodeKey}-${toNodeKey}-${skippedActivity}`

  // 检查是否已存在跳过连接线
  let existingSkip = false
  diagram.links.each((link: go.Link) => {
    if (link.data.key === skipLinkKey) {
      existingSkip = true
      return false
    }
  })

  if (!existingSkip) {
    // 创建跳过连接线数据
    const skipLinkData = {
      key: skipLinkKey,
      from: fromNodeKey,
      to: toNodeKey,
      isSkip: true,
      deviation: {
        type: 'SKIPPED_ACTIVITY',
        severity: 'medium'
      },
      skippedActivity: skippedActivity
    }

    // 添加到图表模型
    diagram.startTransaction('add skip link')
    const model = diagram.model as go.GraphLinksModel
    model.addLinkData(skipLinkData)
    diagram.commitTransaction('add skip link')
  }

  return skipLinkKey
}

// 应用相关节点和连接线的高亮效果
const applyRelatedHighlight = (
  relatedNodes: Set<string>,
  relatedLinks: Set<string>,
  deviationNodes: Set<string>,
  deviationLinks: Set<string>
) => {
  if (!diagram) return

  diagram.startTransaction('highlight related elements')

  // 处理节点高亮
  diagram.nodes.each((node: go.Node) => {
    const shape = node.findObject('SHAPE') as go.Shape
    if (shape) {
      if (relatedNodes.has(node.data.key)) {
        // 相关节点保持高亮
        shape.opacity = 1.0
        shape.strokeWidth = 3
        shape.stroke = '#ef4444'

        if (deviationNodes.has(node.data.key)) {
          // 偏差节点：红色边框 + 虚线（表示不存在或有问题）
          shape.strokeDashArray = [5, 5] // 虚线样式
          shape.fill = '#fecaca' // 浅红色填充
        } else {
          // 正常相关节点：红色边框 + 实线
          shape.strokeDashArray = null // 实线
          shape.fill = shape.fill || '#ffffff' // 保持原有填充色
        }
      } else {
        // 弱化其他节点
        shape.opacity = 0.3
      }
    }
  })

  // 处理连接线高亮
  diagram.links.each((link: go.Link) => {
    const linkShape = link.findObject('LINKSHAPE') as go.Shape
    const arrowShape = link.findObject('ARROWSHAPE') as go.Shape
    const linkKey = link.data.key || `${link.data.from}-${link.data.to}`

    if (relatedLinks.has(linkKey)) {
      // 相关连接线保持高亮，统一使用红色
      if (linkShape) {
        linkShape.opacity = 1.0
        linkShape.strokeWidth = 3
        linkShape.stroke = '#ef4444'

        if (deviationLinks.has(linkKey)) {
          // 偏差连接线：红色 + 虚线（表示不存在或有问题的流程）
          linkShape.strokeDashArray = [8, 4] // 虚线样式

          // 如果是回环连接线，使用更粗的线条
          if (link.data.isLoop) {
            linkShape.strokeWidth = 4
          }
        } else {
          // 正常相关连接线：红色 + 实线
          linkShape.strokeDashArray = null // 实线
        }
      }

      if (arrowShape) {
        arrowShape.opacity = 1.0
        arrowShape.stroke = '#ef4444'
        arrowShape.fill = '#ef4444'
      }
    } else {
      // 弱化其他连接线
      if (linkShape) {
        linkShape.opacity = 0.2
      }
      if (arrowShape) {
        arrowShape.opacity = 0.2
      }
    }
  })

  diagram.commitTransaction('highlight related elements')
}

// 高亮显示变体轨迹对比
const highlightVariantTrace = (actualTrace: string[], alignedTrace: string[]) => {
  if (!diagram) return

  // 确保轨迹包含开始和结束节点
  const completeActualTrace = ensureTraceWithStartEnd(actualTrace)
  const completeAlignedTrace = ensureTraceWithStartEnd(alignedTrace)

  // 清除之前的高亮
  clearHighlight()

  diagram.startTransaction('highlight variant trace')
  isHighlightMode.value = true

  // 创建活动集合用于快速查找
  const actualSet = new Set(completeActualTrace)
  const alignedSet = new Set(completeAlignedTrace)

  // 创建虚拟节点用于不存在的额外活动
  const virtualNodes = new Map<string, string>()
  const virtualLinks = new Set<string>() // 存储创建的虚拟连接线

  // 批量检查需要创建虚拟节点的活动，并去重
  const activitiesToCreateVirtual = [...new Set(completeActualTrace)].filter((activityName) => {
    const existingNodeKey = findNodeKeyByActivity(activityName)
    return !existingNodeKey && !alignedSet.has(activityName) && !virtualNodes.has(activityName)
  })

  // 批量创建虚拟节点
  activitiesToCreateVirtual.forEach((activityName) => {
    const actualIndex = completeActualTrace.indexOf(activityName)
    const virtualNodeKey = createVirtualNodeForTrace(activityName, actualIndex, completeActualTrace)
    if (virtualNodeKey) {
      virtualNodes.set(activityName, virtualNodeKey)
    }
  })

  // 创建实际流程的连接线
  for (let i = 0; i < completeActualTrace.length - 1; i++) {
    const currentActivity = completeActualTrace[i]
    const nextActivity = completeActualTrace[i + 1]

    // 获取节点键
    const currentNodeKey = virtualNodes.get(currentActivity) || findNodeKeyByActivity(currentActivity)
    const nextNodeKey = virtualNodes.get(nextActivity) || findNodeKeyByActivity(nextActivity)

    if (currentNodeKey && nextNodeKey) {
      // 检查是否已经存在标准连接线
      const existingLinkKey = findLinkKeyByActivities(currentActivity, nextActivity)

      if (!existingLinkKey) {
        // 不存在标准连接线，需要创建虚拟连接线
        const skipLinkKey = `virtual-skip-${currentNodeKey}-${nextNodeKey}`

        // 检查是否已经存在该虚拟连接线
        const existingVirtualLink = diagram.findLinkForKey(skipLinkKey)
        if (!existingVirtualLink && !virtualLinks.has(skipLinkKey)) {
          const model = diagram.model as go.GraphLinksModel

          const skipLinkData = {
            key: skipLinkKey,
            from: currentNodeKey,
            to: nextNodeKey,
            isVirtual: true,
            isSkip: true
          }

          model.addLinkData(skipLinkData)
          virtualLinks.add(skipLinkKey)
        }
      }
    }
  }

  // 处理节点高亮
  diagram.nodes.each((node: go.Node) => {
    const shape = node.findObject('SHAPE') as go.Shape
    if (shape) {
      const activityName = node.data.text || node.data.name || ''

      if (actualSet.has(activityName) && alignedSet.has(activityName)) {
        // 既在实际轨迹中又在对齐轨迹中
        shape.opacity = 1.0
        shape.strokeWidth = 3
        shape.stroke = '#ea4335'
        shape.fill = '#fecaca'
        shape.strokeDashArray = null
      } else if (actualSet.has(activityName) && !alignedSet.has(activityName)) {
        // 只在实际轨迹中
        shape.opacity = 1.0
        shape.strokeWidth = 3
        shape.stroke = '#ea4335'
        shape.fill = '#fecaca'
        shape.strokeDashArray = [8, 4] // 虚线样式，表示不在标准流程中的节点
      } else if (!actualSet.has(activityName) && alignedSet.has(activityName)) {
        // 只在对齐轨迹中 - 缺失活动
        shape.opacity = 1.0
        shape.strokeWidth = 3
        shape.stroke = '#ea4335'
        shape.fill = '#fecaca'
        shape.strokeDashArray = [5, 5]
      } else {
        shape.opacity = 0.3
        shape.strokeWidth = 2
        shape.stroke = '#9ca3af'
        shape.fill = '#f3f4f6'
        shape.strokeDashArray = null
      }
    }
  })

  // 处理连接线高亮
  diagram.links.each((link: go.Link) => {
    const linkShape = link.findObject('LINKSHAPE') as go.Shape
    const arrowShape = link.findObject('ARROWSHAPE') as go.Shape

    if (linkShape) {
      const fromActivity = link.fromNode?.data?.text || link.fromNode?.data?.name || ''
      const toActivity = link.toNode?.data?.text || link.toNode?.data?.name || ''

      // 检查连接类型
      const isErrorConnection = link.data.isErrorConnection || false
      const isCorrectPath = link.data.isCorrectPath || false
      const isVirtualLink = link.data.isVirtual ||
                           (link.data.key && (link.data.key.includes('virtual-trace-link-') ||
                                             link.data.key.includes('virtual-skip-')))

      // 检查连接是否在轨迹中
      const fromInActual = actualSet.has(fromActivity)
      const toInActual = actualSet.has(toActivity)
      const fromInAligned = alignedSet.has(fromActivity)
      const toInAligned = alignedSet.has(toActivity)

      if (isErrorConnection) {
        // 错误连接 - 实际存在但违反BPMN模型的连接
        linkShape.opacity = 1.0
        linkShape.strokeWidth = 3
        linkShape.stroke = '#dc2626' // 更深的红色表示错误
        linkShape.strokeDashArray = [8, 4]
      } else if (isCorrectPath) {
        // 应有连接 - 标准BPMN模型中的正确路径
        linkShape.opacity = 0.8
        linkShape.strokeWidth = 2
        linkShape.stroke = '#6b7280' // 灰色表示应有路径
        linkShape.strokeDashArray = [6, 6]
      } else if (isVirtualLink && (fromInActual || toInActual)) {
        // 虚拟连接线 - 红色虚线
        linkShape.opacity = 1.0
        linkShape.strokeWidth = 2
        linkShape.stroke = '#ea4335'
        linkShape.strokeDashArray = [5, 5]
      } else if ((fromInActual && toInActual) || (fromInAligned && toInAligned)) {
        // 检查是否连接到不在标准流程中的节点
        const fromNotInStandard = fromInActual && !fromInAligned
        const toNotInStandard = toInActual && !toInAligned

        if (fromNotInStandard || toNotInStandard) {
          // 连接涉及不在标准流程中的节点 - 红色虚线
          linkShape.opacity = 1.0
          linkShape.strokeWidth = 2
          linkShape.stroke = '#ea4335'
          linkShape.strokeDashArray = [8, 4]
        } else {
          // 正常连接在轨迹中 - 红色实线
          linkShape.opacity = 1.0
          linkShape.strokeWidth = 2
          linkShape.stroke = '#ea4335'
          linkShape.strokeDashArray = null
        }
      } else {
        // 连接不在轨迹中 - 弱化显示
        linkShape.opacity = 0.3
        linkShape.strokeWidth = 1
        linkShape.stroke = '#9ca3af'
        linkShape.strokeDashArray = null
      }
    }

    if (arrowShape) {
      const fromActivity = link.fromNode?.data?.text || link.fromNode?.data?.name || ''
      const toActivity = link.toNode?.data?.text || link.toNode?.data?.name || ''

      // 检查连接类型
      const isErrorConnection = link.data.isErrorConnection || false
      const isCorrectPath = link.data.isCorrectPath || false
      const isVirtualLink = link.data.isVirtual ||
                           (link.data.key && (link.data.key.includes('virtual-trace-link-') ||
                                             link.data.key.includes('virtual-skip-')))

      const fromInActual = actualSet.has(fromActivity)
      const toInActual = actualSet.has(toActivity)
      const fromInAligned = alignedSet.has(fromActivity)
      const toInAligned = alignedSet.has(toActivity)

      if (isErrorConnection) {
        // 错误连接的箭头 - 深红色
        arrowShape.opacity = 1.0
        arrowShape.stroke = '#dc2626'
        arrowShape.fill = '#dc2626'
      } else if (isCorrectPath) {
        // 应有连接的箭头 - 灰色
        arrowShape.opacity = 0.8
        arrowShape.stroke = '#6b7280'
        arrowShape.fill = '#6b7280'
      } else if (isVirtualLink && (fromInActual || toInActual)) {
        // 虚拟连接线的箭头 - 红色
        arrowShape.opacity = 1.0
        arrowShape.stroke = '#ea4335'
        arrowShape.fill = '#ea4335'
      } else if ((fromInActual && toInActual) || (fromInAligned && toInAligned)) {
        // 连接在轨迹中的箭头 - 红色
        arrowShape.opacity = 1.0
        arrowShape.stroke = '#ea4335'
        arrowShape.fill = '#ea4335'
      } else {
        // 弱化显示的箭头
        arrowShape.opacity = 0.3
        arrowShape.stroke = '#9ca3af'
        arrowShape.fill = '#9ca3af'
      }
    }
  })

  diagram.commitTransaction('highlight variant trace')

  // 检查是否存在虚拟元素
  checkVirtualElements()
}

// 清除高亮
const clearHighlight = () => {
  if (!diagram) return

  diagram.startTransaction('clear highlight')

  // 移除所有动态创建的回环连接线
  const loopLinksToRemove: go.Link[] = []
  diagram.links.each((link: go.Link) => {
    if (link.data.isLoop && link.data.key && link.data.key.includes('-loop')) {
      loopLinksToRemove.push(link)
    }
  })

  if (loopLinksToRemove.length > 0) {
    loopLinksToRemove.forEach(link => {
      if (diagram) {
        const model = diagram.model as go.GraphLinksModel
        model.removeLinkData(link.data)
      }
    })
  }

  // 移除所有动态创建的连接线
  const dynamicLinksToRemove: go.Link[] = []
  diagram.links.each((link: go.Link) => {
    // 移除虚拟连接线
    if (link.data.isVirtual && link.data.key &&
        (link.data.key.includes('virtual-link-') ||
         link.data.key.includes('virtual-wrong-order-') ||
         link.data.key.includes('virtual-trace-link-') ||
         link.data.key.includes('virtual-skip-'))) {
      dynamicLinksToRemove.push(link)
    }
    // 移除错误连接线（新增）
    if (link.data.isErrorConnection && link.data.key && link.data.key.includes('error-connection-')) {
      dynamicLinksToRemove.push(link)
    }
    // 移除应有连接线（新增）
    if (link.data.isCorrectPath && link.data.key && link.data.key.includes('correct-path-')) {
      dynamicLinksToRemove.push(link)
    }
    // 移除错误顺序连接线
    if (link.data.isWrongOrder && link.data.key && link.data.key.includes('wrong-order-')) {
      dynamicLinksToRemove.push(link)
    }
    // 移除跳过连接线
    if (link.data.isSkip && link.data.key && link.data.key.includes('skip-')) {
      dynamicLinksToRemove.push(link)
    }
  })

  if (dynamicLinksToRemove.length > 0) {
    dynamicLinksToRemove.forEach(link => {
      if (diagram) {
        const model = diagram.model as go.GraphLinksModel
        model.removeLinkData(link.data)
      }
    })
  }

  // 移除所有虚拟节点
  const virtualNodesToRemove: go.Node[] = []
  diagram.nodes.each((node: go.Node) => {
    if (node.data.isVirtual && node.data.key &&
        (node.data.key.includes('virtual-extra-') ||
         node.data.key.includes('virtual-trace-'))) {
      virtualNodesToRemove.push(node)
    }
  })

  if (virtualNodesToRemove.length > 0) {
    virtualNodesToRemove.forEach(node => {
      if (diagram) {
        const model = diagram.model as go.GraphLinksModel
        model.removeNodeData(node.data)
      }
    })
  }

  // 恢复所有节点的原始样式
  diagram.nodes.each((node: go.Node) => {
    const shape = node.findObject('SHAPE') as go.Shape
    if (shape) {
      shape.opacity = 1.0
      shape.strokeDashArray = null

      // 根据节点类型恢复原始样式
      const nodeCategory = node.data.category
      const deviation = node.data.deviation

      if (nodeCategory === 'StartEvent') {
        // 开始节点：绿色填充
        shape.fill = '#10b981'
        shape.stroke = '#059669'
        shape.strokeWidth = 2
      } else if (nodeCategory === 'EndEvent') {
        // 结束节点：红色填充
        shape.fill = '#ef4444'
        shape.stroke = '#dc2626'
        shape.strokeWidth = 3
      } else {
        // 普通活动节点：根据偏差情况恢复填充色
        if (deviation) {
          switch (deviation.severity) {
            case 'high':
              shape.fill = '#fecaca'
              shape.stroke = '#ef4444'
              break
            case 'medium':
              shape.fill = '#fed7aa'
              shape.stroke = '#f59e0b'
              break
            case 'low':
              shape.fill = '#fef3c7'
              shape.stroke = '#eab308'
              break
            default:
              shape.fill = props.theme === 'dark' ? '#374151' : 'white'
              shape.stroke = '#3b82f6'
          }
          shape.strokeWidth = 2
        } else {
          // 无偏差的普通节点：白色或深色填充
          shape.fill = props.theme === 'dark' ? '#374151' : 'white'
          shape.stroke = '#3b82f6'
          shape.strokeWidth = 2
        }
      }
    }
  })

  // 恢复所有连线的原始样式
  diagram.links.each((link: go.Link) => {
    const linkShape = link.findObject('LINKSHAPE') as go.Shape
    const arrowShape = link.findObject('ARROWSHAPE') as go.Shape

    if (linkShape) {
      linkShape.opacity = 1.0
      linkShape.strokeDashArray = null // 清除虚线样式

      // 恢复原始连线样式
      const deviation = link.data.deviation
      if (deviation) {
        switch (deviation.severity) {
          case 'high':
            linkShape.stroke = '#ef4444'
            linkShape.strokeWidth = 3
            break
          case 'medium':
            linkShape.stroke = '#f59e0b'
            linkShape.strokeWidth = 3
            break
          case 'low':
            linkShape.stroke = '#eab308'
            linkShape.strokeWidth = 3
            break
          default:
            linkShape.stroke = props.theme === 'dark' ? '#6b7280' : '#9ca3af'
            linkShape.strokeWidth = 2
        }
      } else {
        linkShape.stroke = props.theme === 'dark' ? '#6b7280' : '#9ca3af'
        linkShape.strokeWidth = 2
      }
    }

    if (arrowShape) {
      arrowShape.opacity = 1.0
      // 恢复原始箭头样式
      const deviation = link.data.deviation
      if (deviation) {
        switch (deviation.severity) {
          case 'high':
            arrowShape.stroke = '#ef4444'
            arrowShape.fill = '#ef4444'
            break
          case 'medium':
            arrowShape.stroke = '#f59e0b'
            arrowShape.fill = '#f59e0b'
            break
          case 'low':
            arrowShape.stroke = '#eab308'
            arrowShape.fill = '#eab308'
            break
          default:
            arrowShape.stroke = props.theme === 'dark' ? '#6b7280' : '#9ca3af'
            arrowShape.fill = props.theme === 'dark' ? '#6b7280' : '#9ca3af'
        }
      } else {
        arrowShape.stroke = props.theme === 'dark' ? '#6b7280' : '#9ca3af'
        arrowShape.fill = props.theme === 'dark' ? '#6b7280' : '#9ca3af'
      }
    }
  })

  diagram.commitTransaction('clear highlight')

  // 重置状态
  highlightedNodes.value.clear()
  highlightedLinks.value.clear()
  isHighlightMode.value = false

  // 重置虚拟元素状态
  hasVirtualElements.value = false
}

// 重置模型
const resetModel = () => {
  if (!diagram) return

  diagram.startTransaction('reset model')

  // 清除高亮状态
  isHighlightMode.value = false
  highlightedNodes.value.clear()
  highlightedLinks.value.clear()

  // 清除虚拟元素选中状态
  selectedVirtualNodes.value.clear()
  selectedVirtualLinks.value.clear()

  // 移除所有动态创建的连接线
  const dynamicLinksToRemove: go.Link[] = []
  diagram.links.each((link: go.Link) => {
    const key = link.data.key || ''
    if (
      link.data.isVirtual ||
      link.data.isErrorConnection ||
      link.data.isCorrectPath ||
      link.data.isWrongOrder ||
      link.data.isSkip ||
      link.data.isLoop ||
      key.includes('virtual-') ||
      key.includes('error-connection-') ||
      key.includes('correct-path-') ||
      key.includes('wrong-order-') ||
      key.includes('skip-') ||
      key.includes('-loop')
    ) {
      dynamicLinksToRemove.push(link)
    }
  })

  // 移除所有虚拟节点
  const virtualNodesToRemove: go.Node[] = []
  diagram.nodes.each((node: go.Node) => {
    const key = node.data.key || ''
    if (
      node.data.isVirtual ||
      key.includes('virtual-extra-') ||
      key.includes('virtual-trace-')
    ) {
      virtualNodesToRemove.push(node)
    }
  })

  // 批量移除动态元素
  const model = diagram.model as go.GraphLinksModel
  if (dynamicLinksToRemove.length > 0) {
    console.log(`移除 ${dynamicLinksToRemove.length} 个动态连接线`)
    dynamicLinksToRemove.forEach(link => {
      model.removeLinkData(link.data)
    })
  }

  if (virtualNodesToRemove.length > 0) {
    console.log(`移除 ${virtualNodesToRemove.length} 个虚拟节点`)
    virtualNodesToRemove.forEach(node => {
      model.removeNodeData(node.data)
    })
  }

  // 恢复所有原始样式
  diagram.nodes.each((node: go.Node) => {
    const shape = node.findObject('SHAPE') as go.Shape
    if (shape) {
      shape.opacity = 1.0
      shape.strokeDashArray = null
      // 恢复默认样式
      const nodeCategory = node.data.category
      if (nodeCategory === 'StartEvent') {
        shape.fill = '#10b981'
        shape.stroke = '#059669'
        shape.strokeWidth = 2
      } else if (nodeCategory === 'EndEvent') {
        shape.fill = '#ef4444'
        shape.stroke = '#dc2626'
        shape.strokeWidth = 3
      } else {
        shape.fill = '#dbeafe'
        shape.stroke = '#3b82f6'
        shape.strokeWidth = 2
      }
    }
  })

  diagram.links.each((link: go.Link) => {
    const linkShape = link.findObject('LINKSHAPE') as go.Shape
    const arrowShape = link.findObject('ARROWSHAPE') as go.Shape
    if (linkShape) {
      linkShape.opacity = 1.0
      linkShape.strokeWidth = 1
      linkShape.stroke = '#9ca3af'
      linkShape.strokeDashArray = null
    }
    if (arrowShape) {
      arrowShape.opacity = 1.0
      arrowShape.stroke = '#9ca3af'
      arrowShape.fill = '#9ca3af'
    }
  })

  diagram.commitTransaction('reset model')
}

const zoomIn = () => {
  if (diagram) {
    diagram.commandHandler.increaseZoom()
  }
}

const zoomOut = () => {
  if (diagram) {
    diagram.commandHandler.decreaseZoom()
  }
}

const resetZoom = () => {
  if (diagram) {
    diagram.zoomToFit()
  }
}

const fitToContent = () => {
  if (diagram) {
    diagram.zoomToFit()
  }
}

const getCurrentZoom = () => {
  if (diagram) {
    return Math.round(diagram.scale * 100)
  }
  return 100
}

defineExpose({
  zoomIn,
  zoomOut,
  resetZoom,
  fitToContent,
  getCurrentZoom,
  highlightDeviationCases,
  highlightVariantTrace,
  clearHighlight,
  resetModel,
  clearVirtualElementSelection,
  selectAllVirtualElements,
  removeVirtualNode,
  removeVirtualLink,
  diagram: computed(() => diagram)
})

onMounted(() => {
  nextTick(() => {
    initDiagram()
    // 初始化后立即加载图表
    if (props.model || props.bpmnXml) {
      loadDiagram()
    } else {
      // 如果没有数据，也要设置加载完成状态
      isLoading.value = false
    }
  })
})

onUnmounted(() => {
  if (diagram) {
    diagram.div = null
  }
})
</script>

<style scoped lang="scss">
@use '~/assets/scss/utils/variables' as *;
@use '~/assets/scss/utils/mixins' as *;

.bpmn-diagram-container {
  position: relative;
  width: 100%;
  height: v-bind(height);
  border: 1px solid theme-color(gray, 200);
  overflow: hidden;
  background-color: white;

  :global(.dark) & {
    border-color: theme-color(gray, 700);
    background-color: theme-color(gray, 800);
  }

  .bpmn-diagram {
    width: 100%;
    height: 100%;
    outline: none;
  }

  .diagram-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    @include center-flex;
    flex-direction: column;
    gap: 1rem;
    background-color: rgba(255, 255, 255, 0.9);
    color: theme-color(gray, 600);
    font-size: font-size(sm);

    :global(.dark) & {
      background-color: rgba(31, 41, 55, 0.9);
      color: theme-color(gray, 400);
    }

    .loading-icon {
      font-size: 2rem;
      animation: spin 1s linear infinite;
    }
  }

  .virtual-elements-hint {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 14px;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 6px;
    font-size: 12px;
    color: #1e40af;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
    backdrop-filter: blur(8px);
    z-index: 1000;

    .hint-icon {
      color: #3b82f6;
      font-size: 14px;
      flex-shrink: 0;
    }

    .hint-text {
      font-weight: 600;
      line-height: 1.3;
      white-space: nowrap;
    }

    &:hover {
      background: rgba(59, 130, 246, 0.15);
      border-color: rgba(59, 130, 246, 0.5);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
    }

    transition: all 0.15s ease;

    :global(.dark) & {
      background: rgba(59, 130, 246, 0.15);
      border-color: rgba(59, 130, 246, 0.4);
      color: #93c5fd;

      &:hover {
        background: rgba(59, 130, 246, 0.2);
        border-color: rgba(59, 130, 246, 0.6);
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
