<template>
  <div class="conformance-rate-section">
    <h4 class="rate-label">符合率</h4>
    <div class="rate-display">
      <div class="rate-bar">
        <div
          class="rate-fill"
          :style="{
            width: `${rate}%`,
            background: getProgressColor(rate)
          }"
        />
      </div>
      <span class="rate-percentage">{{ rate }}%</span>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  rate: number;
}

defineProps<Props>();

// 根据比率获取进度条颜色
const getProgressColor = (rate: number): string => {
  if (rate >= 90) {
    // 优秀：深绿色
    return 'linear-gradient(90deg, #10b981 0%, #059669 100%)';
  } else if (rate >= 80) {
    // 良好：浅绿色
    return 'linear-gradient(90deg, #34d399 0%, #10b981 100%)';
  } else if (rate >= 70) {
    // 一般：黄绿色
    return 'linear-gradient(90deg, #84cc16 0%, #65a30d 100%)';
  } else if (rate >= 60) {
    // 较差：黄色
    return 'linear-gradient(90deg, #eab308 0%, #ca8a04 100%)';
  } else if (rate >= 50) {
    // 差：橙色
    return 'linear-gradient(90deg, #f97316 0%, #ea580c 100%)';
  } else {
    // 很差：红色
    return 'linear-gradient(90deg, #ef4444 0%, #dc2626 100%)';
  }
};
</script>

<style scoped lang="scss">
.conformance-rate-section {
  .rate-label {
    font-size: 13px;
    color: #0a1f44;
    margin: 8px 0px;
    font-weight: 600;
    letter-spacing: 0.3px;
  }

  .rate-display {
    display: flex;
    align-items: center;
    gap: 12px;

    .rate-bar {
      flex: 1;
      height: 12px;
      background: rgba(0, 0, 0, 0.08);
      border-radius: 6px;
      overflow: hidden;

      .rate-fill {
        height: 100%;
        border-radius: 6px;
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
          animation: shimmer 2s infinite;
        }
      }
    }

    .rate-percentage {
      font-size: 13px;
      font-weight: 600;
      color: #374151;
      white-space: nowrap;
      flex-shrink: 0;
      min-width: 40px;
      text-align: right;
    }
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }
}
</style>
