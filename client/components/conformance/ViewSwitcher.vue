<template>
  <div class="view-switcher-panel">
    <div
      class="view-switcher-item"
      :class="{ active: currentView === 'deviations' }"
      title="探索偏差"
      @click="$emit('switch-view', 'deviations')"
    >
      <div class="switcher-icon">
        <el-icon><Connection /></el-icon>
      </div>
      <div class="switcher-label">偏差</div>
    </div>

    <div
      class="view-switcher-item"
      :class="{ active: currentView === 'variants' }"
      title="探索变体"
      @click="$emit('switch-view', 'variants')"
    >
      <div class="switcher-icon">
        <el-icon><Menu /></el-icon>
      </div>
      <div class="switcher-label">变体</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Connection, Menu } from "@element-plus/icons-vue";

interface Props {
  currentView: 'deviations' | 'variants';
}

defineProps<Props>();
defineEmits<{
  'switch-view': [view: 'deviations' | 'variants'];
}>();
</script>

<style scoped lang="scss">
.view-switcher-panel {
  width: 88px;
  background: white;
  border-right: 1px solid #d1d9e0;
  display: flex;
  flex-direction: column;
  padding: 8px 0;
  gap: 4px;
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.05);

  .view-switcher-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px;
    cursor: pointer;
    border-radius: 8px;
    margin: 0 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: center;
    position: relative;
    background: transparent;

    &::before {
      content: "";
      position: absolute;
      left: -12px;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 0;
      background: #1976d2;
      border-radius: 0 2px 2px 0;
      transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    &:hover:not(.disabled):not(.active) {
      background: rgba(25, 118, 210, 0.08);
      transform: translateY(-1px);
    }

    &.active {
      background: #d1e5ff;
      color: #264aff;
      transform: translateY(-1px);

      .switcher-icon {
        color: #264aff;
        transform: scale(1.1);
      }

      .switcher-label {
        color: #264aff;
        font-weight: 600;
      }

      &:hover {
        background: #d1e5ff;
        color: #264aff;
        transform: translateY(-1px);
      }
    }

    &.disabled {
      opacity: 0.4;
      cursor: not-allowed;

      &:hover {
        background: transparent;
        transform: none;
        box-shadow: none;
      }
    }

    .switcher-icon {
      font-size: 22px;
      color: #5f6368;
      margin-bottom: 6px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .switcher-label {
      font-size: 11px;
      color: #5f6368;
      line-height: 1.2;
      font-weight: 500;
      letter-spacing: 0.2px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
}
</style>
