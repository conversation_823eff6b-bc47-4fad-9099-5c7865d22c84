<template>
  <div class="deviations-list-view">
    <!-- 偏差列表标题 -->
    <div class="deviations-header">
      <h3 class="deviations-title">偏差 ({{ totalDeviations }})</h3>
    </div>

    <!-- 搜索框 -->
    <div class="search-section">
      <el-input
        v-model="searchQuery"
        placeholder="搜索"
        :prefix-icon="Search"
        class="search-input"
        clearable
      />
    </div>

    <!-- 偏差列表 -->
    <div class="deviations-list">
      <div
        v-for="deviation in filteredDeviations"
        :key="deviation.id"
        class="deviation-item"
        @click="$emit('select-deviation', deviation)"
      >
        <div class="deviation-header">
          <div class="deviation-info">
            <div class="deviation-type">{{ getDeviationTypeText(deviation.type) }}</div>
            <div class="deviation-title">{{ deviation.activity }}</div>
            <div class="deviation-stats">
              <div class="deviation-count-info">
                共 {{ deviation.count }} 个偏差 ({{ deviation.percentage }}%)
              </div>
            </div>
          </div>
          <el-icon class="deviation-arrow">
            <ArrowRight />
          </el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Search, ArrowRight } from "@element-plus/icons-vue";

interface DeviationItem {
  id: number;
  type: string;
  activity: string;
  count: number;
  percentage: number;
  deviations: any[];
}

interface Props {
  deviations: DeviationItem[];
  totalDeviations: number;
  getDeviationTypeText: (type: string) => string;
}

const props = defineProps<Props>();

defineEmits<{
  'select-deviation': [deviation: DeviationItem];
}>();

const searchQuery = ref("");

const filteredDeviations = computed(() => {
  if (!searchQuery.value) return props.deviations;
  return props.deviations.filter(
    (d: DeviationItem) =>
      d.activity.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      props.getDeviationTypeText(d.type).toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});
</script>

<style scoped lang="scss">
.deviations-list-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.deviations-header {
  flex-shrink: 0;

  .deviations-title {
    font-size: 16px;
    font-weight: 700;
    color: #202124;
    margin: 8px 0;
    letter-spacing: -0.1px;
  }
}

.search-section {
  display: flex;
  gap: 12px;
  flex-shrink: 0;

  .search-input {
    flex: 1;

    :deep(.el-input__wrapper) {
      border-radius: 8px;
      border: 1px solid #dadce0;
      box-shadow: none;
      background: #fafbfc;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        border-color: #1976d2;
        background: white;
      }

      &.is-focus {
        border-color: #1976d2;
        box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
        background: white;
      }

      .el-input__inner {
        font-size: 14px;
        color: #3c4043;

        &::placeholder {
          color: #9aa0a6;
        }
      }
    }
  }
}

.deviations-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
  margin-top: 8px;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #dadce0;
    border-radius: 3px;

    &:hover {
      background: #bdc1c6;
    }
  }

  .deviation-item {
    margin-bottom: 8px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      border-color: #adb5bd;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &.active {
      border-color: #0d6efd;
      box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.25);
    }

    .deviation-header {
      padding: 16px;
      display: flex;
      align-items: flex-start;
      justify-content: space-between;

      .deviation-info {
        flex: 1;

        .deviation-type {
          font-size: 11px;
          color: #6c757d;
          text-transform: uppercase;
          font-weight: 500;
          margin-bottom: 4px;
        }

        .deviation-title {
          font-size: 13px;
          font-weight: 600;
          color: #212529;
          margin-bottom: 8px;
          line-height: 1.3;
        }

        .deviation-stats {
          font-size: 12px;
          color: #6c757d;
          line-height: 1.4;

          .deviation-count-info {
            margin-top: 4px;
            font-size: 11px;
            color: #868e96;
            font-weight: 600;
          }
        }
      }

      .deviation-arrow {
        color: #adb5bd;
        margin-left: 8px;
        flex-shrink: 0;
      }
    }
  }
}
</style>
