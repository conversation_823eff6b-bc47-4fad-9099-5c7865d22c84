<template>
  <Teleport to="body">
    <div
      v-if="visible"
      class="bpmn-context-menu"
      :style="{
        left: x + 'px',
        top: y + 'px'
      }"
      @click.stop
    >
      <!-- 节点操作菜单 -->
      <div v-if="type === 'node'" class="menu-content">
        <div class="menu-item" @click="handleMenuClick('add-bypass')">
          <el-icon><Share /></el-icon>
          <span>添加旁路</span>
        </div>
        <div class="menu-item" @click="handleMenuClick('add-self-loop')">
          <el-icon><RefreshRight /></el-icon>
          <span>添加自循环</span>
        </div>
        <div class="menu-divider" />
        <div class="menu-item danger" @click="handleMenuClick('delete-element')">
          <el-icon><Delete /></el-icon>
          <span>删除节点</span>
        </div>
      </div>

      <!-- 连接线操作菜单 -->
      <div v-else-if="type === 'link'" class="menu-content">
        <div class="menu-item" @click="handleMenuClick('insert-event')">
          <el-icon><CirclePlus /></el-icon>
          <span>插入事件</span>
        </div>
        <div class="menu-item" @click="handleMenuClick('insert-parallel-block')">
          <el-icon><CopyDocument /></el-icon>
          <span>插入平行块</span>
        </div>
        <div class="menu-divider" />
        <div class="menu-item danger" @click="handleMenuClick('delete-connection')">
          <el-icon><Delete /></el-icon>
          <span>删除连接</span>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import {
  Share,
  RefreshRight,
  Delete,
  CirclePlus,
  CopyDocument
} from '@element-plus/icons-vue'

interface Props {
  visible: boolean
  x: number
  y: number
  type: 'node' | 'link' | ''
}

defineProps<Props>()

const emit = defineEmits<{
  'add-bypass': []
  'add-self-loop': []
  'delete-element': []
  'insert-event': []
  'insert-parallel-block': []
  'delete-connection': []
  'close': []
}>()

// 处理菜单项点击事件
const handleMenuClick = (eventName: string) => {
  // 触发对应的事件
  emit(eventName as any)
  // 关闭菜单
  emit('close')
}
</script>

<style scoped lang="scss">
@use '~/assets/scss/utils/variables' as *;
@use '~/assets/scss/utils/mixins' as *;

.bpmn-context-menu {
  position: fixed;
  background: white;
  border: 1px solid theme-color(gray, 200);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  min-width: 160px;
  padding: 4px 0;
  backdrop-filter: blur(8px);

  :global(.dark) & {
    background: theme-color(gray, 800);
    border-color: theme-color(gray, 600);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}

.menu-content {
  display: flex;
  flex-direction: column;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  color: theme-color(gray, 700);
  transition: all 0.2s ease;
  user-select: none;

  :global(.dark) & {
    color: theme-color(gray, 300);
  }

  &:hover {
    background-color: theme-color(gray, 50);
    color: theme-color(primary, 600);

    :global(.dark) & {
      background-color: theme-color(gray, 700);
      color: theme-color(primary, 400);
    }
  }

  &.danger {
    color: theme-color(danger, 600);

    :global(.dark) & {
      color: theme-color(danger, 400);
    }

    &:hover {
      background-color: theme-color(danger, 50);
      color: theme-color(danger, 700);

      :global(.dark) & {
        background-color: theme-color(danger, 900);
        color: theme-color(danger, 300);
      }
    }
  }

  .el-icon {
    font-size: 16px;
    flex-shrink: 0;
  }

  span {
    flex: 1;
    white-space: nowrap;
  }
}

.menu-divider {
  height: 1px;
  background-color: theme-color(gray, 200);
  margin: 4px 0;

  :global(.dark) & {
    background-color: theme-color(gray, 600);
  }
}

// 动画效果
.bpmn-context-menu {
  animation: contextMenuFadeIn 0.15s ease-out;
}

@keyframes contextMenuFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-4px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
