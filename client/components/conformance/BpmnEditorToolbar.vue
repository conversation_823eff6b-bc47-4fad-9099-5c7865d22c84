<template>
  <div class="toolbar">
    <el-card class="toolbar-card">
      <div class="toolbar-content">
        <!-- 左侧工具组 -->
        <div class="toolbar-left">
          <!-- 编辑模式切换 -->
          <el-button-group class="edit-tools">
            <el-button
              :type="editMode === 'visual' ? 'primary' : 'default'"
              size="small"
              @click="$emit('switch-mode', 'visual')"
            >
              <el-icon><Edit /></el-icon>
              可视化编辑
            </el-button>
            <el-button
              :type="editMode === 'xml' ? 'primary' : 'default'"
              size="small"
              @click="$emit('switch-mode', 'xml')"
            >
              <el-icon><Document /></el-icon>
              XML编辑
            </el-button>
          </el-button-group>

          <el-divider direction="vertical" />

          <!-- 撤销/重做控制 -->
          <el-button-group class="undo-redo-controls">
            <el-button
              :icon="RefreshLeft"
              size="small"
              title="撤销 (Ctrl+Z)"
              :disabled="!canUndo"
              @click="$emit('undo')"
            />
            <el-button
              :icon="RefreshRight"
              size="small"
              title="重做 (Ctrl+Y)"
              :disabled="!canRedo"
              @click="$emit('redo')"
            />
          </el-button-group>

          <el-divider direction="vertical" />

          <!-- 缩放控制 -->
          <el-button-group class="zoom-controls">
            <el-button
              :icon="ZoomIn"
              size="small"
              title="放大"
              @click="$emit('zoom-in')"
            />
            <el-button
              :icon="ZoomOut"
              size="small"
              title="缩小"
              @click="$emit('zoom-out')"
            />
            <el-button
              :icon="Refresh"
              size="small"
              title="重置缩放"
              @click="$emit('reset-zoom')"
            />
          </el-button-group>

          <div class="zoom-percentage">{{ zoomLevel }}%</div>
          
          <el-divider direction="vertical" />
          
          <!-- 额外工具 -->
          <el-button-group class="extra-tools">
            <el-button 
              size="small" 
              title="导出BPMN"
              @click="$emit('export-bpmn')"
            >
              <el-icon><Download /></el-icon>
              导出
            </el-button>
            <el-button 
              size="small" 
              title="验证模型"
              @click="$emit('validate-model')"
            >
              <el-icon><CircleCheck /></el-icon>
              验证
            </el-button>
          </el-button-group>
        </div>

        <!-- 右侧操作组 -->
        <div class="toolbar-right">
          <el-button
            type="success"
            size="small"
            :loading="isSaving"
            :disabled="!canSave"
            @click="$emit('save')"
          >
            <el-icon><Check /></el-icon>
            保存并继续
          </el-button>
          <el-button 
            size="small" 
            @click="$emit('cancel')"
          >
            取消
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
  Edit,
  Document,
  ZoomIn,
  ZoomOut,
  Refresh,
  RefreshLeft,
  RefreshRight,
  Download,
  CircleCheck,
  Check
} from '@element-plus/icons-vue'

interface Props {
  editMode: 'visual' | 'xml'
  zoomLevel: number
  isSaving: boolean
  canSave: boolean
  canUndo: boolean
  canRedo: boolean
}

defineProps<Props>()

defineEmits<{
  'switch-mode': [mode: 'visual' | 'xml']
  'undo': []
  'redo': []
  'zoom-in': []
  'zoom-out': []
  'reset-zoom': []
  'export-bpmn': []
  'validate-model': []
  'save': []
  'cancel': []
}>()
</script>

<style scoped lang="scss">
@use '~/assets/scss/utils/variables' as *;
@use '~/assets/scss/utils/mixins' as *;

.toolbar {
  margin-bottom: 1rem;
}

.toolbar-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  :global(.dark) & {
    background-color: theme-color(gray, 800);
    border-color: theme-color(gray, 700);
  }

  :deep(.el-card__body) {
    padding: 12px 16px;
  }
}

.toolbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.edit-tools,
.undo-redo-controls,
.zoom-controls,
.extra-tools {
  .el-button {
    border-radius: 4px;
  }
}

.zoom-percentage {
  font-size: 12px;
  color: theme-color(gray, 600);
  font-weight: 500;
  min-width: 40px;
  text-align: center;

  :global(.dark) & {
    color: theme-color(gray, 400);
  }
}

.el-divider--vertical {
  height: 20px;
  margin: 0 8px;
}

// 响应式设计
@media (max-width: 768px) {
  .toolbar-content {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .toolbar-left {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }
  
  .zoom-percentage {
    order: -1;
  }
}
</style>
