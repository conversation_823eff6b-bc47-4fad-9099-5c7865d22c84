<template>
  <div class="page-header">
    <div class="header-left">
      <el-button
        :icon="ArrowLeft"
        link
        @click="$emit('back')"
      />
      <h1 class="process-title">{{ processTitle }}</h1>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft } from "@element-plus/icons-vue";

interface Props {
  processTitle: string;
}

defineProps<Props>();
defineEmits<{
  back: [];
}>();
</script>

<style scoped lang="scss">
.page-header {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .process-title {
      font-size: 18px;
      font-weight: 600;
      color: #212529;
      margin: 0;
      line-height: 1.2;
    }
  }
}
</style>
