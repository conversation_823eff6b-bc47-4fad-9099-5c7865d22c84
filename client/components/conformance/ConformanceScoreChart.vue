<template>
  <div ref="chartContainer" class="conformance-score-chart">
    <div ref="chartRef" class="chart-content"/>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import type { ConformanceResult } from '~/types'

interface TooltipFormatterParams {
  componentType: string
  seriesType: string
  seriesName: string
  name: string
  value: number[]
  data: {
    name: string
    value: number[]
  }
  dataIndex: number
  seriesIndex: number
  color: string
  marker: string
}

interface Props {
  data: ConformanceResult
  height?: string
  theme?: 'light' | 'dark'
}

const props = withDefaults(defineProps<Props>(), {
  height: '300px',
  theme: 'light'
})

const chartContainer = ref<HTMLElement>()
const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 指标配置
const indicators = [
  { name: '符合性\n(Conformance)', key: 'conformanceScore', color: '#3b82f6' },
  { name: '适应性\n(Fitness)', key: 'fitnessScore', color: '#10b981' },
  { name: '精确性\n(Precision)', key: 'precisionScore', color: '#f59e0b' },
  { name: '泛化性\n(Generalization)', key: 'generalizationScore', color: '#ef4444' },
  { name: '简洁性\n(Simplicity)', key: 'simplicityScore', color: '#8b5cf6' }
] as const

// 安全地获取数值
const safeGetValue = (value: string | number | undefined | null): number => {
  if (typeof value === 'string') {
    const parsed = parseFloat(value)
    return isNaN(parsed) ? 0 : parsed
  }
  return typeof value === 'number' && !isNaN(value) ? value : 0
}

// 监听主题变化
watch(() => props.theme, () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
    initChart()
  }
})

// 监听数据变化
watch(() => props.data, () => {
  if (chartInstance) {
    updateChart()
  }
}, { deep: true })

const initChart = () => {
  if (!chartRef.value) return

  try {
    chartInstance = echarts.init(chartRef.value, props.theme)
    updateChart()
  } catch (error) {
    console.error('Failed to initialize chart:', error)
  }
}

const updateChart = () => {
  if (!chartInstance || !props.data) return

  try {
    const option = {
      title: {
        text: '符合性指标雷达图',
        left: 'center',
        textStyle: {
          color: props.theme === 'dark' ? '#e5e7eb' : '#374151',
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'transparent',
        textStyle: {
          color: '#fff',
          fontSize: 12
        },
        formatter: (params: TooltipFormatterParams) => {
          if (params.componentType === 'series' && params.seriesType === 'radar') {
            const indicatorNames = [
              '符合性(Conformance)',
              '适应性(Fitness)',
              '精确性(Precision)',
              '泛化性(Generalization)',
              '简洁性(Simplicity)'
            ]

            let result = `<div style="text-align: left; min-width: 180px;">`
            result += `<div style="margin-bottom: 8px; font-weight: bold;">${params.seriesName}</div>`

            params.value.forEach((value: number, index: number) => {
              const numValue = safeGetValue(value)
              const percentage = Math.round(numValue * 100)
              const color = indicators[index]?.color || '#3b82f6'

              result += `<div style="margin: 4px 0; display: flex; align-items: center;">
                <span style="display: inline-block; width: 8px; height: 8px; background-color: ${color}; border-radius: 50%; margin-right: 8px;"></span>
                <span>${indicatorNames[index]}: <strong>${percentage}%</strong></span>
              </div>`
            })

            result += `</div>`
            return result
          }
          return ''
        }
      },
      radar: {
        indicator: indicators.map(indicator => ({
          name: indicator.name,
          max: 1
        })),
        center: ['50%', '55%'],
        radius: '65%',
        axisName: {
          color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
          fontSize: 12
        },
        splitArea: {
          areaStyle: {
            color: props.theme === 'dark'
              ? ['rgba(59, 130, 246, 0.1)', 'rgba(59, 130, 246, 0.05)']
              : ['rgba(59, 130, 246, 0.1)', 'rgba(59, 130, 246, 0.05)']
          }
        },
        axisLine: {
          lineStyle: {
            color: props.theme === 'dark' ? '#4b5563' : '#d1d5db'
          }
        },
        splitLine: {
          lineStyle: {
            color: props.theme === 'dark' ? '#4b5563' : '#d1d5db'
          }
        }
      },
      series: [
        {
          name: '符合性指标',
          type: 'radar',
          data: [
            {
              value: [
                safeGetValue(props.data.conformanceScore),
                safeGetValue(props.data.fitnessScore),
                safeGetValue(props.data.precisionScore),
                safeGetValue(props.data.generalizationScore),
                safeGetValue(props.data.simplicityScore)
              ],
              name: '当前结果',
              areaStyle: {
                color: 'rgba(59, 130, 246, 0.2)'
              },
              lineStyle: {
                color: '#3b82f6',
                width: 2
              },
              itemStyle: {
                color: '#3b82f6',
                borderColor: '#ffffff',
                borderWidth: 2
              }
            }
          ]
        }
      ]
    }

    chartInstance.setOption(option, true)
  } catch (error) {
    console.error('Failed to update chart:', error)
  }
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })

  // 监听窗口大小变化
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }

  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped lang="scss">
.conformance-score-chart {
  width: 100%;
  height: v-bind(height);
  min-height: 300px;
  position: relative;

  .chart-content {
    width: 100%;
    height: 100%;
  }
}
</style>
