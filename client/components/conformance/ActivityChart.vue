<template>
  <div ref="chartRef" class="activity-chart"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import type { ConformanceActivityAnalysis } from '~/types'

interface Props {
  activities: ConformanceActivityAnalysis[]
  height?: string
  theme?: 'light' | 'dark'
}

const props = withDefaults(defineProps<Props>(), {
  height: '400px',
  theme: 'light'
})

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 监听主题变化
watch(() => props.theme, (newTheme) => {
  if (chartInstance) {
    chartInstance.dispose()
    initChart()
  }
})

// 监听数据变化
watch(() => props.activities, () => {
  updateChart()
}, { deep: true })

const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value, props.theme)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance || !props.activities || props.activities.length === 0) return

  // 按符合率排序
  const sortedActivities = [...props.activities].sort((a, b) => a.conformanceRate - b.conformanceRate)
  
  const activityNames = sortedActivities.map(activity => activity.activity)
  const conformanceRates = sortedActivities.map(activity => (activity.conformanceRate * 100).toFixed(1))
  const frequencies = sortedActivities.map(activity => activity.frequency)

  const option = {
    title: {
      text: '活动符合性分析',
      left: 'center',
      textStyle: {
        color: props.theme === 'dark' ? '#e5e7eb' : '#374151',
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const activity = params[0]
        const frequency = params[1]
        return `
          活动: ${activity.name}<br/>
          符合率: ${activity.value}%<br/>
          频次: ${frequency.value}
        `
      }
    },
    legend: {
      data: ['符合率', '频次'],
      top: '8%',
      textStyle: {
        color: props.theme === 'dark' ? '#9ca3af' : '#6b7280'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: activityNames,
      axisLabel: {
        color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
        interval: 0,
        rotate: 45,
        fontSize: 11
      },
      axisLine: {
        lineStyle: {
          color: props.theme === 'dark' ? '#4b5563' : '#d1d5db'
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '符合率 (%)',
        position: 'left',
        nameTextStyle: {
          color: props.theme === 'dark' ? '#9ca3af' : '#6b7280'
        },
        axisLabel: {
          color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
          formatter: '{value}%'
        },
        axisLine: {
          lineStyle: {
            color: props.theme === 'dark' ? '#4b5563' : '#d1d5db'
          }
        },
        splitLine: {
          lineStyle: {
            color: props.theme === 'dark' ? '#374151' : '#f3f4f6'
          }
        },
        min: 0,
        max: 100
      },
      {
        type: 'value',
        name: '频次',
        position: 'right',
        nameTextStyle: {
          color: props.theme === 'dark' ? '#9ca3af' : '#6b7280'
        },
        axisLabel: {
          color: props.theme === 'dark' ? '#9ca3af' : '#6b7280'
        },
        axisLine: {
          lineStyle: {
            color: props.theme === 'dark' ? '#4b5563' : '#d1d5db'
          }
        }
      }
    ],
    series: [
      {
        name: '符合率',
        type: 'bar',
        yAxisIndex: 0,
        data: conformanceRates.map((rate, index) => ({
          value: rate,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { 
                offset: 0, 
                color: parseFloat(rate) >= 80 ? '#10b981' : parseFloat(rate) >= 60 ? '#f59e0b' : '#ef4444'
              },
              { 
                offset: 1, 
                color: parseFloat(rate) >= 80 ? '#059669' : parseFloat(rate) >= 60 ? '#d97706' : '#dc2626'
              }
            ])
          }
        })),
        barWidth: '40%'
      },
      {
        name: '频次',
        type: 'line',
        yAxisIndex: 1,
        data: frequencies,
        lineStyle: {
          color: '#3b82f6',
          width: 2
        },
        itemStyle: {
          color: '#3b82f6',
          borderColor: '#ffffff',
          borderWidth: 2
        },
        symbol: 'circle',
        symbolSize: 6
      }
    ]
  }

  chartInstance.setOption(option, true)
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })

  // 监听窗口大小变化
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped lang="scss">
.activity-chart {
  width: 100%;
  height: v-bind(height);
  min-height: 400px;
}
</style>
