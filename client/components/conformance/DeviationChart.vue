<template>
  <div ref="chartRef" class="deviation-chart"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import type { ConformanceDeviation, DeviationType } from '~/types'

interface Props {
  deviations: ConformanceDeviation[]
  height?: string
  theme?: 'light' | 'dark'
}

const props = withDefaults(defineProps<Props>(), {
  height: '400px',
  theme: 'light'
})

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 监听主题变化
watch(() => props.theme, (newTheme) => {
  if (chartInstance) {
    chartInstance.dispose()
    initChart()
  }
})

// 监听数据变化
watch(() => props.deviations, () => {
  updateChart()
}, { deep: true })

const getDeviationText = (deviation: DeviationType): string => {
  const texts = {
    missing_activity: '缺失活动',
    extra_activity: '额外活动',
    wrong_order: '顺序错误',
    skipped_activity: '跳过活动',
    repeated_activity: '重复活动',
    timing_violation: '时间违规'
  }
  return texts[deviation] || deviation
}

const getSeverityColor = (severity: string): string => {
  const colors = {
    low: '#10b981',
    medium: '#f59e0b',
    high: '#ef4444'
  }
  return colors[severity as keyof typeof colors] || '#6b7280'
}

const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value, props.theme)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance || !props.deviations) return

  // 统计偏差类型
  const deviationStats = props.deviations.reduce((acc, deviation) => {
    const type = deviation.type
    if (!acc[type]) {
      acc[type] = { low: 0, medium: 0, high: 0, total: 0 }
    }
    acc[type][deviation.severity]++
    acc[type].total++
    return acc
  }, {} as Record<string, { low: number, medium: number, high: number, total: number }>)

  const categories = Object.keys(deviationStats).map(getDeviationText)
  const lowData = Object.values(deviationStats).map(stat => stat.low)
  const mediumData = Object.values(deviationStats).map(stat => stat.medium)
  const highData = Object.values(deviationStats).map(stat => stat.high)

  const option = {
    title: {
      text: '偏差类型分布',
      left: 'center',
      textStyle: {
        color: props.theme === 'dark' ? '#e5e7eb' : '#374151',
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        let result = `${params[0].name}<br/>`
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['低严重性', '中等严重性', '高严重性'],
      top: '8%',
      textStyle: {
        color: props.theme === 'dark' ? '#9ca3af' : '#6b7280'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLabel: {
        color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
        interval: 0,
        rotate: 45,
        fontSize: 11
      },
      axisLine: {
        lineStyle: {
          color: props.theme === 'dark' ? '#4b5563' : '#d1d5db'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '偏差数量',
      nameTextStyle: {
        color: props.theme === 'dark' ? '#9ca3af' : '#6b7280'
      },
      axisLabel: {
        color: props.theme === 'dark' ? '#9ca3af' : '#6b7280'
      },
      axisLine: {
        lineStyle: {
          color: props.theme === 'dark' ? '#4b5563' : '#d1d5db'
        }
      },
      splitLine: {
        lineStyle: {
          color: props.theme === 'dark' ? '#374151' : '#f3f4f6'
        }
      }
    },
    series: [
      {
        name: '低严重性',
        type: 'bar',
        stack: 'severity',
        data: lowData,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#34d399' },
            { offset: 1, color: '#10b981' }
          ])
        }
      },
      {
        name: '中等严重性',
        type: 'bar',
        stack: 'severity',
        data: mediumData,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#fbbf24' },
            { offset: 1, color: '#f59e0b' }
          ])
        }
      },
      {
        name: '高严重性',
        type: 'bar',
        stack: 'severity',
        data: highData,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#f87171' },
            { offset: 1, color: '#ef4444' }
          ])
        }
      }
    ]
  }

  chartInstance.setOption(option, true)
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })

  // 监听窗口大小变化
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped lang="scss">
.deviation-chart {
  width: 100%;
  height: v-bind(height);
  min-height: 400px;
}
</style>
