<template>
  <div class="variants-explorer">
    <div class="explorer-tip">
      <el-icon class="tip-icon"><InfoFilled /></el-icon>
      <span class="tip-text">探索包含偏离目标过程模型的不同情况。</span>
    </div>

    <!-- 标题区域 -->
    <div class="explorer-header">
      <span class="stat-item">偏差变体: {{ deviationVariantsCount }}</span>
      <span class="stat-item">总案例数: {{ totalCases }}</span>
    </div>

    <!-- 变体列表 -->
    <div class="variants-container">
      <div class="variants-header">
        <span class="header-label">案例数量</span>
        <span class="header-label">
          覆盖率
          <el-tooltip
            content="该偏差变体的案例数量占所有偏差变体案例数量的百分比"
            placement="top"
            effect="dark"
          >
            <el-icon class="header-tooltip-icon"><QuestionFilled /></el-icon>
          </el-tooltip>
        </span>
      </div>

      <div class="variants-list">
        <template
          v-for="(variant, index) in visibleVariants"
          :key="`variant-${variant?.id || index}`"
        >
          <div
            v-if="variant"
            class="variant-row"
            :class="{
              active: safeSelectedVariant?.id === variant.id,
              'has-deviation': !variant.isConforming,
            }"
            @click="$emit('select-variant', variant)"
          >
            <div class="variant-cases">
              <span class="cases-count">{{ variant?.cases || 0 }}</span>
            </div>
            <div class="variant-coverage">
              <div class="coverage-bar">
                <div
                  class="coverage-fill"
                  :style="{ width: `${variant?.deviationCoverage || 0}%` }"
                />
              </div>
              <span class="coverage-text"
                >{{ formatCoverage(variant?.deviationCoverage || 0) }}%</span
              >
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 实际轨迹列表 -->
    <div
      v-if="safeSelectedVariant"
      class="trace-list-section"
    >
      <div class="trace-header">
        <span class="trace-title">实际轨迹</span>
      </div>

      <div class="trace-steps">
        <div
          v-for="(activity, index) in safeSelectedVariant.trace"
          :key="`activity-${index}`"
          class="trace-step"
          :class="{
            'has-deviation': hasActivityDeviation(activity),
            'clickable': hasActivityDeviation(activity)
          }"
          @click="handleActivityClick(activity)"
        >
          <div class="step-name">{{ activity }}</div>
          <div class="step-tags">
            <span
              v-for="deviation in getActivityDeviations(activity)"
              :key="deviation.id"
              class="deviation-tag"
              :class="getDeviationTagClass(deviation.type)"
            >
              {{ getDeviationTypeText(deviation.type) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { InfoFilled, QuestionFilled } from "@element-plus/icons-vue";
import type { ProcessVariant } from "~/composables/useConformanceResult";

interface Props {
  variants: ProcessVariant[];
  totalVariants: number;
  displayedVariants: number;
  selectedVariant: ProcessVariant | null;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  "select-variant": [variant: ProcessVariant];
  "case-selected": [caseId: string, variant: ProcessVariant];
  "activity-selected": [activityName: string, caseIds: string[], deviationTypes: string[]];
}>();

const safeVariants = computed(() => {
  if (!props.variants || !Array.isArray(props.variants)) {
    return [];
  }
  return props.variants;
});

const safeSelectedVariant = computed(() => {
  if (!props.selectedVariant) return null;
  if (typeof props.selectedVariant !== "object") {
    console.warn("VariantsList: selectedVariant is not a valid object");
    return null;
  }
  return props.selectedVariant;
});

// 计算偏差变体的覆盖率
const calculateDeviationCoverage = (variantCases: number, totalDeviationCases: number): number => {
  if (totalDeviationCases === 0) return 0;
  return Math.round((variantCases / totalDeviationCases) * 100 * 100) / 100;
};

const visibleVariants = computed(() => {
  const variants = safeVariants.value;
  if (variants.length === 0) {
    return [];
  }

  // 过滤掉 null/undefined 的变体，并确保基本属性存在，同时只显示有偏差的变体
  const deviationVariants = variants.filter((variant) => {
    return (
      variant != null &&
      typeof variant === "object" &&
      variant.id !== undefined &&
      variant.id !== null &&
      !variant.isConforming
    );
  });

  // 计算偏差变体的总案例数
  const totalDeviationCases = deviationVariants.reduce((total, variant) => {
    return total + (variant.cases || 0);
  }, 0);

  // 为每个偏差变体计算覆盖率
  const variantsWithCoverage = deviationVariants.map((variant) => ({
    ...variant,
    deviationCoverage: calculateDeviationCoverage(variant.cases || 0, totalDeviationCases)
  }));

  const displayCount = props.displayedVariants || variantsWithCoverage.length;
  return variantsWithCoverage.slice(0, displayCount);
});

// 格式化覆盖率显示
const formatCoverage = (coverage: number | undefined) => {
  if (coverage === undefined || coverage === null || isNaN(coverage)) {
    return "0.00";
  }
  return Number(coverage).toFixed(2);
};

// 获取特定活动的偏差信息
const getActivityDeviations = (activityName: string) => {
  if (!safeSelectedVariant.value?.deviations) {
    return [];
  }

  return safeSelectedVariant.value.deviations.filter(deviation =>
    deviation.activity === activityName
  );
};

// 获取偏差类型的显示文本
const getDeviationTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    MISSING_ACTIVITY: "缺失",
    EXTRA_ACTIVITY: "额外",
    WRONG_ORDER: "顺序错误",
    SKIPPED_ACTIVITY: "跳过",
    REPEATED_ACTIVITY: "重复",
    TIMING_VIOLATION: "时间违规",
  };
  return typeMap[type.toUpperCase()] || type;
};

// 获取偏差标签的CSS类
const getDeviationTagClass = (type: string) => {
  const classMap: Record<string, string> = {
    MISSING_ACTIVITY: "tag-missing",
    EXTRA_ACTIVITY: "tag-extra",
    WRONG_ORDER: "tag-wrong-order",
    SKIPPED_ACTIVITY: "tag-skipped",
    REPEATED_ACTIVITY: "tag-repeated",
    TIMING_VIOLATION: "tag-timing",
  };
  return classMap[type.toUpperCase()] || "tag-default";
};

// 检查活动是否有偏差
const hasActivityDeviation = (activityName: string) => {
  if (!safeSelectedVariant.value?.deviations) {
    return false;
  }

  return safeSelectedVariant.value.deviations.some(deviation =>
    deviation.activity === activityName
  );
};

// 处理活动点击事件
const handleActivityClick = (activityName: string) => {
  if (!hasActivityDeviation(activityName) || !safeSelectedVariant.value) {
    return;
  }

  // 获取该活动的偏差信息
  const activityDeviations = getActivityDeviations(activityName);
  if (activityDeviations.length === 0) {
    return;
  }

  // 生成案例ID列表（基于变体的案例数量）
  const caseIds = safeSelectedVariant.value.caseIds ||
    Array.from({ length: safeSelectedVariant.value.cases }, (_, i) =>
      `case-${safeSelectedVariant.value!.id}-${i + 1}`
    );

  // 传递所有偏差类型，让父组件处理多偏差情况
  const deviationTypes = activityDeviations.map(d => d.type);
  emit('activity-selected', activityName, caseIds, deviationTypes);
};

// 计算属性：获取有偏差的变体数量
const deviationVariantsCount = computed(() => {
  if (!props.variants || !Array.isArray(props.variants)) return 0;
  return props.variants.filter((variant) => variant && !variant.isConforming)
    .length;
});

// 计算属性：获取总案例数
const totalCases = computed(() => {
  if (!props.variants || !Array.isArray(props.variants)) return 0;
  return props.variants.reduce((sum, variant) => {
    const cases = variant?.cases || 0;
    return sum + (isNaN(cases) ? 0 : cases);
  }, 0);
});
</script>

<style scoped lang="scss">
.variants-explorer {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .explorer-tip {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 12px;
    padding: 10px 14px;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    border: 1px solid #e1e8ff;
    border-radius: 8px;
    font-size: 13px;
    color: #4a5568;
    line-height: 1.4;

    .tip-icon {
      color: #4285f4;
      font-size: 16px;
      flex-shrink: 0;
    }

    .tip-text {
      font-weight: 500;
      letter-spacing: 0.2px;
    }

    &:hover {
      background: linear-gradient(135deg, #f0f4ff 0%, #e8f0ff 100%);
      border-color: #d1dcff;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(66, 133, 244, 0.1);
    }

    transition: all 0.2s ease;
  }

  .explorer-header {
    margin: 10px 0px;
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    align-items: center;

    .stat-item {
      font-size: 13px;
      color: #5f6368;
      background: #f1f3f4;
      padding: 6px 12px;
      border-radius: 6px;
      font-weight: 500;
      white-space: nowrap;

      &:first-child {
        color: #d93025;
        background: #fce8e6;
      }

      &:last-child {
        color: #1a73e8;
        background: #e8f0fe;
      }
    }
  }

  .variants-container {
    background: white;
    border-radius: 8px;
    border: 1px solid #e8eaed;
    margin-bottom: 20px;

    .variants-header {
      display: grid;
      grid-template-columns: 100px 1fr;
      gap: 16px;
      padding: 12px 16px;
      background: #f8f9fa;
      border-bottom: 1px solid #e8eaed;
      border-radius: 8px 8px 0 0;

      .header-label {
        font-size: 12px;
        font-weight: 600;
        color: #5f6368;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: flex;
        align-items: center;
        gap: 4px;

        .header-tooltip-icon {
          font-size: 13px;
          color: #9aa0a6;
          cursor: help;
          transition: color 0.2s ease;
          display: flex;
          align-items: center;
          margin-top: -1px;

          &:hover {
            color: #4285f4;
          }
        }
      }
    }

    .variants-list {
      max-height: 300px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f3f4;
      }

      &::-webkit-scrollbar-thumb {
        background: #dadce0;
        border-radius: 3px;

        &:hover {
          background: #bdc1c6;
        }
      }

      .variant-row {
        display: grid;
        grid-template-columns: 100px 1fr;
        gap: 16px;
        padding: 12px 16px;
        border-bottom: 1px solid #f1f3f4;
        cursor: pointer;
        transition: all 0.2s ease;
        align-items: center;

        &:hover {
          background: #f8f9fa;
        }

        &.active {
          background: #e8f0fe;
          border-left: 3px solid #1a73e8;
        }

        .variant-cases {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          height: 100%;

          .cases-count {
            font-size: 15px;
            font-weight: 500;
            color: #202124;
            line-height: 1;
            text-align: left;
          }
        }

        .variant-coverage {
          display: flex;
          align-items: center;
          gap: 8px;

          .coverage-bar {
            flex: 1;
            height: 4px;
            background: #e8eaed;
            border-radius: 2px;
            overflow: hidden;

            .coverage-fill {
              height: 100%;
              background: #1a73e8;
              transition: width 0.3s ease;
            }
          }

          .coverage-text {
            font-size: 12px;
            color: #5f6368;
            font-weight: 500;
            min-width: 45px;
            text-align: right;
          }
        }
      }
    }
  }

  .trace-list-section {
    background: white;
    border-radius: 8px;
    border: 1px solid #e8eaed;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    flex: 1;
    min-height: 0;

    .trace-header {
      padding: 16px;
      background: #f8f9fa;
      border-bottom: 1px solid #e8eaed;
      border-radius: 8px 8px 0 0;

      .trace-title {
        font-size: 14px;
        font-weight: 600;
        color: #202124;
      }
    }

    .trace-steps {
      padding: 16px;
      display: flex;
      flex-direction: column;
      gap: 12px;
      flex: 1;
      overflow-y: auto;
      min-height: 0;

      .trace-step {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e8eaed;
        transition: all 0.2s ease;

        &:hover {
          background: #e8f0fe;
          border-color: #1a73e8;
        }

        &.has-deviation {
          .step-name {
            font-weight: 600;
          }
        }

        &.clickable {
          cursor: pointer;

          &:hover {
            background: #e8f0fe;
            border-color: #1a73e8;
            transform: translateX(2px);
          }
        }

        .step-name {
          font-size: 13px;
          color: #202124;
          font-weight: 500;
          flex: 1;
        }

        .step-tags {
          display: flex;
          gap: 6px;
          flex-shrink: 0;

          .deviation-tag {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;

            &.tag-missing {
              background: #fef3c7;
              color: #d97706;
              border: 1px solid #f59e0b;
            }

            &.tag-extra {
              background: #fecaca;
              color: #dc2626;
              border: 1px solid #ef4444;
            }

            &.tag-wrong-order {
              background: #e0e7ff;
              color: #4338ca;
              border: 1px solid #6366f1;
            }

            &.tag-skipped {
              background: #f3e8ff;
              color: #7c3aed;
              border: 1px solid #8b5cf6;
            }

            &.tag-repeated {
              background: #fed7d7;
              color: #e53e3e;
              border: 1px solid #f56565;
            }

            &.tag-timing {
              background: #d1fae5;
              color: #059669;
              border: 1px solid #10b981;
            }

            &.tag-default {
              background: #f1f5f9;
              color: #64748b;
              border: 1px solid #94a3b8;
            }
          }
        }
      }
    }
  }
}
</style>
