<template>
  <div class="heatmap-chart">
    <div v-if="loading" class="chart-loading">
      <el-icon class="loading-icon">
        <Loading />
      </el-icon>
      <span>图表加载中...</span>
    </div>
    <div v-else-if="error" class="chart-error">
      <el-icon class="error-icon">
        <Warning />
      </el-icon>
      <span>{{ error }}</span>
    </div>
    <ClientOnly v-else>
      <v-chart :option="chartOption" class="chart" autoresize />
      <template #fallback>
        <div class="chart-loading">
          <span>图表加载中...</span>
        </div>
      </template>
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import { Loading, Warning } from '@element-plus/icons-vue'

// 动态导入VChart以避免SSR问题
const VChart = defineAsyncComponent(() => import('vue-echarts'))

interface HeatmapDataPoint {
  x: number | string
  y: number | string
  value: number
  label?: string
}

interface Props {
  data: HeatmapDataPoint[]
  title?: string
  loading?: boolean
  error?: string
  height?: string
  xAxisData?: string[]
  yAxisData?: string[]
  xAxisName?: string
  yAxisName?: string
  valueFormatter?: (value: number) => string
  colorRange?: [string, string]
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  loading: false,
  error: '',
  height: '400px',
  xAxisName: '',
  yAxisName: '',
  valueFormatter: (value: number) => value.toString(),
  colorRange: () => ['#e3f2fd', '#1976d2']
})

// 计算图表配置
const chartOption = computed(() => {
  if (!props.data || props.data.length === 0) {
    return null
  }

  // 准备热力图数据
  const heatmapData = props.data.map(item => [item.x, item.y, item.value])
  
  // 计算数值范围用于颜色映射
  const values = props.data.map(item => item.value)
  const minValue = Math.min(...values)
  const maxValue = Math.max(...values)

  // 自动生成坐标轴数据（如果未提供）
  const xAxisData = props.xAxisData || [...new Set(props.data.map(item => item.x))].sort()
  const yAxisData = props.yAxisData || [...new Set(props.data.map(item => item.y))].sort()

  return {
    title: props.title ? {
      text: props.title,
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal',
        color: '#374151'
      }
    } : undefined,
    tooltip: {
      position: 'top',
      formatter: (params: any) => {
        const dataPoint = props.data.find(item => 
          item.x === params.data[0] && item.y === params.data[1]
        )
        return `
          ${props.xAxisName}: ${params.data[0]}<br/>
          ${props.yAxisName}: ${params.data[1]}<br/>
          值: ${props.valueFormatter(params.data[2])}
          ${dataPoint?.label ? `<br/>${dataPoint.label}` : ''}
        `
      }
    },
    grid: {
      height: '70%',
      top: props.title ? '15%' : '10%',
      left: '10%',
      right: '15%'
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      splitArea: {
        show: true
      },
      axisLabel: {
        fontSize: 12,
        color: '#6b7280'
      },
      name: props.xAxisName,
      nameLocation: 'middle',
      nameGap: 30,
      nameTextStyle: {
        color: '#6b7280',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'category',
      data: yAxisData,
      splitArea: {
        show: true
      },
      axisLabel: {
        fontSize: 12,
        color: '#6b7280'
      },
      name: props.yAxisName,
      nameLocation: 'middle',
      nameGap: 50,
      nameTextStyle: {
        color: '#6b7280',
        fontSize: 12
      }
    },
    visualMap: {
      min: minValue,
      max: maxValue,
      calculable: true,
      orient: 'vertical',
      left: 'right',
      top: 'middle',
      inRange: {
        color: props.colorRange
      },
      textStyle: {
        color: '#6b7280',
        fontSize: 12
      },
      formatter: (value: number) => props.valueFormatter(value)
    },
    series: [{
      name: 'Heatmap',
      type: 'heatmap',
      data: heatmapData,
      label: {
        show: true,
        fontSize: 10,
        color: '#374151',
        formatter: (params: any) => {
          // 只在值较大时显示标签，避免过于拥挤
          const ratio = (params.data[2] - minValue) / (maxValue - minValue)
          return ratio > 0.3 ? props.valueFormatter(params.data[2]) : ''
        }
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
})
</script>

<style lang="scss" scoped>
.heatmap-chart {
  width: 100%;
  height: v-bind(height);
  position: relative;

  .chart {
    width: 100%;
    height: 100%;
  }

  .chart-loading,
  .chart-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6b7280;
    font-size: 0.875rem;

    .dark & {
      color: #9ca3af;
    }

    .loading-icon,
    .error-icon {
      font-size: 2rem;
      margin-bottom: 8px;
    }

    .loading-icon {
      animation: spin 1s linear infinite;
    }

    .error-icon {
      color: #ef4444;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 深色模式适配
.dark .heatmap-chart {
  :deep(.echarts) {
    .echarts-tooltip {
      background-color: rgba(31, 41, 55, 0.95) !important;
      border-color: rgba(75, 85, 99, 0.5) !important;
      color: #f9fafb !important;
    }
  }
}
</style>
