<template>
  <div class="radar-chart">
    <div v-if="loading" class="chart-loading">
      <el-icon class="loading-icon">
        <Loading />
      </el-icon>
      <span>图表加载中...</span>
    </div>
    <div v-else-if="error" class="chart-error">
      <el-icon class="error-icon">
        <Warning />
      </el-icon>
      <span>{{ error }}</span>
    </div>
    <ClientOnly v-else>
      <v-chart :option="chartOption" class="chart" autoresize />
      <template #fallback>
        <div class="chart-loading">
          <span>图表加载中...</span>
        </div>
      </template>
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import { Loading, Warning } from '@element-plus/icons-vue'

// 动态导入VChart以避免SSR问题
const VChart = defineAsyncComponent(() => import('vue-echarts'))

interface RadarIndicator {
  name: string
  max: number
  min?: number
  color?: string
}

interface RadarDataSeries {
  name: string
  value: number[]
  color?: string
  areaStyle?: {
    opacity: number
  }
}

interface Props {
  indicators: RadarIndicator[]
  series: RadarDataSeries[]
  title?: string
  loading?: boolean
  error?: string
  height?: string
  shape?: 'polygon' | 'circle'
  splitNumber?: number
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  loading: false,
  error: '',
  height: '400px',
  shape: 'polygon',
  splitNumber: 5
})

// 计算图表配置
const chartOption = computed(() => {
  if (!props.indicators || props.indicators.length === 0 || !props.series || props.series.length === 0) {
    return null
  }

  // 默认颜色数组
  const defaultColors = [
    '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',
    '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'
  ]

  return {
    title: props.title ? {
      text: props.title,
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal',
        color: '#374151'
      }
    } : undefined,
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const data = params.data
        let result = `${data.name}<br/>`
        data.value.forEach((value: number, index: number) => {
          result += `${props.indicators[index].name}: ${value}<br/>`
        })
        return result
      }
    },
    legend: {
      data: props.series.map(item => item.name),
      bottom: 10,
      textStyle: {
        color: '#6b7280',
        fontSize: 12
      }
    },
    radar: {
      indicator: props.indicators.map(indicator => ({
        name: indicator.name,
        max: indicator.max,
        min: indicator.min || 0,
        nameGap: 10,
        axisName: {
          color: '#6b7280',
          fontSize: 12
        }
      })),
      shape: props.shape,
      splitNumber: props.splitNumber,
      splitLine: {
        lineStyle: {
          color: '#e5e7eb',
          type: 'dashed'
        }
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: ['rgba(250, 250, 250, 0.3)', 'rgba(200, 200, 200, 0.1)']
        }
      },
      axisLine: {
        lineStyle: {
          color: '#d1d5db'
        }
      },
      center: ['50%', '55%'],
      radius: '70%'
    },
    series: [{
      name: 'Radar',
      type: 'radar',
      data: props.series.map((item, index) => ({
        value: item.value,
        name: item.name,
        itemStyle: {
          color: item.color || defaultColors[index % defaultColors.length]
        },
        lineStyle: {
          color: item.color || defaultColors[index % defaultColors.length],
          width: 2
        },
        areaStyle: item.areaStyle || {
          opacity: 0.2
        }
      }))
    }]
  }
})
</script>

<style lang="scss" scoped>
.radar-chart {
  width: 100%;
  height: v-bind(height);
  position: relative;

  .chart {
    width: 100%;
    height: 100%;
  }

  .chart-loading,
  .chart-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6b7280;
    font-size: 0.875rem;

    .dark & {
      color: #9ca3af;
    }

    .loading-icon,
    .error-icon {
      font-size: 2rem;
      margin-bottom: 8px;
    }

    .loading-icon {
      animation: spin 1s linear infinite;
    }

    .error-icon {
      color: #ef4444;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 深色模式适配
.dark .radar-chart {
  :deep(.echarts) {
    .echarts-tooltip {
      background-color: rgba(31, 41, 55, 0.95) !important;
      border-color: rgba(75, 85, 99, 0.5) !important;
      color: #f9fafb !important;
    }
  }
}
</style>
