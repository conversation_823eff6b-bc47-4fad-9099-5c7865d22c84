<template>
  <div class="box-plot-chart">
    <div v-if="loading" class="chart-loading">
      <el-icon class="loading-icon">
        <Loading />
      </el-icon>
      <span>图表加载中...</span>
    </div>
    <div v-else-if="error" class="chart-error">
      <el-icon class="error-icon">
        <Warning />
      </el-icon>
      <span>{{ error }}</span>
    </div>
    <ClientOnly v-else>
      <v-chart :option="chartOption" class="chart" autoresize />
      <template #fallback>
        <div class="chart-loading">
          <span>图表加载中...</span>
        </div>
      </template>
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import { Loading, Warning } from '@element-plus/icons-vue'

// 动态导入VChart以避免SSR问题
const VChart = defineAsyncComponent(() => import('vue-echarts'))

interface BoxPlotData {
  name: string
  values: number[] // [min, q1, median, q3, max]
  outliers?: Array<{ value: number; label?: string }>
}

interface Props {
  data: BoxPlotData[]
  title?: string
  loading?: boolean
  error?: string
  height?: string
  yAxisName?: string
  formatter?: (value: number) => string
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  loading: false,
  error: '',
  height: '300px',
  yAxisName: '值',
  formatter: (value: number) => value.toString()
})

// 计算图表配置
const chartOption = computed(() => {
  if (!props.data || props.data.length === 0) {
    return null
  }

  // 准备箱线图数据
  const boxData = props.data.map(item => item.values)
  const categories = props.data.map(item => item.name)
  
  // 准备异常值数据
  const outlierData: Array<[number, number]> = []
  props.data.forEach((item, categoryIndex) => {
    if (item.outliers) {
      item.outliers.forEach(outlier => {
        outlierData.push([categoryIndex, outlier.value])
      })
    }
  })

  return {
    title: props.title ? {
      text: props.title,
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal',
        color: '#374151'
      }
    } : undefined,
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        if (params.seriesType === 'boxplot') {
          const data = params.data
          return `
            ${params.name}<br/>
            最大值: ${props.formatter(data[5])}<br/>
            上四分位数: ${props.formatter(data[4])}<br/>
            中位数: ${props.formatter(data[3])}<br/>
            下四分位数: ${props.formatter(data[2])}<br/>
            最小值: ${props.formatter(data[1])}
          `
        } else if (params.seriesType === 'scatter') {
          return `${categories[params.data[0]]}<br/>异常值: ${props.formatter(params.data[1])}`
        }
        return ''
      }
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: props.title ? '15%' : '10%'
    },
    xAxis: {
      type: 'category',
      data: categories,
      boundaryGap: true,
      nameGap: 30,
      splitArea: {
        show: false
      },
      axisLabel: {
        fontSize: 12,
        color: '#6b7280'
      },
      axisLine: {
        lineStyle: {
          color: '#e5e7eb'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: props.yAxisName,
      nameTextStyle: {
        color: '#6b7280',
        fontSize: 12
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: ['rgba(250, 250, 250, 0.3)', 'rgba(200, 200, 200, 0.1)']
        }
      },
      axisLabel: {
        fontSize: 12,
        color: '#6b7280',
        formatter: (value: number) => props.formatter(value)
      },
      axisLine: {
        lineStyle: {
          color: '#e5e7eb'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#e5e7eb',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: 'boxplot',
        type: 'boxplot',
        data: boxData,
        itemStyle: {
          color: '#3b82f6',
          borderColor: '#1d4ed8',
          borderWidth: 1
        },
        boxWidth: ['7%', '50%']
      },
      ...(outlierData.length > 0 ? [{
        name: 'outliers',
        type: 'scatter',
        data: outlierData,
        itemStyle: {
          color: '#ef4444',
          opacity: 0.8
        },
        symbolSize: 6
      }] : [])
    ]
  }
})
</script>

<style lang="scss" scoped>
.box-plot-chart {
  width: 100%;
  height: v-bind(height);
  position: relative;

  .chart {
    width: 100%;
    height: 100%;
  }

  .chart-loading,
  .chart-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6b7280;
    font-size: 0.875rem;

    .dark & {
      color: #9ca3af;
    }

    .loading-icon,
    .error-icon {
      font-size: 2rem;
      margin-bottom: 8px;
    }

    .loading-icon {
      animation: spin 1s linear infinite;
    }

    .error-icon {
      color: #ef4444;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 深色模式适配
.dark .box-plot-chart {
  :deep(.echarts) {
    .echarts-tooltip {
      background-color: rgba(31, 41, 55, 0.95) !important;
      border-color: rgba(75, 85, 99, 0.5) !important;
      color: #f9fafb !important;
    }
  }
}
</style>
