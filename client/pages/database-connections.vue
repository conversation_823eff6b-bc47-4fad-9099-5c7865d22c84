<template>
  <div class="database-connections-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="title-section">
            <h1 class="title">数据库连接管理</h1>
            <p class="description">管理您的数据库连接配置，支持MySQL、PostgreSQL等多种数据库</p>
          </div>
        </div>
        <div class="header-right">
          <el-button
            type="primary"
            :icon="Plus"
            @click="showCreateDialog = true"
          >
            新建连接
          </el-button>
        </div>
      </div>
    </div>

    <!-- 连接列表 -->
    <div class="connections-container">
      <el-card class="connections-card">
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>

        <div v-else-if="connections.length === 0" class="empty-state">
          <el-icon class="empty-state__icon">
            <Connection />
          </el-icon>
          <p class="empty-state__text">
            暂无数据库连接
          </p>
          <p class="empty-state__description">
            点击上方"新建连接"按钮创建您的第一个数据库连接
          </p>
        </div>

        <div v-else class="connections-list">
          <div
            v-for="connection in connections"
            :key="connection.id"
            class="connection-item"
          >
            <div class="connection-card">
              <div class="connection-header">
                <div class="connection-title">
                  <div class="title-left">
                    <div class="connection-icon-wrapper">
                      <el-icon class="connection-icon">
                        <Coin />
                      </el-icon>
                    </div>
                    <div class="title-content">
                      <h3 class="connection-name">{{ connection.name }}</h3>
                      <p v-if="connection.description" class="connection-description">
                        {{ connection.description }}
                      </p>
                    </div>
                  </div>
                  <div class="title-right">
                    <el-tag
                      :type="getStatusTagType(connection.status)"
                      size="large"
                      class="status-tag"
                      effect="light"
                    >
                      <el-icon class="status-icon">
                        <component :is="getStatusIcon(connection.status)" />
                      </el-icon>
                      {{ getStatusText(connection.status) }}
                    </el-tag>
                  </div>
                </div>

                <div class="connection-details">
                  <div class="details-grid">
                    <div class="detail-item">
                      <div class="detail-label">
                        <el-icon><Connection /></el-icon>
                        数据库类型
                      </div>
                      <div class="detail-value">{{ connection.type.toUpperCase() }}</div>
                    </div>
                    <div class="detail-item">
                      <div class="detail-label">
                        <el-icon><Link /></el-icon>
                        连接地址
                      </div>
                      <div class="detail-value">{{ connection.host }}:{{ connection.port }}</div>
                    </div>
                    <div class="detail-item">
                      <div class="detail-label">
                        <el-icon><Clock /></el-icon>
                        最后测试
                      </div>
                      <div class="detail-value">{{ connection.lastTestedDescription }}</div>
                    </div>
                    <div class="detail-item">
                      <div class="detail-label">
                        <el-icon><Coin /></el-icon>
                        数据库名
                      </div>
                      <div class="detail-value">{{ connection.database }}</div>
                    </div>
                  </div>
                </div>

                <div v-if="connection.lastError" class="connection-error">
                  <el-alert
                    :title="connection.lastError"
                    type="error"
                    :closable="false"
                    show-icon
                  />
                </div>

                <div class="connection-actions">
                  <el-button
                    type="primary"
                    :icon="Connection"
                    @click="testConnection(connection)"
                    :loading="testingConnections.has(connection.id)"
                    plain
                  >
                    测试连接
                  </el-button>
                  <el-button
                    :icon="Edit"
                    @click="editConnection(connection)"
                    plain
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    :icon="Delete"
                    @click="deleteConnection(connection)"
                    plain
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 创建/编辑连接对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingConnection ? '编辑连接' : '新建连接'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="连接名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入连接名称" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="2"
            placeholder="请输入连接描述（可选）"
          />
        </el-form-item>

        <el-form-item label="数据库类型" prop="type">
          <el-select
            v-model="form.type"
            placeholder="请选择数据库类型"
            @change="handleDatabaseTypeChange"
          >
            <el-option
              v-for="type in databaseTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>

        <el-row :gutter="16">
          <el-col :span="16">
            <el-form-item label="主机地址" prop="host">
              <el-input v-model="form.host" placeholder="localhost" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="端口" prop="port">
              <el-input-number
                v-model="form.port"
                :min="1"
                :max="65535"
                :controls="false"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="数据库名" prop="database">
          <el-input v-model="form.database" placeholder="请输入数据库名" />
        </el-form-item>

        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.username" placeholder="请输入用户名" />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitForm"
            :loading="submitting"
          >
            {{ editingConnection ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Connection,
  Edit,
  Delete,
  Warning,
  Coin,
  Link,
  Clock,
  CircleCheck,
  CircleClose,
  QuestionFilled
} from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import {
  DatabaseType,
  ConnectionStatus
} from '~/types'
import type {
  DatabaseConnection,
  CreateDatabaseConnectionDto,
  UpdateDatabaseConnectionDto
} from '~/types'
import { useApi } from '~/utils/api'

// 页面配置
definePageMeta({
  layout: 'default'
})

// 设置页面标题
useHead({
  title: '数据库连接管理 - ProMax'
})

// 响应式数据
const api = useApi()
const loading = ref(false)
const submitting = ref(false)
const showCreateDialog = ref(false)
const editingConnection = ref<DatabaseConnection | null>(null)
const connections = ref<DatabaseConnection[]>([])
const testingConnections = ref(new Set<number>())

// 表单相关
const formRef = ref<FormInstance>()
const form = ref<CreateDatabaseConnectionDto>({
  name: '',
  description: '',
  type: DatabaseType.MYSQL,
  host: '127.0.0.1',
  port: 3306,
  database: '',
  username: '',
  password: '',
})

// 数据库类型选项
const databaseTypes = [
  { label: 'MySQL', value: 'mysql', defaultPort: 3306 },
  { label: 'PostgreSQL', value: 'postgresql', defaultPort: 5432 },
  { label: 'SQL Server', value: 'mssql', defaultPort: 1433 },
  { label: 'Oracle', value: 'oracle', defaultPort: 1521 },
]

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入连接名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择数据库类型', trigger: 'change' }
  ],
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口号必须在 1-65535 之间', trigger: 'blur' }
  ],
  database: [
    { required: true, message: '请输入数据库名', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

// 方法
const loadConnections = async () => {
  loading.value = true
  try {
    connections.value = await api.getDatabaseConnections()
  } catch (error) {
    ElMessage.error('加载连接列表失败')
    console.error('加载连接列表失败:', error)
  } finally {
    loading.value = false
  }
}

const getStatusTagType = (status: ConnectionStatus) => {
  switch (status) {
    case 'active':
      return 'success'
    case 'error':
      return 'danger'
    default:
      return 'info'
  }
}

const getStatusText = (status: ConnectionStatus) => {
  switch (status) {
    case 'active':
      return '已连接'
    case 'error':
      return '连接失败'
    default:
      return '未测试'
  }
}

const getStatusIcon = (status: ConnectionStatus) => {
  switch (status) {
    case 'active':
      return CircleCheck
    case 'error':
      return CircleClose
    default:
      return QuestionFilled
  }
}

const handleDatabaseTypeChange = (type: DatabaseType) => {
  const dbType = databaseTypes.find(t => t.value === type)
  if (dbType) {
    form.value.port = dbType.defaultPort
  }
}

const testConnection = async (connection: DatabaseConnection) => {
  testingConnections.value.add(connection.id)
  try {
    const result = await api.testSavedDatabaseConnection(connection.id)
    if (result.success) {
      ElMessage.success(`连接测试成功 (${result.connectionTime}ms)`)
    } else {
      ElMessage.error(`连接测试失败: ${result.error}`)
    }
    // 重新加载连接列表以更新状态
    await loadConnections()
  } catch (error) {
    ElMessage.error('连接测试失败')
    console.error('连接测试失败:', error)
  } finally {
    testingConnections.value.delete(connection.id)
  }
}

const editConnection = (connection: DatabaseConnection) => {
  editingConnection.value = connection
  form.value = {
    name: connection.name,
    description: connection.description || '',
    type: connection.type,
    host: connection.host,
    port: connection.port,
    database: connection.database,
    username: connection.username,
    password: '', // 密码不回显
  }
  showCreateDialog.value = true
}

const deleteConnection = async (connection: DatabaseConnection) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除连接 "${connection.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await api.deleteDatabaseConnection(connection.id)
    ElMessage.success('连接删除成功')
    await loadConnections()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除连接失败')
      console.error('删除连接失败:', error)
    }
  }
}

const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    if (editingConnection.value) {
      // 更新连接
      const updateData: UpdateDatabaseConnectionDto = { ...form.value }
      if (!updateData.password) {
        delete updateData.password // 如果密码为空，不更新密码
      }
      await api.updateDatabaseConnection(editingConnection.value.id, updateData)
      ElMessage.success('连接更新成功')
    } else {
      // 创建连接
      await api.createDatabaseConnection(form.value)
      ElMessage.success('连接创建成功')
    }

    showCreateDialog.value = false
    await loadConnections()
  } catch (error) {
    ElMessage.error(editingConnection.value ? '更新连接失败' : '创建连接失败')
    console.error('提交表单失败:', error)
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  editingConnection.value = null
  form.value = {
    name: '',
    description: '',
    type: DatabaseType.MYSQL,
    host: '127.0.0.1',
    port: 3306,
    database: '',
    username: '',
    password: '',
  }
  formRef.value?.resetFields()
}

// 生命周期
onMounted(() => {
  loadConnections()
})
</script>

<style lang="scss" scoped>
.database-connections-page {
  min-height: 100vh;
  background: #f8fafc;
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @media (min-width: 768px) {
    padding: 2rem 2rem;
  }
}

.page-header {
  margin: 0 auto 2rem auto;
  animation: fadeInDown 0.6s ease-out;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;

    @media (max-width: 1024px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
  }

  .header-left {
    flex: 1;
  }

  .title-section {
    .title {
      font-size: 2rem;
      font-weight: 700;
      color: #1e293b;
      margin: 0 0 0.5rem 0;
      background: linear-gradient(135deg, #3b82f6, #8b5cf6);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;

      :global(.dark) & {
        background: linear-gradient(135deg, #60a5fa, #a78bfa);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    .description {
      font-size: 1rem;
      color: #64748b;
      margin: 0;

      :global(.dark) & {
        color: #94a3b8;
      }
    }
  }
}

.connections-container {
  animation: slideInUp 0.5s ease-out 0.2s both;
}

.connections-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  :global(.dark) & {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(71, 85, 105, 0.3);
  }
}

.loading-container {
  padding: 2rem;
}

.empty-state {
  text-align: center;
  padding: 4rem 2rem;

  &__icon {
    font-size: 4rem;
    color: #cbd5e1;
    margin-bottom: 1rem;

    :global(.dark) & {
      color: #475569;
    }
  }

  &__text {
    font-size: 1.25rem;
    font-weight: 600;
    color: #64748b;
    margin: 0 0 0.5rem 0;

    :global(.dark) & {
      color: #94a3b8;
    }
  }

  &__description {
    color: #94a3b8;
    margin: 0;

    :global(.dark) & {
      color: #64748b;
    }
  }
}

.connections-list {
  display: grid;
  gap: 1.5rem;

  .connection-item {
    animation: slideInUp 0.3s ease-out;

    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
    &:nth-child(4) { animation-delay: 0.4s; }
    &:nth-child(5) { animation-delay: 0.5s; }
  }

  .connection-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(226, 232, 240, 0.5);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border-color: rgba(59, 130, 246, 0.3);

      :global(.dark) & {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        border-color: rgba(96, 165, 250, 0.3);
      }
    }

    :global(.dark) & {
      background: rgba(30, 41, 59, 0.95);
      border-color: rgba(71, 85, 105, 0.3);
    }
  }
}

.connection-header {
  .connection-title {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
    }

    .title-left {
      display: flex;
      align-items: flex-start;
      gap: 1rem;
      flex: 1;

      .connection-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        border-radius: 12px;
        flex-shrink: 0;

        .connection-icon {
          font-size: 1.5rem;
          color: white;
        }

        :global(.dark) & {
          background: linear-gradient(135deg, #60a5fa, #a78bfa);
        }
      }

      .title-content {
        flex: 1;
        min-width: 0;

        .connection-name {
          font-size: 1.25rem;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 0.25rem 0;
          line-height: 1.4;

          :global(.dark) & {
            color: #f1f5f9;
          }
        }

        .connection-description {
          color: #64748b;
          font-size: 0.875rem;
          margin: 0;
          line-height: 1.4;

          :global(.dark) & {
            color: #94a3b8;
          }
        }
      }
    }

    .title-right {
      .status-tag {
        .status-icon {
          margin-right: 0.25rem;
        }
      }
    }
  }

  .connection-details {
    margin-bottom: 1.5rem;

    .details-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 0.75rem;
      }

      .detail-item {
        background: rgba(248, 250, 252, 0.8);
        border: 1px solid rgba(226, 232, 240, 0.5);
        border-radius: 8px;
        padding: 0.75rem;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(59, 130, 246, 0.05);
          border-color: rgba(59, 130, 246, 0.2);
        }

        :global(.dark) & {
          background: rgba(15, 23, 42, 0.8);
          border-color: rgba(71, 85, 105, 0.3);

          &:hover {
            background: rgba(96, 165, 250, 0.1);
            border-color: rgba(96, 165, 250, 0.2);
          }
        }

        .detail-label {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: 0.75rem;
          font-weight: 500;
          color: #64748b;
          margin-bottom: 0.25rem;
          text-transform: uppercase;
          letter-spacing: 0.05em;

          :global(.dark) & {
            color: #94a3b8;
          }
        }

        .detail-value {
          color: #1e293b;
          font-weight: 500;
          font-size: 0.875rem;
          word-break: break-all;

          :global(.dark) & {
            color: #f1f5f9;
          }
        }
      }
    }
  }

  .connection-error {
    margin-bottom: 1rem;
  }

  .connection-actions {
    display: flex;
    gap: 0.75rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(226, 232, 240, 0.5);

    @media (max-width: 768px) {
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    :global(.dark) & {
      border-top-color: rgba(71, 85, 105, 0.3);
    }
  }
}

// 动画定义
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

// 加载状态动画
.loading-container {
  animation: pulse 2s ease-in-out infinite;
}

// 响应式优化
@media (max-width: 640px) {
  .database-connections-page {
    padding: 1rem 0.5rem;
  }

  .page-header {
    .header-content {
      gap: 1rem;
    }

    .title-section {
      .title {
        font-size: 1.5rem;
      }

      .description {
        font-size: 0.875rem;
      }
    }
  }

  .connections-list {
    .connection-card {
      padding: 1rem;
    }

    .connection-header {
      .connection-title {
        .title-left {
          gap: 0.75rem;

          .connection-icon-wrapper {
            width: 40px;
            height: 40px;

            .connection-icon {
              font-size: 1.25rem;
            }
          }

          .title-content {
            .connection-name {
              font-size: 1.125rem;
            }
          }
        }
      }

      .connection-actions {
        flex-direction: column;
        gap: 0.5rem;

        :deep(.el-button) {
          width: 100%;
          justify-content: center;
        }
      }
    }
  }
}

// 对话框样式优化
:deep(.el-dialog) {
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);

  :global(.dark) & {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(71, 85, 105, 0.3);
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;

  :global(.dark) & {
    color: #d1d5db;
  }
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.3);
  }

  &.is-focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
}

:deep(.el-select) {
  .el-input__wrapper {
    border-radius: 8px;
  }
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
  }

  &.is-plain {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.8);

    &:hover {
      background: rgba(255, 255, 255, 0.95);
      border-color: var(--el-button-hover-border-color);
      color: var(--el-button-hover-text-color);
    }

    // Primary plain button
    &.el-button--primary {
      color: var(--el-color-primary);
      border-color: var(--el-color-primary);

      &:hover {
        background: var(--el-color-primary);
        border-color: var(--el-color-primary);
        color: #ffffff;
      }
    }

    // Danger plain button
    &.el-button--danger {
      color: var(--el-color-danger);
      border-color: var(--el-color-danger);

      &:hover {
        background: var(--el-color-danger);
        border-color: var(--el-color-danger);
        color: #ffffff;
      }
    }

    :global(.dark) & {
      background: rgba(30, 41, 59, 0.8);
      border-color: rgba(71, 85, 105, 0.8);

      &:hover {
        background: rgba(30, 41, 59, 0.95);
      }

      // Primary plain button in dark mode
      &.el-button--primary {
        color: var(--el-color-primary-light-3);
        border-color: var(--el-color-primary-light-3);

        &:hover {
          background: var(--el-color-primary);
          border-color: var(--el-color-primary);
          color: #ffffff;
        }
      }

      // Danger plain button in dark mode
      &.el-button--danger {
        color: var(--el-color-danger-light-3);
        border-color: var(--el-color-danger-light-3);

        &:hover {
          background: var(--el-color-danger);
          border-color: var(--el-color-danger);
          color: #ffffff;
        }
      }
    }
  }
}

// 状态标签样式优化
:deep(.el-tag) {
  border-radius: 8px;
  font-weight: 500;

  &.el-tag--large {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }

  &.el-tag--success {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.2);
    color: #059669;

    :global(.dark) & {
      background: rgba(34, 197, 94, 0.2);
      color: #10b981;
    }
  }

  &.el-tag--danger {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
    color: #dc2626;

    :global(.dark) & {
      background: rgba(239, 68, 68, 0.2);
      color: #f87171;
    }
  }

  &.el-tag--info {
    background: rgba(107, 114, 128, 0.1);
    border-color: rgba(107, 114, 128, 0.2);
    color: #6b7280;

    :global(.dark) & {
      background: rgba(107, 114, 128, 0.2);
      color: #9ca3af;
    }
  }
}
</style>
