<template>
  <div class="register-container">
    <div class="register-header">
      <h2 class="register-title">
        创建账户
      </h2>
      <p class="register-subtitle">
        注册 ProMax 账户，开始您的流程挖掘之旅
      </p>
    </div>

    <el-form
      ref="formRef"
      :model="formState"
      :rules="formRules"
      label-position="top"
      size="large"
      class="register-form"
      @submit.prevent="handleSubmit"
    >
      <!-- 用户名 -->
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="formState.username"
          placeholder="请输入用户名"
          :prefix-icon="User"
        />
      </el-form-item>

      <!-- 邮箱 -->
      <el-form-item label="邮箱" prop="email">
        <el-input
          v-model="formState.email"
          type="email"
          placeholder="请输入邮箱地址"
          :prefix-icon="Message"
        />
      </el-form-item>

      <!-- 全名 -->
      <el-form-item label="全名" prop="fullName">
        <el-input
          v-model="formState.fullName"
          placeholder="请输入您的全名（可选）"
          :prefix-icon="UserFilled"
        />
      </el-form-item>

      <!-- 密码 -->
      <el-form-item label="密码" prop="password">
        <el-input
          v-model="formState.password"
          type="password"
          placeholder="请输入密码（至少6位）"
          :prefix-icon="Lock"
          show-password
        />
      </el-form-item>

      <!-- 确认密码 -->
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          v-model="formState.confirmPassword"
          type="password"
          placeholder="请再次输入密码"
          :prefix-icon="Lock"
          show-password
        />
      </el-form-item>

      <!-- 错误信息 -->
      <el-alert
        v-if="authStore.error"
        :title="authStore.error"
        type="error"
        show-icon
        closable
        @close="authStore.clearError"
      />

      <!-- 注册按钮 -->
      <el-form-item>
        <el-button
          type="primary"
          :loading="authStore.isLoading"
          size="large"
          class="register-button"
          @click="handleSubmit"
        >
          注册
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 登录链接 -->
    <div class="login-link">
      <p class="login-text">
        已有账户？
        <NuxtLink
          to="/auth/login"
          class="login-link-text"
        >
          立即登录
        </NuxtLink>
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { User, Lock, Message, UserFilled } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'

// 页面配置
definePageMeta({
  layout: 'auth',
  middleware: 'guest'
})

// 设置页面标题
useHead({
  title: '注册 - ProMax'
})

const authStore = useAuthStore()

// 表单引用
const formRef = ref<FormInstance>()

// 表单状态
interface RegisterForm {
  username: string
  email: string
  fullName?: string
  password: string
  confirmPassword: string
}

const formState = reactive<RegisterForm>({
  username: '',
  email: '',
  fullName: '',
  password: '',
  confirmPassword: ''
})

// 确认密码验证器
const validateConfirmPassword = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== formState.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 表单验证规则
const formRules: FormRules<RegisterForm> = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const { confirmPassword, ...registerData } = formState
        await authStore.register(registerData)
      } catch (error) {
        // 错误已在store中处理
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.register-container {
  animation: fadeInUp 0.6s ease-out;
}

.register-header {
  text-align: center;
  margin-bottom: 2.5rem;

  .register-title {
    font-size: 2rem;
    font-weight: 800;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0 0 1rem 0;
    letter-spacing: -0.025em;

    :global(.dark) & {
      background: linear-gradient(135deg, #60a5fa, #a78bfa);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .register-subtitle {
    color: #64748b;
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
    opacity: 0.8;

    :global(.dark) & {
      color: #94a3b8;
    }
  }
}

.register-form {
  :deep(.el-form-item) {
    margin-bottom: 1.5rem;

    .el-form-item__label {
      font-weight: 600;
      color: #374151;
      font-size: 0.95rem;
      margin-bottom: 0.5rem;

      :global(.dark) & {
        color: #d1d5db;
      }
    }

    .el-input {
      .el-input__wrapper {
        border-radius: 12px;
        border: 2px solid #e5e7eb;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 12px 16px;

        &:hover {
          border-color: #3b82f6;
        }

        &.is-focus {
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        :global(.dark) & {
          border-color: #4b5563;
          background-color: #374151;

          &:hover {
            border-color: #60a5fa;
          }

          &.is-focus {
            border-color: #60a5fa;
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
          }
        }

        .el-input__inner {
          font-size: 1rem;
          color: #111827;

          :global(.dark) & {
            color: #f9fafb;
          }

          &::placeholder {
            color: #9ca3af;

            :global(.dark) & {
              color: #6b7280;
            }
          }
        }

        .el-input__prefix {
          color: #6b7280;

          :global(.dark) & {
            color: #9ca3af;
          }
        }

        .el-input__suffix {
          color: #6b7280;

          :global(.dark) & {
            color: #9ca3af;
          }
        }
      }
    }
  }

  :deep(.el-alert) {
    border-radius: 12px;
    border: none;
    margin-bottom: 1.5rem;
  }
}

.register-button {
  width: 100%;
  border-radius: 12px;
  font-weight: 600;
  padding: 14px 24px;
  font-size: 1rem;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border: none;
  box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
  }

  &:active {
    transform: translateY(0);
  }

  :global(.dark) & {
    background: linear-gradient(135deg, #60a5fa, #a78bfa);
  }
}

.login-link {
  text-align: center;
  margin-top: 2rem;

  .login-text {
    color: #64748b;
    font-size: 0.875rem;
    margin: 0;

    :global(.dark) & {
      color: #94a3b8;
    }
  }

  .login-link-text {
    font-weight: 600;
    color: #3b82f6;
    text-decoration: none;
    transition: all 0.2s ease;

    &:hover {
      color: #2563eb;
      text-decoration: underline;
    }

    :global(.dark) & {
      color: #60a5fa;

      &:hover {
        color: #3b82f6;
      }
    }
  }
}

// 页面进入动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 表单项动画
.register-form :deep(.el-form-item) {
  animation: slideInLeft 0.5s ease-out;
  animation-fill-mode: both;

  @for $i from 1 through 7 {
    &:nth-child(#{$i}) {
      animation-delay: #{$i * 0.1}s;
    }
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
