<template>
  <div class="loading-page">
    <div class="loading-content">
      <el-icon class="loading-icon">
        <TrendCharts />
      </el-icon>
      <h1 class="loading-title">
        ProMax
      </h1>
      <p class="loading-text">
        正在跳转...
      </p>
      <div class="loading-spinner"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { TrendCharts } from '@element-plus/icons-vue'

// 页面配置
definePageMeta({
  layout: false
})

const authStore = useAuthStore()

// 根据认证状态重定向
onMounted(() => {
  if (authStore.isAuthenticated) {
    navigateTo('/dashboard')
  } else {
    navigateTo('/auth/login')
  }
})
</script>

<style lang="scss" scoped>
.loading-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  // 添加背景装饰
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
  }
}

.loading-content {
  text-align: center;
  position: relative;
  z-index: 1;
  padding: 3rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: fadeInScale 0.8s ease-out;

  :global(.dark) & {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(71, 85, 105, 0.3);
  }
}

.loading-icon {
  width: 5rem;
  height: 5rem;
  margin: 0 auto 2rem;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  animation: pulse 2s ease-in-out infinite;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);

  :global(.dark) & {
    background: linear-gradient(135deg, #60a5fa, #a78bfa);
  }
}

.loading-title {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 1rem;
  letter-spacing: -0.025em;

  :global(.dark) & {
    background: linear-gradient(135deg, #60a5fa, #a78bfa);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.loading-text {
  color: #64748b;
  font-size: 1.125rem;
  font-weight: 500;
  margin-bottom: 2rem;
  opacity: 0.8;

  :global(.dark) & {
    color: #94a3b8;
  }
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid rgba(59, 130, 246, 0.2);
  border-top-color: #3b82f6;
  border-radius: 50%;
  margin: 0 auto;
  animation: spin 1s linear infinite;

  :global(.dark) & {
    border-color: rgba(96, 165, 250, 0.2);
    border-top-color: #60a5fa;
  }
}

// 动画定义
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes rotate {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.4);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
