<template>
  <div class="analysis-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        数据分析
      </h1>
      <p class="page-subtitle">
        选择一个流程开始数据分析，或查看所有分析项目
      </p>
    </div>

    <!-- 快速开始 -->
    <div class="quick-start-section">
      <el-card class="quick-start-card">
        <template #header>
          <div class="card-header">
            <h2 class="card-title">快速开始</h2>
            <p class="card-description">选择一个流程开始数据分析</p>
          </div>
        </template>

        <div class="quick-actions">
          <el-button
            type="primary"
            :icon="Plus"
            size="large"
            @click="navigateTo('/processes/create')"
          >
            创建新流程
          </el-button>
          <el-button
            :icon="FolderOpened"
            size="large"
            @click="navigateTo('/processes')"
          >
            选择现有流程
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 流程列表 -->
    <div class="processes-section">
      <div class="section-header">
        <h2 class="section-title">可分析的流程</h2>
        <p class="section-description">点击流程卡片进入数据分析</p>
      </div>

      <!-- 加载状态 -->
      <div v-if="processesStore.isLoading" class="loading-state">
        <el-skeleton :rows="3" animated />
      </div>

      <!-- 空状态 -->
      <div v-else-if="processesStore.processes.length === 0" class="empty-state">
        <el-icon class="empty-icon">
          <FolderOpened />
        </el-icon>
        <h3 class="empty-title">暂无流程</h3>
        <p class="empty-description">
          创建您的第一个流程项目开始数据分析
        </p>
        <el-button
          type="primary"
          :icon="Plus"
          @click="navigateTo('/processes/create')"
        >
          创建流程
        </el-button>
      </div>

      <!-- 流程网格 -->
      <div v-else class="processes-grid">
        <el-card
          v-for="process in processesStore.processes"
          :key="process.id"
          class="process-card"
          @click="navigateTo(`/analysis/${process.id}`)"
        >
          <template #header>
            <div class="process-header">
              <h3 class="process-name">{{ process.name }}</h3>
              <div class="process-status">
                <span
                  class="status-indicator"
                  :class="`status-${process.status}`"
                ></span>
                <span class="status-text">{{ getStatusText(process.status) }}</span>
              </div>
            </div>
          </template>

          <div class="process-content">
            <p class="process-description">
              {{ process.description || '-' }}
            </p>

            <div class="process-meta">
              <div class="business-domain">
                <el-icon class="domain-icon">
                  <OfficeBuilding />
                </el-icon>
                {{ process.businessDomain || '未知' }}
              </div>

              <div class="process-footer">
                <span class="update-time">{{ formatDate(process.updatedAt) }}</span>
                <span v-if="process.user" class="process-owner">{{ process.user.username }}</span>
              </div>
            </div>

            <div class="analysis-actions" @click.stop>
              <el-dropdown
                trigger="click"
                placement="bottom-end"
                @command="(command) => handleAnalysisAction(command, String(process.id))"
              >
                <el-button
                  type="primary"
                  size="small"
                  :icon="Operation"
                  class="action-dropdown-trigger"
                  @click.stop
                >
                  分析功能
                  <el-icon class="el-icon--right">
                    <ArrowDown />
                  </el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="upload">
                      <el-icon><Upload /></el-icon>
                      上传数据
                    </el-dropdown-item>
                    <el-dropdown-item command="discover">
                      <el-icon><Search /></el-icon>
                      流程发现
                    </el-dropdown-item>
                    <el-dropdown-item command="performance">
                      <el-icon><TrendCharts /></el-icon>
                      性能分析
                    </el-dropdown-item>
                    <el-dropdown-item command="conformance">
                      <el-icon><CircleCheck /></el-icon>
                      符合性检查
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Plus,
  FolderOpened,
  OfficeBuilding,
  Upload,
  Search,
  TrendCharts,
  CircleCheck,
  Operation,
  ArrowDown
} from '@element-plus/icons-vue'

// 页面元数据
definePageMeta({
  title: '数据分析',
  description: '流程挖掘数据分析平台'
})

// 使用 stores
const processesStore = useProcessesStore()

// 获取流程列表
await processesStore.fetchProcesses()

// 状态文本映射
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    active: '活跃',
    completed: '已完成',
    archived: '已归档'
  }
  return statusMap[status] || status
}

// 格式化日期
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 处理分析功能操作
const handleAnalysisAction = (command: string, processId: string) => {
  switch (command) {
    case 'upload':
      navigateTo(`/analysis/${processId}/upload`)
      break
    case 'discover':
      navigateTo(`/analysis/${processId}/discover`)
      break
    case 'performance':
      navigateTo(`/analysis/${processId}/performance`)
      break
    case 'conformance':
      navigateTo(`/conformance/check?processId=${processId}`)
      break
  }
}
</script>

<style lang="scss" scoped>
.analysis-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @media (max-width: 768px) {
    padding: 1rem 0.5rem;
  }
}

.page-header {
  max-width: 1200px;
  margin: 0 auto 3rem auto;
  text-align: center;
  animation: fadeInUp 0.6s ease-out;

  .page-title {
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0 0 1rem 0;
    letter-spacing: -0.025em;

    :global(.dark) & {
      background: linear-gradient(135deg, #60a5fa, #a78bfa);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .page-subtitle {
    color: #64748b;
    font-size: 1.125rem;
    font-weight: 500;
    margin: 0;
    opacity: 0.8;

    :global(.dark) & {
      color: #94a3b8;
    }
  }
}

.quick-start-section {
  max-width: 1200px;
  margin: 0 auto 3rem auto;
  animation: slideInLeft 0.5s ease-out 0.2s both;
}

.quick-start-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  :global(.dark) & {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(71, 85, 105, 0.3);
  }

  :deep(.el-card__header) {
    padding: 1.5rem 1.5rem  0.5rem 1.5rem;
    border-bottom: none;
  }

  :deep(.el-card__body) {
    padding: 1rem 1.5rem 1.5rem 1.5rem;
  }

  .card-header {
    text-align: center;

    .card-title {
      font-size: 1.5rem;
      font-weight: 700;
      color: #111827;
      margin: 0 0 0.5rem 0;

      :global(.dark) & {
        color: #ffffff;
      }
    }

    .card-description {
      color: #64748b;
      margin: 0;

      :global(.dark) & {
        color: #94a3b8;
      }
    }
  }

  .quick-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;

    @media (max-width: 640px) {
      flex-direction: column;
    }

    :deep(.el-button) {
      border-radius: 12px;
      font-weight: 600;
      padding: 12px 24px;
      transition: all 0.2s ease;

      &.el-button--primary {
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        border: none;
        box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
        }

        :global(.dark) & {
          background: linear-gradient(135deg, #60a5fa, #a78bfa);
        }
      }

      &:not(.el-button--primary) {
        border: 2px solid #e5e7eb;
        color: #6b7280;

        &:hover {
          border-color: #3b82f6;
          color: #3b82f6;
          transform: translateY(-1px);
        }

        :global(.dark) & {
          border-color: #4b5563;
          color: #9ca3af;

          &:hover {
            border-color: #60a5fa;
            color: #60a5fa;
          }
        }
      }
    }
  }
}

.processes-section {
  max-width: 1200px;
  margin: 0 auto;
  animation: slideInUp 0.5s ease-out 0.4s both;

  .section-header {
    margin-bottom: 2rem;

    .section-title {
      font-size: 1.5rem;
      font-weight: 700;
      color: #111827;
      margin: 0 0 0.5rem 0;

      :global(.dark) & {
        color: #ffffff;
      }
    }

    .section-description {
      color: #64748b;
      margin: 0;

      :global(.dark) & {
        color: #94a3b8;
      }
    }
  }
}

.loading-state {
  :deep(.el-skeleton) {
    .el-skeleton__item {
      background: rgba(226, 232, 240, 0.5);
      border-radius: 8px;

      :global(.dark) & {
        background: rgba(75, 85, 99, 0.5);
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 4rem 1rem;

  .empty-icon {
    width: 4rem;
    height: 4rem;
    color: #cbd5e1;
    margin: 0 auto 1.5rem;

    :global(.dark) & {
      color: #64748b;
    }
  }

  .empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    margin: 0 0 0.5rem 0;

    :global(.dark) & {
      color: #ffffff;
    }
  }

  .empty-description {
    color: #64748b;
    margin: 0 0 2rem 0;

    :global(.dark) & {
      color: #94a3b8;
    }
  }

  :deep(.el-button) {
    border-radius: 12px;
    font-weight: 600;
    padding: 12px 24px;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border: none;
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
    }

    :global(.dark) & {
      background: linear-gradient(135deg, #60a5fa, #a78bfa);
    }
  }
}

.processes-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;

  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }
}

.process-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  :global(.dark) & {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(71, 85, 105, 0.3);
  }

  :deep(.el-card__header) {
    padding: 1.5rem 1.5rem 0.5rem 1.5rem;
    border-bottom: none;
  }

  :deep(.el-card__body) {
    padding: 0 1.5rem 1.5rem 1.5rem;
  }

  .process-header {
    .process-name {
      font-size: 1.125rem;
      font-weight: 700;
      color: #111827;
      margin: 0 0 0.5rem 0;

      :global(.dark) & {
        color: #ffffff;
      }
    }

    .process-status {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .status-text {
        font-size: 0.875rem;
        color: #6b7280;
        font-weight: 500;

        :global(.dark) & {
          color: #9ca3af;
        }
      }
    }
  }

  .process-content {
    .process-description {
      color: #6b7280;
      margin: 0 0 1rem 0;
      line-height: 1.5;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;

      :global(.dark) & {
        color: #9ca3af;
      }
    }

    .process-meta {
      margin-bottom: 1rem;

      .business-domain {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 0.5rem;

        :global(.dark) & {
          color: #9ca3af;
        }

        .domain-icon {
          width: 1rem;
          height: 1rem;
        }
      }

      .process-footer {
        display: flex;
        justify-content: space-between;
        font-size: 0.75rem;
        color: #6b7280;

        :global(.dark) & {
          color: #9ca3af;
        }

        .process-owner {
          font-weight: 600;
          color: #3b82f6;

          :global(.dark) & {
            color: #60a5fa;
          }
        }
      }
    }

    .analysis-actions {
      display: flex;
      justify-content: center;
      padding-top: 1rem;
      border-top: 1px solid rgba(226, 232, 240, 0.5);

      :global(.dark) & {
        border-top-color: rgba(75, 85, 99, 0.5);
      }

      .action-dropdown-trigger {
        border-radius: 8px;
        font-size: 0.875rem;
        padding: 8px 16px;
        font-weight: 600;
        transition: all 0.2s ease;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        border: none;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        :global(.dark) & {
          background: linear-gradient(135deg, #60a5fa, #a78bfa);
        }

        .el-icon--right {
          margin-left: 4px;
        }
      }

      // 下拉菜单样式
      :deep(.el-dropdown-menu) {
        border-radius: 12px;
        border: none;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        padding: 8px;

        :global(.dark) & {
          background: rgba(30, 41, 59, 0.95);
          border: 1px solid rgba(71, 85, 105, 0.3);
        }

        .el-dropdown-menu__item {
          border-radius: 8px;
          padding: 8px 12px;
          margin: 2px 0;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          gap: 8px;

          &:hover {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;

            :global(.dark) & {
              background: rgba(96, 165, 250, 0.1);
              color: #60a5fa;
            }
          }

          .el-icon {
            width: 16px;
            height: 16px;
          }
        }
      }
    }
  }
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;

  &.status-draft {
    background: linear-gradient(135deg, #6b7280, #9ca3af);
  }

  &.status-active {
    background: linear-gradient(135deg, #10b981, #34d399);
  }

  &.status-completed {
    background: linear-gradient(135deg, #06b6d4, #22d3ee);
  }

  &.status-archived {
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
  }
}

// 动画定义
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
