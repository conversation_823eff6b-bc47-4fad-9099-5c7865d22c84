<template>
  <div class="performance-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-title">
        <el-button
          :icon="ArrowLeft"
          text
          class="back-button"
          @click="navigateTo(`/processes/${processId}`)"
        />
        <h1 class="title">
          性能分析
        </h1>
      </div>
      <p class="subtitle">
        分析流程执行的性能指标和瓶颈
      </p>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <el-card>
        <div class="control-content">
          <div class="control-actions">
            <el-button
              v-if="canStartAnalysis"
              type="primary"
              :icon="TrendCharts"
              :loading="isAnalyzing || isLoadingFromCache"
              class="analysis-button"
              @click="() => startAnalysis()"
            >
              {{ isLoadingFromCache ? '加载缓存中...' : '开始性能分析' }}
            </el-button>
            <el-button
              v-if="!isReadOnly && performanceResult && performanceResult.fromCache"
              type="primary"
              :icon="Refresh"
              :loading="isAnalyzing"
              class="refresh-button"
              @click="() => startAnalysis({ ...currentConfig, forceRefresh: true })"
            >
              重新分析
            </el-button>
            <el-button
              v-if="performanceResult"
              :icon="Download"
              class="export-button"
              @click="exportResult"
            >
              导出报告
            </el-button>
            <el-button
              v-if="!isReadOnly && performanceResult"
              :icon="Setting"
              class="config-button"
              @click="showConfigDialog"
            >
              性能分析配置
            </el-button>
          </div>

          <div v-if="performanceResult" class="result-info">
            <div v-if="performanceResult.fromCache" class="cache-status">
              <el-tag type="info" size="small">
                <el-icon><Clock /></el-icon>
                缓存结果
              </el-tag>
            </div>
            <div v-if="performanceResult.timestamp" class="last-updated">
              最后更新: {{ formatDateTime(performanceResult.timestamp) }}
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 结果Tabs -->
    <div class="analysis-tabs">
      <el-tabs v-model="activeTab" class="result-tabs">
        <el-tab-pane label="静态分析结果" name="static">
          <div v-if="isAnalyzing" class="analyzing-state">
            <div class="loading-spinner"></div>
            <p class="analyzing-text">正在分析性能数据...</p>
          </div>
          <div v-else-if="performanceResult" class="analysis-results">
            <!-- 关键指标 -->
            <el-card class="metrics-card">
              <template #header>
                <h3 class="card-title">
                  关键性能指标
                </h3>
              </template>

              <div class="metrics-grid">
                <div class="metric-item metric-avg">
                  <div class="metric-value">
                    {{ formatDuration(performanceResult.caseStatistics?.avgDuration) }}
                  </div>
                  <div class="metric-label">平均案例持续时间</div>
                </div>
                <div class="metric-item metric-min">
                  <div class="metric-value">
                    {{ formatDuration(performanceResult.caseStatistics?.minDuration) }}
                  </div>
                  <div class="metric-label">最短案例时间</div>
                </div>
                <div class="metric-item metric-max">
                  <div class="metric-value">
                    {{ formatDuration(performanceResult.caseStatistics?.maxDuration) }}
                  </div>
                  <div class="metric-label">最长案例时间</div>
                </div>
                <div class="metric-item metric-total">
                  <div class="metric-value">
                    {{ totalCases }}
                  </div>
                  <div class="metric-label">分析案例总数</div>
                </div>
              </div>
            </el-card>

            <!-- 案例持续时间分布 -->
            <el-card class="chart-card">
              <template #header>
                <h3 class="card-title">
                  案例持续时间分布
                </h3>
              </template>

              <div class="chart-container">
                <v-chart
                  v-if="durationChartOption"
                  :option="durationChartOption"
                  class="chart-content"
                  autoresize
                />
                <div v-else class="chart-placeholder">
                  <div class="placeholder-content">
                    <el-icon class="placeholder-icon">
                      <TrendCharts />
                    </el-icon>
                    <p class="placeholder-text">暂无数据</p>
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 活动性能分析 -->
            <el-card class="table-card">
              <template #header>
                <h3 class="card-title">
                  活动性能分析
                </h3>
              </template>

              <div class="table-container">
                <el-table :data="activityPerformanceData" stripe class="performance-table">
                  <el-table-column prop="activity" label="活动名称" />
                  <el-table-column label="平均执行时间" width="150">
                    <template #default="{ row }">
                      {{ formatDuration(row.avgDuration) }}
                    </template>
                  </el-table-column>
                  <el-table-column label="平均等待时间" width="150">
                    <template #default="{ row }">
                      {{ formatDuration(row.avgWaitingTime) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="frequency" label="执行次数" width="100" />
                  <el-table-column label="性能评分" width="150">
                    <template #default="{ row }">
                      <div class="performance-score-cell">
                        <div class="score-value">
                          {{ getPerformanceScoreValue(row) }}分
                        </div>
                        <el-tag
                          :type="getPerformanceTagType(row.performanceScore) as any"
                          size="small"
                        >
                          {{ getPerformanceLevel(row.performanceScore) }}
                        </el-tag>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="主要问题" width="150">
                    <template #default="{ row }">
                      <div class="performance-issues">
                        {{ getPerformanceIssues(row) }}
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-card>

            <!-- 瓶颈识别 -->
            <el-card class="bottlenecks-card">
              <template #header>
                <h3 class="card-title">
                  瓶颈识别
                </h3>
              </template>

              <div v-if="bottlenecks.length > 0" class="bottlenecks-list">
                <div
                  v-for="(bottleneck, index) in bottlenecks"
                  :key="index"
                  class="bottleneck-item"
                >
                  <div class="bottleneck-content">
                    <div class="bottleneck-info">
                      <h4 class="bottleneck-title">{{ bottleneck.activity }}</h4>
                      <p class="bottleneck-description">{{ bottleneck.description }}</p>
                      <div class="bottleneck-metrics">
                        <div class="bottleneck-metric">
                          平均执行时间: {{ formatDuration(bottleneck.avgDuration) }}
                        </div>
                        <div class="bottleneck-metric">
                          平均等待时间: {{ formatDuration(bottleneck.avgWaitTime) }}
                        </div>
                      </div>
                    </div>
                    <div class="bottleneck-tags">
                      <el-tag
                        :type="getBottleneckTagType(bottleneck.bottleneckType)"
                        size="small"
                        class="bottleneck-tag"
                      >
                        {{ bottleneck.typeLabel }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="no-bottlenecks">
                <el-icon class="no-bottlenecks-icon">
                  <CircleCheck />
                </el-icon>
                <p class="no-bottlenecks-text">未发现明显的性能瓶颈</p>
              </div>
            </el-card>

            <!-- 改进建议 -->
            <el-card class="suggestions-card">
              <template #header>
                <h3 class="card-title">
                  改进建议
                </h3>
              </template>

              <div class="suggestions-list">
                <div
                  v-for="(suggestion, index) in performanceSuggestions"
                  :key="index"
                  class="suggestion-item"
                  :class="`priority-${suggestion.priority || 'medium'}`"
                >
                  <div class="suggestion-header">
                    <div class="suggestion-title-section">
                      <el-icon class="suggestion-icon">
                        <HelpFilled />
                      </el-icon>
                      <h4 class="suggestion-title">{{ suggestion.title }}</h4>
                      <el-tag
                        :type="getPriorityTagType(suggestion.priority)"
                        size="small"
                        class="priority-tag"
                      >
                        {{ getPriorityLabel(suggestion.priority) }}
                      </el-tag>
                    </div>
                    <div class="suggestion-meta">
                      <span class="timeframe">{{ suggestion.timeframe }}</span>
                      <span class="cost">{{ suggestion.cost }}</span>
                    </div>
                  </div>

                  <div class="suggestion-content">
                    <p class="suggestion-description">{{ suggestion.description }}</p>

                    <div v-if="suggestion.methods" class="suggestion-methods">
                      <h5 class="methods-title">具体优化方法：</h5>
                      <ul class="methods-list">
                        <li v-for="(method, methodIndex) in suggestion.methods" :key="methodIndex">
                          {{ method }}
                        </li>
                      </ul>
                    </div>

                    <div v-if="suggestion.impact" class="suggestion-impact">
                      <el-icon class="impact-icon">
                        <TrendCharts />
                      </el-icon>
                      <span class="impact-text">{{ suggestion.impact }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <el-icon class="empty-icon">
              <TrendCharts />
            </el-icon>
            <h3 class="empty-title">
              开始性能分析
            </h3>
            <p class="empty-description">
              点击上方按钮开始分析您的流程性能
            </p>
          </div>
        </el-tab-pane>
        <el-tab-pane label="性能趋势分析" name="trend">
          <PerformanceTrends
            :key="performanceResult?.timestamp || 'initial'"
            :required-activities="currentConfig.requiredActivities"
            :trends="performanceResult?.trends || null"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- 性能分析配置对话框 -->
    <PerformanceAnalysisConfig
      v-model:visible="showConfigDialogVisible"
      :process-id="processId"
      :current-config="currentConfig"
      :read-only="isReadOnly"
      @apply="applyConfig"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  ArrowLeft,
  TrendCharts,
  Download,
  CircleCheck,
  HelpFilled,
  Refresh,
  Clock,
  Setting
} from '@element-plus/icons-vue'
import { useApi } from '~/utils/api'
import { AnalysisType, type PerformanceResult, type PerformanceAnalysisOptions } from '~/types'
import PerformanceAnalysisConfig from '~/components/analysis/PerformanceAnalysisConfig.vue'
import PerformanceTrends from '~/components/analysis/PerformanceTrends.vue'

// 页面配置
definePageMeta({
  layout: 'default'
})

const route = useRoute()
const processId = parseInt(route.params.processId as string)

// 设置页面标题
useHead({
  title: '性能分析 - ProMax'
})

// 读取流程状态与数据，计算权限
const processesStore = useProcessesStore()
const { data: process } = await useLazyAsyncData(`process-${processId}`, () => processesStore.fetchProcess(processId))
const { data: stats } = await useLazyAsyncData(`process-stats-${processId}`, () => useApi().getDataStatistics(processId), { default: () => null })

const status = computed(() => process.value?.status)
const hasData = computed(() => (stats.value?.totalEvents ?? 0) > 0)

// 页面入口规则：active/completed 允许；draft 需有数据才显示“开始性能分析”；archived 仅只读
const isReadOnly = computed(() => status.value === 'archived')
const canStartAnalysis = computed(() => {
  if (status.value === 'active' || status.value === 'completed') return true
  if (status.value === 'draft') return hasData.value
  if (status.value === 'archived') return false
  return false
})

// 状态
const isAnalyzing = ref(false)
const isLoadingFromCache = ref(false)
const performanceResult = ref<(PerformanceResult & { timestamp?: string; totalCases?: number; fromCache?: boolean }) | null>(null)
const activeTab = ref('static')

// 配置相关
const showConfigDialogVisible = ref(false)
const currentConfig = ref<PerformanceAnalysisOptions>({
  requiredActivities: [],
  forceRefresh: true // 默认打开强制刷新缓存
})

// 计算属性
const activityPerformanceData = computed(() => {
  if (!performanceResult.value?.activityStatistics) return []
  return performanceResult.value.activityStatistics.map((item: any) => ({
    ...item,
    performanceScore: calculatePerformanceScore(item)
  }))
})

const bottlenecks = computed(() => {
  if (!performanceResult.value?.bottlenecks) return []
  return performanceResult.value.bottlenecks.map((item: any) => ({
    ...item,
    description: item.reason || `该活动可能存在性能瓶颈`,
    avgWaitTime: item.avgWaitingTime || 0,
    typeLabel: getBottleneckTypeLabel(item.bottleneckType)
  }))
})

// 获取瓶颈类型标签
const getBottleneckTypeLabel = (type: string) => {
  const labels = {
    execution: '执行瓶颈',
    waiting: '等待瓶颈',
    combined: '综合瓶颈',
    resource: '资源瓶颈'
  }
  return labels[type as keyof typeof labels] || '瓶颈'
}

// 获取瓶颈标签类型
const getBottleneckTagType = (type: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const types = {
    execution: 'warning' as const,
    waiting: 'info' as const,
    combined: 'danger' as const,
    resource: 'danger' as const
  }
  return types[type as keyof typeof types] || 'danger'
}

// 获取优先级标签
const getPriorityLabel = (priority: string) => {
  const labels = {
    high: '高优先级',
    medium: '中优先级',
    low: '低优先级'
  }
  return labels[priority as keyof typeof labels] || '中优先级'
}

// 获取优先级标签类型
const getPriorityTagType = (priority: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const types = {
    high: 'danger' as const,
    medium: 'warning' as const,
    low: 'info' as const
  }
  return types[priority as keyof typeof types] || 'warning'
}

const totalCases = computed(() => {
  if (!performanceResult.value) return 0
  if (performanceResult.value?.totalCases) {
    return performanceResult.value.totalCases
  }
  // 从活动统计中估算案例总数
  if (performanceResult.value?.activityStatistics?.length > 0) {
    const maxFrequency = Math.max(...performanceResult.value.activityStatistics.map((a: any) => a.frequency))
    return maxFrequency
  }
  return 0
})

const performanceSuggestions = computed(() => {
  if (!performanceResult.value) return []

  const suggestions = []

  // 基于瓶颈类型生成具体建议
  if (bottlenecks.value.length > 0) {
    const executionBottlenecks = bottlenecks.value.filter(b => b.bottleneckType === 'execution' || b.bottleneckType === 'combined')
    const resourceBottlenecks = bottlenecks.value.filter(b => b.bottleneckType === 'resource')
    const waitingBottlenecks = bottlenecks.value.filter(b => b.bottleneckType === 'waiting')

    if (executionBottlenecks.length > 0) {
      const topBottleneck = executionBottlenecks[0]
      suggestions.push({
        title: '优化执行瓶颈活动',
        description: `"${topBottleneck.activity}"等${executionBottlenecks.length}个活动存在执行瓶颈`,
        methods: [
          '流程自动化：引入RPA或工作流自动化工具',
          '技能培训：提升操作人员的专业技能和效率',
          '工具优化：升级或更换低效的操作工具/系统',
          '标准化作业：建立标准操作程序(SOP)减少重复工作'
        ],
        impact: `预期减少 25-40% 的执行时间，节省人工成本 ${Math.round(executionBottlenecks.length * 15)}%`,
        priority: 'high',
        timeframe: '2-4个月',
        cost: '中等投入'
      })
    }

    if (resourceBottlenecks.length > 0) {
      const topResourceBottleneck = resourceBottlenecks[0]
      suggestions.push({
        title: '解决资源配置瓶颈',
        description: `"${topResourceBottleneck.activity}"等${resourceBottlenecks.length}个活动存在资源配置不足问题`,
        methods: [
          '增加人力资源：招聘或调配更多专业人员到瓶颈岗位',
          '设备扩容：增加关键设备或系统处理能力',
          '技能交叉培训：培养多技能人员，提高资源灵活性',
          '外包服务：将部分工作外包给专业服务商',
          '工作时间优化：延长关键资源的工作时间或实行轮班制'
        ],
        impact: `预期减少 40-60% 的等待时间，提升资源利用率 ${Math.round(resourceBottlenecks.length * 25)}%`,
        priority: 'high',
        timeframe: '1-2个月',
        cost: '中等到高投入'
      })
    }

    if (waitingBottlenecks.length > 0) {
      const topWaitingBottleneck = waitingBottlenecks[0]
      suggestions.push({
        title: '优化流程设计',
        description: `"${topWaitingBottleneck.activity}"等${waitingBottlenecks.length}个活动等待时间长但资源充足，可能存在流程设计问题`,
        methods: [
          '流程重设计：简化审批流程，减少不必要的等待环节',
          '并行处理：将串行活动改为并行执行',
          '依赖关系优化：减少活动间的强依赖关系',
          '决策权下放：将决策权下放到执行层，减少审批层级',
          '信息系统集成：提高系统间数据传递效率'
        ],
        impact: `预期减少 20-35% 的等待时间，提升流程效率 ${Math.round(waitingBottlenecks.length * 18)}%`,
        priority: 'medium',
        timeframe: '2-4个月',
        cost: '低到中等投入'
      })
    }
  }

  // 基于活动性能生成建议
  const poorActivities = activityPerformanceData.value.filter(a => a.performanceScore === 'poor')
  if (poorActivities.length > 0) {
    suggestions.push({
      title: '改进低效活动',
      description: `${poorActivities.length}个活动性能较差，需要系统性改进`,
      methods: [
        '流程重设计：简化或合并低效环节',
        '数字化转型：采用数字化工具替代手工操作',
        '质量管控：减少返工和错误率',
        '绩效激励：建立基于效率的激励机制'
      ],
      impact: `预期提升 20-35% 的处理效率，减少 ${Math.round(poorActivities.length * 10)}% 的操作错误`,
      priority: 'medium',
      timeframe: '3-6个月',
      cost: '中等到高投入'
    })
  }

  // 基于案例统计生成建议
  if (performanceResult.value.caseStatistics) {
    const stats = performanceResult.value.caseStatistics
    const variationCoeff = (stats.maxDuration - stats.minDuration) / stats.avgDuration

    if (variationCoeff > 2) {
      suggestions.push({
        title: '标准化流程执行',
        description: '案例处理时间差异较大，需要标准化流程执行',
        methods: [
          '制定标准作业程序：建立详细的操作指南',
          '培训统一化：确保所有操作人员按相同标准执行',
          '质量检查点：在关键节点设置质量控制检查',
          '异常处理机制：建立标准的异常情况处理流程'
        ],
        impact: `预期减少 40-60% 的处理时间变异，提升流程一致性 25%`,
        priority: 'medium',
        timeframe: '2-4个月',
        cost: '低投入'
      })
    }
  }

  // 默认建议
  if (suggestions.length === 0) {
    suggestions.push({
      title: '持续优化监控',
      description: '当前流程性能良好，建议建立持续改进机制',
      methods: [
        '定期性能监控：建立月度/季度性能分析报告',
        '基准对比：与行业最佳实践进行对比分析',
        '预防性维护：定期检查和优化关键流程环节',
        '创新实验：小规模试点新的优化方案'
      ],
      impact: '维持当前高效水平，预防性能退化，持续提升 5-10%',
      priority: 'low',
      timeframe: '持续进行',
      cost: '低投入'
    })
  }

  return suggestions
})

// ECharts图表配置
const durationChartOption = computed(() => {
  if (!performanceResult.value?.caseStatistics) return null

  // 生成持续时间分布数据
  const generateDurationDistribution = () => {
    const { minDuration, maxDuration, avgDuration } = performanceResult.value!.caseStatistics

    // 生成模拟的案例持续时间数据
    const mockDurations = generateMockCaseDurations(totalCases.value, avgDuration, 0.5)

    // 使用智能时间区间算法
    const timeDistribution = generateSmartTimeDistribution(mockDurations)

    // 转换为ECharts需要的格式
    return timeDistribution.intervals.map(interval => ({
      name: interval.range,
      value: interval.count,
      percentage: interval.percentage
    }))
  }

  const distributionData = generateDurationDistribution()

  return {
    title: {
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#374151'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const data = params[0]
        return `${data.name}<br/>案例数量: ${data.value}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: distributionData.map(item => item.name),
      axisLabel: {
        rotate: 45,
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      name: '案例数量',
      nameTextStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '案例数量',
        type: 'bar',
        data: distributionData.map(item => item.value),
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#60a5fa' },
              { offset: 1, color: '#3b82f6' }
            ]
          }
        },
        emphasis: {
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#93c5fd' },
                { offset: 1, color: '#60a5fa' }
              ]
            }
          }
        }
      }
    ]
  }
})

// 开始性能分析
const api = useApi()
const startAnalysis = async (options: PerformanceAnalysisOptions = currentConfig.value) => {
  isAnalyzing.value = true

  try {
    const result = await api.analyzePerformance(processId, options)

    // 更新当前配置为实际使用的配置
    if (result.options) {
      currentConfig.value = {
        requiredActivities: result.options.requiredActivities || [],
        forceRefresh: result.options.forceRefresh || false
      }

      performanceResult.value = {
        ...result,
        timestamp: result.options.forceRefresh ? new Date().toISOString() : undefined,
        fromCache: !result.options.forceRefresh || false
      }
    } else {
      performanceResult.value = {
        ...result,
        timestamp: new Date().toISOString(),
        fromCache: false // 新分析的结果不是来自缓存
      }
    }

    ElMessage.success('性能分析完成！')
  } catch (error: any) {
    ElMessage.error(error.data?.message || '性能分析失败')
  } finally {
    isAnalyzing.value = false
  }
}

// 显示配置对话框
const showConfigDialog = () => {
  showConfigDialogVisible.value = true
}

// 应用配置
const applyConfig = async (config: PerformanceAnalysisOptions) => {
  // 不要在分析前提前更新 currentConfig，避免触发趋势组件的 watcher 导致二次请求
  await startAnalysis(config)
}

// 计算性能评分（综合考虑执行时间和等待时间）
const calculatePerformanceScore = (activity: any) => {
  const avgDuration = activity.avgDuration || 0
  const avgWaitingTime = activity.avgWaitingTime || 0

  // 将毫秒转换为小时进行评分
  const durationHours = avgDuration / (1000 * 60 * 60)
  const waitingHours = avgWaitingTime / (1000 * 60 * 60)

  // 计算执行效率分数（0-100）
  const executionScore = calculateExecutionScore(durationHours)

  // 计算等待效率分数（0-100）
  const waitingScore = calculateWaitingScore(waitingHours)

  // 综合评分：执行时间权重60%，等待时间权重40%
  const compositeScore = executionScore * 0.6 + waitingScore * 0.4

  // 根据综合分数确定等级
  if (compositeScore >= 85) return 'excellent'
  if (compositeScore >= 70) return 'good'
  if (compositeScore >= 50) return 'average'
  return 'poor'
}

// 计算执行效率分数
const calculateExecutionScore = (durationHours: number): number => {
  // 基于执行时间的评分标准
  if (durationHours <= 0.5) return 100      // 30分钟以内：优秀
  if (durationHours <= 2) return 85         // 2小时以内：良好
  if (durationHours <= 8) return 70         // 8小时以内：一般
  if (durationHours <= 24) return 50        // 24小时以内：较差
  if (durationHours <= 72) return 30        // 3天以内：差
  return 10                                  // 超过3天：很差
}

// 计算等待效率分数
const calculateWaitingScore = (waitingHours: number): number => {
  // 基于等待时间的评分标准
  if (waitingHours === 0) return 100        // 无等待：优秀
  if (waitingHours <= 1) return 90          // 1小时以内：优秀
  if (waitingHours <= 4) return 75          // 4小时以内：良好
  if (waitingHours <= 24) return 55         // 24小时以内：一般
  if (waitingHours <= 72) return 35         // 3天以内：较差
  return 15                                  // 超过3天：很差
}

// 获取性能等级文本
const getPerformanceLevel = (score: string) => {
  const levels = {
    excellent: '优秀',
    good: '良好',
    average: '一般',
    poor: '较差'
  }
  return levels[score as keyof typeof levels] || '未知'
}

// 获取性能评分数值
const getPerformanceScoreValue = (activity: any): number => {
  const avgDuration = activity.avgDuration || 0
  const avgWaitingTime = activity.avgWaitingTime || 0

  const durationHours = avgDuration / (1000 * 60 * 60)
  const waitingHours = avgWaitingTime / (1000 * 60 * 60)

  const executionScore = calculateExecutionScore(durationHours)
  const waitingScore = calculateWaitingScore(waitingHours)

  return Math.round(executionScore * 0.6 + waitingScore * 0.4)
}

// 获取性能问题描述
const getPerformanceIssues = (activity: any): string => {
  const avgDuration = activity.avgDuration || 0
  const avgWaitingTime = activity.avgWaitingTime || 0

  const durationHours = avgDuration / (1000 * 60 * 60)
  const waitingHours = avgWaitingTime / (1000 * 60 * 60)

  const issues = []

  // 检查执行时间问题
  if (durationHours > 24) {
    issues.push('执行时间过长')
  } else if (durationHours > 8) {
    issues.push('执行效率偏低')
  }

  // 检查等待时间问题
  if (waitingHours > 24) {
    issues.push('等待时间过长')
  } else if (waitingHours > 4) {
    issues.push('资源瓶颈')
  }

  return issues.length > 0 ? issues.join(', ') : '无明显问题'
}

// 获取性能标签类型
const getPerformanceTagType = (score: string) => {
  const types = {
    excellent: 'success',
    good: 'primary',
    average: 'warning',
    poor: 'danger'
  }
  return types[score as keyof typeof types] || 'info'
}

// 格式化持续时间
const formatDuration = (milliseconds: number | undefined) => {
  if (!milliseconds || isNaN(milliseconds)) {
    return '0分钟'
  }

  // 将毫秒转换为小时
  const hours = milliseconds / (1000 * 60 * 60)

  if (hours < 1) {
    const minutes = Math.round(hours * 60)
    return `${minutes}分钟`
  } else if (hours < 24) {
    return `${hours.toFixed(1)}小时`
  } else {
    const days = Math.floor(hours / 24)
    const remainingHours = Math.round(hours % 24)
    return `${days}天${remainingHours}小时`
  }
}

// 导出结果
const exportResult = () => {
  if (!performanceResult.value) return

  const dataStr = JSON.stringify(performanceResult.value, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)

  const link = document.createElement('a')
  link.href = url
  link.download = `performance-analysis-${processId}-${Date.now()}.json`
  link.click()

  URL.revokeObjectURL(url)
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 页面加载时检查是否有已存在的分析结果
onMounted(async () => {
  isLoadingFromCache.value = true
  try {
    const result = await api.getAnalysisResult(processId, AnalysisType.PERFORMANCE_ANALYSIS)
    if (result) {
      performanceResult.value = {
        ...result,
        fromCache: true // 标记为来自缓存的结果
      } as any

      // 从缓存结果中更新当前配置
      if (result.options) {
        currentConfig.value = {
          requiredActivities: result.options.requiredActivities || [],
          forceRefresh: result.options.forceRefresh || false
        }
      }

      ElMessage.success('已加载缓存的性能分析结果')
    }
  } catch {
    // 没有找到已存在的结果，这是正常的
  } finally {
    isLoadingFromCache.value = false
  }

  // 确保配置默认值正确设置
  if (currentConfig.value.forceRefresh === undefined) {
    currentConfig.value.forceRefresh = true
  }
})

// 生成模拟案例持续时间数据
const generateMockCaseDurations = (count: number, avgDuration: number, variationCoeff: number): number[] => {
  const durations: number[] = []

  for (let i = 0; i < count; i++) {
    // 使用对数正态分布生成更真实的时间数据
    const randomFactor = Math.exp((Math.random() - 0.5) * variationCoeff * 2)
    const duration = Math.max(avgDuration * randomFactor, avgDuration * 0.1) // 最小值为基准的10%
    durations.push(Math.round(duration))
  }

  return durations.sort((a, b) => a - b)
}

// 生成智能时间分布
const generateSmartTimeDistribution = (durations: number[]) => {
  if (durations.length === 0) {
    return {
      intervals: [],
      quartiles: { q1: 0, q2: 0, q3: 0 }
    }
  }

  const min = Math.min(...durations)
  const max = Math.max(...durations)
  const range = max - min

  // 根据数据范围选择合适的区间数量
  let intervalCount = 8
  if (durations.length < 20) {
    intervalCount = 5
  } else if (durations.length > 100) {
    intervalCount = 12
  }

  // 计算合适的区间大小
  const rawStep = range / intervalCount
  const step = roundToNiceNumber(rawStep)

  // 调整起始点到合适的整数
  const adjustedMin = Math.floor(min / step) * step
  const adjustedMax = Math.ceil(max / step) * step

  const intervals: Array<{ range: string; count: number; percentage: number }> = []

  for (let start = adjustedMin; start < adjustedMax; start += step) {
    const end = start + step
    const count = durations.filter(d => d >= start && d < end).length

    // 只包含有数据的区间
    if (count > 0 || intervals.length === 0) {
      intervals.push({
        range: `${formatDuration(start)} - ${formatDuration(end)}`,
        count,
        percentage: (count / durations.length) * 100,
      })
    }
  }

  // 计算四分位数
  const sortedDurations = [...durations].sort((a, b) => a - b)
  const quartiles = {
    q1: sortedDurations[Math.floor(sortedDurations.length * 0.25)],
    q2: sortedDurations[Math.floor(sortedDurations.length * 0.5)],
    q3: sortedDurations[Math.floor(sortedDurations.length * 0.75)],
  }

  return {
    intervals,
    quartiles
  }
}

// 将数字四舍五入到合适的"好看"数字
const roundToNiceNumber = (value: number): number => {
  const magnitude = Math.pow(10, Math.floor(Math.log10(value)))
  const normalized = value / magnitude

  let nice: number
  if (normalized <= 1) {
    nice = 1
  } else if (normalized <= 2) {
    nice = 2
  } else if (normalized <= 5) {
    nice = 5
  } else {
    nice = 10
  }

  return nice * magnitude
}
</script>

<style lang="scss" scoped>
.performance-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @media (max-width: 768px) {
    padding: 1rem 0.5rem;
  }
}

.page-header {
  max-width: 1400px;
  margin: 0 auto 3rem auto;
  animation: fadeInUp 0.6s ease-out;

  .header-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;

    .back-button {
      color: #6b7280;
      transition: all 0.2s ease;
      border-radius: 50%;
      width: 40px;
      height: 40px;

      &:hover {
        color: #3b82f6;
        background-color: rgba(59, 130, 246, 0.1);
        transform: translateX(-2px);
      }

      :global(.dark) & {
        color: #9ca3af;

        &:hover {
          color: #60a5fa;
          background-color: rgba(96, 165, 250, 0.1);
        }
      }
    }

    .title {
      font-size: 2.25rem;
      font-weight: 800;
      background: linear-gradient(135deg, #3b82f6, #8b5cf6);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin: 0;
      letter-spacing: -0.025em;

      :global(.dark) & {
        background: linear-gradient(135deg, #60a5fa, #a78bfa);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  .subtitle {
    color: #64748b;
    font-size: 1.125rem;
    font-weight: 500;
    margin: 0;
    opacity: 0.8;

    :global(.dark) & {
      color: #94a3b8;
    }
  }
}

.control-panel {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  animation: slideInLeft 0.5s ease-out 0.2s both;

  :deep(.el-card) {
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    :global(.dark) & {
      background: rgba(30, 41, 59, 0.95);
      border: 1px solid rgba(71, 85, 105, 0.3);
    }
  }

  .control-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    @media (min-width: 640px) {
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }

    .control-actions {
      display: flex;
      align-items: center;
      gap: 1rem;

      .analysis-button {
        border-radius: 12px;
        font-weight: 600;
        padding: 12px 24px;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        border: none;
        box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
        transition: all 0.2s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
        }

        :global(.dark) & {
          background: linear-gradient(135deg, #60a5fa, #a78bfa);
        }
      }

      .export-button {
        border-radius: 12px;
        font-weight: 600;
        padding: 12px 20px;
        transition: all 0.2s ease;
        border: 2px solid #e5e7eb;
        color: #6b7280;

        &:hover {
          border-color: #3b82f6;
          color: #3b82f6;
          transform: translateY(-1px);
        }

        :global(.dark) & {
          border-color: #4b5563;
          color: #9ca3af;

          &:hover {
            border-color: #60a5fa;
            color: #60a5fa;
          }
        }
      }

      .config-button {
        border-radius: 12px;
        font-weight: 600;
        padding: 12px 20px;
        transition: all 0.2s ease;
        border: 2px solid #e5e7eb;
        color: #6b7280;

        &:hover {
          border-color: #10b981;
          color: #10b981;
          transform: translateY(-1px);
        }

        :global(.dark) & {
          border-color: #4b5563;
          color: #9ca3af;

          &:hover {
            border-color: #34d399;
            color: #34d399;
          }
        }
      }
    }

    .result-info {
      display: flex;
      align-items: center;
      gap: 1rem;
      flex-wrap: wrap;
    }

    .cache-status {
      .el-tag {
        display: flex;
        align-items: center;
        gap: 0.25rem;
      }
    }

    .last-updated {
      font-size: 0.875rem;
      color: #6b7280;
      font-weight: 500;

      :global(.dark) & {
        color: #9ca3af;
      }
    }
  }
}

.analyzing-state {
  text-align: center;
  padding: 4rem 1rem;
  max-width: 1400px;
  margin: 0 auto;

  .loading-spinner {
    width: 2.5rem;
    height: 2.5rem;
    border: 3px solid rgba(59, 130, 246, 0.2);
    border-top-color: #3b82f6;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    animation: spin 1s linear infinite;

    :global(.dark) & {
      border-color: rgba(96, 165, 250, 0.2);
      border-top-color: #60a5fa;
    }
  }

  .analyzing-text {
    color: #64748b;
    font-size: 1.125rem;
    font-weight: 500;
    margin: 0;

    :global(.dark) & {
      color: #94a3b8;
    }
  }
}

.analysis-results {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  animation: slideInUp 0.5s ease-out 0.4s both;

  // 统一卡片样式
  :deep(.el-card) {
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    :global(.dark) & {
      background: rgba(30, 41, 59, 0.95);
      border: 1px solid rgba(71, 85, 105, 0.3);
    }

    .el-card__header {
      padding: 1.5rem 1.5rem  0.5rem 1.5rem;
      border-bottom: none;
    }

    .el-card__body {
      padding: 1rem 1.5rem 1.5rem 1.5rem;
    }
  }

  .card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #111827;
    margin: 0;

    :global(.dark) & {
      color: #ffffff;
    }
  }
}

.metrics-card {
  .metrics-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;

    @media (min-width: 640px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 1024px) {
      grid-template-columns: repeat(4, 1fr);
    }

    .metric-item {
      text-align: center;
      padding: 1.5rem;
      border-radius: 12px;
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
      }

      &:hover {
        transform: translateY(-2px);
      }

      &.metric-avg {
        background: rgba(59, 130, 246, 0.05);
        border: 1px solid rgba(59, 130, 246, 0.2);

        &::before {
          background: linear-gradient(90deg, #3b82f6, #60a5fa);
        }

        .metric-value {
          color: #3b82f6;
        }
      }

      &.metric-min {
        background: rgba(16, 185, 129, 0.05);
        border: 1px solid rgba(16, 185, 129, 0.2);

        &::before {
          background: linear-gradient(90deg, #10b981, #34d399);
        }

        .metric-value {
          color: #10b981;
        }
      }

      &.metric-max {
        background: rgba(245, 158, 11, 0.05);
        border: 1px solid rgba(245, 158, 11, 0.2);

        &::before {
          background: linear-gradient(90deg, #f59e0b, #fbbf24);
        }

        .metric-value {
          color: #f59e0b;
        }
      }

      &.metric-total {
        background: rgba(139, 92, 246, 0.05);
        border: 1px solid rgba(139, 92, 246, 0.2);

        &::before {
          background: linear-gradient(90deg, #8b5cf6, #a78bfa);
        }

        .metric-value {
          color: #8b5cf6;
        }
      }

      .metric-value {
        font-size: 2rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        line-height: 1;
      }

      .metric-label {
        font-size: 0.875rem;
        color: #6b7280;
        font-weight: 500;

        :global(.dark) & {
          color: #9ca3af;
        }
      }
    }
  }
}

.chart-card {
  .chart-container {
    height: 20rem;
    border: 1px solid rgba(226, 232, 240, 0.5);
    border-radius: 12px;
    overflow: hidden;
    position: relative;

    :global(.dark) & {
      border-color: rgba(75, 85, 99, 0.5);
    }

    .chart-content {
      width: 100%;
      height: 100%;
      background: rgba(248, 250, 252, 0.5);

      :global(.dark) & {
        background: rgba(55, 65, 81, 0.5);
      }
    }

    .chart-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(248, 250, 252, 0.5);

      :global(.dark) & {
        background: rgba(55, 65, 81, 0.5);
      }

      .placeholder-content {
        text-align: center;

        .placeholder-icon {
          width: 3rem;
          height: 3rem;
          color: #cbd5e1;
          margin: 0 auto 1rem;

          :global(.dark) & {
            color: #64748b;
          }
        }

        .placeholder-text {
          color: #64748b;
          font-size: 0.875rem;
          margin: 0;

          :global(.dark) & {
            color: #94a3b8;
          }
        }
      }
    }
  }
}

.table-card {
  .table-container {
    :deep(.performance-table) {
      .el-table__header {
        background: rgba(248, 250, 252, 0.8);

        :global(.dark) & {
          background: rgba(55, 65, 81, 0.8);
        }

        th {
          font-weight: 600;
          color: #374151;

          :global(.dark) & {
            color: #d1d5db;
          }
        }
      }

      .el-table__row {
        &:hover {
          background: rgba(59, 130, 246, 0.05);

          :global(.dark) & {
            background: rgba(96, 165, 250, 0.05);
          }
        }
      }

      .performance-score-cell {
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        gap: 0.5rem;
        white-space: nowrap;

        .score-value {
          font-size: 0.875rem;
          font-weight: 600;
          color: #374151;

          :global(.dark) & {
            color: #e5e7eb;
          }
        }
      }

      .performance-issues {
        font-size: 0.8rem;
        color: #6b7280;
        line-height: 1.3;

        :global(.dark) & {
          color: #d1d5db;
        }
      }
    }
  }
}

.bottlenecks-card {
  .bottlenecks-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .bottleneck-item {
      padding: 1.5rem;
      border: 1px solid rgba(239, 68, 68, 0.2);
      background: rgba(239, 68, 68, 0.05);
      border-radius: 12px;
      transition: all 0.2s ease;

      &:hover {
        transform: translateX(4px);
        border-color: rgba(239, 68, 68, 0.3);
        background: rgba(239, 68, 68, 0.08);
      }

      .bottleneck-content {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        gap: 1rem;

        .bottleneck-info {
          flex: 1;

          .bottleneck-title {
            font-weight: 600;
            color: #dc2626;
            margin: 0 0 0.5rem 0;

            :global(.dark) & {
              color: #f87171;
            }
          }

          .bottleneck-description {
            font-size: 0.875rem;
            color: #dc2626;
            margin: 0 0 0.75rem 0;
            opacity: 0.8;

            :global(.dark) & {
              color: #fca5a5;
            }
          }

          .bottleneck-metrics {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            margin-top: 0.5rem;
          }

          .bottleneck-metric {
            font-size: 0.75rem;
            color: #dc2626;
            font-weight: 500;

            :global(.dark) & {
              color: #fecaca;
            }
          }

          .bottleneck-tags {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.25rem;
          }
        }

        .bottleneck-tag {
          flex-shrink: 0;
        }
      }
    }
  }

  .no-bottlenecks {
    text-align: center;
    padding: 3rem 1rem;

    .no-bottlenecks-icon {
      width: 3rem;
      height: 3rem;
      color: #10b981;
      margin: 0 auto 1rem;
    }

    .no-bottlenecks-text {
      color: #64748b;
      font-weight: 500;
      margin: 0;

      :global(.dark) & {
        color: #94a3b8;
      }
    }
  }
}

.suggestions-card {
  .suggestions-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .suggestion-item {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      padding: 1.5rem;
      background: rgba(59, 130, 246, 0.05);
      border: 1px solid rgba(59, 130, 246, 0.2);
      border-radius: 12px;
      transition: all 0.2s ease;

      &.priority-high {
        border-left: 4px solid #ef4444;
        background: rgba(239, 68, 68, 0.05);
      }

      &.priority-medium {
        border-left: 4px solid #f59e0b;
        background: rgba(245, 158, 11, 0.05);
      }

      &.priority-low {
        border-left: 4px solid #10b981;
        background: rgba(16, 185, 129, 0.05);
      }

      &:hover {
        transform: translateX(4px);
        background: rgba(59, 130, 246, 0.08);
        border-color: rgba(59, 130, 246, 0.3);
      }

      .suggestion-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 1rem;

        .suggestion-title-section {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          flex: 1;

          .suggestion-icon {
            width: 1.25rem;
            height: 1.25rem;
            color: #3b82f6;
            flex-shrink: 0;

            :global(.dark) & {
              color: #60a5fa;
            }
          }

          .suggestion-title {
            font-weight: 600;
            color: #1e40af;
            margin: 0;
            flex: 1;

            :global(.dark) & {
              color: #93c5fd;
            }
          }

          .priority-tag {
            flex-shrink: 0;
          }
        }

        .suggestion-meta {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 0.25rem;
          font-size: 0.75rem;
          color: #6b7280;

          :global(.dark) & {
            color: #d1d5db;
          }

          .timeframe {
            font-weight: 500;
          }

          .cost {
            opacity: 0.8;
          }
        }
      }

      .suggestion-content {
        .suggestion-description {
          font-size: 0.875rem;
          color: #1e40af;
          margin: 0 0 1rem 0;
          line-height: 1.5;
          opacity: 0.8;

          :global(.dark) & {
            color: #bfdbfe;
          }
        }

        .suggestion-methods {
          margin-bottom: 1rem;

          .methods-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin: 0 0 0.5rem 0;

            :global(.dark) & {
              color: #e5e7eb;
            }
          }

          .methods-list {
            margin: 0;
            padding-left: 1.25rem;

            li {
              font-size: 0.8rem;
              color: #4b5563;
              line-height: 1.5;
              margin-bottom: 0.25rem;

              :global(.dark) & {
                color: #d1d5db;
              }
            }
          }
        }

        .suggestion-impact {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: 0.8rem;
          color: #059669;
          font-weight: 500;
          padding: 0.75rem;
          background: rgba(5, 150, 105, 0.1);
          border-radius: 8px;
          border: 1px solid rgba(5, 150, 105, 0.2);

          .impact-icon {
            font-size: 1rem;
            flex-shrink: 0;
          }

          .impact-text {
            line-height: 1.4;
          }

          :global(.dark) & {
            color: #34d399;
            background: rgba(52, 211, 153, 0.1);
            border-color: rgba(52, 211, 153, 0.2);
          }
        }
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 4rem 1rem;
  max-width: 1400px;
  margin: 0 auto;

  .empty-icon {
    width: 4rem;
    height: 4rem;
    color: #cbd5e1;
    margin: 0 auto 1.5rem;

    :global(.dark) & {
      color: #64748b;
    }
  }

  .empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    margin: 0 0 0.5rem 0;

    :global(.dark) & {
      color: #ffffff;
    }
  }

  .empty-description {
    color: #64748b;
    margin: 0;

    :global(.dark) & {
      color: #94a3b8;
    }
  }
}

// 动画定义
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 卡片动画
.analysis-results > :deep(.el-card) {
  animation: slideInUp 0.5s ease-out;
  animation-fill-mode: both;

  @for $i from 1 through 6 {
    &:nth-child(#{$i}) {
      animation-delay: #{0.4 + $i * 0.1}s;
    }
  }
}

.analysis-tabs {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  .result-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 1.5rem;
    }
  }
}
</style>
