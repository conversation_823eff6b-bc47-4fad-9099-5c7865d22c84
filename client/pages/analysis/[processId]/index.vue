<template>
  <div class="process-analysis-page">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <p class="loading-text">加载中...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <el-icon class="error-icon">
        <WarningFilled />
      </el-icon>
      <h2 class="error-title">加载失败</h2>
      <p class="error-message">{{ error }}</p>
      <el-button @click="navigateTo('/analysis')">
        返回数据分析
      </el-button>
    </div>

    <!-- 主要内容 -->
    <div v-else class="analysis-content">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <el-button
            :icon="ArrowLeft"
            text
            class="back-button"
            @click="navigateTo('/analysis')"
          />
          <div class="title-section">
            <h1 class="page-title">{{ process?.name }}</h1>
            <div class="status-section">
              <span
                class="status-indicator"
                :class="`status-${process?.status}`"
              ></span>
              <span class="status-text">{{ getStatusText(process?.status) }}</span>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            :icon="Edit"
            @click="navigateTo(`/processes/${processId}`)"
          >
            流程详情
          </el-button>
        </div>
      </div>

      <!-- 分析模块网格 -->
      <div class="analysis-modules">
        <div class="modules-grid">
          <!-- 数据上传模块 -->
          <el-card class="module-card upload-module">
            <template #header>
              <div class="module-header">
                <el-icon class="module-icon upload-icon">
                  <Upload />
                </el-icon>
                <div class="module-info">
                  <h3 class="module-title">数据上传</h3>
                  <p class="module-description">上传事件日志文件开始分析</p>
                </div>
              </div>
            </template>

            <div class="module-content">
              <p class="module-detail">
                支持 CSV/XES 等日志上传，构建规范、完整的数据基础。
              </p>
              <div class="module-actions">
                <el-button
                  type="primary"
                  :icon="Upload"
                  :aria-disabled="!canUpload"
                  :class="{ 'guard-disabled': !canUpload }"
                  @click="onUploadClick"
                >
                  开始上传
                </el-button>
              </div>
            </div>
          </el-card>

          <!-- 流程发现模块 -->
          <el-card class="module-card discover-module">
            <template #header>
              <div class="module-header">
                <el-icon class="module-icon discover-icon">
                  <Search />
                </el-icon>
                <div class="module-info">
                  <h3 class="module-title">流程发现</h3>
                  <p class="module-description">自动发现业务流程模型</p>
                </div>
              </div>
            </template>

            <div class="module-content">
              <p class="module-detail">
                基于事件日志自动生成 DFG 与模型，揭示真实业务路径。
              </p>
              <div class="module-actions">
                <el-button
                  type="primary"
                  :icon="Search"
                  @click="navigateTo(`/analysis/${processId}/discover`)"
                >
                  开始发现
                </el-button>
              </div>
            </div>
          </el-card>

          <!-- 性能分析模块 -->
          <el-card class="module-card performance-module">
            <template #header>
              <div class="module-header">
                <el-icon class="module-icon performance-icon">
                  <TrendCharts />
                </el-icon>
                <div class="module-info">
                  <h3 class="module-title">性能分析</h3>
                  <p class="module-description">分析流程执行性能指标</p>
                </div>
              </div>
            </template>

            <div class="module-content">
              <p class="module-detail">
                分析时长与瓶颈、资源利用，量化关键性能指标。
              </p>
              <div class="module-actions">
                <el-button
                  type="primary"
                  :icon="TrendCharts"
                  @click="navigateTo(`/analysis/${processId}/performance`)"
                >
                  开始分析
                </el-button>
              </div>
            </div>
          </el-card>

          <!-- 子流程发现模块 -->
          <el-card class="module-card subprocess-module">
            <template #header>
              <div class="module-header">
                <el-icon class="module-icon subprocess-icon">
                  <Share />
                </el-icon>
                <div class="module-info">
                  <h3 class="module-title">子流程发现</h3>
                  <p class="module-description">自动识别复杂流程中的子流程</p>
                </div>
              </div>
            </template>

            <div class="module-content">
              <p class="module-detail">
                识别重复序列与子流程模式，简化结构并提升可读性。
              </p>
              <div class="module-actions">
                <el-button
                  type="primary"
                  :icon="Share"
                  @click="navigateTo(`/analysis/${processId}/subprocess`)"
                >
                  开始发现
                </el-button>
              </div>
            </div>
          </el-card>

          <!-- 符合性检查模块 -->
          <el-card class="module-card conformance-module">
            <template #header>
              <div class="module-header">
                <el-icon class="module-icon conformance-icon">
                  <CircleCheck />
                </el-icon>
                <div class="module-info">
                  <h3 class="module-title">符合性检查</h3>
                  <p class="module-description">检查流程执行符合性</p>
                </div>
              </div>
            </template>

            <div class="module-content">
              <p class="module-detail">
                对比事件日志与 BPMN 模型，定位偏差并评估符合性。
              </p>
              <div class="module-actions">
                <el-button
                  type="primary"
                  :icon="CircleCheck"
                  @click="navigateTo(`/conformance/check?processId=${processId}`)"
                >
                  开始检查
                </el-button>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 流程信息 -->
      <div class="process-info">
        <el-card>
          <template #header>
            <h3 class="card-title">流程信息</h3>
          </template>

          <div class="info-grid">
            <div class="info-item">
              <label class="info-label">流程名称</label>
              <p class="info-value">{{ process?.name }}</p>
            </div>

            <div v-if="process?.description" class="info-item">
              <label class="info-label">流程描述</label>
              <p class="info-value">{{ process?.description }}</p>
            </div>

            <div v-if="process?.businessDomain" class="info-item">
              <label class="info-label">业务领域</label>
              <p class="info-value">{{ process?.businessDomain }}</p>
            </div>

            <div class="info-item">
              <label class="info-label">创建时间</label>
              <p class="info-value">{{ formatDate(process?.createdAt) }}</p>
            </div>

            <div class="info-item">
              <label class="info-label">最后更新</label>
              <p class="info-value">{{ formatDate(process?.updatedAt) }}</p>
            </div>

            <div v-if="process?.user" class="info-item">
              <label class="info-label">创建者</label>
              <p class="info-value">{{ process?.user.username }}</p>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ArrowLeft,
  Edit,
  Upload,
  Search,
  TrendCharts,
  WarningFilled,
  CircleCheck,
  Share
} from '@element-plus/icons-vue'

// 页面参数
const route = useRoute()
const processId = parseInt(route.params.processId as string)

// 页面元数据
definePageMeta({
  title: '流程数据分析',
  description: '流程挖掘数据分析'
})

// 使用 stores
const processesStore = useProcessesStore()

// 获取流程详情
const { data: process, pending: isLoading, error } = await useLazyAsyncData(
  `process-analysis-${processId}`,
  () => processesStore.fetchProcess(processId)
)

// 状态文本映射
const getStatusText = (status?: string): string => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    active: '活跃',
    completed: '已完成',
    archived: '已归档'
  }
  if (!status) return '-'
  return statusMap[status] || status
}

// 格式化日期
const formatDate = (date?: string | null): string => {
  if (!date) return '-'
  const d = new Date(date)
  if (isNaN(d.getTime())) return '-'
  return d.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 设置页面标题
useHead({
  title: computed(() => process.value ? `${process.value.name} - 数据分析` : '流程数据分析')
})

// 仅草稿/活跃可上传数据
const canUpload = computed(() => {
  const s = process.value?.status
  return s === 'draft' || s === 'active'
})

const onUploadClick = () => {
  if (!process.value) return
  if (canUpload.value) {
    navigateTo(`/analysis/${process.value.id}/upload`)
  } else {
    ElMessage.error('当前状态不允许上传数据，仅草稿或活跃状态可上传')
  }
}
</script>

<style lang="scss" scoped>
.process-analysis-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @media (max-width: 768px) {
    padding: 1rem 0.5rem;
  }
}

.loading-state {
  text-align: center;
  padding: 4rem 1rem;
  max-width: 1200px;
  margin: 0 auto;

  .loading-spinner {
    width: 2.5rem;
    height: 2.5rem;
    border: 3px solid rgba(59, 130, 246, 0.2);
    border-top-color: #3b82f6;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    animation: spin 1s linear infinite;

    :global(.dark) & {
      border-color: rgba(96, 165, 250, 0.2);
      border-top-color: #60a5fa;
    }
  }

  .loading-text {
    color: #64748b;
    font-size: 1rem;
    margin: 0;

    :global(.dark) & {
      color: #94a3b8;
    }
  }
}

.error-state {
  text-align: center;
  padding: 4rem 1rem;
  max-width: 1200px;
  margin: 0 auto;

  .error-icon {
    width: 4rem;
    height: 4rem;
    color: #ef4444;
    margin: 0 auto 1.5rem;
  }

  .error-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #111827;
    margin: 0 0 0.5rem 0;

    :global(.dark) & {
      color: #ffffff;
    }
  }

  .error-message {
    color: #64748b;
    margin: 0 0 2rem 0;

    :global(.dark) & {
      color: #94a3b8;
    }
  }
}

.analysis-content {
  max-width: 1200px;
  margin: 0 auto;
  animation: fadeInUp 0.6s ease-out;
}

.page-header {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 3rem;

  @media (min-width: 768px) {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
  }

  .header-content {
    display: flex;
    align-items: center;
    gap: 1rem;

    .back-button {
      color: #6b7280;
      transition: all 0.2s ease;
      border-radius: 50%;
      width: 40px;
      height: 40px;

      &:hover {
        color: #3b82f6;
        background-color: rgba(59, 130, 246, 0.1);
        transform: translateX(-2px);
      }

      :global(.dark) & {
        color: #9ca3af;

        &:hover {
          color: #60a5fa;
          background-color: rgba(96, 165, 250, 0.1);
        }
      }
    }

    .title-section {
      .page-title {
        font-size: 2.25rem;
        font-weight: 800;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0 0 0.5rem 0;
        letter-spacing: -0.025em;

        :global(.dark) & {
          background: linear-gradient(135deg, #60a5fa, #a78bfa);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .status-section {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .status-text {
          font-size: 0.875rem;
          color: #6b7280;
          font-weight: 500;

          :global(.dark) & {
            color: #9ca3af;
          }
        }
      }
    }
  }

  .header-actions {
    :deep(.el-button) {
      border-radius: 12px;
      font-weight: 600;
      padding: 10px 20px;
      transition: all 0.2s ease;
      border: 2px solid #e5e7eb;
      color: #6b7280;

      &:hover {
        border-color: #3b82f6;
        color: #3b82f6;
        transform: translateY(-1px);
      }

      :global(.dark) & {
        border-color: #4b5563;
        color: #9ca3af;

        &:hover {
          border-color: #60a5fa;
          color: #60a5fa;
        }
      }
    }
  }
}

.analysis-modules {
  margin-bottom: 3rem;

  .modules-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;

    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 1200px) {
      grid-template-columns: repeat(3, 1fr);
    }

    @media (min-width: 1400px) {
      grid-template-columns: repeat(5, 1fr);
    }
  }
}

.module-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  :global(.dark) & {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(71, 85, 105, 0.3);
  }

  // 模块特定的装饰条
  &.upload-module::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #10b981, #34d399);
  }

  &.discover-module::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #60a5fa);
  }

  &.performance-module::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #8b5cf6, #a78bfa);
  }

  &.subprocess-module::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ec4899, #f472b6);
  }

  &.conformance-module::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #f59e0b, #fbbf24);
  }

  :deep(.el-card__header) {
    padding: 1.5rem 1.5rem 0.5rem 1.5rem;
    border-bottom: none;
  }

  :deep(.el-card__body) {
    padding: 1rem 1.5rem 1.5rem 1.5rem;
  }

  .module-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;

    .module-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      flex-shrink: 0;

      &.upload-icon {
        background: rgba(16, 185, 129, 0.1);
        color: #10b981;
      }

      &.discover-icon {
        background: rgba(59, 130, 246, 0.1);
        color: #3b82f6;
      }

      &.performance-icon {
        background: rgba(139, 92, 246, 0.1);
        color: #8b5cf6;
      }

      &.subprocess-icon {
        background: rgba(236, 72, 153, 0.1);
        color: #ec4899;
      }

      &.conformance-icon {
        background: rgba(245, 158, 11, 0.1);
        color: #f59e0b;
      }
    }

    .module-info {
      flex: 1;

      .module-title {
        font-size: 1.125rem;
        font-weight: 700;
        color: #111827;
        margin: 0 0 0.25rem 0;

        :global(.dark) & {
          color: #ffffff;
        }
      }

      .module-description {
        font-size: 0.875rem;
        color: #6b7280;
        margin: 0;

        :global(.dark) & {
          color: #9ca3af;
        }
      }
    }
  }

  .module-content {
    .module-detail {
      color: #64748b;
      line-height: 1.6;
      margin: 0 0 1.5rem 0;

      :global(.dark) & {
        color: #94a3b8;
      }
    }

    .module-actions {
      margin-top: 1.5rem;

      :deep(.el-button) {
        width: 100%;
        border-radius: 12px;
        font-weight: 600;
        padding: 12px 20px;
        font-size: 0.875rem;
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid rgba(59, 130, 246, 0.2);
        color: #3b82f6;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        // 添加微妙的内阴影效果
        box-shadow:
          inset 0 1px 0 rgba(255, 255, 255, 0.2),
          0 2px 4px rgba(0, 0, 0, 0.05);

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
          transition: left 0.5s ease;
        }

        &:hover {
          transform: translateY(-1px);
          border-color: #3b82f6;
          background: rgba(59, 130, 246, 0.05);
          box-shadow:
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            0 4px 12px rgba(59, 130, 246, 0.15);

          &::before {
            left: 100%;
          }
        }

        &:active {
          transform: translateY(0);
        }

        :global(.dark) & {
          background: rgba(30, 41, 59, 0.8);
          border-color: rgba(96, 165, 250, 0.3);
          color: #60a5fa;
          box-shadow:
            inset 0 1px 0 rgba(255, 255, 255, 0.1),
            0 2px 4px rgba(0, 0, 0, 0.2);

          &:hover {
            background: rgba(96, 165, 250, 0.1);
            border-color: #60a5fa;
            box-shadow:
              inset 0 1px 0 rgba(255, 255, 255, 0.2),
              0 4px 12px rgba(96, 165, 250, 0.2);
          }
        }
      }
    }
  }

  // 模块特定的按钮样式
  &.upload-module .module-actions :deep(.el-button) {
    border-color: rgba(16, 185, 129, 0.2);
    color: #10b981;

    &:hover {
      border-color: #10b981;
      background: rgba(16, 185, 129, 0.05);
      box-shadow:
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 4px 12px rgba(16, 185, 129, 0.15);
    }

    &.guard-disabled,
    &.guard-disabled:hover {
      cursor: not-allowed;
      opacity: 0.6;
      border-color: rgba(16, 185, 129, 0.2) !important;
      background: transparent !important;
      box-shadow: none !important;
    }

    &::before {
      background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.1), transparent);
    }

    :global(.dark) & {
      border-color: rgba(52, 211, 153, 0.3);
      color: #34d399;

      &:hover {
        background: rgba(52, 211, 153, 0.1);
        border-color: #34d399;
        box-shadow:
          inset 0 1px 0 rgba(255, 255, 255, 0.2),
          0 4px 12px rgba(52, 211, 153, 0.2);
      }
    }
  }

  &.discover-module .module-actions :deep(.el-button) {
    border-color: rgba(59, 130, 246, 0.2);
    color: #3b82f6;

    &:hover {
      border-color: #3b82f6;
      background: rgba(59, 130, 246, 0.05);
    }
  }

  &.performance-module .module-actions :deep(.el-button) {
    border-color: rgba(139, 92, 246, 0.2);
    color: #8b5cf6;

    &:hover {
      border-color: #8b5cf6;
      background: rgba(139, 92, 246, 0.05);
      box-shadow:
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 4px 12px rgba(139, 92, 246, 0.15);
    }

    &::before {
      background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.1), transparent);
    }

    :global(.dark) & {
      border-color: rgba(167, 139, 250, 0.3);
      color: #a78bfa;

      &:hover {
        background: rgba(167, 139, 250, 0.1);
        border-color: #a78bfa;
        box-shadow:
          inset 0 1px 0 rgba(255, 255, 255, 0.2),
          0 4px 12px rgba(167, 139, 250, 0.2);
      }
    }
  }

  &.subprocess-module .module-actions :deep(.el-button) {
    border-color: rgba(236, 72, 153, 0.2);
    color: #ec4899;

    &:hover {
      border-color: #ec4899;
      background: rgba(236, 72, 153, 0.05);
      box-shadow:
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 4px 12px rgba(236, 72, 153, 0.15);
    }

    &::before {
      background: linear-gradient(90deg, transparent, rgba(236, 72, 153, 0.1), transparent);
    }

    :global(.dark) & {
      border-color: rgba(244, 114, 182, 0.3);
      color: #f472b6;

      &:hover {
        background: rgba(244, 114, 182, 0.1);
        border-color: #f472b6;
        box-shadow:
          inset 0 1px 0 rgba(255, 255, 255, 0.2),
          0 4px 12px rgba(244, 114, 182, 0.2);
      }
    }
  }

  &.conformance-module .module-actions :deep(.el-button) {
    border-color: rgba(245, 158, 11, 0.2);
    color: #f59e0b;

    &:hover {
      border-color: #f59e0b;
      background: rgba(245, 158, 11, 0.05);
      box-shadow:
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 4px 12px rgba(245, 158, 11, 0.15);
    }

    &::before {
      background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.1), transparent);
    }

    :global(.dark) & {
      border-color: rgba(251, 191, 36, 0.3);
      color: #fbbf24;

      &:hover {
        background: rgba(251, 191, 36, 0.1);
        border-color: #fbbf24;
        box-shadow:
          inset 0 1px 0 rgba(255, 255, 255, 0.2),
          0 4px 12px rgba(251, 191, 36, 0.2);
      }
    }
  }
}

.process-info {
  :deep(.el-card) {
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    :global(.dark) & {
      background: rgba(30, 41, 59, 0.95);
      border: 1px solid rgba(71, 85, 105, 0.3);
    }

    .el-card__header {
      padding: 1.5rem 1.5rem  0.5rem 1.5rem;
      border-bottom: none;
    }

    .el-card__body {
      padding: 1rem 1.5rem 1.5rem 1.5rem;
    }
  }

  .card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #111827;
    margin: 0;

    :global(.dark) & {
      color: #ffffff;
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;

    @media (min-width: 640px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 1024px) {
      grid-template-columns: repeat(3, 1fr);
    }

    .info-item {
      .info-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: #6b7280;
        margin: 0 0 0.5rem 0;
        text-transform: uppercase;
        letter-spacing: 0.05em;

        :global(.dark) & {
          color: #9ca3af;
        }
      }

      .info-value {
        color: #111827;
        font-size: 1rem;
        line-height: 1.5;
        margin: 0;

        :global(.dark) & {
          color: #ffffff;
        }
      }
    }
  }
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  flex-shrink: 0;

  &.status-draft {
    background: linear-gradient(135deg, #6b7280, #9ca3af);
  }

  &.status-active {
    background: linear-gradient(135deg, #10b981, #34d399);
  }

  &.status-completed {
    background: linear-gradient(135deg, #06b6d4, #22d3ee);
  }

  &.status-archived {
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
  }
}

// 动画定义
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 模块卡片动画
.modules-grid .module-card {
  animation: slideInUp 0.5s ease-out;
  animation-fill-mode: both;

  &:nth-child(1) {
    animation-delay: 0.1s;
  }

  &:nth-child(2) {
    animation-delay: 0.2s;
  }

  &:nth-child(3) {
    animation-delay: 0.3s;
  }

  &:nth-child(4) {
    animation-delay: 0.4s;
  }

  &:nth-child(5) {
    animation-delay: 0.5s;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
