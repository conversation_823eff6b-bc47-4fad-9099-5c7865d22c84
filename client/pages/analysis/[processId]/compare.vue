<template>
  <div class="compare-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">流程比对分析</h1>
          <div class="breadcrumb">
            <span>分析</span>
            <el-icon><ArrowRight /></el-icon>
            <span>流程 {{ processId }}</span>
            <el-icon><ArrowRight /></el-icon>
            <span>比对分析</span>
          </div>
        </div>
        <div class="header-right">
          <!-- 导航标签 -->
          <div class="nav-tabs">
            <NuxtLink
              :to="`/analysis/${processId}/discover`"
              class="nav-tab"
            >
              流程发现
            </NuxtLink>
            <NuxtLink
              :to="`/analysis/${processId}/performance`"
              class="nav-tab"
            >
              性能分析
            </NuxtLink>
            <NuxtLink
              :to="`/analysis/${processId}/compare`"
              class="nav-tab nav-tab--active"
            >
              比对分析
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 比对分析结果 -->
    <div v-else-if="!isLoading && (originalDiscoveryResult || leftDfgData || rightDfgData || isAnalyzing)" class="compare-results">
      <div class="main-layout">
        <!-- 左侧筛选器面板容器 -->
        <div class="filter-panel-container">
          <!-- 筛选器面板 -->
          <div class="filter-panel" :class="{ 'collapsed': !filterPanelVisible }">
            <GlobalFilter
              v-if="originalDiscoveryResult"
              class="sidebar-filter"
              :process-id="processId"
              :discovery-data="originalDiscoveryResult"
              @filter-change="onFilterChange"
              @reset="onFilterReset"
            />
          </div>
          <!-- 筛选器切换按钮 -->
          <div
            class="filter-toggle-button"
            title="切换筛选器面板"
            @click="toggleFilterPanel"
          >
            <el-icon>
              <ArrowLeft v-if="filterPanelVisible" />
              <ArrowRight v-else />
            </el-icon>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="content-area" :class="{ 'full-width': !filterPanelVisible }">
          <!-- 时间控制区域 -->
          <div class="time-control-section">
            <el-card class="time-control-card">
              <div class="time-control-content">
                <div class="time-control-item">
                  <div class="control-header">
                    <el-icon class="control-icon"><Clock /></el-icon>
                    <span class="control-title">基准时间段</span>
                  </div>
                  <TimeRangeFilter
                    v-model="leftTimeRange"
                    :process-id="processId"
                    :placeholder="'选择基准时间范围'"
                    @change="onLeftTimeRangeChange"
                  />
                </div>

                <div class="time-control-divider">
                  <el-icon><ArrowRight /></el-icon>
                </div>

                <div class="time-control-item">
                  <div class="control-header">
                    <el-icon class="control-icon"><Timer /></el-icon>
                    <span class="control-title">对比时间段</span>
                  </div>
                  <OffsetDaysControl
                    v-model="offsetDays"
                    :base-range="leftTimeRange"
                    @change="onOffsetDaysChange"
                  />
                </div>
              </div>
            </el-card>
          </div>

          <div class="compare-layout">
            <!-- 左侧 DFG 区域 -->
            <div class="dfg-section left-dfg">
              <el-card class="dfg-card" shadow="hover">
                <template #header>
                  <div class="dfg-header">
                    <div class="dfg-title-section">
                      <div class="dfg-indicator base-indicator" />
                      <h3 class="dfg-title">基准时间段</h3>
                      <el-tag v-if="leftTimeRange" type="success" size="small" class="time-tag">
                        {{ formatTimeRange(leftTimeRange) }}
                      </el-tag>
                    </div>
                    <div v-if="leftDfgData" class="dfg-stats">
                      <span class="stat-item">
                        <el-icon><DataBoard /></el-icon>
                        {{ leftDfgData.statistics?.totalCases || 0 }} 案例
                      </span>
                      <span class="stat-item">
                        <el-icon><Connection /></el-icon>
                        {{ leftDfgData.nodes?.length || 0 }} 活动
                      </span>
                    </div>
                  </div>
                </template>
                <div class="dfg-content">
                  <div ref="leftDfgContainer" class="dfg-container">
                    <div v-if="!leftDfgData && !isAnalyzing" class="dfg-placeholder">
                      <el-icon class="placeholder-icon"><DataBoard /></el-icon>
                      <p class="placeholder-text">请选择时间范围并开始分析</p>
                    </div>
                  </div>
                  <div v-if="isAnalyzing" class="dfg-loading">
                    <el-loading text="正在分析基准时间段..." />
                  </div>
                </div>
              </el-card>
            </div>

            <!-- 右侧 DFG 区域 -->
            <div class="dfg-section right-dfg">
              <el-card class="dfg-card" shadow="hover">
                <template #header>
                  <div class="dfg-header">
                    <div class="dfg-title-section">
                      <div class="dfg-indicator compare-indicator" />
                      <h3 class="dfg-title">对比时间段</h3>
                      <el-tag v-if="leftTimeRange && offsetDays" type="warning" size="small" class="time-tag">
                        {{ formatOffsetTimeRange(leftTimeRange, offsetDays) }}
                      </el-tag>
                    </div>
                    <div v-if="rightDfgData" class="dfg-stats">
                      <span class="stat-item">
                        <el-icon><DataBoard /></el-icon>
                        {{ rightDfgData.statistics?.totalCases || 0 }} 案例
                      </span>
                      <span class="stat-item">
                        <el-icon><Connection /></el-icon>
                        {{ rightDfgData.nodes?.length || 0 }} 活动
                      </span>
                    </div>
                  </div>
                </template>
                <div class="dfg-content">
                  <div ref="rightDfgContainer" class="dfg-container">
                    <div v-if="!rightDfgData && !isAnalyzing" class="dfg-placeholder">
                      <el-icon class="placeholder-icon"><DataBoard /></el-icon>
                      <p class="placeholder-text">等待比对分析结果</p>
                    </div>
                  </div>
                  <div v-if="isAnalyzing" class="dfg-loading">
                    <el-loading text="正在分析对比时间段..." />
                  </div>
                </div>
              </el-card>
            </div>
          </div>

          <!-- 底部控制面板 -->
          <div class="control-panel">
            <div class="control-content">
              <!-- 展示维度控制 -->
              <div class="control-group">
                <label class="control-label">
                  <el-icon><DataBoard /></el-icon>
                  展示维度
                </label>
                <el-radio-group
                  v-model="currentDimension"
                  size="small"
                  @change="onDimensionChange"
                >
                  <el-radio-button value="frequency">
                    <el-icon><DataLine /></el-icon>
                    频次
                  </el-radio-button>
                  <el-radio-button value="duration">
                    <el-icon><Timer /></el-icon>
                    耗时
                  </el-radio-button>
                </el-radio-group>
              </div>

              <!-- 展示方式控制 -->
              <div class="control-group">
                <label class="control-label">
                  <el-icon><View /></el-icon>
                  展示方式
                </label>
                <el-radio-group
                  v-model="displayMode"
                  size="small"
                  @change="onDisplayModeChange"
                >
                  <el-radio-button value="absolute">绝对数值</el-radio-button>
                  <el-radio-button value="difference">数值差异</el-radio-button>
                  <el-radio-button value="percentage">百分比变化</el-radio-button>
                </el-radio-group>
              </div>

              <!-- 操作按钮 -->
              <div class="action-controls">
                <el-button
                  type="primary"
                  size="default"
                  :loading="isAnalyzing"
                  :disabled="!leftTimeRange"
                  @click="startCompareAnalysis"
                >
                  <el-icon><Refresh /></el-icon>
                  {{ isAnalyzing ? '分析中...' : '开始比对分析' }}
                </el-button>
                <el-button
                  size="default"
                  :disabled="!leftDfgData || !rightDfgData"
                  @click="exportCompareResult"
                >
                  <el-icon><Download /></el-icon>
                  导出结果
                </el-button>
                <el-button
                  size="default"
                  :disabled="!leftDfgData || !rightDfgData"
                  @click="resetAnalysis"
                >
                  <el-icon><RefreshLeft /></el-icon>
                  重置
                </el-button>
              </div>

              <!-- 分析状态 -->
              <div v-if="leftDfgData && rightDfgData" class="status-info">
                <div class="status-item">
                  <span class="status-label">基准案例：</span>
                  <span class="status-value">{{ leftDfgData.statistics?.totalCases || 0 }}</span>
                </div>
                <div class="status-item">
                  <span class="status-label">对比案例：</span>
                  <span class="status-value">{{ rightDfgData.statistics?.totalCases || 0 }}</span>
                </div>
                <div class="status-item">
                  <span class="status-label">变化：</span>
                  <span class="status-value" :class="getCaseChangeClass()">
                    {{ getCaseChangeText() }}
                  </span>
                </div>
                <div v-if="hasActiveFilters" class="status-item">
                  <span class="status-label">筛选器：</span>
                  <span class="status-value filter-active">已启用</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-empty description="请先进行流程发现，然后开始比对分析">
        <template #image>
          <el-icon style="font-size: 64px; color: #c0c4cc;"><DataBoard /></el-icon>
        </template>
        <template #description>
          <div class="empty-description">
            <h3>开始流程比对分析</h3>
            <p>比对分析需要基于已有的流程发现结果。请先完成以下步骤：</p>
            <ol class="steps-list">
              <li>上传流程数据文件</li>
              <li>进行流程发现分析</li>
              <li>返回此页面进行比对分析</li>
            </ol>
          </div>
        </template>
        <el-button type="primary" size="large" @click="navigateToDiscover">
          <el-icon><ArrowRight /></el-icon>
          前往流程发现
        </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ArrowLeft, ArrowRight, Refresh, Download, Clock, Timer, DataBoard, Connection,
  DataLine, View, RefreshLeft
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useApi } from '~/utils/api'
import { AnalysisType } from '~/types'
import type { DFGResult, ProcessCompareOptions } from '~/types'
import * as go from 'gojs'
import GlobalFilter from '~/components/analysis/GlobalFilter.vue'
import TimeRangeFilter from '~/components/analysis/TimeRangeFilter.vue'
import OffsetDaysControl from '~/components/analysis/OffsetDaysControl.vue'

// 筛选器状态接口
interface FilterState {
  globalLogic: 'AND' | 'OR'
  groups: Array<{
    id: string
    type: 'resource' | 'duration' | 'businessField' | 'pathway'
    logic: 'AND' | 'OR'
    conditions: Array<{
      id: string
      type: 'resource' | 'duration' | 'businessField' | 'pathway'
      enabled: boolean
      data: Record<string, unknown>
    }>
  }>
}

// 节点数据接口
interface NodeData {
  key: string
  label: string
  frequency: number
  avgDuration: number
  minDuration: number
  maxDuration: number
  currentValue: number
  displayText?: string // 显示文本，可选
  dimension: string
  displayMode?: string // 显示模式，可选
  nodeStyle: Record<string, unknown>
  isStartNode: boolean
  isEndNode: boolean
  color: string
  borderColor: string
  loc?: string // 位置信息，可选
}

// 百分位数数据接口
interface PercentileData {
  p25: number
  p50: number
  p75: number
  p90: number
  max: number
  logScale: boolean
  originalMax?: number
  originalMin?: number
}

// 数据分布接口
interface DataDistribution {
  nodePercentiles: PercentileData
  edgePercentiles: PercentileData
}

// DFG节点接口
interface DFGNode {
  id: string
  label?: string
  frequency?: number
  avgDuration?: number
  minDuration?: number
  maxDuration?: number
  isStartNode?: boolean
  isEndNode?: boolean
}

// DFG边接口
interface DFGEdge {
  source: string
  target: string
  frequency?: number
  avgDuration?: number
  minDuration?: number
  maxDuration?: number
}

// 页面参数
const route = useRoute()
const processId = parseInt(route.params.processId as string)

// 状态管理
const isLoading = ref(true)
const isAnalyzing = ref(false)
const filterPanelVisible = ref(true)

// 数据状态
const originalDiscoveryResult = ref<DFGResult | null>(null)
const leftDfgData = ref<DFGResult | null>(null)
const rightDfgData = ref<DFGResult | null>(null)

// 控制状态
const currentDimension = ref<'frequency' | 'duration'>('frequency')
const displayMode = ref<'absolute' | 'difference' | 'percentage'>('absolute')
const leftTimeRange = ref<[Date, Date] | null>(null)
const offsetDays = ref(7) // 默认偏移7天

// 筛选器状态
const currentFilters = ref<FilterState | null>(null)
const hasActiveFilters = ref(false)

// 图表容器引用
const leftDfgContainer = ref<HTMLElement>()
const rightDfgContainer = ref<HTMLElement>()
let leftDiagram: go.Diagram | null = null
let rightDiagram: go.Diagram | null = null

// API 实例
const api = useApi()

// 方法定义
const toggleFilterPanel = () => {
  filterPanelVisible.value = !filterPanelVisible.value
}

const onFilterChange = (filters: FilterState) => {
  console.log('Filter changed:', filters)
  currentFilters.value = filters
  hasActiveFilters.value = checkHasActiveFilters(filters)

  // 如果有活跃的时间范围，自动重新分析
  if (leftTimeRange.value && hasActiveFilters.value) {
    console.log('Auto-reanalyzing with new filters...')
    startCompareAnalysis()
  }
}

const onFilterReset = () => {
  console.log('Filter reset')
  currentFilters.value = null
  hasActiveFilters.value = false

  // 如果有活跃的时间范围，自动重新分析
  if (leftTimeRange.value) {
    console.log('Auto-reanalyzing after filter reset...')
    startCompareAnalysis()
  }
}

// 检查是否有活跃的筛选器
const checkHasActiveFilters = (filters: FilterState | null): boolean => {
  if (!filters || !filters.groups) return false

  return filters.groups.some(group =>
    group.conditions.some(condition =>
      condition.enabled && hasConditionValue(condition)
    )
  )
}

// 检查筛选条件是否有值
const hasConditionValue = (condition: FilterState['groups'][0]['conditions'][0]): boolean => {
  if (!condition.data) return false

  const data = condition.data as Record<string, any>

  switch (condition.type) {
    case 'resource':
      return data.resources && Array.isArray(data.resources) && data.resources.length > 0
    case 'duration':
      return data.min !== null || data.max !== null
    case 'businessField':
      if (data.operator === 'range') {
        return data.rangeMin !== '' || data.rangeMax !== ''
      } else if (data.operator === 'in') {
        return data.values && Array.isArray(data.values) && data.values.length > 0
      } else {
        return data.value !== ''
      }
    case 'pathway':
      return data.activity && data.frequency &&
             Array.isArray(data.frequency) && data.frequency.length === 2
    default:
      return false
  }
}

// 将筛选器转换为 requiredActivities 参数
const convertFiltersToRequiredActivities = (filters: FilterState | null): string[] => {
  if (!filters || !filters.groups) return []

  const requiredActivities: string[] = []

  // 遍历所有筛选器组
  filters.groups.forEach(group => {
    group.conditions.forEach(condition => {
      if (condition.enabled && condition.type === 'pathway') {
        const data = condition.data as Record<string, any>
        if (data.activity && typeof data.activity === 'string') {
          // 路径筛选器中指定的活动应该被包含
          if (!requiredActivities.includes(data.activity)) {
            requiredActivities.push(data.activity)
          }
        }
      }
    })
  })

  return requiredActivities
}

const onLeftTimeRangeChange = (range: [Date, Date] | null) => {
  leftTimeRange.value = range
  // 自动设置偏移天数为时间范围的长度
  if (range) {
    const duration = range[1].getTime() - range[0].getTime()
    const days = Math.ceil(duration / (1000 * 60 * 60 * 24))
    offsetDays.value = Math.max(1, days)
  }
}

const onOffsetDaysChange = (days: number) => {
  offsetDays.value = days
}

const onDimensionChange = async () => {
  // 重新渲染图表以更新显示维度 - 先渲染左侧，再渲染右侧以保持位置同步
  if (leftDfgData.value && rightDfgData.value) {
    await renderLeftDFG()
    await nextTick()
    await renderRightDFG()
  }
}

const onDisplayModeChange = async () => {
  console.log('=== Display Mode Changed ===')
  console.log('New display mode:', displayMode.value)
  console.log('Has left DFG data:', !!leftDfgData.value)
  console.log('Has right DFG data:', !!rightDfgData.value)

  // 重新渲染图表以更新显示方式 - 先渲染左侧，再渲染右侧以保持位置同步
  if (leftDfgData.value && rightDfgData.value) {
    console.log('Re-rendering DFG charts with new display mode...')
    await renderLeftDFG()
    await nextTick()
    await renderRightDFG()
    console.log('DFG charts re-rendered successfully')
  } else {
    console.log('Cannot re-render: missing DFG data')
  }
}

const startCompareAnalysis = async () => {
  if (!leftTimeRange.value) {
    ElMessage.warning('请先选择基准时间范围')
    return
  }

  // 检查是否有原始流程发现数据
  if (!originalDiscoveryResult.value) {
    ElMessage.warning('请先进行流程发现，然后再进行比对分析')
    return
  }

  console.log('=== Starting Compare Analysis ===')
  console.log('Process ID:', processId)
  console.log('Time range:', leftTimeRange.value)
  console.log('Offset days:', offsetDays.value)
  console.log('Original discovery data available:', !!originalDiscoveryResult.value)

  isAnalyzing.value = true
  try {
    // 将筛选器转换为 requiredActivities（向后兼容）
    const requiredActivities = convertFiltersToRequiredActivities(currentFilters.value)
    console.log('Current filters:', currentFilters.value)
    console.log('Converted requiredActivities:', requiredActivities)

    const options: ProcessCompareOptions = {
      baseStartTime: leftTimeRange.value[0].toISOString(),
      baseEndTime: leftTimeRange.value[1].toISOString(),
      offsetDays: offsetDays.value,
      dimension: currentDimension.value,
      displayMode: displayMode.value,
      forceRefresh: true,
      requiredActivities: requiredActivities.length > 0 ? requiredActivities : undefined,
      filters: currentFilters.value // 传递完整的筛选器状态
    }

    console.log('Starting compare analysis with options:', options)
    const result = await api.compareProcess(processId, options)

    console.log('=== API Response ===')
    console.log('Full result:', result)
    console.log('baseDfg:', result.baseDfg)
    console.log('compareDfg:', result.compareDfg)

    leftDfgData.value = result.baseDfg
    rightDfgData.value = result.compareDfg

    // 详细的数据检查
    console.log('=== Left DFG Data ===')
    console.log('leftDfgData.value:', leftDfgData.value)
    console.log('leftDfgData.value.nodes:', leftDfgData.value?.nodes)
    console.log('leftDfgData.value.edges:', leftDfgData.value?.edges)

    console.log('=== Right DFG Data ===')
    console.log('rightDfgData.value:', rightDfgData.value)
    console.log('rightDfgData.value.nodes:', rightDfgData.value?.nodes)
    console.log('rightDfgData.value.edges:', rightDfgData.value?.edges)

    // 调试信息：检查是否包含开始和结束节点
    if (leftDfgData.value?.nodes) {
      console.log('Left DFG nodes:', leftDfgData.value.nodes.map(n => ({ id: n.id, label: n.label, isStart: n.id === '开始', isEnd: n.id === '结束' })))
    }
    if (rightDfgData.value?.nodes) {
      console.log('Right DFG nodes:', rightDfgData.value.nodes.map(n => ({ id: n.id, label: n.label, isStart: n.id === '开始', isEnd: n.id === '结束' })))
    }

    // 检查是否有数据
    const hasLeftData = leftDfgData.value?.nodes && leftDfgData.value.nodes.length > 0
    const hasRightData = rightDfgData.value?.nodes && rightDfgData.value.nodes.length > 0

    if (!hasLeftData && !hasRightData) {
      ElMessage.warning('选择的时间范围内没有找到数据，请检查时间范围设置或确保已上传相关数据')
      return
    } else if (!hasLeftData) {
      ElMessage.warning('基准时间段内没有找到数据，请调整基准时间范围')
      return
    } else if (!hasRightData) {
      ElMessage.warning('对比时间段内没有找到数据，请调整偏移天数或基准时间范围')
      return
    }

    // 渲染两个 DFG 图表 - 先渲染左侧，再渲染右侧以获取位置信息
    await nextTick()
    await renderLeftDFG()
    // 等待左侧图表完全渲染完成后再渲染右侧图表
    await nextTick()
    await renderRightDFG()

    ElMessage.success('比对分析完成')
  } catch (error: unknown) {
    console.error('Compare analysis error:', error)
    ElMessage.error((error as Error).message || '比对分析失败')
  } finally {
    isAnalyzing.value = false
  }
}

const exportCompareResult = () => {
  if (!leftDfgData.value || !rightDfgData.value) {
    ElMessage.warning('暂无可导出的比对结果')
    return
  }

  // 组织导出数据：包含左右 DFG、基础统计、筛选条件、展示参数等
  const exportPayload = {
    meta: {
      processId,
      exportedAt: new Date().toISOString(),
      dimension: currentDimension.value,
      displayMode: displayMode.value,
      offsetDays: offsetDays.value,
      baseTimeRange: leftTimeRange.value
        ? { start: leftTimeRange.value[0].toISOString(), end: leftTimeRange.value[1].toISOString() }
        : null,
      filters: currentFilters.value || null
    },
    baseDfg: leftDfgData.value,
    compareDfg: rightDfgData.value
  }

  const dataStr = JSON.stringify(exportPayload, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)

  const link = document.createElement('a')
  link.href = url
  link.download = `process-compare-${processId}-${Date.now()}.json`
  link.click()

  URL.revokeObjectURL(url)
  ElMessage.success('比对结果已导出为 JSON')
}

const navigateToDiscover = () => {
  navigateTo(`/analysis/${processId}/discover`)
}

// 格式化时间范围显示
const formatTimeRange = (range: [Date, Date]) => {
  if (!range || range.length !== 2) return ''
  const start = range[0].toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
  const end = range[1].toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
  return `${start} - ${end}`
}

// 格式化偏移时间范围显示
const formatOffsetTimeRange = (baseRange: [Date, Date], offsetDays: number) => {
  if (!baseRange || baseRange.length !== 2 || !offsetDays) return ''

  const offsetMs = offsetDays * 24 * 60 * 60 * 1000
  const start = new Date(baseRange[0].getTime() - offsetMs)
  const end = new Date(baseRange[1].getTime() - offsetMs)

  const startStr = start.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
  const endStr = end.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
  return `${startStr} - ${endStr}`
}

// 重置分析
const resetAnalysis = () => {
  leftDfgData.value = null
  rightDfgData.value = null
  leftTimeRange.value = null
  offsetDays.value = 7

  // 清理图表
  if (leftDiagram) {
    leftDiagram.clear()
  }
  if (rightDiagram) {
    rightDiagram.clear()
  }

  ElMessage.success('已重置分析结果')
}

// 获取案例变化的样式类
const getCaseChangeClass = () => {
  if (!leftDfgData.value || !rightDfgData.value) return ''

  const baseCases = leftDfgData.value.statistics?.totalCases || 0
  const compareCases = rightDfgData.value.statistics?.totalCases || 0

  if (compareCases > baseCases) return 'status-increase'
  if (compareCases < baseCases) return 'status-decrease'
  return 'status-same'
}

// 获取案例变化的文本
const getCaseChangeText = () => {
  if (!leftDfgData.value || !rightDfgData.value) return ''

  const baseCases = leftDfgData.value.statistics?.totalCases || 0
  const compareCases = rightDfgData.value.statistics?.totalCases || 0
  const change = compareCases - baseCases
  const changePercent = baseCases > 0 ? ((change / baseCases) * 100).toFixed(1) : '0'

  if (change > 0) return `+${change} (+${changePercent}%)`
  if (change < 0) return `${change} (${changePercent}%)`
  return '无变化'
}

// 格式化持续时间
const formatDuration = (milliseconds: number): string => {
  if (!milliseconds || milliseconds <= 0) return '0分钟'

  const minutes = Math.floor(milliseconds / (1000 * 60))
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) {
    const remainingHours = hours % 24
    return remainingHours > 0 ? `${days}天${remainingHours}小时` : `${days}天`
  } else if (hours > 0) {
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
  } else {
    return `${minutes}分钟`
  }
}

// DFG 图表初始化和渲染
const initDiagram = (container: HTMLElement) => {
  if (!container) return null

  // 确保容器有最小尺寸
  if (container.offsetWidth === 0 || container.offsetHeight === 0) {
    console.warn('Container has zero dimensions, setting minimum size')
    container.style.minWidth = '400px'
    container.style.minHeight = '300px'
  }

  // 设置 GoJS 许可证密钥
  if (!go.Diagram.licenseKey) {
    go.Diagram.licenseKey = "2bf843e7b36758c511895a25406c7efb0bab2d67ce864df3595012a0ed587a04249fb87b50d7d8c986aa4df9182ec98ed8976121931c0338e737d48f45e0d5f1b63124e5061841dbf4052691c9fb38b1ff7971fbddbc68a2d2"
  }

  console.log('Initializing compare DFG diagram...')
  console.log('Container dimensions:', container.offsetWidth, 'x', container.offsetHeight)

  const $ = go.GraphObject.make

  try {
    const diagram = new go.Diagram(container, {
      'undoManager.isEnabled': true,
      layout: $(go.LayeredDigraphLayout, {
        direction: 90,
        layerSpacing: 80,
        columnSpacing: 50,
        setsPortSpots: false
      }),
      initialContentAlignment: go.Spot.Center,
      // 自动缩放以适应容器
      initialAutoScale: go.AutoScale.Uniform,
      // 启用鼠标滚轮缩放
      'toolManager.mouseWheelBehavior': go.WheelMode.Zoom,
      // 启用动画管理器以支持连线流动动画
      'animationManager.isEnabled': true,
      allowDrop: false,
      allowMove: false,
      allowCopy: false,
      allowDelete: false,
      allowSelect: true, // 允许选择以便点击查看详情
      // 设置缩放范围
      minScale: 0.1,
      maxScale: 5.0,
      // 设置图表边距
      padding: new go.Margin(20, 20, 20, 20),
      // 设置背景点击行为 - 允许在背景拖拽进行平移
      hasHorizontalScrollbar: false,
      hasVerticalScrollbar: false,
      // 启用网格背景
      'grid.visible': true,
      'grid.gridCellSize': new go.Size(20, 20),
      'grid.gridOrigin': new go.Point(0, 0)
    })

    console.log('Compare DFG diagram created successfully')

    // 启用拖拽平移功能
    diagram.toolManager.panningTool.isEnabled = true
    // 禁用节点拖拽
    diagram.toolManager.draggingTool.isEnabled = false
    // 禁用连线工具
    diagram.toolManager.linkingTool.isEnabled = false
    diagram.toolManager.relinkingTool.isEnabled = false
    // 禁用框选工具
    diagram.toolManager.dragSelectingTool.isEnabled = false

    // 定义节点模板 - 使用与 discover.vue 相同的样式
    diagram.nodeTemplate = $(go.Node, 'Auto',
      {
        locationSpot: go.Spot.Center,
        selectable: false, // 禁用节点选择
        movable: false, // 禁止移动节点
        copyable: false, // 禁止复制节点
        deletable: false, // 禁止删除节点
        shadowVisible: true,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowOffset: new go.Point(2, 2),
        shadowBlur: 4
      },
      new go.Binding('location', 'loc', go.Point.parse).makeTwoWay(go.Point.stringify),

      // 节点形状 - 使用数据驱动的动态样式，开始和结束节点使用不同形状
      $(go.Shape, {
        name: 'BODY',
        fill: '#4CAF50',
        stroke: '#388E3C',
        strokeWidth: 2,
        minSize: new go.Size(80, 40),
        parameter1: 8 // 圆角半径
      },
        // 动态形状绑定 - 开始和结束节点使用大圆角矩形
        new go.Binding('figure', '', (_data: Record<string, unknown>) => {
          return 'RoundedRectangle' // 所有节点都使用圆角矩形
        }),
        // 动态圆角半径绑定 - 开始和结束节点使用更大的圆角
        new go.Binding('parameter1', '', (data: Record<string, unknown>) => {
          if (data.isStartNode || data.isEndNode) {
            return 22 // 开始和结束节点使用大圆角半径
          }
          return 8 // 普通节点使用标准圆角
        }),
        // 动态样式绑定
        new go.Binding('fill', '', (data: Record<string, unknown>) => {
          if (!data.nodeStyle) return '#4CAF50'
          return (data.nodeStyle as Record<string, unknown>).fill as string
        }),
        new go.Binding('stroke', '', (data: Record<string, unknown>) => {
          if (!data.nodeStyle) return '#388E3C'
          return (data.nodeStyle as Record<string, unknown>).stroke as string
        }),
        new go.Binding('strokeWidth', '', (data: Record<string, unknown>) => {
          if (!data.nodeStyle) return 2
          return (data.nodeStyle as Record<string, unknown>).strokeWidth as number
        }),
        // 动态尺寸绑定 - 开始和结束节点使用固定的大圆角矩形尺寸
        new go.Binding('width', '', (data: Record<string, unknown>) => {
          if (data.isStartNode || data.isEndNode) {
            return 80 // 开始和结束节点使用固定宽度
          }
          if (!data.nodeStyle) return 80
          return (data.nodeStyle as Record<string, unknown>).width as number
        }),
        new go.Binding('height', '', (data: Record<string, unknown>) => {
          if (data.isStartNode || data.isEndNode) {
            return 50 // 开始和结束节点使用固定高度
          }
          if (!data.nodeStyle) return 40
          return (data.nodeStyle as Record<string, unknown>).height as number
        })),

      // 节点文本 - 改进的排版和样式，开始/结束节点使用不同布局
      $(go.Panel, 'Vertical',
        // 主标签
        $(go.TextBlock, {
          font: 'bold 13px "Segoe UI", Arial, sans-serif',
          margin: new go.Margin(6, 8, 2, 8),
          maxSize: new go.Size(140, NaN),
          wrap: go.Wrap.Fit,
          editable: false,
          textAlign: 'center',
          stroke: '#FFFFFF'
        },
          new go.Binding('text', '', (data: Record<string, unknown>) => {
            // 开始和结束节点显示简化文本
            if (data.isStartNode) return '开始'
            if (data.isEndNode) return '结束'
            return data.label as string
          }),
          new go.Binding('stroke', '', (data: Record<string, unknown>) => {
            if (!data.nodeStyle) return '#FFFFFF'
            return (data.nodeStyle as Record<string, unknown>).textColor as string
          }),
          // 开始和结束节点使用更小的字体
          new go.Binding('font', '', (data: Record<string, unknown>) => {
            if (data.isStartNode || data.isEndNode) {
              return 'bold 11px "Segoe UI", Arial, sans-serif'
            }
            return 'bold 13px "Segoe UI", Arial, sans-serif'
          }),
          // 开始和结束节点使用垂直居中的边距
          new go.Binding('margin', '', (data: Record<string, unknown>) => {
            if (data.isStartNode || data.isEndNode) {
              return new go.Margin(18, 4, 18, 4) // 上下边距相等，实现垂直居中
            }
            return new go.Margin(6, 8, 2, 8)
          })),

        // 数值标签 - 显示频次或耗时
        $(go.TextBlock, {
          font: '10px "Segoe UI", Arial, sans-serif',
          margin: new go.Margin(0, 8, 6, 8),
          textAlign: 'center',
          stroke: 'rgba(255, 255, 255, 0.9)'
        },
          new go.Binding('text', '', (data: Record<string, unknown>) => {
            // 开始和结束节点不显示数值标签
            if (data.isStartNode || data.isEndNode) return ''

            // 如果有自定义显示文本，优先使用
            if (data.displayText && data.displayMode !== 'absolute') {
              return data.displayText as string
            }

            // 否则根据维度显示原始数值
            if (data.dimension === 'duration') {
              return formatDuration((data.avgDuration as number) || 0)
            } else {
              return `${(data.frequency as number) || 0}`
            }
          }),
          new go.Binding('stroke', '', (data: Record<string, unknown>) => {
            if (!data.nodeStyle) return 'rgba(255, 255, 255, 0.9)'
            const textColor = (data.nodeStyle as Record<string, unknown>).textColor as string
            return textColor === '#FFFFFF' ? 'rgba(255, 255, 255, 0.8)' : 'rgba(46, 94, 32, 0.8)'
          }),
          // 开始和结束节点使用更小的边距
          new go.Binding('margin', '', (data: Record<string, unknown>) => {
            if (data.isStartNode || data.isEndNode) {
              return new go.Margin(0, 4, 4, 4)
            }
            return new go.Margin(0, 8, 6, 8)
          }))
      )
    )

    // 定义连接线模板 - 使用正交路由的专业流程挖掘样式
    diagram.linkTemplate = $(go.Link, {
      routing: go.Routing.Orthogonal, // 使用正交路由（直角折线）
      curve: go.Curve.JumpOver, // 线条交叉时跳跃效果
      corner: 6, // 设置拐角圆角半径
      selectable: false, // 禁用连线选择
      cursor: 'pointer',
      shadowVisible: true,
      shadowColor: 'rgba(0, 0, 0, 0.2)',
      shadowOffset: new go.Point(1, 1),
      shadowBlur: 2,
      // 设置连线与节点的连接 - 保持适当距离
      fromSpot: go.Spot.AllSides,
      toSpot: go.Spot.AllSides,
      fromEndSegmentLength: 15, // 起点与节点保持15px距离，避免拐角紧贴节点
      toEndSegmentLength: 15    // 终点与节点保持15px距离，确保箭头清晰可见
    },
      // 连接线形状 - 使用数据驱动的动态样式
      $(go.Shape, {
        name: 'LINKSHAPE',
        strokeWidth: 4,
        stroke: '#2196F3',
        strokeCap: 'round'
      },
        // 动态样式绑定
        new go.Binding('stroke', '', (data: Record<string, unknown>) => {
          if (!data.linkStyle) return '#2196F3'
          return (data.linkStyle as Record<string, unknown>).stroke as string
        }),
        new go.Binding('strokeWidth', '', (data: Record<string, unknown>) => {
          if (!data.linkStyle) return 4
          return (data.linkStyle as Record<string, unknown>).strokeWidth as number
        })),

      // 箭头 - 使用默认定位以确保在正交路由中正确显示
      $(go.Shape, {
        name: 'ARROWSHAPE',
        toArrow: 'Standard',
        fill: '#2196F3',
        stroke: '#2196F3',
        strokeWidth: 1,
        scale: 1.6
        // 移除segmentIndex等属性，让GoJS自动处理箭头位置
      },
        new go.Binding('fill', '', (data: Record<string, unknown>) => {
          if (!data.linkStyle) return '#2196F3'
          return (data.linkStyle as Record<string, unknown>).stroke as string
        }),
        new go.Binding('stroke', '', (data: Record<string, unknown>) => {
          if (!data.linkStyle) return '#2196F3'
          return (data.linkStyle as Record<string, unknown>).stroke as string
        }),
        new go.Binding('scale', '', (data: Record<string, unknown>) => {
          if (!data.linkStyle) return 1.6
          return (data.linkStyle as Record<string, unknown>).arrowScale as number
        })),

      // 连接线标签 - 改进的样式
      $(go.Panel, 'Auto',
        $(go.Shape, 'RoundedRectangle', {
          fill: 'rgba(255, 255, 255, 0.95)',
          stroke: 'rgba(0, 0, 0, 0.1)',
          strokeWidth: 1,
          parameter1: 4
        }),
        $(go.TextBlock, {
          font: 'bold 10px "Segoe UI", Arial, sans-serif',
          margin: new go.Margin(3, 6, 3, 6),
          stroke: '#333',
          textAlign: 'center'
        },
          new go.Binding('text', '', (data: Record<string, unknown>) => {
            // 如果有自定义显示文本，优先使用
            if (data.displayText && data.displayMode !== 'absolute') {
              return data.displayText as string
            }

            // 否则根据维度显示原始数值
            if (data.dimension === 'duration') {
              return formatDuration((data.avgDuration as number) || 0)
            } else {
              return data.frequency ? (data.frequency as number).toString() : '0'
            }
          }),
          new go.Binding('stroke', '', (data: Record<string, unknown>) => {
            if (!data.linkStyle) return '#333'
            // 根据连接线颜色调整文本颜色以确保可读性
            const linkColor = (data.linkStyle as Record<string, unknown>).stroke as string
            if (linkColor === '#D32F2F' || linkColor === '#F44336') return '#D32F2F'
            if (linkColor === '#FF9800') return '#E65100'
            if (linkColor === '#4CAF50') return '#2E7D32'
            return '#1976D2'
          }))
      )
    )

    return diagram
  } catch (error) {
    console.error('Failed to initialize GoJS diagram:', error)
    return null
  }
}

const renderLeftDFG = async () => {
  console.log('=== renderLeftDFG called ===')
  console.log('leftDfgContainer.value:', leftDfgContainer.value)
  console.log('leftDfgData.value:', leftDfgData.value)

  if (!leftDfgContainer.value || !leftDfgData.value) {
    console.log('Missing container or data, returning')
    return
  }

  console.log('Container dimensions:', leftDfgContainer.value.offsetWidth, 'x', leftDfgContainer.value.offsetHeight)
  console.log('DFG data nodes:', leftDfgData.value.nodes?.length)
  console.log('DFG data edges:', leftDfgData.value.edges?.length)

  // 清理现有图表
  if (leftDiagram) {
    leftDiagram.div = null
    leftDiagram = null
  }

  leftDfgContainer.value.innerHTML = ''
  leftDiagram = initDiagram(leftDfgContainer.value)

  if (!leftDiagram) {
    console.error('Failed to initialize left diagram')
    return
  }

  console.log('Left diagram initialized successfully')

  // 验证数据结构
  if (!leftDfgData.value.nodes || !Array.isArray(leftDfgData.value.nodes)) {
    console.error('Invalid nodes data:', leftDfgData.value.nodes)
    return
  }

  if (!leftDfgData.value.edges || !Array.isArray(leftDfgData.value.edges)) {
    console.error('Invalid edges data:', leftDfgData.value.edges)
    return
  }

  // 计算数据分布
  const distribution = calculateDataDistribution(leftDfgData.value.nodes as DFGNode[], leftDfgData.value.edges as DFGEdge[], currentDimension.value)
  console.log('Left DFG data distribution:', distribution, 'Dimension:', currentDimension.value)

  // 准备节点数据
  console.log('Preparing left DFG nodes:', leftDfgData.value.nodes.length)
  console.log('Sample node data:', leftDfgData.value.nodes[0])

  const nodeDataArray = leftDfgData.value.nodes.map((node: Record<string, unknown>): NodeData => {
    // 左侧（基准时间段）始终显示绝对数值
    const value = currentDimension.value === 'frequency'
      ? (node.frequency as number || 0)
      : ((node.avgDuration as number || 0) / 60000) // 转换为分钟

    // 检查是否为开始或结束节点
    const isStartNode = node.id === '开始' || (node.isStartNode as boolean)
    const isEndNode = node.id === '结束' || (node.isEndNode as boolean)

    let nodeStyle
    if (isStartNode) {
      // 开始节点特殊样式
      nodeStyle = {
        fill: '#4CAF50',
        stroke: '#2E7D32',
        strokeWidth: 3,
        textColor: '#FFFFFF'
      }
    } else if (isEndNode) {
      // 结束节点特殊样式
      nodeStyle = {
        fill: '#F44336',
        stroke: '#C62828',
        strokeWidth: 3,
        textColor: '#FFFFFF'
      }
    } else {
      // 普通活动节点使用数据驱动样式（基准时间段始终使用绝对数值样式）
      nodeStyle = getNodeStyle(value, distribution.nodePercentiles, currentDimension.value)
    }

    return {
      key: node.id as string,
      label: (node.label || node.id) as string,
      frequency: (node.frequency || 0) as number,
      avgDuration: (node.avgDuration || 0) as number,
      minDuration: (node.minDuration || 0) as number,
      maxDuration: (node.maxDuration || 0) as number,
      currentValue: value,
      dimension: currentDimension.value,
      displayMode: 'absolute', // 基准时间段始终为绝对模式
      nodeStyle,
      isStartNode,
      isEndNode,
      // 保持向后兼容
      color: nodeStyle.fill as string,
      borderColor: nodeStyle.stroke as string
    }
  })

  console.log('Left DFG prepared nodes:', nodeDataArray.map(n => ({ key: n.key, isStart: n.isStartNode, isEnd: n.isEndNode })))

  // 准备连接线数据
  const linkDataArray = leftDfgData.value.edges.map((edge: Record<string, unknown>) => {
    // 左侧（基准时间段）始终显示绝对数值
    const value = currentDimension.value === 'frequency'
      ? (edge.frequency as number || 0)
      : ((edge.avgDuration as number || 0) / 60000) // 转换为分钟

    const linkStyle = getLinkStyle(value, distribution.edgePercentiles, currentDimension.value)

    return {
      from: edge.source,
      to: edge.target,
      frequency: edge.frequency || 0,
      avgDuration: edge.avgDuration || 0,
      currentValue: value,
      dimension: currentDimension.value,
      displayMode: 'absolute', // 基准时间段始终为绝对模式
      linkStyle
    }
  })

  // 创建图表模型
  console.log('Creating left diagram model with:', nodeDataArray.length, 'nodes and', linkDataArray.length, 'links')
  const model = new go.GraphLinksModel(nodeDataArray, linkDataArray)
  leftDiagram.model = model

  console.log('Left diagram model created, node count:', leftDiagram.model.nodeDataArray.length)

  // 如果没有数据，记录警告但不添加测试节点
  if (nodeDataArray.length === 0) {
    console.warn('No node data available for left diagram')
    return
  }

  // 自动布局
  leftDiagram.layoutDiagram(true)
  console.log('Left diagram layout completed')

  // 对齐开始和结束节点
  alignStartEndNodes(leftDiagram)

  // 立即进行缩放以填满容器
  console.log('Container dimensions:', leftDfgContainer.value?.offsetWidth, 'x', leftDfgContainer.value?.offsetHeight)
  leftDiagram.zoomToFit()
  console.log('Left diagram scale after zoomToFit:', leftDiagram.scale)

  // 强制设置更大的缩放比例
  if (leftDiagram.scale < 1.0) {
    leftDiagram.scale = 1.2
    console.log('Left diagram scale forced to:', leftDiagram.scale)
  }

  // 设置联动事件
  setupDiagramSync(leftDiagram, rightDiagram)

  setTimeout(() => {
    if (leftDiagram) {
      // 强制刷新图表
      leftDiagram.requestUpdate()

      // 使用更合适的缩放方法，确保图表填满容器
      leftDiagram.zoomToFit()
      // 如果图表太小，设置最小缩放比例
      if (leftDiagram.scale < 0.5) {
        leftDiagram.scale = 0.8
      }

      // 添加交互测试事件监听器
      leftDiagram.addDiagramListener('ViewportBoundsChanged', () => {
        console.log('Left diagram viewport changed - interaction working!')
      })

      console.log('Left diagram final setup completed, visible nodes:', leftDiagram.nodes.count)
    }
  }, 200)
}

const renderRightDFG = async () => {
  console.log('=== renderRightDFG called ===')
  console.log('rightDfgContainer.value:', rightDfgContainer.value)
  console.log('rightDfgData.value:', rightDfgData.value)

  if (!rightDfgContainer.value || !rightDfgData.value) {
    console.log('Missing container or data, returning')
    return
  }

  console.log('Container dimensions:', rightDfgContainer.value.offsetWidth, 'x', rightDfgContainer.value.offsetHeight)
  console.log('DFG data nodes:', rightDfgData.value.nodes?.length)
  console.log('DFG data edges:', rightDfgData.value.edges?.length)

  // 清理现有图表
  if (rightDiagram) {
    rightDiagram.div = null
    rightDiagram = null
  }

  rightDfgContainer.value.innerHTML = ''
  rightDiagram = initDiagram(rightDfgContainer.value)

  if (!rightDiagram) {
    console.error('Failed to initialize right diagram')
    return
  }

  console.log('Right diagram initialized successfully')

  // 获取左侧图表的节点位置信息用于对齐
  const leftNodePositions = new Map<string, go.Point>()
  if (leftDiagram) {
    leftDiagram.nodes.each((node: go.Node) => {
      leftNodePositions.set(node.data.key, node.location.copy())
    })
    console.log('Collected left diagram node positions:', leftNodePositions.size)
  }

  // 验证数据结构
  if (!rightDfgData.value.nodes || !Array.isArray(rightDfgData.value.nodes)) {
    console.error('Invalid nodes data:', rightDfgData.value.nodes)
    return
  }

  if (!rightDfgData.value.edges || !Array.isArray(rightDfgData.value.edges)) {
    console.error('Invalid edges data:', rightDfgData.value.edges)
    return
  }

  // 计算数据分布
  const distribution = calculateDataDistribution(rightDfgData.value.nodes as DFGNode[], rightDfgData.value.edges as DFGEdge[], currentDimension.value)
  console.log('Right DFG data distribution:', distribution, 'Dimension:', currentDimension.value)

  // 准备节点数据
  console.log('Preparing right DFG nodes:', rightDfgData.value.nodes.length)
  console.log('Sample node data:', rightDfgData.value.nodes[0])

  const nodeDataArray = rightDfgData.value.nodes.map((node: Record<string, unknown>) => {
    const baseValue = currentDimension.value === 'frequency'
      ? (node.frequency as number || 0)
      : ((node.avgDuration as number || 0) / 60000) // 转换为分钟

    // 计算显示值（根据展示方式）
    let displayValue = baseValue
    let displayText = ''

    // 检查是否为开始或结束节点
    const isStartNode = node.id === '开始' || (node.isStartNode as boolean)
    const isEndNode = node.id === '结束' || (node.isEndNode as boolean)

    // 对于比较分析，需要根据displayMode计算显示值
    if (!isStartNode && !isEndNode && leftDfgData.value) {
      const leftNode = leftDfgData.value.nodes?.find((ln: Record<string, unknown>) => ln.id === node.id)
      if (leftNode) {
        const leftValue = currentDimension.value === 'frequency'
          ? (leftNode.frequency as number || 0)
          : ((leftNode.avgDuration as number || 0) / 60000)

        console.log(`Node ${node.id}: displayMode=${displayMode.value}, baseValue=${baseValue}, leftValue=${leftValue}`)

        switch (displayMode.value) {
          case 'absolute':
            displayValue = baseValue
            break
          case 'difference':
            displayValue = baseValue - leftValue
            displayText = displayValue >= 0 ? `+${displayValue.toFixed(1)}` : displayValue.toFixed(1)
            console.log(`Node ${node.id} difference: ${displayText}`)
            break
          case 'percentage':
            if (leftValue > 0) {
              displayValue = ((baseValue - leftValue) / leftValue) * 100
              displayText = displayValue >= 0 ? `+${displayValue.toFixed(1)}%` : `${displayValue.toFixed(1)}%`
            } else {
              displayValue = 0
              displayText = '0%'
            }
            console.log(`Node ${node.id} percentage: ${displayText}`)
            break
        }
      }
    }

    let nodeStyle
    if (isStartNode) {
      // 开始节点特殊样式
      nodeStyle = {
        fill: '#4CAF50',
        stroke: '#2E7D32',
        strokeWidth: 3,
        textColor: '#FFFFFF'
      }
    } else if (isEndNode) {
      // 结束节点特殊样式
      nodeStyle = {
        fill: '#F44336',
        stroke: '#C62828',
        strokeWidth: 3,
        textColor: '#FFFFFF'
      }
    } else {
      // 普通活动节点使用数据驱动样式
      // 对于差异和百分比模式，使用不同的颜色映射
      if (displayMode.value === 'absolute') {
        nodeStyle = getNodeStyle(baseValue, distribution.nodePercentiles, currentDimension.value)
      } else {
        nodeStyle = getCompareNodeStyle(displayValue, displayMode.value, currentDimension.value)
      }
    }

    // 获取对应左侧节点的位置
    const leftPosition = leftNodePositions.get(node.id as string)
    const nodeData: NodeData = {
      key: node.id as string,
      label: (node.label || node.id) as string,
      frequency: (node.frequency || 0) as number,
      avgDuration: (node.avgDuration || 0) as number,
      minDuration: (node.minDuration || 0) as number,
      maxDuration: (node.maxDuration || 0) as number,
      currentValue: displayValue,
      displayText: displayText,
      dimension: currentDimension.value,
      displayMode: displayMode.value,
      nodeStyle,
      isStartNode,
      isEndNode,
      // 保持向后兼容
      color: nodeStyle.fill as string,
      borderColor: nodeStyle.stroke as string
    }

    // 如果左侧有对应位置，则设置位置信息
    if (leftPosition) {
      nodeData.loc = go.Point.stringify(leftPosition)
      console.log(`Setting position for node ${node.id}:`, leftPosition.toString())
    }

    return nodeData
  })

  console.log('Right DFG prepared nodes:', nodeDataArray.map(n => ({ key: n.key, isStart: n.isStartNode, isEnd: n.isEndNode, hasLoc: !!n.loc })))

  // 准备连接线数据
  const linkDataArray = rightDfgData.value.edges.map((edge: Record<string, unknown>) => {
    const baseValue = currentDimension.value === 'frequency'
      ? (edge.frequency as number || 0)
      : ((edge.avgDuration as number || 0) / 60000) // 转换为分钟

    // 计算显示值（根据展示方式）
    let displayValue = baseValue
    let displayText = ''

    // 对于比较分析，需要根据displayMode计算显示值
    if (leftDfgData.value) {
      const leftEdge = leftDfgData.value.edges?.find((le: Record<string, unknown>) =>
        le.source === edge.source && le.target === edge.target)
      if (leftEdge) {
        const leftValue = currentDimension.value === 'frequency'
          ? (leftEdge.frequency as number || 0)
          : ((leftEdge.avgDuration as number || 0) / 60000)

        switch (displayMode.value) {
          case 'absolute':
            displayValue = baseValue
            break
          case 'difference':
            displayValue = baseValue - leftValue
            displayText = displayValue >= 0 ? `+${displayValue.toFixed(1)}` : displayValue.toFixed(1)
            break
          case 'percentage':
            if (leftValue > 0) {
              displayValue = ((baseValue - leftValue) / leftValue) * 100
              displayText = displayValue >= 0 ? `+${displayValue.toFixed(1)}%` : `${displayValue.toFixed(1)}%`
            } else {
              displayValue = 0
              displayText = '0%'
            }
            break
        }
      }
    }

    const linkStyle = displayMode.value === 'absolute'
      ? getLinkStyle(baseValue, distribution.edgePercentiles, currentDimension.value)
      : getCompareLinkStyle(displayValue, displayMode.value, currentDimension.value)

    return {
      from: edge.source,
      to: edge.target,
      frequency: edge.frequency || 0,
      avgDuration: edge.avgDuration || 0,
      currentValue: displayValue,
      displayText: displayText,
      dimension: currentDimension.value,
      displayMode: displayMode.value,
      linkStyle
    }
  })

  // 创建图表模型
  console.log('Creating right diagram model with:', nodeDataArray.length, 'nodes and', linkDataArray.length, 'links')
  const model = new go.GraphLinksModel(nodeDataArray, linkDataArray)
  rightDiagram.model = model

  console.log('Right diagram model created, node count:', rightDiagram.model.nodeDataArray.length)

  // 如果没有数据，记录警告但不添加测试节点
  if (nodeDataArray.length === 0) {
    console.warn('No node data available for right diagram')
    return
  }

  // 如果有位置信息，跳过自动布局；否则进行自动布局
  if (leftNodePositions.size > 0) {
    console.log('Skipping auto layout for right diagram - using left diagram positions')
    // 不进行自动布局，直接使用设置的位置
    // 但需要确保图表刷新以应用位置
    rightDiagram.layoutDiagram(false) // 不重新计算布局，只刷新显示
  } else {
    console.log('No left diagram positions found, performing auto layout')
    // 自动布局
    rightDiagram.layoutDiagram(true)
    // 对齐开始和结束节点
    alignStartEndNodes(rightDiagram)
  }
  console.log('Right diagram layout completed')

  // 立即进行缩放以填满容器
  console.log('Container dimensions:', rightDfgContainer.value?.offsetWidth, 'x', rightDfgContainer.value?.offsetHeight)
  rightDiagram.zoomToFit()
  console.log('Right diagram scale after zoomToFit:', rightDiagram.scale)

  // 强制设置更大的缩放比例
  if (rightDiagram.scale < 1.0) {
    rightDiagram.scale = 1.2
    console.log('Right diagram scale forced to:', rightDiagram.scale)
  }

  // 如果有左侧位置信息，强制同步节点位置
  if (leftNodePositions.size > 0) {
    setTimeout(() => {
      if (rightDiagram) {
        console.log('Force syncing node positions...')
        rightDiagram.startTransaction('sync positions')
        try {
          rightDiagram.nodes.each((node: go.Node) => {
            const leftPosition = leftNodePositions.get(node.data.key)
            if (leftPosition) {
              node.location = leftPosition.copy()
              console.log(`Synced position for node ${node.data.key}:`, leftPosition.toString())
            }
          })
        } finally {
          rightDiagram.commitTransaction('sync positions')
        }
      }
    }, 100)
  }

  // 设置联动事件
  setupDiagramSync(rightDiagram, leftDiagram)

  setTimeout(() => {
    if (rightDiagram) {
      // 强制刷新图表
      rightDiagram.requestUpdate()

      // 使用更合适的缩放方法，确保图表填满容器
      rightDiagram.zoomToFit()
      // 如果图表太小，设置最小缩放比例
      if (rightDiagram.scale < 0.5) {
        rightDiagram.scale = 0.8
      }

      // 添加交互测试事件监听器
      rightDiagram.addDiagramListener('ViewportBoundsChanged', () => {
        console.log('Right diagram viewport changed - interaction working!')
      })

      console.log('Right diagram final setup completed, visible nodes:', rightDiagram.nodes.count)
    }
  }, 200)
}

// 数据驱动的颜色映射算法（专业流程挖掘标准）
const calculateDataDistribution = (nodes: DFGNode[], edges: DFGEdge[], dimension: 'frequency' | 'duration' = 'frequency'): DataDistribution => {
  let nodeValues: number[] = []
  let edgeValues: number[] = []

  if (dimension === 'frequency') {
    nodeValues = nodes.map(n => n.frequency || 0).filter(f => f > 0)
    edgeValues = edges.map(e => e.frequency || 0).filter(f => f > 0)
  } else {
    // 耗时维度：使用平均耗时（毫秒转换为分钟）
    nodeValues = nodes.map(n => (n.avgDuration || 0) / 60000).filter(d => d > 0)
    edgeValues = edges.map(e => (e.avgDuration || 0) / 60000).filter(d => d > 0)
  }

  if (nodeValues.length === 0 && edgeValues.length === 0) {
    return {
      nodePercentiles: { p25: 0, p50: 0, p75: 0, p90: 0, max: 0, logScale: false },
      edgePercentiles: { p25: 0, p50: 0, p75: 0, p90: 0, max: 0, logScale: false }
    }
  }

  const calculateAdvancedPercentiles = (values: number[]) => {
    if (values.length === 0) return { p25: 0, p50: 0, p75: 0, p90: 0, max: 0, logScale: false }

    const sorted = [...values].sort((a, b) => a - b)
    const min = sorted[0]
    const max = sorted[sorted.length - 1]

    // 检查是否需要对数缩放（数据跨度超过2个数量级）
    const needsLogScale = dimension === 'duration' && max > 0 && min > 0 && (max / min) > 100

    let processedValues = sorted
    if (needsLogScale) {
      // 对数变换以处理高度偏斜的耗时数据
      processedValues = sorted.map(v => Math.log10(Math.max(v, 0.1))) // 避免log(0)
    }

    // 使用更精确的百分位数计算
    const getPercentile = (arr: number[], p: number) => {
      const index = (arr.length - 1) * p
      const lower = Math.floor(index)
      const upper = Math.ceil(index)
      const weight = index % 1

      if (upper >= arr.length) return arr[arr.length - 1]
      return arr[lower] * (1 - weight) + arr[upper] * weight
    }

    const percentiles = {
      p25: getPercentile(processedValues, 0.25),
      p50: getPercentile(processedValues, 0.5),
      p75: getPercentile(processedValues, 0.75),
      p90: getPercentile(processedValues, 0.9),
      max: processedValues[processedValues.length - 1],
      logScale: needsLogScale,
      originalMax: max,
      originalMin: min
    }

    // 如果使用了对数缩放，需要将百分位数转换回原始尺度
    if (needsLogScale) {
      return {
        ...percentiles,
        p25: Math.pow(10, percentiles.p25),
        p50: Math.pow(10, percentiles.p50),
        p75: Math.pow(10, percentiles.p75),
        p90: Math.pow(10, percentiles.p90),
        max: Math.pow(10, percentiles.max)
      }
    }

    return percentiles
  }

  return {
    nodePercentiles: calculateAdvancedPercentiles(nodeValues),
    edgePercentiles: calculateAdvancedPercentiles(edgeValues)
  }
}

// 动态节点样式映射（专业流程挖掘标准）
const getNodeStyle = (value: number, percentiles: PercentileData, dimension: 'frequency' | 'duration' = 'frequency') => {
  const { p25, p50, p75, p90, max, logScale } = percentiles

  // 根据维度选择不同的颜色方案
  const colorSchemes = {
    frequency: {
      high: { fill: '#2E7D32', stroke: '#1B5E20', textColor: '#FFFFFF' }, // 深绿色 - 高频
      mediumHigh: { fill: '#388E3C', stroke: '#2E7D32', textColor: '#FFFFFF' }, // 中深绿色
      medium: { fill: '#4CAF50', stroke: '#388E3C', textColor: '#FFFFFF' }, // 标准绿色
      mediumLow: { fill: '#81C784', stroke: '#4CAF50', textColor: '#1B5E20' }, // 浅绿色
      low: { fill: '#C8E6C9', stroke: '#81C784', textColor: '#2E7D32' } // 很浅绿色
    },
    duration: {
      high: { fill: '#D32F2F', stroke: '#B71C1C', textColor: '#FFFFFF' }, // 深红色 - 高耗时
      mediumHigh: { fill: '#F44336', stroke: '#D32F2F', textColor: '#FFFFFF' }, // 红色
      medium: { fill: '#FF9800', stroke: '#F57C00', textColor: '#FFFFFF' }, // 橙色
      mediumLow: { fill: '#FFC107', stroke: '#FF9800', textColor: '#1B5E20' }, // 黄色
      low: { fill: '#4CAF50', stroke: '#388E3C', textColor: '#FFFFFF' } // 绿色 - 低耗时
    }
  }

  const colors = colorSchemes[dimension]

  // 计算标准化值（0-1范围）用于尺寸映射
  let normalizedValue = max > 0 ? value / max : 0

  // 对于对数缩放的数据，使用更平滑的映射
  if (logScale && dimension === 'duration') {
    const logValue = Math.log10(Math.max(value, 0.1))
    const logMax = Math.log10(Math.max(max, 0.1))
    normalizedValue = logMax > 0 ? logValue / logMax : 0
  }

  // 使用更科学的尺寸映射算法
  const getSizeMultiplier = (normalizedVal: number) => {
    // 使用平方根函数使尺寸差异更加明显但不过于极端
    return Math.sqrt(normalizedVal)
  }

  const sizeMultiplier = getSizeMultiplier(normalizedValue)

  if (value >= p90) {
    return {
      ...colors.high,
      strokeWidth: 3,
      width: Math.max(120, Math.min(160, 120 + sizeMultiplier * 40)),
      height: Math.max(60, Math.min(80, 60 + sizeMultiplier * 20))
    }
  } else if (value >= p75) {
    return {
      ...colors.mediumHigh,
      strokeWidth: 2.5,
      width: Math.max(100, Math.min(140, 100 + sizeMultiplier * 40)),
      height: Math.max(55, Math.min(75, 55 + sizeMultiplier * 20))
    }
  } else if (value >= p50) {
    return {
      ...colors.medium,
      strokeWidth: 2,
      width: Math.max(90, Math.min(120, 90 + sizeMultiplier * 30)),
      height: Math.max(50, Math.min(70, 50 + sizeMultiplier * 20))
    }
  } else if (value >= p25) {
    return {
      ...colors.mediumLow,
      strokeWidth: 2,
      width: Math.max(85, Math.min(110, 85 + sizeMultiplier * 25)),
      height: Math.max(45, Math.min(65, 45 + sizeMultiplier * 20))
    }
  } else {
    return {
      ...colors.low,
      strokeWidth: 1.5,
      width: Math.max(80, Math.min(100, 80 + sizeMultiplier * 20)),
      height: Math.max(40, Math.min(60, 40 + sizeMultiplier * 20))
    }
  }
}

// 比较模式的节点样式映射（用于差异和百分比显示）
const getCompareNodeStyle = (value: number, displayMode: 'difference' | 'percentage', dimension: 'frequency' | 'duration' = 'frequency') => {
  // 差异和百分比模式的颜色方案
  const colorSchemes = {
    positive: {
      high: { fill: '#2E7D32', stroke: '#1B5E20', textColor: '#FFFFFF' }, // 深绿色 - 大幅增加
      medium: { fill: '#4CAF50', stroke: '#388E3C', textColor: '#FFFFFF' }, // 绿色 - 中等增加
      low: { fill: '#81C784', stroke: '#4CAF50', textColor: '#1B5E20' } // 浅绿色 - 小幅增加
    },
    negative: {
      high: { fill: '#D32F2F', stroke: '#B71C1C', textColor: '#FFFFFF' }, // 深红色 - 大幅减少
      medium: { fill: '#F44336', stroke: '#D32F2F', textColor: '#FFFFFF' }, // 红色 - 中等减少
      low: { fill: '#FFCDD2', stroke: '#F44336', textColor: '#D32F2F' } // 浅红色 - 小幅减少
    },
    neutral: { fill: '#9E9E9E', stroke: '#616161', textColor: '#FFFFFF' } // 灰色 - 无变化
  }

  // 根据显示模式确定阈值
  let thresholds
  if (displayMode === 'percentage') {
    // 百分比模式的阈值
    thresholds = { low: 5, medium: 20, high: 50 } // 5%, 20%, 50%
  } else {
    // 差异模式的阈值（根据维度调整）
    if (dimension === 'frequency') {
      thresholds = { low: 1, medium: 5, high: 20 } // 频次差异
    } else {
      thresholds = { low: 5, medium: 30, high: 120 } // 耗时差异（分钟）
    }
  }

  const absValue = Math.abs(value)

  if (value === 0) {
    return {
      ...colorSchemes.neutral,
      strokeWidth: 2,
      width: 90,
      height: 50
    }
  } else if (value > 0) {
    // 正值（增加）
    if (absValue >= thresholds.high) {
      return {
        ...colorSchemes.positive.high,
        strokeWidth: 3,
        width: 120,
        height: 60
      }
    } else if (absValue >= thresholds.medium) {
      return {
        ...colorSchemes.positive.medium,
        strokeWidth: 2.5,
        width: 105,
        height: 55
      }
    } else {
      return {
        ...colorSchemes.positive.low,
        strokeWidth: 2,
        width: 95,
        height: 50
      }
    }
  } else {
    // 负值（减少）
    if (absValue >= thresholds.high) {
      return {
        ...colorSchemes.negative.high,
        strokeWidth: 3,
        width: 120,
        height: 60
      }
    } else if (absValue >= thresholds.medium) {
      return {
        ...colorSchemes.negative.medium,
        strokeWidth: 2.5,
        width: 105,
        height: 55
      }
    } else {
      return {
        ...colorSchemes.negative.low,
        strokeWidth: 2,
        width: 95,
        height: 50
      }
    }
  }
}

// 比较模式的连接线样式映射（用于差异和百分比显示）
const getCompareLinkStyle = (value: number, displayMode: 'difference' | 'percentage', dimension: 'frequency' | 'duration' = 'frequency') => {
  // 差异和百分比模式的颜色方案
  const colorSchemes = {
    positive: {
      high: '#2E7D32', // 深绿色 - 大幅增加
      medium: '#4CAF50', // 绿色 - 中等增加
      low: '#81C784' // 浅绿色 - 小幅增加
    },
    negative: {
      high: '#D32F2F', // 深红色 - 大幅减少
      medium: '#F44336', // 红色 - 中等减少
      low: '#FFCDD2' // 浅红色 - 小幅减少
    },
    neutral: '#9E9E9E' // 灰色 - 无变化
  }

  // 根据显示模式确定阈值
  let thresholds
  if (displayMode === 'percentage') {
    // 百分比模式的阈值
    thresholds = { low: 5, medium: 20, high: 50 } // 5%, 20%, 50%
  } else {
    // 差异模式的阈值（根据维度调整）
    if (dimension === 'frequency') {
      thresholds = { low: 1, medium: 5, high: 20 } // 频次差异
    } else {
      thresholds = { low: 5, medium: 30, high: 120 } // 耗时差异（分钟）
    }
  }

  const absValue = Math.abs(value)

  if (value === 0) {
    return {
      stroke: colorSchemes.neutral,
      strokeWidth: 2,
      arrowScale: 1.2
    }
  } else if (value > 0) {
    // 正值（增加）
    if (absValue >= thresholds.high) {
      return {
        stroke: colorSchemes.positive.high,
        strokeWidth: 6,
        arrowScale: 1.8
      }
    } else if (absValue >= thresholds.medium) {
      return {
        stroke: colorSchemes.positive.medium,
        strokeWidth: 4,
        arrowScale: 1.5
      }
    } else {
      return {
        stroke: colorSchemes.positive.low,
        strokeWidth: 3,
        arrowScale: 1.3
      }
    }
  } else {
    // 负值（减少）
    if (absValue >= thresholds.high) {
      return {
        stroke: colorSchemes.negative.high,
        strokeWidth: 6,
        arrowScale: 1.8
      }
    } else if (absValue >= thresholds.medium) {
      return {
        stroke: colorSchemes.negative.medium,
        strokeWidth: 4,
        arrowScale: 1.5
      }
    } else {
      return {
        stroke: colorSchemes.negative.low,
        strokeWidth: 3,
        arrowScale: 1.3
      }
    }
  }
}

// 动态连接线样式映射（专业流程挖掘标准）
const getLinkStyle = (value: number, percentiles: PercentileData, dimension: 'frequency' | 'duration' = 'frequency') => {
  const { p25, p50, p75, p90, max, logScale } = percentiles

  // 根据维度选择不同的颜色方案
  const colorSchemes = {
    frequency: {
      high: '#D32F2F',    // 深红色 - 高频连接
      mediumHigh: '#F44336', // 红色
      medium: '#FF9800',     // 橙色
      mediumLow: '#2196F3',  // 蓝色
      low: '#90CAF9'         // 浅蓝色 - 低频连接
    },
    duration: {
      high: '#D32F2F',      // 深红色 - 高耗时连接
      mediumHigh: '#F44336', // 红色
      medium: '#FF9800',     // 橙色
      mediumLow: '#FFC107',  // 黄色
      low: '#4CAF50'         // 绿色 - 低耗时连接
    }
  }

  const colors = colorSchemes[dimension]

  // 计算标准化值用于线宽映射
  let normalizedValue = max > 0 ? value / max : 0

  // 对于对数缩放的数据，使用更平滑的映射
  if (logScale && dimension === 'duration') {
    const logValue = Math.log10(Math.max(value, 0.1))
    const logMax = Math.log10(Math.max(max, 0.1))
    normalizedValue = logMax > 0 ? logValue / logMax : 0
  }

  // 使用更科学的线宽映射算法
  const getStrokeMultiplier = (normalizedVal: number) => {
    // 使用平方根函数使线宽差异更加明显但不过于极端
    return Math.sqrt(normalizedVal)
  }

  const strokeMultiplier = getStrokeMultiplier(normalizedValue)

  if (value >= p90) {
    const strokeWidth = Math.max(6, Math.min(12, 6 + strokeMultiplier * 6))
    return {
      stroke: colors.high,
      strokeWidth,
      arrowScale: Math.max(1.2, Math.min(2.5, 0.8 + strokeWidth * 0.15)) // 箭头大小与线宽成正比
    }
  } else if (value >= p75) {
    const strokeWidth = Math.max(5, Math.min(10, 5 + strokeMultiplier * 5))
    return {
      stroke: colors.mediumHigh,
      strokeWidth,
      arrowScale: Math.max(1.1, Math.min(2.2, 0.8 + strokeWidth * 0.15))
    }
  } else if (value >= p50) {
    const strokeWidth = Math.max(4, Math.min(8, 4 + strokeMultiplier * 4))
    return {
      stroke: colors.medium,
      strokeWidth,
      arrowScale: Math.max(1.0, Math.min(2.0, 0.8 + strokeWidth * 0.15))
    }
  } else if (value >= p25) {
    const strokeWidth = Math.max(3, Math.min(6, 3 + strokeMultiplier * 3))
    return {
      stroke: colors.mediumLow,
      strokeWidth,
      arrowScale: Math.max(0.9, Math.min(1.8, 0.8 + strokeWidth * 0.15))
    }
  } else {
    const strokeWidth = Math.max(2, Math.min(4, 2 + strokeMultiplier * 2))
    return {
      stroke: colors.low,
      strokeWidth,
      arrowScale: Math.max(0.8, Math.min(1.5, 0.8 + strokeWidth * 0.15))
    }
  }
}

// 对齐开始和结束节点到垂直中心线
const alignStartEndNodes = (diagram: go.Diagram) => {
  if (!diagram) return

  console.log('Aligning start and end nodes...')

  let startNode: go.Node | undefined = undefined
  let endNode: go.Node | undefined = undefined
  const activityNodes: go.Node[] = []

  // 找到开始、结束和活动节点
  diagram.nodes.each((node: go.Node) => {
    if (node.data.isStartNode) {
      startNode = node
    } else if (node.data.isEndNode) {
      endNode = node
    } else {
      activityNodes.push(node)
    }
  })

  if (!startNode || !endNode) {
    console.log('Start or end node not found for alignment')
    return
  }

  // 计算所有活动节点的水平中心位置
  let totalX = 0
  let nodeCount = 0

  activityNodes.forEach(node => {
    totalX += node.location.x
    nodeCount++
  })

  if (nodeCount === 0) {
    console.log('No activity nodes found for alignment')
    return
  }

  const centerX = totalX / nodeCount
  console.log(`Calculated center X: ${centerX} from ${nodeCount} activity nodes`)

  // 对齐开始和结束节点到中心线
  diagram.startTransaction('align start end nodes')
  try {
    if (startNode) {
      (startNode as go.Node).location = new go.Point(centerX, (startNode as go.Node).location.y)
      console.log(`Aligned start node to (${centerX}, ${(startNode as go.Node).location.y})`)
    }
    if (endNode) {
      (endNode as go.Node).location = new go.Point(centerX, (endNode as go.Node).location.y)
      console.log(`Aligned end node to (${centerX}, ${(endNode as go.Node).location.y})`)
    }
  } finally {
    diagram.commitTransaction('align start end nodes')
  }
}

// 图表联动同步
const setupDiagramSync = (sourceDiagram: go.Diagram | null, targetDiagram: go.Diagram | null) => {
  if (!sourceDiagram || !targetDiagram) return

  let isUpdating = false

  // 监听源图表的视图变化
  sourceDiagram.addDiagramListener('ViewportBoundsChanged', () => {
    if (isUpdating || !targetDiagram) return

    isUpdating = true
    try {
      // 同步缩放和位置
      targetDiagram.scale = sourceDiagram.scale
      targetDiagram.position = sourceDiagram.position.copy()
    } finally {
      setTimeout(() => {
        isUpdating = false
      }, 50)
    }
  })

  // 监听目标图表的视图变化
  targetDiagram.addDiagramListener('ViewportBoundsChanged', () => {
    if (isUpdating || !sourceDiagram) return

    isUpdating = true
    try {
      // 同步缩放和位置
      sourceDiagram.scale = targetDiagram.scale
      sourceDiagram.position = targetDiagram.position.copy()
    } finally {
      setTimeout(() => {
        isUpdating = false
      }, 50)
    }
  })
}

// 窗口大小变化处理
const handleResize = () => {
  if (leftDiagram && rightDiagram) {
    leftDiagram.requestUpdate()
    rightDiagram.requestUpdate()

    // 重新缩放以适应新的容器大小
    setTimeout(() => {
      leftDiagram?.zoomToFit()
      rightDiagram?.zoomToFit()

      // 强制设置更大的缩放比例
      if (leftDiagram && leftDiagram.scale < 1.0) {
        leftDiagram.scale = 1.2
        console.log('Left diagram scale forced to:', leftDiagram.scale)
      }
      if (rightDiagram && rightDiagram.scale < 1.0) {
        rightDiagram.scale = 1.2
        console.log('Right diagram scale forced to:', rightDiagram.scale)
      }
    }, 100)
  }
}

// 页面初始化
onMounted(async () => {
  try {
    // 尝试获取已存在的流程发现结果
    console.log('=== Loading original discovery result ===')
    const result = await api.getAnalysisResult(processId, AnalysisType.PROCESS_DISCOVERY)
    console.log('Original discovery result:', result)

    if (result && typeof result === 'object') {
      originalDiscoveryResult.value = result as DFGResult
      console.log('Original discovery result loaded:', originalDiscoveryResult.value)
      console.log('Original nodes count:', originalDiscoveryResult.value?.nodes?.length)
      console.log('Original edges count:', originalDiscoveryResult.value?.edges?.length)
    } else {
      console.warn('No original discovery result found - user needs to run process discovery first')
    }
  } catch (error) {
    console.debug('No existing analysis result found:', error)
  } finally {
    isLoading.value = false
  }

  // 添加窗口大小变化监听器
  window.addEventListener('resize', handleResize)
})

// 组件卸载时清理资源
onUnmounted(() => {
  // 移除窗口大小变化监听器
  window.removeEventListener('resize', handleResize)

  if (leftDiagram) {
    leftDiagram.div = null
    leftDiagram = null
  }
  if (rightDiagram) {
    rightDiagram.div = null
    rightDiagram = null
  }
})
</script>