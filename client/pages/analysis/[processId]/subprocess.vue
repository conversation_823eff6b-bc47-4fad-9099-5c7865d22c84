<template>
  <div class="subprocess-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-title">
            <el-button
              @click="navigateTo(`/processes/${processId}`)"
              :icon="ArrowLeft"
              text
              class="back-button"
            />
            <div class="title-section">
              <h1 class="title">
                子流程发现
              </h1>
              <p class="description">
                自动识别和发掘复杂流程中的子流程模式
              </p>
            </div>
          </div>
        </div>

        <!-- 导航选项卡 - 移动到右上角 -->
        <div class="header-tabs">
          <div class="tabs-container">
            <el-button
              :type="currentTab === 'basic' ? 'primary' : 'default'"
              :icon="Search"
              @click="switchTab('basic')"
              class="tab-button"
              size="small"
            >
              基础流程发现
            </el-button>
            <el-button
              :type="currentTab === 'subprocess' ? 'primary' : 'default'"
              :icon="Share"
              @click="switchTab('subprocess')"
              class="tab-button"
              size="small"
            >
              子流程发现
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <el-card>
        <div class="control-content">
          <div class="control-actions">
            <el-button
              type="primary"
              :icon="Search"
              @click="startSubprocessDiscovery"
              :loading="isDiscovering"
            >
              开始子流程发现
            </el-button>
            <el-button
              v-if="discoveryResult"
              type="warning"
              :icon="Refresh"
              @click="refreshDiscovery"
              :loading="isDiscovering"
            >
              重新分析
            </el-button>
            <el-button
              :icon="Setting"
              @click="showOptionsDialog = true"
            >
              配置参数
            </el-button>
          </div>

          <div v-if="discoveryResult" class="result-info">
            <div class="discovery-stats">
              <el-tag type="success" size="small">
                发现 {{ discoveryResult.subprocesses?.length || 0 }} 个子流程
              </el-tag>
              <el-tag type="info" size="small">
                压缩率 {{ Math.round((discoveryResult.statistics?.compressionRatio || 0) * 100) }}%
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 分析加载状态 -->
    <div v-if="isDiscovering" class="analyzing-state">
      <div class="loading-spinner" />
      <p class="analyzing-text">正在分析子流程模式...</p>
    </div>

    <!-- 发现结果 -->
    <div v-else-if="discoveryResult" class="discovery-results">
      <div class="grid-layout">
        <!-- 层次化DFG区域 -->
        <div class="hierarchical-dfg-section">
          <el-card class="dfg-card">
            <template #header>
              <div class="dfg-header">
                <h3 class="dfg-title">层次化流程图</h3>
                <div class="dfg-controls">
                  <el-button size="small" @click="zoomIn" :icon="Plus">放大</el-button>
                  <el-button size="small" @click="zoomOut" :icon="Minus">缩小</el-button>
                  <el-button size="small" @click="resetZoom" :icon="RefreshRight">重置</el-button>
                </div>
              </div>
            </template>
            <div class="dfg-container">
              <div ref="hierarchicalDfgContainer" class="dfg-canvas" />
            </div>
          </el-card>
        </div>

        <!-- 子流程模式列表 -->
        <div class="subprocess-patterns-section">
          <el-card class="patterns-card">
            <template #header>
              <h3 class="patterns-title">发现的子流程模式</h3>
            </template>
            <div class="patterns-container">
              <div v-for="pattern in discoveryResult.subprocesses" :key="pattern.id" class="pattern-item">
                <div class="pattern-header">
                  <div class="pattern-info">
                    <h4 class="pattern-name">{{ pattern.name }}</h4>
                    <div class="pattern-meta">
                      <el-tag :type="getPatternTypeColor(pattern.type)" size="small">
                        {{ getPatternTypeLabel(pattern.type) }}
                      </el-tag>
                      <span class="pattern-frequency">频率: {{ pattern.frequency }}</span>
                      <span class="pattern-confidence">置信度: {{ Math.round(pattern.confidence * 100) }}%</span>
                    </div>
                  </div>
                  <div class="pattern-actions">
                    <el-button
                      size="small"
                      type="primary"
                      :icon="Search"
                      @click="highlightSubprocessInDiagram(pattern.id)"
                    >
                      在图中定位
                    </el-button>
                    <el-button
                      size="small"
                      :icon="expandedPatterns.has(pattern.id) ? ArrowUp : ArrowDown"
                      @click="togglePatternDetails(pattern.id)"
                    >
                      {{ expandedPatterns.has(pattern.id) ? '收起' : '展开' }}
                    </el-button>
                  </div>
                </div>
                
                <div v-if="expandedPatterns.has(pattern.id)" class="pattern-details">
                  <div class="pattern-activities">
                    <h5>包含活动:</h5>
                    <div class="activity-flow">
                      <span v-for="(activity, index) in pattern.activities" :key="index" class="activity-node">
                        {{ activity }}
                        <el-icon v-if="index < pattern.activities.length - 1" class="arrow-icon">
                          <ArrowRight />
                        </el-icon>
                      </span>
                    </div>
                  </div>
                  <div class="pattern-stats">
                    <div class="stat-item">
                      <span class="stat-label">平均耗时:</span>
                      <span class="stat-value">{{ formatDuration(pattern.avgDuration) }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">涉及案例:</span>
                      <span class="stat-value">{{ pattern.cases.length }} 个</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 统计信息 -->
        <div class="statistics-section">
          <el-card class="statistics-card">
            <template #header>
              <h3 class="statistics-title">分析统计</h3>
            </template>
            <div class="statistics-grid">
              <div class="statistic-item">
                <div class="statistic-value statistic-value--blue">
                  {{ discoveryResult.statistics?.totalSubprocesses || 0 }}
                </div>
                <div class="statistic-label">子流程总数</div>
              </div>
              <div class="statistic-item">
                <div class="statistic-value statistic-value--green">
                  {{ Math.round(discoveryResult.statistics?.avgSubprocessLength || 0) }}
                </div>
                <div class="statistic-label">平均长度</div>
              </div>
              <div class="statistic-item">
                <div class="statistic-value statistic-value--purple">
                  {{ Math.round((discoveryResult.statistics?.compressionRatio || 0) * 100) }}%
                </div>
                <div class="statistic-label">压缩率</div>
              </div>
              <div class="statistic-item">
                <div class="statistic-value statistic-value--orange">
                  {{ discoveryResult.statistics?.compressedActivities || 0 }}
                </div>
                <div class="statistic-label">压缩后节点</div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 子流程类型分布 -->
        <div class="type-distribution-section">
          <el-card class="chart-card">
            <template #header>
              <h4 class="chart-title">子流程类型分布</h4>
            </template>
            <div class="chart-container">
              <ClientOnly>
                <v-chart :option="typeDistributionOption" class="chart" />
                <template #fallback>
                  <div class="chart-loading">图表加载中...</div>
                </template>
              </ClientOnly>
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-icon class="empty-icon">
        <Search />
      </el-icon>
      <h3 class="empty-title">开始子流程发现</h3>
      <p class="empty-description">
        点击上方按钮开始分析您的流程数据中的子流程模式
      </p>
    </div>

    <!-- 配置对话框 -->
    <el-dialog
      v-model="showOptionsDialog"
      title="子流程发现配置"
      width="500px"
    >
      <el-form :model="discoveryOptions" label-width="120px">
        <el-form-item label="最小频率">
          <el-input-number
            v-model="discoveryOptions.minFrequency"
            :min="1"
            :max="100"
          />
        </el-form-item>
        <el-form-item label="最小长度">
          <el-input-number
            v-model="discoveryOptions.minLength"
            :min="2"
            :max="10"
          />
        </el-form-item>
        <el-form-item label="最大长度">
          <el-input-number
            v-model="discoveryOptions.maxLength"
            :min="3"
            :max="20"
          />
        </el-form-item>
        <el-form-item label="置信度阈值">
          <el-slider
            v-model="discoveryOptions.confidenceThreshold"
            :min="0"
            :max="1"
            :step="0.1"
            show-stops
          />
        </el-form-item>
        <el-form-item label="启用并行检测">
          <el-switch v-model="discoveryOptions.enableParallelDetection" />
        </el-form-item>
        <el-form-item label="启用循环检测">
          <el-switch v-model="discoveryOptions.enableLoopDetection" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showOptionsDialog = false">取消</el-button>
        <el-button type="primary" @click="applyOptions">应用</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  ArrowLeft, Search, Refresh, Setting, Plus, Minus, RefreshRight,
  ArrowDown, ArrowUp, ArrowRight, Share
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useApi } from '~/utils/api'
import * as go from 'gojs'


// 动态导入VChart以避免SSR问题
const VChart = defineAsyncComponent(() => import('vue-echarts'))

// 页面配置
definePageMeta({
  layout: 'default'
})

const route = useRoute()
const processId = parseInt(route.params.processId as string)

// 设置页面标题
useHead({
  title: '子流程发现 - ProMax'
})

// 状态
const isDiscovering = ref(false)
const discoveryResult = ref<{
  subprocesses: Array<{
    id: string
    name: string
    type: string
    frequency: number
    confidence: number
    activities: string[]
    avgDuration: number
    cases: string[]
  }>
  statistics: {
    totalSubprocesses: number
    avgSubprocessLength: number
    compressionRatio: number
    compressedActivities: number
  }
  hierarchicalDFG: {
    nodes: Array<{
      id: string
      label: string
      type: string
      frequency: number
    }>
    edges: Array<{
      source: string
      target: string
      frequency: number
    }>
  }
} | null>(null)
const hierarchicalDfgContainer = ref<HTMLElement>()
const showOptionsDialog = ref(false)
const expandedPatterns = ref(new Set<string>())
const currentTab = ref<'basic' | 'subprocess'>('subprocess')

// 发现选项
const discoveryOptions = ref({
  minFrequency: 2,
  minLength: 2,
  maxLength: 10,
  confidenceThreshold: 0.7,
  enableParallelDetection: true,
  enableLoopDetection: true,
  groupByDepartment: false,
  groupByResource: false,
})

let hierarchicalDiagram: go.Diagram | null = null

// 计算属性
const typeDistributionOption = computed(() => {
  if (!discoveryResult.value?.subprocesses) return {}

  const typeCount = discoveryResult.value.subprocesses.reduce((acc: Record<string, number>, pattern) => {
    acc[pattern.type] = (acc[pattern.type] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const data = Object.entries(typeCount).map(([type, count]) => ({
    name: getPatternTypeLabel(type),
    value: count
  }))

  return {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '子流程类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
})

// 方法
const startSubprocessDiscovery = async () => {
  isDiscovering.value = true
  try {
    const api = useApi()
    const result = await api.discoverSubprocess(processId, discoveryOptions.value)
    discoveryResult.value = result

    // 渲染层次化DFG
    await nextTick()
    setTimeout(() => {
      renderHierarchicalDFG()
    }, 100)

    ElMessage.success('子流程发现完成')
  } catch (error: unknown) {
    console.error('子流程发现失败:', error)
    const errorMessage = error instanceof Error ? error.message : '子流程发现失败'
    ElMessage.error(errorMessage)
  } finally {
    isDiscovering.value = false
  }
}

const refreshDiscovery = () => {
  startSubprocessDiscovery()
}

const applyOptions = () => {
  showOptionsDialog.value = false
  if (discoveryResult.value) {
    startSubprocessDiscovery()
  }
}

const togglePatternDetails = (patternId: string) => {
  if (expandedPatterns.value.has(patternId)) {
    expandedPatterns.value.delete(patternId)
  } else {
    expandedPatterns.value.add(patternId)
  }
}

const getPatternTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    sequential: '顺序',
    parallel: '并行',
    loop: '循环',
    choice: '选择'
  }
  return labels[type] || type
}

const getPatternTypeColor = (type: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const colors: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    sequential: 'primary',
    parallel: 'success',
    loop: 'warning',
    choice: 'info'
  }
  return colors[type] || 'primary'
}

const formatDuration = (milliseconds: number) => {
  if (!milliseconds) return '0ms'

  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`
  } else {
    return `${seconds}s`
  }
}

const renderHierarchicalDFG = () => {
  if (!hierarchicalDfgContainer.value || !discoveryResult.value?.hierarchicalDFG) {
    return
  }

  // 设置 GoJS 许可证密钥
  if (!go.Diagram.licenseKey) {
    go.Diagram.licenseKey = "2bf843e7b36758c511895a25406c7efb0bab2d67ce864df3595012a0ed587a04249fb87b50d7d8c986aa4df9182ec98ed8976121931c0338e737d48f45e0d5f1b63124e5061841dbf4052691c9fb38b1ff7971fbddbc68a2d2"
  }

  // 清理现有图表
  if (hierarchicalDiagram) {
    hierarchicalDiagram.div = null
  }

  // 创建新的GoJS图表
  hierarchicalDiagram = new go.Diagram(hierarchicalDfgContainer.value, {
    'undoManager.isEnabled': true,
    layout: new go.LayeredDigraphLayout({
      direction: 90,
      layerSpacing: 50,
      columnSpacing: 30,
      setsPortSpots: false
    }),
    'toolManager.mouseWheelBehavior': go.WheelMode.Zoom,
    allowZoom: true,
    allowHorizontalScroll: true,
    allowVerticalScroll: true,
    'animationManager.isEnabled': false,
    initialContentAlignment: go.Spot.Center,
    'grid.visible': true
  })

  // 禁用选择工具
  hierarchicalDiagram.isReadOnly = true

  // 定义子流程节点模板
  hierarchicalDiagram.nodeTemplateMap.add('subprocess',
    new go.Node('Auto')
      .add(
        new go.Shape('RoundedRectangle', {
          name: 'SHAPE',
          strokeWidth: 3,
          stroke: '#2196F3',
          fill: '#E3F2FD',
          minSize: new go.Size(120, 70)
        }),
        new go.Panel('Vertical', { margin: 10 })
          .add(
            // 子流程标识
            new go.TextBlock({
              font: 'bold 10px sans-serif',
              stroke: '#1976D2',
              text: '📦 子流程',
              margin: new go.Margin(0, 0, 2, 0),
              opacity: 0.8
            }),
            // 子流程名称
            new go.TextBlock({
              font: 'bold 14px sans-serif',
              stroke: '#1976D2',
              margin: new go.Margin(0, 0, 4, 0),
              wrap: go.Wrap.Fit,
              maxSize: new go.Size(100, NaN)
            })
              .bind('text', 'label'),
            // 频率信息
            new go.TextBlock({
              font: '10px sans-serif',
              stroke: '#666',
              opacity: 0.8
            })
              .bind('text', 'frequency', (freq) => `出现 ${freq} 次`)
          )
      )
  )

  // 定义普通活动节点模板
  hierarchicalDiagram.nodeTemplateMap.add('activity',
    new go.Node('Auto')
      .add(
        new go.Shape('RoundedRectangle', {
          name: 'SHAPE',
          strokeWidth: 2,
          stroke: '#4CAF50',
          fill: '#E8F5E8',
          minSize: new go.Size(90, 50)
        }),
        new go.Panel('Vertical', { margin: 8 })
          .add(
            // 活动标识
            new go.TextBlock({
              font: '9px sans-serif',
              stroke: '#2E7D32',
              text: '⚡ 活动',
              margin: new go.Margin(0, 0, 2, 0),
              opacity: 0.7
            }),
            // 活动名称
            new go.TextBlock({
              font: '12px sans-serif',
              stroke: '#2E7D32',
              wrap: go.Wrap.Fit,
              maxSize: new go.Size(80, NaN),
              margin: new go.Margin(0, 0, 2, 0)
            })
              .bind('text', 'label'),
            // 频率信息
            new go.TextBlock({
              font: '9px sans-serif',
              stroke: '#666',
              opacity: 0.7
            })
              .bind('text', 'frequency', (freq) => `${freq} 次`)
          )
      )
  )

  // 设置默认节点模板为活动类型
  const activityTemplate = hierarchicalDiagram.nodeTemplateMap.getValue('activity')
  if (activityTemplate) {
    hierarchicalDiagram.nodeTemplate = activityTemplate
  }

  // 定义连接线模板
  hierarchicalDiagram.linkTemplate = new go.Link({
    routing: go.Routing.AvoidsNodes,
    curve: go.Curve.Bezier,
    corner: 8,
    toShortLength: 4
  })
    .add(
      new go.Shape({
        strokeWidth: 2,
        stroke: '#666'
      })
        .bind('strokeWidth', 'frequency', (freq) => Math.max(1, Math.min(6, freq / 5))),
      new go.Shape({
        toArrow: 'Standard',
        strokeWidth: 0,
        fill: '#666',
        scale: 1.2
      }),
      // 添加频率标签
      new go.TextBlock({
        segmentIndex: 0,
        segmentFraction: 0.5,
        font: '9px sans-serif',
        stroke: '#666',
        background: 'white',
        margin: 2
      })
        .bind('text', 'frequency', (freq) => freq > 10 ? freq.toString() : '')
    )

  // 设置数据
  const nodes = discoveryResult.value.hierarchicalDFG.nodes || []
  const edges = discoveryResult.value.hierarchicalDFG.edges || []

  const nodeDataArray = nodes.map((node: {
    id: string
    label: string
    type: string
    frequency: number
  }) => ({
    key: node.id,
    label: node.label,
    category: node.type, // 使用category来指定节点模板
    frequency: node.frequency
  }))

  const linkDataArray = edges.map((edge: {
    source: string
    target: string
    frequency: number
  }) => ({
    from: edge.source,
    to: edge.target,
    frequency: edge.frequency
  }))

  hierarchicalDiagram.model = new go.GraphLinksModel(nodeDataArray, linkDataArray)

  console.log('GoJS模型已设置，节点数:', nodeDataArray.length, '边数:', linkDataArray.length)
  console.log('节点数据:', nodeDataArray)
  console.log('边数据:', linkDataArray)
}

const zoomIn = () => {
  if (hierarchicalDiagram) {
    hierarchicalDiagram.commandHandler.increaseZoom()
  }
}

const zoomOut = () => {
  if (hierarchicalDiagram) {
    hierarchicalDiagram.commandHandler.decreaseZoom()
  }
}

const resetZoom = () => {
  if (hierarchicalDiagram) {
    hierarchicalDiagram.scale = 1
    hierarchicalDiagram.position = new go.Point(0, 0)
  }
}

const highlightSubprocessInDiagram = (subprocessId: string) => {
  if (!hierarchicalDiagram) return

  // 清除之前的高亮
  hierarchicalDiagram.clearHighlighteds()

  // 查找并高亮子流程节点
  const node = hierarchicalDiagram.findNodeForKey(subprocessId)
  if (node) {
    // 高亮节点
    node.isHighlighted = true

    // 将视图中心移动到该节点
    hierarchicalDiagram.centerRect(node.actualBounds)

    // 添加临时动画效果
    const shape = node.findObject('SHAPE') as go.Shape
    if (shape) {
      const originalStroke = shape.stroke
      shape.stroke = '#FF5722'
      setTimeout(() => {
        if (shape) shape.stroke = originalStroke
      }, 2000)
    }

    ElMessage.success(`已定位到子流程: ${discoveryResult.value?.subprocesses.find(p => p.id === subprocessId)?.name}`)
  } else {
    ElMessage.warning('未找到对应的子流程节点')
  }
}

// 切换选项卡
const switchTab = (tab: 'basic' | 'subprocess') => {
  if (tab === 'basic') {
    navigateTo(`/analysis/${processId}/discover`)
  } else {
    currentTab.value = tab
  }
}

// 生命周期
onMounted(() => {
  // 可以在这里加载缓存的结果
})

onUnmounted(() => {
  if (hierarchicalDiagram) {
    hierarchicalDiagram.div = null
  }
})
</script>

<style scoped lang="scss">
.subprocess-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @media (min-width: 768px) {
    padding: 2rem 2rem;
  }
}

// 页面标题
.page-header {
  margin: 0 auto 2rem auto;
  animation: fadeInDown 0.6s ease-out;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;

    @media (max-width: 1024px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
  }

  .header-left {
    flex: 1;
  }

  .header-title {
    display: flex;
    align-items: flex-start;
    gap: 1rem;

    .back-button {
      color: #64748b;
      font-size: 1.2rem;
      margin-top: 0.25rem;

      &:hover {
        background: rgba(59, 130, 246, 0.1);
        color: #3b82f6;
      }

      :global(.dark) & {
        color: #94a3b8;

        &:hover {
          background: rgba(96, 165, 250, 0.1);
          color: #60a5fa;
        }
      }
    }

    .title-section {
      flex: 1;

      .title {
        font-size: 2.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0 0 0.5rem 0;
        letter-spacing: -0.025em;

        @media (max-width: 768px) {
          font-size: 2rem;
        }

        :global(.dark) & {
          background: linear-gradient(135deg, #60a5fa, #a78bfa);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .description {
        color: #64748b;
        font-size: 1.125rem;
        font-weight: 500;
        margin: 0;
        opacity: 0.8;

        :global(.dark) & {
          color: #94a3b8;
        }
      }
    }
  }

  .header-tabs {
    flex-shrink: 0;

    @media (max-width: 1024px) {
      width: 100%;
    }

    .tabs-container {
      display: flex;
      gap: 0.5rem;
      background: rgba(255, 255, 255, 0.95);
      padding: 0.5rem;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);

      :global(.dark) & {
        background: rgba(30, 41, 59, 0.95);
        border: 1px solid rgba(71, 85, 105, 0.3);
      }

      @media (max-width: 1024px) {
        justify-content: center;
      }

      .tab-button {
        border-radius: 8px;
        font-weight: 600;
        padding: 8px 16px;
        transition: all 0.2s ease;
        border: none;
        font-size: 0.875rem;

        &.el-button--primary {
          background: linear-gradient(135deg, #3b82f6, #8b5cf6);
          color: white;
          box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
          }

          :global(.dark) & {
            background: linear-gradient(135deg, #60a5fa, #a78bfa);
          }
        }

        &.el-button--default {
          background: transparent;
          color: #6b7280;

          &:hover {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
          }

          :global(.dark) & {
            color: #9ca3af;

            &:hover {
              background: rgba(96, 165, 250, 0.1);
              color: #60a5fa;
            }
          }
        }
      }
    }
  }
}



// 控制面板
.control-panel {
  margin: 0 auto 1.5rem auto;
  animation: slideInLeft 0.5s ease-out 0.2s both;

  :deep(.el-card) {
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    :global(.dark) & {
      background: rgba(30, 41, 59, 0.95);
      border: 1px solid rgba(71, 85, 105, 0.3);
    }
  }

  .control-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;

    @media (min-width: 640px) {
      flex-direction: row;
      align-items: center;
    }

    .control-actions {
      display: flex;
      align-items: center;
      gap: 1rem;

      :deep(.el-button) {
        border-radius: 12px;
        font-weight: 600;
        padding: 10px 20px;
        transition: all 0.2s ease;

        &.el-button--primary {
          background: linear-gradient(135deg, #3b82f6, #8b5cf6);
          border: none;
          box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
          }

          :global(.dark) & {
            background: linear-gradient(135deg, #60a5fa, #a78bfa);
          }
        }

        &.el-button--warning {
          background: linear-gradient(135deg, #f59e0b, #f97316);
          border: none;
          box-shadow: 0 4px 14px 0 rgba(245, 158, 11, 0.3);

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px 0 rgba(245, 158, 11, 0.4);
          }
        }

        &:not(.el-button--primary):not(.el-button--warning) {
          border: 2px solid #e5e7eb;
          color: #6b7280;

          &:hover {
            border-color: #3b82f6;
            color: #3b82f6;
            transform: translateY(-1px);
          }

          :global(.dark) & {
            border-color: #4b5563;
            color: #9ca3af;

            &:hover {
              border-color: #60a5fa;
              color: #60a5fa;
            }
          }
        }
      }
    }

    .result-info {
      display: flex;
      align-items: center;
      gap: 1rem;

      .discovery-stats {
        display: flex;
        gap: 0.5rem;
      }
    }
  }
}

// 加载状态
.analyzing-state {
  text-align: center;
  padding: 4rem 1rem;
  margin: 0 auto;

  .analyzing-text {
    color: #64748b;
    font-size: 1.125rem;
    font-weight: 500;
    margin: 0;

    :global(.dark) & {
      color: #94a3b8;
    }
  }
}

// 加载动画
.loading-spinner {
  width: 2.5rem;
  height: 2.5rem;
  border: 3px solid rgba(59, 130, 246, 0.2);
  border-top-color: #3b82f6;
  border-radius: 50%;
  margin: 0 auto 1.5rem;
  animation: spin 1s linear infinite;

  :global(.dark) & {
    border-color: rgba(96, 165, 250, 0.2);
    border-top-color: #60a5fa;
  }
}

// 发现结果 - Grid全屏布局
.discovery-results {
  height: calc(120vh);
  overflow: hidden;
  margin: 0 auto;
  animation: slideInUp 0.5s ease-out 0.4s both;
}

// Grid布局容器
.grid-layout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
  gap: 1rem;
  height: 100%;
  grid-template-areas:
    "dfg patterns"
    "dfg statistics"
    "chart chart";

  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
    grid-template-rows: 300px 250px 140px 200px;
    grid-template-areas:
      "dfg"
      "patterns"
      "statistics"
      "chart";
    height: auto;
    gap: 1.5rem;
  }

  // 统一卡片样式
  :deep(.el-card) {
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    :global(.dark) & {
      background: rgba(30, 41, 59, 0.95);
      border: 1px solid rgba(71, 85, 105, 0.3);
    }

    .el-card__header {
      border-bottom: 1px solid rgba(226, 232, 240, 0.5);

      :global(.dark) & {
        border-bottom-color: rgba(75, 85, 99, 0.5);
      }
    }
  }

  .hierarchical-dfg-section {
    grid-area: dfg;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  .subprocess-patterns-section {
    grid-area: patterns;
    position: relative;
    overflow: hidden;
  }

  .statistics-section {
    grid-area: statistics;
    position: relative;
    overflow: hidden;
  }

  .type-distribution-section {
    grid-area: chart;
    position: relative;
    overflow: hidden;
  }
}

// DFG区域
.dfg-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;

  :deep(.el-card__header) {
    flex-shrink: 0;
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
  }

  :deep(.el-card__body) {
    flex: 1;
    padding: 0;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  .dfg-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .dfg-title {
      margin: 0;
      color: #333;
      font-size: 1.125rem;
      font-weight: 600;

      :global(.dark) & {
        color: #ffffff;
      }
    }

    .dfg-controls {
      display: flex;
      gap: 0.5rem;

      :deep(.el-button) {
        border-radius: 8px;
        font-size: 0.875rem;
        padding: 6px 12px;
      }
    }
  }

  .dfg-container {
    flex: 1;
    min-height: 0;
    padding: 1rem;

    .dfg-canvas {
      width: 100%;
      height: 100%;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      background: #fafafa;

      :global(.dark) & {
        border-color: #4b5563;
        background: #1f2937;
      }
    }
  }
}

// 子流程模式列表
.patterns-card {
  height: 100%;

  :deep(.el-card__body) {
    height: calc(100% - 60px);
    overflow: hidden;
  }

  .patterns-title {
    margin: 0;
    color: #333;
    font-size: 1.125rem;
    font-weight: 600;

    :global(.dark) & {
      color: #ffffff;
    }
  }

  .patterns-container {
    height: 100%;
    overflow-y: auto;
    padding-right: 0.5rem;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 3px;

      &:hover {
        background: #94a3b8;
      }
    }

    .pattern-item {
      border: 1px solid rgba(226, 232, 240, 0.5);
      border-radius: 12px;
      margin-bottom: 1rem;
      padding: 1rem;
      background: rgba(255, 255, 255, 0.8);
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      :global(.dark) & {
        background: rgba(30, 41, 59, 0.8);
        border-color: rgba(75, 85, 99, 0.5);
      }

      .pattern-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .pattern-info {
          flex: 1;

          .pattern-name {
            margin: 0 0 0.5rem 0;
            color: #1f2937;
            font-size: 1rem;
            font-weight: 600;

            :global(.dark) & {
              color: #ffffff;
            }
          }

          .pattern-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: #6b7280;

            :global(.dark) & {
              color: #9ca3af;
            }
          }
        }

        .pattern-actions {
          display: flex;
          gap: 0.5rem;
          flex-shrink: 0;

          :deep(.el-button) {
            border-radius: 8px;
            font-size: 0.75rem;
            padding: 4px 8px;
            transition: all 0.2s ease;

            &.el-button--primary {
              background: linear-gradient(135deg, #3b82f6, #1d4ed8);
              border: none;
              box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
              }

              :global(.dark) & {
                background: linear-gradient(135deg, #60a5fa, #3b82f6);
              }
            }

            &:not(.el-button--primary) {
              border: 1px solid #e5e7eb;
              color: #6b7280;

              &:hover {
                border-color: #3b82f6;
                color: #3b82f6;
                transform: translateY(-1px);
              }

              :global(.dark) & {
                border-color: #4b5563;
                color: #9ca3af;

                &:hover {
                  border-color: #60a5fa;
                  color: #60a5fa;
                }
              }
            }
          }
        }
      }

      .pattern-details {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid rgba(226, 232, 240, 0.5);

        :global(.dark) & {
          border-top-color: rgba(75, 85, 99, 0.5);
        }

        .pattern-activities {
          margin-bottom: 1rem;

          h5 {
            margin: 0 0 0.5rem 0;
            color: #374151;
            font-size: 0.875rem;
            font-weight: 600;

            :global(.dark) & {
              color: #d1d5db;
            }
          }

          .activity-flow {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 0.5rem;

            .activity-node {
              display: flex;
              align-items: center;
              gap: 0.25rem;
              padding: 0.25rem 0.5rem;
              background: rgba(59, 130, 246, 0.1);
              border-radius: 6px;
              font-size: 0.875rem;
              color: #3b82f6;
              font-weight: 500;

              :global(.dark) & {
                background: rgba(96, 165, 250, 0.2);
                color: #60a5fa;
              }

              .arrow-icon {
                color: #6b7280;

                :global(.dark) & {
                  color: #9ca3af;
                }
              }
            }
          }
        }

        .pattern-stats {
          display: flex;
          gap: 1rem;

          .stat-item {
            display: flex;
            gap: 0.5rem;
            font-size: 0.875rem;

            .stat-label {
              color: #6b7280;

              :global(.dark) & {
                color: #9ca3af;
              }
            }

            .stat-value {
              color: #1f2937;
              font-weight: 600;

              :global(.dark) & {
                color: #ffffff;
              }
            }
          }
        }
      }
    }
  }
}

// 统计信息区域
.statistics-card {
  height: 100%;

  :deep(.el-card__body) {
    height: calc(100% - 60px);
    overflow: hidden;
  }

  .statistics-title {
    margin: 0;
    color: #333;
    font-size: 1.125rem;
    font-weight: 600;

    :global(.dark) & {
      color: #ffffff;
    }
  }

  .statistics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    height: 100%;
    align-content: center;

    .statistic-item {
      text-align: center;
      padding: 1rem;
      background: rgba(248, 250, 252, 0.8);
      border-radius: 12px;
      border: 1px solid rgba(226, 232, 240, 0.5);
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      :global(.dark) & {
        background: rgba(15, 23, 42, 0.8);
        border-color: rgba(75, 85, 99, 0.5);
      }

      .statistic-value {
        font-size: 1.75rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        line-height: 1;

        &--blue {
          color: #3b82f6;

          :global(.dark) & {
            color: #60a5fa;
          }
        }
        &--green {
          color: #10b981;

          :global(.dark) & {
            color: #34d399;
          }
        }
        &--purple {
          color: #8b5cf6;

          :global(.dark) & {
            color: #a78bfa;
          }
        }
        &--orange {
          color: #f59e0b;

          :global(.dark) & {
            color: #fbbf24;
          }
        }
      }

      .statistic-label {
        color: #6b7280;
        font-size: 0.875rem;
        font-weight: 500;

        :global(.dark) & {
          color: #9ca3af;
        }
      }
    }
  }
}

// 图表区域
.chart-card {
  height: 100%;

  :deep(.el-card__body) {
    height: calc(100% - 60px);
    overflow: hidden;
  }

  .chart-title {
    margin: 0;
    color: #333;
    font-size: 1.125rem;
    font-weight: 600;

    :global(.dark) & {
      color: #ffffff;
    }
  }

  .chart-container {
    height: 100%;
    padding: 1rem 0;

    .chart {
      width: 100%;
      height: 100%;
    }

    .chart-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #6b7280;
      font-size: 0.875rem;

      :global(.dark) & {
        color: #9ca3af;
      }
    }
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 3rem 0;

  .empty-icon {
    width: 4rem;
    height: 4rem;
    color: #9ca3af;
    margin: 0 auto 1rem;

    :global(.dark) & {
      color: #6b7280;
    }
  }

  .empty-title {
    font-size: 1.125rem;
    font-weight: 500;
    color: #111827;
    margin: 0 0 0.5rem 0;

    :global(.dark) & {
      color: #ffffff;
    }
  }

  .empty-description {
    color: #6b7280;
    margin: 0 0 1.5rem 0;

    :global(.dark) & {
      color: #9ca3af;
    }
  }
}

// GoJS 图表容器样式
:deep(.go-diagram) {
  width: 100%;
  height: 100%;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;

  :global(.dark) & {
    background: #1f2937;
    border-color: #4b5563;
  }
}

// 动画定义
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
