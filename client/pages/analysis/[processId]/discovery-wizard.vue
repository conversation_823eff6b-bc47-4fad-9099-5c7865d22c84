<template>
  <div class="discovery-wizard">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button
            @click="navigateTo(`/processes/${processId}`)"
            :icon="ArrowLeft"
            text
            class="back-button"
          />
          <div class="title-section">
            <h1 class="title">流程发现向导</h1>
            <p class="description">通过简单步骤完成数据上传和流程发现</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 步骤指示器 -->
    <div class="steps-container">
      <el-steps :active="currentStepIndex" align-center>
        <el-step
          v-for="(step, index) in steps"
          :key="step.key"
          :title="step.title"
          :description="step.description"
          :status="getStepStatus(index)"
          :icon="step.icon"
        />
      </el-steps>
    </div>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 文件上传步骤 -->
      <div v-if="wizardStore.step === ProcessDiscoveryStep.UPLOAD && dataSource !== 'database'" class="step-panel">
        <UploadStep
          @time-field-overrides-changed="handleTimeFieldOverridesChanged"
          @next="goToNextStep"
        />
      </div>

      <!-- SQL查询步骤 -->
      <div v-if="wizardStore.step === ProcessDiscoveryStep.UPLOAD && dataSource === 'database'" class="step-panel">
        <SqlEditor
          :process-id="processId"
          @query-result="handleQueryResult"
        />
      </div>

      <!-- 字段映射步骤 -->
      <div v-if="wizardStore.step === ProcessDiscoveryStep.MAP_FIELDS" class="step-panel">
        <FieldMappingStep
          :file="wizardStore.file || undefined"
          :preview-data="wizardStore.preview?.data || []"
          :available-columns="wizardStore.preview?.columns || []"
          :total-rows="wizardStore.preview?.totalRows || 0"
          :saved-mapping="wizardStore.mapping"
          :saved-clear-data="wizardStore.clearData"
          :time-columns="wizardStore.timeColumns"
          @prev="goToPreviousStep"
          @next="handleMappingNext"
          @mapping-change="handleMappingChange"
          @clear-data-changed="handleClearDataChanged"
        />
      </div>

      <!-- 处理进度步骤 -->
      <div v-if="wizardStore.step === ProcessDiscoveryStep.PROCESSING" class="step-panel">
        <ProcessingStep
          :process-id="processId"
          :processing-status="wizardStore.status"
          :total-rows="wizardStore.preview?.totalRows || 0"
          @completed="handleProcessingCompleted"
          @failed="handleProcessingFailed"
          @cancelled="handleProcessingCancelled"
          @retry="handleProcessingRetry"
        />
      </div>

      <!-- 成功结果步骤 -->
      <div v-if="wizardStore.step === ProcessDiscoveryStep.SUCCESS" class="step-panel">
        <SuccessStep
          :process-id="processId"
          :discovery-result="wizardStore.result"
          @view-details="goToDiscoveryPage"
          @restart="restartWizard"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Upload, Setting, Loading, CircleCheck } from '@element-plus/icons-vue'
import { ProcessDiscoveryStep } from '~/types'
import type {
  DFGResult,
  UploadDataConfig,
  QueryResult
} from '~/types'
import UploadStep from '~/components/discovery/UploadStep.vue'
import FieldMappingStep from '~/components/discovery/FieldMappingStep.vue'
import ProcessingStep from '~/components/discovery/ProcessingStep.vue'
import SuccessStep from '~/components/discovery/SuccessStep.vue'
import SqlEditor from '~/components/database/SqlEditor.vue'
import { useApi } from '~/utils/api'
import { useDiscoveryWizardStore } from '~/stores/discoveryWizard'

// 页面配置
definePageMeta({
  layout: 'default',
  middleware: 'auth'
})

// 路由参数
const route = useRoute()
const processId = parseInt(route.params.processId as string)
const dataSource = route.query.source as string || 'upload'

// 设置页面标题
useHead({
  title: '流程发现向导 - ProMined'
})

const wizardStore = useDiscoveryWizardStore()

wizardStore.init(processId, dataSource)

// 步骤定义
const steps = computed(() => {
  if (dataSource === 'database') {
    return [
      {
        key: ProcessDiscoveryStep.UPLOAD,
        title: 'SQL查询',
        description: '编写SQL查询语句',
        icon: Upload
      },
      {
        key: ProcessDiscoveryStep.MAP_FIELDS,
        title: '字段映射',
        description: '配置数据字段',
        icon: Setting
      },
      {
        key: ProcessDiscoveryStep.PROCESSING,
        title: '处理数据',
        description: '分析流程数据',
        icon: Loading
      },
      {
        key: ProcessDiscoveryStep.SUCCESS,
        title: '完成',
        description: '查看结果',
        icon: CircleCheck
      }
    ]
  } else {
    return [
      {
        key: ProcessDiscoveryStep.UPLOAD,
        title: '上传文件',
        description: '选择事件日志文件',
        icon: Upload
      },
      {
        key: ProcessDiscoveryStep.MAP_FIELDS,
        title: '字段映射',
        description: '配置数据字段',
        icon: Setting
      },
      {
        key: ProcessDiscoveryStep.PROCESSING,
        title: '处理数据',
        description: '分析流程数据',
        icon: Loading
      },
      {
        key: ProcessDiscoveryStep.SUCCESS,
        title: '完成',
        description: '查看结果',
        icon: CircleCheck
      }
    ]
  }
})

// 计算属性
const currentStepIndex = computed(() => {
  return steps.value.findIndex(step => step.key === wizardStore.step) + 1
})

// 获取步骤状态
const getStepStatus = (index: number) => {
  const current = currentStepIndex.value
  if (index < current) return 'finish'
  if (index === current) return 'process'
  return 'wait'
}

// 事件处理
const handleClearDataChanged = (clearData: boolean) => {
  wizardStore.setClearData(clearData)
  console.log('清空数据选项变化:', clearData)
}

const handleTimeFieldOverridesChanged = (overrides: Record<string, boolean>) => {
  wizardStore.updateTimeOverrides(overrides)
  console.log('时间字段配置变化:', overrides)
}

const handleQueryResult = (result: QueryResult, query: string) => {
  console.log('SQL查询结果:', result, query)
  wizardStore.setQuery(result, query)
  // 自动进入下一步
  goToNextStep()
}

const handleMappingChange = (mapping: Record<string, string | boolean>) => {
  const { clearExistingData, ...fieldMappings } = mapping

  // 更新字段映射
  wizardStore.updateMapping(fieldMappings as Record<string, string>)

  // 更新清空数据选项
  if (typeof clearExistingData === 'boolean') {
    wizardStore.setClearData(clearExistingData)
  }

  console.log('字段映射变化:', mapping)
}

const handleMappingNext = (mapping: Record<string, string | boolean>) => {
  // 从映射结果中获取clearExistingData值
  wizardStore.setClearData(Boolean(mapping.clearExistingData) || false)

  // 将映射结果转换为UploadDataConfig格式
  const config = {
    processId: processId,
    ...mapping
  } as UploadDataConfig

  wizardStore.setConfig(config)

  console.log('上传配置:', config)
  console.log('清空数据选项:', wizardStore.clearData)

  // 进入下一步
  startProcessing()
}

const goToNextStep = () => {
  wizardStore.nextStep(steps.value)
}

const goToPreviousStep = () => {
  wizardStore.prevStep(steps.value)
}

const startProcessing = async () => {
  // 验证必要的配置信息
  if (!wizardStore.config) {
    ElMessage.error('缺少必要的配置信息')
    return
  }

  // 对于文件上传模式，需要验证文件是否存在
  if (dataSource !== 'database' && !wizardStore.file) {
    ElMessage.error('缺少必要的文件')
    return
  }

  // 对于数据库模式，需要验证数据预览是否存在
  if (dataSource === 'database' && (!wizardStore.preview || !wizardStore.preview.data || wizardStore.preview.data.length === 0)) {
    ElMessage.error('缺少查询数据，请先执行SQL查询')
    return
  }

  wizardStore.setStep(ProcessDiscoveryStep.PROCESSING)

  try {
    // 初始化处理状态
    const initialStatus = {
      status: 'processing' as const,
      progress: 0,
      currentTask: '开始处理数据',
      phase: 'parse' as const,
    }
    wizardStore.setStatus(initialStatus)

    const api = useApi()

    // 根据数据源类型调用不同的API
    let result
    if (dataSource === 'database') {
      // 对于数据库模式，传递查询数据而不是文件
      result = await api.startDiscoveryWizardWithData(processId, wizardStore.preview!, wizardStore.config)
    } else {
      // 对于文件上传模式，传递文件
      result = await api.startDiscoveryWizard(processId, wizardStore.file!, wizardStore.config)
    }

    // 开始轮询处理状态
    const discoveryId = (result as { discoveryId?: string }).discoveryId
    if (discoveryId) {
      startStatusPolling(discoveryId)
    } else {
      throw new Error('未获取到有效的发现任务ID')
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    ElMessage.error(`启动流程发现失败: ${errorMessage}`)
    const failedStatus = {
      status: 'failed' as const,
      progress: 0,
      currentTask: '处理失败',
      phase: 'parse' as const
    }
    wizardStore.setStatus(failedStatus)
  }
}

// 轮询控制
let currentPollingController: { stop: () => void } | null = null

const startStatusPolling = (discoveryId: string) => {
  // 停止之前的轮询
  if (currentPollingController) {
    currentPollingController.stop()
  }

  const api = useApi()
  let pollCount = 0
  const maxPollCount = 1800 // 最大轮询次数 (1小时，每2秒一次)
  let isPolling = true
  let pollTimeoutId: NodeJS.Timeout | null = null

  // 立即更新状态包含discoveryId
  const currentStatus = wizardStore.status
  if (currentStatus) {
    currentStatus.discoveryId = discoveryId
    wizardStore.setStatus(currentStatus)
  }

  const stopPolling = () => {
    if (!isPolling) {
      console.log('Polling already stopped for discoveryId:', discoveryId)
      return
    }

    isPolling = false
    if (pollTimeoutId) {
      clearTimeout(pollTimeoutId)
      pollTimeoutId = null
    }
  }

  currentPollingController = { stop: stopPolling }

  const pollStatus = async () => {
    if (!isPolling) {
      console.log('Polling stopped, exiting pollStatus')
      return
    }

    pollCount++
    if (pollCount > maxPollCount) {
      handleProcessingFailed('任务超时，请刷新页面查看结果')
      return
    }

    try {
      const status = await api.getDiscoveryStatus(discoveryId)

      // 确保状态中包含discoveryId
      if (!status.discoveryId) {
        status.discoveryId = discoveryId
      }
      wizardStore.setStatus(status)

      if (status.status === 'completed') {
        // 处理完成，停止轮询并获取结果
        stopPolling()
        console.log('Processing completed, fetching discovery result...')
        const result = await api.discoverProcess(processId)
        console.log('API returned result:', result)
        handleProcessingCompleted(result)
      } else if (status.status === 'failed') {
        stopPolling()
        if (status.validationErrors && status.validationErrors.length > 0) {
          const errorCount = status.validationErrors.length
          const errorMessage = `数据验证失败，发现 ${errorCount} 个错误。请查看详细信息。`
          handleProcessingFailed(errorMessage)
        } else {
          handleProcessingFailed(status.currentTask || status.error || '处理失败')
        }
      } else if (status.status === 'cancelled') {
        stopPolling()
        handleProcessingCancelled('任务已被取消')
      } else {
        // 继续轮询
        if (isPolling) {
          pollTimeoutId = setTimeout(pollStatus, 2000)
        }
      }
    } catch (error: unknown) {
      console.error('获取处理状态失败:', error)

      // 检查是否为任务不存在错误
      const errorMessage = String(error)
      const responseStatus = (error as { response?: { status?: number } })?.response?.status
      const isTaskNotFound = errorMessage.includes('发现任务不存在') ||
                            errorMessage.includes('Discovery not found') ||
                            responseStatus === 400

      stopPolling()

      if (isTaskNotFound) {
        // 任务可能已完成但状态过期，尝试获取结果
        try {
          const result = await api.discoverProcess(processId)
          handleProcessingCompleted(result)
          return
        } catch {
          // 没有找到结果，提示重新开始
          handleProcessingFailed('任务状态已过期，请重新开始流程发现')
          return
        }
      }

      // 其他错误
      handleProcessingFailed('获取任务状态失败，请刷新页面重试')
    }
  }

  pollStatus()

  return stopPolling
}

const handleProcessingCompleted = (result: DFGResult) => {
  stopAllPolling()
  wizardStore.setStep(ProcessDiscoveryStep.SUCCESS)
  wizardStore.setResult(result)
}

const handleProcessingFailed = (error: string) => {
  stopAllPolling()
  ElMessage.error(`处理失败: ${error}`)
  // 可以选择回到上一步或显示错误状态
}

const handleProcessingCancelled = (reason: string) => {
  console.log('Processing cancelled:', reason)
  stopAllPolling()
  ElMessage.warning('处理已取消')
  // 回到字段映射步骤，允许用户重新开始
  wizardStore.setStep(ProcessDiscoveryStep.MAP_FIELDS)
}

const handleProcessingRetry = () => {
  console.log('Processing retry requested')
  stopAllPolling()
  ElMessage.info('正在重新配置...')
  // 回到字段映射步骤，允许用户重新配置
  wizardStore.setStep(ProcessDiscoveryStep.MAP_FIELDS)
}

const goToDiscoveryPage = () => {
  navigateTo(`/analysis/${processId}/discover`)
}

const restartWizard = () => {
  wizardStore.reset()
  wizardStore.init(processId, dataSource)
}

// 停止所有轮询
const stopAllPolling = () => {
  if (currentPollingController) {
    currentPollingController.stop()
    currentPollingController = null
  }
}

// 页面离开时清理数据
const cleanupWizardData = () => {
  console.log('清理流程发现向导数据')
  stopAllPolling() // 停止轮询
  wizardStore.reset()
}

// 监听页面卸载事件
onUnmounted(() => {
  cleanupWizardData()
})

// 监听路由离开
onBeforeRouteLeave(() => {
  console.log('Route leaving, stopping polling')
  stopAllPolling()
  return true
})

// 监听浏览器关闭/刷新事件
onMounted(() => {
  const handleBeforeUnload = (event: Event) => {
    // 停止轮询
    stopAllPolling()

    // 如果向导正在进行中，提示用户
    if (wizardStore.isFileUploaded || wizardStore.hasMapping || wizardStore.isProcessing) {
      const message = '您的流程发现向导数据将会丢失，确定要离开吗？'
      event.preventDefault()
      return message
    }
  }

  const handleUnload = () => {
    cleanupWizardData()
  }

  window.addEventListener('beforeunload', handleBeforeUnload)
  window.addEventListener('unload', handleUnload)

  // 清理事件监听器
  onUnmounted(() => {
    window.removeEventListener('beforeunload', handleBeforeUnload)
    window.removeEventListener('unload', handleUnload)
  })
})
</script>

<style lang="scss" scoped>
// 页面容器 - 与discover页面保持一致
.discovery-wizard {
  min-height: 100vh;
  background: #f8fafc;
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @media (min-width: 768px) {
    padding: 2rem 2rem;
  }

  // 页面标题 - 与discover页面保持一致
  .page-header {
    margin: 0 auto 2rem auto;
    animation: fadeInDown 0.6s ease-out;

    .header-content {
      .header-left {
        display: flex;
        align-items: flex-start;
        gap: 1rem;

        .back-button {
          color: #64748b;
          font-size: 1.2rem;
          margin-top: 0.25rem;

          &:hover {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
          }

          :global(.dark) & {
            color: #94a3b8;

            &:hover {
              background: rgba(96, 165, 250, 0.1);
              color: #60a5fa;
            }
          }
        }

        .title-section {
          flex: 1;

          .title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin: 0 0 0.5rem 0;
            letter-spacing: -0.025em;

            @media (max-width: 768px) {
              font-size: 2rem;
            }

            :global(.dark) & {
              background: linear-gradient(135deg, #60a5fa, #a78bfa);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }

          .description {
            color: #64748b;
            font-size: 1.125rem;
            font-weight: 500;
            margin: 0;
            opacity: 0.8;

            :global(.dark) & {
              color: #94a3b8;
            }
          }
        }
      }
    }
  }

  // 步骤容器 - 与discover页面卡片风格保持一致
  .steps-container {
    margin: 0 auto 2rem auto;
    animation: slideInLeft 0.5s ease-out 0.2s both;

    :deep(.el-steps) {
      padding: 1.5rem 0;
      .el-step__icon {
        background: #f8fafc;
      }
    }
  }

  .step-content {
    animation: slideInUp 0.5s ease-out 0.4s both;

    .step-panel {
      min-height: 500px;
    }
  }
}

// 动画定义 - 与discover页面保持一致
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
