<template>
  <div class="data-source-page">
    <div class="data-source-container">
      <div class="page-header">
        <div class="header-content">
          <div class="header-title">
            <el-button
              class="back-button"
              @click="goBack"
              :icon="ArrowLeft"
              circle
              size="large"
            />
            <div class="title-section">
              <h1 class="title">选择数据来源</h1>
              <p class="description">请选择您的数据来源，我们将引导您完成流程发现</p>
            </div>
          </div>
        </div>
      </div>

      <div class="source-options">
        <div class="source-card" @click="selectFeishuTable">
          <div class="source-icon">
            <img src="/images/feishu_table.png" alt="飞书多维表格">
          </div>
          <h3>飞书多维表格</h3>
          <p>从飞书多维表格导入流程数据</p>
          <div class="source-features">
            <span class="feature-tag">实时同步</span>
            <span class="feature-tag">协作便捷</span>
          </div>
        </div>

        <div class="source-card" @click="selectFeishuApproval">
          <div class="source-icon">
            <img src="/images/feishu_approval.png" alt="飞书审批">
          </div>
          <h3>飞书审批</h3>
          <p>从飞书审批流程导入审批数据</p>
          <div class="source-features">
            <span class="feature-tag">流程完整</span>
            <span class="feature-tag">状态追踪</span>
          </div>
        </div>

        <div class="source-card" @click="selectSAP">
          <div class="source-icon">
            <img src="/images/sap.svg" alt="SAP">
          </div>
          <h3>SAP</h3>
          <p>从SAP系统导入业务流程数据</p>
          <div class="source-features">
            <span class="feature-tag">企业级</span>
            <span class="feature-tag">标准化</span>
          </div>
        </div>

        <div class="source-card" @click="selectBPMAX">
          <div class="source-icon">
            <img src="/images/bpmax.png" alt="BPMAX">
          </div>
          <h3>BPMAX</h3>
          <p>从BPMAX平台导入流程优化数据</p>
          <div class="source-features">
            <span class="feature-tag">智能分析</span>
            <span class="feature-tag">优化建议</span>
          </div>
        </div>

        <div class="source-card" @click="selectSalesforce">
          <div class="source-icon">
            <img src="/images/salesforce.svg" alt="Salesforce">
          </div>
          <h3>Salesforce</h3>
          <p>从Salesforce CRM导入销售流程数据</p>
          <div class="source-features">
            <span class="feature-tag">CRM集成</span>
            <span class="feature-tag">销售分析</span>
          </div>
        </div>

        <div class="source-card" @click="selectJira">
          <div class="source-icon">
            <img src="/images/jira.svg" alt="Jira">
          </div>
          <h3>Jira</h3>
          <p>从Jira导入项目管理流程数据</p>
          <div class="source-features">
            <span class="feature-tag">敏捷开发</span>
            <span class="feature-tag">任务追踪</span>
          </div>
        </div>

        <div class="source-card" @click="selectFanwei">
          <div class="source-icon">
            <img src="/images/weaver.png" alt="泛微">
          </div>
          <h3>泛微</h3>
          <p>从泛微OA系统导入办公流程数据</p>
          <div class="source-features">
            <span class="feature-tag">OA集成</span>
            <span class="feature-tag">办公自动化</span>
          </div>
        </div>

        <div class="source-card" @click="selectLanling">
          <div class="source-icon">
            <img src="/images/landray.png" alt="蓝凌">
          </div>
          <h3>蓝凌</h3>
          <p>从蓝凌知识管理系统导入流程数据</p>
          <div class="source-features">
            <span class="feature-tag">知识管理</span>
            <span class="feature-tag">协同办公</span>
          </div>
        </div>
      </div>

      <!-- 本地数据源分区 -->
      <div class="local-source-section">
        <div class="section-header">
          <div class="section-title">
            <el-icon :size="24" class="section-icon">
              <FolderOpened />
            </el-icon>
            <h2>本地数据源</h2>
          </div>
          <div class="section-description">
            <p>从本地文件导入数据，支持多种格式的数据文件</p>
          </div>
        </div>

        <div class="local-source-options source-options">
          <div class="source-card local-card" @click="selectFileUpload">
            <div class="source-icon">
              <el-icon :size="48">
                <Upload />
              </el-icon>
            </div>
            <h3>上传文件</h3>
            <p>上传Excel、CSV等格式的数据文件进行流程挖掘分析</p>
          </div>

          <div class="source-card local-card" @click="selectDatabase">
            <div class="source-icon">
              <el-icon :size="48">
                <Connection />
              </el-icon>
            </div>
            <h3>数据库连接</h3>
            <p>直接连接MySQL、PostgreSQL等数据库导入数据</p>
          </div>

          <div class="source-card local-card coming-soon" @click="selectFileUpload">
            <div class="source-icon">
              <el-icon :size="48">
                <Link />
              </el-icon>
            </div>
            <h3>API接口</h3>
            <p>通过REST API接口导入第三方系统数据</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Upload,
  Connection,
  FolderOpened,
  Link,
  ArrowLeft
} from '@element-plus/icons-vue'

// 页面配置
definePageMeta({
  layout: 'default'
})

// 路由参数
const route = useRoute()
const processId = parseInt(route.params.processId as string)

// 设置页面标题
useHead({
  title: '选择数据来源 - ProMined'
})

// 选择飞书多维表格
const selectFeishuTable = () => {
  navigateTo(`/analysis/${processId}/discovery-wizard?source=feishu`)
}

// 选择飞书审批
const selectFeishuApproval = () => {
  navigateTo(`/analysis/${processId}/discovery-wizard?source=feishu-approval`)
}

// 选择SAP
const selectSAP = () => {
  navigateTo(`/analysis/${processId}/discovery-wizard?source=sap`)
}

// 选择BPMAX
const selectBPMAX = () => {
  navigateTo(`/analysis/${processId}/discovery-wizard?source=bpmax`)
}

// 选择Salesforce
const selectSalesforce = () => {
  navigateTo(`/analysis/${processId}/discovery-wizard?source=salesforce`)
}

// 选择Jira
const selectJira = () => {
  navigateTo(`/analysis/${processId}/discovery-wizard?source=jira`)
}

// 选择泛微
const selectFanwei = () => {
  navigateTo(`/analysis/${processId}/discovery-wizard?source=fanwei`)
}

// 选择蓝凌
const selectLanling = () => {
  navigateTo(`/analysis/${processId}/discovery-wizard?source=lanling`)
}

// 选择文件上传
const selectFileUpload = () => {
  navigateTo(`/analysis/${processId}/discovery-wizard?source=upload`)
}

// 选择数据库连接
const selectDatabase = () => {
  navigateTo(`/analysis/${processId}/discovery-wizard?source=database`)
}

// 选择API接口（即将推出）
const selectAPIConnection = () => {
  ElMessage.info('API接口功能即将推出，敬请期待！')
}

// 回退功能
const goBack = () => {
  navigateTo(`/processes/${processId}`)
}
</script>

<style lang="scss" scoped>
.data-source-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  .data-source-container {
    width: 100%;

    .page-header {
      margin: 0 auto 3rem auto;
      animation: fadeInUp 0.6s ease-out;

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 2rem;

        @media (max-width: 1024px) {
          flex-direction: column;
          align-items: flex-start;
          gap: 1rem;
        }
      }

      .header-title {
        display: flex;
        align-items: center;
        gap: 1rem;

        .back-button {
          color: #6b7280;
          transition: all 0.3s ease;
          border-radius: 50%;
          width: 40px;
          height: 40px;
          border: none;
          background: rgba(255, 255, 255, 0.8);
          backdrop-filter: blur(10px);

          &:hover {
            color: #3b82f6;
            background-color: rgba(59, 130, 246, 0.1);
            transform: translateX(-2px);
          }

          :global(.dark) & {
            color: #9ca3af;
            background: rgba(30, 41, 59, 0.8);

            &:hover {
              color: #60a5fa;
              background-color: rgba(96, 165, 250, 0.1);
            }
          }
        }

        .title-section {
          .title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin: 0 0 0.5rem 0;
            letter-spacing: -0.025em;

            @media (max-width: 768px) {
              font-size: 2rem;
            }

            :global(.dark) & {
              background: linear-gradient(135deg, #60a5fa, #a78bfa);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }

          .description {
            color: #64748b;
            font-size: 1.125rem;
            font-weight: 500;
            margin: 0;
            opacity: 0.8;

            :global(.dark) & {
              color: #94a3b8;
            }
          }
        }
      }
    }

    .source-options {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: 1.5rem;
      justify-items: stretch;

      .source-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        padding: 2rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        box-shadow: 0 8px 12px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        width: 100%;
        height: 230px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        :global(.dark) & {
          background: rgba(30, 41, 59, 0.95);
          border: 2px solid rgba(71, 85, 105, 0.3);
        }

        &:hover {
          transform: translateY(-8px);
          border-color: #3b82f6;
          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

          :global(.dark) & {
            border-color: #60a5fa;
          }

          .source-icon {
            transform: scale(1.1);
          }
        }

        .source-icon {
          color: #3b82f6;
          margin-bottom: 1rem;
          transition: transform 0.3s ease;
          display: flex;
          justify-content: center;
          align-items: center;

          :global(.dark) & {
            color: #60a5fa;
          }

          > img {
            height: 42px !important;
            width: auto !important;
            // border-radius: 10px;
            object-fit: contain;
            max-width: none !important;
            max-height: none !important;
          }
        }

        h3 {
          font-size: 1.25rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 0.75rem 0;

          :global(.dark) & {
            color: #f9fafb;
          }
        }

        p {
          color: #6b7280;
          margin: 0 0 1rem 0;
          line-height: 1.5;
          font-size: 0.9rem;
          flex-grow: 1;

          :global(.dark) & {
            color: #9ca3af;
          }
        }

        .source-features {
          display: flex;
          gap: 0.5rem;
          justify-content: center;
          flex-wrap: wrap;

          .feature-tag {
            background: #eff6ff;
            color: #1d4ed8;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 500;

            :global(.dark) & {
              background: rgba(59, 130, 246, 0.1);
              color: #93c5fd;
            }
          }
        }
      }
    }

  }

  // 本地数据源分区样式
  .local-source-section {
    margin-top: 4rem;

    .section-header {
      margin-bottom: 2rem;

      .section-title {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.75rem;

        .section-icon {
          background: linear-gradient(135deg, #3b82f6, #8b5cf6);
          background-clip: text;

          :global(.dark) & {
            color: #34d399;
          }
        }

        h2 {
          font-size: 1.875rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0;
          
          background: linear-gradient(135deg, #3b82f6, #8b5cf6);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;

          :global(.dark) & {
            
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }

      .section-description {
        p {
          font-size: 1rem;
          color: #6b7280;
          margin: 0;

          :global(.dark) & {
            color: #9ca3af;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .data-source-page {
    padding: 1rem;

    .data-source-container {
      .header {
        margin-bottom: 2rem;

        h1 {
          font-size: 2rem;
        }

        p {
          font-size: 1rem;
        }
      }

      .source-options {
        grid-template-columns: 1fr;
        gap: 1rem;

        .source-card {
          padding: 1.5rem;
          height: 240px;

          h3 {
            font-size: 1.125rem;
          }

          p {
            font-size: 0.875rem;
          }
        }
      }

      .local-source-section {
        margin-top: 2.5rem;

        .section-header {
          margin-bottom: 1.5rem;

          .section-title {
            h2 {
              font-size: 1.5rem;
            }
          }

          .section-description {
            p {
              font-size: 0.9rem;
            }
          }
        }

        .local-source-options {
          grid-template-columns: 1fr;
          gap: 1rem;

          .local-card {
            padding: 1.5rem;
            height: 260px;

            h3 {
              font-size: 1.125rem;
            }

            p {
              font-size: 0.875rem;
            }

            .supported-formats {
              .format-item {
                font-size: 0.7rem;
                padding: 0.2rem 0.4rem;
              }
            }

            &.coming-soon {
              .coming-soon-badge {
                top: 0.75rem;
                right: 0.75rem;
                font-size: 0.7rem;
                padding: 0.2rem 0.6rem;
              }
            }
          }
        }
      }
    }

    // 本地数据源分区样式
    .local-source-section {
      margin-top: 4rem;

      .section-header {
        margin-bottom: 2rem;

        .section-title {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          margin-bottom: 0.75rem;

          .section-icon {
            color: #059669;

            :global(.dark) & {
              color: #34d399;
            }
          }

          h2 {
            font-size: 1.875rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
            background: linear-gradient(135deg, #059669, #10b981);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;

            :global(.dark) & {
              background: linear-gradient(135deg, #34d399, #6ee7b7);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }

        .section-description {
          p {
            font-size: 1rem;
            color: #6b7280;
            margin: 0;

            :global(.dark) & {
              color: #9ca3af;
            }
          }
        }
      }
    }
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .data-source-page {
    .data-source-container {
      .source-options {
        grid-template-columns: repeat(2, 1fr);
      }

      .local-source-section {
        .local-source-options {
          grid-template-columns: repeat(2, 1fr);
        }
      }
    }
  }
}

// 动画定义
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
