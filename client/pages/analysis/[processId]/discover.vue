<template>
  <div class="discover-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-title">
            <el-button :icon="ArrowLeft" text class="back-button" @click="navigateTo(`/processes/${processId}`)" />
            <div class="title-section">
              <h1 class="title">
                流程发现
              </h1>
              <p class="description">
                从事件日志中自动发现业务流程模型
              </p>
            </div>
          </div>
        </div>

        <!-- 导航选项卡 - 移动到右上角 -->
        <div class="header-tabs">
          <div class="tabs-container">
            <el-button
              :type="currentTab === 'basic' ? 'primary' : 'default'" :icon="Search" class="tab-button"
              size="small" @click="switchTab('basic')">
              基础流程发现
            </el-button>
            <el-button
              :type="currentTab === 'subprocess' ? 'primary' : 'default'" :icon="Share" class="tab-button"
              size="small" @click="switchTab('subprocess')">
              子流程发现
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <el-card>
        <div class="control-content">
          <div class="control-actions">
            <el-button v-if="!isReadOnly" type="primary" :icon="Search" :loading="isDiscovering" @click="startDiscovery(false)">
              开始流程发现
            </el-button>
            <el-button
              v-if="!isReadOnly && discoveryResult" type="warning" :icon="Refresh" :loading="isDiscovering"
              @click="startDiscovery(true)">
              强制刷新
            </el-button>
            <el-button v-if="discoveryResult" :icon="Download" @click="exportResult">
              导出结果
            </el-button>
            <el-button v-if="discoveryResult" :icon="Setting" @click="showConfigDialog">
              流程发现配置
            </el-button>
            <el-button v-if="discoveryResult" :icon="DataBoard" @click="navigateToCompare">
              流程比对分析
            </el-button>
          </div>

          <div v-if="discoveryResult" class="result-info">
            <div v-if="discoveryResult.timestamp" class="last-updated">
              最后更新: {{ formatDateTime(discoveryResult.timestamp) }}
            </div>
            <div v-if="isFromCache" class="cache-indicator">
              <el-tag type="success" size="small">
                <el-icon>
                  <Clock />
                </el-icon>
                从缓存加载
              </el-tag>
            </div>
            <div v-if="hasActiveFilters" class="filter-indicator">
              <el-tag type="warning" size="small">
                <el-icon>
                  <Filter />
                </el-icon>
                已应用筛选
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 初始加载状态 -->
    <div v-if="isLoading" class="loading-state">
      <el-skeleton :rows="4" animated />
    </div>

    <!-- 分析加载状态 -->
    <div v-else-if="isDiscovering" class="analyzing-state">
      <div class="loading-spinner" />
      <p class="analyzing-text">正在分析流程...</p>
    </div>

    <!-- 发现结果 - 左右并排布局 -->
    <div v-else-if="!isLoading && (discoveryResult || isDiscovering)" class="discovery-results">
      <div class="main-layout">
        <!-- 左侧筛选器面板容器 -->
        <div class="filter-panel-container">
          <!-- 筛选器面板 -->
          <div class="filter-panel" :class="{ 'collapsed': !filterPanelVisible }">
            <GlobalFilter
              v-if="originalDiscoveryResult"
              class="sidebar-filter"
              :process-id="processId"
              :discovery-data="originalDiscoveryResult"
              @filter-change="onFilterChange"
              @reset="onFilterReset"
            />
          </div>
          <!-- 筛选器切换按钮 - 始终显示 -->
          <div
            class="filter-toggle-button"
            title="切换筛选器面板"
            @click="toggleFilterPanel"
          >
            <el-icon>
              <ArrowLeft v-if="filterPanelVisible" />
              <ArrowRight v-else />
            </el-icon>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="content-area" :class="{ 'full-width': !filterPanelVisible }">
          <div class="grid-layout" :class="{ 'right-collapsed': rightPanelCollapsed }">
            <!-- DFG 流程图区域 -->
            <div class="dfg-section">
              <!-- 右侧面板折叠按钮 -->
              <div
                class="right-toggle-button"
                :title="rightPanelCollapsed ? '展开右侧面板' : '折叠右侧面板'"
                @click="toggleRightPanel"
              >
                <el-icon>
                  <ArrowLeft v-if="rightPanelCollapsed" />
                  <ArrowRight v-else />
                </el-icon>
              </div>
              <el-card class="dfg-card">
                <template #header>
                  <div class="dfg-header">
                    <h3 class="dfg-title">
                      直接跟随图 (DFG)
                    </h3>
                    <div class="dfg-controls">
                      <!-- 维度切换控制 -->
                      <div class="dimension-controls">
                        <el-radio-group
                          v-model="currentDimension" size="small" :disabled="!discoveryResult"
                          @change="onDimensionChange">
                          <el-radio-button value="frequency">频率</el-radio-button>
                          <el-radio-button value="duration">耗时</el-radio-button>
                        </el-radio-group>
                      </div>

                      <!-- 分隔线 -->
                      <div class="control-divider" />

                      <!-- 缩放控制 -->
                      <el-button
                        size="small" :disabled="!discoveryResult" :icon="Plus" title="放大 (Ctrl/Cmd + +)"
                        @click="zoomIn">
                        放大
                      </el-button>
                      <el-button
                        size="small" :disabled="!discoveryResult" :icon="Minus" title="缩小 (Ctrl/Cmd + -)"
                        @click="zoomOut">
                        缩小
                      </el-button>
                      <el-button
                        size="small" :disabled="!discoveryResult" :icon="RefreshRight"
                        title="重置缩放 (Ctrl/Cmd + 0)" @click="resetZoom">
                        重置
                      </el-button>
                      <el-button
                        size="small" :disabled="!discoveryResult" :icon="FullScreen" title="适应屏幕"
                        @click="fitToScreen">
                        适应屏幕
                      </el-button>
                      <div v-if="discoveryResult && zoomPercent !== null" class="zoom-indicator">
                        {{ zoomPercent }}%
                      </div>
                    </div>
                  </div>
                </template>

                <div class="dfg-container">
                  <div ref="dfgContainer" class="dfg-canvas" />
                  <div v-if="isDiscovering" class="dfg-loading-overlay">
                    <div class="dfg-loading-content">
                      <div class="loading-spinner" />
                      <p class="dfg-loading-text">正在生成流程图...</p>
                    </div>
                  </div>
                </div>

                <div v-if="discoveryResult" class="dfg-info" @click="showChartLegend = true">
                  <div class="dfg-info-content">
                    <el-icon class="dfg-info-icon">
                      <InfoFilled />
                    </el-icon>
                    <div class="dfg-info-text">
                      <p class="dfg-info-title">图表说明</p>
                    </div>
                    <el-icon class="dfg-info-arrow">
                      <ArrowRight />
                    </el-icon>
                  </div>
                </div>

              </el-card>
            </div>

            <!-- 右侧面板区域 -->
            <div class="right-panel-area">
              <template v-if="rightPanelCollapsed"/>
              <!-- 统计信息区域 -->
              <div class="statistics-section">
              <el-card v-if="discoveryResult" class="statistics-card">
                <template #header>
                  <h3 class="statistics-title">流程统计</h3>
                </template>
                <div class="statistics-grid">
                  <div class="statistic-item">
                    <div class="statistic-value statistic-value--blue">
                      {{ (discoveryResult && discoveryResult.statistics && discoveryResult.statistics.totalCases) || 0
                      }}
                    </div>
                    <div class="statistic-label">案例总数</div>
                  </div>
                  <div class="statistic-item">
                    <div class="statistic-value statistic-value--green">
                      {{ (discoveryResult && discoveryResult.statistics && discoveryResult.statistics.totalActivities)
                      || 0 }}
                    </div>
                    <div class="statistic-label">活动类型</div>
                  </div>
                  <div class="statistic-item">
                    <div class="statistic-value statistic-value--purple">
                      {{ formatCaseDuration(discoveryResult?.statistics?.avgCaseDuration) }}
                    </div>
                    <div class="statistic-label">平均持续时间</div>
                  </div>
                  <div class="statistic-item">
                    <div class="statistic-value statistic-value--orange">
                      {{(discoveryResult && discoveryResult.nodes && discoveryResult.nodes.filter((node: any) =>
                        node.id !== '开始'
                        && node.id !== '结束' && !node.isStartNode && !node.isEndNode).length) || 0 }}
                    </div>
                    <div class="statistic-label">活动节点</div>
                  </div>
                </div>
              </el-card>
            </div>

            <!-- 活动分布 / 执行频率分布 -->
            <div class="pie-section">
              <el-card v-if="discoveryResult" class="chart-card">
                <template #header>
                  <div class="chart-header">
                    <h4 class="chart-title">{{ pieView === 'activity' ? '活动分布' : '执行频率分布' }}</h4>
                    <div class="chart-controls">
                      <el-switch
                        v-model="pieViewIsActivity"
                        active-text="活动分布"
                        inactive-text="执行频率分布"
                        size="small"
                      />
                    </div>
                  </div>
                </template>
                <div class="chart-container">
                  <ClientOnly>
                    <v-chart :option="pieChartOption" class="chart" :autoresize="true" />
                    <template #fallback>
                      <div class="chart-loading">图表加载中...</div>
                    </template>
                  </ClientOnly>
                </div>
              </el-card>
            </div>

            <!-- 活动执行趋势柱状图 -->
            <div class="bar-chart-section">
              <el-card v-if="discoveryResult" class="chart-card">
                <template #header>
                  <h4 class="chart-title">活动执行趋势</h4>
                </template>
                <div class="chart-container">
                  <ClientOnly>
                    <v-chart :option="barChartOption" class="chart" :autoresize="true" />
                    <template #fallback>
                      <div class="chart-loading">图表加载中...</div>
                    </template>
                  </ClientOnly>
                </div>
              </el-card>
            </div>

            <!-- 活动频率详情表格 -->
            <div class="table-section">
              <el-card v-if="discoveryResult" class="chart-card">
                <template #header>
                  <h4 class="chart-title">活动频率详情</h4>
                </template>
                <div class="chart-container">
                  <el-table :data="activityFrequencyData" stripe size="small" height="100%">
                    <el-table-column prop="activity" label="活动名称" />
                    <el-table-column prop="frequency" label="频率" width="80" />
                    <el-table-column label="占比" width="80">
                      <template #default="{ row }">
                        {{ ((row.frequency / totalEvents) * 100).toFixed(1) }}%
                      </template>
                    </el-table-column>
                    <el-table-column label="频率条" width="120">
                      <template #default="{ row }">
                        <el-progress
                          :percentage="(row.frequency / maxFrequency) * 100" :show-text="false"
                          :stroke-width="6" />
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-card>
            </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!isLoading && !discoveryResult" class="empty-state">
      <el-icon class="empty-icon">
        <Search />
      </el-icon>
      <h3 class="empty-title">
        开始流程发现
      </h3>
      <p class="empty-description">
        点击上方按钮开始分析您的流程数据
      </p>
    </div>

    <!-- DFG详细信息浮层 -->
    <DfgDetailDialog
      v-model:visible="detailDialogVisible" :process-id="processId" :node-id="selectedNodeId"
      :source-id="selectedSourceId" :target-id="selectedTargetId" />

    <!-- 图表说明浮层 -->
    <el-dialog
      v-model="showChartLegend"
      title="图表说明"
      width="600px"
      :show-close="true"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      :modal="true"
      :append-to-body="true"
      :z-index="3000"
    >
      <div class="chart-legend-content">
        <ul class="chart-legend-list">
          <li v-if="currentDimension === 'frequency'">节点大小和颜色基于活动频率的数据分布动态调整</li>
          <li v-if="currentDimension === 'frequency'">绿色节点表示活动，颜色越深频率越高</li>
          <li v-if="currentDimension === 'frequency'">连接线颜色和粗细表示跟随关系频率（红色>橙色>蓝色）</li>
          <li v-if="currentDimension === 'duration'">节点大小和颜色基于活动平均耗时的数据分布动态调整</li>
          <li v-if="currentDimension === 'duration'">红色节点表示高耗时活动，绿色表示低耗时活动</li>
          <li v-if="currentDimension === 'duration'">连接线颜色和粗细表示转换耗时（红色>橙色>绿色）</li>
          <li>所有样式基于实际数据百分位数科学映射</li>
          <li>使用上方按钮切换频率和耗时维度显示</li>
          <li><strong>单击节点</strong>：高亮节点及其相关连线，显示流动动画效果</li>
          <li><strong>双击节点或连线</strong>：打开详细信息模态框，查看统计数据和图表</li>
          <li><strong>点击空白区域</strong>：取消所有高亮效果，恢复原始状态</li>
          <li>鼠标悬停显示快速信息提示</li>
          <li>使用鼠标滚轮或工具栏按钮进行缩放</li>
          <li>在空白区域拖拽可以平移图表视图</li>
          <li>支持键盘快捷键：Ctrl/Cmd + +/- 缩放，Ctrl/Cmd + 0 重置</li>
          <li>高亮模式下连线显示流动虚线动画，清晰表示流程方向</li>
        </ul>
      </div>
    </el-dialog>

    <!-- 筛选处理Loading覆盖层 -->
    <FilterLoadingOverlay :visible="isFilterLoadingVisible" :progress="filterProgress" :stage="filterStage" />

    <!-- 流程发现配置对话框 -->
    <ProcessDiscoveryConfig
      v-model:visible="configDialogVisible"
      :process-id="processId"
      :current-config="currentDiscoveryConfig"
      :read-only="isReadOnly"
      @confirm="applyDiscoveryConfig"
    />
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft, ArrowRight, Search, Download, InfoFilled, Refresh, Clock, Plus, Minus, RefreshRight, FullScreen, Share, Filter, Setting, DataBoard } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useApi } from '~/utils/api'
import { AnalysisType } from '~/types'
import type { ProcessDiscoveryOptions } from '~/types'
import * as go from 'gojs'
import DfgDetailDialog from '~/components/analysis/DfgDetailDialog.vue'
import GlobalFilter from '~/components/analysis/GlobalFilter.vue'
import FilterLoadingOverlay from '~/components/analysis/FilterLoadingOverlay.vue'
import ProcessDiscoveryConfig from '~/components/analysis/ProcessDiscoveryConfig.vue'
// 🔥 导入GoJS安全工具函数
import { validateDiscoveryResult, safeGoJSOperation } from '~/utils/gojs-safety'
// 动态导入VChart以避免SSR问题
const VChart = defineAsyncComponent(() => import('vue-echarts'))

// 页面配置
definePageMeta({
  layout: 'default'
})

const route = useRoute()
const processId = parseInt(route.params.processId as string)

// 设置页面标题
useHead({
  title: '流程发现 - ProMax'
})


// 获取流程状态并判定只读
const processesStore = useProcessesStore()
const { data: process } = await useLazyAsyncData(
  `process-${processId}`,
  () => processesStore.fetchProcess(processId)
)
const isReadOnly = computed(() => {
  const s = process.value?.status
  return s === 'completed' || s === 'archived'
})

// 状态
const isDiscovering = ref(false)
const discoveryResult = ref<any>(null)
const dfgContainer = ref<HTMLElement>()
const isLoading = ref(true)
const isFromCache = ref(false)
const cacheStatus = ref<any>(null)
const currentDimension = ref<'frequency' | 'duration'>('frequency')
const currentTab = ref<'basic' | 'subprocess'>('basic')
const zoomPercent = ref<number | null>(null)

let diagram: go.Diagram | null = null

// 筛选器相关状态
const currentFilters = ref<any>(null)
const hasActiveFilters = ref(false)
const originalDiscoveryResult = ref<any>(null) // 保存原始未筛选的数据

// 饼图视图切换：activity | frequency
const pieView = ref<'activity' | 'frequency'>('activity')
const pieViewIsActivity = computed({
  get: () => pieView.value === 'activity',
  set: (val: boolean) => { pieView.value = val ? 'activity' : 'frequency' }
})
const pieChartOption = computed(() => pieView.value === 'activity' ? activityPieOption.value : frequencyPieOption.value)

const filterPanelVisible = ref(true) // 筛选器面板显示状态

// Web Worker和Loading状态
const isFilterProcessing = ref(false)
const isFilterLoadingVisible = ref(false) // 控制Loading覆盖层的实际显示状态
const filterProgress = ref(0)
const filterStage = ref('initializing')
let filterWorker: Worker | null = null

// 右侧面板折叠状态
const rightPanelCollapsed = ref(false)

const toggleRightPanel = () => {
  rightPanelCollapsed.value = !rightPanelCollapsed.value

  // 延迟调整 GoJS 图表大小，等待 CSS 动画完成
  nextTick(() => {
    setTimeout(() => {
      if (diagram) {
        diagram.requestUpdate()
      }
    }, 350)
  })
}
// Loading节流控制相关的定时器和状态
let showLoadingTimer: NodeJS.Timeout | null = null
let hideLoadingTimer: NodeJS.Timeout | null = null
let minDisplayTimer: NodeJS.Timeout | null = null
let loadingStartTime: number = 0
const SHOW_DELAY = 400 // 显示延迟 400ms
const MIN_DISPLAY_TIME = 1000 // 最小显示时间 1000ms
const HIDE_DELAY = 250 // 消失延迟 250ms

// DFG详细信息浮层相关状态
const detailDialogVisible = ref(false)
const selectedNodeId = ref<string>('')
const showChartLegend = ref(false)
const selectedSourceId = ref<string>('')
const selectedTargetId = ref<string>('')

// DFG交互状态
const highlightedNodeKey = ref<string>('')
const highlightedLinks = ref<Set<string>>(new Set())
const isHighlightMode = ref(false)
let flowAnimation: go.Animation | null = null

// 流程发现配置状态
const configDialogVisible = ref(false)
const currentDiscoveryConfig = ref<ProcessDiscoveryOptions>({
  requiredActivities: [],
  forceRefresh: false
})

// 计算属性 - 过滤掉开始和结束节点
const activityFrequencyData = computed(() => {
  if (!discoveryResult.value || !discoveryResult.value.nodes || !Array.isArray(discoveryResult.value.nodes)) return []
  // 过滤掉开始和结束节点，只统计实际的活动节点
  return discoveryResult.value.nodes
    .filter((node: any) => node.id !== '开始' && node.id !== '结束' && !node.isStartNode && !node.isEndNode)
    .map((node: any) => ({
      activity: node.label || '',
      frequency: node.frequency || 0
    }))
    .sort((a: any, b: any) => b.frequency - a.frequency)
})

const totalEvents = computed(() => {
  if (!discoveryResult.value || !discoveryResult.value.nodes || !Array.isArray(discoveryResult.value.nodes)) return 0
  // 过滤掉开始和结束节点，只统计实际的活动节点
  return discoveryResult.value.nodes
    .filter((node: any) => node.id !== '开始' && node.id !== '结束' && !node.isStartNode && !node.isEndNode)
    .reduce((sum: number, node: any) => sum + (node.frequency || 0), 0)
})

const maxFrequency = computed(() => {
  if (!discoveryResult.value || !discoveryResult.value.nodes || !Array.isArray(discoveryResult.value.nodes) || discoveryResult.value.nodes.length === 0) return 0
  // 过滤掉开始和结束节点，只统计实际的活动节点
  const activityNodes = discoveryResult.value.nodes.filter((node: any) => node.id !== '开始' && node.id !== '结束' && !node.isStartNode && !node.isEndNode)
  if (activityNodes.length === 0) return 0
  return Math.max(...activityNodes.map((node: any) => node.frequency || 0))
})

// ECharts 图表配置
const activityPieOption = computed(() => {
  if (!discoveryResult.value || !discoveryResult.value.nodes) return {}

  const data = activityFrequencyData.value.slice(0, 6).map((item: { activity: string; frequency: number }) => ({
    name: item.activity,
    value: item.frequency
  }))

  return {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 10
      }
    },
    series: [
      {
        name: '活动分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
})

const frequencyPieOption = computed(() => {
  if (!discoveryResult.value || !discoveryResult.value.nodes) return {}

  // 动态计算频率分布范围，基于实际数据
  const frequencies = activityFrequencyData.value.map(item => item.frequency)
  const maxFreq = Math.max(...frequencies)

  // 根据数据范围动态设置阈值
  const highThreshold = Math.ceil(maxFreq * 0.7)  // 70%以上为高频
  const lowThreshold = Math.ceil(maxFreq * 0.3)   // 30%以下为低频

  const ranges = [
    { name: `高频(≥${highThreshold})`, min: highThreshold, color: '#ff4757' },
    { name: `中频(${lowThreshold}-${highThreshold - 1})`, min: lowThreshold, max: highThreshold - 1, color: '#ffa502' },
    { name: `低频(<${lowThreshold})`, max: lowThreshold - 1, color: '#3742fa' }
  ]

  const data = ranges.map(range => {
    const count = activityFrequencyData.value.filter((item: { activity: string; frequency: number }) => {
      if (range.min !== undefined && range.max !== undefined) {
        return item.frequency >= range.min && item.frequency <= range.max
      } else if (range.min !== undefined) {
        return item.frequency >= range.min
      } else if (range.max !== undefined) {
        return item.frequency <= range.max
      }
      return false
    }).length

    return {
      name: range.name,
      value: count,
      itemStyle: { color: range.color }
    }
  }).filter(item => item.value > 0) // 只显示有数据的分类

  return {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}个活动 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 10
      }
    },
    series: [
      {
        name: '频率分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
})

const barChartOption = computed(() => {
  if (!discoveryResult.value || !discoveryResult.value.nodes) return {}

  const data = activityFrequencyData.value.slice(0, 8)

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '1%',
      right: '1%',
      top: '3%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.map((item: { activity: string; frequency: number }) => item.activity),
      axisLabel: {
        rotate: 45,
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 10
      }
    },
    series: [
      {
        name: '执行频率',
        type: 'bar',
        data: data.map((item: { activity: string; frequency: number }) => item.frequency),
        itemStyle: {
          color: '#3742fa'
        }
      }
    ]
  }
})

// 等待容器可用的辅助函数
const waitForContainer = async (maxAttempts = 10): Promise<boolean> => {
  for (let i = 0; i < maxAttempts; i++) {
    if (dfgContainer.value) {
      console.log(`Container found after ${i + 1} attempts`)
      return true
    }
    console.log(`Attempt ${i + 1}: Container not found, waiting...`)
    await new Promise(resolve => setTimeout(resolve, 100))
    await nextTick()
  }
  console.error('Container not found after maximum attempts')
  return false
}

// 数据驱动的颜色映射算法（专业流程挖掘标准）
const calculateDataDistribution = (nodes: any[], edges: any[], dimension: 'frequency' | 'duration' = 'frequency') => {
  let nodeValues: number[] = []
  let edgeValues: number[] = []

  if (dimension === 'frequency') {
    nodeValues = nodes.map(n => n.frequency || 0).filter(f => f > 0)
    edgeValues = edges.map(e => e.frequency || 0).filter(f => f > 0)
  } else {
    // 耗时维度：使用平均耗时（毫秒转换为分钟）
    nodeValues = nodes.map(n => (n.avgDuration || 0) / 60000).filter(d => d > 0)
    edgeValues = edges.map(e => (e.avgDuration || 0) / 60000).filter(d => d > 0)
  }

  if (nodeValues.length === 0 && edgeValues.length === 0) {
    return {
      nodePercentiles: { p25: 0, p50: 0, p75: 0, p90: 0, max: 0, logScale: false },
      edgePercentiles: { p25: 0, p50: 0, p75: 0, p90: 0, max: 0, logScale: false }
    }
  }

  const calculateAdvancedPercentiles = (values: number[]) => {
    if (values.length === 0) return { p25: 0, p50: 0, p75: 0, p90: 0, max: 0, logScale: false }

    const sorted = [...values].sort((a, b) => a - b)
    const min = sorted[0]
    const max = sorted[sorted.length - 1]

    // 检查是否需要对数缩放（数据跨度超过2个数量级）
    const needsLogScale = dimension === 'duration' && max > 0 && min > 0 && (max / min) > 100

    let processedValues = sorted
    if (needsLogScale) {
      // 对数变换以处理高度偏斜的耗时数据
      processedValues = sorted.map(v => Math.log10(Math.max(v, 0.1))) // 避免log(0)
    }

    // 使用更精确的百分位数计算
    const getPercentile = (arr: number[], p: number) => {
      const index = (arr.length - 1) * p
      const lower = Math.floor(index)
      const upper = Math.ceil(index)
      const weight = index % 1

      if (upper >= arr.length) return arr[arr.length - 1]
      return arr[lower] * (1 - weight) + arr[upper] * weight
    }

    const percentiles = {
      p25: getPercentile(processedValues, 0.25),
      p50: getPercentile(processedValues, 0.5),
      p75: getPercentile(processedValues, 0.75),
      p90: getPercentile(processedValues, 0.9),
      max: processedValues[processedValues.length - 1],
      logScale: needsLogScale,
      originalMax: max,
      originalMin: min
    }

    // 如果使用了对数缩放，需要将百分位数转换回原始尺度
    if (needsLogScale) {
      return {
        ...percentiles,
        p25: Math.pow(10, percentiles.p25),
        p50: Math.pow(10, percentiles.p50),
        p75: Math.pow(10, percentiles.p75),
        p90: Math.pow(10, percentiles.p90),
        max: Math.pow(10, percentiles.max)
      }
    }

    return percentiles
  }

  return {
    nodePercentiles: calculateAdvancedPercentiles(nodeValues),
    edgePercentiles: calculateAdvancedPercentiles(edgeValues)
  }
}

// 筛选器面板切换
const toggleFilterPanel = () => {
  console.log('Toggle filter panel clicked, current state:', filterPanelVisible.value)
  filterPanelVisible.value = !filterPanelVisible.value
  console.log('New filter panel state:', filterPanelVisible.value)

  // 保存状态到本地存储
  localStorage.setItem(`filter_panel_visible_${processId}`, filterPanelVisible.value.toString())

  // 延迟调整图表大小以适应新布局
  nextTick(() => {
    if (diagram) {
      setTimeout(() => {
        diagram?.requestUpdate()
        diagram?.zoomToFit()
      }, 300) // 等待CSS动画完成
    }
  })
}

// 维度切换处理
const onDimensionChange = () => {
  if (discoveryResult.value && diagram) {
    // 先清除之前的高亮状态
    clearHighlight()
    // 然后重新渲染图表
    renderDFG()
  }
}

// 格式化耗时显示 - 统一格式化逻辑，与浮层保持一致
const formatDuration = (milliseconds: number): string => {
  if (!milliseconds || milliseconds <= 0) {
    return '0秒'
  }

  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) {
    return `${days}天${hours % 24}小时`
  } else if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟`
  } else if (seconds > 0) {
    return `${seconds}秒`
  } else {
    return '<1秒'
  }
}

// 格式化案例持续时间显示（专门用于统计卡片）
const formatCaseDuration = (milliseconds: number | undefined): string => {
  if (!milliseconds || milliseconds <= 0) {
    return '0小时'
  }

  // 将毫秒转换为小时，保留1位小数
  const hours = milliseconds / (1000 * 60 * 60)

  if (hours < 1) {
    const minutes = Math.round(milliseconds / (1000 * 60))
    return `${minutes}分钟`
  } else if (hours < 24) {
    return `${hours.toFixed(1)}小时`
  } else {
    const days = hours / 24
    return `${days.toFixed(1)}天`
  }
}

// 动态节点样式映射（专业流程挖掘标准）
const getNodeStyle = (value: number, percentiles: any, dimension: 'frequency' | 'duration' = 'frequency') => {
  const { p25, p50, p75, p90, max, logScale } = percentiles

  // 根据维度选择不同的颜色方案
  const colorSchemes = {
    frequency: {
      high: { fill: '#2E7D32', stroke: '#1B5E20', textColor: '#FFFFFF' }, // 深绿色 - 高频
      mediumHigh: { fill: '#388E3C', stroke: '#2E7D32', textColor: '#FFFFFF' }, // 中深绿色
      medium: { fill: '#4CAF50', stroke: '#388E3C', textColor: '#FFFFFF' }, // 标准绿色
      mediumLow: { fill: '#81C784', stroke: '#4CAF50', textColor: '#1B5E20' }, // 浅绿色
      low: { fill: '#C8E6C9', stroke: '#81C784', textColor: '#2E7D32' } // 很浅绿色
    },
    duration: {
      high: { fill: '#D32F2F', stroke: '#B71C1C', textColor: '#FFFFFF' }, // 深红色 - 高耗时
      mediumHigh: { fill: '#F44336', stroke: '#D32F2F', textColor: '#FFFFFF' }, // 红色
      medium: { fill: '#FF9800', stroke: '#F57C00', textColor: '#FFFFFF' }, // 橙色
      mediumLow: { fill: '#FFC107', stroke: '#FF9800', textColor: '#1B5E20' }, // 黄色
      low: { fill: '#4CAF50', stroke: '#388E3C', textColor: '#FFFFFF' } // 绿色 - 低耗时
    }
  }

  const colors = colorSchemes[dimension]

  // 计算标准化值（0-1范围）用于尺寸映射
  let normalizedValue = max > 0 ? value / max : 0

  // 对于对数缩放的数据，使用更平滑的映射
  if (logScale && dimension === 'duration') {
    const logValue = Math.log10(Math.max(value, 0.1))
    const logMax = Math.log10(Math.max(max, 0.1))
    normalizedValue = logMax > 0 ? logValue / logMax : 0
  }

  // 使用更科学的尺寸映射算法
  const getSizeMultiplier = (normalizedVal: number) => {
    // 使用平方根函数使尺寸差异更加明显但不过于极端
    return Math.sqrt(normalizedVal)
  }

  const sizeMultiplier = getSizeMultiplier(normalizedValue)

  if (value >= p90) {
    return {
      ...colors.high,
      strokeWidth: 3,
      width: Math.max(120, Math.min(160, 120 + sizeMultiplier * 40)),
      height: Math.max(60, Math.min(80, 60 + sizeMultiplier * 20))
    }
  } else if (value >= p75) {
    return {
      ...colors.mediumHigh,
      strokeWidth: 2.5,
      width: Math.max(100, Math.min(140, 100 + sizeMultiplier * 40)),
      height: Math.max(55, Math.min(75, 55 + sizeMultiplier * 20))
    }
  } else if (value >= p50) {
    return {
      ...colors.medium,
      strokeWidth: 2,
      width: Math.max(90, Math.min(120, 90 + sizeMultiplier * 30)),
      height: Math.max(50, Math.min(70, 50 + sizeMultiplier * 20))
    }
  } else if (value >= p25) {
    return {
      ...colors.mediumLow,
      strokeWidth: 2,
      width: Math.max(85, Math.min(110, 85 + sizeMultiplier * 25)),
      height: Math.max(45, Math.min(65, 45 + sizeMultiplier * 20))
    }
  } else {
    return {
      ...colors.low,
      strokeWidth: 1.5,
      width: Math.max(80, Math.min(100, 80 + sizeMultiplier * 20)),
      height: Math.max(40, Math.min(60, 40 + sizeMultiplier * 20))
    }
  }
}

// 动态连接线样式映射（专业流程挖掘标准）
const getLinkStyle = (value: number, percentiles: any, dimension: 'frequency' | 'duration' = 'frequency') => {
  const { p25, p50, p75, p90, max, logScale } = percentiles

  // 根据维度选择不同的颜色方案
  const colorSchemes = {
    frequency: {
      high: '#D32F2F',    // 深红色 - 高频连接
      mediumHigh: '#F44336', // 红色
      medium: '#FF9800',     // 橙色
      mediumLow: '#2196F3',  // 蓝色
      low: '#90CAF9'         // 浅蓝色 - 低频连接
    },
    duration: {
      high: '#D32F2F',      // 深红色 - 高耗时连接
      mediumHigh: '#F44336', // 红色
      medium: '#FF9800',     // 橙色
      mediumLow: '#FFC107',  // 黄色
      low: '#4CAF50'         // 绿色 - 低耗时连接
    }
  }

  const colors = colorSchemes[dimension]

  // 计算标准化值用于线宽映射
  let normalizedValue = max > 0 ? value / max : 0

  // 对于对数缩放的数据，使用更平滑的映射
  if (logScale && dimension === 'duration') {
    const logValue = Math.log10(Math.max(value, 0.1))
    const logMax = Math.log10(Math.max(max, 0.1))
    normalizedValue = logMax > 0 ? logValue / logMax : 0
  }

  // 使用更科学的线宽映射算法
  const getStrokeMultiplier = (normalizedVal: number) => {
    // 使用平方根函数使线宽差异更加明显但不过于极端
    return Math.sqrt(normalizedVal)
  }

  const strokeMultiplier = getStrokeMultiplier(normalizedValue)

  if (value >= p90) {
    const strokeWidth = Math.max(6, Math.min(12, 6 + strokeMultiplier * 6))
    return {
      stroke: colors.high,
      strokeWidth,
      arrowScale: Math.max(1.2, Math.min(2.5, 0.8 + strokeWidth * 0.15)) // 箭头大小与线宽成正比
    }
  } else if (value >= p75) {
    const strokeWidth = Math.max(5, Math.min(10, 5 + strokeMultiplier * 5))
    return {
      stroke: colors.mediumHigh,
      strokeWidth,
      arrowScale: Math.max(1.1, Math.min(2.2, 0.8 + strokeWidth * 0.15))
    }
  } else if (value >= p50) {
    const strokeWidth = Math.max(4, Math.min(8, 4 + strokeMultiplier * 4))
    return {
      stroke: colors.medium,
      strokeWidth,
      arrowScale: Math.max(1.0, Math.min(2.0, 0.8 + strokeWidth * 0.15))
    }
  } else if (value >= p25) {
    const strokeWidth = Math.max(3, Math.min(6, 3 + strokeMultiplier * 3))
    return {
      stroke: colors.mediumLow,
      strokeWidth,
      arrowScale: Math.max(0.9, Math.min(1.8, 0.8 + strokeWidth * 0.15))
    }
  } else {
    const strokeWidth = Math.max(2, Math.min(4, 2 + strokeMultiplier * 2))
    return {
      stroke: colors.low,
      strokeWidth,
      arrowScale: Math.max(0.8, Math.min(1.5, 0.8 + strokeWidth * 0.15))
    }
  }
}

// 高亮节点及其相关连线
const highlightNode = (nodeKey: string) => {
  if (!diagram) return

  console.log('Highlighting node:', nodeKey)

  // 如果之前有高亮的节点，先清除之前的高亮状态
  if (highlightedNodeKey.value && highlightedNodeKey.value !== nodeKey) {
    console.log('Clearing previous highlight before setting new one')
    clearHighlight()
  }

  highlightedNodeKey.value = nodeKey
  highlightedLinks.value.clear()
  isHighlightMode.value = true

  diagram.startTransaction('highlight node')

  // 重置所有节点和连线的高亮状态
  diagram.nodes.each((node: go.Node) => {
    const shape = node.findObject('BODY') as go.Shape
    if (shape) {
      if (node.data.key === nodeKey) {
        // 高亮选中的节点 - 只改变边框，不改变尺寸
        shape.opacity = 1.0
        // shape.stroke = '#FFD700' // 金色边框
        // shape.strokeWidth = Math.max((node.data.nodeStyle?.strokeWidth || 2) + 1, 3)
      } else {
        // 弱化其他节点
        shape.opacity = 0.3
        // 保持原始边框样式
        shape.stroke = node.data.nodeStyle?.stroke || '#388E3C'
        // shape.strokeWidth = node.data.nodeStyle?.strokeWidth || 2
      }
    }
  })

  // 找到与选中节点直接相关的连线
  diagram.links.each((link: go.Link) => {
    const linkShape = link.findObject('LINKSHAPE') as go.Shape
    const arrowShape = link.findObject('ARROWSHAPE') as go.Shape
    const linkKey = link.data.key || `${link.data.from}-${link.data.to}`

    // 只高亮直接连接到选中节点的连线
    if (link.fromNode?.data.key === nodeKey || link.toNode?.data.key === nodeKey) {
      // 高亮相关连线
      highlightedLinks.value.add(linkKey)
      if (linkShape) {
        linkShape.opacity = 1.0
        // 不改变线宽，保持原始样式
        linkShape.strokeWidth = link.data.linkStyle?.strokeWidth || 4
      }
      if (arrowShape) {
        arrowShape.opacity = 1.0
      }

      // 设置虚线样式，根据连线粗细动态调整间隔长度
      const originalWidth = link.data.linkStyle?.strokeWidth || 4

      // 根据连线粗细动态调整虚线间隔，确保视觉协调性
      // 粗连线使用更大的虚线间隔，细连线使用较小的间隔
      const dashLength = Math.max(originalWidth * 4, 16) // 增加虚线长度，使动画更明显
      const gapLength = Math.max(originalWidth * 2.5, 10) // 调整间隔长度，保持比例协调

      if (linkShape) {
        linkShape.strokeDashArray = [dashLength, gapLength]
        linkShape.strokeWidth = originalWidth + 2
        // 初始化strokeDashOffset为虚线周期长度，准备从正值到0的动画
        const dashCycle = dashLength + gapLength
        linkShape.strokeDashOffset = dashCycle
        console.log('Set dash array for link:', linkKey, 'dashArray:', [dashLength, gapLength], 'dashCycle:', dashCycle)
      }
    } else {
      // 弱化其他连线
      if (linkShape) {
        linkShape.opacity = 0.2
      }
      if (arrowShape) {
        arrowShape.opacity = 0.2
      }
    }
  })

  diagram.commitTransaction('highlight node')

  // 先对齐开始和结束节点，确保布局正确
  alignStartEndNodes(diagram)

  // 然后启动流动动画
  console.log('About to start flow animation, highlighted links count:', highlightedLinks.value.size)
  startFlowAnimationForHighlightedLinks()
}

// 高亮分组及其内部所有节点和连线
const highlightGroup = (groupKey: string) => {
  if (!diagram) return

  console.log('Highlighting group:', groupKey)

  // 如果之前有高亮的节点，先清除之前的高亮状态
  if (highlightedNodeKey.value && highlightedNodeKey.value !== groupKey) {
    console.log('Clearing previous highlight before setting new one')
    clearHighlight()
  }

  highlightedNodeKey.value = groupKey
  highlightedLinks.value.clear()
  isHighlightMode.value = true

  diagram.startTransaction('highlight group')

  // 找到目标分组
  let targetGroup: go.Group | null = null
  diagram.nodes.each((node: go.Node) => {
    if (node instanceof go.Group && (node.data.key === groupKey || node.data.text === groupKey)) {
      targetGroup = node as go.Group
    }
  })

  if (!targetGroup) {
    console.warn('Target group not found:', groupKey)
    diagram.commitTransaction('highlight group')
    return
  }

  // 获取分组内的所有节点
  const groupMembers = new Set<string>()
  diagram.nodes.each((node: go.Node) => {
    if (node.containingGroup === targetGroup) {
      groupMembers.add(node.data.key || node.data.id || node.data.label)
    }
  })

  // 重置所有节点的高亮状态
  diagram.nodes.each((node: go.Node) => {
    const shape = node.findObject('BODY') as go.Shape
    if (shape) {
      const nodeKey = node.data.key || node.data.id || node.data.label
      if (node === targetGroup || groupMembers.has(nodeKey)) {
        // 高亮分组和分组内的节点
        shape.opacity = 1.0
      } else {
        // 弱化其他节点
        shape.opacity = 0.3
      }
    }
  })

  // 高亮分组内的连线和与分组相关的连线
  diagram.links.each((link: go.Link) => {
    const linkShape = link.findObject('LINKSHAPE') as go.Shape
    const arrowShape = link.findObject('ARROWSHAPE') as go.Shape
    const linkKey = link.data.key || `${link.data.from}-${link.data.to}`

    const fromNodeKey = link.fromNode?.data.key || link.fromNode?.data.id || link.fromNode?.data.label
    const toNodeKey = link.toNode?.data.key || link.toNode?.data.id || link.toNode?.data.label

    // 高亮分组内的连线或与分组相关的连线
    if (groupMembers.has(fromNodeKey) || groupMembers.has(toNodeKey)) {
      highlightedLinks.value.add(linkKey)
      if (linkShape) {
        linkShape.opacity = 1.0
        linkShape.strokeWidth = (link.data.linkStyle?.strokeWidth || 4) + 2
      }
      if (arrowShape) {
        arrowShape.opacity = 1.0
      }

      // 为分组内的连线添加流动动画
      const originalWidth = link.data.linkStyle?.strokeWidth || 4
      const dashLength = Math.max(originalWidth * 4, 16)
      const gapLength = Math.max(originalWidth * 2.5, 10)

      if (linkShape) {
        linkShape.strokeDashArray = [dashLength, gapLength]
        linkShape.strokeDashOffset = dashLength + gapLength
      }
    } else {
      // 弱化其他连线
      if (linkShape) {
        linkShape.opacity = 0.2
      }
      if (arrowShape) {
        arrowShape.opacity = 0.2
      }
    }
  })

  diagram.commitTransaction('highlight group')

  // 启动流动动画
  console.log('About to start flow animation for group, highlighted links count:', highlightedLinks.value.size)
  startFlowAnimationForHighlightedLinks()
}

// 清除所有高亮效果
const clearHighlight = () => {
  if (!diagram) return

  console.log('Clearing highlight')

  highlightedNodeKey.value = ''
  highlightedLinks.value.clear()
  isHighlightMode.value = false

  // 停止所有动画
  stopAllAnimations()

  diagram.startTransaction('clear highlight')

  // 恢复所有节点的原始状态
  diagram.nodes.each((node: go.Node) => {
    const shape = node.findObject('BODY') as go.Shape
    if (shape) {
      shape.opacity = 1.0
      shape.strokeWidth = node.data.nodeStyle?.strokeWidth || 2
      shape.stroke = node.data.nodeStyle?.stroke || '#388E3C'
    }
  })

  // 恢复所有连线的原始状态
  diagram.links.each((link: go.Link) => {
    const linkShape = link.findObject('LINKSHAPE') as go.Shape
    const arrowShape = link.findObject('ARROWSHAPE') as go.Shape

    if (linkShape) {
      linkShape.opacity = 1.0
      linkShape.strokeWidth = link.data.linkStyle?.strokeWidth || 4
      linkShape.stroke = link.data.linkStyle?.stroke || '#2196F3'
      // 确保移除虚线样式和偏移
      linkShape.strokeDashArray = null
      linkShape.strokeDashOffset = 0
    }
    if (arrowShape) {
      arrowShape.opacity = 1.0
      arrowShape.fill = link.data.linkStyle?.stroke || '#2196F3'
      arrowShape.stroke = link.data.linkStyle?.stroke || '#2196F3'
    }

    // 移除流动动画
    removeFlowAnimation(link)
  })

  diagram.commitTransaction('clear highlight')

  // 重新对齐开始和结束节点，确保布局正确
  alignStartEndNodes(diagram)
}



// 移除流动动画效果
const removeFlowAnimation = (link: go.Link) => {
  if (!link) return

  try {
    const linkShape = link.findObject('LINKSHAPE') as go.Shape
    if (linkShape) {
      // 移除虚线样式
      linkShape.strokeDashArray = null

      // 重置虚线偏移
      linkShape.strokeDashOffset = 0

      // 恢复原始线宽
      linkShape.strokeWidth = link.data.linkStyle?.strokeWidth || 4
    }
  } catch (error) {
    console.debug('Error in removeFlowAnimation:', error)
  }
}

// 为所有高亮的连线启动流动动画
const startFlowAnimationForHighlightedLinks = () => {
  if (!diagram || !isHighlightMode.value) {
    console.log('Animation not started: diagram or highlight mode check failed', { diagram: !!diagram, isHighlightMode: isHighlightMode.value })
    return
  }

  try {
    console.log('Starting flow animation, highlighted links:', highlightedLinks.value.size)

    // 停止之前的动画 - 参考官方Process Flow示例
    if (flowAnimation) {
      console.log('Stopping previous animation')
      flowAnimation.stop()
    }

    // 创建新的动画 - 优化速度和流畅度
    flowAnimation = new go.Animation()
    flowAnimation.easing = go.Animation.EaseLinear
    flowAnimation.duration = 1200 // 提升动画速度，从2000ms减少到1200ms，使动画更快更流畅
    flowAnimation.runCount = Infinity // 确保无限循环效果，创建持续的流动视觉效果

    console.log('Created new animation object')

    // 确保动画管理器已启用，这是动画正常运行的前提
    if (diagram.animationManager) {
      console.log('AnimationManager state:', {
        isEnabled: diagram.animationManager.isEnabled,
        isAnimating: diagram.animationManager.isAnimating,
        isTicking: diagram.animationManager.isTicking
      })

      // 强制启用动画管理器，确保动画能够正常播放
      if (!diagram.animationManager.isEnabled) {
        console.log('Enabling AnimationManager for flow animation')
        diagram.animationManager.isEnabled = true
      }
    } else {
      console.warn('AnimationManager not found on diagram')
    }

    // 为所有高亮的连线添加动画
    let animationCount = 0
    diagram.links.each((link: go.Link) => {
      const linkKey = link.data.key || `${link.data.from}-${link.data.to}`
      if (highlightedLinks.value.has(linkKey)) {
        const linkShape = link.findObject('LINKSHAPE') as go.Shape
        console.log('Processing link:', linkKey, 'linkShape found:', !!linkShape)

        if (linkShape) {
          console.log('Link shape properties:', {
            strokeDashArray: linkShape.strokeDashArray,
            strokeDashOffset: linkShape.strokeDashOffset,
            strokeWidth: linkShape.strokeWidth
          })

          if (linkShape.strokeDashArray) {
            const dashArray = linkShape.strokeDashArray
            const dashCycle = dashArray.reduce((sum, val) => sum + val, 0)
            const strokeWidth = linkShape.strokeWidth || 4

            console.log('Adding animation for link:', linkKey, 'dashCycle:', dashCycle, 'dashArray:', dashArray, 'strokeWidth:', strokeWidth)

            // 根据连线粗细动态调整动画参数，确保动画与连线粗细成比例
            // 粗连线使用更大的动画偏移量，细连线使用较小的偏移量
            const animationOffset = Math.max(dashCycle, strokeWidth * 5)

            // 参考官方Process Flow示例的实现方式
            // 使用动态计算的偏移量作为起始值，0作为结束值，确保流动方向正确
            flowAnimation!.add(linkShape, "strokeDashOffset", animationOffset, 0)
            animationCount++

            console.log('Animation offset for link:', linkKey, 'offset:', animationOffset)
          } else {
            console.log('No strokeDashArray found for link:', linkKey)
          }
        }
      }
    })

    console.log('Added animations for', animationCount, 'links')

    // 启动动画
    console.log('Animation object state:', {
      duration: flowAnimation.duration,
      runCount: flowAnimation.runCount,
      easing: flowAnimation.easing,
      animationCount: animationCount
    })

    if (animationCount > 0) {
      console.log('Starting animation with', animationCount, 'links')
      try {
        flowAnimation.start()
        console.log('Animation started successfully, isAnimating:', flowAnimation.isAnimating)
      } catch (startError) {
        console.error('Error starting animation:', startError)
      }
    } else {
      console.log('No links to animate')
    }
  } catch (error) {
    console.error('Error in startFlowAnimationForHighlightedLinks:', error)
  }
}

// 停止所有流动动画
const stopAllAnimations = () => {
  if (flowAnimation) {
    console.log('Stopping flow animation')
    try {
      flowAnimation.stop()
      flowAnimation = null
      console.log('Flow animation stopped successfully')
    } catch (error) {
      console.error('Error stopping animation:', error)
      flowAnimation = null
    }
  }
}

// 对齐开始和结束节点到垂直中心线
const alignStartEndNodes = (diagram: go.Diagram) => {
  if (!diagram) return

  console.log('Aligning start and end nodes...')

  const activityNodes: go.Node[] = []

  // 找到开始、结束和活动节点（包括子流程的开始和结束节点）
  const startNodes: go.Node[] = []
  const endNodes: go.Node[] = []

  diagram.nodes.each((node: go.Node) => {
    const nodeId = node.data.key || node.data.id
    if (node.data.isStartNode || nodeId === '开始' || nodeId.startsWith('开始_')) {
      startNodes.push(node)
    } else if (node.data.isEndNode || nodeId === '结束' || nodeId.startsWith('结束_')) {
      endNodes.push(node)
    } else {
      activityNodes.push(node)
    }
  })

  // 计算所有活动节点的水平中心位置
  let totalX = 0
  let nodeCount = 0

  activityNodes.forEach(node => {
    totalX += node.location.x
    nodeCount++
  })

  // 如果没有活动节点，使用画布中心
  let centerX: number
  if (nodeCount > 0) {
    centerX = totalX / nodeCount
  } else {
    const bounds = diagram.documentBounds
    centerX = bounds.centerX
  }

  console.log('Calculated center X:', centerX)
  console.log('Found start nodes:', startNodes.length, 'end nodes:', endNodes.length)

  // 对齐所有开始和结束节点到中心位置
  diagram.startTransaction('align start end nodes')

  // 对齐所有开始节点
  startNodes.forEach((node) => {
    const startY = node.location.y
    node.location = new go.Point(centerX, startY)
    console.log(`Aligned start node ${node.data.key || node.data.id} to (${centerX}, ${startY})`)
  })

  // 对齐所有结束节点
  endNodes.forEach((node) => {
    const endY = node.location.y
    node.location = new go.Point(centerX, endY)
    console.log(`Aligned end node ${node.data.key || node.data.id} to (${centerX}, ${endY})`)
  })

  diagram.commitTransaction('align start end nodes')
}

// 初始化 GoJS 图表
const initDiagram = () => {
  if (!dfgContainer.value) {
    console.error('DFG container not found')
    return null
  }

  // 设置 GoJS 许可证密钥
  if (!go.Diagram.licenseKey) {
    go.Diagram.licenseKey = "2bf843e7b36758c511895a25406c7efb0bab2d67ce864df3595012a0ed587a04249fb87b50d7d8c986aa4df9182ec98ed8976121931c0338e737d48f45e0d5f1b63124e5061841dbf4052691c9fb38b1ff7971fbddbc68a2d2"
  }

  console.log('Initializing GoJS diagram...')
  console.log('Container dimensions:', dfgContainer.value.offsetWidth, 'x', dfgContainer.value.offsetHeight)
  console.log('GoJS loaded:', typeof go)

  const $ = go.GraphObject.make

  try {
    const myDiagram = new go.Diagram(dfgContainer.value, {
      'undoManager.isEnabled': true,
      layout: $(go.LayeredDigraphLayout, {
        direction: 90,
        layerSpacing: 80,
        columnSpacing: 50,
        setsPortSpots: false,
        packOption: go.LayeredDigraphLayout.PackStraighten
      }),
      initialContentAlignment: go.Spot.Center,
      // 启用鼠标滚轮缩放
      'toolManager.mouseWheelBehavior': go.WheelMode.Zoom,
      // 启用动画管理器以支持连线流动动画
      'animationManager.isEnabled': true,
      allowDrop: false,
      allowMove: false,
      allowCopy: false,
      allowDelete: false,
      allowSelect: true, // 允许选择以便点击查看详情
      // 设置缩放范围
      minScale: 0.1,
      maxScale: 5.0,
      // 设置图表边距
      padding: new go.Margin(20, 20, 20, 20),
      // 设置背景点击行为 - 允许在背景拖拽进行平移
      hasHorizontalScrollbar: false,
      hasVerticalScrollbar: false,
      // 启用网格背景
      'grid.visible': true,
      'grid.gridCellSize': new go.Size(20, 20),
      'grid.gridOrigin': new go.Point(0, 0)
    })

    console.log('GoJS diagram created successfully')

    // 启用拖拽平移功能
    myDiagram.toolManager.panningTool.isEnabled = true
    // 禁用节点拖拽
    myDiagram.toolManager.draggingTool.isEnabled = false
    // 禁用连线工具
    myDiagram.toolManager.linkingTool.isEnabled = false
    myDiagram.toolManager.relinkingTool.isEnabled = false
    // 禁用框选工具
    myDiagram.toolManager.dragSelectingTool.isEnabled = false

    // 定义分组模板 - 用于子流程可视化
    myDiagram.groupTemplate = $(go.Group, 'Auto',
      {
        background: 'transparent',
        ungroupable: true,
        // 设置分组的布局
        layout: $(go.LayeredDigraphLayout, {
          direction: 90,
          layerSpacing: 60,
          columnSpacing: 40,
          setsPortSpots: false
        }),
        computesBoundsAfterDrag: true,
        computesBoundsIncludingLocation: true,
        handlesDragDropForMembers: true,
        mouseDragEnter: (e: any, group: any) => {
          const diagram = group.diagram
          diagram.isModified = true
          const shape = group.findObject('GROUPSHAPE')
          if (shape) {
            shape.fill = 'rgba(255, 255, 0, 0.2)'
            shape.stroke = '#FFEB3B'
          }
        },
        mouseDragLeave: (e: any, group: any) => {
          const shape = group.findObject('GROUPSHAPE')
          if (shape) {
            shape.fill = 'rgba(173, 216, 230, 0.1)'
            shape.stroke = '#87CEEB'
          }
        },
        selectable: false,
        avoidable: false
      },
      // 分组边框
      $(go.Shape, 'RoundedRectangle', {
        name: 'GROUPSHAPE',
        fill: 'rgba(173, 216, 230, 0.1)', // 浅蓝色半透明背景
        stroke: '#87CEEB', // 天蓝色边框
        strokeWidth: 2,
        strokeDashArray: [5, 5], // 虚线边框
        parameter1: 10 // 圆角半径
      }),
      // 分组标题
      $(go.Panel, 'Vertical',
        // 标题栏
        $(go.Panel, 'Horizontal',
          {
            alignment: go.Spot.TopLeft,
            alignmentFocus: go.Spot.TopLeft
          },
          $(go.TextBlock, {
            font: 'bold 12px "Segoe UI", Arial, sans-serif',
            stroke: '#2E86AB',
            margin: new go.Margin(8, 8, 4, 8),
            maxSize: new go.Size(200, NaN),
            wrap: go.Wrap.Fit
          },
            new go.Binding('text', '', (data: any) => {
              if (data.parentCaseId) {
                return `子流程 (${data.parentCaseId})`
              }
              return `子流程 Level ${data.subprocessLevel || 1}`
            }))
        ),
        // 分组内容区域
        $(go.Placeholder, {
          padding: new go.Margin(10, 10, 10, 10),
          alignment: go.Spot.TopLeft
        })
      )
    )

    // 定义节点模板 - 使用专业的流程挖掘样式
    myDiagram.nodeTemplate = $(go.Node, 'Auto',
      {
        locationSpot: go.Spot.Center,
        selectable: false, // 禁用节点选择
        shadowVisible: true,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowOffset: new go.Point(2, 2),
        shadowBlur: 4
      },
      new go.Binding('location', 'loc', go.Point.parse).makeTwoWay(go.Point.stringify),

      // 节点形状 - 使用数据驱动的动态样式，开始和结束节点使用不同形状
      $(go.Shape, {
        name: 'BODY',
        fill: '#4CAF50',
        stroke: '#388E3C',
        strokeWidth: 2,
        minSize: new go.Size(80, 40),
        parameter1: 8 // 圆角半径
      },
        // 动态形状绑定 - 开始和结束节点使用大圆角矩形
        new go.Binding('figure', '', () => {
          return 'RoundedRectangle' // 所有节点都使用圆角矩形
        }),
        // 动态圆角半径绑定 - 开始和结束节点使用更大的圆角
        new go.Binding('parameter1', '', (data: any) => {
          if (data.isStartNode || data.isEndNode) {
            return 22 // 开始和结束节点使用大圆角半径，约为高度的一半，形成明显的大圆角效果
          }
          return 8 // 普通节点使用标准圆角
        }),
        // 动态样式绑定
        new go.Binding('fill', '', (data: any) => {
          if (!data.nodeStyle) return '#4CAF50'
          return data.nodeStyle.fill
        }),
        new go.Binding('stroke', '', (data: any) => {
          if (!data.nodeStyle) return '#388E3C'
          return data.nodeStyle.stroke
        }),
        new go.Binding('strokeWidth', '', (data: any) => {
          if (!data.nodeStyle) return 2
          return data.nodeStyle.strokeWidth
        }),
        // 动态尺寸绑定 - 开始和结束节点使用固定的大圆角矩形尺寸
        new go.Binding('width', '', (data: any) => {
          if (data.isStartNode || data.isEndNode) {
            return 80 // 开始和结束节点使用固定宽度
          }
          if (!data.nodeStyle) return 80
          // 🔥 确保宽度值是有效数值，防止NaN导致的GoJS错误
          const width = data.nodeStyle.width
          return Number.isFinite(width) && width > 0 ? Math.max(60, Math.min(200, width)) : 80
        }),
        new go.Binding('height', '', (data: any) => {
          if (data.isStartNode || data.isEndNode) {
            return 50 // 开始和结束节点使用固定高度
          }
          if (!data.nodeStyle) return 40
          // 🔥 确保高度值是有效数值，防止NaN导致的GoJS错误
          const height = data.nodeStyle.height
          return Number.isFinite(height) && height > 0 ? Math.max(30, Math.min(100, height)) : 40
        })),

      // 节点文本 - 改进的排版和样式，开始/结束节点使用不同布局
      $(go.Panel, 'Vertical',
        // 主标签
        $(go.TextBlock, {
          font: 'bold 13px "Segoe UI", Arial, sans-serif',
          margin: new go.Margin(6, 8, 2, 8),
          maxSize: new go.Size(140, NaN),
          wrap: go.Wrap.Fit,
          editable: false,
          textAlign: 'center',
          stroke: '#FFFFFF'
        },
          new go.Binding('text', '', (data: any) => {
            // 开始和结束节点显示简化文本
            if (data.isStartNode) return '开始'
            if (data.isEndNode) return '结束'
            return data.label
          }),
          new go.Binding('stroke', '', (data: any) => {
            if (!data.nodeStyle) return '#FFFFFF'
            return data.nodeStyle.textColor
          }),
          // 开始和结束节点使用更小的字体
          new go.Binding('font', '', (data: any) => {
            if (data.isStartNode || data.isEndNode) {
              return 'bold 11px "Segoe UI", Arial, sans-serif'
            }
            return 'bold 13px "Segoe UI", Arial, sans-serif'
          }),
          // 开始和结束节点使用垂直居中的边距
          new go.Binding('margin', '', (data: any) => {
            if (data.isStartNode || data.isEndNode) {
              return new go.Margin(18, 4, 18, 4) // 上下边距相等，实现垂直居中
            }
            return new go.Margin(6, 8, 2, 8)
          })),

        // 数值标签（频率或耗时）- 开始和结束节点不显示数值
        $(go.TextBlock, {
          font: '10px "Segoe UI", Arial, sans-serif',
          margin: new go.Margin(0, 8, 6, 8),
          stroke: 'rgba(255, 255, 255, 0.9)',
          textAlign: 'center'
        },
          new go.Binding('text', '', (data: any) => {
            // 开始和结束节点不显示数值标签
            if (data.isStartNode || data.isEndNode) return ''

            if (data.dimension === 'duration') {
              return formatDuration(data.avgDuration || 0)
            } else {
              return `${data.frequency || 0}`
            }
          }),
          new go.Binding('stroke', '', (data: any) => {
            if (!data.nodeStyle) return 'rgba(255, 255, 255, 0.9)'
            return data.nodeStyle.textColor === '#FFFFFF' ? 'rgba(255, 255, 255, 0.8)' : 'rgba(46, 94, 32, 0.8)'
          }),
          // 开始和结束节点使用更小的边距
          new go.Binding('margin', '', (data: any) => {
            if (data.isStartNode || data.isEndNode) {
              return new go.Margin(0, 4, 4, 4)
            }
            return new go.Margin(0, 8, 6, 8)
          }))
      )
    )

    // 定义连接线模板 - 使用正交路由的专业流程挖掘样式
    console.log('Setting up link template...')
    myDiagram.linkTemplate = $(go.Link, {
      routing: go.Routing.Orthogonal, // 使用正交路由（直角折线）
      curve: go.Curve.JumpOver, // 线条交叉时跳跃效果
      corner: 6, // 设置拐角圆角半径
      selectable: false, // 禁用连线选择
      cursor: 'pointer',
      shadowVisible: true,
      shadowColor: 'rgba(0, 0, 0, 0.2)',
      shadowOffset: new go.Point(1, 1),
      shadowBlur: 2,
      // 设置连线与节点的连接 - 保持适当距离
      fromSpot: go.Spot.AllSides,
      toSpot: go.Spot.AllSides,
      fromEndSegmentLength: 15, // 起点与节点保持15px距离，避免拐角紧贴节点
      toEndSegmentLength: 15    // 终点与节点保持15px距离，确保箭头清晰可见
    },
      // 连接线形状 - 使用数据驱动的动态样式
      $(go.Shape, {
        name: 'LINKSHAPE',
        strokeWidth: 4,
        stroke: '#2196F3',
        strokeCap: 'round'
      },
        // 动态样式绑定
        new go.Binding('stroke', '', (data: any) => {
          if (!data.linkStyle) return '#2196F3'
          return data.linkStyle.stroke || '#2196F3'
        }),
        new go.Binding('strokeWidth', '', (data: any) => {
          if (!data.linkStyle) return 4
          // 🔥 确保线宽值是有效数值，防止NaN导致的GoJS错误
          const strokeWidth = data.linkStyle.strokeWidth
          return Number.isFinite(strokeWidth) && strokeWidth > 0 ? Math.max(1, Math.min(15, strokeWidth)) : 4
        })),

      // 箭头 - 使用默认定位以确保在正交路由中正确显示
      $(go.Shape, {
        name: 'ARROWSHAPE',
        toArrow: 'Standard',
        fill: '#2196F3',
        stroke: '#2196F3',
        strokeWidth: 1,
        scale: 1.6
        // 移除segmentIndex等属性，让GoJS自动处理箭头位置
      },
        new go.Binding('fill', '', (data: any) => {
          if (!data.linkStyle) return '#2196F3'
          return data.linkStyle.stroke
        }),
        new go.Binding('stroke', '', (data: any) => {
          if (!data.linkStyle) return '#2196F3'
          return data.linkStyle.stroke
        }),
        new go.Binding('scale', '', (data: any) => {
          if (!data.linkStyle) return 1.6
          // 🔥 确保箭头缩放值是有效数值，防止NaN导致的GoJS错误
          const arrowScale = data.linkStyle.arrowScale
          return Number.isFinite(arrowScale) && arrowScale > 0 ? Math.max(0.5, Math.min(3.0, arrowScale)) : 1.6
        })),

      // 连接线标签 - 改进的样式
      $(go.Panel, 'Auto',
        $(go.Shape, 'RoundedRectangle', {
          fill: 'rgba(255, 255, 255, 0.95)',
          stroke: 'rgba(0, 0, 0, 0.1)',
          strokeWidth: 1,
          parameter1: 4
        }),
        $(go.TextBlock, {
          font: 'bold 10px "Segoe UI", Arial, sans-serif',
          margin: new go.Margin(3, 6, 3, 6),
          stroke: '#333',
          textAlign: 'center'
        },
          new go.Binding('text', '', (data: any) => {
            if (data.dimension === 'duration') {
              return formatDuration(data.avgDuration || 0)
            } else {
              return data.frequency ? data.frequency.toString() : '0'
            }
          }),
          new go.Binding('stroke', '', (data: any) => {
            if (!data.linkStyle) return '#333'
            // 根据连接线颜色调整文本颜色以确保可读性
            const linkColor = data.linkStyle.stroke
            if (linkColor === '#D32F2F' || linkColor === '#F44336') return '#D32F2F'
            if (linkColor === '#FF9800') return '#E65100'
            if (linkColor === '#4CAF50') return '#2E7D32'
            return '#1976D2'
          }))
      )
    )
    console.log('Link template set')

    // 添加节点和连线双击事件（详情展示）
    myDiagram.addDiagramListener('ObjectDoubleClicked', (e: go.DiagramEvent) => {
      const part = e.subject.part
      if (part instanceof go.Node) {
        const nodeData = part.data

        // 检查是否为分组节点
        if (nodeData.isGroup) {
          // 分组节点双击：展开/折叠分组或显示分组信息
          console.log('Double-clicked on group:', nodeData.text || nodeData.key)
          // 可以在这里添加分组特定的交互逻辑
          return
        }

        // 普通节点：打开节点详细信息浮层
        selectedNodeId.value = nodeData.key || nodeData.id || nodeData.label
        selectedSourceId.value = ''
        selectedTargetId.value = ''
        detailDialogVisible.value = true
      } else if (part instanceof go.Link) {
        const linkData = part.data
        const fromNode = part.fromNode?.data
        const toNode = part.toNode?.data
        // 打开连接详细信息浮层
        selectedNodeId.value = ''
        selectedSourceId.value = fromNode?.key || fromNode?.id || fromNode?.label || linkData.from
        selectedTargetId.value = toNode?.key || toNode?.id || toNode?.label || linkData.to
        detailDialogVisible.value = true
      }
    })

    // 添加节点单击事件（高亮功能）
    myDiagram.addDiagramListener('ObjectSingleClicked', (e: go.DiagramEvent) => {
      const part = e.subject.part
      if (part instanceof go.Node) {
        const nodeData = part.data
        const nodeKey = nodeData.key || nodeData.id || nodeData.label

        // 检查是否为分组节点
        if (nodeData.isGroup) {
          // 分组节点单击：高亮整个分组
          console.log('Single-clicked on group:', nodeData.text || nodeKey)
          if (highlightedNodeKey.value === nodeKey) {
            clearHighlight()
          } else {
            highlightGroup(nodeKey)
          }
          return
        }

        // 普通节点的高亮逻辑
        if (highlightedNodeKey.value === nodeKey) {
          // 如果点击的是已高亮的节点，则取消高亮
          clearHighlight()
        } else {
          // 高亮新选中的节点
          highlightNode(nodeKey)
        }
      }
    })

    // 添加背景点击事件（取消高亮）
    myDiagram.addDiagramListener('BackgroundSingleClicked', () => {
      clearHighlight()
    })

    // 添加工具提示
    const tooltip = $(go.Adornment, 'Auto',
      $(go.Shape, { fill: '#FFFFCC', stroke: '#666', strokeWidth: 1 }),
      $(go.TextBlock, {
        margin: 4,
        font: '12px sans-serif'
      },
        new go.Binding('text', '', (data: any) => {
          if (data.dimension === 'duration') {
            return `活动: ${data.label}\n频率: ${data.frequency}\n平均耗时: ${formatDuration(data.avgDuration || 0)}`
          } else {
            return `活动: ${data.label}\n频率: ${data.frequency}`
          }
        }))
    )

    myDiagram.nodeTemplate.toolTip = tooltip

    // 添加键盘快捷键支持
    myDiagram.commandHandler.doKeyDown = function () {
      const e = myDiagram.lastInput
      if (e.control || e.meta) {
        switch (e.key) {
          case '+':
          case '=':
            myDiagram.commandHandler.increaseZoom()
            return
          case '-':
            myDiagram.commandHandler.decreaseZoom()
            return
          case '0':
            myDiagram.scale = 1.0
            myDiagram.centerRect(myDiagram.documentBounds)
            return
        }
      }
      // 调用原始方法处理其他键盘事件
      go.CommandHandler.prototype.doKeyDown.call(this)
    }

    // 添加窗口大小变化监听器
    const resizeObserver = new ResizeObserver(() => {
      if (myDiagram && dfgContainer.value) {
        myDiagram.requestUpdate()
        // 延迟调整以确保容器大小已更新
        setTimeout(() => {
          myDiagram.commandHandler.zoomToFit()
        }, 100)
      }
    })

    if (dfgContainer.value) {
      resizeObserver.observe(dfgContainer.value)
    }

    return myDiagram
  } catch (error) {
    console.error('Failed to initialize GoJS diagram:', error)
    return null
  }
}

// 开始流程发现
const api = useApi()
const startDiscovery = async (forceRefresh = false) => {
  isDiscovering.value = true
  isFromCache.value = false

  try {
    const result = await api.discoverProcess(processId, { forceRefresh })

    // 检查结果是否来自缓存（通过检查时间戳判断）
    if (result && result.timestamp) {
      const resultTime = new Date(result.timestamp).getTime()
      const now = Date.now()
      // 如果结果时间戳很新（5秒内），可能是刚计算的；否则可能来自缓存
      isFromCache.value = !forceRefresh && (now - resultTime > 5000)
    }

    if (result && typeof result === 'object') {
      const processedResult = {
        ...result,
        timestamp: result.timestamp || new Date().toISOString()
      }

      // 保存原始数据和当前显示数据
      originalDiscoveryResult.value = processedResult
      discoveryResult.value = processedResult

      const message = isFromCache.value ? '从缓存加载完成！' : '流程发现完成！'
      ElMessage.success(message)

      // 渲染DFG图 - 等待多个 tick 确保 DOM 完全更新
      await nextTick()
      await nextTick()
      setTimeout(() => {
        renderDFG()
      }, 50)
    } else {
      throw new Error('无效的分析结果')
    }
  } catch (error: any) {
    console.error('Discovery error:', error)
    ElMessage.error(error.message || error.data?.message || '流程发现失败')
  } finally {
    isDiscovering.value = false
  }
}

// 渲染DFG图
const renderDFG = async () => {
  const startTime = performance.now()
  // console.log('🚀 renderDFG started')
  // console.log('renderDFG called, dfgContainer.value:', dfgContainer.value)
  // console.log('discoveryResult.value:', discoveryResult.value)

  // 步骤1: 检查容器
  let stepTime = performance.now()
  if (!dfgContainer.value) {
    console.error('dfgContainer is null, waiting for container...')
    const containerReady = await waitForContainer()
    if (!containerReady) {
      console.error('Failed to get container after waiting')
      const endTime = performance.now()
      console.log(`❌ renderDFG execution time: ${(endTime - startTime).toFixed(2)}ms (failed - no container)`)
      return
    }
  }
  // console.log(`✅ Step 1 - Container check: ${(performance.now() - stepTime).toFixed(2)}ms`)

  // 步骤2: 检查发现结果
  stepTime = performance.now()
  if (!discoveryResult.value) {
    console.error('discoveryResult is null')
    const endTime = performance.now()
    console.log(`❌ renderDFG execution time: ${(endTime - startTime).toFixed(2)}ms (failed - no discovery result)`)
    return
  }

  // 🔥 步骤2.5: 数据安全验证，防止GoJS NaN错误
  if (!validateDiscoveryResult(discoveryResult.value)) {
    console.error('Discovery result data validation failed')
    const endTime = performance.now()
    // console.log(`❌ renderDFG execution time: ${(endTime - startTime).toFixed(2)}ms (failed - data validation)`)
    return
  }
  // console.log(`✅ Step 2 - Discovery result check and validation: ${(performance.now() - stepTime).toFixed(2)}ms`)

  // 步骤3: 数据提取和验证
  stepTime = performance.now()
  const nodes = (discoveryResult.value.nodes && Array.isArray(discoveryResult.value.nodes)) ? discoveryResult.value.nodes : []
  const edges = (discoveryResult.value.edges && Array.isArray(discoveryResult.value.edges)) ? discoveryResult.value.edges : []
  // console.log(`✅ Step 3 - Data extraction: ${(performance.now() - stepTime).toFixed(2)}ms (nodes: ${nodes.length}, edges: ${edges.length})`)

  if (nodes.length === 0) {
    // 清理现有图表
    if (diagram) {
      diagram.div = null
      diagram = null
    }
    dfgContainer.value!.innerHTML = `
      <div class="dfg-empty-state">
        <div class="dfg-empty-content">
          <div class="dfg-empty-title">暂无流程数据</div>
          <div class="dfg-empty-text">活动节点过滤后没有找到匹配的案例数据</div>
        </div>
      </div>
    `
    const endTime = performance.now()
    console.log(`❌ renderDFG execution time: ${(endTime - startTime).toFixed(2)}ms (empty data)`)
    return
  }

  // 步骤4: 清理现有图表
  stepTime = performance.now()
  if (diagram) {
    diagram.div = null
    diagram = null
  }
  dfgContainer.value!.innerHTML = ''
  // console.log(`✅ Step 4 - Cleanup existing diagram: ${(performance.now() - stepTime).toFixed(2)}ms`)

  // 步骤5: 初始化新图表
  stepTime = performance.now()
  diagram = initDiagram()
  if (!diagram) {
    console.error('Failed to initialize diagram')
    const endTime = performance.now()
    // console.log(`❌ renderDFG execution time: ${(endTime - startTime).toFixed(2)}ms (failed - diagram initialization)`)
    return
  }
  // console.log(`✅ Step 5 - Initialize diagram: ${(performance.now() - stepTime).toFixed(2)}ms`)

  // console.log('Diagram initialized successfully', diagram)
  // 绑定缩放变化监听，更新 zoom 指示
  try {
    if (diagram) {
      diagram.addDiagramListener('ViewportBoundsChanged', () => {
        zoomPercent.value = Math.round(diagram!.scale * 100)
      })
      // 初始化时设置一次，确保初次显示
      zoomPercent.value = Math.round(diagram.scale * 100)
    }
  } catch (error) {
    console.warn('Failed to attach zoom listener:', error)
  }

  // 步骤6: 计算数据分布
  stepTime = performance.now()
  const distribution = calculateDataDistribution(nodes, edges, currentDimension.value)
  // console.log(`✅ Step 6 - Calculate data distribution: ${(performance.now() - stepTime).toFixed(2)}ms`)
  // console.log('Data distribution:', distribution, 'Dimension:', currentDimension.value)

  // 步骤7: 准备节点数据
  stepTime = performance.now()
  const allNodeDataArray = nodes.map((node: any) => {
    // 🔥 数据验证和清理 - 防止NaN值导致的GoJS错误
    const safeFrequency = Number.isFinite(node.frequency) ? node.frequency : 0
    const safeAvgDuration = Number.isFinite(node.avgDuration) ? node.avgDuration : 0
    const safeMinDuration = Number.isFinite(node.minDuration) ? node.minDuration : 0
    const safeMaxDuration = Number.isFinite(node.maxDuration) ? node.maxDuration : 0

    const value = currentDimension.value === 'frequency'
      ? safeFrequency
      : (safeAvgDuration / 60000) // 转换为分钟

    // 确保value是有效数值
    const safeValue = Number.isFinite(value) ? Math.max(0, value) : 0

    // 检查是否为开始或结束节点（包括子流程的开始和结束节点）
    const nodeId = String(node.id || '')
    const isStartNode = node.isStartNode || nodeId === '开始' || nodeId.startsWith('开始_')
    const isEndNode = node.isEndNode || nodeId === '结束' || nodeId.startsWith('结束_')

    let nodeStyle
    if (isStartNode) {
      // 开始节点特殊样式 - 使用固定安全的尺寸
      nodeStyle = {
        fill: '#4CAF50',
        stroke: '#2E7D32',
        strokeWidth: 3,
        textColor: '#FFFFFF',
        width: 80,
        height: 50
      }
    } else if (isEndNode) {
      // 结束节点特殊样式 - 使用固定安全的尺寸
      nodeStyle = {
        fill: '#F44336',
        stroke: '#C62828',
        strokeWidth: 3,
        textColor: '#FFFFFF',
        width: 80,
        height: 50
      }
    } else {
      // 普通活动节点使用数据驱动样式
      nodeStyle = getNodeStyle(safeValue, distribution.nodePercentiles, currentDimension.value)

      // 🔥 确保nodeStyle中的所有数值都是有效的
      nodeStyle = {
        ...nodeStyle,
        width: Number.isFinite(nodeStyle.width) ? Math.max(80, nodeStyle.width) : 80,
        height: Number.isFinite(nodeStyle.height) ? Math.max(40, nodeStyle.height) : 40,
        strokeWidth: Number.isFinite(nodeStyle.strokeWidth) ? Math.max(1, nodeStyle.strokeWidth) : 2
      }
    }

    return {
      key: nodeId,
      label: String(node.label || nodeId),
      frequency: safeFrequency,
      avgDuration: safeAvgDuration,
      minDuration: safeMinDuration,
      maxDuration: safeMaxDuration,
      currentValue: safeValue,
      dimension: currentDimension.value,
      nodeStyle,
      isStartNode,
      isEndNode,
      // 层次结构相关字段
      group: node.groupId, // GoJS分组ID
      isSubprocessNode: Boolean(node.isSubprocessNode),
      subprocessLevel: Number.isFinite(node.subprocessLevel) ? node.subprocessLevel : 0,
      parentCaseId: node.parentCaseId,
      // 保持向后兼容
      color: nodeStyle.fill,
      borderColor: nodeStyle.stroke
    }
  })

  // 🔥 新增：过滤掉子流程组内的开始和结束节点
  const nodeDataArray = allNodeDataArray.filter((node: any) => {
    // 保留主流程的开始和结束节点（没有group属性或group为空）
    if (!node.group) {
      return true
    }

    // 对于有group属性的节点（位于子流程组内），过滤掉开始和结束节点
    if (node.isStartNode || node.isEndNode) {
      console.log(`🗑️ 移除子流程组内的${node.isStartNode ? '开始' : '结束'}节点: ${node.key} (组: ${node.group})`)
      return false
    }

    // 保留子流程组内的其他业务活动节点
    return true
  })

  const removedNodesCount = allNodeDataArray.length - nodeDataArray.length
  // console.log(`✅ Step 7 - Prepare node data: ${(performance.now() - stepTime).toFixed(2)}ms (${nodeDataArray.length} nodes, 移除了 ${removedNodesCount} 个子流程组内的开始/结束节点)`)

  // 步骤8: 准备连接线数据
  stepTime = performance.now()

  // 获取保留的节点ID集合，用于过滤连接线
  const retainedNodeIds = new Set(nodeDataArray.map((node: any) => node.key))

  const allLinkDataArray = edges.map((edge: any) => {
    // 🔥 数据验证和清理 - 防止NaN值导致的GoJS错误
    const safeFrequency = Number.isFinite(edge.frequency) ? edge.frequency : 0
    const safeAvgDuration = Number.isFinite(edge.avgDuration) ? edge.avgDuration : 0
    const safeMinDuration = Number.isFinite(edge.minDuration) ? edge.minDuration : 0
    const safeMaxDuration = Number.isFinite(edge.maxDuration) ? edge.maxDuration : 0

    const value = currentDimension.value === 'frequency'
      ? safeFrequency
      : (safeAvgDuration / 60000) // 转换为分钟

    // 确保value是有效数值
    const safeValue = Number.isFinite(value) ? Math.max(0, value) : 0

    const linkStyle = getLinkStyle(safeValue, distribution.edgePercentiles, currentDimension.value)

    // 🔥 确保linkStyle中的所有数值都是有效的
    const safeLinkStyle = {
      ...linkStyle,
      strokeWidth: Number.isFinite(linkStyle.strokeWidth) ? Math.max(1, linkStyle.strokeWidth) : 2,
      arrowScale: Number.isFinite(linkStyle.arrowScale) ? Math.max(0.5, linkStyle.arrowScale) : 1.0
    }

    return {
      from: String(edge.from || edge.source || ''),  // 支持 from 或 source 字段，确保是字符串
      to: String(edge.to || edge.target || ''),      // 支持 to 或 target 字段，确保是字符串
      frequency: safeFrequency,
      avgDuration: safeAvgDuration,
      minDuration: safeMinDuration,
      maxDuration: safeMaxDuration,
      currentValue: safeValue,
      dimension: currentDimension.value,
      linkStyle: safeLinkStyle
    }
  })

  // 🔥 新增：过滤掉与被删除节点相关的连接线和冗余的跨层级连接
  const linkDataArray = allLinkDataArray.filter((link: any) => {
    const fromExists = retainedNodeIds.has(link.from)
    const toExists = retainedNodeIds.has(link.to)

    // 基本存在性检查
    if (!fromExists || !toExists) {
      // console.log(`🗑️ 移除连接线: ${link.from} -> ${link.to} (源节点存在: ${fromExists}, 目标节点存在: ${toExists})`)
      return false
    }

    // 🔥 跨层级连接优化1：避免从主流程开始节点直接连接到子流程节点
    const isFromStartNode = link.from === '开始' || link.from.startsWith('开始_')
    const toNode = nodeDataArray.find((node: any) => node.key === link.to)
    const isToSubprocessNode = toNode && toNode.group // 有group属性表示是子流程节点

    if (isFromStartNode && isToSubprocessNode) {
      // 检查是否存在从开始节点到主流程业务活动的连接
      const hasMainProcessConnection = allLinkDataArray.some((otherLink: any) =>
        otherLink.from === link.from &&
        retainedNodeIds.has(otherLink.to) &&
        !nodeDataArray.find((node: any) => node.key === otherLink.to)?.group // 目标是主流程节点（无group）
      )

      if (hasMainProcessConnection) {
        // console.log(`🗑️ 移除冗余跨层级连接: ${link.from} -> ${link.to} (已存在到主流程的连接)`)
        return false
      }
    }

    // 🔥 跨层级连接优化2：避免子流程节点直接连接到主流程结束节点
    const isToEndNode = link.to === '结束' || link.to.startsWith('结束_')
    const fromNode = nodeDataArray.find((node: any) => node.key === link.from)
    const isFromSubprocessNode = fromNode && fromNode.group // 有group属性表示是子流程节点

    if (isFromSubprocessNode && isToEndNode) {
      // 检查是否存在从该子流程节点到主流程业务活动的连接
      const hasMainProcessBusinessConnection = allLinkDataArray.some((otherLink: any) =>
        otherLink.from === link.from &&
        retainedNodeIds.has(otherLink.to) &&
        !nodeDataArray.find((node: any) => node.key === otherLink.to)?.group && // 目标是主流程节点（无group）
        otherLink.to !== '结束' && !otherLink.to.startsWith('结束_') // 目标不是结束节点
      )

      if (hasMainProcessBusinessConnection) {
        // console.log(`🗑️ 移除冗余跨层级连接: ${link.from} -> ${link.to} (子流程节点已连接到主流程业务活动)`)
        return false
      }
    }

    return true
  })

  const removedLinksCount = allLinkDataArray.length - linkDataArray.length
  // console.log(`✅ Step 8 - Prepare link data: ${(performance.now() - stepTime).toFixed(2)}ms (${linkDataArray.length} links, 移除了 ${removedLinksCount} 个与删除节点相关的连接线)`)

  // console.log('Processed linkDataArray:', linkDataArray)

  // 步骤8.5: 准备分组数据（如果存在层次结构）
  stepTime = performance.now()
  const groupDataArray: any[] = []
  if (discoveryResult.value.hierarchicalInfo?.hasHierarchy) {
    const hierarchyMap = discoveryResult.value.hierarchicalInfo.hierarchyMap
    Object.entries(hierarchyMap).forEach(([parentCaseId]) => {
      // 找到该子流程的节点以确定层级
      const subprocessNodes = nodeDataArray.filter(node =>
        node.group && node.group.includes(parentCaseId)
      )

      if (subprocessNodes.length > 0) {
        const level = subprocessNodes[0].subprocessLevel || 1
        const groupId = `subprocess_${parentCaseId}_level_${level}`

        groupDataArray.push({
          key: groupId,
          text: `子流程 (${parentCaseId})`,
          isGroup: true,
          parentCaseId,
          subprocessLevel: level,
          // 分组样式
          color: 'rgba(173, 216, 230, 0.1)',
          borderColor: '#87CEEB'
        })
      }
    })
  }
  // console.log(`✅ Step 8.5 - Prepare group data: ${(performance.now() - stepTime).toFixed(2)}ms (${groupDataArray.length} groups)`)

  // console.log('Setting diagram data:')
  // console.log('Nodes:', nodeDataArray)
  // console.log('Links:', linkDataArray)
  // console.log('Groups:', groupDataArray)
  // console.log('Original edges:', edges)

  // 步骤9: 创建图表模型
  stepTime = performance.now()
  // console.log('Creating GraphLinksModel...')
  const model = new go.GraphLinksModel(nodeDataArray, linkDataArray)

  // 如果有分组数据，设置分组
  if (groupDataArray.length > 0) {
    // 为模型添加分组支持
    model.nodeGroupKeyProperty = 'group'
    model.nodeIsGroupProperty = 'isGroup'

    // 将分组数据添加到节点数据中
    groupDataArray.forEach(group => {
      model.addNodeData(group)
    })
  }
  // console.log(`✅ Step 9 - Create GraphLinksModel: ${(performance.now() - stepTime).toFixed(2)}ms`)
  // console.log('Model created:', model)
  // console.log('Model nodeDataArray:', model.nodeDataArray)
  // console.log('Model linkDataArray:', model.linkDataArray)

  // 步骤10: 设置图表数据
  stepTime = performance.now()
  const modelAssignmentSuccess = safeGoJSOperation(() => {
    diagram.model = model
    // console.log(`✅ Step 10 - Set diagram model: ${(performance.now() - stepTime).toFixed(2)}ms`)
    // console.log('Model assigned to diagram')

    // 验证图表状态
    // console.log('Diagram nodes count:', diagram.nodes.count)
    // console.log('Diagram links count:', diagram.links.count)

    // console.log('Diagram model set, performing layout...')

    // 步骤11: 自动布局
    stepTime = performance.now()
    diagram.layoutDiagram(true)
    // console.log(`✅ Step 11 - Layout diagram: ${(performance.now() - stepTime).toFixed(2)}ms`)
    return true
  }, false, 'Diagram model assignment and layout')

  if (!modelAssignmentSuccess) {
    // 尝试创建一个简化的安全模型
    const safeModel = new go.GraphLinksModel([], [])
    diagram.model = safeModel
    const endTime = performance.now()
    // console.log(`❌ renderDFG execution time: ${(endTime - startTime).toFixed(2)}ms (failed - model assignment error)`)
    return
  }

  // 步骤12: 对齐开始和结束节点
  stepTime = performance.now()
  safeGoJSOperation(() => {
    alignStartEndNodes(diagram)
  }, undefined, 'Align start/end nodes')
  // console.log(`✅ Step 12 - Align start/end nodes: ${(performance.now() - stepTime).toFixed(2)}ms`)

  // 步骤12.5: 优化分组布局（如果存在层次结构）
  stepTime = performance.now()
  if (discoveryResult.value.hierarchicalInfo?.hasHierarchy) {
    safeGoJSOperation(() => {
      optimizeGroupLayout(diagram)
    }, undefined, 'Optimize group layout')
  }
  // console.log(`✅ Step 12.5 - Optimize group layout: ${(performance.now() - stepTime).toFixed(2)}ms`)

  // 步骤13: 强制刷新图表
  stepTime = performance.now()
  console.log('Forcing diagram update...')
  safeGoJSOperation(() => {
    diagram.requestUpdate()
    diagram.invalidateDocumentBounds()
  }, undefined, 'Force diagram update')
  // console.log(`✅ Step 13 - Force diagram update: ${(performance.now() - stepTime).toFixed(2)}ms`)

  // 步骤14: 最终渲染和适应容器
  setTimeout(() => {
    if (diagram) {
      const finalStepTime = performance.now()
      console.log('Final update and fitting to screen...')

      safeGoJSOperation(() => {
        diagram.requestUpdate()
        diagram.commandHandler.zoomToFit()
      }, undefined, 'Final diagram update and zoom fit')

      // 再次检查链接
      // console.log('Final check - Links count:', diagram.links.count)

      // console.log(`✅ Step 14 - Final rendering and zoom fit: ${(performance.now() - finalStepTime).toFixed(2)}ms`)

      // 记录总执行时长
      const endTime = performance.now()
      const totalTime = endTime - startTime
      // console.log(`🎉 renderDFG COMPLETED - Total execution time: ${totalTime.toFixed(2)}ms`)

      // 性能分析总结
      // console.log('📊 Performance Summary:')
      // console.log(`   • Total time: ${totalTime.toFixed(2)}ms`)
      // console.log(`   • Nodes processed: ${nodeDataArray.length}`)
      // console.log(`   • Links processed: ${linkDataArray.length}`)
      // console.log(`   • Average time per node: ${(totalTime / nodeDataArray.length).toFixed(2)}ms`)
      // console.log(`   • Average time per link: ${(totalTime / linkDataArray.length).toFixed(2)}ms`)
    }
  }, 200)
}

// 缩放控制
const zoomIn = () => {
  if (diagram && diagram.div) {
    console.log('Zooming in, current scale:', diagram.scale)
    diagram.commandHandler.increaseZoom()
    console.log('New scale after zoom in:', diagram.scale)
    zoomPercent.value = Math.round(diagram.scale * 100)
  } else {
    console.warn('Diagram not available for zoom in')
  }
}

const zoomOut = () => {
  if (diagram && diagram.div) {
    console.log('Zooming out, current scale:', diagram.scale)
    diagram.commandHandler.decreaseZoom()
    console.log('New scale after zoom out:', diagram.scale)
    zoomPercent.value = Math.round(diagram.scale * 100)
  } else {
    console.warn('Diagram not available for zoom out')
  }
}

const resetZoom = () => {
  if (diagram && diagram.div) {
    console.log('Resetting zoom, current scale:', diagram.scale)
    diagram.scale = 1.0
    diagram.position = new go.Point(0, 0)
    diagram.centerRect(diagram.documentBounds)
    zoomPercent.value = Math.round(diagram.scale * 100)
    console.log('Zoom reset to scale 1.0')
  } else {
    console.warn('Diagram not available for reset zoom')
  }
}

const fitToScreen = () => {
  if (diagram && diagram.div) {
    console.log('Fitting to screen, current scale:', diagram.scale)
    diagram.commandHandler.zoomToFit()
    console.log('New scale after fit to screen:', diagram.scale)
    zoomPercent.value = Math.round(diagram.scale * 100)
  } else {
    console.warn('Diagram not available for fit to screen')
  }
}

// 导出结果
const exportResult = () => {
  if (!discoveryResult.value) return

  const dataStr = JSON.stringify(discoveryResult.value, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)

  const link = document.createElement('a')
  link.href = url
  link.download = `process-discovery-${processId}-${Date.now()}.json`
  link.click()

  URL.revokeObjectURL(url)
}

// 显示配置对话框
const showConfigDialog = () => {
  configDialogVisible.value = true
}

// 应用配置并重新运行流程发现
const applyDiscoveryConfig = async (config: ProcessDiscoveryOptions) => {
  try {
    // 更新当前配置
    currentDiscoveryConfig.value = { ...config }

    // 使用新配置重新运行流程发现
    isDiscovering.value = true
    isFromCache.value = false

    const result = await api.discoverProcess(processId, config)

    // 处理结果数据，确保包含时间戳
    const processedResult = {
      ...result,
      timestamp: result.timestamp || new Date().toISOString()
    }

    // 检查结果是否来自缓存
    if (processedResult && processedResult.timestamp) {
      const resultTime = new Date(processedResult.timestamp).getTime()
      const now = Date.now()
      isFromCache.value = !config.forceRefresh && (now - resultTime > 5000)
    }

    // 更新原始数据和当前显示数据
    originalDiscoveryResult.value = processedResult
    discoveryResult.value = processedResult

    // 如果有活跃的筛选器，重新应用筛选器
    if (hasActiveFilters.value && currentFilters.value) {
      console.log('Re-applying active filters after config change')
      await onFilterChange(currentFilters.value)
    } else {
      // 重新渲染DFG
      await nextTick()
      setTimeout(() => {
        console.log('🔧 [DEBUG] Calling renderDFG after config apply')
        renderDFG()
      }, 50)
    }

    ElMessage.success('流程发现配置已应用')
  } catch (error) {
    console.error('🔧 [DEBUG] Failed to apply discovery config:', error)
    ElMessage.error('应用配置失败: ' + (error as Error).message)
  } finally {
    isDiscovering.value = false
  }
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 切换选项卡
const switchTab = (tab: 'basic' | 'subprocess') => {
  if (tab === 'subprocess') {
    navigateTo(`/analysis/${processId}/subprocess`)
  } else {
    currentTab.value = tab
  }
}

// 导航到流程比对分析页面
const navigateToCompare = () => {
  navigateTo(`/analysis/${processId}/compare`)
}

// 筛选器事件处理
const onFilterChange = async (filters: any) => {
  console.log('Filter change received:', filters)
  currentFilters.value = filters
  hasActiveFilters.value = checkHasActiveFilters(filters)

  // 在应用筛选前清除高亮状态
  clearHighlight()

  if (originalDiscoveryResult.value) {
    console.log('Original data:', originalDiscoveryResult.value)

    try {
      // 开始节流控制的Loading显示
      startFilterLoading()

      // 使用Web Worker应用筛选器到原始数据
      const filteredResult = await applyFiltersWithWorker(originalDiscoveryResult.value, filters)
      console.log('Filtered result:', filteredResult)
      discoveryResult.value = filteredResult

      // 完成节流控制的Loading隐藏
      completeFilterLoading()

      // 重新渲染DFG图
      nextTick(() => {
        renderDFG()
      })
    } catch (error) {
      console.error('Filter processing failed:', error)
      ElMessage.error(`筛选处理失败: ${error.message}`)
      // 取消Loading显示
      cancelFilterLoading()
    }
  } else {
    console.warn('No original discovery result available for filtering')
    ElMessage.warning('请先进行流程发现，然后再使用筛选功能')
  }
}

const onFilterReset = () => {
  console.log('Filter reset triggered')

  // 取消任何正在进行的Loading显示
  cancelFilterLoading()

  currentFilters.value = null
  hasActiveFilters.value = false

  if (originalDiscoveryResult.value) {
    // 恢复原始数据
    discoveryResult.value = originalDiscoveryResult.value

    // 重新渲染DFG图
    nextTick(() => {
      renderDFG()
    })
  } else {
    ElMessage.warning('请先进行流程发现，然后再使用筛选功能')
  }
}

// 检查是否有活跃的筛选条件
const checkHasActiveFilters = (filters: any): boolean => {
  if (!filters || !filters.groups) return false

  return filters.groups.some((group: any) =>
    group.conditions.some((condition: any) =>
      condition.enabled && hasConditionValue(condition)
    )
  )
}

// 检查条件是否有值
const hasConditionValue = (condition: any): boolean => {
  switch (condition.type) {
    case 'resource':
      return condition.data.resources && condition.data.resources.length > 0
    case 'duration':
      return condition.data.min !== null || condition.data.max !== null
    case 'businessField':
      return condition.data.field !== '' && (
        condition.data.value !== '' ||
        (condition.data.operator === 'range' && condition.data.rangeMin !== '' && condition.data.rangeMax !== '')
      )
    case 'pathway':
      return condition.data.activity !== '' &&
        condition.data.frequency &&
        condition.data.frequency[0] >= 1 &&
        condition.data.frequency[1] >= condition.data.frequency[0]
    default:
      return false
  }
}

// Loading节流控制函数
const clearAllLoadingTimers = () => {
  if (showLoadingTimer) {
    clearTimeout(showLoadingTimer)
    showLoadingTimer = null
  }
  if (hideLoadingTimer) {
    clearTimeout(hideLoadingTimer)
    hideLoadingTimer = null
  }
  if (minDisplayTimer) {
    clearTimeout(minDisplayTimer)
    minDisplayTimer = null
  }
}

// 开始显示Loading（带延迟）
const startFilterLoading = () => {
  console.log('Starting filter loading with throttling...')

  // 清除所有现有的定时器
  clearAllLoadingTimers()

  // 立即设置处理状态
  isFilterProcessing.value = true
  filterProgress.value = 0
  filterStage.value = 'initializing'
  loadingStartTime = Date.now()

  // 延迟显示Loading覆盖层
  showLoadingTimer = setTimeout(() => {
    console.log('Showing loading overlay after delay')
    isFilterLoadingVisible.value = true
    showLoadingTimer = null
  }, SHOW_DELAY)
}

// 完成Loading（带最小显示时间和延迟隐藏）
const completeFilterLoading = () => {
  console.log('Completing filter loading with throttling...')

  const currentTime = Date.now()
  const elapsedTime = currentTime - loadingStartTime

  // 清除显示定时器（如果还没有显示）
  if (showLoadingTimer) {
    clearTimeout(showLoadingTimer)
    showLoadingTimer = null
    // 如果还没有显示过Loading，直接完成
    console.log('Loading never shown, completing immediately')
    isFilterProcessing.value = false
    isFilterLoadingVisible.value = false
    return
  }

  // 如果Loading已经显示，需要考虑最小显示时间
  if (isFilterLoadingVisible.value) {
    const remainingMinTime = Math.max(0, MIN_DISPLAY_TIME - elapsedTime)

    console.log(`Loading visible, elapsed: ${elapsedTime}ms, remaining min time: ${remainingMinTime}ms`)

    // 确保达到最小显示时间
    minDisplayTimer = setTimeout(() => {
      console.log('Min display time reached, starting hide sequence')

      // 设置为完成状态，显示一段时间
      filterProgress.value = 100
      filterStage.value = 'finalizing'

      // 延迟隐藏
      hideLoadingTimer = setTimeout(() => {
        console.log('Hiding loading overlay')
        isFilterProcessing.value = false
        isFilterLoadingVisible.value = false
        clearAllLoadingTimers()
      }, HIDE_DELAY)

      minDisplayTimer = null
    }, remainingMinTime)
  } else {
    // Loading还没有显示，直接完成
    console.log('Loading not visible, completing immediately')
    isFilterProcessing.value = false
    isFilterLoadingVisible.value = false
  }
}

// 取消Loading（用于错误情况或重置）
const cancelFilterLoading = () => {
  console.log('Cancelling filter loading')
  clearAllLoadingTimers()
  isFilterProcessing.value = false
  isFilterLoadingVisible.value = false
}

// 初始化Web Worker
const initializeFilterWorker = () => {
  if (typeof Worker !== 'undefined') {
    try {
      filterWorker = new Worker('/workers/filterWorker.js')

      filterWorker.onmessage = (event) => {
        const { type, payload } = event.data

        switch (type) {
          case 'FILTER_PROGRESS':
            filterProgress.value = payload.progress
            filterStage.value = payload.stage
            // console.log(`Filter progress: ${payload.stage} - ${payload.progress}%`)
            break

          case 'FILTER_COMPLETE':
            // console.log('Filter processing complete:', payload)
            discoveryResult.value = payload
            // 注意：不在这里设置 isFilterProcessing，由节流控制函数管理
            filterProgress.value = 100

            // 重新渲染DFG图
            nextTick(() => {
              renderDFG()
            })
            break

          case 'FILTER_ERROR':
            // console.error('Filter processing error:', payload.error)
            ElMessage.error(`筛选处理失败: ${payload.error}`)
            // 注意：不在这里设置 isFilterProcessing，由节流控制函数管理
            break
        }
      }

      filterWorker.onerror = (error) => {
        console.error('Filter worker error:', error)
        ElMessage.error('筛选处理器初始化失败')
        // 注意：不在这里设置 isFilterProcessing，由节流控制函数管理
      }

      console.log('Filter worker initialized successfully')
    } catch (error) {
      console.error('Failed to initialize filter worker:', error)
      ElMessage.warning('Web Worker不可用，将使用主线程处理筛选')
    }
  } else {
    console.warn('Web Worker not supported, will use main thread for filtering')
  }
}

// 清理Web Worker
const cleanupFilterWorker = () => {
  if (filterWorker) {
    filterWorker.terminate()
    filterWorker = null
    console.log('Filter worker terminated')
  }
}

// 清理所有资源
const cleanup = () => {
  cleanupFilterWorker()
  clearAllLoadingTimers()
}

// 使用Web Worker应用筛选器
const applyFiltersWithWorker = async (originalData: any, filters: any) => {
  // console.log('=== APPLYING FILTERS WITH WORKER ===')

  if (!filters || !originalData || !filters.groups || filters.groups.length === 0) {
    console.log('No filters to apply, returning original data')
    return originalData
  }

  if (!filterWorker) {
    console.warn('Filter worker not available, falling back to main thread')
    return applyFiltersMainThread(originalData, filters)
  }

  return new Promise((resolve, reject) => {
    // 注意：不在这里设置 isFilterProcessing，由节流控制函数管理

    // 设置超时处理
    const timeout = setTimeout(() => {
      reject(new Error('筛选处理超时'))
    }, 30000) // 30秒超时

    // 临时存储原始的消息处理器
    const originalOnMessage = filterWorker!.onmessage

    filterWorker!.onmessage = (event) => {
      const { type, payload } = event.data

      if (type === 'FILTER_COMPLETE') {
        clearTimeout(timeout)
        // 注意：不在这里设置 isFilterProcessing，由节流控制函数管理
        filterWorker!.onmessage = originalOnMessage // 恢复原始处理器
        resolve(payload)
      } else if (type === 'FILTER_ERROR') {
        clearTimeout(timeout)
        // 注意：不在这里设置 isFilterProcessing，由节流控制函数管理
        filterWorker!.onmessage = originalOnMessage // 恢复原始处理器
        reject(new Error(payload.error))
      } else {
        // 其他消息继续使用原始处理器
        originalOnMessage?.(event)
      }
    }

    // 发送筛选任务到Worker
    filterWorker!.postMessage({
      type: 'APPLY_FILTERS',
      payload: {
        originalData: JSON.parse(JSON.stringify(originalData)), // 深拷贝避免引用问题
        filters: JSON.parse(JSON.stringify(filters))
      }
    })
  })
}

// 主线程筛选处理（备用方案）
const applyFiltersMainThread = (originalData: any, filters: any) => {
  // console.log('=== APPLYING FILTERS ON MAIN THREAD ===')
  // console.log('Filters received:', JSON.stringify(filters, null, 2))
  // console.log('Original data structure:', {
  //   nodes: originalData.nodes?.length,
  //   edges: originalData.edges?.length
  // })

  if (!filters || !originalData || !filters.groups || filters.groups.length === 0) {
    console.log('No filters to apply, returning original data')
    return originalData
  }

  // 检查是否有启用的路径筛选条件
  const pathwayConditions = filters.groups
    .flatMap((group: any) => group.conditions)
    .filter((condition: any) => condition.enabled && condition.type === 'pathway')

  // console.log('Found pathway conditions:', pathwayConditions.length)
  pathwayConditions.forEach((condition: any, index: number) => {
    console.log(`Pathway condition ${index + 1}:`, {
      activity: condition.data.activity,
      type: condition.data.type,
      frequency: condition.data.frequency
    })
  })

  let filteredNodes = [...(originalData.nodes || [])]
  let filteredEdges = [...(originalData.edges || [])]

  console.log('Initial counts - Nodes:', filteredNodes.length, 'Edges:', filteredEdges.length)

  // 打印前几条边的数据结构以便调试
  if (filteredEdges.length > 0) {
    console.log('Sample edge data:', filteredEdges.slice(0, 3).map(edge => ({
      source: edge.source,
      target: edge.target,
      from: edge.from,
      to: edge.to,
      frequency: edge.frequency,
      label: edge.label
    })))
  }

  // 应用筛选组
  filters.groups.forEach((group: any, groupIndex: number) => {
    console.log(`Processing group ${groupIndex}:`, group)

    const enabledConditions = group.conditions.filter((condition: any) => condition.enabled)
    if (enabledConditions.length === 0) return

    // 根据组内逻辑处理条件
    if (group.logic === 'AND') {
      // AND 逻辑：所有条件都必须满足
      enabledConditions.forEach((condition: any) => {
        const result = applyCondition(filteredNodes, filteredEdges, condition)
        filteredNodes = result.nodes
        filteredEdges = result.edges
      })
    } else {
      // OR 逻辑：任一条件满足即可
      const originalNodes = [...filteredNodes]
      const originalEdges = [...filteredEdges]
      let combinedNodes: any[] = []
      let combinedEdges: any[] = []

      enabledConditions.forEach((condition: any) => {
        const result = applyCondition(originalNodes, originalEdges, condition)
        // 合并结果，去重
        combinedNodes = [...new Set([...combinedNodes, ...result.nodes])]
        combinedEdges = [...new Set([...combinedEdges, ...result.edges])]
      })

      filteredNodes = combinedNodes
      filteredEdges = combinedEdges
    }
  })

  // 确保边的节点都存在于筛选后的节点中
  const nodeIds = new Set(filteredNodes.map((node: any) => node.id || node.label))
  const originalEdgeCount = filteredEdges.length
  filteredEdges = filteredEdges.filter((edge: any) => {
    const fromId = edge.from || edge.source
    const toId = edge.to || edge.target
    const isValid = nodeIds.has(fromId) && nodeIds.has(toId)
    if (!isValid) {
      console.log(`Removing edge ${fromId} -> ${toId} because nodes not found`)
    }
    return isValid
  })

  console.log(`Edge cleanup: ${originalEdgeCount} -> ${filteredEdges.length} edges`)

  // 移除没有连线的孤立节点（保留开始和结束节点，包括子流程的开始和结束节点）
  const connectedNodeIds = new Set<string>()
  filteredEdges.forEach((edge: any) => {
    const fromId = edge.from || edge.source
    const toId = edge.to || edge.target
    connectedNodeIds.add(fromId)
    connectedNodeIds.add(toId)
  })

  const originalNodeCount = filteredNodes.length
  filteredNodes = filteredNodes.filter((node: any) => {
    const nodeId = node.id || node.label

    // 保留所有开始和结束节点，包括子流程的开始和结束节点
    if (node.isStartNode || node.isEndNode ||
        nodeId === '开始' || nodeId === '结束' ||
        nodeId.startsWith('开始_') || nodeId.startsWith('结束_')) {
      return true
    }

    // 保留有连线的节点
    const isConnected = connectedNodeIds.has(nodeId)
    if (!isConnected) {
      console.log(`Removing isolated node: ${nodeId}`)
    }
    return isConnected
  })

  console.log(`Node cleanup: ${originalNodeCount} -> ${filteredNodes.length} nodes (removed isolated nodes)`)

  // 迭代式移除只有单向连接的节点（保留开始和结束节点）
  console.log('=== ITERATIVE UNIDIRECTIONAL NODE REMOVAL ===')
  let iterationCount = 0
  const totalRemovedNodes: string[] = []

  while (true) {
    iterationCount++
    console.log(`\n--- Iteration ${iterationCount} ---`)

    const nodeConnectionInfo = new Map<string, { incoming: number, outgoing: number }>()

    // 初始化所有节点的连接信息
    filteredNodes.forEach((node: any) => {
      const nodeId = node.id || node.label
      nodeConnectionInfo.set(nodeId, { incoming: 0, outgoing: 0 })
    })

    // 统计每个节点的入边和出边数量
    filteredEdges.forEach((edge: any) => {
      const fromId = edge.from || edge.source
      const toId = edge.to || edge.target

      const fromInfo = nodeConnectionInfo.get(fromId)
      const toInfo = nodeConnectionInfo.get(toId)

      if (fromInfo) fromInfo.outgoing++
      if (toInfo) toInfo.incoming++
    })

    // 打印连接性分析结果
    console.log('Node connectivity analysis:')
    nodeConnectionInfo.forEach((info, nodeId) => {
      const node = filteredNodes.find((n: any) => (n.id || n.label) === nodeId)
      const isSpecialNode = node && (node.isStartNode || node.isEndNode ||
        nodeId === '开始' || nodeId === '结束' ||
        nodeId.startsWith('开始_') || nodeId.startsWith('结束_'))
      console.log(`  ${nodeId}: incoming=${info.incoming}, outgoing=${info.outgoing}${isSpecialNode ? ' (special node)' : ''}`)
    })

    // 识别本轮要移除的单向连接节点
    const currentIterationRemovedNodes: string[] = []
    const beforeCount = filteredNodes.length

    filteredNodes = filteredNodes.filter((node: any) => {
      const nodeId = node.id || node.label

      // 保留所有开始和结束节点，包括子流程的开始和结束节点，即使它们只有单向连接
      if (node.isStartNode || node.isEndNode ||
          nodeId === '开始' || nodeId === '结束' ||
          nodeId.startsWith('开始_') || nodeId.startsWith('结束_')) {
        return true
      }

      const connectionInfo = nodeConnectionInfo.get(nodeId)
      if (!connectionInfo) return false

      // 检查是否为单向连接节点（只有入边或只有出边，但不是两者都有）
      const hasIncoming = connectionInfo.incoming > 0
      const hasOutgoing = connectionInfo.outgoing > 0
      const isBidirectional = hasIncoming && hasOutgoing

      // 只移除那些只有单向连接的节点（死胡同节点）
      if (!isBidirectional && (hasIncoming || hasOutgoing)) {
        currentIterationRemovedNodes.push(nodeId)
        // const direction = hasIncoming ? 'only incoming' : 'only outgoing'
        // console.log(`Removing unidirectional node: ${nodeId} (${direction}, incoming: ${connectionInfo.incoming}, outgoing: ${connectionInfo.outgoing})`)
        return false
      }

      return true
    })

    // 如果本轮没有移除任何节点，则停止迭代
    if (currentIterationRemovedNodes.length === 0) {
      console.log(`No more unidirectional nodes found. Stopping after ${iterationCount} iterations.`)
      break
    }

    // 移除与本轮移除节点相关的边
    const removedNodeSet = new Set(currentIterationRemovedNodes)
    const beforeEdgeCount = filteredEdges.length

    filteredEdges = filteredEdges.filter((edge: any) => {
      const fromId = edge.from || edge.source
      const toId = edge.to || edge.target

      const shouldRemove = removedNodeSet.has(fromId) || removedNodeSet.has(toId)
      if (shouldRemove) {
        console.log(`Removing edge connected to unidirectional node: ${fromId} -> ${toId}`)
      }
      return !shouldRemove
    })

    totalRemovedNodes.push(...currentIterationRemovedNodes)
    console.log(`Iteration ${iterationCount} results: ${beforeCount} -> ${filteredNodes.length} nodes (removed ${currentIterationRemovedNodes.length})`)
    console.log(`Edges: ${beforeEdgeCount} -> ${filteredEdges.length}`)

    // 防止无限循环
    if (iterationCount > 10) {
      console.warn('Maximum iterations reached, stopping unidirectional node removal')
      break
    }
  }

  console.log(`\n=== UNIDIRECTIONAL NODE REMOVAL COMPLETE ===`)
  console.log(`Total iterations: ${iterationCount}`)
  console.log(`Total removed nodes: ${totalRemovedNodes.length}`)
  if (totalRemovedNodes.length > 0) {
    console.log('All removed unidirectional nodes:', totalRemovedNodes)
  }

  // 最终验证：检查剩余节点的连接性
  console.log('\n=== FINAL CONNECTIVITY VERIFICATION ===')
  const finalConnectionInfo = new Map<string, { incoming: number, outgoing: number }>()

  // 重新统计剩余节点的连接信息
  filteredNodes.forEach((node: any) => {
    const nodeId = node.id || node.label
    finalConnectionInfo.set(nodeId, { incoming: 0, outgoing: 0 })
  })

  filteredEdges.forEach((edge: any) => {
    const fromId = edge.from || edge.source
    const toId = edge.to || edge.target

    const fromInfo = finalConnectionInfo.get(fromId)
    const toInfo = finalConnectionInfo.get(toId)

    if (fromInfo) fromInfo.outgoing++
    if (toInfo) toInfo.incoming++
  })

  // 检查是否还有单向连接节点
  const remainingUnidirectionalNodes: string[] = []
  finalConnectionInfo.forEach((info, nodeId) => {
    const node = filteredNodes.find((n: any) => (n.id || n.label) === nodeId)
    const isSpecialNode = node && (node.isStartNode || node.isEndNode ||
      nodeId === '开始' || nodeId === '结束' ||
      nodeId.startsWith('开始_') || nodeId.startsWith('结束_'))

    const hasIncoming = info.incoming > 0
    const hasOutgoing = info.outgoing > 0
    const isBidirectional = hasIncoming && hasOutgoing

    if (!isSpecialNode && !isBidirectional && (hasIncoming || hasOutgoing)) {
      remainingUnidirectionalNodes.push(nodeId)
    }

    console.log(`Final: ${nodeId}: incoming=${info.incoming}, outgoing=${info.outgoing}${isSpecialNode ? ' (special)' : ''}${!isBidirectional && !isSpecialNode && (hasIncoming || hasOutgoing) ? ' (STILL UNIDIRECTIONAL!)' : ''}`)
  })

  if (remainingUnidirectionalNodes.length > 0) {
    console.warn('WARNING: Still found unidirectional nodes after cleanup:', remainingUnidirectionalNodes)
  } else {
    console.log('✓ All non-special nodes are now bidirectional or isolated')
  }

  console.log('Final filtered counts - Nodes:', filteredNodes.length, 'Edges:', filteredEdges.length)

  // 移除与开始节点和结束节点不连通的孤立子图
  console.log('\n=== CONNECTIVITY ANALYSIS AND ISOLATED SUBGRAPH REMOVAL ===')

  // 找到开始节点和结束节点（包括子流程的开始和结束节点）
  const startNodes = filteredNodes.filter((node: any) => {
    const nodeId = node.id || node.label
    return node.isStartNode || nodeId === '开始' || nodeId.startsWith('开始_')
  })
  const endNodes = filteredNodes.filter((node: any) => {
    const nodeId = node.id || node.label
    return node.isEndNode || nodeId === '结束' || nodeId.startsWith('结束_')
  })

  console.log('Start nodes found:', startNodes.map((n: any) => n.id || n.label))
  console.log('End nodes found:', endNodes.map((n: any) => n.id || n.label))

  if (startNodes.length === 0 || endNodes.length === 0) {
    console.warn('No start or end nodes found, skipping connectivity analysis')
  } else {
    // 构建正向和反向邻接表
    const forwardAdjacencyMap = buildForwardAdjacencyMap(filteredEdges)
    const backwardAdjacencyMap = buildBackwardAdjacencyMap(filteredEdges)

    // 从开始节点进行正向遍历，找到所有可达节点
    const reachableFromStart = new Set<string>()
    startNodes.forEach((startNode: any) => {
      const startNodeId = startNode.id || startNode.label
      const reachable = dfsTraversal(startNodeId, forwardAdjacencyMap)
      reachable.forEach(nodeId => reachableFromStart.add(nodeId))
    })

    console.log('Nodes reachable from start:', Array.from(reachableFromStart))

    // 从结束节点进行反向遍历，找到所有可以到达结束节点的节点
    const canReachEnd = new Set<string>()
    endNodes.forEach((endNode: any) => {
      const endNodeId = endNode.id || endNode.label
      const reachable = dfsTraversal(endNodeId, backwardAdjacencyMap)
      reachable.forEach(nodeId => canReachEnd.add(nodeId))
    })

    console.log('Nodes that can reach end:', Array.from(canReachEnd))

    // 找到在主流程路径上的节点（既可以从开始节点到达，又可以到达结束节点）
    const mainPathNodes = new Set<string>()
    reachableFromStart.forEach(nodeId => {
      if (canReachEnd.has(nodeId)) {
        mainPathNodes.add(nodeId)
      }
    })

    console.log('Main path nodes (reachable from start AND can reach end):', Array.from(mainPathNodes))

    // 识别孤立节点（不在主流程路径上的节点）
    const isolatedNodes: string[] = []
    const beforeConnectivityCount = filteredNodes.length

    filteredNodes = filteredNodes.filter((node: any) => {
      const nodeId = node.id || node.label

      if (mainPathNodes.has(nodeId)) {
        return true
      } else {
        isolatedNodes.push(nodeId)
        console.log(`Removing isolated node: ${nodeId} (not in main path)`)
        return false
      }
    })

    // 移除与孤立节点相关的边
    const isolatedNodeSet = new Set(isolatedNodes)
    const beforeIsolatedEdgeCleanup = filteredEdges.length

    filteredEdges = filteredEdges.filter((edge: any) => {
      const fromId = edge.from || edge.source
      const toId = edge.to || edge.target

      const shouldRemove = isolatedNodeSet.has(fromId) || isolatedNodeSet.has(toId)
      if (shouldRemove) {
        console.log(`Removing edge connected to isolated node: ${fromId} -> ${toId}`)
      }
      return !shouldRemove
    })

    console.log(`Connectivity cleanup: ${beforeConnectivityCount} -> ${filteredNodes.length} nodes (removed ${isolatedNodes.length} isolated nodes)`)
    console.log(`Edge cleanup after connectivity removal: ${beforeIsolatedEdgeCleanup} -> ${filteredEdges.length} edges`)
    if (isolatedNodes.length > 0) {
      console.log('Removed isolated nodes:', isolatedNodes)
    }

    // 最终连通性验证
    console.log('\n=== FINAL CONNECTIVITY VERIFICATION ===')
    const finalForwardMap = buildForwardAdjacencyMap(filteredEdges)
    const finalBackwardMap = buildBackwardAdjacencyMap(filteredEdges)

    // 验证所有剩余节点都在主流程路径上
    const finalReachableFromStart = new Set<string>()
    const finalCanReachEnd = new Set<string>()

    startNodes.forEach((startNode: any) => {
      const startNodeId = startNode.id || startNode.label
      if (filteredNodes.some((n: any) => (n.id || n.label) === startNodeId)) {
        const reachable = dfsTraversal(startNodeId, finalForwardMap)
        reachable.forEach(nodeId => finalReachableFromStart.add(nodeId))
      }
    })

    endNodes.forEach((endNode: any) => {
      const endNodeId = endNode.id || endNode.label
      if (filteredNodes.some((n: any) => (n.id || n.label) === endNodeId)) {
        const reachable = dfsTraversal(endNodeId, finalBackwardMap)
        reachable.forEach(nodeId => finalCanReachEnd.add(nodeId))
      }
    })

    const remainingIsolatedNodes: string[] = []
    filteredNodes.forEach((node: any) => {
      const nodeId = node.id || node.label
      if (!finalReachableFromStart.has(nodeId) || !finalCanReachEnd.has(nodeId)) {
        remainingIsolatedNodes.push(nodeId)
      }
    })

    if (remainingIsolatedNodes.length > 0) {
      console.warn('WARNING: Still found isolated nodes after connectivity cleanup:', remainingIsolatedNodes)
    } else {
      console.log('✓ All remaining nodes are in the main process path')
    }
  }

  // 计算筛选后的统计信息
  const activityNodes = filteredNodes.filter((node: any) =>
    !node.isStartNode && !node.isEndNode &&
    node.id !== '开始' && node.id !== '结束'
  )

  const result = {
    ...originalData,
    nodes: filteredNodes,
    edges: filteredEdges,
    statistics: {
      ...originalData.statistics,
      totalCases: originalData.statistics?.totalCases || 0, // 保持原始案例数
      totalActivities: activityNodes.length, // 只计算实际的活动节点数
      filteredActivities: activityNodes.length, // 新增：筛选后的活动数
      filteredEdges: filteredEdges.length // 新增：筛选后的边数
    }
  }

  console.log('Returning filtered result:', result)
  return result
}

// 应用单个筛选条件
const applyCondition = (nodes: any[], edges: any[], condition: any) => {
  console.log('Applying condition:', condition)

  let filteredNodes = [...nodes]
  let filteredEdges = [...edges]

  switch (condition.type) {
    case 'resource':
      if (condition.data.resources?.length > 0) {
        filteredNodes = filteredNodes.filter((node: any) => {
          const resourceFields = ['resource', 'performer', 'executor', 'assignee', 'user', 'actor']
          const nodeResource = resourceFields.find(field => node[field])

          if (nodeResource) {
            const resourceValue = node[nodeResource]
            return condition.data.resources.includes(resourceValue)
          }
          return true // 如果节点没有资源信息，默认包含
        })
      }
      break

    case 'duration':
      console.log('xbisnsds')
      if (condition.data.min !== null || condition.data.max !== null) {
        const minDuration = convertToMilliseconds(condition.data.min || 0, condition.data.unit)
        const maxDuration = condition.data.max ?
          convertToMilliseconds(condition.data.max, condition.data.unit) :
          Infinity

        filteredNodes = filteredNodes.filter((node: any) => {
          // 保留开始/结束节点（包括子流程的开始/结束）
          const nodeId = (node.id || node.label || '') as string
          if (nodeId === '开始' || nodeId === '结束') {
            return true
          }
          const duration = node.avgDuration || 0
          return duration >= minDuration && duration <= maxDuration
        })

        filteredEdges = filteredEdges.filter((edge: any) => {
          // 当开启“忽略边界路径”时，剔除从开始节点出发的边和指向结束节点的边
          if (condition.data.ignoreBoundaryEdges) {
            const src = edge.source || edge.from
            const dst = edge.target || edge.to
            const isStartLike = (id: string) => !!id && (id === '开始' || id === 'Start' || id.startsWith('开始') || id.startsWith('Start') || (edge.fromIsStartNode === true))
            const isEndLike = (id: string) => !!id && (id === '结束' || id === 'End' || id.startsWith('结束') || id.startsWith('End') || (edge.toIsEndNode === true))
            if (isStartLike(String(src)) || isEndLike(String(dst))) {
              return true
            }
          }
          const duration = edge.avgDuration || 0
          return duration >= minDuration && duration <= maxDuration
        })
      }
      break

    case 'businessField':
      if (condition.data.field && (condition.data.value ||
        (condition.data.operator === 'range' && condition.data.rangeMin && condition.data.rangeMax))) {
        filteredNodes = filteredNodes.filter((node: any) => {
          const fieldValue = node[condition.data.field]
          if (!fieldValue) return false

          switch (condition.data.operator) {
            case 'contains':
              return String(fieldValue).toLowerCase().includes(condition.data.value.toLowerCase())
            case 'equals':
              return String(fieldValue) === condition.data.value
            case 'gt':
              return Number(fieldValue) > Number(condition.data.value)
            case 'lt':
              return Number(fieldValue) < Number(condition.data.value)
            case 'range': {
              const min = Number(condition.data.rangeMin)
              const max = Number(condition.data.rangeMax)
              const val = Number(fieldValue)
              return val >= min && val <= max
            }
            default:
              return true
          }
        })
      }
      break

    case 'pathway':
      if (condition.data.activity) {
        const targetActivity = condition.data.activity
        const minFrequency = condition.data.frequency[0]
        const maxFrequency = condition.data.frequency[1]

        console.log('Applying pathway filter:', {
          targetActivity,
          minFrequency,
          maxFrequency,
          type: condition.data.type,
          originalEdgeCount: filteredEdges.length
        })

        if (targetActivity === 'ALL_NODES') {
          // 全部活动模式：对所有边进行筛选
          const beforeCount = filteredEdges.length
          filteredEdges = filteredEdges.filter((edge: any) => {
            const frequency = edge.frequency || 0
            const passes = frequency >= minFrequency && frequency <= maxFrequency
            if (!passes) {
              const sourceId = edge.source || edge.from
              const targetId = edge.target || edge.to
              console.log(`Filtering out edge ${sourceId} -> ${targetId} (frequency: ${frequency})`)
            }
            return passes
          })
          console.log(`ALL_NODES filter: ${beforeCount} -> ${filteredEdges.length} edges`)
        } else {
          // 特定活动模式：筛选与指定活动相关的边
          const beforeCount = filteredEdges.length
          if (condition.data.type === 'predecessor') {
            // 前置活动筛选：筛选指向目标活动的边
            filteredEdges = filteredEdges.filter((edge: any) => {
              const targetId = edge.target || edge.to
              if (targetId === targetActivity) {
                const frequency = edge.frequency || 0
                const passes = frequency >= minFrequency && frequency <= maxFrequency
                if (!passes) {
                  const sourceId = edge.source || edge.from
                  console.log(`Filtering out predecessor edge ${sourceId} -> ${targetActivity} (frequency: ${frequency})`)
                }
                return passes
              }
              return true // 保留与目标活动无关的边
            })
          } else {
            // 后续活动筛选：筛选从目标活动出发的边
            filteredEdges = filteredEdges.filter((edge: any) => {
              const sourceId = edge.source || edge.from
              if (sourceId === targetActivity) {
                const frequency = edge.frequency || 0
                const passes = frequency >= minFrequency && frequency <= maxFrequency
                if (!passes) {
                  const targetId = edge.target || edge.to
                  console.log(`Filtering out successor edge ${targetActivity} -> ${targetId} (frequency: ${frequency})`)
                }
                return passes
              }
              return true // 保留与目标活动无关的边
            })
          }
          console.log(`Specific activity filter (${condition.data.type}): ${beforeCount} -> ${filteredEdges.length} edges`)
        }
      }
      break
  }

  return { nodes: filteredNodes, edges: filteredEdges }
}

// 辅助函数：转换时间单位为毫秒
const convertToMilliseconds = (value: number, unit: string): number => {
  switch (unit) {
    case 'minutes':
      return value * 60 * 1000
    case 'hours':
      return value * 60 * 60 * 1000
    case 'days':
      return value * 24 * 60 * 60 * 1000
    default:
      return value
  }
}

// 优化分组布局
const optimizeGroupLayout = (diagram: any) => {
  if (!diagram) return

  console.log('Optimizing group layout...')

  diagram.startTransaction('optimize group layout')

  try {
    // 获取所有分组
    const groups = new Set<any>()
    diagram.nodes.each((node: any) => {
      if (node.isGroup) {
        groups.add(node)
      }
    })

    // 为每个分组优化内部布局
    groups.forEach(group => {
      // 获取分组内的节点
      const groupMembers = new Set<any>()
      diagram.nodes.each((node: any) => {
        if (node.containingGroup === group) {
          groupMembers.add(node)
        }
      })

      if (groupMembers.size > 0) {
        // 计算分组内节点的边界
        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity
        groupMembers.forEach(member => {
          const bounds = member.actualBounds
          minX = Math.min(minX, bounds.x)
          minY = Math.min(minY, bounds.y)
          maxX = Math.max(maxX, bounds.x + bounds.width)
          maxY = Math.max(maxY, bounds.y + bounds.height)
        })

        // 确保分组有足够的内边距
        const padding = 20
        const groupBounds = new go.Rect(
          minX - padding,
          minY - padding,
          maxX - minX + 2 * padding,
          maxY - minY + 2 * padding
        )

        // 更新分组位置和大小
        group.location = groupBounds.position
        group.resizeObject.desiredSize = groupBounds.size
      }
    })

    // 确保分组不重叠
    const groupArray = Array.from(groups)
    for (let i = 0; i < groupArray.length; i++) {
      for (let j = i + 1; j < groupArray.length; j++) {
        const group1 = groupArray[i]
        const group2 = groupArray[j]

        if (group1.actualBounds.intersectsRect(group2.actualBounds)) {
          // 如果分组重叠，调整位置
          const offset = 50
          group2.location = new go.Point(
            group2.location.x + offset,
            group2.location.y + offset
          )
        }
      }
    }

  } catch (error) {
    console.error('Error optimizing group layout:', error)
  } finally {
    diagram.commitTransaction('optimize group layout')
  }
}

// 图遍历算法：从指定节点开始进行深度优先搜索
const dfsTraversal = (startNodeId: string, adjacencyMap: Map<string, string[]>, visited: Set<string> = new Set()): Set<string> => {
  const reachableNodes = new Set<string>()
  const stack = [startNodeId]

  while (stack.length > 0) {
    const currentNode = stack.pop()!

    if (visited.has(currentNode)) continue

    visited.add(currentNode)
    reachableNodes.add(currentNode)

    const neighbors = adjacencyMap.get(currentNode) || []
    for (const neighbor of neighbors) {
      if (!visited.has(neighbor)) {
        stack.push(neighbor)
      }
    }
  }

  return reachableNodes
}

// 构建邻接表（正向图：从源到目标）
const buildForwardAdjacencyMap = (edges: any[]): Map<string, string[]> => {
  const adjacencyMap = new Map<string, string[]>()

  edges.forEach((edge: any) => {
    const fromId = edge.from || edge.source
    const toId = edge.to || edge.target

    if (!adjacencyMap.has(fromId)) {
      adjacencyMap.set(fromId, [])
    }
    adjacencyMap.get(fromId)!.push(toId)
  })

  return adjacencyMap
}

// 构建邻接表（反向图：从目标到源）
const buildBackwardAdjacencyMap = (edges: any[]): Map<string, string[]> => {
  const adjacencyMap = new Map<string, string[]>()

  edges.forEach((edge: any) => {
    const fromId = edge.from || edge.source
    const toId = edge.to || edge.target

    if (!adjacencyMap.has(toId)) {
      adjacencyMap.set(toId, [])
    }
    adjacencyMap.get(toId)!.push(fromId)
  })

  return adjacencyMap
}

// 页面加载时检查是否有已存在的分析结果
onMounted(async () => {
  try {
    // 初始化Web Worker
    initializeFilterWorker()

    // 加载筛选器面板状态
    const savedPanelState = localStorage.getItem(`filter_panel_visible_${processId}`)
    if (savedPanelState !== null) {
      filterPanelVisible.value = savedPanelState === 'true'
    }

    // 检查缓存状态
    try {
      cacheStatus.value = await api.getCacheStatus(processId)
    } catch (error) {
      console.debug('Failed to get cache status:', error)
    }

    // 尝试获取已存在的分析结果
    const result = await api.getAnalysisResult(processId, AnalysisType.PROCESS_DISCOVERY)
    if (result && typeof result === 'object') {
      // 保存原始数据和当前显示数据
      originalDiscoveryResult.value = result
      discoveryResult.value = result
      isFromCache.value = true // 从数据库加载的结果标记为缓存
      await nextTick()

      // 渲染DFG，GlobalFilter组件会自动加载并应用已保存的筛选器
      renderDFG()
    }
  } catch (error) {
    // 没有找到已存在的结果，这是正常的
    console.debug('No existing analysis result found:', error)
  } finally {
    isLoading.value = false
  }
})

// 组件卸载时清理资源
onUnmounted(() => {
  // 清理所有资源（包括Web Worker和定时器）
  cleanup()

  // 停止所有动画
  stopAllAnimations()

  // 清除高亮状态
  clearHighlight()

  // 清理图表资源
  if (diagram) {
    diagram.div = null
    diagram = null
  }
})
</script>

<style scoped lang="scss">
@use '../../../assets/scss/pages/_discover.scss';
</style>
