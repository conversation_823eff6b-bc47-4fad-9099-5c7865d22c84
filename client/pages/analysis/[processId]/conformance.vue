<template>
  <div class="conformance-analysis-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <el-button
            :icon="ArrowLeft"
            class="back-button"
            link
            @click="navigateTo(`/processes/${processId}`)"
          />
          <div>
            <h1 class="title">流程符合性检查</h1>
            <p class="description">分析流程执行与标准模型的符合性</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-state">
      <div class="loading-spinner"/>
      <p class="loading-text">加载中...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <el-alert
        :title="error"
        type="error"
        show-icon
        :closable="false"
      >
        <template #default>
          <p>{{ error }}</p>
          <el-button type="primary" size="small" @click="loadData">
            重新加载
          </el-button>
        </template>
      </el-alert>
    </div>

    <!-- 主要内容 -->
    <div v-else class="main-content">
      <!-- 快速开始 -->
      <div class="quick-start-section">
        <el-card>
          <template #header>
            <h3 class="section-title">快速开始</h3>
          </template>
          
          <div class="quick-actions">
            <div v-if="canStartCheck" class="action-item" @click="startConformanceCheck">
              <div class="action-icon">
                <el-icon><DocumentChecked /></el-icon>
              </div>
              <div class="action-content">
                <h4 class="action-title">开始符合性检查</h4>
                <p class="action-description">选择BPMN模型，执行符合性分析</p>
              </div>
              <div class="action-arrow">
                <el-icon><ArrowRight /></el-icon>
              </div>
            </div>

            <div class="action-item" @click="viewResults">
              <div class="action-icon">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="action-content">
                <h4 class="action-title">查看历史结果</h4>
                <p class="action-description">浏览之前的检查结果和报告</p>
              </div>
              <div class="action-arrow">
                <el-icon><ArrowRight /></el-icon>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 流程信息 -->
      <div v-if="process" class="process-info-section">
        <el-card>
          <template #header>
            <h3 class="section-title">流程信息</h3>
          </template>
          
          <div class="process-details">
            <div class="detail-item">
              <span class="detail-label">流程名称：</span>
              <span class="detail-value">{{ process.name }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">描述：</span>
              <span class="detail-value">{{ process.description || '无' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">业务领域：</span>
              <span class="detail-value">{{ process.businessDomain || '未知' }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 最近的检查结果 -->
      <div v-if="recentResults.length > 0" class="recent-results-section">
        <el-card>
          <template #header>
            <div class="section-header">
              <h3 class="section-title">最近的检查结果</h3>
              <el-button link @click="viewResults">
                查看全部
              </el-button>
            </div>
          </template>
          
          <div class="results-list">
            <div
              v-for="result in recentResults.slice(0, 3)"
              :key="result.id"
              class="result-item"
              @click="viewResult(result.id)"
            >
              <div class="result-info">
                <div class="result-title">
                  {{ result.description || `检查结果 #${result.id}` }}
                </div>
                <div class="result-meta">
                  <span class="result-date">
                    {{ formatDate(result.createdAt) }}
                  </span>
                  <el-tag
                    :type="getStatusType(result.status)"
                    size="small"
                  >
                    {{ getStatusText(result.status) }}
                  </el-tag>
                </div>
              </div>
              <div v-if="result.status === 'completed'" class="result-score">
                <span class="score-label">符合性得分</span>
                <span class="score-value">{{ (result.conformanceScore * 100).toFixed(1) }}%</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ArrowLeft,
  ArrowRight,
  DocumentChecked,
  DataAnalysis
} from '@element-plus/icons-vue'
import { useApi } from '~/utils/api'
import type { Process, ConformanceResult } from '~/types'
import { ElMessage } from 'element-plus'

// 页面配置
definePageMeta({
  layout: 'default'
})

// 路由参数
const route = useRoute()
const processId = parseInt(route.params.processId as string)

// 状态
const isLoading = ref(true)
const error = ref<string | null>(null)
const process = ref<Process | null>(null)
const recentResults = ref<ConformanceResult[]>([])

// 设置页面标题
useHead({
  title: computed(() => process.value ? `${process.value.name} - 符合性检查 - ProMax` : '符合性检查 - ProMax')
})

// API
const api = useApi()

// 方法
const loadData = async () => {
  try {
    isLoading.value = true
    error.value = null

    // 并行加载数据
    const [processData, resultsData] = await Promise.allSettled([
      api.getProcess(processId),
      api.getConformanceResults(processId)
    ])

    // 处理流程数据
    if (processData.status === 'fulfilled') {
      process.value = processData.value
    } else {
      throw new Error('流程不存在或加载失败')
    }

    // 处理结果数据
    if (resultsData.status === 'fulfilled') {
      recentResults.value = Array.isArray(resultsData.value) ? resultsData.value : []
    }

  } catch (err: unknown) {
    error.value = err instanceof Error ? err.message : '数据加载失败'
  } finally {
    isLoading.value = false
  }
}

const canStartCheck = computed(() => {
  const s = process.value?.status
  return s === 'active' || s === 'completed'
})

const startConformanceCheck = () => {
  if (!canStartCheck.value) {
    ElMessage.warning('当前状态不允许发起新的符合性检查，仅可查看历史结果')
    return
  }
  navigateTo(`/conformance/check?processId=${processId}`)
}

const viewResults = () => {
  navigateTo(`/conformance/results?processId=${processId}`)
}

const viewResult = (resultId: number) => {
  navigateTo(`/conformance/results/${resultId}`)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const getStatusType = (status: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    pending: 'info',
    processing: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '等待中',
    processing: '处理中',
    completed: '已完成',
    failed: '失败'
  }
  return textMap[status] || status
}

// 页面加载时获取数据
onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.conformance-analysis-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }
}

.page-header {
  max-width: 1200px;
  margin: 0 auto 2rem auto;
  animation: fadeInUp 0.6s ease-out;

  .header-content {
    .header-title {
      display: flex;
      align-items: center;
      gap: 1rem;

      .back-button {
        color: #6b7280;
        transition: all 0.2s ease;
        border-radius: 50%;
        width: 40px;
        height: 40px;

        &:hover {
          color: #3b82f6;
          background-color: rgba(59, 130, 246, 0.1);
          transform: translateX(-2px);
        }
      }

      .title {
        font-size: 2.25rem;
        font-weight: 800;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0 0 0.5rem 0;
      }

      .description {
        color: #6b7280;
        margin: 0;
      }
    }
  }
}

.loading-state, .error-state {
  text-align: center;
  padding: 4rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.loading-spinner {
  width: 2.5rem;
  height: 2.5rem;
  border: 3px solid rgba(59, 130, 246, 0.2);
  border-top-color: #3b82f6;
  border-radius: 50%;
  margin: 0 auto 1.5rem;
  animation: spin 1s linear infinite;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin: 0;

  :global(.dark) & {
    color: #ffffff;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;

  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.action-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 12px;
  background: rgba(248, 250, 252, 0.8);
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    border-color: #3b82f6;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }

  :global(.dark) & {
    background: rgba(30, 41, 59, 0.8);
    border: 2px solid rgba(71, 85, 105, 0.3);

    &:hover {
      border-color: #60a5fa;
    }
  }

  .action-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, #8b5cf6, #a78bfa);
  }

  .action-content {
    flex: 1;

    .action-title {
      font-size: 1.125rem;
      font-weight: 700;
      color: #111827;
      margin: 0 0 0.25rem 0;

      :global(.dark) & {
        color: #ffffff;
      }
    }

    .action-description {
      font-size: 0.875rem;
      color: #6b7280;
      margin: 0;

      :global(.dark) & {
        color: #9ca3af;
      }
    }
  }

  .action-arrow {
    color: #6b7280;
    font-size: 1.25rem;

    :global(.dark) & {
      color: #9ca3af;
    }
  }
}

.process-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  .detail-item {
    display: flex;
    gap: 0.5rem;

    .detail-label {
      font-weight: 600;
      color: #6b7280;
      min-width: 100px;

      :global(.dark) & {
        color: #9ca3af;
      }
    }

    .detail-value {
      color: #111827;

      :global(.dark) & {
        color: #ffffff;
      }
    }
  }
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  .result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-radius: 8px;
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.5);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    :global(.dark) & {
      background: rgba(55, 65, 81, 0.8);
      border: 1px solid rgba(75, 85, 99, 0.5);
    }

    .result-info {
      flex: 1;

      .result-title {
        font-weight: 600;
        color: #111827;
        margin-bottom: 0.25rem;

        :global(.dark) & {
          color: #ffffff;
        }
      }

      .result-meta {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .result-date {
          font-size: 0.875rem;
          color: #6b7280;

          :global(.dark) & {
            color: #9ca3af;
          }
        }
      }
    }

    .result-score {
      text-align: right;

      .score-label {
        display: block;
        font-size: 0.75rem;
        color: #6b7280;
        margin-bottom: 0.25rem;

        :global(.dark) & {
          color: #9ca3af;
        }
      }

      .score-value {
        font-size: 1.125rem;
        font-weight: 700;
        color: #10b981;

        :global(.dark) & {
          color: #34d399;
        }
      }
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
