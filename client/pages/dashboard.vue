<template>
  <div class="dashboard-container">
    <div class="dashboard">
      <!-- 页面标题 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-text">
            <h1 class="page-title">
              仪表板
            </h1>
            <p class="page-subtitle">
              欢迎回来，{{ authStore.user?.fullName || authStore.user?.username }}！
            </p>
          </div>
          <div class="header-actions">
            <el-button
              type="primary"
              :icon="Plus"
              size="large"
              class="quick-action-btn"
              @click="navigateTo('/processes/create')"
            >
              创建新流程
            </el-button>
          </div>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div v-if="statistics !== null" class="stats-section">
        <div class="section-header">
          <h2 class="section-title">数据概览</h2>
        </div>
        <div class="stats-grid">
          <el-card class="stat-card">
            <div class="stat-card__content">
              <div class="stat-card__icon stat-card__icon--primary">
                <el-icon>
                  <Folder />
                </el-icon>
              </div>
              <div class="stat-card__info">
                <p class="stat-card__label">
                  总流程数
                </p>
                <p class="stat-card__value">
                  {{ (statistics && statistics.total) || 0 }}
                </p>
              </div>
            </div>
          </el-card>

          <el-card class="stat-card">
            <div class="stat-card__content">
              <div class="stat-card__icon stat-card__icon--success">
                <el-icon>
                  <VideoPlay />
                </el-icon>
              </div>
              <div class="stat-card__info">
                <p class="stat-card__label">
                  活跃流程
                </p>
                <p class="stat-card__value">
                  {{ (statistics && statistics.byStatus && statistics.byStatus.active) || 0 }}
                </p>
              </div>
            </div>
          </el-card>

          <el-card class="stat-card">
            <div class="stat-card__content">
              <div class="stat-card__icon stat-card__icon--info">
                <el-icon>
                  <CircleCheck />
                </el-icon>
              </div>
              <div class="stat-card__info">
                <p class="stat-card__label">
                  已完成
                </p>
                <p class="stat-card__value">
                  {{ (statistics && statistics.byStatus && statistics.byStatus.completed) || 0 }}
                </p>
              </div>
            </div>
          </el-card>

          <el-card class="stat-card">
            <div class="stat-card__content">
              <div class="stat-card__icon stat-card__icon--warning">
                <el-icon>
                  <Document />
                </el-icon>
              </div>
              <div class="stat-card__info">
                <p class="stat-card__label">
                  草稿
                </p>
                <p class="stat-card__value">
                  {{ (statistics && statistics.byStatus && statistics.byStatus.draft) || 0 }}
                </p>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-else class="loading-state">
        <el-skeleton :rows="2" animated />
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 快速操作 -->
        <div class="quick-actions-section">
          <div class="section-header">
            <h2 class="section-title">快速操作</h2>
          </div>
          <div class="quick-actions-grid">
            <div class="quick-action-item" @click="handleUploadClick">
              <div class="quick-action-item__icon quick-action-item__icon--upload">
                <el-icon>
                  <Upload />
                </el-icon>
              </div>
              <div class="quick-action-item__content">
                <h3 class="quick-action-item__title">上传数据文件</h3>
                <p class="quick-action-item__desc">导入事件日志数据</p>
              </div>
            </div>

            <div class="quick-action-item" @click="navigateTo('/discovery')">
              <div class="quick-action-item__icon quick-action-item__icon--discovery">
                <el-icon>
                  <Search />
                </el-icon>
              </div>
              <div class="quick-action-item__content">
                <h3 class="quick-action-item__title">流程发现</h3>
                <p class="quick-action-item__desc">分析业务流程模型</p>
              </div>
            </div>

            <div class="quick-action-item" @click="navigateTo('/processes')">
              <div class="quick-action-item__icon quick-action-item__icon--manage">
                <el-icon>
                  <Plus />
                </el-icon>
              </div>
              <div class="quick-action-item__content">
                <h3 class="quick-action-item__title">流程管理</h3>
                <p class="quick-action-item__desc">创建和管理流程</p>
              </div>
            </div>

            <div class="quick-action-item" @click="navigateTo('/conformance')">
              <div class="quick-action-item__icon quick-action-item__icon--check">
                <el-icon>
                  <CircleCheck />
                </el-icon>
              </div>
              <div class="quick-action-item__content">
                <h3 class="quick-action-item__title">符合性检查</h3>
                <p class="quick-action-item__desc">验证流程合规性</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近活动 -->
        <div class="recent-section">
          <div class="section-header">
            <h2 class="section-title">最近流程</h2>
            <el-button
              text
              type="primary"
              @click="navigateTo('/processes')"
            >
              查看全部
            </el-button>
          </div>
          <el-card class="action-card recent-card">
            <div v-if="recentProcesses && recentProcesses.length > 0" class="recent-list">
              <div
                v-for="process in recentProcesses"
                :key="process.id"
                class="recent-item"
                @click="navigateTo(`/processes/${process.id}`)"
              >
                <div class="recent-item__content">
                  <div class="recent-item__status">
                    <span
                      class="status-indicator"
                      :class="`status-indicator--${process.status}`"
                    />
                  </div>
                  <div class="recent-item__info">
                    <p class="recent-item__name">
                      {{ process.name }}
                    </p>
                    <p class="recent-item__date">
                      {{ formatDate(process.updatedAt) }}
                    </p>
                  </div>
                </div>
                <el-button
                  text
                  :icon="ArrowRight"
                  @click.stop="navigateTo(`/processes/${process.id}`)"
                />
              </div>
            </div>

            <div v-else class="empty-state">
              <el-icon class="empty-state__icon">
                <FolderOpened />
              </el-icon>
              <p class="empty-state__text">
                暂无流程数据
              </p>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Folder,
  VideoPlay,
  CircleCheck,
  Document,
  Plus,
  Upload,
  ArrowRight,
  FolderOpened,
  Search
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 页面配置
definePageMeta({
  layout: 'default'
})

// 设置页面标题
useHead({
  title: '仪表板 - ProMax'
})

const authStore = useAuthStore()
const processesStore = useProcessesStore()

// 获取统计数据
const { data: statistics } = await useLazyAsyncData('process-statistics', () =>
  processesStore.fetchStatistics(),
  {
    default: () => null
  }
)

// 获取最近的流程
await useLazyAsyncData('recent-processes', () =>
  processesStore.fetchProcesses(),
  {
    default: () => []
  }
)

// 最近的5个流程
const recentProcesses = computed(() => {
  if (!processesStore.processes || processesStore.processes.length === 0) {
    return []
  }
  return processesStore.processes
    .slice()
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
    .slice(0, 5)
})

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = (now.getTime() - date.getTime()) / 1000;

  if (diffInSeconds < 60) {
    return `${Math.floor(diffInSeconds)}秒前`;
  } else if (diffInSeconds < 3600) {
    return `${Math.floor(diffInSeconds / 60)}分钟前`;
  } else if (diffInSeconds < 86400) { // 24 * 60 * 60
    return `${Math.floor(diffInSeconds / 3600)}小时前`;
  } else if (diffInSeconds < 604800) { // 24 * 60 * 60 * 7
    return `${Math.floor(diffInSeconds / 86400)}天前`;
  } else {
    return date.toLocaleDateString('zh-CN');
  }
}

// 处理上传点击
const handleUploadClick = () => {
  // 如果有流程，跳转到第一个流程的上传页面
  const filteredProcesses = recentProcesses.value.filter(process => ['draft', 'active'].includes(process.status))

  if (filteredProcesses.length > 0) {
    navigateTo(`/analysis/${filteredProcesses[0].id}/upload`)
  } else {
    // 如果没有流程，先跳转到流程管理页面
    ElMessage.info('请先创建一个流程，然后再上传数据文件')
    navigateTo('/processes')
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }
}

.dashboard {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem 2rem;
  animation: fadeInUp 0.6s ease-out;

  @media (max-width: 768px) {
    padding: 1rem 1rem;
  }
}

.page-header {
  margin-bottom: 2rem;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 1.5rem;
    }
  }

  .header-text {
    flex: 1;
  }

  .header-actions {
    flex-shrink: 0;

    @media (max-width: 768px) {
      width: 100%;
    }
  }

  .page-title {
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0 0 0.5rem 0;
    letter-spacing: -0.025em;

    @media (max-width: 768px) {
      font-size: 2rem;
    }

    :global(.dark) & {
      background: linear-gradient(135deg, #60a5fa, #a78bfa);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .page-subtitle {
    color: #64748b;
    font-size: 1.125rem;
    font-weight: 500;
    margin: 0;
    opacity: 0.8;

    :global(.dark) & {
      color: #94a3b8;
    }
  }

  .quick-action-btn {
    border-radius: 12px;
    font-weight: 600;
    padding: 12px 24px;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border: none;
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
    }

    @media (max-width: 768px) {
      width: 100%;
      justify-content: center;
    }

    :global(.dark) & {
      background: linear-gradient(135deg, #60a5fa, #a78bfa);
    }
  }
}

// 区域样式
.stats-section {
  margin-bottom: 2.5rem;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;

  .section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #111827;
    margin: 0;

    :global(.dark) & {
      color: #ffffff;
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;

  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }

  @media (min-width: 1024px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }
}

.main-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2.5rem;

  @media (min-width: 1024px) {
    grid-template-columns: 1fr 1.5fr;
    gap: 3rem;
  }
}

.quick-actions-section {
  @media (min-width: 1024px) {
    order: 1;
  }
}

.recent-section {
  @media (min-width: 1024px) {
    order: 2;
  }
}

.stat-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  :global(.dark) & {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(71, 85, 105, 0.3);
  }

  // 添加渐变装饰条
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--stat-color), var(--stat-color-light));
  }

  &__content {
    display: flex;
    align-items: center;
    padding: 1.5rem;
  }

  &__icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    background: rgba(var(--stat-color-rgb), 0.1);
    color: var(--stat-color);
    font-size: 1.5rem;

    &--primary {
      --stat-color: #3b82f6;
      --stat-color-light: #60a5fa;
      --stat-color-rgb: 59, 130, 246;
    }

    &--success {
      --stat-color: #10b981;
      --stat-color-light: #34d399;
      --stat-color-rgb: 16, 185, 129;
    }

    &--info {
      --stat-color: #06b6d4;
      --stat-color-light: #22d3ee;
      --stat-color-rgb: 6, 182, 212;
    }

    &--warning {
      --stat-color: #f59e0b;
      --stat-color-light: #fbbf24;
      --stat-color-rgb: 245, 158, 11;
    }
  }

  &__info {
    margin-left: 1rem;
    flex: 1;
  }

  &__label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
    margin: 0 0 0.5rem 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;

    :global(.dark) & {
      color: #9ca3af;
    }
  }

  &__value {
    font-size: 2rem;
    font-weight: 700;
    color: #111827;
    margin: 0;
    line-height: 1;

    :global(.dark) & {
      color: #ffffff;
    }
  }
}

.action-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  :global(.dark) & {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(71, 85, 105, 0.3);
  }

  :deep(.el-card__header) {
    padding: 1.5rem 1.5rem 0.5rem 1.5rem;
    border-bottom: none;
  }

  :deep(.el-card__body) {
    padding: 1rem 1.5rem 1.5rem 1.5rem;
  }
}



.recent-card {
  height: fit-content;
  min-height: 280px;

  @media (min-width: 1024px) {
    min-height: 320px;
  }
}

.card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin: 0;

  :global(.dark) & {
    color: #ffffff;
  }
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;

  @media (max-width: 640px) {
    grid-template-columns: 1fr;
  }

  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
}

.quick-action-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 1.25rem;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  min-height: 120px;
  display: flex;
  flex-direction: column;

  @media (min-width: 768px) {
    padding: 1.5rem;
    min-height: 140px;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: rgba(59, 130, 246, 0.3);
  }

  :global(.dark) & {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(71, 85, 105, 0.3);

    &:hover {
      border-color: rgba(96, 165, 250, 0.3);
    }
  }

  // 添加渐变装饰条
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--action-gradient);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::before {
    opacity: 1;
  }

  &__icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.75rem;
    font-size: 1.25rem;
    background: var(--action-bg);
    color: var(--action-color);
    transition: all 0.3s ease;
    flex-shrink: 0;

    @media (min-width: 768px) {
      width: 48px;
      height: 48px;
      font-size: 1.5rem;
      margin-bottom: 1rem;
    }

    &--upload {
      --action-gradient: linear-gradient(135deg, #3b82f6, #1d4ed8);
      --action-bg: rgba(59, 130, 246, 0.1);
      --action-color: #3b82f6;
    }

    &--discovery {
      --action-gradient: linear-gradient(135deg, #10b981, #047857);
      --action-bg: rgba(16, 185, 129, 0.1);
      --action-color: #10b981;
    }

    &--manage {
      --action-gradient: linear-gradient(135deg, #8b5cf6, #7c3aed);
      --action-bg: rgba(139, 92, 246, 0.1);
      --action-color: #8b5cf6;
    }

    &--check {
      --action-gradient: linear-gradient(135deg, #f59e0b, #d97706);
      --action-bg: rgba(245, 158, 11, 0.1);
      --action-color: #f59e0b;
    }
  }

  &__content {
    text-align: left;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  &__title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #111827;
    margin: 0 0 0.25rem 0;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    @media (min-width: 768px) {
      font-size: 1rem;
      margin: 0 0 0.5rem 0;
      white-space: normal;
    }

    :global(.dark) & {
      color: #ffffff;
    }
  }

  &__desc {
    font-size: 0.75rem;
    color: #6b7280;
    margin: 0;
    line-height: 1.3;
    display: none;

    @media (min-width: 768px) {
      display: block;
      font-size: 0.875rem;
      line-height: 1.4;
    }

    :global(.dark) & {
      color: #9ca3af;
    }
  }

  &:hover &__icon {
    transform: scale(1.1);
    background: var(--action-color);
    color: white;
  }
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-height: 400px;
  overflow-x: hidden;
  overflow-y: auto;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(226, 232, 240, 0.3);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(59, 130, 246, 0.5);
    }
  }

  :global(.dark) & {
    &::-webkit-scrollbar-track {
      background: rgba(75, 85, 99, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(96, 165, 250, 0.3);

      &:hover {
        background: rgba(96, 165, 250, 0.5);
      }
    }
  }
}

.recent-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.875rem;
  border-radius: 10px;
  background: rgba(248, 250, 252, 0.6);
  border: 1px solid rgba(226, 232, 240, 0.4);
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    background: rgba(241, 245, 249, 0.8);
    transform: translateX(2px);
    border-color: rgba(59, 130, 246, 0.2);
  }

  :global(.dark) & {
    background: rgba(55, 65, 81, 0.6);
    border: 1px solid rgba(75, 85, 99, 0.4);

    &:hover {
      background: rgba(75, 85, 99, 0.8);
      border-color: rgba(96, 165, 250, 0.2);
    }
  }

  &__content {
    display: flex;
    align-items: center;
    gap: 0.875rem;
    flex: 1;
  }

  &__status {
    flex-shrink: 0;
  }

  &__info {
    flex: 1;
    min-width: 0;
  }

  &__name {
    font-size: 0.875rem;
    font-weight: 600;
    color: #111827;
    margin: 0 0 0.25rem 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    :global(.dark) & {
      color: #ffffff;
    }
  }

  &__date {
    font-size: 0.75rem;
    color: #6b7280;
    margin: 0;

    :global(.dark) & {
      color: #9ca3af;
    }
  }

  :deep(.el-button) {
    border-radius: 6px;
    color: #6b7280;
    transition: all 0.2s ease;
    padding: 4px;
    min-height: auto;

    &:hover {
      color: #3b82f6;
      background-color: rgba(59, 130, 246, 0.1);
    }

    :global(.dark) & {
      color: #9ca3af;

      &:hover {
        color: #60a5fa;
        background-color: rgba(96, 165, 250, 0.1);
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 2rem 1rem;

  &__icon {
    width: 48px;
    height: 48px;
    color: #cbd5e1;
    margin: 0 auto 1rem;

    :global(.dark) & {
      color: #64748b;
    }
  }

  &__text {
    color: #64748b;
    font-size: 0.875rem;
    font-weight: 500;
    margin: 0;

    :global(.dark) & {
      color: #94a3b8;
    }
  }
}

.loading-state {
  margin-bottom: 3rem;

  :deep(.el-skeleton) {
    .el-skeleton__item {
      background: rgba(226, 232, 240, 0.5);
      border-radius: 8px;

      :global(.dark) & {
        background: rgba(75, 85, 99, 0.5);
      }
    }
  }
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8);

  :global(.dark) & {
    box-shadow: 0 0 0 2px rgba(30, 41, 59, 0.8);
  }

  &--draft {
    background: linear-gradient(135deg, #6b7280, #9ca3af);
  }

  &--active {
    background: linear-gradient(135deg, #10b981, #34d399);
  }

  &--completed {
    background: linear-gradient(135deg, #06b6d4, #22d3ee);
  }

  &--archived {
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
  }
}

// 添加页面进入动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 统计卡片动画
.stats-grid .stat-card {
  animation: slideInUp 0.5s ease-out;
  animation-fill-mode: both;

  @for $i from 1 through 4 {
    &:nth-child(#{$i}) {
      animation-delay: #{$i * 0.1}s;
    }
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 快速操作动画
.quick-actions-grid .quick-action-item {
  animation: slideInUp 0.5s ease-out;
  animation-fill-mode: both;

  @for $i from 1 through 4 {
    &:nth-child(#{$i}) {
      animation-delay: #{0.4 + $i * 0.1}s;
    }
  }
}

// 添加点击动画
.quick-action-item {
  &:active {
    transform: translateY(-2px) scale(0.98);
  }
}
</style>
