<template>
  <div class="conformance-page">
    <!-- 顶部导航栏 -->
    <ConformancePageHeader
      :process-title="processTitle"
      @back="navigateTo('/conformance/results')"
    />

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-content">
        <div class="loading-spinner">
          <el-icon class="loading-icon"><Loading /></el-icon>
        </div>
        <div class="loading-text">
          <h3 v-if="result && result.status === 'processing'">符合性检查正在进行中...</h3>
          <h3 v-else>正在加载符合性结果...</h3>
          <p v-if="result && result.status === 'processing'">请稍候，系统正在分析流程数据并生成符合性报告</p>
          <p v-else>请稍候，正在获取数据...</p>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div v-else class="main-content">
      <!-- 左侧视图切换栏 -->
      <ViewSwitcher
        :current-view="currentView"
        @switch-view="handleViewSwitch"
      />

      <!-- 主内容面板 -->
      <div class="content-panel">
        <!-- 面板标题 -->
        <div v-if="showPanelHeader" class="panel-header">
          <h2 class="panel-title">{{ currentViewTitle }}</h2>

          <!-- 重新分析按钮 -->
          <div v-if="needsReanalysis" class="reanalyze-section">
            <el-tooltip
              :content="reanalysisReason"
              placement="bottom"
              :show-after="500"
            >
              <el-button
                type="warning"
                class="reanalyze-btn"
                :loading="isReanalyzing"
                @click="reanalyzeConformance"
              >
                <el-icon v-if="!isReanalyzing"><Refresh /></el-icon>
                重新分析
              </el-button>
            </el-tooltip>
          </div>
        </div>

        <!-- 符合率显示 -->
        <ConformanceRateDisplay
          v-if="showConformanceRate"
          :rate="conformanceRate"
          :rate-text="conformanceRateText"
        />

        <!-- 偏差视图 -->
        <div v-if="currentView === 'deviations'" class="deviations-view">
          <!-- 偏差列表视图 -->
          <DeviationsList
            v-if="!selectedDeviation"
            :deviations="deviations"
            :total-deviations="totalDeviations"
            :get-deviation-type-text="getDeviationTypeText"
            @select-deviation="selectDeviation"
          />

          <!-- 偏差详情视图 -->
          <DeviationDetail
            v-else
            :deviation="selectedDeviation"
            :get-deviation-type-text="getDeviationTypeText"
            @back="handleBackFromDetail"
            @case-selected="handleCaseSelected"
          />
        </div>

        <!-- 变体视图 -->
        <div v-else-if="currentView === 'variants'" class="variants-view">
          <!-- 变体加载状态 -->
          <div v-if="isVariantsLoading" class="variants-loading">
            <div class="loading-spinner">
              <el-icon class="loading-icon"><Loading /></el-icon>
            </div>
            <div class="loading-text">正在加载变体数据...</div>
          </div>

          <!-- 变体列表 -->
          <VariantsList
            v-else
            :variants="processVariants"
            :total-variants="totalVariants"
            :displayed-variants="displayedVariants"
            :selected-variant="selectedVariant"
            @select-variant="selectVariant"
            @activity-selected="handleActivitySelected"
          />
        </div>
      </div>

      <!-- 中间的BPMN图表区域 -->
      <div class="diagram-area">
        <BpmnDiagram
          ref="bpmnDiagramRef"
          :model="bpmnModel || undefined"
          :bpmn-xml="bpmnModel?.bpmnXml"
          :deviations="result?.deviations || []"
          :result="result || undefined"
          height="100%"
          :theme="isDark ? 'dark' : 'light'"
          :readonly="true"
          :enable-dragging="true"
          :show-grid="false"
          :current-view="currentView"
          @node-click="handleNodeClick"
          @link-click="handleLinkClick"
          @zoom-changed="handleZoomChanged"
          @virtual-elements-selected="handleVirtualElementsSelected"
        />

        <!-- 图表控制按钮 -->
        <div class="diagram-controls">
          <el-button-group class="zoom-controls">
            <el-button :icon="ZoomIn" @click="zoomIn" />
            <el-button :icon="ZoomOut" @click="zoomOut" />
            <el-button :icon="Refresh" @click="resetZoom" />
          </el-button-group>
          <div class="zoom-percentage">{{ zoomLevel }}%</div>
        </div>
      </div>

      <!-- 右侧控制面板 -->
      <div v-if="showControlPanel" class="control-panel">
        <div class="panel-header">
          <h3 class="panel-title">选中的元素</h3>
          <div class="header-actions">
            <el-button
              text
              size="small"
              @click="selectAllVirtualElements"
            >
              全选
            </el-button>
            <el-button
              text
              size="small"
              @click="clearSelection"
            >
              清除选择
            </el-button>
          </div>
        </div>

        <!-- 多选提示 -->
        <div class="multi-select-tip">
          <el-icon><InfoFilled /></el-icon>
          <span>按住 <kbd>Ctrl</kbd> 键点击可多选，或点击"全选"按钮</span>
        </div>

        <div class="panel-content">
          <!-- 选中的节点 -->
          <div v-if="selectedVirtualElements.nodes.size > 0" class="element-section">
            <h4 class="section-title">节点 ({{ selectedVirtualElements.nodes.size }})</h4>
            <div class="element-list">
              <div
                v-for="nodeKey in Array.from(selectedVirtualElements.nodes)"
                :key="nodeKey"
                class="element-item node-item"
              >
                <div class="element-icon">
                  <el-icon><Operation /></el-icon>
                </div>
                <div class="element-info">
                  <div class="element-name">{{ getNodeDisplayName(nodeKey) }}</div>
                </div>
                <el-button
                  text
                  size="small"
                  class="delete-btn"
                  @click="removeVirtualNode(nodeKey)"
                >
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <!-- 选中的连线 -->
          <div v-if="selectedVirtualElements.links.size > 0" class="element-section">
            <h4 class="section-title">连线 ({{ selectedVirtualElements.links.size }})</h4>
            <div class="element-list">
              <div
                v-for="(linkObj, index) in Array.from(selectedVirtualElements.links)"
                :key="`${linkObj.from}-${linkObj.to}-${index}`"
                class="element-item link-item"
              >
                <div class="element-icon">
                  <el-icon><Connection /></el-icon>
                </div>
                <div class="element-info">
                  <div class="element-name">{{ linkObj.from }} → {{ linkObj.to }}</div>
                </div>
                <el-button
                  text
                  size="small"
                  class="delete-btn"
                  @click="removeVirtualLink(linkObj)"
                >
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="panel-actions">
            <el-button
              type="primary"
              size="default"
              :loading="false"
              @click="addVirtualElementsToStandardModel"
            >
              <el-icon><Plus /></el-icon>
              添加到标准模型
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ZoomIn,
  ZoomOut,
  Refresh,
  Loading,
  Operation,
  Connection,
  Plus,
  InfoFilled,
  Close,
} from "@element-plus/icons-vue";
import BpmnDiagram from "~/components/conformance/BpmnDiagram.vue";
import ConformancePageHeader from "~/components/conformance/ConformancePageHeader.vue";
import ViewSwitcher from "~/components/conformance/ViewSwitcher.vue";
import ConformanceRateDisplay from "~/components/conformance/ConformanceRateDisplay.vue";
import DeviationsList from "~/components/conformance/DeviationsList.vue";
import DeviationDetail from "~/components/conformance/DeviationDetail.vue";
import VariantsList from "~/components/conformance/VariantsList.vue";
import { useConformanceResult } from "~/composables/useConformanceResult";
import type { DeviationItem, ProcessVariant } from "~/composables/useConformanceResult";
import { addVirtualElementsToBpmnXml, validateBpmnXml } from "~/utils/bpmnXmlModifier";
import { ElMessageBox } from 'element-plus';

// 页面参数
const route = useRoute();
const resultId = computed(() => route.params.id as string);

const {
  result,
  bpmnModel,
  isLoading,
  processTitle,
  conformanceRate,
  conformanceRateText,
  deviations,
  totalDeviations,
  processVariants,
  totalVariants,
  displayedVariants,
  loadData,
  getDeviationTypeText,
} = useConformanceResult(resultId.value);

// 页面状态
const selectedDeviation = ref<DeviationItem | null>(null);
const currentView = ref<"deviations" | "variants">("deviations");
const selectedVariant = ref<ProcessVariant | null>(null);
const zoomLevel = ref(100);
const isDark = useDark();

// 虚拟元素选中状态
const selectedVirtualElements = ref<{ nodes: Set<string>, links: Set<{from: string, to: string}> }>({
  nodes: new Set(),
  links: new Set()
});

// 标准模型修改状态
const modelModified = ref(false);
const isReanalyzing = ref(false);

// 组件引用
const bpmnDiagramRef = ref<InstanceType<typeof BpmnDiagram> | null>(null);

// 计算属性
const currentViewTitle = computed(() => {
  return currentView.value === "deviations" ? "偏差探索" : "偏差变体探索";
});

// 判断是否需要重新分析
const needsReanalysis = computed(() => {
  if (!result.value || !bpmnModel.value) {
    return false;
  }

  if (bpmnModel.value.createdAt && bpmnModel.value.updatedAt) {
    const modelCreatedAt = bpmnModel.value.createdAt;
    const modelUpdatedAt = bpmnModel.value.updatedAt;
    if (modelCreatedAt === modelUpdatedAt) {
      return false;
    }
  }

  // 基于时间戳比较
  if (bpmnModel.value.updatedAt && result.value.updatedAt) {
    const modelUpdatedAt = new Date(bpmnModel.value.updatedAt);
    const analysisUpdatedAt = new Date(result.value.updatedAt);

    // 如果模型的更新时间晚于分析的更新时间，说明模型被修改过
    if (modelUpdatedAt > analysisUpdatedAt) {
      return true;
    }
  }

  // 手动修改标记（当前会话中的修改）
  if (modelModified.value) {
    console.log('检测到当前会话中的模型修改，需要重新分析');
    return true;
  }

  return false;
});

// 重新分析的原因提示
const reanalysisReason = computed(() => {
  if (!needsReanalysis.value) return '';

  if (modelModified.value) {
    return '您在当前会话中修改了标准模型，建议重新分析以获得最新结果';
  }

  if (result.value && bpmnModel.value) {
    const modelUpdatedAt = new Date(bpmnModel.value.updatedAt);
    const analysisUpdatedAt = new Date(result.value.updatedAt);

    if (modelUpdatedAt > analysisUpdatedAt) {
      return `标准模型已更新，当前分析结果基于 ${analysisUpdatedAt.toLocaleString()} 的模型数据。建议重新分析以获取最新结果`;
    }
  }

  return '检测到标准模型已被修改，建议重新分析';
});

// 刷新分析状态
const forceRefreshReanalysisState = () => {
  const currentModified = modelModified.value;
  modelModified.value = !currentModified;
  nextTick(() => {
    modelModified.value = currentModified;
  });
};

const showPanelHeader = computed(() => {
  return currentView.value === 'variants' || (currentView.value === 'deviations' && !selectedDeviation.value);
});

const showConformanceRate = computed(() => {
  return currentView.value === 'deviations' && !selectedDeviation.value;
});

const showControlPanel = computed(() => {
  return currentView.value === 'variants' &&
    (selectedVirtualElements.value.nodes.size > 0 || selectedVirtualElements.value.links.size > 0);
});

// 变体加载状态
const isVariantsLoading = ref(false);

// 事件处理
const handleViewSwitch = async (view: "deviations" | "variants") => {
  const startTime = performance.now();

  // 如果切换到变体页面，显示加载状态
  if (view === 'variants') {
    isVariantsLoading.value = true;
    console.log('开始切换到变体页面...');
  }

  currentView.value = view;

  // 切换视图时清除所有选择和高亮
  selectedDeviation.value = null;
  selectedVariant.value = null;

  if (bpmnDiagramRef.value) {
    bpmnDiagramRef.value.resetModel();
    // 清除虚拟元素选中状态
    bpmnDiagramRef.value.clearVirtualElementSelection();
  }

  // 如果是变体页面，异步处理数据加载
  if (view === 'variants') {
    // 使用 nextTick 确保 DOM 更新完成
    await nextTick();

    const processVariantsAsync = () => {
      return new Promise<void>((resolve) => {
        const batchProcess = () => {
          try {
            // 触发变体数据计算（通过访问计算属性）
            const variantCount = processVariants.value.length;

            const endTime = performance.now();
            const duration = endTime - startTime;
            console.log(`变体页面切换完成，耗时: ${duration.toFixed(2)}ms，加载了 ${variantCount} 个变体`);

            isVariantsLoading.value = false;

            if (duration > 3000) {
              console.warn(`变体页面切换耗时过长: ${duration.toFixed(2)}ms`);
            }

            resolve();
          } catch (error) {
            console.error('变体数据加载失败:', error);
            isVariantsLoading.value = false;
            resolve();
          }
        };

        if (window.requestIdleCallback) {
          window.requestIdleCallback(batchProcess, { timeout: 5000 });
        } else {
          setTimeout(batchProcess, 16);
        }
      });
    };

    // 异步处理变体数据
    await processVariantsAsync();
  }
};

const selectDeviation = (deviation: DeviationItem) => {
  // 设置选中的偏差，进入详情视图
  selectedDeviation.value = deviation;

  // 先清理之前的高亮，然后高亮新的偏差
  if (bpmnDiagramRef.value) {
    // 先进行彻底清理
    bpmnDiagramRef.value.resetModel();

    // 然后高亮新的偏差
    if (deviation.deviations && deviation.activity && deviation.deviations.length > 0) {
      // 只取第一个案例进行高亮，避免性能问题
      const firstCaseId = deviation.deviations[0].caseId;

      // 使用setTimeout确保清理完成后再高亮
      setTimeout(() => {
        if (bpmnDiagramRef.value) {
          bpmnDiagramRef.value.highlightDeviationCases(deviation.activity, [firstCaseId], deviation.type);
        }
      }, 100);

      console.log(`进入偏差详情视图，只高亮第一个案例: ${firstCaseId}，共有 ${deviation.deviations.length} 个案例`);
    }
  }
};

const selectVariant = (variant: ProcessVariant) => {
  const wasSelected = selectedVariant.value?.id === variant.id;
  selectedVariant.value = wasSelected ? null : variant;

  // 切换变体时清除虚拟元素选中状态
  if (bpmnDiagramRef.value) {
    bpmnDiagramRef.value.clearVirtualElementSelection();
  }

  if (bpmnDiagramRef.value) {
    if (wasSelected) {
      // 如果取消选择，彻底清除高亮
      bpmnDiagramRef.value.resetModel();
    } else {
      // 先彻底清理之前的高亮
      bpmnDiagramRef.value.resetModel();

      // 性能优化：对于大型变体，使用异步高亮
      const traceLength = variant.trace?.length || 0;
      const alignedTraceLength = variant.alignedTrace?.length || 0;

      if (traceLength > 50 || alignedTraceLength > 50) {
        // 大型变体使用异步高亮，避免阻塞UI
        console.log(`变体 ${variant.id} 包含 ${traceLength} 个活动，使用异步高亮优化性能`);

        // 异步执行高亮
        setTimeout(() => {
          if (bpmnDiagramRef.value && selectedVariant.value?.id === variant.id) {
            bpmnDiagramRef.value.highlightVariantTrace(variant.trace, variant.alignedTrace);
          }
        }, 150); // 增加延迟确保清理完成
      } else {
        // 小型变体也使用异步高亮，确保清理完成
        setTimeout(() => {
          if (bpmnDiagramRef.value && selectedVariant.value?.id === variant.id) {
            bpmnDiagramRef.value.highlightVariantTrace(variant.trace, variant.alignedTrace);
          }
        }, 100);
      }
    }
  }

  // 同时清除偏差选择
  selectedDeviation.value = null;
};

const handleNodeClick = (nodeData: unknown) => {
  console.log("Node clicked:", nodeData);
};

const handleLinkClick = (linkData: unknown) => {
  console.log("Link clicked:", linkData);
};

const handleZoomChanged = (newZoomLevel: number) => {
  zoomLevel.value = newZoomLevel;
};

const handleCaseSelected = (activityName: string, caseIds: string[], deviationType: string) => {
  console.log(`选中案例: 活动"${activityName}", ${caseIds.length}个案例, 偏差类型: ${deviationType}`)
  // 高亮选中案例的偏差
  if (bpmnDiagramRef.value) {
    bpmnDiagramRef.value.highlightDeviationCases(activityName, caseIds, deviationType);
  }
};

const handleActivitySelected = (activityName: string, caseIds: string[], deviationTypes: string[]) => {
  console.log(deviationTypes)
  // 为每个偏差类型分别调用高亮逻辑，与偏差页面保持一致
  deviationTypes.forEach(deviationType => {
    handleCaseSelected(activityName, caseIds, deviationType);
  });
};

const handleBackFromDetail = () => {
  // 清除高亮状态
  if (bpmnDiagramRef.value) {
    bpmnDiagramRef.value.clearHighlight();
  }

  // 返回偏差列表
  selectedDeviation.value = null;
};

const zoomIn = () => {
  if (bpmnDiagramRef.value) {
    bpmnDiagramRef.value.zoomIn();
  }
};

const zoomOut = () => {
  if (bpmnDiagramRef.value) {
    bpmnDiagramRef.value.zoomOut();
  }
};

const resetZoom = () => {
  if (bpmnDiagramRef.value) {
    bpmnDiagramRef.value.resetZoom();
  }
};

const handleVirtualElementsSelected = (selectedElements: { nodes: Set<string>, links: Set<{from: string, to: string}> }) => {
  selectedVirtualElements.value = selectedElements;
};

// 添加虚拟元素到标准模型
const addVirtualElementsToStandardModel = async () => {
  if (!bpmnModel.value) {
    ElMessage.error('无法获取BPMN模型');
    return;
  }

  // 检查是否有选中的元素
  const hasSelectedNodes = selectedVirtualElements.value.nodes.size > 0;
  const hasSelectedLinks = selectedVirtualElements.value.links.size > 0;

  if (!hasSelectedNodes && !hasSelectedLinks) {
    ElMessage.warning('请先选择要添加的元素');
    return;
  }

  let confirmMessage = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif; line-height: 1.5; color: #374151;">';

  // 标题
  confirmMessage += '<div style="font-size: 15px; font-weight: 600; color: #1f2937; margin-bottom: 18px;">';
  confirmMessage += '确定要将以下元素添加到标准模型中吗？';
  confirmMessage += '</div>';

  // 显示具体的节点
  if (selectedVirtualElements.value.nodes.size > 0) {
    confirmMessage += '<div style="margin-bottom: 14px;">';
    confirmMessage += '<div style="font-weight: 600; color: #059669; margin-bottom: 8px; font-size: 13px;">节点：</div>';
    Array.from(selectedVirtualElements.value.nodes).forEach(nodeKey => {
      const nodeName = getNodeDisplayName(nodeKey);
      confirmMessage += `<div style="margin-bottom: 4px; font-size: 13px; color: #4b5563;">📄 ${nodeName}</div>`;
    });
    confirmMessage += '</div>';
  }

  // 显示具体的连线
  if (selectedVirtualElements.value.links.size > 0) {
    confirmMessage += '<div style="margin-bottom: 14px;">';
    confirmMessage += '<div style="font-weight: 600; color: #7c3aed; margin-bottom: 8px; font-size: 13px;">连线：</div>';
    Array.from(selectedVirtualElements.value.links).forEach(link => {
      confirmMessage += `<div style="margin-bottom: 4px; font-size: 13px; color: #4b5563;">🔗 ${link.from} <span style="color: #9ca3af;">→</span> ${link.to}</div>`;
    });
    confirmMessage += '</div>';
  }

  // 警告提示
  confirmMessage += '<div style="margin-top: 16px; padding: 10px 12px; background: #fef3c7; border-radius: 6px; border-left: 4px solid #f59e0b;">';
  confirmMessage += '<div style="color: #92400e; font-size: 12px; line-height: 1.4;">⚠️ 此操作将修改标准模型，现有的符合性结果可能不准确</div>';
  confirmMessage += '</div>';

  confirmMessage += '</div>';

  try {
    // 显示确认对话框
    await ElMessageBox.confirm(
      confirmMessage,
      '添加到标准模型',
      {
        confirmButtonText: '确定添加',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        showClose: true,
        closeOnClickModal: false,
        closeOnPressEscape: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '添加中...';
            setTimeout(() => {
              done();
            }, 100);
          } else {
            done();
          }
        }
      }
    );
  } catch {
    // 用户取消操作
    return;
  }

  const { nodes, links } = selectedVirtualElements.value;
  if (nodes.size === 0 && links.size === 0) {
    ElMessage.warning('请先选择要添加的虚拟节点或连线');
    return;
  }

  try {
    // 修改BPMN XML - 直接使用选中的数据
    const modifiedBpmnXml = addVirtualElementsToBpmnXml(
      bpmnModel.value.bpmnXml,
      Array.from(nodes),
      Array.from(links)
    );

    // 验证修改后的BPMN XML
    const validation = validateBpmnXml(modifiedBpmnXml);
    if (!validation.isValid) {
      console.error('BPMN XML验证失败:', validation.errors);
      ElMessage.error(`修改后的BPMN XML无效: ${validation.errors[0]}`);
      return;
    }

    // 更新BPMN模型 - 后端会自动重新解析modelData
    const updatedModel = await api.updateBpmnModel(bpmnModel.value.id, {
      bpmnXml: modifiedBpmnXml
    });

    // 使用后端返回的最新数据更新本地模型
    bpmnModel.value = updatedModel;

    ElMessage.success(`添加成功`);

    // 清除选中状态
    if (bpmnDiagramRef.value) {
      bpmnDiagramRef.value.clearVirtualElementSelection();
    }

    // 标记需要重新分析
    modelModified.value = true;

    // 显示重新分析提醒弹窗
    try {
      await ElMessageBox.confirm(
        '标准模型已更新，建议立即重新分析以获得准确的符合性结果。',
        '重新分析',
        {
          confirmButtonText: '立即重新分析',
          cancelButtonText: '稍后分析',
          type: 'info',
          showClose: true,
          closeOnClickModal: false,
          closeOnPressEscape: true
        }
      );

      // 用户选择立即重新分析
      await reanalyzeConformance();

    } catch {
      // 用户选择稍后分析，不做任何操作
    }

  } catch (error) {
    console.error('添加虚拟元素到标准模型失败:', error);
    ElMessage.error('添加到标准模型失败: ' + (error as Error).message);
  }
};

// 重新执行符合性分析
const reanalyzeConformance = async () => {
  if (!result.value || !bpmnModel.value) {
    ElMessage.error('无法获取分析结果或BPMN模型');
    return;
  }

  try {
    isReanalyzing.value = true;

    // 执行重新分析，直接更新当前结果
    await api.reanalyzeConformanceResult(
      result.value.id,
      result.value.parameters || {}
    );

    ElMessage.success('重新分析已开始，正在更新结果...');

    // 重置修改状态
    modelModified.value = false;

    // 等待一小段时间让后端开始处理
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 轮询检查分析状态直到完成
    await pollForAnalysisCompletion();

    // 强制触发响应式更新
    await nextTick();

    ElMessage.success('分析结果已更新');

  } catch (error) {
    console.error('重新分析失败:', error);
    ElMessage.error('重新分析失败: ' + (error as Error).message);
  } finally {
    isReanalyzing.value = false;
  }
};

// 轮询检查分析完成状态
const pollForAnalysisCompletion = async () => {
  const maxAttempts = 30; // 最多轮询30次（约5分钟）
  const pollInterval = 10000; // 每10秒轮询一次
  let attempts = 0;

  while (attempts < maxAttempts) {
    try {
      // 重新加载数据
      await loadData();

      // 检查分析状态
      if (result.value && result.value.status === 'completed') {
        console.log('分析完成，停止轮询');
        // 强制重置修改状态，确保按钮隐藏
        modelModified.value = false;

        // 等待一个tick确保响应式更新完成
        await nextTick();

        // 强制刷新重新分析状态
        forceRefreshReanalysisState();

        return;
      } else if (result.value && result.value.status === 'failed') {
        console.log('分析失败，停止轮询');
        ElMessage.error('分析失败，请查看详细信息');
        return;
      }

      // 如果还在处理中，继续轮询
      attempts++;
      if (attempts < maxAttempts) {
        console.log(`分析进行中，第${attempts}次轮询，${pollInterval/1000}秒后重试...`);
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      }
    } catch (error) {
      console.error('轮询过程中发生错误:', error);
      attempts++;
      if (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      }
    }
  }

  // 如果轮询超时
  ElMessage.warning('分析时间较长，请稍后手动刷新页面查看结果');
};

// 清除选择
const clearSelection = () => {
  if (bpmnDiagramRef.value) {
    bpmnDiagramRef.value.clearVirtualElementSelection();
  }
};

// 删除单个虚拟节点
const removeVirtualNode = (nodeKey: string) => {
  if (bpmnDiagramRef.value) {
    bpmnDiagramRef.value.removeVirtualNode(nodeKey);
  }
};

// 删除单个虚拟连线
const removeVirtualLink = (linkObj: { from: string; to: string }) => {
  if (bpmnDiagramRef.value) {
    bpmnDiagramRef.value.removeVirtualLink(linkObj);
  }
};

// 全选虚拟元素
const selectAllVirtualElements = () => {
  if (bpmnDiagramRef.value) {
    bpmnDiagramRef.value.selectAllVirtualElements();
  }
};

// 获取节点显示名称
const getNodeDisplayName = (nodeKey: string): string => {
  if (!bpmnDiagramRef.value?.diagram) return nodeKey;

  const node = bpmnDiagramRef.value.diagram.findNodeForKey(nodeKey);
  return node?.data.name || nodeKey;
};

// 页面初始化
onMounted(() => {
  loadData();

  // 监听图表加载完成，同步初始缩放比例
  nextTick(() => {
    if (bpmnDiagramRef.value) {
      // 延迟一点时间确保图表完全初始化
      setTimeout(() => {
        if (bpmnDiagramRef.value) {
          zoomLevel.value = bpmnDiagramRef.value.getCurrentZoom();
        }
      }, 200);
    }
  });
});
</script>

<style scoped lang="scss">
.conformance-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;

  .loading-container {
    flex: 1;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;

    .loading-content {
      text-align: center;
      padding: 40px;

      .loading-spinner {
        margin-bottom: 24px;

        .loading-icon {
          font-size: 48px;
          color: #409eff;
          animation: spin 2s linear infinite;
          transform-origin: center center;
          will-change: transform;
        }
      }

      .loading-text {
        h3 {
          margin: 0 0 12px 0;
          color: #303133;
          font-size: 18px;
          font-weight: 600;
        }

        p {
          margin: 0;
          color: #909399;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
    align-items: stretch;
    gap: 1px;

    .content-panel {
      width: 380px;
      background: white;
      border-right: 1px solid #e1e5e9;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      padding: 16px;

      .panel-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16px;

        .panel-title {
          font-size: 18px;
          font-weight: 700;
          color: #202124;
          margin: 0;
          letter-spacing: -0.2px;
        }

        .reanalyze-section {
          display: flex;
          align-items: center;

          .reanalyze-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
            transition: all 0.2s ease;

            .el-icon {
              font-size: 14px;
            }

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
            }

            &.is-loading {
              transform: none;
            }
          }
        }
      }

      .deviations-view {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
    }

    .diagram-area {
      flex: 1;
      position: relative;
      background: white;
      display: flex;
      flex-direction: column;

      .diagram-controls {
        position: absolute;
        bottom: 20px;
        left: 20px;
        display: flex;
        align-items: center;
        gap: 12px;
        z-index: 10;

        .zoom-controls {
          background: white;
          border-radius: 6px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

          .el-button {
            border: none;
            background: transparent;

            &:hover {
              background: #f8f9fa;
            }
          }
        }

        .zoom-percentage {
          background: white;
          padding: 6px 12px;
          border-radius: 6px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          font-size: 12px;
          font-weight: 500;
          color: #495057;
        }

        .fullscreen-btn {
          background: white;
          border: none;
          border-radius: 6px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

          &:hover {
            background: #f8f9fa;
          }
        }

        .reanalyze-btn {
          margin-left: 12px;
          border-radius: 6px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          font-size: 12px;
          padding: 8px 16px;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          }
        }
      }
    }

    .control-panel {
      position: relative;
      width: 340px;
      background: #ffffff;
      border: 1px solid rgba(0, 0, 0, 0.08);
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      overflow: hidden;

      .panel-header {
        padding: 20px 20px 16px 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);
        display: flex;
        justify-content: space-between;
        align-items: center;

        .panel-title {
          font-size: 16px;
          font-weight: 600;
          color: #1f2937;
          margin: 0;
          line-height: 1.3;
        }

        .header-actions {
          display: flex;
          gap: 8px;
          flex-shrink: 0;

          .el-button {
            border-radius: 6px;
            font-weight: 500;
            font-size: 12px;
            padding: 6px 12px;
            height: auto;
            transition: all 0.15s ease;

            &.is-text {
              // 默认样式
              color: #6b7280;
              background: rgba(107, 114, 128, 0.08);
              border: 1px solid rgba(107, 114, 128, 0.15);

              &:hover {
                color: #374151;
                background: rgba(107, 114, 128, 0.12);
                border-color: rgba(107, 114, 128, 0.25);
              }
            }

            &:first-child.is-text {
              color: #059669;
              background: rgba(5, 150, 105, 0.08);
              border-color: rgba(5, 150, 105, 0.15);

              &:hover {
                color: #047857;
                background: rgba(5, 150, 105, 0.12);
                border-color: rgba(5, 150, 105, 0.25);
              }
            }

            &:last-child.is-text {
              color: #d97706;
              background: rgba(217, 119, 6, 0.08);
              border-color: rgba(217, 119, 6, 0.15);

              &:hover {
                color: #b45309;
                background: rgba(217, 119, 6, 0.12);
                border-color: rgba(217, 119, 6, 0.25);
              }
            }
          }
        }
      }

      .multi-select-tip {
        padding: 12px 20px;
        background: rgba(14, 165, 233, 0.05);
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        color: #0369a1;
        line-height: 1.4;

        .el-icon {
          font-size: 14px;
          color: #0ea5e9;
          flex-shrink: 0;
        }

        kbd {
          background: rgba(14, 165, 233, 0.1);
          color: #0369a1;
          padding: 2px 6px;
          border-radius: 3px;
          font-size: 11px;
          font-weight: 600;
          border: 1px solid rgba(14, 165, 233, 0.2);
          font-family: ui-monospace, 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
        }
      }

      .panel-content {
        flex: 1;
        padding: 20px;
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.05);
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 3px;

          &:hover {
            background: rgba(0, 0, 0, 0.3);
          }
        }

        .element-section {
          margin-bottom: 28px;

          .section-title {
            font-size: 13px;
            font-weight: 600;
            color: #374151;
            margin: 0 0 12px 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          .element-list {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .element-item {
              display: flex;
              align-items: center;
              gap: 12px;
              padding: 12px;
              border-radius: 6px;
              border: 1px solid rgba(0, 0, 0, 0.06);
              transition: all 0.15s ease;

              &:hover {
                background: rgba(0, 0, 0, 0.02);
                border-color: rgba(0, 0, 0, 0.1);

                .delete-btn {
                  opacity: 1;
                  background: rgba(239, 68, 68, 0.12);
                  border-color: rgba(239, 68, 68, 0.3);
                }
              }

              .delete-btn {
                opacity: 0.6;
                transition: all 0.15s ease;
                color: #ef4444;
                background: rgba(239, 68, 68, 0.08);
                border: 1px solid rgba(239, 68, 68, 0.2);
                padding: 6px;
                min-width: 28px;
                height: 28px;
                border-radius: 6px;

                &:hover {
                  opacity: 1;
                  background: rgba(239, 68, 68, 0.15);
                  border-color: rgba(239, 68, 68, 0.4);
                  color: #dc2626;
                  transform: scale(1.05);
                  box-shadow: 0 2px 6px rgba(239, 68, 68, 0.3);
                }

                .el-icon {
                  font-size: 14px;
                }
              }
              .element-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                border-radius: 6px;
                font-size: 16px;
                flex-shrink: 0;
              }

              &.node-item .element-icon {
                background: rgba(16, 185, 129, 0.1);
                color: #059669;
                border: 1px solid rgba(16, 185, 129, 0.2);
              }

              &.link-item .element-icon {
                background: rgba(139, 92, 246, 0.1);
                color: #7c3aed;
                border: 1px solid rgba(139, 92, 246, 0.2);
              }

              .element-info {
                flex: 1;
                min-width: 0;

                .element-name {
                  font-size: 13px;
                  font-weight: 500;
                  color: #1f2937;
                  margin-bottom: 2px;
                  line-height: 1.4;
                  word-break: break-word;
                }
              }
            }
          }
        }

        .panel-actions {
          margin-top: 24px;
          padding-top: 16px;
          border-top: 1px solid rgba(0, 0, 0, 0.06);

          .el-button {
            width: 100%;
            height: 40px;
            font-weight: 600;
            font-size: 13px;
            border-radius: 6px;
            transition: all 0.15s ease;

            &.el-button--primary {
              background: #3b82f6;
              border-color: #3b82f6;
              box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3);

              &:hover {
                background: #2563eb;
                border-color: #2563eb;
                box-shadow: 0 2px 6px rgba(59, 130, 246, 0.4);
                transform: translateY(-1px);
              }

              &:active {
                transform: translateY(0);
              }
            }

            .el-icon {
              margin-right: 6px;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}

// 变体加载状态样式
.variants-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 24px;
  min-height: 300px;

  .loading-spinner {
    margin-bottom: 20px;

    .loading-icon {
      font-size: 40px;
      color: #409eff;
      animation: spin 2s linear infinite;
      transform-origin: center center;
      will-change: transform;
    }
  }

  .loading-text {
    color: #909399;
    font-size: 14px;
    text-align: center;
  }
}

.variants-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
