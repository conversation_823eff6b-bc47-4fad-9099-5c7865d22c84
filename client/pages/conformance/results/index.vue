<template>
  <div class="results-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <el-button
            @click="navigateTo('/conformance')"
            :icon="ArrowLeft"
            text
            class="back-button"
          />
          <div>
            <h1 class="title">符合性检查结果</h1>
            <p class="description">查看和管理符合性检查的历史结果</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            :icon="Search"
            @click="navigateTo('/conformance/check')"
          >
            新建检查
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filters-section">
      <el-card>
        <div class="filters-content">
          <div class="search-box">
            <el-input
              v-model="searchQuery"
              placeholder="搜索检查描述..."
              :prefix-icon="Search"
              clearable
            />
          </div>
          <div class="filter-controls">
            <el-select
              v-model="selectedProcess"
              placeholder="选择流程"
              clearable
            >
              <el-option
                v-for="process in processes"
                :key="process.id"
                :label="process.name"
                :value="process.id"
              />
            </el-select>
            <el-select
              v-model="statusFilter"
              placeholder="检查状态"
              clearable
            >
              <el-option label="全部" value="" />
              <el-option label="已完成" value="completed" />
              <el-option label="处理中" value="processing" />
              <el-option label="失败" value="failed" />
            </el-select>
            <el-select
              v-model="levelFilter"
              placeholder="符合性等级"
              clearable
            >
              <el-option label="全部" value="" />
              <el-option label="优秀" value="excellent" />
              <el-option label="良好" value="good" />
              <el-option label="一般" value="fair" />
              <el-option label="较差" value="poor" />
            </el-select>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 结果列表 -->
    <div class="results-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3 class="section-title">检查结果</h3>
            <div class="section-actions">
              <el-button
                :icon="Refresh"
                :loading="isLoading"
                @click="loadResults"
              >
                刷新
              </el-button>
            </div>
          </div>
        </template>

        <div v-if="isLoading" class="loading-state">
          <el-skeleton :rows="5" animated />
        </div>

        <div v-else-if="filteredResults.length === 0" class="empty-state">
          <el-icon class="empty-icon"><DataAnalysis /></el-icon>
          <h4 class="empty-title">
            {{ hasFilters ? '未找到匹配的结果' : '暂无检查结果' }}
          </h4>
          <p class="empty-description">
            {{ hasFilters ? '请尝试调整筛选条件' : '开始您的第一次符合性检查' }}
          </p>
          <el-button
            v-if="!hasFilters"
            type="primary"
            @click="navigateTo('/conformance/check')"
          >
            开始检查
          </el-button>
        </div>

        <div v-else class="results-list">
          <div
            v-for="result in filteredResults"
            :key="result.id"
            class="result-item"
            @click="navigateTo(`/conformance/results/${result.id}`)"
          >
            <div class="result-header">
              <div class="result-title">
                {{ result.description || `符合性检查 #${result.id}` }}
              </div>
              <div class="result-badges">
                <el-tag
                  :type="getStatusType(result.status) as any"
                  size="small"
                >
                  {{ getStatusText(result.status) }}
                </el-tag>
                <el-tag
                  v-if="result.status === 'completed'"
                  :type="getLevelType(result.conformanceLevel) as any"
                  size="small"
                >
                  {{ getLevelText(result.conformanceLevel) }}
                </el-tag>
              </div>
            </div>

            <div class="result-content">
              <div class="result-info">
                <div class="info-grid">
                  <div class="info-item">
                    <span class="info-label">流程ID:</span>
                    <span class="info-value">{{ result.processId }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">模型ID:</span>
                    <span class="info-value">{{ result.bpmnModelId }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">案例数:</span>
                    <span class="info-value">{{ result.totalCases }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">创建时间:</span>
                    <span class="info-value">{{ formatDateTime(result.createdAt) }}</span>
                  </div>
                </div>
              </div>

              <div v-if="result.status === 'completed'" class="result-scores">
                <div class="score-item">
                  <div class="score-label">符合性</div>
                  <div class="score-value">{{ formatScore(result.conformanceScore) }}</div>
                </div>
                <div class="score-item">
                  <div class="score-label">适应性</div>
                  <div class="score-value">{{ formatScore(result.fitnessScore) }}</div>
                </div>
                <div class="score-item">
                  <div class="score-label">精确性</div>
                  <div class="score-value">{{ formatScore(result.precisionScore) }}</div>
                </div>
              </div>

              <div v-if="result.status === 'completed'" class="result-summary">
                <div class="summary-item">
                  <span class="summary-label">符合案例:</span>
                  <span class="summary-value success">{{ result.conformingCases }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">偏差案例:</span>
                  <span class="summary-value danger">{{ result.deviatingCases }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">主要偏差:</span>
                  <span class="summary-value">{{ result.majorDeviationTypes?.length || 0 }} 种</span>
                </div>
              </div>
            </div>

            <div class="result-actions">
              <el-button
                size="small"
                @click.stop="viewResult(result)"
              >
                查看详情
              </el-button>
              <el-dropdown trigger="click" @command="handleResultAction">
                <el-button size="small" :icon="MoreFilled" @click.stop />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      :command="{ action: 'export', result }"
                      :icon="Download"
                    >
                      导出结果
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{ action: 'recheck', result }"
                      :icon="Refresh"
                    >
                      重新检查
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{ action: 'delete', result }"
                      :icon="Delete"
                      divided
                    >
                      删除结果
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="filteredResults.length > 0" class="pagination-section">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalResults"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ArrowLeft,
  Search,
  Refresh,
  DataAnalysis,
  MoreFilled,
  Download,
  Delete
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useApi } from '~/utils/api'
import type { ConformanceResult, Process, ConformanceStatus } from '~/types'

// 页面配置
definePageMeta({
  layout: 'default'
})

// 设置页面标题
useHead({
  title: '符合性检查结果 - ProMax'
})

// 状态
const isLoading = ref(true)
const results = ref<ConformanceResult[]>([])
const processes = ref<Process[]>([])
const searchQuery = ref('')
const selectedProcess = ref<number | undefined>(undefined)
const statusFilter = ref('')
const levelFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalResults = ref(0)

// API
const api = useApi()
const isInitializing = ref(true)


const route = useRoute()

// 计算属性
const hasFilters = computed(() => {
  return !!(searchQuery.value || selectedProcess.value || statusFilter.value || levelFilter.value)
})

const filteredResults = computed(() => results.value)

// 方法
const loadResults = async () => {
  try {
    isLoading.value = true
    const { data, meta } = await api.getConformanceResultsPaged({
      page: currentPage.value,
      pageSize: pageSize.value,
      search: searchQuery.value || undefined,
      processId: selectedProcess.value || undefined,
      status: statusFilter.value || undefined,
      level: levelFilter.value || undefined,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    })
    results.value = data
    totalResults.value = meta.total
  } catch (error) {
    console.error('Failed to load results:', error)
    ElMessage.error('加载结果列表失败')
    results.value = []
  } finally {
    isLoading.value = false
  }
}

type ProcessesResponse = Process[] | { data?: Process[] } | undefined | null

const loadProcesses = async () => {
  try {
    const res = (await api.getProcesses()) as unknown as ProcessesResponse
    processes.value = Array.isArray(res)
      ? (res as Process[])
      : ((res && 'data' in res && Array.isArray(res.data) ? res.data : []) as Process[])
  } catch (error) {
    console.error('Failed to load processes:', error)
    processes.value = []
  }
}


const handleSizeChange = async (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  await loadResults()
}

const handleCurrentChange = async (page: number) => {
  currentPage.value = page
  await loadResults()
}

watch([searchQuery, selectedProcess, statusFilter, levelFilter], async () => {
  if (isInitializing.value) return
  currentPage.value = 1
  await loadResults()
})


const viewResult = (result: ConformanceResult) => {
  navigateTo(`/conformance/results/${result.id}`)
}

const handleResultAction = async ({ action, result }: { action: string, result: ConformanceResult }) => {
  switch (action) {
    case 'export':
      await exportResult(result)
      break
    case 'recheck':
      navigateTo(`/conformance/check?processId=${result.processId}&modelId=${result.bpmnModelId}`)
      break
    case 'delete':
      await deleteResult(result)
      break
  }
}

const exportResult = async (result: ConformanceResult) => {
  try {
    const dataStr = JSON.stringify(result, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)

    const link = document.createElement('a')
    link.href = url
    link.download = `conformance-result-${result.id}-${Date.now()}.json`
    link.click()

    URL.revokeObjectURL(url)
    ElMessage.success('结果导出成功')
  } catch (error) {
    console.error('Failed to export result:', error)
    ElMessage.error('结果导出失败')
  }
}

const deleteResult = async (result: ConformanceResult) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除检查结果 #${result.id} 吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await api.deleteConformanceResult(result.id)
    ElMessage.success('结果删除成功')
    await loadResults()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete result:', error)
      ElMessage.error('结果删除失败')
    }
  }
}

const getStatusType = (status: ConformanceStatus): string => {
  const types = {
    completed: 'success',
    processing: 'warning',
    failed: 'danger',
    pending: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status: ConformanceStatus): string => {
  const texts = {
    completed: '已完成',
    processing: '处理中',
    failed: '失败',
    pending: '等待中'
  }
  return texts[status] || status
}

const getLevelType = (level: string | undefined): string => {
  const types = {
    excellent: 'success',
    good: 'success',
    fair: 'warning',
    poor: 'danger'
  } as const
  return types[(level as keyof typeof types)] ?? 'info'
}

const getLevelText = (level: string | undefined): string => {
  const texts = {
    excellent: '优秀',
    good: '良好',
    fair: '一般',
    poor: '较差'
  } as const
  return texts[(level as keyof typeof texts)] ?? (level ?? '')
}

const formatScore = (score: number): string => {
  return `${Math.round(score * 100)}%`
}

const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 根据 URL 查询参数预选流程
const initSelectedProcessFromQuery = () => {
  const pid = route.query?.processId as string | string[] | undefined
  const raw = Array.isArray(pid) ? pid[0] : pid
  const num = raw != null ? Number(raw) : NaN
  if (!Number.isNaN(num)) {
    selectedProcess.value = num
  }
}

// 监听路由变化，动态更新预选流程
watch(
  () => route.query?.processId,
  () => {
    initSelectedProcessFromQuery()
  }
)

// 页面加载时获取数据
onMounted(async () => {
  // 从查询参数中预选流程
  initSelectedProcessFromQuery()
  await loadProcesses()
  await loadResults()
  isInitializing.value = false
})
</script>

<style scoped lang="scss">
@use '~/assets/scss/utils/variables' as *;
@use '~/assets/scss/utils/mixins' as *;
@use 'sass:map';

.results-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @include respond-to(md) {
    padding: 2rem 2rem;
  }
}

.page-header {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  animation: fadeInUp 0.6s ease-out;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;

    @include respond-to-max(md) {
      flex-direction: column;
      align-items: stretch;
    }
  }

  .header-title {
    display: flex;
    align-items: center;
    gap: 1rem;

    .back-button {
      color: theme-color(gray, 600);
      transition: map.get($transition, all);
      border-radius: 50%;
      width: 40px;
      height: 40px;

      &:hover {
        color: theme-color(primary, 600);
        background-color: rgba(59, 130, 246, 0.1);
        transform: translateX(-2px);
      }

      :global(.dark) & {
        color: theme-color(gray, 400);

        &:hover {
          color: theme-color(primary, 400);
        }
      }
    }

    .title {
      font-size: font-size(3xl);
      font-weight: map.get($font-weight, bold);
      color: theme-color(gray, 900);
      margin: 0 0 spacing(1) 0;

      :global(.dark) & {
        color: theme-color(gray, 100);
      }
    }

    .description {
      font-size: font-size(base);
      color: theme-color(gray, 600);
      margin: 0;

      :global(.dark) & {
        color: theme-color(gray, 400);
      }
    }
  }

  .header-actions {
    flex-shrink: 0;
  }
}

.filters-section {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  animation: fadeInUp 0.6s ease-out 0.1s both;

  .filters-content {
    display: flex;
    gap: 1rem;
    align-items: center;

    @include respond-to-max(md) {
      flex-direction: column;
      align-items: stretch;
    }

    .search-box {
      flex: 1;
      min-width: 300px;

      @include respond-to-max(md) {
        min-width: auto;
      }
    }

    .filter-controls {
      display: flex;
      gap: 1rem;
      flex-shrink: 0;

      @include respond-to-max(md) {
        flex-direction: column;
      }

      .el-select {
        width: 150px;

        @include respond-to-max(md) {
          width: 100%;
        }
      }
    }
  }
}

.results-section {
  max-width: 1400px;
  margin: 0 auto;
  animation: fadeInUp 0.6s ease-out 0.2s both;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .section-title {
      font-size: font-size(lg);
      font-weight: map.get($font-weight, semibold);
      color: theme-color(gray, 900);
      margin: 0;

      :global(.dark) & {
        color: theme-color(gray, 100);
      }
    }

    .section-actions {
      display: flex;
      gap: 0.5rem;
    }
  }

  .loading-state,
  .empty-state {
    padding: 3rem 1rem;
    text-align: center;
  }

  .empty-state {
    .empty-icon {
      font-size: 3rem;
      color: theme-color(gray, 400);
      margin-bottom: 1rem;

      :global(.dark) & {
        color: theme-color(gray, 600);
      }
    }

    .empty-title {
      font-size: font-size(lg);
      font-weight: map.get($font-weight, semibold);
      color: theme-color(gray, 900);
      margin: 0 0 0.5rem 0;

      :global(.dark) & {
        color: theme-color(gray, 100);
      }
    }

    .empty-description {
      color: theme-color(gray, 600);
      margin: 0 0 1.5rem 0;

      :global(.dark) & {
        color: theme-color(gray, 400);
      }
    }
  }

  .results-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .result-item {
      padding: 1.5rem;
      border: 1px solid theme-color(gray, 200);
      border-radius: map.get($border-radius, lg);
      background-color: white;
      transition: map.get($transition, all);
      cursor: pointer;

      &:hover {
        border-color: theme-color(primary, 300);
        box-shadow: shadow(sm);
        transform: translateY(-1px);
      }

      :global(.dark) & {
        background-color: theme-color(gray, 800);
        border-color: theme-color(gray, 700);

        &:hover {
          border-color: theme-color(primary, 600);
        }
      }

      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;

        @include respond-to-max(md) {
          flex-direction: column;
          gap: 0.5rem;
        }

        .result-title {
          font-size: font-size(lg);
          font-weight: map.get($font-weight, semibold);
          color: theme-color(gray, 900);

          :global(.dark) & {
            color: theme-color(gray, 100);
          }
        }

        .result-badges {
          display: flex;
          gap: 0.5rem;
          flex-wrap: wrap;
        }
      }

      .result-content {
        display: grid;
        grid-template-columns: 1fr auto auto;
        gap: 2rem;
        align-items: start;

        @include respond-to-max(lg) {
          grid-template-columns: 1fr;
          gap: 1rem;
        }

        .result-info {
          .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;

            @include respond-to-max(md) {
              grid-template-columns: 1fr;
            }

            .info-item {
              display: flex;
              gap: 0.5rem;
              font-size: font-size(sm);

              .info-label {
                color: theme-color(gray, 600);
                font-weight: map.get($font-weight, medium);

                :global(.dark) & {
                  color: theme-color(gray, 400);
                }
              }

              .info-value {
                color: theme-color(gray, 900);

                :global(.dark) & {
                  color: theme-color(gray, 100);
                }
              }
            }
          }
        }

        .result-scores {
          display: flex;
          gap: 1rem;

          @include respond-to-max(lg) {
            justify-content: center;
          }

          .score-item {
            text-align: center;

            .score-label {
              font-size: font-size(xs);
              color: theme-color(gray, 600);
              margin-bottom: 0.25rem;

              :global(.dark) & {
                color: theme-color(gray, 400);
              }
            }

            .score-value {
              font-size: font-size(lg);
              font-weight: map.get($font-weight, bold);
              color: theme-color(primary, 600);

              :global(.dark) & {
                color: theme-color(primary, 400);
              }
            }
          }
        }

        .result-summary {
          display: flex;
          flex-direction: column;
          gap: 0.25rem;

          @include respond-to-max(lg) {
            flex-direction: row;
            justify-content: center;
            gap: 1rem;
          }

          .summary-item {
            display: flex;
            gap: 0.5rem;
            font-size: font-size(sm);

            .summary-label {
              color: theme-color(gray, 600);

              :global(.dark) & {
                color: theme-color(gray, 400);
              }
            }

            .summary-value {
              font-weight: map.get($font-weight, medium);

              &.success {
                color: theme-color(success, 600);

                :global(.dark) & {
                  color: theme-color(success, 400);
                }
              }

              &.danger {
                color: theme-color(error, 600);

                :global(.dark) & {
                  color: theme-color(error, 400);
                }
              }
            }
          }
        }
      }

      .result-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid theme-color(gray, 200);

        :global(.dark) & {
          border-top-color: theme-color(gray, 700);
        }
      }
    }
  }

  .pagination-section {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid theme-color(gray, 200);

    :global(.dark) & {
      border-top-color: theme-color(gray, 700);
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
