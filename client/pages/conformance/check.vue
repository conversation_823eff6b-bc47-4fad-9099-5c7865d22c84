<template>
  <div class="check-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <el-button
            link
            :icon="ArrowLeft"
            class="back-button"
            @click="navigateTo('/conformance')"
          />
          <div>
            <h1 class="title">符合性检查</h1>
            <p class="description">对比事件日志与BPMN模型，分析流程执行的符合性</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 检查配置 -->
    <div class="config-section">
      <el-card>
        <template #header>
          <h3 class="section-title">检查配置</h3>
        </template>

        <el-form
          ref="formRef"
          :model="checkForm"
          :rules="formRules"
          label-width="120px"
          label-position="left"
        >
          <!-- 基本配置 -->
          <div class="form-section">
            <h4 class="form-section-title">基本配置</h4>

            <el-form-item prop="processId" required>
              <template #label>
                <div class="form-label-with-tooltip">
                  <span>选择流程</span>
                  <el-tooltip
                    content="选择要进行符合性检查的流程。系统将使用该流程的事件日志数据与标准模型进行对比分析，识别偏差。"
                    placement="top"
                    :show-after="300"
                  >
                    <el-icon class="info-icon"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <el-select
                v-model="checkForm.processId"
                placeholder="请选择要检查的流程"
                style="width: 100%"
                no-data-text="暂无流程数据"
                :loading="isLoadingProcesses"
                @change="onProcessChange"
              >
                <el-option
                  v-for="process in filteredProcesses"
                  :key="process.id"
                  :label="process.name"
                  :value="process.id"
                />
                <template #empty>
                  <div class="empty-state">
                    <p class="empty-text">暂无流程数据</p>
                    <el-button
                      type="primary"
                      size="small"
                      @click="navigateTo('/discover')"
                    >
                      去创建流程
                    </el-button>
                  </div>
                </template>
              </el-select>
            </el-form-item>

            <el-form-item prop="bpmnModelId" required>
              <template #label>
                <div class="form-label-with-tooltip">
                  <span>标准模型</span>
                  <el-tooltip placement="top" :show-after="300">
                    <template #content>
                      <div class="tooltip-content">
                        <div><strong>流程发现结果：</strong>基于事件日志自动挖掘的流程模型，反映实际执行情况，适合发现流程变异和异常模式。</div>
                        <div><strong>已有模型：</strong>预定义的标准流程模型，用于检查实际执行是否符合既定规范和业务规则。</div>
                      </div>
                    </template>
                    <el-icon class="info-icon"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <el-select
                ref="modelSelectRef"
                v-model="checkForm.bpmnModelId"
                placeholder="请选择标准模型"
                :disabled="!checkForm.processId"
                style="width: 100%"
                :loading="isLoadingModels"
                no-data-text="暂无标准模型"
              >
                <!-- 流程发现模型组 -->
                <el-option-group v-if="checkForm.processId" label="流程发现">
                  <el-option
                    :value="'discovery'"
                    label="使用流程发现结果"
                  >
                    <div class="model-option discovery-option">
                      <div class="option-content">
                        <div class="model-info">
                          <el-icon class="model-icon"><Cpu /></el-icon>
                          <span class="model-name">使用流程发现结果</span>
                        </div>
                        <div class="option-actions">
                          <el-button
                            link
                            size="small"
                            @click.stop="viewDiscoveryProcess"
                          >
                            查看
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </el-option>
                </el-option-group>

                <!-- 现有模型组 -->
                <el-option-group
                  v-if="checkForm.processId && availableModels.length > 0"
                  label="已有模型"
                >
                  <el-option
                    v-for="model in availableModels"
                    :key="model.id"
                    :label="model.name"
                    :value="model.id"
                  >
                    <div class="model-option">
                      <div class="option-content">
                        <span class="model-name">{{ model.name }}</span>
                        <el-tag
                          :type="model.isValid ? 'success' : 'danger'"
                          size="small"
                        >
                          {{ model.isValid ? '有效' : '无效' }}
                        </el-tag>
                      </div>
                    </div>
                  </el-option>
                </el-option-group>

                <template #empty>
                  <div class="empty-state">
                    <p class="empty-text">
                      {{ checkForm.processId ? '该流程暂无BPMN模型' : '请先选择流程' }}
                    </p>
                    <el-button
                      v-if="checkForm.processId"
                      type="primary"
                      size="small"
                      @click="navigateTo('/conformance/models/create')"
                    >
                      去创建模型
                    </el-button>
                  </div>
                </template>
              </el-select>
            </el-form-item>

            <el-form-item>
              <template #label>
                <div class="form-label-with-tooltip">
                  <span>检查描述</span>
                  <el-tooltip
                    content="为本次符合性检查添加描述信息，便于后续查看和管理检查结果。描述将显示在检查历史记录中。"
                    placement="top"
                    :show-after="300"
                  >
                    <el-icon class="info-icon"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <el-input
                v-model="checkForm.description"
                placeholder="可选：为此次检查添加描述"
                type="textarea"
                :rows="2"
              />
            </el-form-item>
          </div>

          <!-- 算法参数 -->
          <div class="form-section">
            <h4 class="form-section-title">算法参数</h4>

            <el-form-item>
              <template #label>
                <div class="form-label-with-tooltip">
                  <span>对齐算法</span>
                  <el-tooltip placement="top" :show-after="300">
                    <template #content>
                      <div class="tooltip-content">
                        <div><strong>最优算法：</strong>计算精度最高，能找到全局最优解，但计算时间较长，适合小规模数据或对精度要求极高的场景。</div>
                        <div><strong>启发式算法：</strong>平衡精度和速度，在合理时间内找到近似最优解，适合大多数实际应用场景。</div>
                        <div><strong>遗传算法：</strong>适合大规模复杂数据，通过进化策略寻找解，计算速度快但精度相对较低。</div>
                      </div>
                    </template>
                    <el-icon class="info-icon"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <el-radio-group v-model="checkForm.parameters!.alignmentAlgorithm">
                <el-radio value="optimal">最优算法</el-radio>
                <el-radio value="heuristic">启发式算法</el-radio>
                <el-radio value="genetic">遗传算法</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item>
              <template #label>
                <div class="form-label-with-tooltip">
                  <span>移动成本</span>
                  <el-tooltip placement="top" :show-after="300">
                    <template #content>
                      <div class="tooltip-content">
                        <div><strong>模型移动：</strong>模型中存在但日志中缺失的活动成本。值越高，算法越倾向于认为缺失活动是正常的。</div>
                        <div><strong>日志移动：</strong>日志中存在但模型中未定义的活动成本。值越高，算法越倾向于忽略额外活动。</div>
                        <div><strong>同步移动：</strong>日志与模型完全匹配的活动成本，通常设为0表示无成本。</div>
                      </div>
                    </template>
                    <el-icon class="info-icon"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="cost-inputs">
                <div class="cost-item">
                  <label>模型移动</label>
                  <el-input-number
                    v-model="checkForm.parameters!.moveCosts!.modelMove"
                    :min="0"
                    :max="10"
                    :step="0.1"
                    size="small"
                  />
                </div>
                <div class="cost-item">
                  <label>日志移动</label>
                  <el-input-number
                    v-model="checkForm.parameters!.moveCosts!.logMove"
                    :min="0"
                    :max="10"
                    :step="0.1"
                    size="small"
                  />
                </div>
                <div class="cost-item">
                  <label>同步移动</label>
                  <el-input-number
                    v-model="checkForm.parameters!.moveCosts!.synchronousMove"
                    :min="0"
                    :max="10"
                    :step="0.1"
                    size="small"
                  />
                </div>
              </div>
            </el-form-item>
          </div>

          <!-- 过滤条件 -->
          <div class="form-section">
            <h4 class="form-section-title">过滤条件</h4>

            <el-form-item>
              <template #label>
                <div class="form-label-with-tooltip">
                  <span>活动数量范围</span>
                  <el-tooltip
                    content="过滤案例中活动数量的范围。只有活动数量在此范围内的案例才会被纳入符合性检查。可用于排除异常短或异常长的案例，提高分析质量。"
                    placement="top"
                    :show-after="300"
                  >
                    <el-icon class="info-icon"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="range-inputs">
                <el-input-number
                  v-model="checkForm.parameters!.caseFilter!.minActivities"
                  placeholder="最小"
                  :min="1"
                  size="small"
                />
                <span class="range-separator">至</span>
                <el-input-number
                  v-model="checkForm.parameters!.caseFilter!.maxActivities"
                  placeholder="最大"
                  :min="1"
                  size="small"
                />
              </div>
            </el-form-item>

            <el-form-item>
              <template #label>
                <div class="form-label-with-tooltip">
                  <span>时间范围</span>
                  <el-tooltip
                    content="限制符合性检查的时间范围。只有在指定时间段内开始或结束的案例才会被分析。可用于分析特定时期的流程执行情况。"
                    placement="top"
                    :show-after="300"
                  >
                    <el-icon class="info-icon"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <el-date-picker
                v-model="dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                @change="onDateRangeChange"
              />
            </el-form-item>
          </div>

          <!-- 输出选项 -->
          <div class="form-section">
            <h4 class="form-section-title">输出选项</h4>

            <el-form-item>
              <template #label>
                <div class="form-label-with-tooltip">
                  <span>详细分析</span>
                  <el-tooltip placement="top" :show-after="300">
                    <template #content>
                      <div class="tooltip-content">
                        <div><strong>详细对齐结果：</strong>显示每个案例的具体对齐路径和移动序列，便于深入分析偏差原因，但会增加计算时间和存储空间。</div>
                        <div><strong>案例级别分析：</strong>提供每个案例的符合性得分和偏差统计，适合识别问题案例和异常模式。</div>
                        <div><strong>活动级别分析：</strong>统计各活动的符合性表现，识别经常出现偏差的活动节点，有助于流程优化。</div>
                      </div>
                    </template>
                    <el-icon class="info-icon"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <el-checkbox-group v-model="outputOptions">
                <el-checkbox value="includeAlignment">包含详细对齐结果</el-checkbox>
                <el-checkbox value="includeCaseAnalysis">包含案例级别分析</el-checkbox>
                <el-checkbox value="includeActivityAnalysis">包含活动级别分析</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item>
              <template #label>
                <div class="form-label-with-tooltip">
                  <span>性能限制</span>
                  <el-tooltip placement="top" :show-after="300">
                    <template #content>
                      <div class="tooltip-content">
                        <div><strong>最大执行时间：</strong>限制单次检查的最长运行时间，防止复杂计算导致系统阻塞。建议根据数据规模调整：小数据集60-300秒，大数据集300-1800秒。</div>
                        <div><strong>最大内存使用：</strong>限制检查过程的内存占用，避免内存溢出。建议根据系统配置设置。</div>
                      </div>
                    </template>
                    <el-icon class="info-icon"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="performance-inputs">
                <div class="performance-item">
                  <label>最大执行时间（秒）</label>
                  <el-input-number
                    v-model="checkForm.parameters!.maxExecutionTime"
                    :min="60"
                    :max="3600"
                    size="small"
                  />
                </div>
                <div class="performance-item">
                  <label>最大内存使用（MB）</label>
                  <el-input-number
                    v-model="checkForm.parameters!.maxMemoryUsage"
                    :min="512"
                    :max="8192"
                    size="small"
                  />
                </div>
              </div>
            </el-form-item>
          </div>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button
              type="primary"
              size="large"
              :loading="isChecking"
              :disabled="!canStartCheck"
              @click="startCheck"
            >
              <el-icon><Search /></el-icon>
              开始符合性检查
            </el-button>
            <el-button
              size="large"
              @click="resetForm"
            >
              重置配置
            </el-button>
          </div>
        </el-form>
      </el-card>
    </div>

    <!-- 检查进度 -->
    <div v-if="isChecking" class="progress-section">
      <el-card>
        <template #header>
          <h3 class="section-title">检查进度</h3>
        </template>
        <div class="progress-content">
          <div class="progress-spinner">
            <div class="loading-spinner"/>
          </div>
          <div class="progress-info">
            <h4 class="progress-title">正在执行符合性检查...</h4>
            <p class="progress-description">
              这可能需要几分钟时间，请耐心等待
            </p>
            <div class="progress-details">
              <div class="detail-item">
                <span class="detail-label">算法：</span>
                <span class="detail-value">{{ getAlgorithmName(checkForm.parameters?.alignmentAlgorithm || 'heuristic') }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">模型：</span>
                <span class="detail-value">{{ selectedModelName }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ArrowLeft,
  Search,
  Cpu,
  InfoFilled
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useApi } from '~/utils/api'
import type { Process, BpmnModel, ConformanceCheckParams, ConformanceCheckRequest } from '~/types'

// 页面配置
definePageMeta({
  layout: 'default'
})

// 设置页面标题
useHead({
  title: '符合性检查 - ProMax'
})

// 状态
const isChecking = ref(false)
const isLoadingProcesses = ref(false)
const isLoadingModels = ref(false)
const processes = ref<Process[]>([])
const models = ref<BpmnModel[]>([])
const formRef = ref<FormInstance>()
const modelSelectRef = ref()
const dateRange = ref<[string, string] | undefined>(undefined)
const outputOptions = ref<string[]>(['includeCaseAnalysis'])

// 表单数据
const checkForm = ref<ConformanceCheckParams>({
  processId: undefined as any,
  bpmnModelId: undefined as any,
  description: '',
  parameters: {
    alignmentAlgorithm: 'heuristic',
    moveCosts: {
      modelMove: 1.0,
      logMove: 1.0,
      synchronousMove: 0.0
    },
    caseFilter: {
      minActivities: undefined,
      maxActivities: undefined,
      startDate: undefined,
      endDate: undefined
    },
    maxExecutionTime: 300,
    maxMemoryUsage: 2048,
    includeAlignment: false,
    includeCaseAnalysis: true,
    includeActivityAnalysis: false
  },
  forceRefresh: false
})

// 表单验证规则
const formRules: FormRules = {
  processId: [
    { required: true, message: '请选择流程', trigger: 'change' }
  ],
  bpmnModelId: [
    { required: true, message: '请选择BPMN模型', trigger: 'change' }
  ]
}

// API
const api = useApi()

// 计算属性
const availableModels = computed(() => {
  if (!checkForm.value.processId) {
    console.log('No processId selected, returning empty models')
    return []
  }

  // 过滤出当前流程的模型
  const filtered = models.value.filter(model => {
    // 只显示当前流程的模型
    if (model.processId !== checkForm.value.processId) {
      return false
    }

    // 过滤非激活状态的模型
    if (model.status !== 'active') {
      return false
    }

    return true
  })

  console.log(`Available models for process ${checkForm.value.processId}:`, filtered)
  console.log('All models:', models.value)
  console.log('Filtered out discovered models:', models.value.filter(m =>
    m.processId === checkForm.value.processId && m.modelType === 'discovered'
  ))

  return filtered
})

// 仅允许 active/completed 的流程发起检查
const filteredProcesses = computed(() => {
  return processes.value.filter(p => p.status === 'active' || p.status === 'completed')
})

// 额外校验：如果当前选择的流程不在允许范围，强制清空
watch(() => checkForm.value.processId, (pid) => {
  if (!pid) return
  const ok = filteredProcesses.value.some(p => p.id === pid)
  if (!ok) checkForm.value.processId = undefined as any
})

// 如果当前选择的流程不在允许范围，强制清空
watch(() => checkForm.value.processId, (pid) => {
  if (!pid) return
  const ok = filteredProcesses.value.some(p => p.id === pid)
  if (!ok) checkForm.value.processId = undefined as any
})

const selectedModelName = computed(() => {
  if (!checkForm.value.bpmnModelId) return ''
  if (checkForm.value.bpmnModelId === 'discovery') return '流程发现结果'
  const model = models.value.find(m => m.id === checkForm.value.bpmnModelId)
  return model?.name || ''
})

const canStartCheck = computed(() => {
  return checkForm.value.processId &&
         checkForm.value.bpmnModelId &&
         !isChecking.value
})

// 方法
const loadProcesses = async () => {
  try {
    isLoadingProcesses.value = true
    const result = await api.getProcesses()
    processes.value = Array.isArray(result) ? result : []

    if (processes.value.length === 0) {
      console.log('No processes found')
    }
  } catch (error) {
    console.error('Failed to load processes:', error)
    ElMessage.error('加载流程列表失败')
    processes.value = []
  } finally {
    isLoadingProcesses.value = false
  }
}

const loadModels = async () => {
  try {
    isLoadingModels.value = true
    // 获取所有BPMN模型
    const result = await api.getBpmnModels()
    models.value = Array.isArray(result) ? result : []

    if (models.value.length === 0) {
      console.log('No BPMN models found')
    }

    // 已选择模型且选择流程为空时，自动设置流程
    if (checkForm.value.bpmnModelId && !checkForm.value.processId) {
      const model = models.value.find(m => m.id === checkForm.value.bpmnModelId)
      if (model) {
        checkForm.value.processId = model.processId
      }
    }
  } catch (error) {
    console.error('Failed to load models:', error)
    ElMessage.error('加载模型列表失败')
    models.value = []
  } finally {
    isLoadingModels.value = false
  }
}

const loadModelsForProcess = async (processId: number) => {
  try {
    isLoadingModels.value = true
    // 获取指定流程的BPMN模型
    const result = await api.getBpmnModels(processId)
    // 更新models数组，保留其他流程的模型，替换当前流程的模型
    const otherModels = models.value.filter(model => model.processId !== processId)
    const currentProcessModels = Array.isArray(result) ? result : []
    models.value = [...otherModels, ...currentProcessModels]

    console.log(`Loaded ${currentProcessModels.length} models for process ${processId}`)
  } catch (error) {
    console.error('Failed to load models for process:', error)
    ElMessage.error('加载流程模型失败')
  } finally {
    isLoadingModels.value = false
  }
}

const onProcessChange = async () => {
  checkForm.value.bpmnModelId = undefined as any
  // 当流程改变时，重新加载该流程的模型
  if (checkForm.value.processId) {
    await loadModelsForProcess(checkForm.value.processId)
  }
}

const onDateRangeChange = (value: [string, string] | null | undefined) => {
  if (value && Array.isArray(value) && value.length === 2) {
    checkForm.value.parameters!.caseFilter!.startDate = new Date(value[0])
    checkForm.value.parameters!.caseFilter!.endDate = new Date(value[1])
  } else {
    checkForm.value.parameters!.caseFilter!.startDate = undefined
    checkForm.value.parameters!.caseFilter!.endDate = undefined
  }
}

const getAlgorithmName = (algorithm: string): string => {
  const names = {
    optimal: '最优算法',
    heuristic: '启发式算法',
    genetic: '遗传算法'
  }
  return names[algorithm as keyof typeof names] || algorithm
}

const startCheck = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 确保必要的字段都有值
    if (!checkForm.value.processId || !checkForm.value.bpmnModelId) {
      ElMessage.error('请选择流程和BPMN模型')
      return
    }

    // 如果选择的是流程发现结果，跳转到编辑页面
    if (checkForm.value.bpmnModelId === 'discovery') {
      // 跳转到流程发现结果编辑页面
      navigateTo(`/conformance/discovery-edit/${checkForm.value.processId}?returnTo=check`)
      return
    }

    isChecking.value = true

    const actualBpmnModelId = checkForm.value.bpmnModelId

    // 设置输出选项
    if (checkForm.value.parameters) {
      checkForm.value.parameters.includeAlignment = outputOptions.value.includes('includeAlignment')
      checkForm.value.parameters.includeCaseAnalysis = outputOptions.value.includes('includeCaseAnalysis')
      checkForm.value.parameters.includeActivityAnalysis = outputOptions.value.includes('includeActivityAnalysis')
    }

    // 创建请求参数，确保类型正确
    const requestParams: ConformanceCheckRequest = {
      ...checkForm.value,
      processId: checkForm.value.processId as number,
      bpmnModelId: actualBpmnModelId as number,
      forceRefresh: true
    }

    const result = await api.performConformanceCheck(requestParams)

    ElMessage.success('符合性检查已开始')

    // 跳转到结果页面
    navigateTo(`/conformance/results/${result.id}`)
  } catch (error) {
    console.error('Failed to start conformance check:', error)
    ElMessage.error('启动符合性检查失败')
  } finally {
    isChecking.value = false
  }
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  dateRange.value = undefined
  outputOptions.value = ['includeCaseAnalysis']
}

// 查看流程发现结果
const viewDiscoveryProcess = () => {
  if (!checkForm.value.processId) {
    ElMessage.warning('请先选择流程')
    return
  }

  // 关闭下拉框
  if (modelSelectRef.value) {
    modelSelectRef.value.blur()
  }

  // 在新页面中打开流程发现页面
  const url = `/analysis/${checkForm.value.processId}/discover`
  window.open(url, '_blank')
}

// 页面加载时获取数据
onMounted(async () => {
  // 从URL参数中获取预设值
  const route = useRoute()
  if (route.query.processId) {
    const processId = parseInt(route.query.processId as string)
    checkForm.value.processId = processId
    // 加载该流程的模型
    await loadModelsForProcess(processId)
  }
  if (route.query.modelId) {
    checkForm.value.bpmnModelId = parseInt(route.query.modelId as string)
  }

  await Promise.all([
    loadProcesses(),
    loadModels()
  ])
})
</script>

<style scoped lang="scss">
@use '~/assets/scss/utils/variables' as *;
@use '~/assets/scss/utils/mixins' as *;
@use 'sass:map';

.check-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @include respond-to(md) {
    padding: 2rem 2rem;
  }
}

.page-header {
  max-width: 1200px;
  margin: 0 auto 2rem auto;
  animation: fadeInUp 0.6s ease-out;

  .header-content {
    .header-title {
      display: flex;
      align-items: center;
      gap: 1rem;

      .back-button {
        color: theme-color(gray, 600);
        transition: map.get($transition, all);
        border-radius: 50%;
        width: 40px;
        height: 40px;

        &:hover {
          color: theme-color(primary, 600);
          background-color: rgba(59, 130, 246, 0.1);
          transform: translateX(-2px);
        }

        :global(.dark) & {
          color: theme-color(gray, 400);

          &:hover {
            color: theme-color(primary, 400);
          }
        }
      }

      .title {
        font-size: font-size(3xl);
        font-weight: map.get($font-weight, bold);
        color: theme-color(gray, 900);
        margin: 0 0 spacing(1) 0;

        :global(.dark) & {
          color: theme-color(gray, 100);
        }
      }

      .description {
        font-size: font-size(base);
        color: theme-color(gray, 600);
        margin: 0;

        :global(.dark) & {
          color: theme-color(gray, 400);
        }
      }
    }
  }
}

.config-section,
.progress-section {
  max-width: 1200px;
  margin: 0 auto 2rem auto;
  animation: fadeInUp 0.6s ease-out 0.1s both;

  .section-title {
    font-size: font-size(lg);
    font-weight: map.get($font-weight, semibold);
    color: theme-color(gray, 900);
    margin: 0;

    :global(.dark) & {
      color: theme-color(gray, 100);
    }
  }
}

.form-section {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid theme-color(gray, 200);

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  :global(.dark) & {
    border-bottom-color: theme-color(gray, 700);
  }

  .form-section-title {
    font-size: font-size(lg);
    font-weight: map.get($font-weight, semibold);
    color: theme-color(gray, 900);
    margin: 0 0 1.5rem 0;

    :global(.dark) & {
      color: theme-color(gray, 100);
    }
  }
}

.model-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .model-name {
    flex: 1;
  }
}

// 表单标签和提示样式
.form-label-with-tooltip {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .info-icon {
    color: theme-color(gray, 400);
    font-size: 14px;
    cursor: help;
    transition: map.get($transition, all);

    &:hover {
      color: theme-color(primary, 500);
      transform: scale(1.1);
    }

    :global(.dark) & {
      color: theme-color(gray, 500);

      &:hover {
        color: theme-color(primary, 400);
      }
    }
  }
}

// Tooltip内容样式
.tooltip-content {
  max-width: 300px;
  line-height: 1.5;

  div {
    margin-bottom: 0.5rem;

    &:last-child {
      margin-bottom: 0;
    }

    strong {
      color: theme-color(primary, 300);
      font-weight: map.get($font-weight, medium);
    }
  }
}

.form-help {
  font-size: font-size(sm);
  color: theme-color(gray, 500);
  margin-top: 0.5rem;
  line-height: 1.5;

  :global(.dark) & {
    color: theme-color(gray, 500);
  }

  .help-section {
    margin-bottom: 0.5rem;

    &:last-child {
      margin-bottom: 0;
    }

    strong {
      color: theme-color(gray, 700);
      font-weight: map.get($font-weight, medium);

      :global(.dark) & {
        color: theme-color(gray, 300);
      }
    }
  }
}

.cost-inputs,
.range-inputs,
.performance-inputs {
  display: flex;
  gap: 1rem;
  align-items: center;

  @include respond-to-max(md) {
    flex-direction: column;
    align-items: stretch;
  }

  .cost-item,
  .performance-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    label {
      font-size: font-size(sm);
      color: theme-color(gray, 700);

      :global(.dark) & {
        color: theme-color(gray, 300);
      }
    }
  }

  .range-separator {
    color: theme-color(gray, 500);
    font-size: font-size(sm);

    :global(.dark) & {
      color: theme-color(gray, 500);
    }
  }
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  padding-top: 2rem;

  @include respond-to-max(md) {
    flex-direction: column;
  }
}

.progress-content {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding: 2rem 0;

  @include respond-to-max(md) {
    flex-direction: column;
    text-align: center;
  }

  .progress-spinner {
    .loading-spinner {
      width: 60px;
      height: 60px;
      border: 4px solid theme-color(gray, 200);
      border-top: 4px solid theme-color(primary, 600);
      border-radius: 50%;
      animation: spin 1s linear infinite;

      :global(.dark) & {
        border-color: theme-color(gray, 700);
        border-top-color: theme-color(primary, 400);
      }
    }
  }

  .progress-info {
    flex: 1;

    .progress-title {
      font-size: font-size(lg);
      font-weight: map.get($font-weight, semibold);
      color: theme-color(gray, 900);
      margin: 0 0 0.5rem 0;

      :global(.dark) & {
        color: theme-color(gray, 100);
      }
    }

    .progress-description {
      color: theme-color(gray, 600);
      margin: 0 0 1rem 0;

      :global(.dark) & {
        color: theme-color(gray, 400);
      }
    }

    .progress-details {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      .detail-item {
        display: flex;
        gap: 0.5rem;

        .detail-label {
          font-weight: map.get($font-weight, medium);
          color: theme-color(gray, 700);

          :global(.dark) & {
            color: theme-color(gray, 300);
          }
        }

        .detail-value {
          color: theme-color(gray, 900);

          :global(.dark) & {
            color: theme-color(gray, 100);
          }
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 模型选项通用样式
.model-option {
  width: 100%;

  .option-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    min-height: 32px;
    padding: 4px 0;
    gap: 16px; // 增加更大的间距
    white-space: nowrap;
  }

  .model-name {
    flex: 1;
    font-weight: map.get($font-weight, medium);
    line-height: 1.2;
    white-space: nowrap;
    min-width: 0;
    // 移除 overflow 和 text-overflow，让文字完整显示
  }

  // 标签样式优化
  .el-tag {
    flex-shrink: 0;
    white-space: nowrap;
    font-size: 12px;
    height: 20px;
    line-height: 18px;
  }
}

// 优化下拉选择的整体样式
:deep(.el-select-dropdown__item) {
  padding: 8px 12px !important;
  line-height: 1.2 !important;
  display: flex !important;
  align-items: center !important;
  min-height: 40px !important;
  overflow: visible !important; // 允许内容正常显示
  white-space: nowrap !important; // 防止换行
}

// 确保选项组内的选项也有正确的样式
:deep(.el-select-group .el-select-dropdown__item) {
  padding: 8px 12px !important;
  min-height: 40px !important;
  white-space: nowrap !important;
}

// 下拉框宽度优化 - 增加更大的宽度
:deep(.el-select-dropdown) {
  min-width: 400px !important; // 增加宽度确保内容不换行
  max-width: 500px !important; // 设置最大宽度
}

// 选项内容不换行
:deep(.el-select-dropdown__item .model-option) {
  width: 100% !important;
  white-space: nowrap !important;
}

:deep(.el-select-dropdown__item .option-content) {
  width: 100% !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  white-space: nowrap !important;
  gap: 12px !important;
}

// 强制防止所有子元素换行
:deep(.el-select-dropdown__item *) {
  white-space: nowrap !important;
}

// 确保模型信息区域不换行
:deep(.el-select-dropdown__item .model-info) {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  white-space: nowrap !important;
  flex: 1 !important;
  min-width: 0 !important;
}

// 确保按钮区域不换行
:deep(.el-select-dropdown__item .option-actions) {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  white-space: nowrap !important;
  flex-shrink: 0 !important;
}

// 选项组标题样式
:deep(.el-select-group__title) {
  font-size: 12px !important;
  font-weight: 600 !important;
  color: theme-color(gray, 600) !important;
  padding: 8px 12px 4px !important;
  white-space: nowrap !important;

  :global(.dark) & {
    color: theme-color(gray, 400) !important;
  }
}

// 空状态样式
.empty-state {
  padding: 1rem;
  text-align: center;

  .empty-text {
    color: theme-color(gray, 500);
    font-size: font-size(sm);
    margin: 0 0 0.75rem 0;

    :global(.dark) & {
      color: theme-color(gray, 400);
    }
  }
}

// 流程发现选项样式
.discovery-option {
  width: 100%;

  .option-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    min-height: 32px;
    gap: 16px; // 增加更大的间距
    white-space: nowrap;
  }

  .model-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
    white-space: nowrap;

    .model-icon {
      color: theme-color(primary, 500);
      font-size: 16px;
      flex-shrink: 0;
    }

    .model-name {
      font-weight: map.get($font-weight, medium);
      line-height: 1.2;
      white-space: nowrap;
    }
  }

  .option-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
    white-space: nowrap;

    .el-button {
      padding: 4px 8px !important;
      font-size: 12px !important;
      height: 24px !important;
      line-height: 1.2 !important;
      display: flex !important;
      align-items: center !important;
      white-space: nowrap !important;
      flex-shrink: 0 !important;
    }
  }
}

// 选项组样式优化
:deep(.el-select-group__title) {
  font-size: 12px;
  font-weight: 600;
  color: theme-color(gray, 600);
  padding: 8px 20px 4px;

  :global(.dark) & {
    color: theme-color(gray, 400);
  }
}

// 确保标签也正确对齐
:deep(.el-tag) {
  display: inline-flex;
  align-items: center;
  line-height: 1.2;
  height: 24px;
}

// 确保按钮在选项中正确对齐
:deep(.el-select-dropdown__item .el-button) {
  margin: 0;
  vertical-align: middle;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
