<template>
  <div class="conformance-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="title">符合性检查</h1>
        <p class="description">
          对比事件日志与BPMN模型，分析流程执行的符合性
        </p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-if="isError && !isLoading" class="error-state">
      <el-alert
        :title="errorMessage"
        type="error"
        show-icon
        :closable="false"
      >
        <template #default>
          <p>{{ errorMessage }}</p>
          <el-button type="primary" size="small" @click="retryLoad">
            重新加载
          </el-button>
        </template>
      </el-alert>
    </div>

    <!-- 统计卡片 -->
    <div v-if="!isError" class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon stat-icon--primary">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ statistics.totalModels }}</div>
            <div class="stat-label">BPMN模型</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon stat-icon--success">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ statistics.totalChecks }}</div>
            <div class="stat-label">符合性检查</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon stat-icon--warning">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ formatScore(statistics.averageScore) }}</div>
            <div class="stat-label">平均符合性</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon stat-icon--info">
            <el-icon><Calendar /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ statistics.recentChecks }}</div>
            <div class="stat-label">本月检查</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 快速操作 -->
    <div v-if="!isError" class="quick-actions">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3 class="section-title">快速操作</h3>
            <el-button
              type="primary"
              :icon="Plus"
              @click="navigateTo('/conformance/models/create')"
            >
              创建BPMN模型
            </el-button>
          </div>
        </template>
        <div class="actions-grid">
          <div
            class="action-item"
            @click="navigateTo('/conformance/models')"
          >
            <div class="action-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="action-content">
              <h4 class="action-title">管理BPMN模型</h4>
              <p class="action-description">上传、编辑和管理流程模型</p>
            </div>
            <div class="action-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>

          <div
            class="action-item"
            @click="navigateTo('/conformance/check')"
          >
            <div class="action-icon">
              <el-icon><Search /></el-icon>
            </div>
            <div class="action-content">
              <h4 class="action-title">执行符合性检查</h4>
              <p class="action-description">对比日志与模型，分析符合性</p>
            </div>
            <div class="action-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>

          <div
            class="action-item"
            @click="navigateTo('/conformance/results')"
          >
            <div class="action-icon">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="action-content">
              <h4 class="action-title">查看分析结果</h4>
              <p class="action-description">浏览历史检查结果和报告</p>
            </div>
            <div class="action-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 最近的检查结果 -->
    <div v-if="!isError" class="recent-results">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3 class="section-title">最近的检查结果</h3>
            <el-button
              text
              type="primary"
              @click="navigateTo('/conformance/results')"
            >
              查看全部
            </el-button>
          </div>
        </template>

        <div v-if="isLoading" class="loading-state">
          <el-skeleton :rows="3" animated />
        </div>

        <div v-else-if="recentResults.length === 0" class="empty-state">
          <el-icon class="empty-icon"><Search /></el-icon>
          <h4 class="empty-title">暂无检查结果</h4>
          <p class="empty-description">开始您的第一次符合性检查</p>
          <el-button
            type="primary"
            @click="navigateTo('/conformance/check')"
          >
            开始检查
          </el-button>
        </div>

        <div v-else class="results-list">
          <div
            v-for="result in recentResults"
            :key="result.id"
            class="result-item"
            @click="navigateTo(`/conformance/results/${result.id}`)"
          >
            <div class="result-info">
              <div class="result-title">
                {{ result.description || `检查结果 #${result.id}` }}
              </div>
              <div class="result-meta">
                <span class="result-date">{{ formatDateTime(result.createdAt) }}</span>
                <span class="result-separator">•</span>
                <span class="result-cases">{{ result.totalCases }} 个案例</span>
              </div>
            </div>
            <div class="result-score">
              <div
                class="score-badge"
                :class="`score-badge--${result.conformanceLevel}`"
              >
                {{ formatScore(result.conformanceScore) }}
              </div>
            </div>
            <div class="result-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Plus,
  Document,
  CircleCheck,
  TrendCharts,
  Calendar,
  ArrowRight,
  Search,
  DataAnalysis
} from '@element-plus/icons-vue'
import { useApi } from '~/utils/api'
import type { ConformanceResult } from '~/types'

// 页面配置
definePageMeta({
  layout: 'default'
})

// 设置页面标题
useHead({
  title: '符合性检查 - ProMax'
})

// 状态
const isLoading = ref(true)
const isError = ref(false)
const errorMessage = ref('')
const statistics = ref({
  totalModels: 0,
  totalChecks: 0,
  averageScore: 0,
  recentChecks: 0
})
const recentResults = ref<ConformanceResult[]>([])

// API 实例
const api = useApi()

// 格式化得分
const formatScore = (score: number): string => {
  return `${Math.round(score * 100)}%`
}

// 格式化日期时间
const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载数据
const loadData = async () => {
  try {
    isLoading.value = true
    isError.value = false
    errorMessage.value = ''

    // 并行获取统计数据和最近结果
    const [statisticsResponse, recentResultsResponse] = await Promise.allSettled([
      // 获取符合性检查统计数据
      api.getGlobalConformanceStatistics(),
      // 获取最近的检查结果
      api.getRecentConformanceResults(5)
    ])

    // 处理统计数据响应
    if (statisticsResponse.status === 'fulfilled') {
      const stats = statisticsResponse.value
      statistics.value = {
        totalModels: stats.totalModels || 0,
        totalChecks: stats.totalChecks || 0,
        averageScore: stats.averageScore || 0,
        recentChecks: stats.recentChecks || 0
      }
    } else {
      console.error('Failed to load statistics:', statisticsResponse.reason)
      // 使用默认值作为降级处理
      statistics.value = {
        totalModels: 0,
        totalChecks: 0,
        averageScore: 0,
        recentChecks: 0
      }
    }

    // 处理最近结果响应
    if (recentResultsResponse.status === 'fulfilled') {
      recentResults.value = recentResultsResponse.value || []
    } else {
      console.error('Failed to load recent results:', recentResultsResponse.reason)
      recentResults.value = []
    }

    // 如果两个请求都失败，显示错误状态
    if (statisticsResponse.status === 'rejected' && recentResultsResponse.status === 'rejected') {
      isError.value = true
      errorMessage.value = '数据加载失败，请稍后重试'
    }

  } catch (error) {
    console.error('Failed to load conformance data:', error)
    isError.value = true
    errorMessage.value = '网络连接异常，请检查网络后重试'

    // 设置默认值作为降级处理
    statistics.value = {
      totalModels: 0,
      totalChecks: 0,
      averageScore: 0,
      recentChecks: 0
    }
    recentResults.value = []
  } finally {
    isLoading.value = false
  }
}

// 重新加载数据
const retryLoad = () => {
  loadData()
}

// 页面加载时获取数据
onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
@use '~/assets/scss/utils/variables' as *;
@use '~/assets/scss/utils/mixins' as *;
@use 'sass:map';

.conformance-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @include respond-to(md) {
    padding: 2rem 2rem;
  }

  @include respond-to-max(sm) {
    padding: 1rem 0.5rem;
  }
}

.page-header {
  max-width: 1400px;
  margin: 0 auto 3rem auto;
  text-align: center;
  animation: fadeInUp 0.6s ease-out;

  .header-content {
    .title {
      font-size: 2.5rem;
      font-weight: map.get($font-weight, extrabold);
      background: linear-gradient(135deg, theme-color(primary, 600), theme-color(primary, 800));
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin: 0 0 spacing(3) 0;
      letter-spacing: -0.025em;

      :global(.dark) & {
        background: linear-gradient(135deg, theme-color(primary, 400), theme-color(primary, 200));
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      @include respond-to-max(sm) {
        font-size: font-size(2xl);
      }
    }

    .description {
      font-size: font-size(lg);
      font-weight: map.get($font-weight, medium);
      color: theme-color(gray, 600);
      margin: 0;
      opacity: 0.8;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;

      :global(.dark) & {
        color: theme-color(gray, 400);
      }
    }
  }
}

.error-state {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  animation: fadeInUp 0.6s ease-out 0.1s both;

  :deep(.el-alert) {
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 15px -3px rgba(239, 68, 68, 0.1), 0 4px 6px -2px rgba(239, 68, 68, 0.05);
    backdrop-filter: blur(10px);
    background: rgba(254, 242, 242, 0.95);

    :global(.dark) & {
      background: rgba(127, 29, 29, 0.95);
      border: 1px solid rgba(185, 28, 28, 0.3);
    }

    .el-alert__content {
      padding: spacing(4);

      .el-alert__title {
        font-weight: map.get($font-weight, semibold);
        margin-bottom: spacing(2);
      }

      p {
        margin: 0 0 spacing(3) 0;
        color: theme-color(error, 700);

        :global(.dark) & {
          color: theme-color(error, 200);
        }
      }

      .el-button {
        border-radius: 8px;
        font-weight: map.get($font-weight, medium);
      }
    }
  }
}

.stats-grid {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  animation: slideInLeft 0.5s ease-out 0.2s both;

  @include respond-to-max(sm) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-card {
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    transition: all 0.3s ease;
    overflow: hidden;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    :global(.dark) & {
      background: rgba(30, 41, 59, 0.95);
      border: 1px solid rgba(71, 85, 105, 0.3);
    }

    :deep(.el-card__body) {
      padding: spacing(6);
    }

    .stat-content {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .stat-icon {
      width: 3.5rem;
      height: 3.5rem;
      border-radius: map.get($border-radius, xl);
      @include center-flex;
      font-size: 1.75rem;
      flex-shrink: 0;

      &--primary {
        background: linear-gradient(135deg, theme-color(primary, 100), theme-color(primary, 200));
        color: theme-color(primary, 600);

        :global(.dark) & {
          background: linear-gradient(135deg, theme-color(primary, 900), theme-color(primary, 800));
          color: theme-color(primary, 400);
        }
      }

      &--success {
        background: linear-gradient(135deg, theme-color(success, 100), theme-color(success, 200));
        color: theme-color(success, 600);

        :global(.dark) & {
          background: linear-gradient(135deg, theme-color(success, 900), theme-color(success, 800));
          color: theme-color(success, 400);
        }
      }

      &--warning {
        background: linear-gradient(135deg, theme-color(warning, 100), theme-color(warning, 200));
        color: theme-color(warning, 600);

        :global(.dark) & {
          background: linear-gradient(135deg, theme-color(warning, 900), theme-color(warning, 800));
          color: theme-color(warning, 400);
        }
      }

      &--info {
        background: linear-gradient(135deg, theme-color(info, 100), theme-color(info, 200));
        color: theme-color(info, 600);

        :global(.dark) & {
          background: linear-gradient(135deg, theme-color(info, 900), theme-color(info, 800));
          color: theme-color(info, 400);
        }
      }
    }

    .stat-info {
      flex: 1;
      min-width: 0;

      .stat-value {
        font-size: font-size(2xl);
        font-weight: map.get($font-weight, bold);
        color: theme-color(gray, 900);
        line-height: 1.2;
        margin-bottom: spacing(1);

        :global(.dark) & {
          color: theme-color(gray, 100);
        }
      }

      .stat-label {
        font-size: font-size(sm);
        font-weight: map.get($font-weight, medium);
        color: theme-color(gray, 600);
        margin: 0;

        :global(.dark) & {
          color: theme-color(gray, 400);
        }
      }
    }
  }
}

.quick-actions {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  animation: slideInUp 0.5s ease-out 0.4s both;

  :deep(.el-card) {
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    overflow: hidden;

    :global(.dark) & {
      background: rgba(30, 41, 59, 0.95);
      border: 1px solid rgba(71, 85, 105, 0.3);
    }

    .el-card__header {
      padding: spacing(6) spacing(6) spacing(3) spacing(6);
      border-bottom: none;
    }

    .el-card__body {
      padding: 1rem spacing(6) spacing(6) spacing(6);
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;

    @include respond-to-max(sm) {
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;
    }

    .section-title {
      font-size: font-size(xl);
      font-weight: map.get($font-weight, bold);
      color: theme-color(gray, 900);
      margin: 0;

      :global(.dark) & {
        color: theme-color(gray, 100);
      }
    }

    :deep(.el-button) {
      border-radius: 12px;
      font-weight: map.get($font-weight, semibold);
      padding: 10px 20px;
      background: linear-gradient(135deg, theme-color(primary, 600), theme-color(primary, 700));
      border: none;
      box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
      transition: all 0.2s ease;
      flex-shrink: 0;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
      }

      :global(.dark) & {
        background: linear-gradient(135deg, theme-color(primary, 500), theme-color(primary, 600));
      }

      @include respond-to-max(sm) {
        width: 100%;
        justify-content: center;
      }

      &[type="text"] {
        background: none;
        box-shadow: none;
        color: theme-color(primary, 600);
        border: 1px solid transparent;

        &:hover {
          background: rgba(59, 130, 246, 0.1);
          transform: translateY(-1px);
          box-shadow: none;
        }

        :global(.dark) & {
          color: theme-color(primary, 400);

          &:hover {
            background: rgba(96, 165, 250, 0.1);
          }
        }
      }
    }
  }

  .actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1rem;

    @include respond-to-max(sm) {
      grid-template-columns: 1fr;
    }

    .action-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1.5rem;
      border: 2px solid theme-color(gray, 200);
      border-radius: map.get($border-radius, xl);
      background-color: white;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(139, 92, 246, 0.05));
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        border-color: theme-color(primary, 300);
        box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.1);
        transform: translateY(-2px);

        &::before {
          opacity: 1;
        }

        .action-arrow {
          color: theme-color(primary, 600);
          transform: translateX(4px);

          :global(.dark) & {
            color: theme-color(primary, 400);
          }
        }
      }

      :global(.dark) & {
        background-color: theme-color(gray, 800);
        border-color: theme-color(gray, 700);

        &:hover {
          border-color: theme-color(primary, 600);
        }
      }

      .action-icon {
        width: 3.5rem;
        height: 3.5rem;
        border-radius: map.get($border-radius, xl);
        background: linear-gradient(135deg, theme-color(primary, 100), theme-color(primary, 200));
        color: theme-color(primary, 600);
        @include center-flex;
        font-size: 1.75rem;
        flex-shrink: 0;
        position: relative;
        z-index: 1;

        :global(.dark) & {
          background: linear-gradient(135deg, theme-color(primary, 900), theme-color(primary, 800));
          color: theme-color(primary, 400);
        }
      }

      .action-content {
        flex: 1;
        min-width: 0;
        position: relative;
        z-index: 1;

        .action-title {
          font-size: font-size(base);
          font-weight: map.get($font-weight, semibold);
          color: theme-color(gray, 900);
          margin: 0 0 0.25rem 0;

          :global(.dark) & {
            color: theme-color(gray, 100);
          }
        }

        .action-description {
          font-size: font-size(sm);
          color: theme-color(gray, 600);
          margin: 0;
          line-height: 1.4;

          :global(.dark) & {
            color: theme-color(gray, 400);
          }
        }
      }

      .action-arrow {
        color: theme-color(gray, 400);
        font-size: 1.25rem;
        transition: all 0.3s ease;
        position: relative;
        z-index: 1;

        :global(.dark) & {
          color: theme-color(gray, 600);
        }
      }
    }
  }
}

.recent-results {
  max-width: 1400px;
  margin: 0 auto;
  animation: slideInUp 0.5s ease-out 0.6s both;

  :deep(.el-card) {
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    overflow: hidden;

    :global(.dark) & {
      background: rgba(30, 41, 59, 0.95);
      border: 1px solid rgba(71, 85, 105, 0.3);
    }

    .el-card__header {
      padding: spacing(6) spacing(6) spacing(3) spacing(6);
      border-bottom: none;
    }

    .el-card__body {
      padding: 1rem spacing(6) spacing(6) spacing(6);
    }
  }

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 1rem;
    }

    .section-header :deep(.el-button) {
      margin-left: auto;
    }




  .loading-state {
    text-align: center;
    padding: 3rem 1rem;

    :deep(.el-skeleton) {
      .el-skeleton__item {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;

        :global(.dark) & {
          background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
          background-size: 200% 100%;
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 4rem 1rem;

    .empty-icon {
      width: 4rem;
      height: 4rem;
      color: theme-color(gray, 300);
      margin: 0 auto 1.5rem;

      :global(.dark) & {
        color: theme-color(gray, 600);
      }
    }

    .empty-title {
      font-size: font-size(xl);
      font-weight: map.get($font-weight, semibold);
      color: theme-color(gray, 900);
      margin: 0 0 0.5rem 0;

      :global(.dark) & {
        color: theme-color(gray, 100);
      }
    }

    .empty-description {
      color: theme-color(gray, 600);
      font-size: font-size(base);
      margin: 0 0 2rem 0;

      :global(.dark) & {
        color: theme-color(gray, 400);
      }
    }

    :deep(.el-button) {
      border-radius: 12px;
      font-weight: map.get($font-weight, semibold);
      padding: 12px 24px;
      background: linear-gradient(135deg, theme-color(primary, 600), theme-color(primary, 700));
      border: none;
      box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
      }

      :global(.dark) & {
        background: linear-gradient(135deg, theme-color(primary, 500), theme-color(primary, 600));
      }
    }
  }

  .results-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;

    .result-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1.25rem;
      border: 1px solid theme-color(gray, 200);
      border-radius: map.get($border-radius, lg);
      background-color: white;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        border-color: theme-color(primary, 300);
        box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1);
        transform: translateY(-1px);

        .result-arrow {
          color: theme-color(primary, 600);
          transform: translateX(2px);

          :global(.dark) & {
            color: theme-color(primary, 400);
          }
        }
      }

      :global(.dark) & {
        background-color: theme-color(gray, 800);
        border-color: theme-color(gray, 700);

        &:hover {
          border-color: theme-color(primary, 600);
        }
      }

      .result-info {
        flex: 1;
        min-width: 0;

        .result-title {
          font-size: font-size(base);
          font-weight: map.get($font-weight, medium);
          color: theme-color(gray, 900);
          margin: 0 0 0.25rem 0;
          @include text-truncate;

          :global(.dark) & {
            color: theme-color(gray, 100);
          }
        }

        .result-meta {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: font-size(sm);
          color: theme-color(gray, 600);

          :global(.dark) & {
            color: theme-color(gray, 400);
          }

          .result-separator {
            color: theme-color(gray, 400);

            :global(.dark) & {
              color: theme-color(gray, 600);
            }
          }
        }
      }

      .result-score {
        flex-shrink: 0;

        .score-badge {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          min-width: 3.5rem;
          height: 2rem;
          padding: 0 0.75rem;
          border-radius: map.get($border-radius, full);
          font-size: font-size(sm);
          font-weight: map.get($font-weight, semibold);
          border: 1px solid;

          &--excellent {
            background-color: theme-color(success, 100);
            color: theme-color(success, 800);
            border-color: theme-color(success, 200);

            :global(.dark) & {
              background-color: theme-color(success, 900);
              color: theme-color(success, 200);
              border-color: theme-color(success, 700);
            }
          }

          &--good {
            background-color: theme-color(success, 100);
            color: theme-color(success, 700);
            border-color: theme-color(success, 200);

            :global(.dark) & {
              background-color: theme-color(success, 900);
              color: theme-color(success, 300);
              border-color: theme-color(success, 700);
            }
          }

          &--fair {
            background-color: theme-color(warning, 100);
            color: theme-color(warning, 800);
            border-color: theme-color(warning, 200);

            :global(.dark) & {
              background-color: theme-color(warning, 900);
              color: theme-color(warning, 200);
              border-color: theme-color(warning, 700);
            }
          }

          &--poor {
            background-color: theme-color(error, 100);
            color: theme-color(error, 800);
            border-color: theme-color(error, 200);

            :global(.dark) & {
              background-color: theme-color(error, 900);
              color: theme-color(error, 200);
              border-color: theme-color(error, 700);
            }
          }
        }
      }

      .result-arrow {
        color: theme-color(gray, 400);
        font-size: 1.25rem;
        transition: all 0.3s ease;
        flex-shrink: 0;

        :global(.dark) & {
          color: theme-color(gray, 600);
        }
      }
    }
  }
}

// 动画定义
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 响应式优化
@include respond-to-max(sm) {
  .conformance-page {
    padding: 1rem 0.5rem;
  }

  .page-header {
    margin-bottom: 2rem;

    .header-content .title {
      font-size: font-size(2xl);
    }
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .quick-actions .actions-grid {
    grid-template-columns: 1fr;
  }

  .recent-results {
    .results-list .result-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.75rem;

      .result-info {
        width: 100%;
      }

      .result-score {
        align-self: flex-end;
      }

      .result-arrow {
        align-self: center;
        transform: rotate(90deg);
      }
    }
  }
}
</style>
