<template>
  <div class="discovery-edit-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <el-button
            link
            :icon="ArrowLeft"
            class="back-button"
            @click="handleBack"
          />
          <div>
            <h1 class="title">编辑流程发现结果</h1>
            <p class="description">基于流程发现结果编辑流程模型，用于符合性检查</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="dataManager.isLoading.value" class="loading-section">
      <el-card>
        <el-skeleton :rows="8" animated />
      </el-card>
    </div>

    <!-- 编辑内容 -->
    <div v-else class="edit-section">
      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <!-- 撤销重做按钮 -->
          <el-button-group>
            <el-tooltip content="撤销" :disabled="undoRedo.canUndo.value">
              <el-button
                :disabled="!undoRedo.canUndo.value"
                @click="undoRedo.undo">
                <el-icon><RefreshLeft /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="重做" :disabled="undoRedo.canRedo.value">
              <el-button
                :disabled="!undoRedo.canRedo.value"
                @click="undoRedo.redo">
                <el-icon><RefreshRight /></el-icon>
              </el-button>
            </el-tooltip>
          </el-button-group>
        </div>

        <div class="toolbar-right">
          <el-tooltip
            :content="isProcessEmpty ? '当前流程为空，无法保存' : !isProcessConnected ? `当前流程不连通，无法保存：${getConnectivityErrorMessage}` : ''"
            :disabled="!isProcessEmpty && isProcessConnected">
            <el-button
              type="primary"
              :disabled="isProcessEmpty || !isProcessConnected"
              @click="showSaveDialog">
              保存模型
            </el-button>
          </el-tooltip>
          <el-button @click="handleBack">
            取消
          </el-button>
        </div>
      </div>

      <!-- 保存模型对话框 -->
      <el-dialog
        v-model="saveDialogVisible"
        title="保存流程模型"
        width="500px"
        :close-on-click-modal="false"
      >
        <el-form
          ref="saveFormRef"
          :model="saveForm"
          :rules="saveFormRules"
          label-width="80px"
        >
          <el-form-item label="模型名称" prop="name">
            <el-input
              v-model="saveForm.name"
              placeholder="请输入模型名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="模型描述" prop="description">
            <el-input
              v-model="saveForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入模型描述（可选）"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="模型类型" prop="modelType">
            <el-select v-model="saveForm.modelType" placeholder="请选择模型类型">
              <el-option label="发现模型" value="discovered" />
              <el-option label="参考模型" value="reference" />
              <el-option label="规范模型" value="normative" />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="saveDialogVisible = false">取消</el-button>
            <el-button
              type="primary"
              :loading="dataManager.isSaving.value"
              :disabled="!isProcessConnected"
              @click="handleSaveConfirm"
            >
              保存
            </el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 可编辑的DFG流程图 -->
      <div class="main-content">
        <el-card class="editor-card">
          <template #header>
            <div class="dfg-header">
              <div class="dfg-title-section">
                <h3 class="dfg-title">流程发现结果编辑</h3>
                <div class="edit-tips">
                  <el-tooltip placement="bottom" effect="light">
                    <template #content>
                      <div class="tips-content">
                        <div class="tip-item">
                          <strong>节点编辑：</strong>
                          <ul>
                            <li>右键点击节点：显示节点操作菜单</li>
                            <li>双击节点文本：直接编辑节点名称</li>
                            <li>拖拽节点：创建节点间连接</li>
                          </ul>
                        </div>
                        <div class="tip-item">
                          <strong>连接线编辑：</strong>
                          <ul>
                            <li>右键点击连接线：显示连接线操作菜单</li>
                            <li>拖拽连接点：重新连接节点</li>
                          </ul>
                        </div>
                      </div>
                    </template>
                    <el-icon class="help-icon">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
              </div>
              <div class="dfg-controls">
                <!-- 维度切换控制 -->
                <div class="dimension-controls">
                  <el-radio-group
                    v-model="currentDimension"
                    size="small"
                    :disabled="!originalDiscoveryData"
                    @change="onDimensionChange">
                    <el-radio-button value="frequency">频率</el-radio-button>
                    <el-radio-button value="duration">耗时</el-radio-button>
                  </el-radio-group>
                </div>

                <!-- 分隔线 -->
                <div class="control-divider" />

                <!-- 缩放控制 -->
                <el-button
                  size="small"
                  :disabled="!originalDiscoveryData"
                  :icon="Plus"
                  title="放大 (Ctrl/Cmd + +)"
                  @click="zoomIn">
                  放大
                </el-button>
                <el-button
                  size="small"
                  :disabled="!originalDiscoveryData"
                  :icon="Minus"
                  title="缩小 (Ctrl/Cmd + -)"
                  @click="zoomOut">
                  缩小
                </el-button>
                <el-button
                  size="small"
                  :disabled="!originalDiscoveryData"
                  :icon="RefreshRight"
                  title="重置缩放 (Ctrl/Cmd + 0)"
                  @click="resetZoom">
                  重置
                </el-button>
                <el-button
                  size="small"
                  :disabled="!originalDiscoveryData"
                  :icon="FullScreen"
                  title="适应屏幕"
                  @click="fitToScreen">
                  适应屏幕
                </el-button>
                <div v-if="originalDiscoveryData && diagram" class="zoom-indicator">
                  {{ Math.round(currentZoomScale * 100) }}%
                </div>
              </div>
            </div>
          </template>

          <div class="visual-editor">
            <div class="diagram-container">
              <!-- DFG画布 -->
              <div ref="dfgContainer" class="dfg-canvas" />

              <!-- 加载状态 -->
              <div v-if="dataManager.isLoading.value" class="dfg-loading-overlay">
                <div class="dfg-loading-content">
                  <div class="loading-spinner" />
                  <p class="dfg-loading-text">正在生成流程图...</p>
                </div>
              </div>

              <!-- 图表说明 -->
              <div v-if="originalDiscoveryData" class="dfg-info">
                <div class="dfg-info-content" @click="showChartLegend = true">
                  <el-icon class="dfg-info-icon">
                    <InfoFilled />
                  </el-icon>
                  <div class="dfg-info-text">
                    <p class="dfg-info-title">图表说明</p>
                  </div>
                  <el-icon class="dfg-info-arrow">
                    <ArrowRight />
                  </el-icon>
                </div>
              </div>


            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 上下文菜单 -->
    <div
      v-show="contextMenu.contextMenu.value.visible"
      class="context-menu"
      :style="{
        left: contextMenu.contextMenu.value.x + 'px',
        top: contextMenu.contextMenu.value.y + 'px'
      }"
      @click.stop
    >
      <!-- 节点菜单 -->
      <div v-if="contextMenu.contextMenu.value.type === 'node'" class="menu-items">
        <div class="menu-item" @click="dfgOperations.addParallelPath">
          <el-icon><Plus /></el-icon>
          <span>添加并行路径</span>
        </div>
        <div class="menu-item" @click="dfgOperations.addSelfLoop">
          <el-icon><RefreshRight /></el-icon>
          <span>添加自循环</span>
        </div>
        <div class="menu-divider" />
        <div class="menu-item danger" @click="dfgOperations.deleteElement">
          <el-icon><Minus /></el-icon>
          <span>删除节点</span>
        </div>
      </div>

      <!-- 连接线菜单 -->
      <div v-if="contextMenu.contextMenu.value.type === 'link'" class="menu-items">
        <div class="menu-item" @click="dfgOperations.insertActivity">
          <el-icon><Plus /></el-icon>
          <span>插入活动</span>
        </div>
        <div class="menu-divider" />
        <div class="menu-item danger" @click="dfgOperations.deleteConnection">
          <el-icon><Minus /></el-icon>
          <span>删除连接</span>
        </div>
      </div>
    </div>

    <!-- 路径筛选对话框 -->
    <el-dialog
      v-model="filterDialogVisible"
      :title="isFirstTimeFilter ? '设置路径筛选条件' : '路径筛选'"
      width="400px"
      :before-close="handleFilterDialogClose"
      :close-on-click-modal="!isFirstTimeFilter"
      :close-on-press-escape="!isFirstTimeFilter"
      :show-close="!isFirstTimeFilter"
      center
    >
      <div class="filter-dialog-content">
        <div class="filter-section">
          <div class="section-title">
            <el-icon><Filter /></el-icon>
            <span>频次范围</span>
          </div>

          <div class="frequency-display">
            <span class="frequency-label">当前范围:</span>
            <span class="frequency-value">{{ tempPathFrequencyRange[0] }} - {{ tempPathFrequencyRange[1] }}</span>
          </div>

          <div class="slider-container">
            <el-slider
              v-model="tempPathFrequencyRange"
              range
              :min="minPathFrequency"
              :max="maxPathFrequency"
              :step="1"
              :show-tooltip="true"
              :format-tooltip="formatTooltip"
              size="default"
            />
          </div>


        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetTempFilter">重置</el-button>
          <el-button v-if="!isFirstTimeFilter" @click="handleFilterDialogClose">取消</el-button>
          <el-button type="primary" @click="applyFilterAndClose">
            {{ isFirstTimeFilter ? '开始使用' : '确定' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft, Plus, Minus, RefreshRight, FullScreen, InfoFilled, RefreshLeft, ArrowRight, Filter, QuestionFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useDiscoveryData } from '~/composables/useDiscoveryData'
import { useContextMenu } from '~/composables/useContextMenu'
import { useDfgOperations } from '~/composables/useDfgOperations'
import { useUndoRedo } from '~/composables/useUndoRedo'
import { useApi } from '~/utils/api'
import type { ConformanceCheckRequest, DFGResult } from '~/types'
import * as go from 'gojs'

// 页面配置
definePageMeta({
  layout: 'default'
})

// 设置页面标题
useHead({
  title: '编辑流程发现结果 - ProMax'
})

// 获取路由和颜色模式
const router = useRouter()
const route = useRoute()

// 使用组合式函数
const dataManager = useDiscoveryData()
const api = useApi()

// 创建空的DFG数据结构
const createEmptyDFGResult = (): DFGResult => ({
  nodes: [],
  edges: [],
  statistics: {
    totalCases: 0,
    totalActivities: 0,
    avgCaseDuration: 0,
    startActivities: [],
    endActivities: []
  }
})

// 路径筛选相关状态
const pathFrequencyRange = ref<[number, number]>([1, 100])
const tempPathFrequencyRange = ref<[number, number]>([1, 100])  // 对话框中的临时值
const minPathFrequency = ref(1)
const maxPathFrequency = ref(100)
const totalPathCount = ref(0)
const filteredPathCount = ref(0)
const originalDiscoveryData = ref<DFGResult | null>(null)
const currentDiscoveryData = ref<DFGResult | null>(null)
const isProcessEmpty = ref(false)  // 标记当前流程是否为空
const isProcessConnected = ref(true)  // 标记当前流程是否连通
const connectivityError = ref<string | null>(null)  // 连通性错误信息
const filterDialogVisible = ref(false)  // 筛选对话框是否显示
const isFirstTimeFilter = ref(true)  // 标记是否是首次筛选

// DFG图表相关状态
const dfgContainer = ref<HTMLElement>()
const currentDimension = ref<'frequency' | 'duration'>('frequency')
let diagram: go.Diagram | null = null
const showChartLegend = ref(false)
const shouldRecreateModel = ref(true) // 控制是否需要重新创建模型
const currentZoomScale = ref(1.0)

// DFG交互状态
const highlightedNodeKey = ref<string>('')
const highlightedLinks = ref<Set<string>>(new Set())
const isHighlightMode = ref(false)

// 上下文菜单和操作状态
const selectedElement = ref(null)
const contextMenu = useContextMenu()

// 撤销重做功能
const undoRedo = useUndoRedo({
  diagramRef: () => ({ diagram }),
  onStateChange: () => {
    // 状态变化时更新连通性
    updateConnectivityFromCurrentModel()
  },
  maxHistorySize: 50
})

// 状态跟踪器 - 用于记录用户手动操作的撤销重做
let lastModelState: { nodes: any[], links: any[] } | null = null

// 记录当前模型状态
const recordCurrentModelState = () => {
  const diagram = getDiagram()
  if (diagram) {
    const model = diagram.model as go.GraphLinksModel
    lastModelState = {
      nodes: model.nodeDataArray.map(node => ({ ...node })),
      links: model.linkDataArray.map(link => ({ ...link }))
    }
  }
}

const dfgOperations = useDfgOperations({
  diagramRef: computed(() => ({ diagram })),
  selectedElement,
  contextMenuTarget: computed(() => contextMenu.contextMenu.value.target),
  onOperationComplete: () => {
    contextMenu.hideContextMenu()
    selectedElement.value = null
  },
  updateConnectivity: () => {
    // 从当前GoJS模型更新连通性状态
    console.log('触发连通性检查 - 来自dfgOperations')
    updateConnectivityFromCurrentModel()
  },
  refreshData: () => {
    // 刷新DFG数据，但不重新创建模型（保持用户编辑）
    if (diagram) {
      // 不设置shouldRecreateModel.value = true，保持现有模型
      console.log('刷新DFG显示，保持用户编辑')
      diagram.layoutDiagram(true)
    }
  },
  // 撤销重做支持
  undoRedo: {
    recordAddNode: undoRedo.recordAddNode,
    recordDeleteNode: undoRedo.recordDeleteNode,
    recordAddLink: undoRedo.recordAddLink,
    recordDeleteLink: undoRedo.recordDeleteLink,
    recordBatchOperation: undoRedo.recordBatchOperation,
    getCurrentState: undoRedo.getCurrentState
  }
})

// 保存对话框相关状态
const saveDialogVisible = ref(false)
const saveFormRef = ref()
const saveForm = ref({
  name: '',
  description: '',
  modelType: 'discovered' as 'reference' | 'discovered' | 'normative'
})

// 表单验证规则
const saveFormRules = {
  name: [
    { required: true, message: '请输入模型名称', trigger: 'blur' },
    { min: 1, max: 100, message: '模型名称长度应在 1 到 100 个字符', trigger: 'blur' }
  ]
}

// 等待容器可用的辅助函数
const waitForContainer = async (maxAttempts = 10): Promise<boolean> => {
  for (let i = 0; i < maxAttempts; i++) {
    if (dfgContainer.value) {
      console.log(`Container found after ${i + 1} attempts`)
      return true
    }
    console.log(`Attempt ${i + 1}: Container not found, waiting...`)
    await new Promise(resolve => setTimeout(resolve, 100))
    await nextTick()
  }
  console.error('Container not found after maximum attempts')
  return false
}

// 格式化耗时显示
const formatDuration = (milliseconds: number): string => {
  if (!milliseconds || milliseconds <= 0) {
    return '0秒'
  }

  if (milliseconds < 1000) {
    return '<1秒'
  } else if (milliseconds < 60000) {
    const seconds = Math.round(milliseconds / 1000)
    return `${seconds}秒`
  } else if (milliseconds < 3600000) {
    const minutes = Math.round(milliseconds / 60000)
    return `${minutes}分钟`
  } else if (milliseconds < 86400000) {
    const hours = Math.round(milliseconds / 3600000)
    return `${hours}小时`
  } else {
    const days = Math.round(milliseconds / 86400000)
    return `${days}天`
  }
}

// 数据驱动的颜色映射算法（专业流程挖掘标准）
const calculateDataDistribution = (nodes: any[], edges: any[], dimension: 'frequency' | 'duration' = 'frequency') => {
  let nodeValues: number[] = []
  let edgeValues: number[] = []

  if (dimension === 'frequency') {
    nodeValues = nodes.map(n => n.frequency || 0).filter(f => f > 0)
    edgeValues = edges.map(e => e.frequency || 0).filter(f => f > 0)
  } else {
    // 耗时维度：使用平均耗时（毫秒转换为分钟）
    nodeValues = nodes.map(n => (n.avgDuration || 0) / 60000).filter(d => d > 0)
    edgeValues = edges.map(e => (e.avgDuration || 0) / 60000).filter(d => d > 0)
  }

  if (nodeValues.length === 0 && edgeValues.length === 0) {
    return {
      nodePercentiles: { p25: 0, p50: 0, p75: 0, p90: 0, max: 0, logScale: false },
      edgePercentiles: { p25: 0, p50: 0, p75: 0, p90: 0, max: 0, logScale: false }
    }
  }

  const calculateAdvancedPercentiles = (values: number[]) => {
    if (values.length === 0) return { p25: 0, p50: 0, p75: 0, p90: 0, max: 0, logScale: false }

    const sorted = [...values].sort((a, b) => a - b)
    const min = sorted[0]
    const max = sorted[sorted.length - 1]

    // 检查是否需要对数缩放（数据跨度超过2个数量级）
    const needsLogScale = dimension === 'duration' && max > 0 && min > 0 && (max / min) > 100

    let processedValues = sorted
    if (needsLogScale) {
      // 对数变换以处理高度偏斜的耗时数据
      processedValues = sorted.map(v => Math.log10(Math.max(v, 0.1))) // 避免log(0)
    }

    // 使用更精确的百分位数计算
    const getPercentile = (arr: number[], p: number) => {
      const index = (arr.length - 1) * p
      const lower = Math.floor(index)
      const upper = Math.ceil(index)
      const weight = index % 1

      if (upper >= arr.length) return arr[arr.length - 1]
      return arr[lower] * (1 - weight) + arr[upper] * weight
    }

    const percentiles = {
      p25: getPercentile(processedValues, 0.25),
      p50: getPercentile(processedValues, 0.5),
      p75: getPercentile(processedValues, 0.75),
      p90: getPercentile(processedValues, 0.9),
      max: processedValues[processedValues.length - 1],
      logScale: needsLogScale,
      originalMax: max,
      originalMin: min
    }

    // 如果使用了对数缩放，需要将百分位数转换回原始尺度
    if (needsLogScale) {
      return {
        ...percentiles,
        p25: Math.pow(10, percentiles.p25),
        p50: Math.pow(10, percentiles.p50),
        p75: Math.pow(10, percentiles.p75),
        p90: Math.pow(10, percentiles.p90),
        max: Math.pow(10, percentiles.max)
      }
    }

    return percentiles
  }

  return {
    nodePercentiles: calculateAdvancedPercentiles(nodeValues),
    edgePercentiles: calculateAdvancedPercentiles(edgeValues)
  }
}

// 初始化DFG图表
const initializeDFG = async () => {
  console.log('Initializing DFG...')

  if (!await waitForContainer()) {
    console.error('Failed to initialize DFG: container not available')
    return
  }

  try {
    const $ = go.GraphObject.make

    // 创建GoJS图表实例
    diagram = new go.Diagram(dfgContainer.value!, {
      'undoManager.isEnabled': true,
      'toolManager.hoverDelay': 100,
      'toolManager.toolTipDuration': 10000,
      // 禁用默认右键菜单工具
      'contextMenuTool.isEnabled': false,
      layout: $(go.LayeredDigraphLayout, {
        direction: 90,
        layerSpacing: 80,
        columnSpacing: 50,
        setsPortSpots: false,
        packOption: go.LayeredDigraphLayout.PackStraighten
      }),
      initialContentAlignment: go.Spot.Center,
      // 启用鼠标滚轮缩放
      'toolManager.mouseWheelBehavior': go.WheelMode.Zoom,
      // 启用动画管理器
      'animationManager.isEnabled': true,
      allowDrop: true,
      allowTextEdit: true,
      allowMove: true,
      allowCopy: false,
      allowDelete: false,
      allowSelect: true,
      allowLink: true,  // 启用连接功能
      allowRelink: true, // 启用重新连接功能
      // 设置缩放范围
      minScale: 0.1,
      maxScale: 5.0,
      // 设置图表边距
      padding: new go.Margin(100, 100, 100, 100),
      // 设置背景点击行为
      hasHorizontalScrollbar: false,
      hasVerticalScrollbar: false,
      // 启用网格背景
      'grid.visible': true,
      'grid.gridCellSize': new go.Size(20, 20)
    })

    // 设置节点模板
    diagram.nodeTemplate = createNodeTemplate()

    // 设置连接线模板
    diagram.linkTemplate = createLinkTemplate()

    // 添加事件监听器
    diagram.addDiagramListener('ObjectSingleClicked', handleDFGNodeClick)
    diagram.addDiagramListener('BackgroundSingleClicked', clearHighlight)

    // 添加上下文菜单事件监听器
    diagram.addDiagramListener('ObjectContextClicked', handleContextMenu)
    diagram.addDiagramListener('BackgroundContextClicked', () => {
      contextMenu.hideContextMenu()
    })

    // 添加删除事件监听器
    diagram.addDiagramListener('SelectionDeleting', handleSelectionDeleting)

    // 添加缩放变化监听器
    diagram.addDiagramListener('ViewportBoundsChanged', updateZoomIndicator)

    // 初始化缩放指示器
    currentZoomScale.value = diagram.scale

    // 注意：我们使用LinkingTool和SelectionDeleting事件来监听用户操作
    // 不再使用通用的ModelChangedListener，因为它会捕获所有变化

    // 配置连接工具并监听连接事件
    setupLinkingTool(diagram)

    // 确保连接工具可用
    diagram.toolManager.linkingTool.isEnabled = true
    diagram.toolManager.relinkingTool.isEnabled = true

    // 阻止图表容器的默认右键菜单
    const diagramDiv = diagram.div
    if (diagramDiv) {
      diagramDiv.addEventListener('contextmenu', (e: Event) => {
        e.preventDefault()
        e.stopPropagation()
      })
    }

    console.log('DFG initialized successfully')

    // 如果有数据，立即渲染
    if (originalDiscoveryData.value) {
      await renderDFG()
      // 记录初始状态
      setTimeout(() => {
        recordCurrentModelState()
      }, 100)
    }
  } catch (error) {
    console.error('Error initializing DFG:', error)
  }
}

// 创建节点模板
const createNodeTemplate = () => {
  const $ = go.GraphObject.make

  return $(go.Node, 'Auto',
    {
      locationSpot: go.Spot.Center,
      selectable: true,
      shadowVisible: true,
      shadowColor: 'rgba(0, 0, 0, 0.3)',
      shadowOffset: new go.Point(2, 2),
      shadowBlur: 4,
      // 启用连接功能 - 使用正确的GoJS属性
      fromLinkable: true,
      toLinkable: true,
      fromLinkableSelfNode: true,
      toLinkableSelfNode: true,
      fromLinkableDuplicates: true,
      toLinkableDuplicates: true,
      // 设置连接点
      fromSpot: go.Spot.AllSides,
      toSpot: go.Spot.AllSides
    },
    new go.Binding('location', 'loc', go.Point.parse).makeTwoWay(go.Point.stringify),

    // 节点形状 - 使用数据驱动的动态样式
    $(go.Shape, {
      name: 'BODY',
      fill: '#4CAF50',
      stroke: '#388E3C',
      strokeWidth: 2,
      parameter1: 8, // 圆角半径
      portId: '',
      fromLinkable: true,
      toLinkable: true,
      fromSpot: go.Spot.AllSides,
      toSpot: go.Spot.AllSides
    },
      // 动态形状绑定
      new go.Binding('figure', '', () => {
        return 'RoundedRectangle'
      }),
      // 动态圆角半径绑定
      new go.Binding('parameter1', '', (data) => {
        if (data.isStartNode || data.isEndNode) {
          return 22 // 开始和结束节点使用大圆角半径
        }
        return 8 // 普通节点使用标准圆角
      }),
      // 动态样式绑定
      new go.Binding('fill', '', (data) => {
        if (!data.nodeStyle) return '#4CAF50'
        return data.nodeStyle.fill
      }),
      new go.Binding('stroke', '', (data) => {
        if (!data.nodeStyle) return '#388E3C'
        return data.nodeStyle.stroke
      }),
      new go.Binding('strokeWidth', '', (data) => {
        if (!data.nodeStyle) return 2
        return data.nodeStyle.strokeWidth
      }),
      // 让节点大小主要由内容决定
      new go.Binding('minSize', '', (data) => {
        if (data.isStartNode || data.isEndNode) {
          return new go.Size(80, 50) // 开始和结束节点最小尺寸
        }
        return new go.Size(60, 30) // 普通节点最小尺寸
      })),

    // 节点文本
    $(go.Panel, 'Vertical',
      // 主标签
      $(go.TextBlock, {
        font: 'bold 13px "Segoe UI", Arial, sans-serif',
        margin: new go.Margin(6, 8, 2, 8),
        wrap: go.Wrap.Fit,
        editable: true,
        textAlign: 'center',
        stroke: '#FFFFFF'
      },
        new go.Binding('text', 'text').makeTwoWay(),
        new go.Binding('stroke', '', (data: any) => {
          if (!data.nodeStyle) return '#FFFFFF'
          return data.nodeStyle.textColor
        }),
        // 开始和结束节点使用更小的字体
        new go.Binding('font', '', (data: any) => {
          if (data.isStartNode || data.isEndNode) {
            return 'bold 11px "Segoe UI", Arial, sans-serif'
          }
          return 'bold 13px "Segoe UI", Arial, sans-serif'
        }),
        // 设置文字最大宽度，根据节点类型和数据驱动的样式
        new go.Binding('maxSize', '', (data: any) => {
          let maxWidth = 120 // 默认最大宽度

          if (data.isStartNode || data.isEndNode) {
            maxWidth = 64 // 开始和结束节点较小的文字宽度
          } else if (data.nodeStyle && data.nodeStyle.width) {
            // 如果有样式数据，使用样式中的宽度作为参考
            const styleWidth = data.nodeStyle.width
            if (Number.isFinite(styleWidth) && styleWidth > 0) {
              maxWidth = Math.max(60, Math.min(180, styleWidth - 16))
            }
          }

          return new go.Size(maxWidth, NaN)
        }))
    )
  )
}

// 创建连接线模板
const createLinkTemplate = () => {
  const $ = go.GraphObject.make

  return $(go.Link, {
    routing: go.Routing.Orthogonal, // 使用正交路由（直角折线）
    curve: go.Curve.JumpOver, // 线条交叉时跳跃效果
    corner: 6, // 设置拐角圆角半径
    selectable: true,
    cursor: 'pointer',
    shadowVisible: true,
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOffset: new go.Point(1, 1),
    shadowBlur: 2,
    // 设置连线与节点的连接 - 保持适当距离
    fromSpot: go.Spot.AllSides,
    toSpot: go.Spot.AllSides,
    fromEndSegmentLength: 15, // 起点与节点保持15px距离
    toEndSegmentLength: 15,   // 终点与节点保持15px距离
    relinkableFrom: true,
    relinkableTo: true,
    reshapable: true,
    resegmentable: true
  },
    // 连接线形状 - 使用数据驱动的动态样式
    $(go.Shape, {
      name: 'LINKSHAPE',
      strokeWidth: 4,
      stroke: '#2196F3',
      strokeCap: 'round'
    },
      // 动态样式绑定
      new go.Binding('stroke', '', (data: any) => {
        if (!data.linkStyle) return '#2196F3'
        return data.linkStyle.stroke || '#2196F3'
      }),
      new go.Binding('strokeWidth', '', (data: any) => {
        if (!data.linkStyle) return 4
        // 确保线宽值是有效数值，防止NaN导致的GoJS错误
        const strokeWidth = data.linkStyle.strokeWidth
        return Number.isFinite(strokeWidth) && strokeWidth > 0 ? Math.max(1, Math.min(15, strokeWidth)) : 4
      })),

    // 箭头 - 使用默认定位以确保在正交路由中正确显示
    $(go.Shape, {
      name: 'ARROWSHAPE',
      toArrow: 'Standard',
      fill: '#2196F3',
      stroke: '#2196F3',
      strokeWidth: 1,
      scale: 1.6
    },
      new go.Binding('fill', '', (data: any) => {
        if (!data.linkStyle) return '#2196F3'
        return data.linkStyle.stroke
      }),
      new go.Binding('stroke', '', (data: any) => {
        if (!data.linkStyle) return '#2196F3'
        return data.linkStyle.stroke
      }),
      new go.Binding('scale', '', (data: any) => {
        if (!data.linkStyle) return 1.6
        // 确保箭头缩放值是有效数值，防止NaN导致的GoJS错误
        const arrowScale = data.linkStyle.arrowScale
        return Number.isFinite(arrowScale) && arrowScale > 0 ? Math.max(0.5, Math.min(3.0, arrowScale)) : 1.6
      })),

    // 连接线标签 - 改进的样式
    $(go.Panel, 'Auto',
      $(go.Shape, 'RoundedRectangle', {
        fill: 'rgba(255, 255, 255, 0.95)',
        stroke: 'rgba(0, 0, 0, 0.1)',
        strokeWidth: 1,
        parameter1: 4
      }),
      $(go.TextBlock, {
        font: 'bold 10px "Segoe UI", Arial, sans-serif',
        margin: new go.Margin(3, 6, 3, 6),
        stroke: '#333',
        textAlign: 'center',
        editable: true
      },
        // 优先显示label，如果没有则显示frequency
        new go.Binding('text', '', (data: any) => {
          if (data.label) return data.label
          if (data.frequency) return String(data.frequency)
          return '1' // 默认显示1
        }).makeTwoWay((text: string, data: any) => {
          // 双向绑定：当用户编辑文本时，同时更新label和frequency
          const numValue = parseInt(text) || 1
          data.label = text
          data.frequency = numValue
          return text
        }),
        // 显示条件：有label或showLabel为true，或者有frequency
        new go.Binding('visible', '', (data: any) => {
          return data.showLabel === true || data.label || data.frequency > 0
        }))
    )
  )
}

// 渲染DFG图表
const renderDFG = async () => {
  if (!diagram || !originalDiscoveryData.value) {
    console.log('Cannot render DFG: diagram or data not available')
    return
  }

  try {
    const data = currentDiscoveryData.value || originalDiscoveryData.value
    const { nodes, edges } = data

    if (!nodes || !edges) {
      console.log('No nodes or edges to render')
      return
    }

    console.log('=== RENDER DFG DEBUG ===')
    console.log('Rendering nodes:', nodes.length)
    console.log('Rendering edges:', edges.length)

    // 分析要渲染的开始和结束节点
    const renderStartNodes = nodes.filter((node: any) => {
      const nodeId = String(node.id || '')
      return node.isStartNode || nodeId === '开始' || nodeId.startsWith('开始_')
    })
    const renderEndNodes = nodes.filter((node: any) => {
      const nodeId = String(node.id || '')
      return node.isEndNode || nodeId === '结束' || nodeId.startsWith('结束_')
    })

    console.log('Rendering start nodes:', renderStartNodes.map(n => ({ id: n.id, label: n.label, isStartNode: n.isStartNode })))
    console.log('Rendering end nodes:', renderEndNodes.map(n => ({ id: n.id, label: n.label, isEndNode: n.isEndNode })))

    // 计算数据分布用于颜色映射
    const { nodePercentiles, edgePercentiles } = calculateDataDistribution(nodes, edges, currentDimension.value)

    // 节点去重处理 - 解决重复ID问题（如多个"结束"节点）
    const uniqueNodesMap = new Map()
    nodes.forEach((node: any) => {
      const nodeId = String(node.id || '')
      if (!uniqueNodesMap.has(nodeId)) {
        uniqueNodesMap.set(nodeId, node)
      } else {
        // 如果有重复节点，合并频率数据，保留isEndNode/isStartNode标记
        const existingNode = uniqueNodesMap.get(nodeId)
        const mergedNode = {
          ...existingNode,
          frequency: (existingNode.frequency || 0) + (node.frequency || 0),
          // 保留重要的节点类型标记
          isStartNode: existingNode.isStartNode || node.isStartNode,
          isEndNode: existingNode.isEndNode || node.isEndNode
        }
        uniqueNodesMap.set(nodeId, mergedNode)
        console.log(`合并重复节点: ${nodeId}, 合并后频率: ${mergedNode.frequency}`)
      }
    })
    const uniqueNodes = Array.from(uniqueNodesMap.values())

    // 处理节点数据 - 与discover.vue完全一致的数据结构
    const processedNodes = uniqueNodes.map((node: any) => {
      // 数据验证和清理 - 防止NaN值导致的GoJS错误
      const safeFrequency = Number.isFinite(node.frequency) ? node.frequency : 0
      const safeAvgDuration = Number.isFinite(node.avgDuration) ? node.avgDuration : 0
      const safeMinDuration = Number.isFinite(node.minDuration) ? node.minDuration : 0
      const safeMaxDuration = Number.isFinite(node.maxDuration) ? node.maxDuration : 0

      const value = currentDimension.value === 'frequency'
        ? safeFrequency
        : (safeAvgDuration / 60000) // 转换为分钟

      // 确保value是有效数值
      const safeValue = Number.isFinite(value) ? Math.max(0, value) : 0

      // 检查是否为开始或结束节点（包括子流程的开始和结束节点）
      const nodeId = String(node.id || '')
      const isStartNode = node.isStartNode || nodeId === '开始' || nodeId.startsWith('开始_')
      const isEndNode = node.isEndNode || nodeId === '结束' || nodeId.startsWith('结束_')

      let nodeStyle
      if (isStartNode) {
        // 开始节点特殊样式 - 使用固定安全的尺寸
        nodeStyle = {
          fill: '#4CAF50',
          stroke: '#2E7D32',
          strokeWidth: 3,
          textColor: '#FFFFFF',
          width: 80,
          height: 50
        }
      } else if (isEndNode) {
        // 结束节点特殊样式 - 使用固定安全的尺寸
        nodeStyle = {
          fill: '#F44336',
          stroke: '#C62828',
          strokeWidth: 3,
          textColor: '#FFFFFF',
          width: 80,
          height: 50
        }
      } else {
        // 普通活动节点使用数据驱动样式
        nodeStyle = getNodeStyle(safeValue, nodePercentiles, currentDimension.value)

        // 确保nodeStyle中的所有数值都是有效的
        nodeStyle = {
          ...nodeStyle,
          width: Number.isFinite(nodeStyle.width) ? Math.max(80, nodeStyle.width) : 80,
          height: Number.isFinite(nodeStyle.height) ? Math.max(40, nodeStyle.height) : 40,
          strokeWidth: Number.isFinite(nodeStyle.strokeWidth) ? Math.max(1, nodeStyle.strokeWidth) : 2
        }
      }

      return {
        key: nodeId,
        label: String(node.label || nodeId),
        text: isStartNode ? '开始' : isEndNode ? '结束' : (node.label || nodeId),
        frequency: safeFrequency,
        avgDuration: safeAvgDuration,
        minDuration: safeMinDuration,
        maxDuration: safeMaxDuration,
        currentValue: safeValue,
        dimension: currentDimension.value,
        nodeStyle,
        isStartNode,
        isEndNode,
        // 层次结构相关字段
        group: node.groupId, // GoJS分组ID
        isSubprocessNode: Boolean(node.isSubprocessNode),
        subprocessLevel: Number.isFinite(node.subprocessLevel) ? node.subprocessLevel : 0,
        parentCaseId: node.parentCaseId,
        loc: node.loc || `${Math.random() * 400} ${Math.random() * 300}`
      }
    })

    // 处理连接线数据
    const processedLinks = edges.map((edge: any) => {
      // 数据验证和清理 - 防止NaN值导致的GoJS错误
      const safeFrequency = Number.isFinite(edge.frequency) ? edge.frequency : 0
      const safeAvgDuration = Number.isFinite(edge.avgDuration) ? edge.avgDuration : 0
      const safeMinDuration = Number.isFinite(edge.minDuration) ? edge.minDuration : 0
      const safeMaxDuration = Number.isFinite(edge.maxDuration) ? edge.maxDuration : 0

      const value = currentDimension.value === 'frequency'
        ? safeFrequency
        : (safeAvgDuration / 60000) // 转换为分钟

      // 确保value是有效数值
      const safeValue = Number.isFinite(value) ? Math.max(0, value) : 0

      const linkStyle = getLinkStyle(safeValue, edgePercentiles, currentDimension.value)

      // 确保linkStyle中的所有数值都是有效的
      const safeLinkStyle = {
        ...linkStyle,
        strokeWidth: Number.isFinite(linkStyle.strokeWidth) ? Math.max(1, linkStyle.strokeWidth) : 2,
        arrowScale: Number.isFinite(linkStyle.arrowScale) ? Math.max(0.5, linkStyle.arrowScale) : 1.0
      }

      return {
        from: String(edge.from || edge.source || ''),  // 支持 from 或 source 字段，确保是字符串
        to: String(edge.to || edge.target || ''),      // 支持 to 或 target 字段，确保是字符串
        frequency: safeFrequency,
        avgDuration: safeAvgDuration,
        minDuration: safeMinDuration,
        maxDuration: safeMaxDuration,
        currentValue: safeValue,
        dimension: currentDimension.value,
        linkStyle: safeLinkStyle,
        color: safeLinkStyle.stroke,
        width: safeLinkStyle.strokeWidth,
        label: currentDimension.value === 'frequency'
          ? `${safeFrequency}`
          : formatDuration(safeAvgDuration),
        showLabel: true
      }
    })

    // 连通性检查
    const connectivityResult = checkProcessConnectivity(uniqueNodes, edges)

    // 更新连通性状态
    isProcessConnected.value = connectivityResult.isConnected
    connectivityError.value = connectivityResult.error

    // 更新图表数据 - 只有在需要时才重新创建模型
    if (shouldRecreateModel.value) {
      console.log('创建新的GraphLinksModel')
      diagram.model = new go.GraphLinksModel(processedNodes, processedLinks)
      shouldRecreateModel.value = false // 标记模型已创建
    } else {
      console.log('保持现有模型，用户可能正在编辑')
      // 不重新创建模型，保持用户的编辑状态
    }

    // 自动布局
    diagram.layoutDiagram(true)

    console.log(`DFG rendered with ${processedNodes.length} nodes and ${processedLinks.length} links`)
  } catch (error) {
    console.error('Error rendering DFG:', error)
  }
}

// DFG到BPMN转换函数
const convertDfgToBpmn = (dfgData: DFGResult): string => {
  const processId = route.params.processId
  const timestamp = Date.now()

  // 分析节点的连接关系
  const nodeConnections = new Map<string, { incoming: string[], outgoing: string[] }>()

  // 初始化所有节点的连接关系
  dfgData.nodes?.forEach((node: any) => {
    const nodeId = String(node.id || node.key || '')
    nodeConnections.set(nodeId, { incoming: [], outgoing: [] })
  })

  // 分析边的连接关系
  const flows: Array<{ id: string, from: string, to: string }> = []
  dfgData.edges?.forEach((edge: any, index: number) => {
    const fromId = String(edge.source || edge.from || '')
    const toId = String(edge.target || edge.to || '')
    const flowId = `Flow_${fromId}_to_${toId}_${index}`

    flows.push({ id: flowId, from: fromId, to: toId })

    // 更新节点连接关系
    const fromNode = nodeConnections.get(fromId)
    const toNode = nodeConnections.get(toId)

    if (fromNode) fromNode.outgoing.push(flowId)
    if (toNode) toNode.incoming.push(flowId)
  })

  // 识别开始和结束节点
  const startNodes: string[] = []
  const endNodes: string[] = []
  const middleNodes: string[] = []

  dfgData.nodes?.forEach((node: any) => {
    const nodeId = String(node.id || node.key || '')
    const nodeName = String(node.name || node.label || nodeId)
    const connections = nodeConnections.get(nodeId) || { incoming: [], outgoing: [] }

    // 严格的开始节点识别：只有明确标记为开始节点或名称为"开始"的节点
    if (node.isStartNode || nodeId === '开始' || nodeName === '开始') {
      startNodes.push(nodeId)
    }
    // 严格的结束节点识别：只有明确标记为结束节点或名称为"结束"的节点
    else if (node.isEndNode || nodeId === '结束' || nodeName === '结束') {
      endNodes.push(nodeId)
    }
    // 其他所有节点都是中间节点（任务节点）
    else {
      middleNodes.push(nodeId)
    }
  })

  // 如果有多个结束节点，创建一个统一的结束节点
  let unifiedEndNodeId = ''
  let needsUnifiedEnd = endNodes.length > 1

  if (needsUnifiedEnd) {
    unifiedEndNodeId = `UnifiedEnd_${timestamp}`
    console.log(`检测到 ${endNodes.length} 个结束节点，创建统一结束节点: ${unifiedEndNodeId}`)
  }

  // 生成BPMN XML头部
  let bpmnXml = `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                   xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                   xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
                   xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                   id="Definitions_${processId}_${timestamp}"
                   targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_${processId}" isExecutable="true">
`

  // 转换节点
  dfgData.nodes?.forEach((node: any) => {
    const nodeId = String(node.id || node.key || '')
    const nodeName = (node.name || node.label || nodeId).replace(/[<>&"']/g, '') // 清理XML特殊字符
    const connections = nodeConnections.get(nodeId) || { incoming: [], outgoing: [] }

    if (startNodes.includes(nodeId)) {
      // 开始事件
      bpmnXml += `    <bpmn:startEvent id="${nodeId}" name="${nodeName}">\n`
      connections.outgoing.forEach(flowId => {
        bpmnXml += `      <bpmn:outgoing>${flowId}</bpmn:outgoing>\n`
      })
      bpmnXml += `    </bpmn:startEvent>\n`
    } else if (endNodes.includes(nodeId)) {
      if (needsUnifiedEnd) {
        // 如果需要统一结束节点，将原结束节点转换为普通任务
        bpmnXml += `    <bpmn:task id="${nodeId}" name="${nodeName}">\n`
        connections.incoming.forEach(flowId => {
          bpmnXml += `      <bpmn:incoming>${flowId}</bpmn:incoming>\n`
        })
        // 添加到统一结束节点的连接
        const unifiedFlowId = `Flow_${nodeId}_to_${unifiedEndNodeId}`
        bpmnXml += `      <bpmn:outgoing>${unifiedFlowId}</bpmn:outgoing>\n`
        bpmnXml += `    </bpmn:task>\n`

        // 记录到统一结束节点的连接
        flows.push({ id: unifiedFlowId, from: nodeId, to: unifiedEndNodeId })
      } else {
        // 单个结束节点
        bpmnXml += `    <bpmn:endEvent id="${nodeId}" name="${nodeName}">\n`
        connections.incoming.forEach(flowId => {
          bpmnXml += `      <bpmn:incoming>${flowId}</bpmn:incoming>\n`
        })
        bpmnXml += `    </bpmn:endEvent>\n`
      }
    } else {
      // 普通任务
      bpmnXml += `    <bpmn:task id="${nodeId}" name="${nodeName}">\n`
      connections.incoming.forEach(flowId => {
        bpmnXml += `      <bpmn:incoming>${flowId}</bpmn:incoming>\n`
      })
      connections.outgoing.forEach(flowId => {
        bpmnXml += `      <bpmn:outgoing>${flowId}</bpmn:outgoing>\n`
      })
      bpmnXml += `    </bpmn:task>\n`
    }
  })

  // 如果需要，添加统一的结束节点
  if (needsUnifiedEnd) {
    bpmnXml += `    <bpmn:endEvent id="${unifiedEndNodeId}" name="流程结束">\n`
    endNodes.forEach(endNodeId => {
      const unifiedFlowId = `Flow_${endNodeId}_to_${unifiedEndNodeId}`
      bpmnXml += `      <bpmn:incoming>${unifiedFlowId}</bpmn:incoming>\n`
    })
    bpmnXml += `    </bpmn:endEvent>\n`
  }

  // 转换连接线
  flows.forEach(flow => {
    bpmnXml += `    <bpmn:sequenceFlow id="${flow.id}" sourceRef="${flow.from}" targetRef="${flow.to}" />\n`
  })

  // 关闭BPMN XML
  bpmnXml += `  </bpmn:process>
</bpmn:definitions>`

  console.log(`BPMN转换完成: ${startNodes.length} 个开始节点, ${needsUnifiedEnd ? 1 : endNodes.length} 个结束节点, ${middleNodes.length} 个中间节点`)

  return bpmnXml
}

// 获取连通性错误信息
const getConnectivityErrorMessage = computed(() => {
  return connectivityError.value || ''
})

const checkProcessConnectivity = (nodes: any[], edges: any[]) => {
  // 空流程
  if (!nodes || nodes.length === 0) {
    return { isConnected: false, error: '流程为空' }
  }

  // 获取节点ID
  const getNodeId = (node: any) => String(node.key || node.id || '')

  // 查找开始和结束节点 - 与BPMN生成逻辑保持一致
  const startNodes = nodes.filter(node => {
    const nodeId = getNodeId(node)
    const nodeName = String(node.name || node.label || nodeId)
    return node.isStartNode || nodeId === '开始' || nodeName === '开始'
  })
  const endNodes = nodes.filter(node => {
    const nodeId = getNodeId(node)
    const nodeName = String(node.name || node.label || nodeId)
    return node.isEndNode || nodeId === '结束' || nodeName === '结束'
  })

  if (startNodes.length === 0) {
    return { isConnected: false, error: '缺少开始节点' }
  }
  if (endNodes.length === 0) {
    return { isConnected: false, error: '缺少结束节点' }
  }

  // 只有一个节点的情况
  if (nodes.length === 1) {
    return { isConnected: true, error: null }
  }

  // 构建邻接表
  const graph = new Map<string, string[]>()
  nodes.forEach(node => {
    graph.set(getNodeId(node), [])
  })

  edges.forEach(edge => {
    const from = String(edge.source || edge.from || '')
    const to = String(edge.target || edge.to || '')
    if (from && to && graph.has(from)) {
      graph.get(from)!.push(to)
    }
  })

  // BFS检查连通性并识别孤立节点
  const checkConnectivity = () => {
    const visited = new Set<string>()
    const queue: string[] = []

    // 从所有开始节点开始BFS
    startNodes.forEach(node => {
      const nodeId = getNodeId(node)
      queue.push(nodeId)
      visited.add(nodeId)
    })

    let canReachEnd = false
    while (queue.length > 0) {
      const current = queue.shift()!

      // 检查是否到达结束节点
      if (endNodes.some(node => getNodeId(node) === current)) {
        canReachEnd = true
      }

      // 遍历邻居节点
      const neighbors = graph.get(current) || []
      neighbors.forEach(neighbor => {
        if (!visited.has(neighbor)) {
          visited.add(neighbor)
          queue.push(neighbor)
        }
      })
    }

    // 检查孤立节点
    const allNodeIds = nodes.map(node => getNodeId(node))
    const isolatedNodes = allNodeIds.filter(nodeId => !visited.has(nodeId))

    // 生成错误信息
    let error = null
    if (!canReachEnd) {
      error = '开始节点无法到达结束节点'
    } else if (isolatedNodes.length > 0) {
      const isolatedNames = isolatedNodes.slice(0, 3).join('、')
      const moreCount = isolatedNodes.length > 3 ? `等${isolatedNodes.length}个` : ''
      error = `发现孤立节点：${isolatedNames}${moreCount}`
    }

    return {
      isConnected: !error,
      error,
      isolatedNodes
    }
  }

  const result = checkConnectivity()
  return {
    isConnected: result.isConnected,
    error: result.error
  }
}

// 动态节点样式映射（专业流程挖掘标准）
const getNodeStyle = (value: number, percentiles: any, dimension: 'frequency' | 'duration' = 'frequency') => {
  const { p25, p50, p75, p90, max, logScale } = percentiles

  // 根据维度选择不同的颜色方案
  const colorSchemes = {
    frequency: {
      high: { fill: '#2E7D32', stroke: '#1B5E20', textColor: '#FFFFFF' }, // 深绿色 - 高频
      mediumHigh: { fill: '#388E3C', stroke: '#2E7D32', textColor: '#FFFFFF' }, // 中深绿色
      medium: { fill: '#4CAF50', stroke: '#388E3C', textColor: '#FFFFFF' }, // 标准绿色
      mediumLow: { fill: '#81C784', stroke: '#4CAF50', textColor: '#1B5E20' }, // 浅绿色
      low: { fill: '#C8E6C9', stroke: '#81C784', textColor: '#2E7D32' } // 很浅绿色
    },
    duration: {
      high: { fill: '#D32F2F', stroke: '#B71C1C', textColor: '#FFFFFF' }, // 深红色 - 高耗时
      mediumHigh: { fill: '#F44336', stroke: '#D32F2F', textColor: '#FFFFFF' }, // 红色
      medium: { fill: '#FF9800', stroke: '#F57C00', textColor: '#FFFFFF' }, // 橙色
      mediumLow: { fill: '#FFC107', stroke: '#FF9800', textColor: '#1B5E20' }, // 黄色
      low: { fill: '#4CAF50', stroke: '#388E3C', textColor: '#FFFFFF' } // 绿色 - 低耗时
    }
  }

  const colors = colorSchemes[dimension]

  // 计算标准化值（0-1范围）用于尺寸映射
  let normalizedValue = max > 0 ? value / max : 0

  // 对于对数缩放的数据，使用更平滑的映射
  if (logScale && dimension === 'duration') {
    const logValue = Math.log10(Math.max(value, 0.1))
    const logMax = Math.log10(Math.max(max, 0.1))
    normalizedValue = logMax > 0 ? logValue / logMax : 0
  }

  // 使用更科学的尺寸映射算法
  const getSizeMultiplier = (normalizedVal: number) => {
    // 使用平方根函数使尺寸差异更加明显但不过于极端
    return Math.sqrt(normalizedVal)
  }

  const sizeMultiplier = getSizeMultiplier(normalizedValue)

  if (value >= p90) {
    return {
      ...colors.high,
      strokeWidth: 3,
      width: Math.max(120, Math.min(160, 120 + sizeMultiplier * 40)),
      height: Math.max(60, Math.min(80, 60 + sizeMultiplier * 20))
    }
  } else if (value >= p75) {
    return {
      ...colors.mediumHigh,
      strokeWidth: 2.5,
      width: Math.max(100, Math.min(140, 100 + sizeMultiplier * 40)),
      height: Math.max(55, Math.min(75, 55 + sizeMultiplier * 20))
    }
  } else if (value >= p50) {
    return {
      ...colors.medium,
      strokeWidth: 2,
      width: Math.max(90, Math.min(120, 90 + sizeMultiplier * 30)),
      height: Math.max(50, Math.min(70, 50 + sizeMultiplier * 20))
    }
  } else if (value >= p25) {
    return {
      ...colors.mediumLow,
      strokeWidth: 2,
      width: Math.max(85, Math.min(110, 85 + sizeMultiplier * 25)),
      height: Math.max(45, Math.min(65, 45 + sizeMultiplier * 20))
    }
  } else {
    return {
      ...colors.low,
      strokeWidth: 1.5,
      width: Math.max(80, Math.min(100, 80 + sizeMultiplier * 20)),
      height: Math.max(40, Math.min(60, 40 + sizeMultiplier * 20))
    }
  }
}

// 动态连接线样式映射（专业流程挖掘标准）
const getLinkStyle = (value: number, percentiles: any, dimension: 'frequency' | 'duration' = 'frequency') => {
  const { p25, p50, p75, p90, max, logScale } = percentiles

  // 根据维度选择不同的颜色方案
  const colorSchemes = {
    frequency: {
      high: '#D32F2F',    // 深红色 - 高频连接
      mediumHigh: '#F44336', // 红色
      medium: '#FF9800',     // 橙色
      mediumLow: '#2196F3',  // 蓝色
      low: '#90CAF9'         // 浅蓝色 - 低频连接
    },
    duration: {
      high: '#D32F2F',      // 深红色 - 高耗时连接
      mediumHigh: '#F44336', // 红色
      medium: '#FF9800',     // 橙色
      mediumLow: '#FFC107',  // 黄色
      low: '#4CAF50'         // 绿色 - 低耗时连接
    }
  }

  const colors = colorSchemes[dimension]

  // 计算标准化值用于线宽映射
  let normalizedValue = max > 0 ? value / max : 0

  // 对于对数缩放的数据，使用更平滑的映射
  if (logScale && dimension === 'duration') {
    const logValue = Math.log10(Math.max(value, 0.1))
    const logMax = Math.log10(Math.max(max, 0.1))
    normalizedValue = logMax > 0 ? logValue / logMax : 0
  }

  // 使用更科学的线宽映射算法
  const getStrokeMultiplier = (normalizedVal: number) => {
    // 使用平方根函数使线宽差异更加明显但不过于极端
    return Math.sqrt(normalizedVal)
  }

  const strokeMultiplier = getStrokeMultiplier(normalizedValue)

  if (value >= p90) {
    const strokeWidth = Math.max(6, Math.min(12, 6 + strokeMultiplier * 6))
    return {
      stroke: colors.high,
      strokeWidth,
      arrowScale: Math.max(1.2, Math.min(2.5, 0.8 + strokeWidth * 0.15)) // 箭头大小与线宽成正比
    }
  } else if (value >= p75) {
    const strokeWidth = Math.max(5, Math.min(10, 5 + strokeMultiplier * 5))
    return {
      stroke: colors.mediumHigh,
      strokeWidth,
      arrowScale: Math.max(1.1, Math.min(2.2, 0.8 + strokeWidth * 0.15))
    }
  } else if (value >= p50) {
    const strokeWidth = Math.max(4, Math.min(8, 4 + strokeMultiplier * 4))
    return {
      stroke: colors.medium,
      strokeWidth,
      arrowScale: Math.max(1.0, Math.min(2.0, 0.8 + strokeWidth * 0.15))
    }
  } else if (value >= p25) {
    const strokeWidth = Math.max(3, Math.min(6, 3 + strokeMultiplier * 3))
    return {
      stroke: colors.mediumLow,
      strokeWidth,
      arrowScale: Math.max(0.9, Math.min(1.8, 0.8 + strokeWidth * 0.15))
    }
  } else {
    const strokeWidth = Math.max(2, Math.min(4, 2 + strokeMultiplier * 2))
    return {
      stroke: colors.low,
      strokeWidth,
      arrowScale: Math.max(0.8, Math.min(1.5, 0.8 + strokeWidth * 0.15))
    }
  }
}

// 获取连接线宽度

// 路径筛选方法
const formatTooltip = (value: number) => {
  return `频次: ${value}`
}

// DFG事件处理
const handleDFGNodeClick = (e: go.DiagramEvent) => {
  const node = e.subject.part
  if (node instanceof go.Node) {
    const nodeData = node.data
    console.log('Node clicked:', nodeData)
    // 这里可以添加节点编辑逻辑
  }
}

const clearHighlight = () => {
  highlightedNodeKey.value = ''
  highlightedLinks.value.clear()
  isHighlightMode.value = false
}

// 从当前GoJS模型更新连通性状态
const updateConnectivityFromCurrentModel = () => {
  console.log('=== 开始连通性检查 ===')
  const diagram = getDiagram()
  if (!diagram) {
    console.warn('图表实例不存在，跳过连通性检查')
    return
  }

  const model = diagram.model as go.GraphLinksModel
  const nodes = model.nodeDataArray
  const links = model.linkDataArray

  console.log(`当前模型状态: ${nodes.length} 个节点, ${links.length} 条连接线`)

  // 转换为连通性检查需要的格式
  const edges = links.map((link: any) => ({
    source: link.source || link.from,
    target: link.target || link.to,
    frequency: link.frequency || 1
  }))

  console.log('连通性检查输入数据:', {
    nodes: nodes.map((n: any) => ({ key: n.key, id: n.id, isStartNode: n.isStartNode, isEndNode: n.isEndNode })),
    edges: edges.map((e: any) => ({ source: e.source, target: e.target }))
  })

  const result = checkProcessConnectivity(nodes, edges)
  isProcessConnected.value = result.isConnected
  connectivityError.value = result.error

  console.log('连通性检查结果:', {
    isConnected: result.isConnected,
    error: result.error
  })

  if (!result.isConnected) {
    console.warn('⚠️ 连通性检查失败，原因:', result.error)
  } else {
    console.log('✅ 流程连通性正常')
  }
  console.log('=== 连通性检查完成 ===')
}

// 获取图表实例的辅助函数
const getDiagram = (): go.Diagram | null => {
  return diagram
}

// 从GoJS模型获取当前DFG数据（包括用户手动添加的连接线）
const getCurrentDfgDataFromModel = (): DFGResult => {
  const diagram = getDiagram()
  if (!diagram || !currentDiscoveryData.value) {
    console.warn('图表实例或数据不存在，返回当前数据')
    return currentDiscoveryData.value || { nodes: [], edges: [] }
  }

  const model = diagram.model as go.GraphLinksModel

  // 获取节点数据
  const nodes = model.nodeDataArray.map((nodeData: any) => ({
    id: nodeData.id || nodeData.key,
    label: nodeData.label || nodeData.name || nodeData.text,
    frequency: nodeData.frequency || 1,
    isStartNode: nodeData.isStartNode || false,
    isEndNode: nodeData.isEndNode || false,
    // 保留其他可能的属性
    avgDuration: nodeData.avgDuration,
    minDuration: nodeData.minDuration,
    maxDuration: nodeData.maxDuration,
    parentCaseId: nodeData.parentCaseId,
    caseCount: nodeData.caseCount,
    subprocessLevel: nodeData.subprocessLevel,
    groupId: nodeData.groupId
  }))

  // 获取连接线数据
  const edges = model.linkDataArray.map((linkData: any) => ({
    source: linkData.source || linkData.from,
    target: linkData.target || linkData.to,
    frequency: linkData.frequency || 1,
    // 保留其他可能的属性
    avgDuration: linkData.avgDuration,
    minDuration: linkData.minDuration,
    maxDuration: linkData.maxDuration,
    isSubprocessEdge: linkData.isSubprocessEdge,
    subprocessLevel: linkData.subprocessLevel,
    groupId: linkData.groupId
  }))

  console.log(`从GoJS模型获取数据: ${nodes.length} 个节点, ${edges.length} 条边`)

  // 返回完整的DFG数据结构
  return {
    ...currentDiscoveryData.value,
    nodes,
    edges
  }
}

// 配置连接工具并监听连接事件
const setupLinkingTool = (diagram: go.Diagram) => {
  // 配置连接工具
  const linkingTool = diagram.toolManager.linkingTool
  const relinkingTool = diagram.toolManager.relinkingTool

  // 保存原始方法的引用
  const originalLinkingDoActivate = linkingTool.doActivate
  const originalLinkingDoCancel = linkingTool.doCancel
  const originalLinkingDoStop = linkingTool.doStop

  const originalRelinkingDoActivate = relinkingTool.doActivate
  const originalRelinkingDoCancel = relinkingTool.doCancel
  const originalRelinkingDoStop = relinkingTool.doStop

  // === 连接工具事件监听 ===

  // 重写doActivate方法 - 连接开始时调用
  linkingTool.doActivate = function() {
    console.log('开始连接操作')
    // 记录操作前状态
    recordCurrentModelState()
    // 调用原始方法
    originalLinkingDoActivate.call(this)
  }

  // 重写doCancel方法 - 连接取消时调用
  linkingTool.doCancel = function() {
    console.log('连接操作被取消')
    // 调用原始方法
    originalLinkingDoCancel.call(this)
  }

  // 重写doStop方法 - 连接完成时调用
  linkingTool.doStop = function() {
    console.log('连接操作完成')

    // 调用原始方法
    originalLinkingDoStop.call(this)

    // 延迟处理，确保连接已经添加到模型中
    setTimeout(() => {
      handleLinkingComplete()
    }, 100)
  }

  // === 重新连接工具事件监听 ===

  // 重写重新连接的doActivate方法
  relinkingTool.doActivate = function() {
    console.log('开始重新连接操作')
    // 记录操作前状态
    recordCurrentModelState()
    // 调用原始方法
    originalRelinkingDoActivate.call(this)
  }

  // 重写重新连接的doCancel方法
  relinkingTool.doCancel = function() {
    console.log('重新连接操作被取消')
    // 调用原始方法
    originalRelinkingDoCancel.call(this)
  }

  // 重写重新连接的doStop方法
  relinkingTool.doStop = function() {
    console.log('重新连接操作完成')

    // 调用原始方法
    originalRelinkingDoStop.call(this)

    // 延迟处理
    setTimeout(() => {
      handleRelinkingComplete()
    }, 100)
  }
}

// 处理连接完成事件
const handleLinkingComplete = () => {
  console.log('处理连接完成事件')

  const diagram = getDiagram()
  if (!diagram) return

  // 直接从GoJS模型获取所有连接线
  const model = diagram.model as go.GraphLinksModel
  const currentLinks = model.linkDataArray

  // 检查是否有新的连接线（没有frequency属性的就是新添加的）
  const newLinks = currentLinks.filter((linkData: any) => !linkData.frequency)

  if (newLinks.length > 0) {
    console.log(`发现 ${newLinks.length} 条新连接线，设置默认属性`)

    // 为新连接线设置默认数据
    diagram.startTransaction('set default link data')

    newLinks.forEach((linkData: any) => {
      // 设置频率
      diagram.model.setDataProperty(linkData, 'frequency', 1)

      // 确保source和target属性
      if (!linkData.source && linkData.from) {
        diagram.model.setDataProperty(linkData, 'source', linkData.from)
      }
      if (!linkData.target && linkData.to) {
        diagram.model.setDataProperty(linkData, 'target', linkData.to)
      }

      // 设置样式
      diagram.model.setDataProperty(linkData, 'linkStyle', {
        stroke: '#2196F3',
        strokeWidth: 2,
        arrowScale: 1.0
      })

      // 设置标签 - 显示频次
      diagram.model.setDataProperty(linkData, 'label', '1')
      diagram.model.setDataProperty(linkData, 'showLabel', true)

      console.log('为新连接线设置默认数据:', {
        key: linkData.key,
        from: linkData.from,
        to: linkData.to,
        source: linkData.source,
        target: linkData.target,
        frequency: linkData.frequency,
        label: linkData.label,
        showLabel: linkData.showLabel
      })
    })

    diagram.commitTransaction('set default link data')

    // 记录到撤销重做历史
    if (lastModelState) {
      const currentState = undoRedo.getCurrentState()
      undoRedo.recordBatchOperation('手动添加连接线', lastModelState, currentState)
    }

    // 更新连通性状态
    updateConnectivityFromCurrentModel()

    // 记录新状态
    recordCurrentModelState()

    // 显示成功提示
    ElMessage.success(`连接线添加成功，显示频次: 1`)

    console.log(`成功为 ${newLinks.length} 条连接线设置频次标签`)
  }
}

// 处理重新连接完成事件
const handleRelinkingComplete = () => {
  console.log('处理重新连接完成事件')

  // 获取当前模型状态
  const currentState = undoRedo.getCurrentState()

  // 记录到撤销重做历史
  if (lastModelState) {
    undoRedo.recordBatchOperation('重新连接线', lastModelState, currentState)
  }

  // 更新连通性状态
  updateConnectivityFromCurrentModel()

  // 记录新状态
  recordCurrentModelState()

  // 显示成功提示
  ElMessage.success('连接线重新连接成功')

  console.log('重新连接操作处理完成')
}

// 处理删除选择事件
const handleSelectionDeleting = (e: go.DiagramEvent) => {
  console.log('检测到删除操作')

  // 记录操作前状态
  recordCurrentModelState()

  // 获取被删除的对象
  const deletedObjects: any[] = []
  e.subject.each((part: go.Part) => {
    if (part instanceof go.Node) {
      deletedObjects.push({ type: 'node', data: part.data })
      console.log('将删除节点:', part.data)
    } else if (part instanceof go.Link) {
      deletedObjects.push({ type: 'link', data: part.data })
      console.log('将删除连接线:', part.data)
    }
  })

  // 延迟处理删除完成事件
  setTimeout(() => {
    handleDeletionComplete(deletedObjects)
  }, 100)
}

// 处理删除完成事件
const handleDeletionComplete = (deletedObjects: any[]) => {
  console.log('处理删除完成事件', deletedObjects)

  // 获取当前模型状态
  const currentState = undoRedo.getCurrentState()

  // 记录到撤销重做历史
  if (lastModelState) {
    const nodeCount = deletedObjects.filter(obj => obj.type === 'node').length
    const linkCount = deletedObjects.filter(obj => obj.type === 'link').length

    let description = ''
    if (nodeCount > 0 && linkCount > 0) {
      description = `删除 ${nodeCount} 个节点和 ${linkCount} 条连接线`
    } else if (nodeCount > 0) {
      description = `删除 ${nodeCount} 个节点`
    } else if (linkCount > 0) {
      description = `删除 ${linkCount} 条连接线`
    }

    if (description) {
      undoRedo.recordBatchOperation(description, lastModelState, currentState)
    }
  }

  // 更新连通性状态
  updateConnectivityFromCurrentModel()

  // 记录新状态
  recordCurrentModelState()

  // 显示成功提示
  const nodeCount = deletedObjects.filter(obj => obj.type === 'node').length
  const linkCount = deletedObjects.filter(obj => obj.type === 'link').length

  if (nodeCount > 0 || linkCount > 0) {
    ElMessage.success(`删除操作完成`)
  }

  console.log('删除操作处理完成')
}

// 上下文菜单事件处理
const handleContextMenu = (e: go.DiagramEvent) => {
  const obj = e.subject.part

  if (obj instanceof go.Node) {
    // 节点右键菜单
    const nodeData = obj.data
    selectedElement.value = {
      id: nodeData.key || nodeData.id,
      key: nodeData.key || nodeData.id,
      name: nodeData.name || nodeData.label,
      label: nodeData.label || nodeData.name,
      frequency: nodeData.frequency,
      isStartNode: nodeData.isStartNode,
      isEndNode: nodeData.isEndNode
    }

    contextMenu.showContextMenu('node', nodeData)

  } else if (obj instanceof go.Link) {
    // 连接线右键菜单
    const linkData = obj.data
    selectedElement.value = {
      id: linkData.key,
      key: linkData.key,
      name: `${linkData.from || linkData.source} -> ${linkData.to || linkData.target}`,
      frequency: linkData.frequency
    }

    contextMenu.showContextMenu('link', {
      key: linkData.key,
      from: linkData.from || linkData.source,
      to: linkData.to || linkData.target,
      source: linkData.source || linkData.from,
      target: linkData.target || linkData.to,
      frequency: linkData.frequency
    })
  }
}

// 维度切换
const onDimensionChange = (val: string | number | boolean | undefined) => {
  const dimension = val as 'frequency' | 'duration'
  console.log(`切换维度: ${currentDimension.value} -> ${dimension}`)

  currentDimension.value = dimension

  if (diagram && originalDiscoveryData.value) {
    // 标记需要重新创建模型以更新样式和标签
    shouldRecreateModel.value = true

    // 重新渲染DFG
    renderDFG()

    console.log(`维度切换完成: ${dimension}`)
  }
}

// 更新缩放指示器
const updateZoomIndicator = () => {
  if (diagram) {
    currentZoomScale.value = diagram.scale
  }
}

// 缩放控制
const zoomIn = () => {
  if (diagram) {
    diagram.commandHandler.increaseZoom()
    updateZoomIndicator()
  }
}

const zoomOut = () => {
  if (diagram) {
    diagram.commandHandler.decreaseZoom()
    updateZoomIndicator()
  }
}

const resetZoom = () => {
  if (diagram) {
    diagram.scale = 1.0
    updateZoomIndicator()
  }
}

const fitToScreen = () => {
  if (diagram) {
    diagram.commandHandler.zoomToFit()
    updateZoomIndicator()
  }
}



// 关闭筛选对话框
const handleFilterDialogClose = () => {
  // 如果是首次使用，不允许直接关闭，必须设置筛选条件
  if (isFirstTimeFilter.value) {
    ElMessage.warning('请先设置筛选条件后再继续使用')
    return false
  }

  filterDialogVisible.value = false
  return true
}

// 重置临时筛选条件
const resetTempFilter = () => {
  tempPathFrequencyRange.value = [minPathFrequency.value, maxPathFrequency.value]
}

// 应用筛选并关闭对话框
const applyFilterAndClose = () => {
  // 将临时值应用到实际筛选值
  pathFrequencyRange.value = [...tempPathFrequencyRange.value]

  // 应用筛选
  const filterResult = applyPathFrequencyFilter()

  // 检查筛选结果是否包含开始和结束节点
  if (filterResult && (!filterResult.hasStartNode || !filterResult.hasEndNode)) {
    const missingNodes: string[] = []
    if (!filterResult.hasStartNode) missingNodes.push('开始节点')
    if (!filterResult.hasEndNode) missingNodes.push('结束节点')

    const suggestions = []
    if (!filterResult.hasStartNode) {
      suggestions.push('• 降低最小频次以包含更多起始路径')
    }
    if (!filterResult.hasEndNode) {
      suggestions.push('• 降低最小频次以包含更多结束路径')
    }
    suggestions.push('• 或者扩大频次范围以包含更多流程路径')

    ElMessageBox.confirm(
      `筛选后的流程缺少${missingNodes.join('和')}，这将导致流程不完整无法保存。`,
      '筛选结果不完整',
      {
        confirmButtonText: '重置筛选条件',
        cancelButtonText: '手动调整',
        type: 'warning',
        dangerouslyUseHTMLString: false,
      }
    ).then(() => {
      // 用户选择重置筛选条件
      console.log('用户选择重置筛选条件')
      resetTempFilter()
      ElMessage.info('筛选条件已重置，请重新设置')
    }).catch(() => {
      // 用户选择手动调整，不关闭对话框
      console.log('用户选择手动调整筛选条件，缺少:', missingNodes)
    })
    return // 不关闭对话框，不标记完成
  }

  // 标记首次使用已完成
  if (isFirstTimeFilter.value) {
    isFirstTimeFilter.value = false
    console.log('首次筛选设置完成')
  }

  // 关闭对话框
  filterDialogVisible.value = false

  console.log('筛选条件已应用并关闭对话框:', pathFrequencyRange.value)
}

const resetPathFilter = () => {
  pathFrequencyRange.value = [minPathFrequency.value, maxPathFrequency.value]

  // 恢复原始数据
  if (originalDiscoveryData.value) {
    currentDiscoveryData.value = JSON.parse(JSON.stringify(originalDiscoveryData.value))

    // 更新统计信息
    totalPathCount.value = originalDiscoveryData.value.edges?.length || 0
    filteredPathCount.value = totalPathCount.value
    isProcessEmpty.value = false  // 重置后流程不为空

    // 重新渲染DFG
    if (diagram) {
      renderDFG()
    }

    ElMessage.success('路径筛选已重置')
  }
}

const applyPathFrequencyFilter = () => {
  if (!originalDiscoveryData.value) {
    return { hasStartNode: false, hasEndNode: false, success: false }
  }

  try {
    const [minFreq, maxFreq] = pathFrequencyRange.value

    // 1. 首先按频次筛选边
    const originalEdges = originalDiscoveryData.value.edges || []
    const frequencyFilteredEdges = originalEdges.filter((edge: any) => {
      const frequency = edge.frequency || 0
      return frequency >= minFreq && frequency <= maxFreq
    })

    // 如果没有符合条件的边，清空图表
    if (frequencyFilteredEdges.length === 0) {
      currentDiscoveryData.value = createEmptyDFGResult()

      totalPathCount.value = originalEdges.length
      filteredPathCount.value = 0
      isProcessEmpty.value = true  // 标记流程为空
      isProcessConnected.value = false  // 空流程标记为不连通
      connectivityError.value = '流程为空'

      // 清空DFG图表
      if (diagram) {
        diagram.model.nodeDataArray = []
        diagram.model.linkDataArray = []
      }

      ElMessage.warning(`没有频次在 ${minFreq}-${maxFreq} 范围内的普通路径`)
      return { hasStartNode: false, hasEndNode: false, success: false }
    }

    // 2. 获取所有涉及的节点ID
    const involvedNodeIds = new Set<string>()
    frequencyFilteredEdges.forEach((edge) => {
      const fromId = edge.source || edge.from
      const toId = edge.target || edge.to
      if (fromId) involvedNodeIds.add(fromId)
      if (toId) involvedNodeIds.add(toId)
    })

    // 3. 筛选出涉及的节点
    const originalNodes = originalDiscoveryData.value.nodes || []
    const filteredNodes = originalNodes.filter((node) => {
      const nodeId = node.id || node.key
      return involvedNodeIds.has(nodeId)
    })

    // 4. 检查筛选后是否包含开始和结束节点
    const hasStartNode = filteredNodes.some((node) => {
      const nodeId = String(node.key || node.id || '')
      return node.isStartNode || nodeId === '开始' || nodeId.startsWith('开始_')
    })

    const hasEndNode = filteredNodes.some((node) => {
      const nodeId = String(node.key || node.id || '')
      return node.isEndNode || nodeId === '结束' || nodeId.startsWith('结束_')
    })

    console.log('筛选后节点检查:', {
      totalNodes: filteredNodes.length,
      hasStartNode,
      hasEndNode,
      startNodes: filteredNodes.filter(n => n.isStartNode || (n.key || n.id) === '开始').map(n => n.key || n.id),
      endNodes: filteredNodes.filter(n => n.isEndNode || (n.key || n.id) === '结束').map(n => n.key || n.id)
    })

    // 5. 连通性检查
    const connectivityResult = checkProcessConnectivity(filteredNodes, frequencyFilteredEdges)

    // 更新连通性状态
    isProcessConnected.value = connectivityResult.isConnected
    connectivityError.value = connectivityResult.error

    // 6. 创建筛选后的数据
    const filteredData = {
      ...originalDiscoveryData.value,
      nodes: filteredNodes,
      edges: frequencyFilteredEdges
    }

    // 更新当前数据
    currentDiscoveryData.value = filteredData

    // 标记需要重新创建模型（数据筛选）
    shouldRecreateModel.value = true

    // 重新渲染DFG
    if (diagram) {
      renderDFG()
    }

    // 更新统计信息
    totalPathCount.value = originalEdges.length
    filteredPathCount.value = frequencyFilteredEdges.length
    isProcessEmpty.value = false  // 标记流程不为空

    ElMessage.success(`已筛选显示 ${filteredPathCount.value} 条路径`)

    // 返回筛选结果信息
    return { hasStartNode, hasEndNode, success: true }

  } catch (error) {
    ElMessage.error(`应用路径筛选失败: ${error instanceof Error ? error.message : '未知错误'}`)
    return { hasStartNode: false, hasEndNode: false, success: false }
  }
}

// 事件处理方法
const handleBack = () => {
  router.push('/conformance/check')
}

// 显示保存对话框
const showSaveDialog = () => {
  // 检查流程是否为空
  if (isProcessEmpty.value) {
    ElMessage.error('当前流程为空，无法保存。请调整筛选条件以显示有效的流程。')
    return
  }

  // 检查流程连通性
  if (!isProcessConnected.value) {
    const errorMessage = getConnectivityErrorMessage.value
    ElMessage.error(`当前流程不连通，无法保存：${errorMessage}。请调整筛选条件以确保流程连通性。`)
    return
  }

  // 检查是否有有效的节点和连接
  if (filteredPathCount.value === 0) {
    ElMessage.error('当前没有有效的流程路径，无法保存。请调整筛选条件。')
    return
  }

  // 生成默认模型名
  const defaultName = `流程发现编辑模型_${new Date().toLocaleString()}`
  saveForm.value = {
    name: defaultName,
    description: '基于流程发现结果编辑的流程模型，用于符合性检查',
    modelType: 'discovered'
  }

  saveDialogVisible.value = true
}

// 确认保存
const handleSaveConfirm = async () => {
  if (!saveFormRef.value) return

  try {
    await saveFormRef.value.validate()

    // 再次检查流程连通性（防止绕过UI限制）
    if (!isProcessConnected.value) {
      const errorMessage = getConnectivityErrorMessage.value
      ElMessage.error(`流程不连通，无法保存：${errorMessage}。请调整筛选条件以确保流程连通性。`)
      return
    }

    // 直接保存当前的流程发现数据作为模型
    if (!currentDiscoveryData.value || !currentDiscoveryData.value.nodes || currentDiscoveryData.value.nodes.length === 0) {
      ElMessage.error('没有可保存的流程数据')
      return
    }

    // 从GoJS模型获取最新数据（包括用户手动添加的连接线）
    const latestDfgData = getCurrentDfgDataFromModel()

    // 将DFG数据转换为BPMN格式
    const bpmnXml = convertDfgToBpmn(latestDfgData)

    console.log('从GoJS模型获取的最新数据:', latestDfgData)
    console.log('转换后的BPMN XML:', bpmnXml)

    const result = await dataManager.saveBpmnModel(bpmnXml, {
      name: saveForm.value.name,
      description: saveForm.value.description,
      modelType: saveForm.value.modelType,
      status: 'active'
    })

    if (result.success) {
      saveDialogVisible.value = false

      // 检查是否是从符合性检查页面跳转过来的
      const returnTo = route.query.returnTo as string
      if (returnTo === 'check') {
        // 直接开始符合性检查，使用保存的模型ID
        await startConformanceCheck(result.modelId)
      } else {
        // 否则返回到符合性检查页面
        handleBack()
      }
    }
  } catch (error) {
    console.error('保存模型失败:', error)
  }
}

// 直接开始符合性检查
const startConformanceCheck = async (modelId?: number) => {
  try {
    const processId = parseInt(route.params.processId as string)
    if (!processId || isNaN(processId)) {
      ElMessage.error('无效的流程ID')
      return
    }

    if (!modelId) {
      ElMessage.error('保存的模型ID无效，无法开始符合性检查')
      return
    }

    ElMessage.info('正在启动符合性检查...')

    // 创建符合性检查请求，使用保存的BPMN模型
    const requestParams: ConformanceCheckRequest = {
      processId: processId,
      bpmnModelId: modelId, // 使用实际保存的模型ID
      parameters: {
        alignmentAlgorithm: 'optimal',
        includeAlignment: true,
        includeCaseAnalysis: true,
        includeActivityAnalysis: false
      },
      description: '基于编辑后的流程发现结果进行符合性检查',
      forceRefresh: true
    }

    const result = await api.performConformanceCheck(requestParams)

    ElMessage.success('符合性检查已开始')

    // 跳转到结果页面
    await router.push(`/conformance/results/${result.id}`)
  } catch (error) {
    console.error('启动符合性检查失败:', error)
    ElMessage.error('启动符合性检查失败')
  }
}

// 生命周期钩子
onMounted(async () => {
  // 强制刷新数据
  await dataManager.loadDiscoveryResult(true)

  // 初始化DFG图表
  await nextTick()
  await initializeDFG()

  // 初始化上下文菜单鼠标跟踪
  contextMenu.initMouseTracking()

  // 如果数据已经加载完成，数据监听器会自动处理初始化
  if (!dataManager.isLoading.value) {
    console.log('数据已加载，等待监听器处理')
  }
})

onUnmounted(() => {
  // 清理上下文菜单鼠标跟踪
  contextMenu.cleanupMouseTracking()

  // 清理DFG图表
  if (diagram) {
    diagram.div = null
    diagram = null
  }
})

// 监听数据变化
watch(() => dataManager.discoveryResult.value, async (newResult) => {
  if (newResult && newResult.nodes && newResult.edges) {
    console.log('=== CONFORMANCE PAGE DATA ANALYSIS ===')
    console.log('Total nodes:', newResult.nodes.length)
    console.log('Total edges:', newResult.edges.length)

    // 分析开始和结束节点
    const startNodes = newResult.nodes.filter((node) => {
      const nodeId = String(node.id || '')
      return node.isStartNode || nodeId === '开始' || nodeId.startsWith('开始_')
    })
    const endNodes = newResult.nodes.filter((node) => {
      const nodeId = String(node.id || '')
      return node.isEndNode || nodeId === '结束' || nodeId.startsWith('结束_')
    })

    console.log('Start nodes found:', startNodes.map(n => ({ id: n.id, label: n.label, isStartNode: n.isStartNode })))
    console.log('End nodes found:', endNodes.map(n => ({ id: n.id, label: n.label, isEndNode: n.isEndNode })))

    // 检查是否有重复的节点ID
    const nodeIds = newResult.nodes.map((node) => node.id)
    const uniqueNodeIds = new Set(nodeIds)
    if (nodeIds.length !== uniqueNodeIds.size) {
      console.warn('⚠️ 发现重复的节点ID!')
      const duplicates = nodeIds.filter((id, index) => nodeIds.indexOf(id) !== index)
      console.warn('重复的节点ID:', duplicates)
    }

    originalDiscoveryData.value = newResult
    currentDiscoveryData.value = JSON.parse(JSON.stringify(newResult))

    // 初始化路径筛选范围
    const frequencies = newResult.edges?.map((edge) => edge.frequency || 0) || []
    if (frequencies.length > 0) {
      minPathFrequency.value = Math.min(...frequencies)
      maxPathFrequency.value = Math.max(...frequencies)
      pathFrequencyRange.value = [minPathFrequency.value, maxPathFrequency.value]
      tempPathFrequencyRange.value = [minPathFrequency.value, maxPathFrequency.value]  // 同时初始化临时值
      totalPathCount.value = frequencies.length
      filteredPathCount.value = frequencies.length

      // 如果是首次访问，且频次范围有意义（最小值 < 最大值），自动显示筛选对话框
      if (isFirstTimeFilter.value) {
        if (minPathFrequency.value < maxPathFrequency.value) {
          // 延迟一点确保页面渲染完成
          nextTick(() => {
            setTimeout(() => {
              filterDialogVisible.value = true
              console.log('首次访问，自动显示筛选对话框')
            }, 500)
          })
        } else {
          // 频次最小值与最大值相同，无需弹窗，直接视为已完成首次设置
          isFirstTimeFilter.value = false
        }
      }
    }

    // 标记需要重新创建模型（新数据加载）
    shouldRecreateModel.value = true

    // 清空撤销重做历史（新数据加载时重置）
    undoRedo.clearHistory()

    // 渲染DFG - 等待多个 tick 确保 DOM 完全更新
    if (diagram) {
      await nextTick()
      await nextTick()
      setTimeout(() => {
        renderDFG()
      }, 50)
    }
  }
}, { immediate: true })
</script>

<style scoped lang="scss">
@use '~/assets/scss/utils/variables' as *;
@use '~/assets/scss/utils/mixins' as *;
@use 'sass:map';

.discovery-edit-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @include respond-to(md) {
    padding: 2rem 2rem;
  }
}

.page-header {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  animation: fadeInUp 0.6s ease-out;

  .header-content {
    .header-title {
      display: flex;
      align-items: center;
      gap: 1rem;

      .back-button {
        color: theme-color(gray, 600);
        transition: map.get($transition, all);
        border-radius: 50%;
        width: 40px;
        height: 40px;

        &:hover {
          color: theme-color(primary, 600);
          background-color: rgba(59, 130, 246, 0.1);
          transform: translateX(-2px);
        }

        :global(.dark) & {
          color: theme-color(gray, 400);

          &:hover {
            color: theme-color(primary, 400);
          }
        }
      }

      .title {
        font-size: font-size(3xl);
        font-weight: map.get($font-weight, bold);
        color: theme-color(gray, 900);
        margin: 0 0 spacing(1) 0;

        :global(.dark) & {
          color: theme-color(gray, 100);
        }
      }

      .description {
        font-size: font-size(base);
        color: theme-color(gray, 600);
        margin: 0;

        :global(.dark) & {
          color: theme-color(gray, 400);
        }
      }
    }
  }
}

.loading-section,
.edit-section {
  max-width: 1600px;
  margin: 0 auto;
  animation: fadeInUp 0.6s ease-out 0.1s both;
}

.toolbar {
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  :global(.dark) & {
    background: theme-color(gray, 800);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
}

.main-content {
  height: calc(100vh - 200px);
  min-height: 600px;
}

.editor-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  :global(.dark) & {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  :deep(.el-card__body) {
    padding: 0;
    height: calc(100% - 60px);
  }
}

.visual-editor {
  height: 100%;
  position: relative;

  .diagram-container {
    width: 100%;
    height: 100%;
    border-radius: 0 0 8px 8px;
    overflow: hidden;
    background: #fafafa;
    position: relative;

    :global(.dark) & {
      background: #1a1a1a;
    }
  }
}

// 响应式设计
@include respond-to-max(md) {
  .discovery-edit-page {
    padding: 1rem 0.5rem;
  }

  .main-content {
    height: auto;
    min-height: 400px;
  }

  .visual-editor .diagram-container {
    height: 400px;
  }

  .toolbar {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;

    .toolbar-left {
      justify-content: center;
    }

    .toolbar-right {
      justify-content: center;
    }
  }
}

// 悬浮路径筛选面板样式
.floating-filter-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 280px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid theme-color(gray, 200);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  z-index: 100;
  padding: 16px;
  animation: slideInRight 0.3s ease-out;

  :global(.dark) & {
    background: rgba(31, 41, 55, 0.95);
    border-color: theme-color(gray, 600);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .filter-title {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 14px;
      font-weight: 600;
      color: theme-color(gray, 900);

      :global(.dark) & {
        color: theme-color(gray, 100);
      }

      .el-icon {
        color: theme-color(primary, 600);
        font-size: 16px;

        :global(.dark) & {
          color: theme-color(primary, 400);
        }
      }
    }

    .filter-actions {
      display: flex;
      gap: 4px;
    }
  }

  .filter-content {
    .filter-stats {
      margin-bottom: 12px;
      text-align: center;

      .stats-text {
        font-size: 12px;
        color: theme-color(gray, 600);
        font-weight: 500;
        background: theme-color(gray, 100);
        padding: 4px 8px;
        border-radius: 6px;
        display: inline-block;

        :global(.dark) & {
          color: theme-color(gray, 300);
          background: theme-color(gray, 700);
        }
      }
    }

    .slider-container {
      .slider-label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;
        color: theme-color(gray, 700);

        :global(.dark) & {
          color: theme-color(gray, 300);
        }

        .frequency-value {
          font-weight: 600;
          color: theme-color(primary, 600);
          font-size: 11px;
          background: theme-color(primary, 50);
          padding: 2px 6px;
          border-radius: 4px;

          :global(.dark) & {
            color: theme-color(primary, 400);
            background: theme-color(primary, 900);
          }
        }
      }

      :deep(.el-slider) {
        .el-slider__runway {
          background-color: theme-color(gray, 200);

          :global(.dark) & {
            background-color: theme-color(gray, 600);
          }
        }

        .el-slider__bar {
          background-color: theme-color(primary, 500);

          :global(.dark) & {
            background-color: theme-color(primary, 400);
          }
        }

        .el-slider__button {
          border-color: theme-color(primary, 500);
          width: 16px;
          height: 16px;

          :global(.dark) & {
            border-color: theme-color(primary, 400);
          }
        }
      }
    }
  }
}



@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// DFG图表样式
.dfg-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .dfg-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .dfg-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;

    .dimension-controls {
      display: flex;
      align-items: center;

      .el-radio-group {
        .el-radio-button {
          --el-radio-button-checked-bg-color: var(--el-color-primary);
          --el-radio-button-checked-text-color: #fff;
        }
      }
    }

    .control-divider {
      width: 1px;
      height: 24px;
      background-color: var(--el-border-color-light);
      margin: 0 4px;
      flex-shrink: 0;
    }

    .el-button {
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .zoom-indicator {
      font-size: 12px;
      color: var(--el-text-color-regular);
      min-width: 50px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;
      border: 1px solid var(--el-border-color-light);
      font-weight: 500;
      flex-shrink: 0;
    }
  }
}

.dfg-canvas {
  width: 100%;
  height: calc(100vh - 240px);
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  background-color: var(--el-bg-color-page);
  position: relative;
  overflow: hidden;
}

.dfg-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;

  :global(.dark) & {
    background-color: rgba(0, 0, 0, 0.8);
  }

  .dfg-loading-content {
    text-align: center;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid var(--el-border-color-light);
      border-top: 4px solid var(--el-color-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 16px;
    }

    .dfg-loading-text {
      color: var(--el-text-color-regular);
      font-size: 14px;
      margin: 0;
    }
  }
}

// 保存对话框样式
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

// 上下文菜单样式
.context-menu {
  position: fixed;
  z-index: 9999;
  background: white;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  min-width: 160px;
  font-size: 14px;

  .menu-items {
    .menu-item {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      cursor: pointer;
      transition: background-color 0.2s;
      gap: 8px;

      &:hover {
        background-color: var(--el-fill-color-light);
      }

      &.danger {
        color: var(--el-color-danger);

        &:hover {
          background-color: var(--el-color-danger-light-9);
        }
      }

      .el-icon {
        font-size: 16px;
      }

      span {
        flex: 1;
      }
    }

    .menu-divider {
      height: 1px;
      background-color: var(--el-border-color-lighter);
      margin: 4px 0;
    }
  }
}

// 筛选对话框样式
.filter-dialog-content {
  .filter-section {
    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: theme-color(gray, 800);
      margin-bottom: 16px;

      :global(.dark) & {
        color: theme-color(gray, 200);
      }
    }

    .frequency-display {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .frequency-label {
        font-size: 14px;
        color: theme-color(gray, 600);

        :global(.dark) & {
          color: theme-color(gray, 400);
        }
      }

      .frequency-value {
        font-size: 14px;
        color: theme-color(primary, 600);
        font-weight: 600;
        background: theme-color(primary, 50);
        padding: 4px 8px;
        border-radius: 4px;

        :global(.dark) & {
          color: theme-color(primary, 400);
          background: theme-color(primary, 900);
        }
      }
    }

    .slider-container {
      margin-bottom: 16px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 编辑提示样式
.dfg-title-section {
  display: flex;
  align-items: center;
  gap: 12px;

  .dfg-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: theme-color(gray, 800);

    :global(.dark) & {
      color: theme-color(gray, 200);
    }
  }

  .edit-tips {
    .help-icon {
      font-size: 16px;
      color: theme-color(primary, 500);
      cursor: help;
      transition: color 0.2s ease;

      &:hover {
        color: theme-color(primary, 600);
      }

      :global(.dark) & {
        color: theme-color(primary, 400);

        &:hover {
          color: theme-color(primary, 300);
        }
      }
    }
  }
}

.tips-content {
  max-width: 400px;
  font-size: 13px;
  line-height: 1.5;

  .tip-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    strong {
      display: block;
      margin-bottom: 6px;
      color: theme-color(gray, 800);
      font-weight: 600;

      :global(.dark) & {
        color: theme-color(gray, 200);
      }
    }

    ul {
      margin: 0;
      padding-left: 16px;
      color: theme-color(gray, 600);

      :global(.dark) & {
        color: theme-color(gray, 400);
      }

      li {
        margin-bottom: 4px;
        line-height: 1.4;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
