<template>
  <div class="create-model-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <el-button
            :icon="ArrowLeft"
            text
            class="back-button"
            @click="navigateTo('/conformance/models')"
          />
          <div>
            <h1 class="title">{{ isEdit ? '编辑BPMN模型' : '创建BPMN模型' }}</h1>
            <p class="description">{{ isEdit ? '修改现有的BPMN模型信息' : '上传或创建新的BPMN流程模型' }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建表单 -->
    <div class="form-section">
      <el-card>
        <template #header>
          <h3 class="section-title">模型信息</h3>
        </template>

        <el-form
          ref="formRef"
          :model="modelForm"
          :rules="formRules"
          label-width="120px"
          label-position="left"
        >
          <!-- 基本信息 -->
          <div class="form-group">
            <h4 class="form-group-title">基本信息</h4>

            <el-form-item label="模型名称" prop="name" required>
              <el-input
                v-model="modelForm.name"
                placeholder="请输入模型名称"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="关联流程" prop="processId" required>
              <el-select
                v-model="modelForm.processId"
                placeholder="请选择关联的流程"
                style="width: 100%"
                :loading="isLoadingProcesses"
                no-data-text="暂无流程数据"
              >
                <el-option
                  v-for="process in processes"
                  :key="process.id"
                  :label="process.name"
                  :value="process.id"
                />
                <template #empty>
                  <div class="empty-state">
                    <p class="empty-text">暂无流程数据</p>
                    <el-button
                      type="primary"
                      size="small"
                      @click="navigateTo('/discover')"
                    >
                      去创建流程
                    </el-button>
                  </div>
                </template>
              </el-select>
            </el-form-item>

            <el-form-item label="模型类型" prop="modelType" required>
              <el-select
                v-model="modelForm.modelType"
                placeholder="请选择模型类型"
                style="width: 100%"
              >
                <el-option
                  value="reference"
                  label="参考模型"
                >
                  <div class="model-type-option">
                    <span class="type-name">参考模型</span>
                    <span class="type-desc">用于符合性检查的标准模型</span>
                  </div>
                </el-option>
                <el-option
                  value="discovered"
                  label="发现模型"
                >
                  <div class="model-type-option">
                    <span class="type-name">发现模型</span>
                    <span class="type-desc">从事件日志中挖掘得到的模型</span>
                  </div>
                </el-option>
                <el-option
                  value="normative"
                  label="规范模型"
                >
                  <div class="model-type-option">
                    <span class="type-name">规范模型</span>
                    <span class="type-desc">业务规范定义的标准模型</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="模型描述">
              <el-input
                v-model="modelForm.description"
                type="textarea"
                :rows="3"
                placeholder="请输入模型描述（可选）"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </div>

          <!-- BPMN文件上传 -->
          <div class="form-group">
            <h4 class="form-group-title">BPMN文件</h4>

            <el-form-item label="上传方式" required>
              <el-radio-group v-model="uploadMethod">
                <el-radio value="file">上传文件</el-radio>
                <el-radio value="text">直接输入</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 隐藏的BPMN内容验证字段（文件模式下也挂载到表单模型） -->
            <el-form-item v-if="uploadMethod === 'file'" prop="bpmnXml" style="display: none;">
              <el-input v-model="modelForm.bpmnXml" />
            </el-form-item>

            <el-form-item
              v-if="uploadMethod === 'file'"
              label="BPMN文件"
            >
              <!-- 文件上传区域 - 只在没有文件时显示 -->
              <el-upload
                v-if="fileList.length === 0"
                ref="uploadRef"
                class="bpmn-upload"
                drag
                :auto-upload="false"
                :limit="1"
                accept=".bpmn,.xml"
                :on-change="handleFileChange"
                :on-remove="handleFileRemove"
                :file-list="fileList"
              >
                <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
                <div class="el-upload__text">
                  将BPMN文件拖到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    只能上传 .bpmn 或 .xml 文件，且不超过 10MB
                  </div>
                </template>
              </el-upload>

              <!-- 已上传文件显示区域 -->
              <div v-if="fileList.length > 0" class="uploaded-file-display">
                <div class="file-item">
                  <div class="file-icon">
                    <el-icon><Document /></el-icon>
                  </div>
                  <div class="file-info">
                    <div class="file-name">{{ fileList[0].name }}</div>
                    <div class="file-size">{{ formatFileSize(fileList[0].size) }}</div>
                  </div>
                  <div class="file-actions">
                    <el-button
                      type="danger"
                      size="small"
                      :icon="Delete"
                      circle
                      text
                      @click="handleFileRemove"
                    />
                  </div>
                </div>
              </div>
            </el-form-item>

            <el-form-item
              v-if="uploadMethod === 'text'"
              label="BPMN内容"
              prop="bpmnXml"
              :required="!isEdit"
            >
              <el-input
                v-model="modelForm.bpmnXml"
                type="textarea"
                :rows="10"
                placeholder="请粘贴BPMN XML内容"
                class="bpmn-textarea"
                @input="handleBpmnXmlInput"
              />
              <div class="form-help">
                请确保输入的是有效的BPMN 2.0 XML格式
              </div>
            </el-form-item>
          </div>

          <!-- 验证结果 -->
          <div v-if="validationResult" class="validation-section">
            <h4 class="form-group-title">验证结果</h4>

            <el-alert
              :title="validationResult.isValid ? '模型验证通过' : '模型验证失败'"
              :type="validationResult.isValid ? 'success' : 'error'"
              :closable="false"
              show-icon
            >
              <template v-if="!validationResult.isValid">
                <div class="validation-errors">
                  <h5>错误信息：</h5>
                  <ul>
                    <li v-for="error in validationResult.errors" :key="error">
                      {{ error }}
                    </li>
                  </ul>
                </div>
              </template>

              <template v-if="validationResult.warnings.length > 0">
                <div class="validation-warnings">
                  <h5>警告信息：</h5>
                  <ul>
                    <li v-for="warning in validationResult.warnings" :key="warning">
                      {{ warning }}
                    </li>
                  </ul>
                </div>
              </template>

              <template v-if="validationResult.isValid && validationResult.modelInfo">
                <div class="model-info">
                  <h5>模型信息：</h5>
                  <div class="info-grid">
                    <div class="info-item">
                      <span class="info-label">活动数量：</span>
                      <span class="info-value">{{ validationResult.modelInfo.activities }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">路径数量：</span>
                      <span class="info-value">{{ validationResult.modelInfo.paths }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">复杂度：</span>
                      <span class="info-value">{{ validationResult.modelInfo.complexity }}</span>
                    </div>
                  </div>
                </div>
              </template>
            </el-alert>
          </div>

          <!-- 调试信息 -->
          <div v-if="isDev" class="debug-panel">
            <h4>调试信息</h4>
            <pre>{{ JSON.stringify(debugInfo, null, 2) }}</pre>
          </div>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button
              type="primary"
              size="large"
              :loading="isValidating"
              :disabled="!canValidate"
              @click="validateModel"
            >
              <el-icon><CircleCheck /></el-icon>
              验证模型
              <span v-if="!canValidate" style="font-size: 12px; opacity: 0.7;">
                (需要BPMN内容)
              </span>
            </el-button>
            <el-button
              type="success"
              size="large"
              :loading="isSaving"
              :disabled="!canSave"
              @click="saveModel"
            >
              <el-icon><Check /></el-icon>
              {{ isEdit ? '更新模型' : '保存模型' }}
            </el-button>
            <el-button
              size="large"
              @click="navigateTo('/conformance/models')"
            >
              取消
            </el-button>
          </div>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ArrowLeft,
  UploadFilled,
  CircleCheck,
  Check,
  Document,
  Delete
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules, UploadFile, UploadFiles } from 'element-plus'
import { useApi } from '~/utils/api'
import type { Process, BpmnModelValidation } from '~/types'

// 页面配置
definePageMeta({
  layout: 'default'
})

// 路由参数
const route = useRoute()
const modelId = route.params.id ? parseInt(route.params.id as string) : null
const isEdit = computed(() => !!modelId)

// 设置页面标题
useHead({
  title: computed(() => `${isEdit.value ? '编辑' : '创建'}BPMN模型 - ProMax`)
})

// 状态
const isValidating = ref(false)
const isSaving = ref(false)
const isLoadingProcesses = ref(false)
const processes = ref<Process[]>([])
const formRef = ref<FormInstance>()
const uploadRef = ref()
const uploadMethod = ref<'file' | 'text'>('file')
const fileList = ref<UploadFile[]>([])
const validationResult = ref<BpmnModelValidation | null>(null)

// 表单数据
const modelForm = ref({
  name: '',
  description: '',
  processId: null as unknown as number,
  bpmnXml: '',
  modelType: 'reference' as 'reference' | 'discovered' | 'normative'
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入模型名称', trigger: 'blur' },
    { min: 2, max: 100, message: '模型名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  processId: [
    { required: true, message: '请选择关联流程', trigger: 'change' }
  ],
  modelType: [
    { required: true, message: '请选择模型类型', trigger: 'change' }
  ],
  bpmnXml: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (isEdit.value) {
          callback()
          return
        }
        const xml = value as string
        if (!xml || xml.trim().length === 0) {
          callback(new Error(uploadMethod.value === 'file' ? '请上传BPMN文件' : '请输入BPMN XML内容'))
        } else {
          callback()
        }
      },
      trigger: ['change', 'blur']
    }
  ]
}

// API
const api = useApi()

const isDev = computed(() => {
  return import.meta.dev
})

const canValidate = computed(() => {
  // 无论哪种模式，都需要有BPMN XML内容才能验证
  return modelForm.value.bpmnXml && modelForm.value.bpmnXml.trim().length > 0
})

const canSave = computed(() => {
  return validationResult.value?.isValid &&
         modelForm.value.name.trim() &&
         modelForm.value.processId &&
         modelForm.value.processId !== null &&
         !isSaving.value
})

// 调试信息 - 帮助了解按钮状态
const debugInfo = computed(() => {
  return {
    hasName: !!modelForm.value.name.trim(),
    hasProcessId: !!(modelForm.value.processId && modelForm.value.processId !== null),
    hasBpmnXml: !!modelForm.value.bpmnXml.trim(),
    uploadMethod: uploadMethod.value,
    fileListLength: fileList.value.length,
    isValidated: !!validationResult.value?.isValid,
    canValidate: canValidate.value,
    canSave: canSave.value
  }
})

const formatFileSize = (bytes: number | undefined): string => {
  if (!bytes) return '0 B'

  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))

  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

const loadProcesses = async () => {
  try {
    isLoadingProcesses.value = true
    const result = await api.getProcesses()
    processes.value = Array.isArray(result) ? result : []

    if (processes.value.length === 0) {
      console.log('No processes found')
    }
  } catch (error) {
    console.error('Failed to load processes:', error)
    ElMessage.error('加载流程列表失败')
    processes.value = []
  } finally {
    isLoadingProcesses.value = false
  }
}

const loadModel = async () => {
  if (!modelId) return

  try {
    const model = await api.getBpmnModel(modelId)
    modelForm.value = {
      name: model.name,
      description: model.description || '',
      processId: model.processId,
      bpmnXml: model.bpmnXml,
      modelType: model.modelType || 'reference'
    }

    // 如果有BPMN内容，设置为文本输入模式
    if (model.bpmnXml) {
      uploadMethod.value = 'text'
    }
  } catch (error) {
    console.error('Failed to load model:', error)
    ElMessage.error('加载模型信息失败')
    navigateTo('/conformance/models')
  }
}

const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  if (file.raw) {
    // 更新文件列表
    fileList.value = files

    const reader = new FileReader()
    reader.onload = async (e) => {
      modelForm.value.bpmnXml = e.target?.result as string
      validationResult.value = null // 清除之前的验证结果

      // 触发表单验证，清除BPMN内容字段的错误
      setTimeout(async () => {
        formRef.value?.clearValidate(['bpmnXml'])
        // 手动触发验证以确保状态正确
        try {
          await formRef.value?.validateField('bpmnXml')
        } catch (error) {
          console.warn('BPMN XML内容验证失败', error)
        }

        // 自动验证模型
        if (canValidate.value) {
          await validateModel()
        }
      }, 100) // 稍微延长等待时间，确保表单状态更新完成
    }
    reader.readAsText(file.raw)
  }
}

const handleFileRemove = () => {
  // 清空文件列表
  fileList.value = []
  // 清空表单内容
  modelForm.value.bpmnXml = ''
  validationResult.value = null

  // 清空上传组件的文件列表
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

const handleBpmnXmlInput = () => {
  validationResult.value = null // 清除之前的验证结果
  // 触发BPMN内容验证
  setTimeout(() => {
    formRef.value?.validateField('bpmnXml').catch((error) => {
      console.warn('BPMN XML内容验证失败', error)
    })
  }, 100)
}

const validateModel = async () => {
  if (!canValidate.value) return

  try {
    isValidating.value = true

    // 创建临时模型进行验证
    const tempModel = await api.createBpmnModel({
      name: modelForm.value.name || 'temp-validation',
      description: 'Temporary model for validation',
      processId: modelForm.value.processId || 1,
      bpmnXml: modelForm.value.bpmnXml,
      modelType: modelForm.value.modelType
    })

    // 验证模型
    validationResult.value = await api.validateBpmnModel(tempModel.id)

    // 删除临时模型
    await api.deleteBpmnModel(tempModel.id)

    if (validationResult.value.isValid) {
      ElMessage.success('模型验证通过')
    } else {
      ElMessage.warning('模型验证失败，请检查错误信息')
    }
  } catch (error) {
    console.error('Failed to validate model:', error)
    ElMessage.error('模型验证失败')
  } finally {
    isValidating.value = false
  }
}

const saveModel = async () => {
  if (!formRef.value) return

  try {
    // 添加调试信息
    console.log('保存模型前的状态:', {
      name: modelForm.value.name,
      processId: modelForm.value.processId,
      bpmnXml: modelForm.value.bpmnXml ? '有内容' : '无内容',
      bpmnXmlLength: modelForm.value.bpmnXml?.length || 0,
      uploadMethod: uploadMethod.value,
      isEdit: isEdit.value,
      canSave: canSave.value
    })

    await formRef.value.validate()

    isSaving.value = true

    if (isEdit.value && modelId) {
      // 更新模型
      await api.updateBpmnModel(modelId, {
        name: modelForm.value.name,
        description: modelForm.value.description,
        bpmnXml: modelForm.value.bpmnXml
      })
      ElMessage.success('模型更新成功')
    } else {
      // 创建模型
      await api.createBpmnModel({
        name: modelForm.value.name,
        description: modelForm.value.description,
        processId: modelForm.value.processId,
        bpmnXml: modelForm.value.bpmnXml,
        modelType: modelForm.value.modelType
      })
      ElMessage.success('模型创建成功')
    }

    navigateTo('/conformance/models')
  } catch (error) {
    console.error('Failed to save model:', error)
    ElMessage.error(isEdit.value ? '模型更新失败' : '模型创建失败')
  } finally {
    isSaving.value = false
  }
}

// 页面加载时获取数据
onMounted(async () => {
  await loadProcesses()
  if (isEdit.value) {
    await loadModel()
  }
})
</script>

<style scoped lang="scss">
@use '~/assets/scss/utils/variables' as *;
@use '~/assets/scss/utils/mixins' as *;
@use 'sass:map';

.create-model-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @include respond-to(md) {
    padding: 2rem 2rem;
  }
}

.page-header {
  max-width: 1200px;
  margin: 0 auto 2rem auto;
  animation: fadeInUp 0.6s ease-out;

  .header-content {
    .header-title {
      display: flex;
      align-items: center;
      gap: 1rem;

      .back-button {
        color: theme-color(gray, 600);
        transition: map.get($transition, all);
        border-radius: 50%;
        width: 40px;
        height: 40px;

        &:hover {
          color: theme-color(primary, 600);
          background-color: rgba(59, 130, 246, 0.1);
          transform: translateX(-2px);
        }

        :global(.dark) & {
          color: theme-color(gray, 400);

          &:hover {
            color: theme-color(primary, 400);
          }
        }
      }

      .title {
        font-size: font-size(3xl);
        font-weight: map.get($font-weight, bold);
        color: theme-color(gray, 900);
        margin: 0 0 spacing(1) 0;

        :global(.dark) & {
          color: theme-color(gray, 100);
        }
      }

      .description {
        font-size: font-size(base);
        color: theme-color(gray, 600);
        margin: 0;

        :global(.dark) & {
          color: theme-color(gray, 400);
        }
      }
    }
  }
}

.form-section {
  max-width: 1200px;
  margin: 0 auto;
  animation: fadeInUp 0.6s ease-out 0.1s both;

  .section-title {
    font-size: font-size(lg);
    font-weight: map.get($font-weight, semibold);
    color: theme-color(gray, 900);
    margin: 0;

    :global(.dark) & {
      color: theme-color(gray, 100);
    }
  }
}

.form-group {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid theme-color(gray, 200);

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  :global(.dark) & {
    border-bottom-color: theme-color(gray, 700);
  }

  .form-group-title {
    font-size: font-size(lg);
    font-weight: map.get($font-weight, semibold);
    color: theme-color(gray, 900);
    margin: 0 0 1.5rem 0;

    :global(.dark) & {
      color: theme-color(gray, 100);
    }
  }
}

.bpmn-upload {
  :global(.el-upload-dragger) {
    width: 100%;
    height: 180px;
    border: 2px dashed theme-color(gray, 300);
    border-radius: map.get($border-radius, lg);
    background-color: theme-color(gray, 50);
    transition: map.get($transition, all);

    &:hover {
      border-color: theme-color(primary, 400);
      background-color: theme-color(primary, 50);
    }

    :global(.dark) & {
      border-color: theme-color(gray, 600);
      background-color: theme-color(gray, 800);

      &:hover {
        border-color: theme-color(primary, 500);
        background-color: theme-color(primary, 900);
      }
    }
  }

  :global(.el-icon--upload) {
    font-size: 3rem;
    color: theme-color(gray, 400);
    margin-bottom: 1rem;

    :global(.dark) & {
      color: theme-color(gray, 500);
    }
  }

  :global(.el-upload__text) {
    color: theme-color(gray, 600);
    font-size: font-size(base);

    :global(.dark) & {
      color: theme-color(gray, 400);
    }

    em {
      color: theme-color(primary, 600);
      font-style: normal;

      :global(.dark) & {
        color: theme-color(primary, 400);
      }
    }
  }

  :global(.el-upload__tip) {
    color: theme-color(gray, 500);
    font-size: font-size(sm);
    margin-top: 0.5rem;

    :global(.dark) & {
      color: theme-color(gray, 500);
    }
  }
}

.uploaded-file-display {
  width: 100%;
  max-width: 260px;

  .file-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 1rem;
    height: 60px;
    border: 1px solid theme-color(gray, 300);
    border-radius: map.get($border-radius, lg);
    background-color: theme-color(gray, 50);

    :global(.dark) & {
      border-color: theme-color(gray, 600);
      background-color: theme-color(gray, 800);
    }

    .file-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      border-radius: map.get($border-radius, md);
      background-color: theme-color(primary, 100);
      color: theme-color(primary, 600);
      flex-shrink: 0;

      :global(.dark) & {
        background-color: theme-color(primary, 900);
        color: theme-color(primary, 400);
      }

      :global(.el-icon) {
        font-size: 1.125rem;
      }
    }

    .file-info {
      flex: 1;
      min-width: 0;

      .file-name {
        font-weight: map.get($font-weight, medium);
        color: theme-color(gray, 900);
        word-break: break-all;

        :global(.dark) & {
          color: theme-color(gray, 100);
        }
      }

      .file-size {
        font-size: font-size(sm);
        color: theme-color(gray, 500);

        :global(.dark) & {
          color: theme-color(gray, 400);
        }
      }
    }

    .file-actions {
      flex-shrink: 0;
    }
  }
}

.bpmn-textarea {
  :global(.el-textarea__inner) {
    font-family: map.get($font-family, mono);
    font-size: font-size(sm);
    line-height: 1.5;
  }
}

.form-help {
  font-size: font-size(sm);
  color: theme-color(gray, 500);
  margin-top: 0.25rem;

  :global(.dark) & {
    color: theme-color(gray, 500);
  }
}

.validation-section {
  margin-top: 2rem;

  .validation-errors,
  .validation-warnings {
    margin-top: 1rem;

    h5 {
      font-size: font-size(sm);
      font-weight: map.get($font-weight, semibold);
      margin: 0 0 0.5rem 0;
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.25rem;
        font-size: font-size(sm);
      }
    }
  }

  .model-info {
    margin-top: 1rem;

    h5 {
      font-size: font-size(sm);
      font-weight: map.get($font-weight, semibold);
      margin: 0 0 0.5rem 0;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 1rem;

      @include respond-to-max(md) {
        grid-template-columns: 1fr;
      }

      .info-item {
        display: flex;
        gap: 0.5rem;
        font-size: font-size(sm);

        .info-label {
          font-weight: map.get($font-weight, medium);
        }

        .info-value {
          color: theme-color(primary, 600);
          font-weight: map.get($font-weight, semibold);

          :global(.dark) & {
            color: theme-color(primary, 400);
          }
        }
      }
    }
  }
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  padding-top: 2rem;

  @include respond-to-max(md) {
    flex-direction: column;
  }
}

// 模型类型选择器样式
.model-type-option {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;

  .type-name {
    font-weight: 500;
    color: theme-color(gray, 900);

    :global(.dark) & {
      color: theme-color(gray, 100);
    }
  }

  .type-desc {
    font-size: font-size(xs);
    color: theme-color(gray, 500);

    :global(.dark) & {
      color: theme-color(gray, 400);
    }
  }
}

// 调试面板样式
.debug-panel {
  margin: 2rem 0;
  padding: 1rem;
  background-color: theme-color(gray, 50);
  border: 1px solid theme-color(gray, 200);
  border-radius: map.get($border-radius, md);

  :global(.dark) & {
    background-color: theme-color(gray, 800);
    border-color: theme-color(gray, 700);
  }

  h4 {
    margin: 0 0 1rem 0;
    color: theme-color(gray, 700);
    font-size: font-size(sm);

    :global(.dark) & {
      color: theme-color(gray, 300);
    }
  }

  pre {
    margin: 0;
    font-size: font-size(xs);
    color: theme-color(gray, 600);
    background: none;
    border: none;
    padding: 0;

    :global(.dark) & {
      color: theme-color(gray, 400);
    }
  }
}

// 空状态样式
.empty-state {
  padding: 1rem;
  text-align: center;

  .empty-text {
    color: theme-color(gray, 500);
    font-size: font-size(sm);
    margin: 0 0 0.75rem 0;

    :global(.dark) & {
      color: theme-color(gray, 400);
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
