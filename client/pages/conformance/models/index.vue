<template>
  <div class="models-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <el-button
            @click="navigateTo('/conformance')"
            :icon="ArrowLeft"
            text
            class="back-button"
          />
          <div>
            <h1 class="title">BPMN模型管理</h1>
            <p class="description">管理流程模型，用于符合性检查</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            :icon="Plus"
            @click="navigateTo('/conformance/models/create')"
          >
            创建模型
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filters-section">
      <el-card>
        <div class="filters-content">
          <div class="search-box">
            <el-input
              v-model="searchQuery"
              placeholder="搜索模型名称或描述..."
              :prefix-icon="Search"
              clearable
              @input="handleSearch"
            />
          </div>
          <div class="filter-controls">
            <el-select
              v-model="selectedProcess"
              placeholder="选择流程"
              clearable
              @change="handleFilterChange"
              :loading="isLoadingProcesses"
            >
              <el-option
                v-for="process in processes"
                :key="process.id"
                :label="process.name"
                :value="process.id"
              />
            </el-select>
            <el-select
              v-model="validationFilter"
              placeholder="验证状态"
              clearable
              @change="handleFilterChange"
            >
              <el-option label="全部" value="" />
              <el-option label="有效" value="valid" />
              <el-option label="无效" value="invalid" />
            </el-select>
            <el-select
              v-model="statusFilter"
              placeholder="模型状态"
              clearable
              @change="handleFilterChange"
            >
              <el-option label="全部" value="" />
              <el-option label="活跃" value="active" />
              <el-option label="草稿" value="draft" />
              <el-option label="已归档" value="archived" />
            </el-select>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <div class="stats-grid">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ models.length }}</div>
            <div class="stat-label">总模型数</div>
          </div>
        </el-card>
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ activeModelsCount }}</div>
            <div class="stat-label">活跃模型</div>
          </div>
        </el-card>
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ validModelsCount }}</div>
            <div class="stat-label">有效模型</div>
          </div>
        </el-card>
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ filteredModels.length }}</div>
            <div class="stat-label">筛选结果</div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 模型列表 -->
    <div class="models-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3 class="section-title">模型列表</h3>
            <div class="section-actions">
              <el-button
                :icon="Refresh"
                @click="loadModels"
                :loading="isLoading"
              >
                刷新
              </el-button>
            </div>
          </div>
        </template>

        <div v-if="isLoading" class="loading-state">
          <el-skeleton :rows="5" animated />
        </div>

        <div v-else-if="filteredModels.length === 0" class="empty-state">
          <el-icon class="empty-icon"><Document /></el-icon>
          <h4 class="empty-title">
            {{ searchQuery || selectedProcess || validationFilter || statusFilter ? '未找到匹配的模型' : '暂无BPMN模型' }}
          </h4>
          <p class="empty-description">
            {{ searchQuery || selectedProcess || validationFilter || statusFilter ? '请尝试调整搜索条件或清除过滤器' : '创建您的第一个BPMN模型来开始流程符合性检查' }}
          </p>
          <div class="empty-actions">
            <el-button
              v-if="!searchQuery && !selectedProcess && !validationFilter && !statusFilter"
              type="primary"
              :icon="Plus"
              @click="navigateTo('/conformance/models/create')"
            >
              创建模型
            </el-button>
            <el-button
              v-else
              @click="clearFilters"
            >
              清除过滤器
            </el-button>
          </div>
        </div>

        <div v-else class="models-list">
          <div 
            v-for="model in filteredModels" 
            :key="model.id"
            class="model-item"
          >
            <div class="model-info">
              <div class="model-header">
                <div class="model-title-section">
                  <h4 class="model-name">{{ model.name }}</h4>
                  <span v-if="model.description" class="model-description">
                    {{ model.description }}
                  </span>
                </div>
                <div class="model-badges">
                  <!-- 主要状态：优先显示模型状态，如果是活跃状态则显示验证状态 -->
                  <el-tag
                    v-if="model.status === 'active'"
                    :type="getModelValidationStatus(model) === 'valid' ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ getModelValidationStatus(model) === 'valid' ? '有效模型' : '无效模型' }}
                  </el-tag>
                  <el-tag
                    v-else
                    :type="getModelStatusType(model.status)"
                    size="small"
                  >
                    {{ getModelStatusText(model.status) }}
                  </el-tag>

                  <!-- 模型类型 -->
                  <el-tag
                    :type="getModelTypeColor(model.modelType)"
                    size="small"
                  >
                    {{ getModelTypeText(model.modelType) }}
                  </el-tag>

                  <!-- 活动数量（如果有的话） -->
                  <el-tag
                    v-if="model.activities && model.activities.length > 0"
                    type="info"
                    size="small"
                  >
                    {{ model.activities.length }} 个活动
                  </el-tag>
                </div>
              </div>
              <div class="model-meta">
                <span class="meta-item">
                  <el-icon><Calendar /></el-icon>
                  {{ formatDateTime(model.createdAt) }}
                </span>
                <span class="meta-item">
                  <el-icon><User /></el-icon>
                  {{ getProcessName(model.processId) }}
                </span>
                <span class="meta-item">
                  <el-icon><Document /></el-icon>
                  {{ getModelTypeText(model.modelType) }}
                </span>
                <span v-if="model.version" class="meta-item">
                  <el-icon><Collection /></el-icon>
                  v{{ model.version }}
                </span>
                <span v-if="model.author" class="meta-item">
                  <el-icon><User /></el-icon>
                  {{ model.author }}
                </span>
              </div>
            </div>
            <div class="model-actions">
              <el-button
                size="small"
                :icon="View"
                @click="viewModel(model)"
              >
                查看
              </el-button>
              <el-button
                size="small"
                type="primary"
                :icon="Edit"
                @click="editModel(model)"
              >
                编辑
              </el-button>
              <el-dropdown @command="handleModelAction" trigger="click">
                <el-button
                  size="small"
                  :icon="MoreFilled"
                  circle
                />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      :command="{ action: 'validate', model }"
                      :icon="CircleCheck"
                    >
                      验证模型
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{ action: 'activate', model }"
                      :icon="CircleCheck"
                      v-if="model.status !== 'active'"
                    >
                      激活模型
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{ action: 'archive', model }"
                      :icon="Document"
                      v-if="model.status === 'active'"
                    >
                      归档模型
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{ action: 'check', model }"
                      :icon="Search"
                    >
                      符合性检查
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{ action: 'download', model }"
                      :icon="Download"
                    >
                      下载BPMN
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{ action: 'delete', model }"
                      :icon="Delete"
                      divided
                    >
                      删除模型
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ArrowLeft,
  Plus,
  Search,
  Refresh,
  Document,
  Calendar,
  User,
  MoreFilled,
  CircleCheck,
  Download,
  Delete,
  Collection,
  View,
  Edit
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useApi } from '~/utils/api'
import type { BpmnModel, Process } from '~/types'

// 页面配置
definePageMeta({
  layout: 'default'
})

// 设置页面标题
useHead({
  title: 'BPMN模型管理 - ProMax'
})

// 状态
const isLoading = ref(true)
const isLoadingProcesses = ref(false)
const models = ref<BpmnModel[]>([])
const processes = ref<Process[]>([])
const searchQuery = ref('')
const selectedProcess = ref<number | null>(null)
const validationFilter = ref('')
const statusFilter = ref('')

// API
const api = useApi()

// 计算属性
const filteredModels = computed(() => {
  let filtered = models.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(model => 
      model.name.toLowerCase().includes(query) ||
      (model.description && model.description.toLowerCase().includes(query))
    )
  }

  // 流程过滤
  if (selectedProcess.value) {
    filtered = filtered.filter(model => model.processId === selectedProcess.value)
  }

  // 验证状态过滤
  if (validationFilter.value) {
    if (validationFilter.value === 'valid') {
      filtered = filtered.filter(model => getModelValidationStatus(model) === 'valid')
    } else if (validationFilter.value === 'invalid') {
      filtered = filtered.filter(model => getModelValidationStatus(model) === 'invalid')
    }
  }

  // 模型状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(model => model.status === statusFilter.value)
  }

  return filtered
})

// 统计计算属性
const activeModelsCount = computed(() => {
  return models.value.filter(model => model.status === 'active').length
})

const validModelsCount = computed(() => {
  return models.value.filter(model => getModelValidationStatus(model) === 'valid').length
})

// 方法
const loadModels = async () => {
  try {
    isLoading.value = true
    const result = await api.getBpmnModels()
    models.value = Array.isArray(result) ? result : []
    console.log('加载的模型列表:', models.value)
  } catch (error) {
    console.error('Failed to load models:', error)
    ElMessage.error('加载模型列表失败')
    models.value = []
  } finally {
    isLoading.value = false
  }
}

const loadProcesses = async () => {
  try {
    isLoadingProcesses.value = true
    const result = await api.getProcesses()
    processes.value = Array.isArray(result) ? result : []
    console.log('加载的流程列表:', processes.value)
  } catch (error) {
    console.error('Failed to load processes:', error)
    processes.value = []
  } finally {
    isLoadingProcesses.value = false
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleFilterChange = () => {
  // 过滤逻辑已在计算属性中处理
}

const clearFilters = () => {
  searchQuery.value = ''
  selectedProcess.value = null
  validationFilter.value = ''
  statusFilter.value = ''
}

const viewModel = (model: BpmnModel) => {
  navigateTo(`/conformance/models/${model.id}`)
}

const editModel = (model: BpmnModel) => {
  navigateTo(`/conformance/models/${model.id}/edit`)
}

const handleModelAction = async ({ action, model }: { action: string, model: BpmnModel }) => {
  switch (action) {
    case 'validate':
      await validateModel(model)
      break
    case 'activate':
      await activateModel(model)
      break
    case 'archive':
      await archiveModel(model)
      break
    case 'check':
      navigateTo(`/conformance/check?modelId=${model.id}`)
      break
    case 'download':
      downloadModel(model)
      break
    case 'delete':
      await deleteModel(model)
      break
  }
}

const validateModel = async (model: BpmnModel) => {
  try {
    const result = await api.validateBpmnModel(model.id)
    if (result.isValid) {
      ElMessage.success('模型验证通过')
    } else {
      ElMessage.warning(`模型验证失败：${result.errors.join(', ')}`)
    }
    await loadModels() // 重新加载以更新验证状态
  } catch (error) {
    console.error('Failed to validate model:', error)
    ElMessage.error('模型验证失败')
  }
}

const downloadModel = (model: BpmnModel) => {
  const blob = new Blob([model.bpmnXml], { type: 'application/xml' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${model.name}.bpmn`
  link.click()
  URL.revokeObjectURL(url)
}

const activateModel = async (model: BpmnModel) => {
  try {
    await api.activateBpmnModel(model.id)
    ElMessage.success('模型激活成功')
    await loadModels()
  } catch (error) {
    console.error('Failed to activate model:', error)
    ElMessage.error('模型激活失败')
  }
}

const archiveModel = async (model: BpmnModel) => {
  try {
    await ElMessageBox.confirm(
      `确定要归档模型 "${model.name}" 吗？归档后模型将不再活跃。`,
      '确认归档',
      {
        confirmButtonText: '归档',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await api.archiveBpmnModel(model.id)
    ElMessage.success('模型归档成功')
    await loadModels()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to archive model:', error)
      ElMessage.error('模型归档失败')
    }
  }
}

const deleteModel = async (model: BpmnModel) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模型 "${model.name}" 吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await api.deleteBpmnModel(model.id)
    ElMessage.success('模型删除成功')
    await loadModels()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete model:', error)
      ElMessage.error('模型删除失败')
    }
  }
}

const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取模型验证状态
const getModelValidationStatus = (model: any): 'valid' | 'invalid' => {
  // 如果有 isValid 属性，直接使用
  if (typeof model.isValid === 'boolean') {
    return model.isValid ? 'valid' : 'invalid'
  }

  // 否则根据 BPMN XML 内容简单判断
  if (model.bpmnXml && model.bpmnXml.trim().length > 0) {
    return model.bpmnXml.includes('<bpmn:') || model.bpmnXml.includes('<bpmn2:') ? 'valid' : 'invalid'
  }

  return 'invalid'
}

// 获取模型状态类型
const getModelStatusType = (status: string): string => {
  switch (status) {
    case 'active':
      return 'success'
    case 'draft':
      return 'warning'
    case 'archived':
      return 'info'
    default:
      return 'info'
  }
}

// 获取模型状态文本
const getModelStatusText = (status: string): string => {
  switch (status) {
    case 'active':
      return '活跃'
    case 'draft':
      return '草稿'
    case 'archived':
      return '已归档'
    default:
      return '未知'
  }
}

// 获取流程名称
const getProcessName = (processId: number): string => {
  const process = processes.value.find(p => p.id === processId)
  return process ? process.name : `流程 ${processId}`
}

// 获取模型类型文本
const getModelTypeText = (modelType: string): string => {
  switch (modelType) {
    case 'reference':
      return '参考模型'
    case 'discovered':
      return '发现模型'
    case 'normative':
      return '规范模型'
    default:
      return '未知类型'
  }
}

// 获取模型类型颜色
const getModelTypeColor = (modelType: string): string => {
  switch (modelType) {
    case 'reference':
      return 'primary'
    case 'discovered':
      return 'success'
    case 'normative':
      return 'warning'
    default:
      return 'info'
  }
}

// 页面加载时获取数据
onMounted(async () => {
  await Promise.all([
    loadModels(),
    loadProcesses()
  ])
})
</script>

<style scoped lang="scss">
@use '~/assets/scss/utils/variables' as *;
@use '~/assets/scss/utils/mixins' as *;
@use 'sass:map';

.models-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @include respond-to(md) {
    padding: 2rem 2rem;
  }
}

.page-header {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  animation: fadeInUp 0.6s ease-out;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;

    @include respond-to-max(md) {
      flex-direction: column;
      align-items: stretch;
    }
  }

  .header-title {
    display: flex;
    align-items: center;
    gap: 1rem;

    .back-button {
      color: theme-color(gray, 600);
      transition: map.get($transition, all);
      border-radius: 50%;
      width: 40px;
      height: 40px;

      &:hover {
        color: theme-color(primary, 600);
        background-color: rgba(59, 130, 246, 0.1);
        transform: translateX(-2px);
      }

      :global(.dark) & {
        color: theme-color(gray, 400);

        &:hover {
          color: theme-color(primary, 400);
        }
      }
    }

    .title {
      font-size: font-size(3xl);
      font-weight: map.get($font-weight, bold);
      color: theme-color(gray, 900);
      margin: 0 0 spacing(1) 0;

      :global(.dark) & {
        color: theme-color(gray, 100);
      }
    }

    .description {
      font-size: font-size(base);
      color: theme-color(gray, 600);
      margin: 0;

      :global(.dark) & {
        color: theme-color(gray, 400);
      }
    }
  }

  .header-actions {
    flex-shrink: 0;
  }
}

.filters-section {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  animation: fadeInUp 0.6s ease-out 0.1s both;

  .filters-content {
    display: flex;
    gap: 1rem;
    align-items: center;

    @include respond-to-max(md) {
      flex-direction: column;
      align-items: stretch;
    }

    .search-box {
      flex: 1;
      min-width: 300px;

      @include respond-to-max(md) {
        min-width: auto;
      }
    }

    .filter-controls {
      display: flex;
      gap: 1rem;
      flex-shrink: 0;

      @include respond-to-max(md) {
        flex-direction: column;
      }

      .el-select {
        width: 150px;

        @include respond-to-max(md) {
          width: 100%;
        }
      }
    }
  }
}

.stats-section {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  animation: fadeInUp 0.6s ease-out 0.15s both;

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;

    @include respond-to-max(md) {
      grid-template-columns: repeat(2, 1fr);
    }

    @include respond-to-max(sm) {
      grid-template-columns: 1fr;
    }

    .stat-card {
      border: 1px solid theme-color(gray, 200);
      transition: map.get($transition, all);

      &:hover {
        border-color: theme-color(primary, 300);
        box-shadow: shadow(sm);
        transform: translateY(-2px);
      }

      :global(.dark) & {
        border-color: theme-color(gray, 700);

        &:hover {
          border-color: theme-color(primary, 600);
        }
      }

      .stat-content {
        text-align: center;
        padding: 1rem;

        .stat-number {
          font-size: font-size(3xl);
          font-weight: map.get($font-weight, bold);
          color: theme-color(primary, 600);
          margin-bottom: 0.5rem;

          :global(.dark) & {
            color: theme-color(primary, 400);
          }
        }

        .stat-label {
          font-size: font-size(sm);
          color: theme-color(gray, 600);
          font-weight: map.get($font-weight, medium);

          :global(.dark) & {
            color: theme-color(gray, 400);
          }
        }
      }
    }
  }
}

.models-section {
  max-width: 1400px;
  margin: 0 auto;
  animation: fadeInUp 0.6s ease-out 0.2s both;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .section-title {
      font-size: font-size(lg);
      font-weight: map.get($font-weight, semibold);
      color: theme-color(gray, 900);
      margin: 0;

      :global(.dark) & {
        color: theme-color(gray, 100);
      }
    }

    .section-actions {
      display: flex;
      gap: 0.5rem;
    }
  }

  .loading-state,
  .empty-state {
    padding: 3rem 1rem;
    text-align: center;
  }

  .empty-state {
    .empty-icon {
      font-size: 3rem;
      color: theme-color(gray, 400);
      margin-bottom: 1rem;

      :global(.dark) & {
        color: theme-color(gray, 600);
      }
    }

    .empty-title {
      font-size: font-size(lg);
      font-weight: map.get($font-weight, semibold);
      color: theme-color(gray, 900);
      margin: 0 0 0.5rem 0;

      :global(.dark) & {
        color: theme-color(gray, 100);
      }
    }

    .empty-description {
      color: theme-color(gray, 600);
      margin: 0 0 1.5rem 0;

      :global(.dark) & {
        color: theme-color(gray, 400);
      }
    }
  }

  .models-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .model-item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 1.5rem;
      border: 1px solid theme-color(gray, 200);
      border-radius: map.get($border-radius, lg);
      background-color: white;
      transition: map.get($transition, all);

      &:hover {
        border-color: theme-color(primary, 300);
        box-shadow: shadow(sm);
      }

      :global(.dark) & {
        background-color: theme-color(gray, 800);
        border-color: theme-color(gray, 700);

        &:hover {
          border-color: theme-color(primary, 600);
        }
      }

      @include respond-to-max(md) {
        flex-direction: column;
        gap: 1rem;
      }

      .model-info {
        flex: 1;

        .model-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 1rem;

          @include respond-to-max(md) {
            flex-direction: column;
            gap: 1rem;
          }

          .model-title-section {
            flex: 1;

            .model-name {
              font-size: font-size(lg);
              font-weight: map.get($font-weight, semibold);
              color: theme-color(gray, 900);
              margin: 0 0 0.5rem 0;

              :global(.dark) & {
                color: theme-color(gray, 100);
              }
            }

            .model-description {
              font-size: font-size(sm);
              color: theme-color(gray, 600);
              line-height: 1.4;
              margin: 0;

              :global(.dark) & {
                color: theme-color(gray, 400);
              }
            }
          }

          .model-badges {
            display: flex;
            gap: 0.5rem;
            margin-right: 2rem;
            flex-wrap: wrap;
            align-items: flex-start;
          }
        }



        .model-meta {
          display: flex;
          gap: 1.5rem;
          flex-wrap: wrap;
          margin-top: 0.5rem;

          @include respond-to-max(md) {
            gap: 1rem;
          }

          .meta-item {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            font-size: font-size(xs);
            color: theme-color(gray, 500);
            background-color: theme-color(gray, 50);
            padding: 0.25rem 0.5rem;
            border-radius: map.get($border-radius, sm);
            border: 1px solid theme-color(gray, 200);

            :global(.dark) & {
              color: theme-color(gray, 400);
              background-color: theme-color(gray, 800);
              border-color: theme-color(gray, 700);
            }

            .el-icon {
              font-size: 0.75rem;
              color: theme-color(gray, 400);

              :global(.dark) & {
                color: theme-color(gray, 500);
              }
            }
          }
        }
      }

      .model-actions {
        display: flex;
        gap: 0.5rem;
        flex-shrink: 0;

        @include respond-to-max(md) {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
