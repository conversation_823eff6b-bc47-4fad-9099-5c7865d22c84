<template>
  <div class="edit-model-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <el-button
            @click="navigateTo('/conformance/models')"
            :icon="ArrowLeft"
            text
            class="back-button"
          />
          <div>
            <h1 class="title">编辑BPMN模型</h1>
            <p class="description">修改现有的BPMN模型信息</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-section">
      <el-card>
        <el-skeleton :rows="8" animated />
      </el-card>
    </div>

    <!-- 编辑表单 -->
    <div v-else-if="modelForm.id" class="form-section">
      <el-card>
        <template #header>
          <h3 class="section-title">模型信息</h3>
        </template>

        <el-form
          ref="formRef"
          :model="modelForm"
          :rules="formRules"
          label-width="120px"
          label-position="left"
        >
          <!-- 基本信息 -->
          <div class="form-group">
            <h4 class="form-group-title">基本信息</h4>
            
            <el-form-item label="模型名称" prop="name" required>
              <el-input
                v-model="modelForm.name"
                placeholder="请输入模型名称"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="模型类型" prop="modelType" required>
              <el-select
                v-model="modelForm.modelType"
                placeholder="请选择模型类型"
                style="width: 100%"
              >
                <el-option
                  value="reference"
                  label="参考模型"
                />
                <el-option
                  value="discovered"
                  label="发现模型"
                />
                <el-option
                  value="normative"
                  label="规范模型"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="模型描述">
              <el-input
                v-model="modelForm.description"
                type="textarea"
                :rows="3"
                placeholder="请输入模型描述（可选）"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </div>

          <!-- BPMN内容 -->
          <div class="form-group">
            <h4 class="form-group-title">BPMN内容</h4>
            
            <el-form-item label="编辑方式" required>
              <el-radio-group v-model="editMethod">
                <el-radio value="text">直接编辑</el-radio>
                <el-radio value="file">重新上传</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item 
              v-if="editMethod === 'text'"
              label="BPMN内容" 
              prop="bpmnXml"
            >
              <el-input
                v-model="modelForm.bpmnXml"
                type="textarea"
                :rows="12"
                placeholder="请输入BPMN XML内容"
                class="bpmn-textarea"
                @input="handleBpmnXmlInput"
              />
              <div class="form-help">
                请确保输入的是有效的BPMN 2.0 XML格式
              </div>
            </el-form-item>

            <el-form-item 
              v-if="editMethod === 'file'"
              label="BPMN文件" 
              prop="bpmnFile"
            >
              <el-upload
                ref="uploadRef"
                class="bpmn-upload"
                drag
                :auto-upload="false"
                :limit="1"
                accept=".bpmn,.xml"
                :on-change="handleFileChange"
                :on-remove="handleFileRemove"
                :file-list="fileList"
              >
                <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
                <div class="el-upload__text">
                  将BPMN文件拖到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    只能上传 .bpmn 或 .xml 文件，且不超过 10MB
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </div>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button
              type="primary"
              size="large"
              :loading="isSaving"
              @click="saveModel"
            >
              <el-icon><Check /></el-icon>
              更新模型
            </el-button>
            <el-button
              size="large"
              @click="navigateTo('/conformance/models')"
            >
              取消
            </el-button>
          </div>
        </el-form>
      </el-card>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-section">
      <el-card>
        <el-result
          icon="error"
          title="加载失败"
          sub-title="无法加载模型信息，请检查模型是否存在"
        >
          <template #extra>
            <el-button type="primary" @click="navigateTo('/conformance/models')">
              返回列表
            </el-button>
          </template>
        </el-result>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  ArrowLeft,
  UploadFilled,
  Check
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules, UploadFile, UploadFiles } from 'element-plus'
import { useApi } from '~/utils/api'
import type { BpmnModel } from '~/types'

// 页面配置
definePageMeta({
  layout: 'default'
})

// 路由参数
const route = useRoute()
const modelId = parseInt(route.params.id as string)

// 设置页面标题
useHead({
  title: '编辑BPMN模型 - ProMax'
})

// 状态
const isLoading = ref(true)
const isSaving = ref(false)
const formRef = ref<FormInstance>()
const uploadRef = ref()
const editMethod = ref<'text' | 'file'>('text')
const fileList = ref<UploadFile[]>([])

// 表单数据
const modelForm = ref({
  id: 0,
  name: '',
  description: '',
  bpmnXml: '',
  modelType: 'reference' as 'reference' | 'discovered' | 'normative'
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入模型名称', trigger: 'blur' },
    { min: 2, max: 100, message: '模型名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  modelType: [
    { required: true, message: '请选择模型类型', trigger: 'change' }
  ],
  bpmnXml: [
    { required: true, message: '请输入BPMN XML内容', trigger: 'blur' }
  ]
}

// API
const api = useApi()

// 方法
const loadModel = async () => {
  try {
    isLoading.value = true
    const model = await api.getBpmnModel(modelId)
    modelForm.value = {
      id: model.id,
      name: model.name,
      description: model.description || '',
      bpmnXml: model.bpmnXml,
      modelType: model.modelType || 'reference'
    }
  } catch (error) {
    console.error('Failed to load model:', error)
    ElMessage.error('加载模型信息失败')
  } finally {
    isLoading.value = false
  }
}

const handleFileChange = (file: UploadFile, _files: UploadFiles) => {
  if (file.raw) {
    const reader = new FileReader()
    reader.onload = (e) => {
      modelForm.value.bpmnXml = e.target?.result as string
    }
    reader.readAsText(file.raw)
  }
}

const handleFileRemove = () => {
  modelForm.value.bpmnXml = ''
}

const handleBpmnXmlInput = () => {
  // 可以在这里添加实时验证逻辑
}

const saveModel = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    isSaving.value = true

    await api.updateBpmnModel(modelId, {
      name: modelForm.value.name,
      description: modelForm.value.description,
      bpmnXml: modelForm.value.bpmnXml
    })
    
    ElMessage.success('模型更新成功')
    navigateTo('/conformance/models')
  } catch (error) {
    console.error('Failed to update model:', error)
    ElMessage.error('模型更新失败')
  } finally {
    isSaving.value = false
  }
}

// 页面加载时获取数据
onMounted(async () => {
  await loadModel()
})
</script>

<style scoped lang="scss">
@use '~/assets/scss/utils/variables' as *;
@use '~/assets/scss/utils/mixins' as *;
@use 'sass:map';

.edit-model-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @include respond-to(md) {
    padding: 2rem 2rem;
  }
}

.page-header {
  max-width: 1200px;
  margin: 0 auto 2rem auto;
  animation: fadeInUp 0.6s ease-out;

  .header-content {
    .header-title {
      display: flex;
      align-items: center;
      gap: 1rem;

      .back-button {
        color: theme-color(gray, 600);
        transition: map.get($transition, all);
        border-radius: 50%;
        width: 40px;
        height: 40px;

        &:hover {
          color: theme-color(primary, 600);
          background-color: rgba(59, 130, 246, 0.1);
          transform: translateX(-2px);
        }

        :global(.dark) & {
          color: theme-color(gray, 400);

          &:hover {
            color: theme-color(primary, 400);
          }
        }
      }

      .title {
        font-size: font-size(3xl);
        font-weight: map.get($font-weight, bold);
        color: theme-color(gray, 900);
        margin: 0 0 spacing(1) 0;

        :global(.dark) & {
          color: theme-color(gray, 100);
        }
      }

      .description {
        font-size: font-size(base);
        color: theme-color(gray, 600);
        margin: 0;

        :global(.dark) & {
          color: theme-color(gray, 400);
        }
      }
    }
  }
}

.loading-section,
.form-section,
.error-section {
  max-width: 1200px;
  margin: 0 auto;
  animation: fadeInUp 0.6s ease-out 0.1s both;

  .section-title {
    font-size: font-size(lg);
    font-weight: map.get($font-weight, semibold);
    color: theme-color(gray, 900);
    margin: 0;

    :global(.dark) & {
      color: theme-color(gray, 100);
    }
  }
}

.form-group {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid theme-color(gray, 200);

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  :global(.dark) & {
    border-bottom-color: theme-color(gray, 700);
  }

  .form-group-title {
    font-size: font-size(lg);
    font-weight: map.get($font-weight, semibold);
    color: theme-color(gray, 900);
    margin: 0 0 1.5rem 0;

    :global(.dark) & {
      color: theme-color(gray, 100);
    }
  }
}

.bpmn-upload {
  :global(.el-upload-dragger) {
    width: 100%;
    height: 180px;
    border: 2px dashed theme-color(gray, 300);
    border-radius: map.get($border-radius, lg);
    background-color: theme-color(gray, 50);
    transition: map.get($transition, all);

    &:hover {
      border-color: theme-color(primary, 400);
      background-color: theme-color(primary, 50);
    }

    :global(.dark) & {
      border-color: theme-color(gray, 600);
      background-color: theme-color(gray, 800);

      &:hover {
        border-color: theme-color(primary, 500);
        background-color: theme-color(primary, 900);
      }
    }
  }

  :global(.el-icon--upload) {
    font-size: 3rem;
    color: theme-color(gray, 400);
    margin-bottom: 1rem;

    :global(.dark) & {
      color: theme-color(gray, 500);
    }
  }

  :global(.el-upload__text) {
    color: theme-color(gray, 600);
    font-size: font-size(base);

    :global(.dark) & {
      color: theme-color(gray, 400);
    }

    em {
      color: theme-color(primary, 600);
      font-style: normal;

      :global(.dark) & {
        color: theme-color(primary, 400);
      }
    }
  }

  :global(.el-upload__tip) {
    color: theme-color(gray, 500);
    font-size: font-size(sm);
    margin-top: 0.5rem;

    :global(.dark) & {
      color: theme-color(gray, 500);
    }
  }
}

.bpmn-textarea {
  :global(.el-textarea__inner) {
    font-family: map.get($font-family, mono);
    font-size: font-size(sm);
    line-height: 1.5;
  }
}

.form-help {
  font-size: font-size(sm);
  color: theme-color(gray, 500);
  margin-top: 0.25rem;

  :global(.dark) & {
    color: theme-color(gray, 500);
  }
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  padding-top: 2rem;

  @include respond-to-max(md) {
    flex-direction: column;
  }
}



@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
