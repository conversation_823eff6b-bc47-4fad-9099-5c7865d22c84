<template>
  <div class="view-model-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <el-button
            @click="navigateTo('/conformance/models')"
            :icon="ArrowLeft"
            text
            class="back-button"
          />
          <div>
            <h1 class="title">{{ model?.name || '模型详情' }}</h1>
            <p class="description">查看BPMN模型的详细信息</p>
          </div>
        </div>
        <div class="header-actions" v-if="model && model.id">
          <el-button
            type="primary"
            :icon="Edit"
            @click="navigateTo(`/conformance/models/${model.id}/edit`)"
          >
            编辑模型
          </el-button>
          <el-dropdown @command="handleAction" trigger="click">
            <el-button :icon="MoreFilled">
              更多操作
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item 
                  command="validate"
                  :icon="CircleCheck"
                >
                  验证模型
                </el-dropdown-item>
                <el-dropdown-item 
                  command="check"
                  :icon="Search"
                >
                  符合性检查
                </el-dropdown-item>
                <el-dropdown-item 
                  command="download"
                  :icon="Download"
                >
                  下载BPMN
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-section">
      <el-card>
        <el-skeleton :rows="8" animated />
      </el-card>
    </div>

    <!-- 模型详情 -->
    <div v-else-if="model" class="content-section">
      <!-- 基本信息 -->
      <el-card class="info-card">
        <template #header>
          <h3 class="card-title">基本信息</h3>
        </template>
        
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">模型名称</span>
            <span class="info-value">{{ model?.name || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">模型类型</span>
            <el-tag :type="getModelTypeColor(model?.modelType || '')">
              {{ getModelTypeText(model?.modelType || '') }}
            </el-tag>
          </div>
          <div class="info-item">
            <span class="info-label">状态</span>
            <el-tag :type="getModelStatusType(model?.status || '')">
              {{ getModelStatusText(model?.status || '') }}
            </el-tag>
          </div>
          <div class="info-item">
            <span class="info-label">验证状态</span>
            <el-tag :type="getModelValidationStatus(model) === 'valid' ? 'success' : 'danger'">
              {{ getModelValidationStatus(model) === 'valid' ? '有效' : '无效' }}
            </el-tag>
          </div>
          <div class="info-item">
            <span class="info-label">关联流程</span>
            <span class="info-value">流程 ID: {{ model?.processId || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">活动数量</span>
            <span class="info-value">{{ getActivitiesCount(model) }} 个</span>
          </div>
          <div class="info-item">
            <span class="info-label">创建时间</span>
            <span class="info-value">{{ model?.createdAt ? formatDateTime(model.createdAt) : '-' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">更新时间</span>
            <span class="info-value">{{ model?.updatedAt ? formatDateTime(model.updatedAt) : '-' }}</span>
          </div>
        </div>

        <div v-if="model?.description" class="description-section">
          <h4 class="description-title">模型描述</h4>
          <p class="description-text">{{ model.description }}</p>
        </div>
      </el-card>

      <!-- BPMN内容 -->
      <el-card class="bpmn-card">
        <template #header>
          <div class="card-header">
            <h3 class="card-title">BPMN内容</h3>
            <div class="card-actions">
              <el-button
                size="small"
                :icon="Download"
                @click="downloadModel"
              >
                下载
              </el-button>
              <el-button
                size="small"
                :icon="DocumentCopy"
                @click="copyBpmnContent"
              >
                复制
              </el-button>
            </div>
          </div>
        </template>
        
        <div class="bpmn-content">
          <el-input
            :model-value="model?.bpmnXml || ''"
            type="textarea"
            :rows="20"
            readonly
            class="bpmn-textarea"
          />
        </div>
      </el-card>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-section">
      <el-card>
        <el-result
          icon="error"
          title="加载失败"
          sub-title="无法加载模型信息，请检查模型是否存在"
        >
          <template #extra>
            <el-button type="primary" @click="navigateTo('/conformance/models')">
              返回列表
            </el-button>
          </template>
        </el-result>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ArrowLeft,
  Edit,
  MoreFilled,
  CircleCheck,
  Search,
  Download,
  DocumentCopy
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useApi } from '~/utils/api'
import type { BpmnModel } from '~/types'

// 页面配置
definePageMeta({
  layout: 'default'
})

// 路由参数
const route = useRoute()
const modelId = parseInt(route.params.id as string)

// 状态
const isLoading = ref(true)
const model = ref<BpmnModel | null>(null)

// API
const api = useApi()

// 设置页面标题
useHead({
  title: '模型详情 - ProMax'
})

// 方法
const loadModel = async () => {
  try {
    isLoading.value = true
    model.value = await api.getBpmnModel(modelId)
  } catch (error) {
    console.error('Failed to load model:', error)
    ElMessage.error('加载模型信息失败')
  } finally {
    isLoading.value = false
  }
}

const handleAction = async (command: string) => {
  if (!model.value) return

  switch (command) {
    case 'validate':
      await validateModel()
      break
    case 'check':
      navigateTo(`/conformance/check?modelId=${model.value.id}`)
      break
    case 'download':
      downloadModel()
      break
  }
}

const validateModel = async () => {
  if (!model.value) return

  try {
    const result = await api.validateBpmnModel(model.value.id)
    if (result.isValid) {
      ElMessage.success('模型验证通过')
    } else {
      ElMessage.warning(`模型验证失败：${result.errors.join(', ')}`)
    }
  } catch (error) {
    console.error('Failed to validate model:', error)
    ElMessage.error('模型验证失败')
  }
}

const downloadModel = () => {
  if (!model.value) return

  const blob = new Blob([model.value.bpmnXml], { type: 'application/xml' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${model.value.name}.bpmn`
  link.click()
  URL.revokeObjectURL(url)
}

const copyBpmnContent = async () => {
  if (!model.value) return

  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(model.value.bpmnXml)
      ElMessage.success('BPMN内容已复制到剪贴板')
      return
    }
    
    // 回退方案
    const el = document.createElement('textarea')
    el.value = model.value.bpmnXml
    el.style.position = 'absolute'
    el.style.left = '-9999px'
    document.body.appendChild(el)
    el.select()
    const success = document.execCommand('copy')
    document.body.removeChild(el)
    
    if (success) {
      ElMessage.success('BPMN内容已复制到剪贴板')
    } else {
      throw new Error('execCommand返回失败')
    }
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// 辅助函数
const getModelTypeText = (modelType: string): string => {
  if (!modelType) return '未知类型'

  switch (modelType) {
    case 'reference':
      return '参考模型'
    case 'discovered':
      return '发现模型'
    case 'normative':
      return '规范模型'
    default:
      return '未知类型'
  }
}

const getModelTypeColor = (modelType: string): string => {
  if (!modelType) return 'info'

  switch (modelType) {
    case 'reference':
      return 'primary'
    case 'discovered':
      return 'success'
    case 'normative':
      return 'warning'
    default:
      return 'info'
  }
}

const getModelStatusType = (status: string): string => {
  if (!status) return 'info'

  switch (status) {
    case 'active':
      return 'success'
    case 'draft':
      return 'warning'
    case 'archived':
      return 'info'
    default:
      return 'info'
  }
}

const getModelStatusText = (status: string): string => {
  if (!status) return '未知'

  switch (status) {
    case 'active':
      return '活跃'
    case 'draft':
      return '草稿'
    case 'archived':
      return '已归档'
    default:
      return '未知'
  }
}

const getModelValidationStatus = (model: any): 'valid' | 'invalid' => {
  if (!model) return 'invalid'

  if (typeof model.isValid === 'boolean') {
    return model.isValid ? 'valid' : 'invalid'
  }

  if (model.bpmnXml && model.bpmnXml.trim().length > 0) {
    return model.bpmnXml.includes('<bpmn:') || model.bpmnXml.includes('<bpmn2:') ? 'valid' : 'invalid'
  }

  return 'invalid'
}

const getActivitiesCount = (model: BpmnModel | null): number => {
  if (!model) return 0

  // 如果有根级别的 activities 属性且是数组
  if (Array.isArray(model.activities)) {
    return model.activities.length
  }

  // 如果有 modelData.activities 属性且是数组（兼容当前数据格式）
  if (model.modelData && Array.isArray(model.modelData.activities)) {
    return model.modelData.activities.length
  }

  return 0
}

const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 页面加载时获取数据
onMounted(async () => {
  await loadModel()
})
</script>

<style scoped lang="scss">
@use '~/assets/scss/utils/variables' as *;
@use '~/assets/scss/utils/mixins' as *;
@use 'sass:map';

.view-model-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @include respond-to(md) {
    padding: 2rem 2rem;
  }
}

.page-header {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  animation: fadeInUp 0.6s ease-out;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;

    @include respond-to-max(md) {
      flex-direction: column;
      align-items: stretch;
    }
  }

  .header-title {
    display: flex;
    align-items: center;
    gap: 1rem;

    .back-button {
      color: theme-color(gray, 600);
      transition: map.get($transition, all);
      border-radius: 50%;
      width: 40px;
      height: 40px;

      &:hover {
        color: theme-color(primary, 600);
        background-color: rgba(59, 130, 246, 0.1);
        transform: translateX(-2px);
      }

      :global(.dark) & {
        color: theme-color(gray, 400);

        &:hover {
          color: theme-color(primary, 400);
        }
      }
    }

    .title {
      font-size: font-size(3xl);
      font-weight: map.get($font-weight, bold);
      color: theme-color(gray, 900);
      margin: 0 0 spacing(1) 0;

      :global(.dark) & {
        color: theme-color(gray, 100);
      }
    }

    .description {
      font-size: font-size(base);
      color: theme-color(gray, 600);
      margin: 0;

      :global(.dark) & {
        color: theme-color(gray, 400);
      }
    }
  }

  .header-actions {
    display: flex;
    gap: 1rem;
    flex-shrink: 0;

    @include respond-to-max(md) {
      width: 100%;
      justify-content: flex-end;
    }
  }
}

.loading-section,
.content-section,
.error-section {
  max-width: 1400px;
  margin: 0 auto;
  animation: fadeInUp 0.6s ease-out 0.1s both;
}

.content-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.info-card,
.bpmn-card {
  .card-title {
    font-size: font-size(lg);
    font-weight: map.get($font-weight, semibold);
    color: theme-color(gray, 900);
    margin: 0;

    :global(.dark) & {
      color: theme-color(gray, 100);
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-actions {
      display: flex;
      gap: 0.5rem;
    }
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;

  @include respond-to-max(md) {
    grid-template-columns: 1fr;
  }

  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: theme-color(gray, 50);
    border-radius: map.get($border-radius, md);
    border: 1px solid theme-color(gray, 200);

    :global(.dark) & {
      background-color: theme-color(gray, 800);
      border-color: theme-color(gray, 700);
    }

    .info-label {
      font-weight: map.get($font-weight, medium);
      color: theme-color(gray, 700);
      font-size: font-size(sm);

      :global(.dark) & {
        color: theme-color(gray, 300);
      }
    }

    .info-value {
      color: theme-color(gray, 900);
      font-weight: map.get($font-weight, semibold);

      :global(.dark) & {
        color: theme-color(gray, 100);
      }
    }
  }
}

.description-section {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid theme-color(gray, 200);

  :global(.dark) & {
    border-top-color: theme-color(gray, 700);
  }

  .description-title {
    font-size: font-size(base);
    font-weight: map.get($font-weight, semibold);
    color: theme-color(gray, 900);
    margin: 0 0 1rem 0;

    :global(.dark) & {
      color: theme-color(gray, 100);
    }
  }

  .description-text {
    color: theme-color(gray, 700);
    line-height: 1.6;
    margin: 0;

    :global(.dark) & {
      color: theme-color(gray, 300);
    }
  }
}

.bpmn-content {
  .bpmn-textarea {
    :global(.el-textarea__inner) {
      font-family: map.get($font-family, mono);
      font-size: font-size(xs);
      line-height: 1.4;
      background-color: theme-color(gray, 50);
      border: 1px solid theme-color(gray, 200);

      :global(.dark) & {
        background-color: theme-color(gray, 900);
        border-color: theme-color(gray, 700);
        color: theme-color(gray, 200);
      }
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
