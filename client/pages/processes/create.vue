<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-title">
        <el-button
          :icon="ArrowLeft"
          text
          class="back-button"
          title="返回流程列表"
          @click="navigateTo('/processes')"
        />
        <h1 class="title">
          创建新流程
        </h1>
      </div>
      <p class="subtitle">
        设计和配置您的业务流程，开始数据挖掘之旅
      </p>
    </div>

    <!-- 创建表单 -->
    <div class="form-container">
      <el-card>
        <el-form
          ref="formRef"
          :model="formState"
          :rules="formRules"
          label-width="120px"
          class="create-form"
          @submit.prevent="handleSubmit"
        >
          <!-- 流程名称 -->
          <el-form-item label="流程名称" prop="name" required>
            <el-input
              v-model="formState.name"
              placeholder="请输入流程名称"
              size="large"
            />
          </el-form-item>

          <!-- 流程描述 -->
          <el-form-item label="流程描述" prop="description">
            <el-input
              v-model="formState.description"
              type="textarea"
              placeholder="请描述这个流程的目的和范围"
              :rows="4"
            />
          </el-form-item>

          <!-- 业务领域 -->
          <el-form-item label="业务领域" prop="businessDomain">
            <el-select
              v-model="formState.businessDomain"
              placeholder="选择业务领域"
              filterable
              size="large"
              class="full-width-select"
            >
              <el-option
                v-for="option in businessDomainOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>

          <!-- 流程状态 -->
          <el-form-item label="初始状态" prop="status">
            <el-select
              v-model="formState.status"
              placeholder="选择初始状态"
              size="large"
              class="full-width-select"
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>

          <!-- 错误信息 -->
          <el-alert
            v-if="processesStore.error"
            :title="processesStore.error"
            type="error"
            show-icon
            closable
            @close="processesStore.clearError"
          />

          <!-- 操作按钮 -->
          <el-form-item>
            <div class="form-actions">
              <el-button
                size="large"
                @click="navigateTo('/processes')"
              >
                取消
              </el-button>
              <el-button
                type="primary"
                :loading="processesStore.isLoading"
                size="large"
                @click="handleSubmit"
              >
                创建流程
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft } from '@element-plus/icons-vue'
import { ProcessStatus } from '~/types'
import type { FormInstance, FormRules } from 'element-plus'

// 页面配置
definePageMeta({
  layout: 'default'
})

// 设置页面标题
useHead({
  title: '创建流程 - ProMax'
})

const processesStore = useProcessesStore()

// 表单引用
const formRef = ref<FormInstance>()

// 表单状态
const formState = reactive({
  name: '',
  description: '',
  businessDomain: '',
  status: ProcessStatus.DRAFT
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入流程名称', trigger: 'blur' },
    { min: 1, max: 100, message: '流程名称长度在 1 到 100 个字符', trigger: 'blur' }
  ]
}

// 业务领域选项
const businessDomainOptions = [
  { label: '电子商务', value: '电子商务' },
  { label: '制造业', value: '制造业' },
  { label: '金融服务', value: '金融服务' },
  { label: '医疗健康', value: '医疗健康' },
  { label: '教育培训', value: '教育培训' },
  { label: '物流运输', value: '物流运输' },
  { label: '人力资源', value: '人力资源' },
  { label: '客户服务', value: '客户服务' },
  { label: '研发创新', value: '研发创新' },
  { label: '其他', value: '其他' }
]

// 状态选项
const statusOptions = [
  { label: '草稿', value: ProcessStatus.DRAFT },
  { label: '活跃', value: ProcessStatus.ACTIVE }
]

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const newProcess = await processesStore.createProcess(formState)

        // 跳转到新创建的流程详情页
        await navigateTo(`/processes/${newProcess.id}`)
      } catch (error) {
        // 错误已在store中处理
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @media (max-width: 768px) {
    padding: 1rem 0.5rem;
  }
}

.page-header {
  max-width: 48rem;
  margin: 0 auto 3rem auto;
  text-align: center;

  .header-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;

    .back-button {
      position: absolute;
      left: 0;
      color: #6b7280;
      transition: all 0.2s ease;
      border-radius: 50%;
      width: 40px;
      height: 40px;

      &:hover {
        color: #3b82f6;
        background-color: rgba(59, 130, 246, 0.1);
        transform: translateX(-2px);
      }

      :global(.dark) & {
        color: #9ca3af;

        &:hover {
          color: #60a5fa;
          background-color: rgba(96, 165, 250, 0.1);
        }
      }
    }

    .title {
      font-size: 2.25rem;
      font-weight: 800;
      background: linear-gradient(135deg, #3b82f6, #8b5cf6);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin: 0;
      letter-spacing: -0.025em;

      :global(.dark) & {
        background: linear-gradient(135deg, #60a5fa, #a78bfa);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  .subtitle {
    color: #64748b;
    font-size: 1.125rem;
    font-weight: 500;
    margin: 0;
    opacity: 0.8;

    :global(.dark) & {
      color: #94a3b8;
    }
  }
}

.form-container {
  max-width: 48rem;
  margin: 0 auto;

  :deep(.el-card) {
    border-radius: 16px;
    border: none;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
    }

    :global(.dark) & {
      background: rgba(30, 41, 59, 0.95);
      border: 1px solid rgba(71, 85, 105, 0.3);
    }

    .el-card__body {
      padding: 2.5rem;

      @media (max-width: 768px) {
        padding: 1.5rem;
      }
    }
  }
}

.create-form {
  :deep(.el-form-item) {
    margin-bottom: 2rem;

    .el-form-item__label {
      font-weight: 600;
      color: #374151;
      font-size: 0.95rem;

      :global(.dark) & {
        color: #d1d5db;
      }
    }

    .el-input {
      .el-input__wrapper {
        border-radius: 12px;
        border: 2px solid #e5e7eb;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        &:hover {
          border-color: #3b82f6;
        }

        &.is-focus {
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        :global(.dark) & {
          border-color: #4b5563;
          background-color: #374151;

          &:hover {
            border-color: #60a5fa;
          }

          &.is-focus {
            border-color: #60a5fa;
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
          }
        }
      }
    }

    .el-textarea {
      .el-textarea__inner {
        border-radius: 12px;
        border: 2px solid #e5e7eb;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        resize: vertical;

        &:hover {
          border-color: #3b82f6;
        }

        &:focus {
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        :global(.dark) & {
          border-color: #4b5563;
          background-color: #374151;
          color: #f9fafb;

          &:hover {
            border-color: #60a5fa;
          }

          &:focus {
            border-color: #60a5fa;
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
          }
        }
      }
    }
  }

  :deep(.el-alert) {
    border-radius: 12px;
    border: none;
    margin-bottom: 1.5rem;
  }
}

.full-width-select {
  width: 100%;

  :deep(.el-select) {
    width: 100%;

    .el-select__wrapper {
      border-radius: 12px;
      border: 2px solid #e5e7eb;
      transition: all 0.2s ease;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      &:hover {
        border-color: #3b82f6;
      }

      &.is-focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      :global(.dark) & {
        border-color: #4b5563;
        background-color: #374151;

        &:hover {
          border-color: #60a5fa;
        }

        &.is-focus {
          border-color: #60a5fa;
          box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
        }
      }
    }
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2.5rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;

  :global(.dark) & {
    border-top-color: #4b5563;
  }

  :deep(.el-button) {
    border-radius: 12px;
    font-weight: 600;
    padding: 12px 24px;
    transition: all 0.2s ease;

    &:not(.el-button--primary) {
      border: 2px solid #e5e7eb;
      color: #6b7280;

      &:hover {
        border-color: #3b82f6;
        color: #3b82f6;
        transform: translateY(-1px);
      }

      :global(.dark) & {
        border-color: #4b5563;
        color: #9ca3af;
        background-color: transparent;

        &:hover {
          border-color: #60a5fa;
          color: #60a5fa;
        }
      }
    }

    &.el-button--primary {
      background: linear-gradient(135deg, #3b82f6, #8b5cf6);
      border: none;
      box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
      }

      &:active {
        transform: translateY(0);
      }

      :global(.dark) & {
        background: linear-gradient(135deg, #60a5fa, #a78bfa);
      }
    }
  }

  @media (max-width: 640px) {
    flex-direction: column-reverse;

    :deep(.el-button) {
      width: 100%;
    }
  }
}

// 添加页面进入动画
.page-container {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 表单项动画
.create-form :deep(.el-form-item) {
  animation: slideInLeft 0.5s ease-out;
  animation-fill-mode: both;

  @for $i from 1 through 6 {
    &:nth-child(#{$i}) {
      animation-delay: #{$i * 0.1}s;
    }
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
