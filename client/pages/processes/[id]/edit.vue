<template>
  <div class="page-container">
    <!-- 加载状态 -->
    <div v-if="pending" class="loading-state">
      <div class="loading-spinner"/>
      <p class="loading-text">加载中...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <el-icon class="error-icon">
        <WarningFilled />
      </el-icon>
      <h3 class="error-title">
        加载失败
      </h3>
      <p class="error-message">
        {{ error.message }}
      </p>
      <el-button plain @click="navigateTo('/processes')">
        返回流程列表
      </el-button>
    </div>

    <!-- 编辑表单 -->
    <div v-else-if="process">
      <!-- 页面标题 -->
      <div class="page-header">
        <div class="header-content">
          <el-button
            :icon="ArrowLeft"
            text
            class="back-button"
            title="返回流程详情"
            @click="navigateTo(`/processes/${processId}`)"
          />
          <div class="title-section">
            <h1 class="page-title">
              编辑流程
            </h1>
            <p class="subtitle">
              修改流程信息和配置
            </p>
          </div>
        </div>
      </div>

      <!-- 表单容器 -->
      <div class="form-container">
        <el-card class="form-card" shadow="hover">
          <el-form
            ref="formRef"
            :model="formState"
            :rules="formRules"
            label-width="120px"
            size="large"
            class="edit-form"
            @submit.prevent="handleSubmit"
          >
          <!-- 流程名称 -->
          <el-form-item label="流程名称" prop="name">
            <el-input
              v-model="formState.name"
              placeholder="请输入流程名称"
              maxlength="100"
              :disabled="isReadOnly"
              show-word-limit
            />
          </el-form-item>

          <!-- 流程描述 -->
          <el-form-item label="流程描述" prop="description">
            <el-input
              v-model="formState.description"
              type="textarea"
              placeholder="请输入流程描述"
              :rows="4"
              maxlength="500"
              :disabled="isReadOnly"
              show-word-limit
            />
          </el-form-item>

          <!-- 业务领域 -->
          <el-form-item label="业务领域" prop="businessDomain">
            <el-input
              v-model="formState.businessDomain"
              placeholder="请输入业务领域，如：电商、金融、制造等"
              maxlength="50"
              :disabled="isReadOnly"
              show-word-limit
            />
          </el-form-item>

          <!-- 流程状态 -->
          <el-form-item label="流程状态" prop="status">
            <el-select
              v-model="formState.status"
              placeholder="请选择流程状态"
              style="width: 100%"
              :disabled="isReadOnly"
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>

          <!-- 错误信息 -->
          <el-alert
            v-if="processesStore.error"
            :title="processesStore.error"
            type="error"
            show-icon
            closable
            @close="processesStore.clearError"
          />

            <!-- 操作按钮 -->
            <el-form-item>
              <div class="form-actions">
                <el-button
                  size="large"
                  @click="navigateTo(`/processes/${processId}`)"
                >
                  取消
                </el-button>
                <el-button
                  v-if="!isReadOnly"
                  type="primary"
                  :loading="processesStore.isLoading"
                  size="large"
                  @click="handleSubmit"
                >
                  保存修改
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft, WarningFilled } from '@element-plus/icons-vue'
import { ProcessStatus } from '~/types'
import type { FormInstance, FormRules } from 'element-plus'

// 页面配置
definePageMeta({
  layout: 'default'
})

// 获取路由参数
const route = useRoute()
const processId = parseInt(route.params.id as string)

// 设置页面标题
useHead({
  title: '编辑流程 - ProMax'
})

// 使用 stores
const processesStore = useProcessesStore()

// 获取流程详情
const { data: process, pending, error } = await useLazyAsyncData(
 `process-edit-${processId}`,
  () => processesStore.fetchProcess(processId)
)

// 只读判断：completed、archived
const isReadOnly = computed(() => {
  const s = process.value?.status
  return s === 'completed' || s === 'archived'
})

// 表单引用和状态
const formRef = ref<FormInstance>()
const formState = reactive({
  name: '',
  description: '',
  businessDomain: '',
  status: ProcessStatus.DRAFT
})

// 监听流程数据变化，初始化表单
watch(process, (newProcess) => {
  if (newProcess) {
    formState.name = newProcess.name
    formState.description = newProcess.description || ''
    formState.businessDomain = newProcess.businessDomain || ''
    formState.status = newProcess.status
  }
}, { immediate: true })

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入流程名称', trigger: 'blur' },
    { min: 2, max: 100, message: '流程名称长度应在 2-100 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过 500 个字符', trigger: 'blur' }
  ],
  businessDomain: [
    { max: 50, message: '业务领域长度不能超过 50 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择流程状态', trigger: 'change' }
  ]
}

// 状态选项
const statusOptions = [
  { label: '草稿', value: ProcessStatus.DRAFT },
  { label: '活跃', value: ProcessStatus.ACTIVE },
  { label: '已完成', value: ProcessStatus.COMPLETED },
  { label: '已归档', value: ProcessStatus.ARCHIVED }
]

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await processesStore.updateProcess(processId, formState)

        ElMessage.success('流程更新成功')

        // 跳转回流程详情页
        await navigateTo(`/processes/${processId}`)
      } catch (error) {
        // 错误已在store中处理
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @media (max-width: 768px) {
    padding: 1rem 0.5rem;
  }
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #64748b;
  font-size: 1.1rem;
}

.error-icon {
  font-size: 3rem;
  color: #ef4444;
  margin-bottom: 1rem;
}

.error-title {
  color: #1e293b;
  margin-bottom: 0.5rem;

  :global(.dark) & {
    color: #f1f5f9;
  }
}

.error-message {
  color: #64748b;
  margin-bottom: 2rem;
}

// 页面头部
.page-header {
  max-width: 1200px;
  margin: 0 auto 3rem auto;
  animation: fadeInUp 0.6s ease-out;

  .header-content {
    display: flex;
    align-items: flex-start;
    gap: 1rem;

    .back-button {
      color: #6b7280;
      transition: all 0.2s ease;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      flex-shrink: 0;

      &:hover {
        color: #3b82f6;
        background-color: rgba(59, 130, 246, 0.1);
        transform: translateX(-2px);
      }

      :global(.dark) & {
        color: #9ca3af;

        &:hover {
          color: #60a5fa;
          background-color: rgba(96, 165, 250, 0.1);
        }
      }
    }

    .title-section {
      .page-title {
        font-size: 2.25rem;
        font-weight: 800;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0 0 0.5rem 0;
        letter-spacing: -0.025em;

        :global(.dark) & {
          background: linear-gradient(135deg, #60a5fa, #a78bfa);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .subtitle {
        color: #64748b;
        font-size: 1.125rem;
        font-weight: 500;
        margin: 0;
        opacity: 0.8;

        :global(.dark) & {
          color: #94a3b8;
        }
      }
    }
  }
}

// 表单容器
.form-container {
  max-width: 48rem;
  margin: 0 auto;

  :deep(.el-card) {
    border-radius: 16px;
    border: none;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    transition: all 0.3s ease;
    animation: slideInUp 0.5s ease-out 0.2s both;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
    }

    :global(.dark) & {
      background: rgba(30, 41, 59, 0.95);
      border: 1px solid rgba(71, 85, 105, 0.3);
    }

    .el-card__body {
      padding: 2.5rem;

      @media (max-width: 768px) {
        padding: 1.5rem;
      }
    }
  }
}

// 表单样式
.edit-form {
  :deep(.el-form-item) {
    margin-bottom: 2rem;

    .el-form-item__label {
      font-weight: 600;
      color: #374151;
      font-size: 0.95rem;

      :global(.dark) & {
        color: #d1d5db;
      }
    }

    .el-input {
      .el-input__wrapper {
        border-radius: 12px;
        border: 2px solid #e5e7eb;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        &:hover {
          border-color: #3b82f6;
        }

        &.is-focus {
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        :global(.dark) & {
          border-color: #4b5563;
          background-color: #374151;

          &:hover {
            border-color: #60a5fa;
          }

          &.is-focus {
            border-color: #60a5fa;
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
          }
        }
      }
    }

    .el-textarea {
      .el-textarea__inner {
        border-radius: 12px;
        border: 2px solid #e5e7eb;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        resize: vertical;

        &:hover {
          border-color: #3b82f6;
        }

        &:focus {
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        :global(.dark) & {
          border-color: #4b5563;
          background-color: #374151;
          color: #f9fafb;

          &:hover {
            border-color: #60a5fa;
          }

          &:focus {
            border-color: #60a5fa;
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
          }
        }
      }
    }

    .el-select {
      width: 100%;

      .el-select__wrapper {
        border-radius: 12px;
        border: 2px solid #e5e7eb;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        &:hover {
          border-color: #3b82f6;
        }

        &.is-focus {
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        :global(.dark) & {
          border-color: #4b5563;
          background-color: #374151;

          &:hover {
            border-color: #60a5fa;
          }

          &.is-focus {
            border-color: #60a5fa;
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
          }
        }
      }
    }
  }

  :deep(.el-alert) {
    border-radius: 12px;
    border: none;
    margin-bottom: 1.5rem;
  }
}

// 表单操作按钮
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2.5rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;

  :global(.dark) & {
    border-top-color: #4b5563;
  }

  :deep(.el-button) {
    border-radius: 12px;
    font-weight: 600;
    padding: 12px 24px;
    transition: all 0.2s ease;

    &:not(.el-button--primary) {
      border: 2px solid #e5e7eb;
      color: #6b7280;

      &:hover {
        border-color: #3b82f6;
        color: #3b82f6;
        transform: translateY(-1px);
      }

      :global(.dark) & {
        border-color: #4b5563;
        color: #9ca3af;
        background-color: transparent;

        &:hover {
          border-color: #60a5fa;
          color: #60a5fa;
        }
      }
    }

    &.el-button--primary {
      background: linear-gradient(135deg, #3b82f6, #8b5cf6);
      border: none;
      box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
      }

      &:active {
        transform: translateY(0);
      }

      :global(.dark) & {
        background: linear-gradient(135deg, #60a5fa, #a78bfa);
      }
    }
  }

  @media (max-width: 640px) {
    flex-direction: column-reverse;

    :deep(.el-button) {
      width: 100%;
    }
  }
}

// 添加页面进入动画
.page-container {
  animation: fadeInUp 0.6s ease-out;
}

// 动画定义
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 表单项动画
.edit-form :deep(.el-form-item) {
  animation: slideInLeft 0.5s ease-out;
  animation-fill-mode: both;

  @for $i from 1 through 6 {
    &:nth-child(#{$i}) {
      animation-delay: #{$i * 0.1}s;
    }
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
