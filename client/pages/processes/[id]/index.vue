<template>
  <div class="process-detail-page">
    <!-- 加载状态 -->
    <div v-if="pending" class="loading-state">
      <div class="loading-spinner"/>
      <p class="loading-text">加载中...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <el-icon class="error-icon">
        <WarningFilled />
      </el-icon>
      <h3 class="error-title">
        加载失败
      </h3>
      <p class="error-message">
        {{ error.message }}
      </p>
      <el-button plain @click="navigateTo('/processes')">
        返回流程列表
      </el-button>
    </div>

    <!-- 流程详情 -->
    <div v-else-if="process">
      <!-- 页面标题和操作 -->
      <div class="page-header">
        <div class="header-content">
          <el-button
            :icon="ArrowLeft"
            text
            class="back-button"
            @click="navigateTo('/processes')"
          />
          <div class="title-section">
            <h1 class="page-title">
              {{ process.name }}
            </h1>
            <div class="status-section">
              <span
                class="status-indicator"
                :class="`status-indicator--${process.status}`"
              />
              <span class="status-text">
                {{ getStatusText(process.status) }}
              </span>
            </div>
          </div>
        </div>

        <div class="header-actions">
          <el-button
            v-if="process.status === 'draft' || process.status === 'active'"
            :icon="Edit"
            plain
            @click="navigateTo(`/processes/${process.id}/edit`)"
          >
            编辑
          </el-button>
          <el-button
            v-if="isReadOnlyProcess"
            :icon="InfoFilled"
            plain
            @click="navigateTo(`/processes/${process.id}/meta`)"
          >
            流程信息
          </el-button>
          <el-button
            :icon="TrendCharts"
            type="primary"
            @click="navigateTo(`/analysis/${process.id}`)"
          >
            数据分析
          </el-button>
        </div>
      </div>

      <!-- 快速操作区域 -->
      <div class="quick-actions-section">
        <div class="quick-actions-grid">
          <div
            class="action-card"
            :class="{ 'is-disabled': !canAddData }"
            @click="onAddDataClick"
          >
            <div class="action-icon upload-icon">
              <el-icon>
                <Upload />
              </el-icon>
            </div>
            <div class="action-content">
              <h3 class="action-title">添加数据</h3>
              <p class="action-description">上传事件日志文件，开始流程分析</p>
            </div>
          </div>

          <div
            class="action-card"
            @click="navigateTo(`/analysis/${process.id}/discover`)"
          >
            <div class="action-icon discover-icon">
              <el-icon>
                <Search />
              </el-icon>
            </div>
            <div class="action-content">
              <h3 class="action-title">流程发现</h3>
              <p class="action-description">自动发现流程模型和路径</p>
            </div>
          </div>

          <div
            class="action-card"
            :class="{ 'is-disabled': isPerformanceDisabled }"
            @click="onPerformanceClick"
          >
            <div class="action-icon performance-icon">
              <el-icon>
                <TrendCharts />
              </el-icon>
            </div>
            <div class="action-content">
              <h3 class="action-title">性能分析</h3>
              <p class="action-description">分析流程性能和瓶颈</p>
            </div>
          </div>

          <div
            class="action-card"
            @click="navigateTo(`/analysis/${process.id}/conformance`)"
          >
            <div class="action-icon conformance-icon">
              <el-icon>
                <DocumentChecked />
              </el-icon>
            </div>
            <div class="action-content">
              <h3 class="action-title">符合性检查</h3>
              <p class="action-description">检查流程执行的符合性</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 流程信息 -->
      <div class="content-grid">
        <!-- 基本信息 -->
        <div class="main-content">
          <el-card>
            <template #header>
              <div class="card-header">
                <el-icon class="card-icon">
                  <InfoFilled />
                </el-icon>
                <h3 class="card-title">
                  基本信息
                </h3>
              </div>
            </template>

            <div class="info-sections">
              <div class="info-item">
                <div class="info-label-with-icon">
                  <el-icon class="info-icon">
                    <Edit />
                  </el-icon>
                  <h4 class="info-label">
                    描述
                  </h4>
                </div>
                <p class="info-value">
                  {{ process.description || '未填写' }}
                </p>
              </div>

              <div class="info-item">
                <div class="info-label-with-icon">
                  <el-icon class="info-icon">
                    <DataAnalysis />
                  </el-icon>
                  <h4 class="info-label">
                    业务领域
                  </h4>
                </div>
                <p class="info-value">
                  {{ process.businessDomain || '未知' }}
                </p>
              </div>

              <div class="info-grid">
                <div class="info-item">
                  <div class="info-label-with-icon">
                    <el-icon class="info-icon">
                      <Calendar />
                    </el-icon>
                    <h4 class="info-label">
                      创建时间
                    </h4>
                  </div>
                  <p class="info-value">
                    {{ formatDateTime(process.createdAt) }}
                  </p>
                </div>
                <div class="info-item">
                  <div class="info-label-with-icon">
                    <el-icon class="info-icon">
                      <Clock />
                    </el-icon>
                    <h4 class="info-label">
                      最后更新
                    </h4>
                  </div>
                  <p class="info-value">
                    {{ formatDateTime(process.updatedAt) }}
                  </p>
                </div>
              </div>

              <div v-if="process.user" class="info-item">
                <div class="info-label-with-icon">
                  <el-icon class="info-icon">
                    <User />
                  </el-icon>
                  <h4 class="info-label">
                    创建者
                  </h4>
                </div>
                <div class="user-info">
                  <el-avatar
                    :src="avatarLoadError ? '' : process.user.avatar"
                    :alt="process.user.fullName || process.user.username"
                    :size="32"
                    @error="onAvatarError"
                  >
                    {{ getUserInitials(process.user.fullName, process.user.username) }}
                  </el-avatar>
                  <span class="user-name">
                    {{ process.user.fullName || process.user.username }}
                  </span>
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 侧边栏 -->
        <div class="sidebar">
          <!-- 数据统计 -->
          <el-card v-if="dataStats">
            <template #header>
              <div class="card-header">
                <el-icon class="card-icon">
                  <DataAnalysis />
                </el-icon>
                <h3 class="card-title">
                  数据统计
                </h3>
              </div>
            </template>

            <div class="stats-list">
              <div class="stat-item">
                <div class="stat-icon-wrapper events-icon">
                  <el-icon>
                    <TrendCharts />
                  </el-icon>
                </div>
                <div class="stat-content">
                  <span class="stat-label">事件总数</span>
                  <span class="stat-value">
                    {{ dataStats.totalEvents.toLocaleString() }}
                  </span>
                </div>
              </div>
              <div class="stat-item">
                <div class="stat-icon-wrapper cases-icon">
                  <el-icon>
                    <DocumentChecked />
                  </el-icon>
                </div>
                <div class="stat-content">
                  <span class="stat-label">案例数量</span>
                  <span class="stat-value">
                    {{ dataStats.uniqueCases.toLocaleString() }}
                  </span>
                </div>
              </div>
              <div class="stat-item">
                <div class="stat-icon-wrapper activities-icon">
                  <el-icon>
                    <Search />
                  </el-icon>
                </div>
                <div class="stat-content">
                  <span class="stat-label">活动类型</span>
                  <span class="stat-value">
                    {{ dataStats.uniqueActivities }}
                  </span>
                </div>
              </div>
              <div class="stat-item">
                <div class="stat-icon-wrapper resources-icon">
                  <el-icon>
                    <User />
                  </el-icon>
                </div>
                <div class="stat-content">
                  <span class="stat-label">资源数量</span>
                  <span class="stat-value">
                    {{ dataStats.uniqueResources }}
                  </span>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  WarningFilled,
  ArrowLeft,
  Edit,
  TrendCharts,
  Upload,
  Search,
  InfoFilled,
  DocumentChecked,
  DataAnalysis,
  User,
  Calendar,
  Clock
} from '@element-plus/icons-vue'

// 页面配置
definePageMeta({
  layout: 'default'
})

const route = useRoute()
const processId = parseInt(route.params.id as string)

// 获取流程详情
const { data: process, pending, error } = await useLazyAsyncData(
  `process-${processId}`,
  () => useProcessesStore().fetchProcess(processId)
)

// 获取数据统计
const { data: dataStats } = await useLazyAsyncData(
  `process-stats-${processId}`,
  () => useApi().getDataStatistics(processId),
  {
    default: () => null
  }
)

// 设置页面标题
useHead({
  title: computed(() => process.value ? `${process.value.name} - ProMax` : '流程详情 - ProMax')
})

const isReadOnlyProcess = computed(() => {
  const s = process.value?.status
  return s === 'completed' || s === 'archived'
})

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    active: '活跃',
    completed: '已完成',
    archived: '已归档'
  }
  return statusMap[status] || status
}

// 仅草稿/活跃可添加数据
const canAddData = computed(() => {
  const s = process.value?.status
  return s === 'draft' || s === 'active'
})

const onAddDataClick = () => {
  if (!process.value) return
  if (canAddData.value) {
    navigateTo(`/analysis/${process.value.id}/upload`)
  } else {
    ElMessage.error('当前状态不允许添加数据，仅草稿或活跃状态可添加数据')
  }
}

// 性能分析限制：active/completed允许；draft需有数据；archived只读
const hasData = computed(() => (dataStats.value?.totalEvents ?? 0) > 0)
const status = computed(() => process.value?.status)
const isPerformanceDisabled = computed(() => {
  const s = status.value
  if (s === 'draft') return !hasData.value
  if (s === 'active' || s === 'completed') return false
  if (s === 'archived') return false // 允许进入，但页面内应只读
  return true
})

const onPerformanceClick = () => {
  if (!process.value) return
  const s = status.value
  if (s === 'draft' && !hasData.value) {
    return ElMessage.warning('草稿状态需先添加数据后再查看性能分析')
  }
  // 进入页面，是否只读由目标页根据状态自行处理
  navigateTo(`/analysis/${process.value.id}/performance`)
}

// 头像加载失败状态
const avatarLoadError = ref(false)
const onAvatarError = () => { avatarLoadError.value = true }

// 获取用户名首字符
const getUserInitials = (fullName?: string | null, username?: string | null) => {
  const name = (fullName || username || '').trim()
  if (!name) return 'U'
  const words = name.split(/\s+/)
  if (words.length > 1) return words[0].charAt(0).toUpperCase()
  const first = name.charAt(0)
  return first.toUpperCase()
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style lang="scss" scoped>
.process-detail-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @media (max-width: 768px) {
    padding: 1rem 0.5rem;
  }
}

// 加载状态
.loading-state {
  text-align: center;
  padding: 4rem 1rem;
  max-width: 1200px;
  margin: 0 auto;

  .loading-spinner {
    width: 2.5rem;
    height: 2.5rem;
    border: 3px solid rgba(59, 130, 246, 0.2);
    border-top-color: #3b82f6;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    animation: spin 1s linear infinite;

    :global(.dark) & {
      border-color: rgba(96, 165, 250, 0.2);
      border-top-color: #60a5fa;
    }
  }

  .loading-text {
    color: #64748b;
    font-size: 1rem;
    margin: 0;

    :global(.dark) & {
      color: #94a3b8;
    }
  }
}

// 错误状态
.error-state {
  text-align: center;
  padding: 4rem 1rem;
  max-width: 1200px;
  margin: 0 auto;

  .error-icon {
    width: 4rem;
    height: 4rem;
    color: #ef4444;
    margin: 0 auto 1.5rem;
  }

  .error-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    margin: 0 0 0.5rem 0;

    :global(.dark) & {
      color: #ffffff;
    }
  }

  .error-message {
    color: #64748b;
    margin: 0 0 2rem 0;

    :global(.dark) & {
      color: #94a3b8;
    }
  }
}

// 页面头部
.page-header {
  max-width: 1200px;
  margin: 0 auto 2rem auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  animation: fadeInUp 0.6s ease-out;

  @media (min-width: 640px) {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
  }

  .header-content {
    display: flex;
    align-items: center;
    gap: 1rem;

    .back-button {
      color: #6b7280;
      transition: all 0.2s ease;
      border-radius: 50%;
      width: 40px;
      height: 40px;

      &:hover {
        color: #3b82f6;
        background-color: rgba(59, 130, 246, 0.1);
        transform: translateX(-2px);
      }

      :global(.dark) & {
        color: #9ca3af;

        &:hover {
          color: #60a5fa;
          background-color: rgba(96, 165, 250, 0.1);
        }
      }
    }

    .title-section {
      .page-title {
        font-size: 2.25rem;
        font-weight: 800;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0 0 0.5rem 0;
        letter-spacing: -0.025em;

        :global(.dark) & {
          background: linear-gradient(135deg, #60a5fa, #a78bfa);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .status-section {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .status-text {
          font-size: 0.875rem;
          color: #6b7280;
          font-weight: 500;
          text-transform: capitalize;

          :global(.dark) & {
            color: #9ca3af;
          }
        }
      }
    }
  }

  .header-actions {
    display: flex;
    gap: 0.75rem;

    :deep(.el-button) {
      border-radius: 12px;
      font-weight: 600;
      padding: 10px 20px;
      transition: all 0.2s ease;

      &.el-button--primary {
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        border: none;
        box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
        }

        :global(.dark) & {
          background: linear-gradient(135deg, #60a5fa, #a78bfa);
        }
      }

      &:not(.el-button--primary) {
        border: 2px solid #e5e7eb;
        color: #6b7280;

        &:hover {
          border-color: #3b82f6;
          color: #3b82f6;
          transform: translateY(-1px);
        }

        :global(.dark) & {
          border-color: #4b5563;
          color: #9ca3af;

          &:hover {
            border-color: #60a5fa;
            color: #60a5fa;
          }
        }
      }
    }
  }
}

// 快速操作区域
.quick-actions-section {
  max-width: 1200px;
  margin: 0 auto 3rem auto;
  animation: slideInUp 0.5s ease-out 0.1s both;

  .quick-actions-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;

    @media (min-width: 640px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 1024px) {
      grid-template-columns: repeat(4, 1fr);
    }

    .action-card {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 16px;
      padding: 1.5rem;
      border: 2px solid transparent;
      cursor: pointer;
      transition: all 0.3s ease;

        &.is-disabled {
          cursor: not-allowed;
          opacity: 0.6;

          &:hover {
            transform: none;
            border-color: transparent;
            box-shadow: none;
          }
        }

      backdrop-filter: blur(10px);
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

      &:hover {
        transform: translateY(-4px);
        border-color: #3b82f6;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      }

      :global(.dark) & {
        background: rgba(30, 41, 59, 0.95);
        border: 2px solid rgba(71, 85, 105, 0.3);

        &:hover {
          border-color: #60a5fa;
        }
      }

      .action-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        font-size: 1.5rem;
        color: white;

        &.upload-icon {
          background: linear-gradient(135deg, #10b981, #34d399);
        }

        &.discover-icon {
          background: linear-gradient(135deg, #3b82f6, #60a5fa);
        }

        &.performance-icon {
          background: linear-gradient(135deg, #f59e0b, #fbbf24);
        }

        &.conformance-icon {
          background: linear-gradient(135deg, #8b5cf6, #a78bfa);
        }
      }

      .action-content {
        .action-title {
          font-size: 1.125rem;
          font-weight: 700;
          color: #111827;
          margin: 0 0 0.5rem 0;

          :global(.dark) & {
            color: #ffffff;
          }
        }

        .action-description {
          font-size: 0.875rem;
          color: #6b7280;
          margin: 0;
          line-height: 1.4;

          :global(.dark) & {
            color: #9ca3af;
          }
        }
      }
    }
  }
}

// 内容网格
.content-grid {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  animation: slideInUp 0.5s ease-out 0.2s both;

  @media (min-width: 1024px) {
    grid-template-columns: 2fr 1fr;
  }

  :deep(.el-card) {
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    :global(.dark) & {
      background: rgba(30, 41, 59, 0.95);
      border: 1px solid rgba(71, 85, 105, 0.3);
    }

    .el-card__header {
      padding: 1.5rem 1.5rem  0.5rem 1.5rem;
      border-bottom: none;
    }

    .el-card__body {
      padding: 1rem 1.5rem 1.5rem 1.5rem;
    }
  }

  .main-content {
    .card-header {
      display: flex;
      align-items: center;
      gap: 0.75rem;

      .card-icon {
        color: #3b82f6;
        font-size: 1.25rem;

        :global(.dark) & {
          color: #60a5fa;
        }
      }

      .card-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #111827;
        margin: 0;

        :global(.dark) & {
          color: #ffffff;
        }
      }
    }

    .info-sections {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;

      .info-item {
        .info-label-with-icon {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          margin-bottom: 0.5rem;

          .info-icon {
            color: #6b7280;
            font-size: 1rem;

            :global(.dark) & {
              color: #9ca3af;
            }
          }

          .info-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #6b7280;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.05em;

            :global(.dark) & {
              color: #9ca3af;
            }
          }
        }

        .info-value {
          color: #111827;
          font-size: 1rem;
          line-height: 1.5;

          :global(.dark) & {
            color: #ffffff;
          }
        }
      }

      .info-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1.5rem;

        @media (min-width: 640px) {
          grid-template-columns: 1fr 1fr;
        }
      }

      .user-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;

        .user-name {
          color: #111827;
          font-weight: 600;

          :global(.dark) & {
            color: #ffffff;
          }
        }
      }
    }
  }

  // 侧边栏
  .sidebar {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;

    .card-header {
      display: flex;
      align-items: center;
      gap: 0.75rem;

      .card-icon {
        color: #3b82f6;
        font-size: 1.25rem;

        :global(.dark) & {
          color: #60a5fa;
        }
      }

      .card-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #111827;
        margin: 0;

        :global(.dark) & {
          color: #ffffff;
        }
      }
    }

    .stats-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        border-radius: 12px;
        background: rgba(248, 250, 252, 0.8);
        border: 1px solid rgba(226, 232, 240, 0.5);
        transition: all 0.2s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        :global(.dark) & {
          background: rgba(55, 65, 81, 0.8);
          border: 1px solid rgba(75, 85, 99, 0.5);
        }

        .stat-icon-wrapper {
          width: 40px;
          height: 40px;
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.25rem;
          color: white;
          flex-shrink: 0;

          &.events-icon {
            background: linear-gradient(135deg, #3b82f6, #60a5fa);
          }

          &.cases-icon {
            background: linear-gradient(135deg, #10b981, #34d399);
          }

          &.activities-icon {
            background: linear-gradient(135deg, #f59e0b, #fbbf24);
          }

          &.resources-icon {
            background: linear-gradient(135deg, #8b5cf6, #a78bfa);
          }
        }

        .stat-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 0.25rem;

          .stat-label {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;

            :global(.dark) & {
              color: #9ca3af;
            }
          }

          .stat-value {
            font-size: 1.125rem;
            font-weight: 700;
            color: #111827;

            :global(.dark) & {
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}

// 状态指示器样式
.status-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 0.5rem;
  flex-shrink: 0;

  &--draft {
    background: linear-gradient(135deg, #6b7280, #9ca3af);
  }

  &--active {
    background: linear-gradient(135deg, #10b981, #34d399);
  }

  &--completed {
    background: linear-gradient(135deg, #06b6d4, #22d3ee);
  }

  &--archived {
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
  }

  &--error {
    background: linear-gradient(135deg, #ef4444, #f87171);
  }
}

// 动画定义
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
