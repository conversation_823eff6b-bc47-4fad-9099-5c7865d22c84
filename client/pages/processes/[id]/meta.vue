<template>
  <div class="meta-page">
    <div v-if="pending" class="loading-state">
      <div class="loading-spinner" />
      <p class="loading-text">加载中...</p>
    </div>

    <div v-else-if="error" class="error-state">
      <el-icon class="error-icon"><WarningFilled /></el-icon>
      <h3 class="error-title">加载失败</h3>
      <p class="error-message">{{ error.message }}</p>
      <el-button plain @click="navigateTo(`/processes/${processId}`)">
        返回流程详情
      </el-button>
    </div>

    <div v-else-if="process" class="content">
      <div class="page-header">
        <div class="header-content">
          <el-button
            :icon="ArrowLeft"
            text
            class="back-button"
            @click="navigateTo(`/processes/${processId}`)"
          />
          <div class="title-section">
            <h1 class="page-title">流程信息</h1>
            <p class="subtitle">查看名称、描述、业务领域和状态</p>
          </div>
        </div>
      </div>

      <el-card class="meta-card">
        <div class="meta-grid">
          <div class="meta-item">
            <label>名称</label>
            <div class="value">{{ process.name }}</div>
          </div>
          <div class="meta-item">
            <label>描述</label>
            <div class="value">{{ process.description || "-" }}</div>
          </div>
          <div class="meta-item">
            <label>业务领域</label>
            <div class="value">{{ process.businessDomain || "-" }}</div>
          </div>
          <div class="meta-item">
            <label>状态</label>
            <div class="value">{{ getStatusText(process.status) }}</div>
          </div>
          <div class="meta-item">
            <label>创建时间</label>
            <div class="value">{{ formatDateTime(process.createdAt) }}</div>
          </div>
          <div class="meta-item">
            <label>更新时间</label>
            <div class="value">{{ formatDateTime(process.updatedAt) }}</div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft, WarningFilled } from "@element-plus/icons-vue";

definePageMeta({ layout: "default" });

const route = useRoute();
const processId = parseInt(route.params.id as string);

const processesStore = useProcessesStore();
const {
  data: process,
  pending,
  error,
} = await useLazyAsyncData(`process-meta-${processId}`, () =>
  processesStore.fetchProcess(processId)
);

const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    draft: "草稿",
    active: "活跃",
    completed: "已完成",
    archived: "已归档",
  };
  return map[status] || status;
};

const formatDateTime = (date?: string) => {
  if (!date) return "-";
  const d = new Date(date);
  return d.toLocaleString("zh-CN", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};
</script>

<style scoped lang="scss">
.meta-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;
  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }
}
.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
}
.loading-spinner {
  width: 2.5rem;
  height: 2.5rem;
  border: 3px solid rgba(59, 130, 246, 0.2);
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}
.back-button {
  margin-right: 0.25rem;
}
.title-section {
  display: flex;
  flex-direction: column;
}
.page-title {
  margin: 0;
  font-size: 1.25rem;
  line-height: 1.2;
}
.subtitle {
  margin: 0.25rem 0 0;
  color: #6b7280;
}
:global(.dark) .subtitle {
  color: #9ca3af;
}

.page-header {
  max-width: 1200px;
  margin: 0 auto 1.5rem;
}
.meta-card {
  max-width: 1200px;
  margin: 0 auto 1rem;
  border-radius: 16px;
  border: none;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  :global(.dark) & {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(71, 85, 105, 0.3);
  }
  :deep(.el-card__header) {
    padding: 1.25rem 1.5rem 0.5rem;
    border-bottom: none;
  }
  :deep(.el-card__body) {
    padding: 1rem 1.5rem 1.5rem;
  }
}
.meta-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}
@media (min-width: 768px) {
  .meta-grid {
    grid-template-columns: 1fr 1fr;
  }
}
.meta-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 1rem 1.25rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
  :global(.dark) & {
    background: rgba(30, 41, 59, 0.95);
    border-color: rgba(71, 85, 105, 0.3);
  }
}
.meta-item label {
  display: block;
  font-size: 0.85rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
  :global(.dark) & {
    color: #9ca3af;
  }
}
.meta-item .value {
  color: #111827;
  :global(.dark) & {
    color: #e5e7eb;
  }
}
.kv-item {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: 0.5rem;
  padding: 0.5rem 0;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.06);
}
.kv-item:last-child {
  border-bottom: none;
}
.empty-meta {
  color: #6b7280;
}
</style>
