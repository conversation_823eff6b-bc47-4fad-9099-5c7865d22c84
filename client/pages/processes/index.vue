<template>
  <div class="processes-container">
    <!-- 页面标题和操作 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          流程管理
        </h1>
        <p class="page-subtitle">
          管理您的业务流程项目
        </p>
      </div>
      <div class="header-actions">
        <el-button
          type="primary"
          :icon="Plus"
          size="large"
          class="create-button"
          @click="navigateTo('/processes/create')"
        >
          创建流程
        </el-button>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filters-section">
      <div class="search-input">
        <el-input
          v-model="searchQuery"
          placeholder="搜索流程名称..."
          :prefix-icon="Search"
          size="large"
        />
      </div>
      <el-select
        v-model="statusFilter"
        placeholder="筛选状态"
        size="large"
        class="status-filter"
        clearable
      >
        <el-option
          v-for="option in statusOptions"
          :key="option.value"
          :label="option.label"
          :value="option.value"
        />
      </el-select>
    </div>

    <!-- 流程列表 -->
    <div v-if="isLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <p class="loading-text">加载中...</p>
    </div>

    <div v-else-if="filteredProcesses.length === 0" class="empty-state">
      <el-icon class="empty-icon">
        <FolderOpened />
      </el-icon>
      <h3 class="empty-title">
        {{ searchQuery || statusFilter ? '未找到匹配的流程' : '暂无流程' }}
      </h3>
      <p class="empty-description">
        {{ searchQuery || statusFilter ? '请尝试调整搜索条件' : '创建您的第一个流程项目' }}
      </p>
      <el-button
        v-if="!searchQuery && !statusFilter"
        type="primary"
        :icon="Plus"
        class="empty-action"
        @click="navigateTo('/processes/create')"
      >
        创建流程
      </el-button>
    </div>

    <div v-else class="processes-grid">
      <el-card
        v-for="process in filteredProcesses"
        :key="process.id"
        class="process-card"
        @click="navigateTo(`/processes/${process.id}`)"
      >
        <template #header>
          <div class="card-header">
            <div class="card-header-content">
              <h3 class="process-name">
                {{ process.name }}
              </h3>
              <div class="process-status">
                <span
                  class="status-indicator"
                  :class="`status-${process.status}`"
                ></span>
                <span class="status-text">
                  {{ getStatusText(process.status) }}
                </span>
              </div>
            </div>
            <el-dropdown @command="(command) => handleProcessAction(command, process)">
              <el-button text :icon="MoreFilled" class="action-button" @click.stop />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="view">
                    <el-icon><View /></el-icon>
                    查看详情
                  </el-dropdown-item>
                  <el-dropdown-item command="edit" divided>
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item command="analysis">
                    <el-icon><TrendCharts /></el-icon>
                    数据分析
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>

        <div class="card-content">
          <p class="process-description">
            {{ process.description || '-' }}
          </p>

          <div class="business-domain">
            <el-icon class="domain-icon">
              <OfficeBuilding />
            </el-icon>
            {{ process.businessDomain || '未知' }}
          </div>

          <div class="card-footer">
            <span class="update-time">{{ formatDate(process.updatedAt) }}</span>
            <span v-if="process.user" class="process-owner">{{ process.user.username }}</span>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Plus,
  Search,
  FolderOpened,
  MoreFilled,
  View,
  Edit,
  TrendCharts,
  Delete,
  OfficeBuilding
} from '@element-plus/icons-vue'
import type { Process } from '~/types'

// 页面配置
definePageMeta({
  layout: 'default'
})

// 设置页面标题
useHead({
  title: '流程管理 - ProMax'
})

const processesStore = useProcessesStore()

// 响应式状态
const searchQuery = ref('')
const statusFilter = ref('')
const isLoading = ref(false)

// 状态选项
const statusOptions = [
  { label: '全部状态', value: '' },
  { label: '草稿', value: 'draft' },
  { label: '活跃', value: 'active' },
  { label: '已完成', value: 'completed' },
  { label: '已归档', value: 'archived' }
]

// 获取流程列表
const { data: processes } = await useLazyAsyncData('processes', () => 
  processesStore.fetchProcesses()
)

// 过滤后的流程列表
const filteredProcesses = computed(() => {
  let filtered = processesStore.processes

  // 按搜索关键词过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(process => 
      process.name.toLowerCase().includes(query) ||
      process.description?.toLowerCase().includes(query) ||
      process.businessDomain?.toLowerCase().includes(query)
    )
  }

  // 按状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(process => process.status === statusFilter.value)
  }

  return filtered
})

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    active: '活跃',
    completed: '已完成',
    archived: '已归档'
  }
  return statusMap[status] || status
}

// 处理流程操作
const handleProcessAction = async (command: string, process: Process) => {
  switch (command) {
    case 'view':
      navigateTo(`/processes/${process.id}`)
      break
    case 'edit':
      navigateTo(`/processes/${process.id}/edit`)
      break
    case 'analysis':
      navigateTo(`/analysis/${process.id}`)
      break
    case 'delete':
      await handleDeleteProcess(process)
      break
  }
}

// 处理删除流程
const handleDeleteProcess = async (process: Process) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除流程"${process.name}"吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await processesStore.deleteProcess(process.id)
    ElMessage.success('流程删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败，请重试')
    }
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>

<style lang="scss" scoped>
.processes-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 1rem;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  @media (max-width: 768px) {
    padding: 1rem 0.5rem;
  }
}

.page-header {
  max-width: 1200px;
  margin: 0 auto 3rem auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  animation: fadeInUp 0.6s ease-out;

  @media (min-width: 640px) {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
  }

  .header-content {
    flex: 1;

    .page-title {
      font-size: 2.5rem;
      font-weight: 800;
      background: linear-gradient(135deg, #3b82f6, #8b5cf6);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin: 0 0 0.5rem 0;
      letter-spacing: -0.025em;

      :global(.dark) & {
        background: linear-gradient(135deg, #60a5fa, #a78bfa);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    .page-subtitle {
      color: #64748b;
      font-size: 1.125rem;
      font-weight: 500;
      margin: 0;
      opacity: 0.8;

      :global(.dark) & {
        color: #94a3b8;
      }
    }
  }

  .header-actions {
    .create-button {
      border-radius: 12px;
      font-weight: 600;
      padding: 12px 24px;
      background: linear-gradient(135deg, #3b82f6, #8b5cf6);
      border: none;
      box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
      }

      :global(.dark) & {
        background: linear-gradient(135deg, #60a5fa, #a78bfa);
      }
    }
  }
}

.filters-section {
  max-width: 1200px;
  margin: 0 auto 2rem auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  animation: slideInLeft 0.5s ease-out 0.2s both;

  @media (min-width: 640px) {
    flex-direction: row;
  }

  .search-input {
    flex: 1;

    :deep(.el-input) {
      .el-input__wrapper {
        border-radius: 12px;
        border: 2px solid #e5e7eb;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        &:hover {
          border-color: #3b82f6;
        }

        &.is-focus {
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        :global(.dark) & {
          border-color: #4b5563;
          background-color: #374151;

          &:hover {
            border-color: #60a5fa;
          }

          &.is-focus {
            border-color: #60a5fa;
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
          }
        }
      }
    }
  }

  .status-filter {
    width: 100%;

    @media (min-width: 640px) {
      width: 12rem;
    }

    :deep(.el-select) {
      .el-select__wrapper {
        border-radius: 12px;
        border: 2px solid #e5e7eb;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        &:hover {
          border-color: #3b82f6;
        }

        &.is-focus {
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        :global(.dark) & {
          border-color: #4b5563;
          background-color: #374151;

          &:hover {
            border-color: #60a5fa;
          }

          &.is-focus {
            border-color: #60a5fa;
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
          }
        }
      }
    }
  }
}

.loading-state {
  text-align: center;
  padding: 4rem 1rem;
  max-width: 1200px;
  margin: 0 auto;

  .loading-spinner {
    width: 2.5rem;
    height: 2.5rem;
    border: 3px solid rgba(59, 130, 246, 0.2);
    border-top-color: #3b82f6;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    animation: spin 1s linear infinite;

    :global(.dark) & {
      border-color: rgba(96, 165, 250, 0.2);
      border-top-color: #60a5fa;
    }
  }

  .loading-text {
    color: #64748b;
    font-size: 1rem;
    margin: 0;

    :global(.dark) & {
      color: #94a3b8;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 4rem 1rem;
  max-width: 1200px;
  margin: 0 auto;

  .empty-icon {
    width: 4rem;
    height: 4rem;
    color: #cbd5e1;
    margin: 0 auto 1.5rem;

    :global(.dark) & {
      color: #64748b;
    }
  }

  .empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    margin: 0 0 0.5rem 0;

    :global(.dark) & {
      color: #ffffff;
    }
  }

  .empty-description {
    color: #64748b;
    font-size: 1rem;
    margin: 0 0 2rem 0;

    :global(.dark) & {
      color: #94a3b8;
    }
  }

  .empty-action {
    border-radius: 12px;
    font-weight: 600;
    padding: 12px 24px;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border: none;
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
    }

    :global(.dark) & {
      background: linear-gradient(135deg, #60a5fa, #a78bfa);
    }
  }
}

.processes-grid {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  animation: slideInUp 0.5s ease-out 0.4s both;

  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }
}

.process-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  :global(.dark) & {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(71, 85, 105, 0.3);
  }

  :deep(.el-card__header) {
    padding: 1.5rem 1.5rem 0.5rem 1.5rem;
    border-bottom: none;
  }

  :deep(.el-card__body) {
    padding: 0 1.5rem 1.5rem 1.5rem;
  }

  .card-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 1rem;

    .card-header-content {
      flex: 1;
      min-width: 0;

      .process-name {
        font-size: 1.125rem;
        font-weight: 700;
        color: #111827;
        margin: 0 0 0.5rem 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        :global(.dark) & {
          color: #ffffff;
        }
      }

      .process-status {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .status-text {
          font-size: 0.875rem;
          color: #6b7280;
          font-weight: 500;
          text-transform: capitalize;

          :global(.dark) & {
            color: #9ca3af;
          }
        }
      }
    }

    .action-button {
      color: #6b7280;
      border-radius: 8px;
      transition: all 0.2s ease;

      &:hover {
        color: #3b82f6;
        background-color: rgba(59, 130, 246, 0.1);
      }

      :global(.dark) & {
        color: #9ca3af;

        &:hover {
          color: #60a5fa;
          background-color: rgba(96, 165, 250, 0.1);
        }
      }
    }
  }

  .card-content {
    .process-description {
      font-size: 0.875rem;
      color: #6b7280;
      margin: 0 0 1rem 0;
      line-height: 1.5;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;

      :global(.dark) & {
        color: #9ca3af;
      }
    }

    .business-domain {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.875rem;
      color: #6b7280;
      margin-bottom: 1rem;

      :global(.dark) & {
        color: #9ca3af;
      }

      .domain-icon {
        width: 1rem;
        height: 1rem;
        flex-shrink: 0;
      }
    }

    .card-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 0.75rem;
      color: #6b7280;

      :global(.dark) & {
        color: #9ca3af;
      }

      .update-time {
        font-weight: 500;
      }

      .process-owner {
        font-weight: 600;
        color: #3b82f6;

        :global(.dark) & {
          color: #60a5fa;
        }
      }
    }
  }
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  flex-shrink: 0;

  &.status-draft {
    background: linear-gradient(135deg, #6b7280, #9ca3af);
  }

  &.status-active {
    background: linear-gradient(135deg, #10b981, #34d399);
  }

  &.status-completed {
    background: linear-gradient(135deg, #06b6d4, #22d3ee);
  }

  &.status-archived {
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
  }
}

// 动画定义
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 流程卡片动画
.processes-grid .process-card {
  animation: slideInUp 0.5s ease-out;
  animation-fill-mode: both;

  @for $i from 1 through 100 {
    &:nth-child(#{$i}) {
      animation-delay: #{0.4 + $i * 0.05}s;
    }
  }
}
</style>
