<template>
  <div class="discovery-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="title">流程发现</h1>
        <p class="description">
          从事件日志中自动发现和分析业务流程模型
        </p>
      </div>
    </div>

    <!-- 功能模块 -->
    <div class="modules-container">
      <div class="modules-grid">
        <!-- 基础流程发现 -->
        <el-card class="module-card basic-discovery">
          <template #header>
            <div class="module-header">
              <el-icon class="module-icon basic-icon">
                <Search />
              </el-icon>
              <div class="module-info">
                <h3 class="module-title">基础流程发现</h3>
                <p class="module-subtitle">传统流程挖掘分析</p>
              </div>
            </div>
          </template>

          <div class="module-content">
            <p class="module-description">
              使用经典的流程挖掘算法，从事件日志中发现直接跟随图（DFG）和流程模型，
              适合中小规模的流程分析。
            </p>
            
            <div class="module-features">
              <div class="feature-item">
                <el-icon><CircleCheck /></el-icon>
                <span>DFG可视化</span>
              </div>
              <div class="feature-item">
                <el-icon><TrendCharts /></el-icon>
                <span>频率分析</span>
              </div>
              <div class="feature-item">
                <el-icon><Timer /></el-icon>
                <span>时间分析</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 子流程发现 -->
        <el-card class="module-card subprocess-discovery">
          <template #header>
            <div class="module-header">
              <el-icon class="module-icon subprocess-icon">
                <Share />
              </el-icon>
              <div class="module-info">
                <h3 class="module-title">子流程发现</h3>
                <p class="module-subtitle">智能模式识别</p>
              </div>
            </div>
          </template>

          <div class="module-content">
            <p class="module-description">
              使用先进的模式识别算法，自动发现复杂流程中的子流程模式，
              将大型流程简化为层次化结构。
            </p>
            
            <div class="module-features">
              <div class="feature-item">
                <el-icon><Operation /></el-icon>
                <span>模式识别</span>
              </div>
              <div class="feature-item">
                <el-icon><Rank /></el-icon>
                <span>层次化视图</span>
              </div>
              <div class="feature-item">
                <el-icon><DataAnalysis /></el-icon>
                <span>压缩分析</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 流程对比 -->
        <el-card class="module-card process-comparison">
          <template #header>
            <div class="module-header">
              <el-icon class="module-icon comparison-icon">
                <Switch />
              </el-icon>
              <div class="module-info">
                <h3 class="module-title">流程对比</h3>
                <p class="module-subtitle">变体分析</p>
              </div>
            </div>
          </template>

          <div class="module-content">
            <p class="module-description">
              比较不同时间段、部门或业务场景下的流程执行路径，
              精准识别流程偏差、变化趋势和潜在的优化机会。
            </p>
            
            <div class="module-features">
              <div class="feature-item">
                <el-icon><Histogram /></el-icon>
                <span>变体分析</span>
              </div>
              <div class="feature-item">
                <el-icon><TrendCharts /></el-icon>
                <span>趋势对比</span>
              </div>
              <div class="feature-item">
                <el-icon><Warning /></el-icon>
                <span>异常检测</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 最近的流程 -->
    <div v-if="recentProcesses.length > 0" class="recent-processes">
      <el-card>
        <template #header>
          <h3 class="recent-title">最近分析的流程</h3>
        </template>
        
        <div class="processes-grid">
          <div
            v-for="process in recentProcesses"
            :key="process.id"
            class="process-item"
            @click="navigateToProcess(process.id)"
          >
            <div class="process-info">
              <h4 class="process-name">{{ process.name }}</h4>
              <p class="process-description">{{ process.description || '暂无描述' }}</p>
              <div class="process-meta">
                <span class="process-date">{{ formatDate(process.updatedAt) }}</span>
                <el-tag size="small" type="info">{{ process.eventLogCount ?? 0 }} 条事件记录</el-tag>
              </div>
            </div>
            <div class="process-actions">
              <el-button
                type="primary"
                :icon="Search"
                size="small"
                @click.stop="navigateToBasicDiscovery(process.id)"
              >
                基础发现
              </el-button>
              <el-button
                type="warning"
                :icon="Share"
                size="small"
                @click.stop="navigateToSubprocessDiscovery(process.id)"
              >
                子流程发现
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-icon class="empty-icon">
        <FolderOpened />
      </el-icon>
      <h3 class="empty-title">还没有流程数据</h3>
      <p class="empty-description">
        请先上传事件日志数据，然后开始流程发现分析
      </p>
      <el-button
        type="primary"
        :icon="Upload"
        size="large"
        @click="navigateTo('/processes')"
      >
        上传数据
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Search, Share, Switch, Upload, CircleCheck, TrendCharts, Timer,
  Operation, Rank, DataAnalysis, Histogram, Warning, FolderOpened
} from '@element-plus/icons-vue'
import { useApi } from '~/utils/api'
import type { Process } from '~/types'

// 页面配置
definePageMeta({
  layout: 'default'
})

// 设置页面标题
useHead({
  title: '流程发现 - ProMax'
})

// 状态
const recentProcesses = ref<Process[]>([])
const isLoading = ref(true)

// 方法
const navigateToBasicDiscovery = (processId?: number) => {
  if (processId) {
    navigateTo(`/analysis/${processId}/discover`)
  } else {
    navigateTo('/processes')
  }
}

const navigateToSubprocessDiscovery = (processId?: number) => {
  if (processId) {
    navigateTo(`/analysis/${processId}/subprocess`)
  } else {
    navigateTo('/processes')
  }
}

const navigateToProcess = (processId: number) => {
  navigateTo(`/processes/${processId}`)
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 加载最近的流程
const loadRecentProcesses = async () => {
  try {
    const api = useApi()
    const response = await api.getProcesses()
    // 限制显示最近的6个流程，按更新时间排序
    recentProcesses.value = (response || [])
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
      .slice(0, 6)
  } catch (error) {
    console.error('加载最近流程失败:', error)
    recentProcesses.value = []
  } finally {
    isLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadRecentProcesses()
})
</script>

<style scoped lang="scss">
.discovery-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
  animation: fadeInDown 0.6s ease-out;

  .header-content {
    max-width: 600px;
    margin: 0 auto;

    .title {
      color: white;
      font-size: 3rem;
      font-weight: 700;
      margin: 0 0 1rem 0;
      background: linear-gradient(135deg, #ffffff, #e2e8f0);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .description {
      color: rgba(255, 255, 255, 0.9);
      font-size: 1.2rem;
      margin: 0;
      line-height: 1.6;
    }
  }
}

.modules-container {
  max-width: 1200px;
  margin: 0 auto 3rem auto;
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.module-card {
  border-radius: 20px;
  border: none;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  &.basic-discovery {
    border-top: 4px solid #3b82f6;
  }

  &.subprocess-discovery {
    border-top: 4px solid #ec4899;
  }

  &.process-comparison {
    border-top: 4px solid #f59e0b;
  }

  .module-header {
    display: flex;
    align-items: center;
    gap: 1rem;

    .module-icon {
      width: 50px;
      height: 50px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;

      &.basic-icon {
        background: rgba(59, 130, 246, 0.1);
        color: #3b82f6;
      }

      &.subprocess-icon {
        background: rgba(236, 72, 153, 0.1);
        color: #ec4899;
      }

      &.comparison-icon {
        background: rgba(245, 158, 11, 0.1);
        color: #f59e0b;
      }
    }

    .module-info {
      flex: 1;

      .module-title {
        margin: 0 0 0.25rem 0;
        color: #1f2937;
        font-size: 1.25rem;
        font-weight: 600;
      }

      .module-subtitle {
        margin: 0;
        color: #6b7280;
        font-size: 0.9rem;
      }
    }
  }

  .module-content {
    padding: 1.5rem 0 0 0;

    .module-description {
      color: #4b5563;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .module-features {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 2rem;

      .feature-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #6b7280;
        font-size: 0.9rem;

        .el-icon {
          color: #3b82f6;
        }
      }
    }

    .module-actions {
      text-align: center;

      .el-button {
        border-radius: 12px;
        font-weight: 600;
        padding: 12px 24px;
      }
    }
  }
}

.recent-processes {
  max-width: 1200px;
  margin: 0 auto;
  animation: fadeInUp 0.6s ease-out 0.4s both;

  .recent-title {
    margin: 0;
    color: #1f2937;
    font-size: 1.25rem;
    font-weight: 600;
  }

  .processes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .process-item {
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    background: #f9fafb;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #f3f4f6;
      border-color: #3b82f6;
      transform: translateY(-2px);
    }

    .process-info {
      margin-bottom: 1rem;

      .process-name {
        margin: 0 0 0.5rem 0;
        color: #1f2937;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .process-description {
        margin: 0 0 0.75rem 0;
        color: #6b7280;
        font-size: 0.9rem;
        line-height: 1.4;
      }

      .process-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .process-date {
          color: #9ca3af;
          font-size: 0.8rem;
        }
      }
    }

    .process-actions {
      display: flex;
      gap: 0.5rem;

      .el-button {
        flex: 1;
        border-radius: 8px;
        font-size: 0.8rem;
      }
    }
  }
}

.empty-state {
  text-align: center;
  max-width: 500px;
  margin: 0 auto;
  padding: 3rem 1rem;
  animation: fadeInUp 0.6s ease-out 0.4s both;

  .empty-icon {
    font-size: 4rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1.5rem;
  }

  .empty-title {
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
  }

  .empty-description {
    color: rgba(255, 255, 255, 0.8);
    margin: 0 0 2rem 0;
    line-height: 1.6;
  }

  .el-button {
    border-radius: 12px;
    font-weight: 600;
    padding: 12px 24px;
  }
}

// 动画
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
