<template>
  <div class="layout">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="container">
        <div class="navbar__content">
          <!-- Logo -->
          <div class="navbar__logo">
            <NuxtLink to="/dashboard" class="logo-link">
              <img src="~/assets/promax.svg" alt="ProMax" class="logo-icon">
            </NuxtLink>
          </div>

          <!-- 导航菜单 -->
          <div class="navbar__menu">
            <NuxtLink
              to="/dashboard"
              class="nav-link"
              active-class="nav-link--active"
            >
              仪表板
            </NuxtLink>
            <NuxtLink
              to="/processes"
              class="nav-link"
              active-class="nav-link--active"
            >
              流程管理
            </NuxtLink>
            <NuxtLink
              to="/analysis"
              class="nav-link"
              active-class="nav-link--active"
            >
              数据分析
            </NuxtLink>
            <NuxtLink
              to="/discovery"
              class="nav-link"
              active-class="nav-link--active"
            >
              流程发现
            </NuxtLink>
            <NuxtLink
              to="/conformance"
              class="nav-link"
              active-class="nav-link--active"
            >
              符合性检查
            </NuxtLink>
          </div>

          <!-- 右侧菜单 -->
          <div class="navbar__actions">
            <!-- 颜色模式切换 -->
            <!-- <el-button
              :icon="isDark ? Sunny : Moon"
              text
              @click="toggleColorMode"
            /> -->

            <!-- 用户菜单 -->
            <el-dropdown @command="handleUserMenuCommand">
              <el-avatar
                :src="authStore.user?.avatar"
                class="user-avatar"
                size="small"
              >
                {{ authStore.user?.fullName?.[0] || authStore.user?.username?.[0] || 'U' }}
              </el-avatar>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item disabled>
                    {{ authStore.user?.fullName || authStore.user?.username || '用户' }}
                  </el-dropdown-item>
                  <el-dropdown-item divided command="database-connections">
                    <el-icon><Connection /></el-icon>
                    数据库连接
                  </el-dropdown-item>
                  <el-dropdown-item command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <!-- 移动端菜单按钮 -->
            <el-button
              :icon="Menu"
              text
              class="mobile-menu-btn"
              @click="isMobileMenuOpen = !isMobileMenuOpen"
            />
          </div>
        </div>

        <!-- 移动端菜单 -->
        <div v-show="isMobileMenuOpen" class="mobile-menu">
          <div class="mobile-menu__content">
            <NuxtLink
              to="/dashboard"
              class="mobile-nav-link"
              @click="isMobileMenuOpen = false"
            >
              仪表板
            </NuxtLink>
            <NuxtLink
              to="/processes"
              class="mobile-nav-link"
              @click="isMobileMenuOpen = false"
            >
              流程管理
            </NuxtLink>
            <NuxtLink
              to="/analysis"
              class="mobile-nav-link"
              @click="isMobileMenuOpen = false"
            >
              数据分析
            </NuxtLink>
            <NuxtLink
              to="/discovery"
              class="mobile-nav-link"
              @click="isMobileMenuOpen = false"
            >
              流程发现
            </NuxtLink>
            <NuxtLink
              to="/conformance"
              class="mobile-nav-link"
              @click="isMobileMenuOpen = false"
            >
              符合性检查
            </NuxtLink>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
      <slot />
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer__content">
          <div class="footer__logo">
            <img src="~/assets/promax.svg" alt="ProMax" class="footer-icon">
          </div>
          <div class="footer__copyright">
            © 2024 ProMax. 专业流程挖掘平台.
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import {
  SwitchButton,
  Menu,
  Connection
} from '@element-plus/icons-vue'

const authStore = useAuthStore()

// 响应式状态
const isMobileMenuOpen = ref(false)

// 处理用户菜单命令
const handleUserMenuCommand = (command: string) => {
  switch (command) {
    case 'database-connections':
      navigateTo('/database-connections')
      break
    case 'logout':
      authStore.logout()
      break
  }
}

// 监听路由变化，关闭移动端菜单
watch(() => useRoute().path, () => {
  isMobileMenuOpen.value = false
})
</script>

<style lang="scss" scoped>
.layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  animation: fadeIn 0.6s ease-out;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }
}

.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  position: sticky;
  top: 0;
  z-index: 50;
  transition: all 0.3s ease;

  :global(.dark) & {
    background: rgba(30, 41, 59, 0.95);
    border-bottom: 1px solid rgba(71, 85, 105, 0.5);
  }

  .container {
    max-width: 95vw;
    margin: 0 auto;
    padding: 0 1rem;

    @media (max-width: 768px) {
      padding: 0 0.5rem;
    }
  }

  &__content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
  }

  &__logo {
    display: flex;
    align-items: center;
  }

  &__menu {
    display: none;
    align-items: center;
    gap: 2rem;

    @media (min-width: 768px) {
      display: flex;
    }
  }

  &__actions {
    display: flex;
    align-items: center;
    gap: 1rem;

    :deep(.el-button) {
      border-radius: 50%;
      width: 40px;
      height: 40px;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(59, 130, 246, 0.1);
        color: #3b82f6;
        transform: scale(1.05);
      }

      :global(.dark) & {
        &:hover {
          background-color: rgba(96, 165, 250, 0.1);
          color: #60a5fa;
        }
      }
    }
  }
}

.logo-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.02);
  }
}

.logo-icon {
  // width: 50px;
  height: 40px;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.05);
  }
}



.nav-link {
  color: #64748b;
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover {
    color: #3b82f6;
    background-color: rgba(59, 130, 246, 0.05);
    transform: translateY(-1px);

    &::before {
      opacity: 1;
    }
  }

  &--active {
    color: #3b82f6;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
  }

  :global(.dark) & {
    color: #94a3b8;

    &:hover {
      color: #60a5fa;
      background-color: rgba(96, 165, 250, 0.05);
    }

    &--active {
      color: #60a5fa;
      background: linear-gradient(135deg, rgba(96, 165, 250, 0.1), rgba(167, 139, 250, 0.1));
      box-shadow: 0 2px 8px rgba(96, 165, 250, 0.2);
    }
  }
}

.user-avatar {
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
  }

  :global(.dark) & {
    background: linear-gradient(135deg, #60a5fa, #a78bfa);
  }
}

.mobile-menu-btn {
  @media (min-width: 768px) {
    display: none;
  }
}

.mobile-menu {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(226, 232, 240, 0.5);
  animation: slideDown 0.3s ease-out;

  @media (min-width: 768px) {
    display: none;
  }

  :global(.dark) & {
    background: rgba(30, 41, 59, 0.98);
    border-top: 1px solid rgba(71, 85, 105, 0.5);
  }

  &__content {
    padding: 1rem 1rem 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    @media (min-width: 640px) {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }
}

.mobile-nav-link {
  display: block;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  color: #64748b;
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    color: #3b82f6;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
    transform: translateX(4px);
  }

  :global(.dark) & {
    color: #94a3b8;

    &:hover {
      color: #60a5fa;
      background: linear-gradient(135deg, rgba(96, 165, 250, 0.1), rgba(167, 139, 250, 0.1));
    }
  }
}

.main-content {
  flex: 1;
  position: relative;
  z-index: 1;
}

.footer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(226, 232, 240, 0.5);
  margin-top: auto;

  :global(.dark) & {
    background: rgba(30, 41, 59, 0.95);
    border-top: 1px solid rgba(71, 85, 105, 0.5);
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;

    @media (max-width: 768px) {
      padding: 0 0.5rem;
    }
  }

  &__content {
    padding: 2rem 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;

    @media (min-width: 768px) {
      flex-direction: row;
      gap: 0;
    }
  }

  &__logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  &__copyright {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;

    :global(.dark) & {
      color: #94a3b8;
    }
  }
}

.footer-icon {
  // width: 36px;
  height: 36px;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.05);
  }
}



// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 下拉菜单样式优化
:deep(.el-dropdown-menu) {
  border-radius: 12px;
  border: none;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);

  :global(.dark) & {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(71, 85, 105, 0.3);
  }

  .el-dropdown-menu__item {
    border-radius: 8px;
    margin: 4px 8px;
    transition: all 0.2s ease;

    &:hover {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
      color: #3b82f6;

      :global(.dark) & {
        background: linear-gradient(135deg, rgba(96, 165, 250, 0.1), rgba(167, 139, 250, 0.1));
        color: #60a5fa;
      }
    }

    &.is-disabled {
      color: #9ca3af;
      font-weight: 600;

      :global(.dark) & {
        color: #6b7280;
      }
    }
  }
}
</style>
