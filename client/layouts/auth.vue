<template>
  <div class="auth-container">
    <div class="auth-content">
      <!-- Logo -->
      <div class="auth-header">
        <div class="auth-logo">
          <img src="~/assets/promax.svg" alt="ProMax" class="auth-icon">
        </div>
      </div>

      <!-- 认证表单容器 -->
      <div class="auth-form">
        <slot />
      </div>

      <!-- 颜色模式切换 -->
      <div class="color-mode-switch">
        <!-- <el-button
          :icon="isDark ? Sunny : Moon"
          text
          @click="toggleColorMode"
        >
          {{ isDark ? '浅色模式' : '深色模式' }}
        </el-button> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面逻辑可以在这里添加
</script>

<style lang="scss" scoped>
.auth-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  position: relative;
  overflow: hidden;
  animation: fadeIn 0.8s ease-out;

  :global(.dark) & {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  // 添加背景装饰
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
  }
}

.auth-content {
  max-width: 28rem;
  width: 100%;
  position: relative;
  z-index: 1;
}

.auth-header {
  text-align: center;
  margin-bottom: 3rem;
  animation: slideInDown 0.6s ease-out 0.2s both;
}

.auth-logo {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.auth-icon {
  // width: 7rem;
  height: 6rem;
  animation: pulse 2s ease-in-out infinite;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}



.auth-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 2.5rem;
  border-radius: 24px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideInUp 0.6s ease-out 0.4s both;

  :global(.dark) & {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(71, 85, 105, 0.3);
  }

  @media (max-width: 640px) {
    padding: 2rem 1.5rem;
  }
}

.color-mode-switch {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  animation: slideInUp 0.6s ease-out 0.6s both;

  :deep(.el-button) {
    border-radius: 12px;
    font-weight: 600;
    padding: 10px 20px;
    transition: all 0.2s ease;
    border: 2px solid rgba(226, 232, 240, 0.5);
    color: #64748b;

    &:hover {
      border-color: #3b82f6;
      color: #3b82f6;
      background: rgba(59, 130, 246, 0.05);
      transform: translateY(-1px);
    }

    :global(.dark) & {
      border-color: rgba(71, 85, 105, 0.5);
      color: #94a3b8;

      &:hover {
        border-color: #60a5fa;
        color: #60a5fa;
        background: rgba(96, 165, 250, 0.05);
      }
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes rotate {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
