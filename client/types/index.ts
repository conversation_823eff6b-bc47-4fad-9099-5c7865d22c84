// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  fullName?: string
  avatar?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface LoginCredentials {
  usernameOrEmail: string
  password: string
}

export interface RegisterData {
  username: string
  email: string
  password: string
  fullName?: string
}

export interface AuthResponse {
  access_token: string
  user: User
}

// 流程相关类型
export enum ProcessStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  ARCHIVED = 'archived'
}

export interface Process {
  id: number
  name: string
  description?: string
  status: ProcessStatus
  businessDomain?: string
  metadata?: Record<string, any>
  createdAt: string
  updatedAt: string
  userId: number
  user?: User
  eventLogCount?: number
  analysisResults?: AnalysisResult[]
}

export interface CreateProcessData {
  name: string
  description?: string
  businessDomain?: string
  status?: ProcessStatus
  metadata?: Record<string, any>
}

// 事件日志相关类型
export interface EventLog {
  id: number
  caseId: string
  activity: string
  timestamp: string
  resource?: string
  cost?: number
  attributes?: Record<string, any>
  processId: number
  parentCaseId?: string
}

export interface UploadDataConfig {
  processId: number
  caseIdField: string
  activityField: string
  timestampField: string
  resourceField?: string
  costField?: string
  activityIdField?: string
  previousActivityField?: string
  endTimestampField?: string
  parentCaseIdField?: string
  timestampFormat?: string
  endTimestampFormat?: string
  clearExistingData?: boolean
  dateTimeFormats?: {
    [fieldName: string]: string
  }
}

export interface DataValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  totalRows: number
  validRows: number
  preview: any[]
}

// 分析相关类型
export enum AnalysisType {
  PROCESS_DISCOVERY = 'process_discovery',
  PERFORMANCE_ANALYSIS = 'performance_analysis',
  CONFORMANCE_CHECK = 'conformance_check',
  VARIANT_ANALYSIS = 'variant_analysis',
  SOCIAL_NETWORK = 'social_network'
}

export interface AnalysisResult {
  id: number
  analysisType: AnalysisType
  resultData: Record<string, any>
  parameters?: Record<string, any>
  description?: string
  createdAt: string
  processId: number
}

// DFG相关类型
export interface DFGNode {
  id: string
  label: string
  frequency: number
  avgDuration?: number
  minDuration?: number
  maxDuration?: number
  // 层次结构相关字段
  parentCaseId?: string
  isSubprocessNode?: boolean
  subprocessLevel?: number
  groupId?: string // 用于GoJS分组
  // 节点类型标识
  isStartNode?: boolean // 标识开始节点（包括子流程开始节点）
  isEndNode?: boolean // 标识结束节点（包括子流程结束节点）
}

export interface DFGEdge {
  source: string
  target: string
  frequency: number
  avgDuration?: number
  minDuration?: number
  maxDuration?: number
  // 层次结构相关字段
  isSubprocessEdge?: boolean
  subprocessLevel?: number
  groupId?: string // 用于GoJS分组
}

export interface DFGResult {
  nodes: DFGNode[]
  edges: DFGEdge[]
  statistics: {
    totalCases: number
    totalActivities: number
    avgCaseDuration: number
    startActivities: string[]
    endActivities: string[]
  }
  hierarchicalInfo?: {
    hasHierarchy: boolean
    maxLevel: number
    subprocessCount: number
    hierarchyMap: Record<string, string[]> // parentCaseId -> childCaseIds
  }
}

// 流程发现配置接口
export interface ProcessDiscoveryOptions {
  requiredActivities?: string[]
  forceRefresh?: boolean
}

// 性能分析配置接口
export interface PerformanceAnalysisOptions {
  requiredActivities?: string[]
  forceRefresh?: boolean
}

// 流程比对分析相关类型
export interface ProcessCompareOptions {
  baseStartTime: string
  baseEndTime: string
  offsetDays: number
  requiredActivities?: string[]
  filters?: any // 完整的筛选器状态
  forceRefresh?: boolean
  dimension?: 'frequency' | 'duration'
  displayMode?: 'absolute' | 'difference' | 'percentage'
}

export interface ProcessCompareResult {
  baseDfg: DFGResult
  compareDfg: DFGResult
  compareStatistics: {
    baseTimeRange: {
      start: string
      end: string
      duration: number
    }
    compareTimeRange: {
      start: string
      end: string
      duration: number
    }
    offsetDays: number
    baseCaseCount: number
    compareCaseCount: number
    baseActivityCount: number
    compareActivityCount: number
    commonActivities: string[]
    uniqueBaseActivities: string[]
    uniqueCompareActivities: string[]
    changeMetrics: {
      caseCountChange: number
      caseCountChangePercent: number
      activityCountChange: number
      activityCountChangePercent: number
      avgDurationChange?: number
      avgDurationChangePercent?: number
    }
  }
  timestamp: string
  fromCache: boolean
}

// 扩展的节点详细信息接口
export interface NodeDetailInfo {
  id: string
  label: string
  basicStats: {
    frequency: number
    avgDuration: number
    minDuration: number
    maxDuration: number
    medianDuration: number
    stdDeviation: number
  }
  timeDistribution: {
    intervals: Array<{
      range: string
      count: number
      percentage: number
    }>
    quartiles: {
      q1: number
      q2: number
      q3: number
    }
  }
  resourceAnalysis: {
    resources: Array<{
      resource: string
      frequency: number
      avgDuration: number
      efficiency: number
    }>
  }
  pathAnalysis: {
    predecessors: Array<{
      activity: string
      frequency: number
      percentage: number
    }>
    successors: Array<{
      activity: string
      frequency: number
      percentage: number
    }>
  }
  timePatterns: {
    hourlyDistribution: Array<{
      hour: number
      count: number
    }>
    weeklyDistribution: Array<{
      dayOfWeek: number
      count: number
    }>
  }
  anomalies: Array<{
    caseId: string
    duration: number
    reason: string
    severity: 'low' | 'medium' | 'high'
  }>
}

// 扩展的连接详细信息接口
export interface EdgeDetailInfo {
  source: string
  target: string
  basicStats: {
    frequency: number
    avgDuration: number
    minDuration: number
    maxDuration: number
    medianDuration: number
    percentage: number
  }
  timeDistribution: {
    intervals: Array<{
      range: string
      count: number
      percentage: number
    }>
    quartiles: {
      q1: number
      q2: number
      q3: number
    }
  }
  trendAnalysis: {
    timeSeriesData: Array<{
      period: string
      frequency: number
      avgDuration: number
    }>
    trend: 'increasing' | 'decreasing' | 'stable'
  }
  efficiencyAnalysis: {
    comparedToOtherPaths: Array<{
      alternativePath: string
      frequencyDiff: number
      durationDiff: number
    }>
  }
  contextAnalysis: {
    commonPredecessors: Array<{
      activity: string
      frequency: number
    }>
    commonSuccessors: Array<{
      activity: string
      frequency: number
    }>
  }
  caseDetails: Array<{
    caseId: string
    sourceTime: Date
    targetTime: Date
    duration: number
  }>
}

// 变体分析类型
export interface ProcessVariant {
  id: string
  path: string[]
  frequency: number
  percentage: number
  avgDuration: number
  cases: string[]
}

export interface VariantResult {
  variants: ProcessVariant[]
  statistics: {
    totalVariants: number
    mostFrequentVariant: string
    variantCoverage: number
  }
}

// 性能分析类型
export interface PerformanceResult {
  caseStatistics: {
    avgDuration: number
    minDuration: number
    maxDuration: number
    medianDuration: number
  }
  activityStatistics: Array<{
    activity: string
    avgDuration: number
    frequency: number
    avgWaitingTime: number
  }>
  bottlenecks: Array<{
    activity: string
    avgDuration: number
    frequency: number
    impact: number
  }>
  trends: PerformanceTrends
  options?: {
    forceRefresh?: boolean
    requiredActivities?: string[]
  }
}

export interface PerformanceTrends {
    granularities: {
      [granularity: string]: {
        overall: {
          avgDurationChange: number
          avgWaitTimeChange: number
          totalCases: number
        }
        timeSeries: Array<{
          period: string
          start: Date
          end: Date
          avgDuration: number
          avgWaitTime: number
          caseCount: number
        }>
        rankings: {
          activity: {
            improvements: Array<{
              name: string
              change: number
              currentValue: number
              previousValue: number
            }>
            declines: Array<{
              name: string
              change: number
              currentValue: number
              previousValue: number
            }>
          }
          resource: {
            improvements: Array<{
              name: string
              change: number
              currentValue: number
              previousValue: number
            }>
            declines: Array<{
              name: string
              change: number
              currentValue: number
              previousValue: number
            }>
          }
        }
      }
    }
    activityDurations: {
      current: Array<{
        name: string
        value: number
        percentage: number
      }>
    }
    resourceWaitTimes: {
      current: {
        legend: string[]
        data: number[]
        periods: string[]
      }
    }
    metadata?: {
      totalEvents: number
      totalCases: number
      timeRange: { start: Date; end: Date }
      computedAt: Date
    }
  }

// API响应类型
export interface ApiResponse<T = any> {
  success?: boolean
  message?: string
  data?: T
  errors?: string[]
}

// 分页类型
export interface PaginationMeta {
  page: number
  limit: number
  total: number
  totalPages: number
}

export interface PaginatedResponse<T> {
  data: T[]
  meta: PaginationMeta
}

export type PaginationRequest = {
  page?: number
  pageSize?: number
}

// 统计类型
export interface ProcessStatistics {
  total: number
  byStatus: {
    draft: number
    active: number
    completed: number
    archived: number
  }
}

export interface DataStatistics {
  totalEvents: number
  uniqueCases: number
  uniqueActivities: number
  uniqueResources: number
  dateRange: {
    start: string
    end: string
  }
}

// 符合性检查相关类型定义

export enum ConformanceStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export enum DeviationType {
  MISSING_ACTIVITY = 'missing_activity',
  EXTRA_ACTIVITY = 'extra_activity',
  WRONG_ORDER = 'wrong_order',
  SKIPPED_ACTIVITY = 'skipped_activity',
  REPEATED_ACTIVITY = 'repeated_activity',
  TIMING_VIOLATION = 'timing_violation',
}

export interface BpmnModel {
  id: number
  name: string
  description?: string
  bpmnXml: string
  processId: number
  modelType: 'reference' | 'discovered' | 'normative'
  status?: string
  activities: string[]
  paths: Array<{ from: string; to: string }>
  isValid: boolean
  validationErrors: string[]
  validationWarnings: string[]
  modelData?: {
    activities: string[]
    paths: Array<{ from: string; to: string }>
    events: string[]
    gateways: string[]
    complexity: number
    elementCount: number
    validationErrors?: string[]
    validationWarnings?: string[]
  }
  modelHash?: string
  version?: string
  author?: string
  metadata?: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface ConformanceDeviation {
  caseId: string
  type: DeviationType
  description: string
  activity?: string
  expectedActivity?: string
  timestamp?: Date
  severity: 'low' | 'medium' | 'high'
}

export interface ConformanceCaseAnalysis {
  caseId: string
  isConforming: boolean
  deviationCount: number
  conformanceScore: number
  trace: string[]
  alignedTrace: string[]
}

export interface ConformanceActivityAnalysis {
  activity: string
  frequency: number
  conformanceRate: number
  commonDeviations: string[]
}

export interface ConformanceResult {
  id: number
  processId: number
  bpmnModelId: number
  status: ConformanceStatus
  conformanceScore: number
  fitnessScore: number
  precisionScore: number
  generalizationScore: number
  simplicityScore: number
  totalCases: number
  conformingCases: number
  deviatingCases: number
  conformanceLevel: 'excellent' | 'good' | 'fair' | 'poor'
  majorDeviationTypes: DeviationType[]
  deviations: ConformanceDeviation[]
  alignmentResult: Record<string, any>
  caseAnalysis?: ConformanceCaseAnalysis[]
  activityAnalysis?: ConformanceActivityAnalysis[]
  description?: string
  parameters?: any
  fromCache?: boolean
  expiresAt?: string
  createdAt: string
  updatedAt: string
  process: Process
}

export interface ConformanceCheckParams {
  processId: number | undefined
  bpmnModelId: number | string | undefined
  parameters?: {
    alignmentAlgorithm?: 'optimal' | 'heuristic' | 'genetic'
    moveCosts?: {
      modelMove: number
      logMove: number
      synchronousMove: number
    }
    caseFilter?: {
      minActivities?: number
      maxActivities?: number
      startDate?: Date
      endDate?: Date
      includeActivities?: string[]
      excludeActivities?: string[]
    }
    maxExecutionTime?: number
    maxMemoryUsage?: number
    includeAlignment?: boolean
    includeCaseAnalysis?: boolean
    includeActivityAnalysis?: boolean
  }
  forceRefresh?: boolean
  description?: string
}

// API请求时使用的类型，确保必要字段都有值
export interface ConformanceCheckRequest {
  processId: number
  bpmnModelId: number
  parameters?: {
    alignmentAlgorithm?: 'optimal' | 'heuristic' | 'genetic'
    moveCosts?: {
      modelMove: number
      logMove: number
      synchronousMove: number
    }
    caseFilter?: {
      minActivities?: number
      maxActivities?: number
      startDate?: Date
      endDate?: Date
      includeActivities?: string[]
      excludeActivities?: string[]
    }
    maxExecutionTime?: number
    maxMemoryUsage?: number
    includeAlignment?: boolean
    includeCaseAnalysis?: boolean
    includeActivityAnalysis?: boolean
  }
  forceRefresh?: boolean
  description?: string
}

export interface BpmnModelValidation {
  isValid: boolean
  errors: string[]
  warnings: string[]
  modelInfo: {
    activities: number
    paths: number
    complexity: number
  }
}

export interface ConformanceStatistics {
  period: string
  dataPoints: Array<{
    date: string
    averageScore: number
    analysisCount: number
  }>
  trend: string
  improvement: number
}

// 多步骤流程发现相关类型
export enum ProcessDiscoveryStep {
  UPLOAD = 'upload',
  CONFIGURE = 'configure',
  MAP_FIELDS = 'map_fields',
  PROCESSING = 'processing',
  SUCCESS = 'success'
}

export interface ProcessDiscoveryState {
  currentStep: ProcessDiscoveryStep
  processId: number
  file?: File
  uploadConfig?: UploadDataConfig
  validationResult?: DataValidationResult
  processingStatus?: ProcessingStatus
  discoveryResult?: DFGResult
}

export interface ProcessingStatus {
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  progress: number
  currentTask: string
  startTime?: string
  endTime?: string
  error?: string
  discoveryId?: string
  currentStepId?: string
  failedStepId?: string
  phase: 'parse' | 'validate' | 'save' | 'discover'
  phaseError?: string
  validationErrors?: string[]
  result?: {
    savedRecords?: number
    statistics?: {
      totalCases?: number
      totalActivities?: number
      [key: string]: any
    }
    [key: string]: any
  }
}

export interface ProcessingStep {
  id: string
  name: string
  description: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  startTime?: string
  endTime?: string
  error?: string
  validationErrors?: string[]
}

// 数据库连接相关类型
export enum DatabaseType {
  MYSQL = 'mysql',
  POSTGRESQL = 'postgresql',
  MSSQL = 'mssql',
  ORACLE = 'oracle',
}

export enum ConnectionStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error',
}

export interface DatabaseConnection {
  id: number
  name: string
  description?: string
  type: DatabaseType
  host: string
  port: number
  database: string
  username: string
  status: ConnectionStatus
  options?: Record<string, any>
  lastTestedAt?: string
  lastError?: string
  createdAt: string
  updatedAt: string
  connectionString: string
  isAvailable: boolean
  lastTestedDescription: string
}

export interface CreateDatabaseConnectionDto {
  name: string
  description?: string
  type: DatabaseType
  host: string
  port: number
  database: string
  username: string
  password: string
  options?: Record<string, any>
}

export interface UpdateDatabaseConnectionDto {
  name?: string
  description?: string
  type?: DatabaseType
  host?: string
  port?: number
  database?: string
  username?: string
  password?: string
  options?: Record<string, any>
}

export interface TestConnectionDto {
  type: DatabaseType
  host: string
  port: number
  database: string
  username: string
  password: string
  options?: Record<string, any>
}

export interface TestConnectionResponse {
  success: boolean
  message: string
  connectionTime?: number
  version?: string
  error?: string
}

export interface ExecuteQueryDto {
  connectionId: number
  query: string
  limit?: number
}

export interface QueryResult {
  success: boolean
  columns: string[]
  data: any[]
  totalRows: number
  executionTime: number
  error?: string
  fieldTypes?: Record<string, string>
}
