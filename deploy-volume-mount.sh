#!/bin/bash

# ProMax 流程挖掘平台 - 挂载式部署脚本
# 采用代码同步+远程构建+挂载启动的策略

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 加载环境配置
source "$SCRIPT_DIR/scripts/load-env.sh" --quiet

echo "🚀 ProMax 流程挖掘平台 - 挂载式部署"
echo "======================================="
echo "目标服务器: ${SERVER_HOST}"
echo "用户: ${SERVER_USER}"
echo "容器名称: ${CONTAINER_NAME}"
echo "代码目录: ~/promax-code"
echo ""

# 定义远程路径
REMOTE_CODE_DIR="/home/<USER>/promax-code"
RUNTIME_IMAGE_NAME="promax-runtime"
RUNTIME_IMAGE_TAG="latest"

# 1. 代码同步到远程服务器
echo "📤 同步代码到远程服务器..."
ssh ${SERVER_USER}@${SERVER_HOST} "mkdir -p /home/<USER>/promax-code"

# 使用rsync同步代码，排除不必要的文件
rsync -avz --progress \
    --exclude='node_modules' \
    --exclude='.git' \
    --exclude='dist' \
    --exclude='.nuxt' \
    --exclude='.output' \
    --exclude='promax_db.sql.zip' \
    --exclude='__pycache__' \
    --exclude='logs' \
    --exclude='uploads' \
    --exclude='docker-data' \
    --exclude='deployment-temp' \
    --exclude='*.tar.gz' \
    --exclude='coverage' \
    --exclude='test-results' \
    --exclude='reports' \
    . ${SERVER_USER}@${SERVER_HOST}:${REMOTE_CODE_DIR}/

if [ $? -ne 0 ]; then
    echo "❌ 代码同步失败"
    exit 1
fi

echo "✅ 代码同步成功"

# 2. 创建远程构建和部署脚本
echo "📝 创建远程构建脚本..."
cat > /tmp/remote-build-and-deploy.sh << EOF
#!/bin/bash

set -e

REMOTE_CODE_DIR="/home/<USER>/promax-code"
RUNTIME_IMAGE_NAME="promax-runtime"
RUNTIME_IMAGE_TAG="latest"
CONTAINER_NAME="promax-platform"

echo "🔄 开始远程构建和部署..."
echo "当前用户: \$(whoami)"
echo "代码目录: \${REMOTE_CODE_DIR}"

cd \${REMOTE_CODE_DIR}

# 在Docker容器中进行构建
echo "🔨 在Docker容器中构建代码..."

# 检查并构建运行时基础镜像
echo "🔍 检查运行时基础镜像..."
if ! docker images --format 'table {{.Repository}}:{{.Tag}}' | grep -q "^\${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG}\$"; then
    echo "📦 构建运行时基础镜像..."
    docker build -f docker/Dockerfile.runtime -t \${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG} .

    if [ \$? -ne 0 ]; then
        echo "❌ 运行时镜像构建失败"
        exit 1
    fi
    echo "✅ 运行时镜像构建成功"
else
    echo "✅ 运行时镜像已存在，检查yarn是否可用..."
    if ! docker run --rm \${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG} which yarn > /dev/null 2>&1; then
        echo "⚠️  运行时镜像中yarn不可用，重新构建镜像..."
        docker rmi \${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG} || true
        docker build -f docker/Dockerfile.runtime -t \${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG} .

        if [ \$? -ne 0 ]; then
            echo "❌ 运行时镜像重建失败"
            exit 1
        fi
        echo "✅ 运行时镜像重建成功"
    else
        echo "✅ 运行时镜像yarn可用"
    fi
fi

# 使用运行时镜像进行构建
echo "🎨 构建前端..."
if ! docker run --rm \\
    -v \$(pwd):/workspace \\
    -w /workspace/client \\
    -u \$(id -u):\$(id -g) \\
    \${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG} \\
    bash -c "
        set -e
        echo '📦 安装前端依赖...'
        if [ ! -d 'node_modules' ]; then
            yarn install || exit 1
        fi
        echo '🔨 构建前端...'
        yarn build || exit 1
        echo '✅ 前端构建完成'
    "; then
    echo "❌ 前端构建失败"
    exit 1
fi

echo "⚙️  构建后端..."
if ! docker run --rm \\
    -v \$(pwd):/workspace \\
    -w /workspace/server \\
    -u \$(id -u):\$(id -g) \\
    \${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG} \\
    bash -c "
        set -e
        echo '📦 安装后端依赖...'
        if [ ! -d 'node_modules' ]; then
            yarn install || exit 1
        fi
        echo '🔨 构建后端...'
        yarn build || exit 1
        echo '✅ 后端构建完成'

        # 验证构建产物
        echo '🔍 验证构建产物...'
        if [ -d 'dist' ]; then
            echo '✅ dist目录存在'
            ls -la dist/
            if [ -f 'dist/src/main.js' ]; then
                echo '✅ 构建产物验证成功: dist/src/main.js 存在'
            else
                echo '❌ 构建产物验证失败: dist/src/main.js 不存在'
                echo 'dist目录结构:'
                find dist/ -name "*.js" | head -10
                exit 1
            fi
        else
            echo '❌ dist目录不存在'
            exit 1
        fi
    "; then
    echo "❌ 后端构建失败"
    exit 1
fi

echo "🐍 检查Python服务依赖..."
docker run --rm \\
    -v \$(pwd):/workspace \\
    -w /workspace/python-mining-service \\
    \${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG} \\
    bash -c "
        if [ -f 'requirements.txt' ]; then
            echo '📦 安装Python依赖...'
            pip install -r requirements.txt
        fi
        echo '✅ Python服务准备完成'
    "

# 停止并删除现有容器
if docker ps -a --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
    echo "🛑 停止现有容器..."
    docker stop ${CONTAINER_NAME} || true
    docker rm ${CONTAINER_NAME} || true
    echo "✅ 现有容器已清理"
fi

# 创建数据卷目录
echo "📁 创建数据目录..."
mkdir -p /home/<USER>/promax-data/{logs,uploads,reports,result-data,supervisor-logs}
chmod -R 755 /home/<USER>/promax-data

# 启动新容器（挂载代码目录）
echo "🚀 启动新容器..."

# 从环境配置生成Docker参数
source scripts/load-env.sh --quiet

# 生成环境变量参数
ENV_ARGS=\$(generate_docker_env_args | tr '\\n' ' ')
PORT_ARGS=\$(generate_docker_port_args | tr '\\n' ' ')

echo "启动容器参数:"
echo "端口映射: \$PORT_ARGS"
echo "环境变量数量: \$(echo \$ENV_ARGS | wc -w)"

docker run -d \\
    --name \${CONTAINER_NAME} \\
    --restart unless-stopped \\
    --gpus all \\
    \$PORT_ARGS \\
    -v \$(pwd):/app/code \\
    -v /home/<USER>/promax-data/logs:/app/logs \\
    -v /home/<USER>/promax-data/uploads:/app/uploads \\
    -v /home/<USER>/promax-data/reports:/app/reports \\
    -v /home/<USER>/promax-data/result-data:/app/result-data \\
    -v /home/<USER>/promax-data/supervisor-logs:/var/log/supervisor \\
    -e NVIDIA_VISIBLE_DEVICES=all \\
    -e NVIDIA_DRIVER_CAPABILITIES=compute,utility \\
    \$ENV_ARGS \\
    \${RUNTIME_IMAGE_NAME}:\${RUNTIME_IMAGE_TAG}

if [ \$? -ne 0 ]; then
    echo "❌ 容器启动失败"
    docker logs \${CONTAINER_NAME}
    exit 1
fi

echo "✅ 容器启动成功"

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 20

# 检查容器状态
if docker ps --format 'table {{.Names}}\\t{{.Status}}' | grep -q "^\${CONTAINER_NAME}"; then
    echo "✅ 容器运行正常"
    docker ps --filter name=\${CONTAINER_NAME}
else
    echo "❌ 容器启动失败，查看日志:"
    docker logs \${CONTAINER_NAME}
    exit 1
fi

echo ""
echo "🎉 挂载式部署完成！"
echo "=================================="
echo "访问地址: http://192.168.31.96:3100"
echo "API文档: http://192.168.31.96:3100/api/docs"
echo "挖掘服务文档: http://192.168.31.96:3100/mining/docs"
echo ""
echo "代码目录: \${REMOTE_CODE_DIR}"
echo "数据目录: /home/<USER>/promax-data"
echo "Supervisor日志: /home/<USER>/promax-data/supervisor-logs"
echo ""
echo "管理命令:"
echo "查看容器日志: docker logs -f \${CONTAINER_NAME}"
echo "查看Supervisor日志: tail -f /home/<USER>/promax-data/supervisor-logs/*.log"
echo "查看服务状态: docker exec \${CONTAINER_NAME} supervisorctl status"
echo "进入容器: docker exec -it \${CONTAINER_NAME} bash"
echo "重启容器: docker restart \${CONTAINER_NAME}"

EOF

# 上传并执行远程脚本
echo "📤 上传构建脚本..."
scp /tmp/remote-build-and-deploy.sh ${SERVER_USER}@${SERVER_HOST}:~/remote-build-and-deploy.sh

echo "🔄 在服务器上执行构建和部署..."
ssh -t ${SERVER_USER}@${SERVER_HOST} "chmod +x ~/remote-build-and-deploy.sh && sudo ~/remote-build-and-deploy.sh"

if [ $? -ne 0 ]; then
    echo "❌ 远程构建和部署失败"
    exit 1
fi

# 清理临时文件
rm -f /tmp/remote-build-and-deploy.sh

echo ""
echo "🎉 挂载式部署完成！"
echo "=================================="
echo "访问地址: http://${SERVER_HOST}:3100"
echo "API文档: http://${SERVER_HOST}:3100/api/docs"
echo "挖掘服务文档: http://${SERVER_HOST}:3100/mining/docs"
echo ""
echo "管理命令:"
echo "查看容器状态: ssh ${SERVER_USER}@${SERVER_HOST} 'sudo docker ps'"
echo "查看容器日志: ssh ${SERVER_USER}@${SERVER_HOST} 'sudo docker logs -f ${CONTAINER_NAME}'"
echo "查看Supervisor日志: ssh ${SERVER_USER}@${SERVER_HOST} 'tail -f ~/promax-data/supervisor-logs/*.log'"
echo "查看服务状态: ssh ${SERVER_USER}@${SERVER_HOST} 'sudo docker exec ${CONTAINER_NAME} supervisorctl status'"
echo "重启容器: ssh ${SERVER_USER}@${SERVER_HOST} 'sudo docker restart ${CONTAINER_NAME}'"
echo "进入容器: ssh ${SERVER_USER}@${SERVER_HOST} 'sudo docker exec -it ${CONTAINER_NAME} bash'"
echo ""
echo "💡 代码更新流程:"
echo "1. 修改本地代码"
echo "2. 运行: ./deploy-volume-mount.sh"
echo "3. 系统会自动同步代码、重新构建并重启服务"
