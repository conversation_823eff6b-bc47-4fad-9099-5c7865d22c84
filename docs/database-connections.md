# 数据库连接功能

## 概述

数据库连接功能允许用户直接连接到MySQL、PostgreSQL等数据库，通过SQL查询获取数据进行流程挖掘分析。

## 功能特性

### 1. 数据库连接管理
- 支持多种数据库类型：MySQL、PostgreSQL、SQL Server、Oracle
- 安全的密码加密存储
- 连接状态监控和测试
- 连接配置的CRUD操作

### 2. SQL查询编辑器
- 语法高亮的SQL编辑器
- 表结构助手，快速插入表名
- 查询结果预览
- 查询性能监控

### 3. 数据预览和字段映射
- 查询结果的实时预览
- 自动字段类型识别
- 灵活的字段映射配置
- 与现有流程发现流程无缝集成

## 使用方法

### 1. 创建数据库连接

1. 点击右上角用户头像，选择"数据库连接"
2. 点击"新建连接"按钮
3. 填写连接信息：
   - 连接名称：为连接起一个易识别的名称
   - 数据库类型：选择数据库类型（目前支持MySQL）
   - 主机地址：数据库服务器地址
   - 端口：数据库端口（MySQL默认3306）
   - 数据库名：要连接的数据库名称
   - 用户名和密码：数据库认证信息
4. 点击"创建"保存连接

### 2. 测试连接

在连接列表中，点击"测试连接"按钮验证连接是否正常。连接状态会显示为：
- **已连接**：连接正常，可以使用
- **连接失败**：连接有问题，需要检查配置
- **未测试**：尚未测试连接

### 3. 使用SQL查询进行流程发现

1. 在流程管理页面，选择要分析的流程
2. 点击"选择数据来源"
3. 在"本地数据源"部分选择"数据库连接"
4. 在SQL编辑器中：
   - 选择要使用的数据库连接
   - 编写SQL查询语句
   - 使用表结构助手快速插入表名
   - 点击"执行查询"预览结果
5. 确认查询结果后，点击"使用此数据"
6. 在字段映射步骤中配置字段类型
7. 完成流程发现分析

## SQL查询要求

为了进行流程挖掘分析，SQL查询结果应包含以下字段：

### 必需字段
- **案例ID**：标识流程实例的唯一ID
- **活动名称**：流程中的活动或步骤名称

### 可选字段
- **时间戳**：活动发生的时间
- **资源**：执行活动的人员或系统
- **成本**：活动的成本信息
- **其他属性**：任何相关的业务属性

### 示例SQL查询

```sql
-- 基本的流程事件查询
SELECT 
    order_id as case_id,
    status_change as activity,
    created_at as timestamp,
    user_name as resource
FROM order_status_log 
WHERE created_at >= '2024-01-01'
ORDER BY order_id, created_at;

-- 包含成本信息的查询
SELECT 
    ticket_id as case_id,
    action_type as activity,
    action_time as timestamp,
    agent_name as resource,
    processing_cost as cost
FROM support_ticket_actions
WHERE action_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
ORDER BY ticket_id, action_time;
```

## 安全注意事项

1. **密码安全**：数据库密码使用AES-256-CBC加密存储
2. **连接权限**：建议为流程挖掘创建专用的只读数据库用户
3. **查询限制**：SQL查询默认限制返回1000行，最大不超过10000行
4. **网络安全**：确保数据库服务器网络访问安全

## 故障排除

### 连接失败
1. 检查数据库服务器是否运行
2. 验证主机地址和端口是否正确
3. 确认用户名和密码是否正确
4. 检查数据库用户是否有访问权限
5. 确认网络连接是否正常

### 查询执行失败
1. 检查SQL语法是否正确
2. 确认表名和字段名是否存在
3. 验证数据库用户是否有查询权限
4. 检查查询是否超时（默认10秒）

### 字段映射问题
1. 确保查询结果包含必需的案例ID和活动名称字段
2. 检查时间字段格式是否正确
3. 验证数据类型是否匹配

## API接口

### 数据库连接管理
- `GET /api/v1/database-connections` - 获取连接列表
- `POST /api/v1/database-connections` - 创建连接
- `GET /api/v1/database-connections/:id` - 获取连接详情
- `PATCH /api/v1/database-connections/:id` - 更新连接
- `DELETE /api/v1/database-connections/:id` - 删除连接
- `POST /api/v1/database-connections/:id/test` - 测试连接

### SQL查询执行
- `POST /api/v1/database-connections/execute-query` - 执行SQL查询
- `GET /api/v1/database-connections/:id/tables` - 获取表列表
- `GET /api/v1/database-connections/:id/tables/:table/structure` - 获取表结构

## 技术实现

### 后端技术栈
- NestJS + TypeORM
- MySQL2 数据库驱动
- AES-256-CBC 密码加密
- JWT 身份认证

### 前端技术栈
- Nuxt3 + Vue3
- Element Plus UI组件
- TypeScript
- Sass样式

### 数据库表结构
```sql
CREATE TABLE database_connections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    type ENUM('mysql', 'postgresql', 'mssql', 'oracle') DEFAULT 'mysql',
    host VARCHAR(255) NOT NULL,
    port INT DEFAULT 3306,
    database VARCHAR(100) NOT NULL,
    username VARCHAR(100) NOT NULL,
    encryptedPassword VARCHAR(500) NOT NULL,
    status ENUM('active', 'inactive', 'error') DEFAULT 'inactive',
    options JSON,
    lastTestedAt TIMESTAMP NULL,
    lastError TEXT,
    userId INT NOT NULL,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_userId_name (userId, name),
    INDEX idx_userId_status (userId, status),
    FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
);
```
