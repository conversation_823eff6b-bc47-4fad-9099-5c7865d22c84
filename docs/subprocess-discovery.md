# 子流程自动发掘功能

## 概述

子流程自动发掘是ProMined流程挖掘套件的高级功能，专门用于处理复杂的业务流程，特别是制造业质量管理等包含大量节点的场景。该功能能够自动识别和提取流程中的重复模式，将复杂的流程图简化为更易理解的层次化结构。

## 功能特点

### 🔍 智能模式识别
- **顺序子流程**: 识别按固定顺序执行的活动序列
- **并行子流程**: 检测可能同时执行的活动组合
- **循环子流程**: 发现重复执行的活动模式
- **选择子流程**: 识别分支和汇聚点之间的不同路径

### 📊 层次化可视化
- **压缩视图**: 将子流程作为单个节点显示，大幅简化流程图
- **详细展开**: 支持展开子流程查看内部活动
- **统计信息**: 提供压缩率、子流程频率等关键指标

### ⚙️ 灵活配置
- **频率阈值**: 设置子流程最小出现次数
- **长度范围**: 控制子流程包含的活动数量
- **置信度**: 调整模式识别的严格程度
- **检测选项**: 启用/禁用特定类型的模式检测

## 技术实现

### 算法架构

```
事件日志 → 模式提取 → 置信度计算 → 层次化构建 → 可视化渲染
    ↓         ↓          ↓           ↓           ↓
  案例分组   滑动窗口    统计分析    DFG压缩     GoJS图表
```

### 核心算法

1. **滑动窗口序列提取**
   - 使用可配置长度的滑动窗口扫描活动序列
   - 提取所有可能的子序列模式
   - 统计每个模式在不同案例中的出现频率

2. **置信度计算**
   ```typescript
   confidence = pattern_frequency / total_cases
   ```

3. **层次化DFG构建**
   - 将识别的子流程作为复合节点
   - 重新计算节点间的连接关系
   - 保持原有的频率和时间统计信息

### 数据结构

```typescript
interface SubprocessPattern {
  id: string;
  name: string;
  activities: string[];
  frequency: number;
  avgDuration: number;
  cases: string[];
  type: 'sequential' | 'parallel' | 'loop' | 'choice';
  confidence: number;
}

interface SubprocessDiscoveryResult {
  subprocesses: SubprocessPattern[];
  hierarchicalDFG: {
    nodes: SubprocessNode[];
    edges: SubprocessEdge[];
  };
  statistics: {
    totalSubprocesses: number;
    avgSubprocessLength: number;
    compressionRatio: number;
    originalActivities: number;
    compressedActivities: number;
  };
}
```

## API接口

### 1. 子流程发现
```http
POST /api/v1/analysis/subprocess-discovery/:processId
Content-Type: application/json

{
  "minFrequency": 2,
  "minLength": 2,
  "maxLength": 10,
  "confidenceThreshold": 0.7,
  "enableParallelDetection": true,
  "enableLoopDetection": true,
  "groupByDepartment": false,
  "groupByResource": false
}
```

### 2. 层次化DFG生成
```http
POST /api/v1/analysis/hierarchical-dfg/:processId
```

### 3. 子流程模式列表
```http
GET /api/v1/analysis/subprocess-patterns/:processId
```

## 使用场景

### 制造业质量管理
- **采购子流程**: 原材料采购申请 → 供应商资质审核 → 采购合同签署
- **检验子流程**: 入库检验 → 质量评估 → 合格确认
- **生产子流程**: 生产准备 → 首件检验 → 批量生产
- **异常处理**: 不合格品处理 → 整改通知 → 重新检验

### 优势体现
1. **复杂度降低**: 将包含50+活动的流程压缩为10-15个高级节点
2. **模式识别**: 自动发现业务流程中的标准操作程序
3. **异常检测**: 识别偏离标准模式的异常流程
4. **流程优化**: 为流程改进提供数据支持

## 前端界面

### 主要组件
- **配置面板**: 调整发现参数
- **层次化DFG**: 显示压缩后的流程图
- **模式列表**: 展示发现的子流程模式
- **统计仪表板**: 显示分析结果统计

### 交互功能
- **缩放控制**: 支持图表缩放和平移
- **模式展开**: 点击展开查看子流程详情
- **类型筛选**: 按子流程类型过滤显示
- **导出功能**: 支持结果导出

## 性能优化

### 缓存策略
- **结果缓存**: 缓存分析结果避免重复计算
- **增量更新**: 数据变更时智能更新缓存
- **TTL管理**: 自动过期和清理机制

### 算法优化
- **并行处理**: 多线程处理大规模数据
- **内存管理**: 优化内存使用避免OOM
- **索引优化**: 数据库查询性能优化

## 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| minFrequency | number | 2 | 子流程最小出现频率 |
| minLength | number | 2 | 子流程最小长度 |
| maxLength | number | 10 | 子流程最大长度 |
| confidenceThreshold | number | 0.7 | 置信度阈值 |
| enableParallelDetection | boolean | true | 启用并行检测 |
| enableLoopDetection | boolean | true | 启用循环检测 |
| groupByDepartment | boolean | false | 按部门分组 |
| groupByResource | boolean | false | 按资源分组 |

## 最佳实践

### 参数调优
1. **频率阈值**: 根据数据规模调整，小数据集使用较低值
2. **长度范围**: 业务复杂度高时适当增加最大长度
3. **置信度**: 要求高精度时提高阈值，探索性分析时降低

### 数据准备
1. **数据质量**: 确保事件日志完整性和一致性
2. **活动命名**: 使用标准化的活动名称
3. **时间戳**: 保证时间戳的准确性

### 结果解读
1. **压缩率**: 高压缩率表明流程规律性强
2. **子流程类型**: 关注不同类型的分布情况
3. **频率分析**: 识别核心业务流程和异常情况

## 扩展功能

### 未来规划
- **机器学习增强**: 使用ML算法提高模式识别准确性
- **实时发现**: 支持流式数据的实时子流程发现
- **跨流程分析**: 识别不同流程间的共同模式
- **自动命名**: 基于活动语义自动生成子流程名称
