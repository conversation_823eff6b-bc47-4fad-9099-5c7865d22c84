# 层次化流程图显示改进

## 问题描述

原有的子流程发现功能存在以下问题：
1. **缺乏真正的层次化结构**：所有节点都显示为普通活动，子流程没有明显区分
2. **子流程不够突出**：用户无法直观地识别哪些是发现的子流程
3. **缺乏交互功能**：无法在图中定位特定的子流程

## 解决方案

### 1. 后端改进 (Python挖掘服务)

#### 层次化DFG构建算法优化
- **文件**: `python-mining-service/services/subprocess_mining_service.py`
- **方法**: `_build_hierarchical_dfg_async()`

**主要改进**：
1. **序列压缩功能**：将原始活动序列中的子流程模式替换为单个复合节点
2. **节点类型区分**：明确区分子流程节点(`subprocess`)和活动节点(`activity`)
3. **智能过滤**：只保留不在任何子流程中的独立活动

**核心算法**：
```python
def _compress_sequence_with_subprocesses(self, sequence, patterns):
    """将活动序列中的子流程替换为单个节点"""
    compressed = []
    i = 0
    
    while i < len(sequence):
        # 检查是否匹配任何子流程模式
        matched_pattern = None
        max_length = 0
        
        for pattern in patterns:
            pattern_length = len(pattern.activities)
            if (i + pattern_length <= len(sequence) and 
                sequence[i:i + pattern_length] == pattern.activities and
                pattern_length > max_length):
                matched_pattern = pattern
                max_length = pattern_length
        
        if matched_pattern:
            # 用子流程ID替换整个模式
            compressed.append(matched_pattern.id)
            i += len(matched_pattern.activities)
        else:
            # 保留原始活动
            compressed.append(sequence[i])
            i += 1
    
    return compressed
```

### 2. 前端改进 (Vue.js + GoJS)

#### 可视化增强
- **文件**: `client/pages/analysis/[processId]/subprocess.vue`

**主要改进**：

1. **差异化节点模板**：
   - 子流程节点：蓝色背景，较大尺寸，带有📦图标
   - 活动节点：绿色背景，标准尺寸，带有⚡图标

2. **增强的连接线**：
   - 基于频率的动态线宽
   - 贝塞尔曲线提升美观度
   - 频率标签显示

3. **交互功能**：
   - "在图中定位"按钮：快速定位子流程节点
   - 高亮动画效果
   - 自动居中显示

#### 节点模板设计

**子流程节点**：
```javascript
hierarchicalDiagram.nodeTemplateMap.add('subprocess', 
  new go.Node('Auto')
    .add(
      new go.Shape('RoundedRectangle', {
        name: 'SHAPE',
        strokeWidth: 3,
        stroke: '#2196F3',
        fill: '#E3F2FD',
        minSize: new go.Size(120, 70)
      }),
      new go.Panel('Vertical', { margin: 10 })
        .add(
          new go.TextBlock({
            font: 'bold 10px sans-serif',
            stroke: '#1976D2',
            text: '📦 子流程'
          }),
          new go.TextBlock({
            font: 'bold 14px sans-serif',
            stroke: '#1976D2'
          }).bind('text', 'label'),
          new go.TextBlock({
            font: '10px sans-serif',
            stroke: '#666'
          }).bind('text', 'frequency', (freq) => `出现 ${freq} 次`)
        )
    )
)
```

**活动节点**：
```javascript
hierarchicalDiagram.nodeTemplateMap.add('activity',
  new go.Node('Auto')
    .add(
      new go.Shape('RoundedRectangle', {
        name: 'SHAPE',
        strokeWidth: 2,
        stroke: '#4CAF50',
        fill: '#E8F5E8',
        minSize: new go.Size(90, 50)
      }),
      new go.Panel('Vertical', { margin: 8 })
        .add(
          new go.TextBlock({
            font: '9px sans-serif',
            stroke: '#2E7D32',
            text: '⚡ 活动'
          }),
          new go.TextBlock({
            font: '12px sans-serif',
            stroke: '#2E7D32'
          }).bind('text', 'label'),
          new go.TextBlock({
            font: '9px sans-serif',
            stroke: '#666'
          }).bind('text', 'frequency', (freq) => `${freq} 次`)
        )
    )
)
```

### 3. 用户体验改进

#### 模式列表增强
- 添加"在图中定位"按钮
- 改进按钮样式和布局
- 提供即时反馈

#### 高亮定位功能
```javascript
const highlightSubprocessInDiagram = (subprocessId) => {
  if (!hierarchicalDiagram) return
  
  // 清除之前的高亮
  hierarchicalDiagram.clearHighlighteds()
  
  // 查找并高亮子流程节点
  const node = hierarchicalDiagram.findNodeForKey(subprocessId)
  if (node) {
    // 高亮节点
    node.isHighlighted = true
    
    // 将视图中心移动到该节点
    hierarchicalDiagram.centerRect(node.actualBounds)
    
    // 添加临时动画效果
    const shape = node.findObject('SHAPE')
    if (shape) {
      const originalStroke = shape.stroke
      shape.stroke = '#FF5722'
      setTimeout(() => {
        if (shape) shape.stroke = originalStroke
      }, 2000)
    }
  }
}
```

## 测试验证

### 单元测试
- **文件**: `python-mining-service/test_hierarchical_dfg.py`
- **覆盖范围**：
  - 序列压缩算法
  - 节点类型分类
  - 边连接正确性
  - 频率统计准确性

### 测试结果
```
✅ 子流程节点创建正确
✅ 活动节点过滤正确  
✅ 所有边连接正确
```

## 效果展示

### 改进前
- 所有节点外观相同
- 无法区分子流程和活动
- 缺乏交互功能

### 改进后
- 子流程节点：蓝色背景 + 📦图标 + 大尺寸
- 活动节点：绿色背景 + ⚡图标 + 标准尺寸
- 支持快速定位和高亮显示
- 基于频率的动态连接线

## 技术特点

1. **算法优化**：智能序列压缩，准确识别子流程边界
2. **视觉区分**：差异化设计，直观识别节点类型
3. **交互增强**：定位高亮，提升用户体验
4. **性能优化**：异步处理，支持大规模数据

## 未来扩展

1. **展开/收起功能**：点击子流程节点展开内部结构
2. **多层嵌套**：支持子流程内部的子流程
3. **动态更新**：实时更新层次化结构
4. **导出功能**：支持层次化图表导出
