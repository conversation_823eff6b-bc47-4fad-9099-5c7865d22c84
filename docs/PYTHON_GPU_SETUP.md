# 🚀 Python GPU加速子流程挖掘服务 - 快速设置指南

## 📋 前置条件

1. **macOS with Apple Silicon (M1/M2/M3)** - 用于GPU加速
2. **Conda** - Python环境管理
3. **Python 3.12+** - 最新Python版本

## 🛠️ 快速设置

### 步骤1: 检查Conda安装

```bash
# 检查conda版本
conda --version

# 如果未安装，下载Miniconda:
# https://docs.conda.io/en/latest/miniconda.html
```

### 步骤2: 设置Python服务

```bash
# 进入Python服务目录
cd python-mining-service

# 方式1: 自动设置环境 (推荐)
chmod +x setup_env.sh
./setup_env.sh

# 方式2: 如果遇到依赖冲突，使用手动安装
chmod +x install_manual.sh
./install_manual.sh

# 方式3: 使用简化环境配置
conda env create -f environment-simple.yml
conda activate promined-mining

# 方式4: 纯pip安装 (适用于已有Python 3.12)
chmod +x install_pip.sh
./install_pip.sh
```

### 步骤3: 配置环境变量

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件，设置数据库连接
vim .env
```

重要配置项：
```bash
# 数据库配置 (与NestJS保持一致)
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=promined

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# 性能配置
ENABLE_GPU_ACCELERATION=true
MAX_WORKERS=4
BATCH_SIZE=1000
```

### 步骤4: 启动Python服务

```bash
# 方式1: 使用启动脚本 (推荐)
python start_service.py

# 方式2: 直接启动
conda activate promined-mining
python app/main.py

# 服务将在 http://localhost:8000 启动
```

### 步骤5: 验证服务

```bash
# 健康检查
curl http://localhost:8000/health

# 系统信息 (检查GPU状态)
curl http://localhost:8000/system-info

# 运行完整测试
python test_service.py
```

### 步骤6: 配置NestJS集成

在 `server/.env` 文件中添加：
```bash
PYTHON_MINING_SERVICE_URL=http://localhost:8000
```

### 步骤7: 重启NestJS服务

```bash
cd server
npm run start:dev
```

## 🎯 测试集成

### 测试子流程发现API

```bash
# 使用大数据集测试 (会自动调用Python服务)
curl -X POST http://localhost:3003/api/v1/analysis/subprocess-discovery/2 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "options": {
      "minFrequency": 2,
      "confidenceThreshold": 0.1,
      "enableParallelDetection": false,
      "enableLoopDetection": false
    }
  }'
```

## 📊 性能验证

### 预期性能提升

| 数据量 | Node.js (原版) | Python GPU | 性能提升 |
|--------|----------------|------------|----------|
| 1K事件 | 2-4秒          | 1-2秒      | 2x       |
| 10K事件| 20-45秒        | 5-12秒     | 4x       |
| 50K事件| 内存溢出       | 30-60秒    | ∞        |

### GPU加速验证

检查是否正确启用GPU加速：

```bash
# 检查系统信息
curl http://localhost:8000/system-info

# 应该看到:
# "gpu_available": true
# "device": "mps"
```

## 🔧 故障排除

### 常见问题

1. **Conda环境创建失败**
   ```bash
   # 错误: LibMambaUnsatisfiableError 或包冲突

   # 解决方案1: 使用手动安装
   ./install_manual.sh

   # 解决方案2: 使用简化环境
   conda env create -f environment-simple.yml

   # 解决方案3: 使用pip安装
   ./install_pip.sh
   ```

2. **pytorch-metal包不存在**
   ```bash
   # 这个包已经不需要了，PyTorch 2.0+原生支持MPS
   # 使用简化环境配置即可
   conda env create -f environment-simple.yml
   ```

3. **GPU不可用**
   ```bash
   # 检查MPS支持
   python -c "import torch; print(torch.backends.mps.is_available())"

   # 如果返回False，服务会自动使用CPU模式
   ```

4. **端口冲突**
   ```bash
   # 更改Python服务端口
   export PORT=8001

   # 同时更新NestJS配置
   PYTHON_MINING_SERVICE_URL=http://localhost:8001
   ```

5. **内存不足**
   ```bash
   # 减少批处理大小
   export BATCH_SIZE=500

   # 降低内存限制
   export MAX_MEMORY_USAGE_PERCENT=70.0
   ```

6. **数据库连接失败**
   ```bash
   # 确保MySQL服务运行
   brew services start mysql

   # 检查连接
   mysql -h localhost -u root -p promined
   ```

### 性能调优

1. **苹果M芯片优化**
   ```bash
   export PYTORCH_ENABLE_MPS_FALLBACK=1
   export OMP_NUM_THREADS=8
   ```

2. **大数据集处理**
   ```bash
   export MAX_WORKERS=8
   export GPU_MEMORY_FRACTION=0.8
   ```

## 📈 监控和维护

### 实时监控

```bash
# 查看服务日志
tail -f python-mining-service/logs/mining_service.log

# 监控系统资源
htop
```

### API文档

访问 http://localhost:8000/docs 查看完整的API文档

### 性能测试

```bash
cd python-mining-service
python test_service.py
```

## 🎉 完成！

现在你的ProMined系统已经集成了高性能的Python GPU加速子流程挖掘服务！

### 主要优势

- ✅ **GPU加速**: 利用苹果M芯片MPS
- ✅ **内存优化**: 解决大数据集内存溢出问题
- ✅ **智能降级**: Python服务不可用时自动使用Node.js
- ✅ **无缝集成**: 与现有NestJS API完全兼容
- ✅ **实时监控**: 性能和资源使用监控

### 下一步

1. 在前端页面测试子流程发现功能
2. 监控性能指标和内存使用
3. 根据实际使用情况调优参数
4. 考虑部署到生产环境

如有问题，请查看详细文档：
- `python-mining-service/README.md`
- `python-mining-service/DEPLOYMENT.md`
