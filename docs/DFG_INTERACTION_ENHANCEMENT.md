# DFG图表交互功能增强

## 概述

为ProMined项目的DFG（Directly-Follows Graph）图表实现了全面的交互功能增强，提升用户体验和数据分析效率。

## 实现的功能

### 1. 双击详情展示功能 ✅

**功能描述：**
- 双击节点：打开活动详细信息模态框
- 双击连线：打开连接详细信息模态框
- 模态框包含统计数据和ECharts图表

**技术实现：**
- 使用GoJS的`ObjectDoubleClicked`事件监听器
- 集成现有的`DfgDetailDialog`组件
- 支持节点和连线的详细数据展示

**用户体验：**
- 直观的双击操作
- 丰富的数据可视化
- Element Plus Dialog组件提供良好的交互体验

### 2. 单击高亮功能 ✅

**功能描述：**
- 单击节点：高亮选中节点及其直接相关的连线
- 其他节点和连线自动弱化（透明度降低）
- 选中节点显示金色边框强调

**技术实现：**
- 使用GoJS的`ObjectSingleClicked`事件监听器
- 动态修改节点和连线的视觉属性
- 保持原始布局不变，只改变视觉样式

**视觉效果：**
- 高亮节点：金色边框，完全不透明
- 相关连线：保持原色，完全不透明
- 其他元素：透明度降至30%和20%

### 3. 连线方向动画效果 ✅

**功能描述：**
- 高亮连线显示流动虚线动画
- 根据线宽智能调整虚线间隔
- 清晰显示流程方向

**技术实现：**
- 动态计算虚线间隔：`dashLength = max(lineWidth * 3, 12)`
- 使用GoJS Animation类实现专业动画
- 通过`strokeDashOffset`控制虚线流动
- 统一管理所有高亮连线的动画

**视觉效果：**
- 智能虚线间隔：根据线宽自适应
- 流动动画：虚线向前移动表示方向
- 线宽增加：原始宽度 + 2px
- 动画周期：2秒，无限循环

### 4. 空白区域点击取消高亮 ✅

**功能描述：**
- 点击图表空白区域取消所有高亮效果
- 恢复所有元素的原始状态
- 停止所有流动动画

**技术实现：**
- 使用GoJS的`BackgroundSingleClicked`事件监听器
- 调用`clearHighlight()`函数重置状态
- 移除虚线样式和CSS动画

### 5. 性能优化和兼容性 ✅

**优化措施：**
- 避免重新布局，只修改视觉属性
- 使用GoJS Animation类，性能优异且专业
- 智能虚线间隔计算，适配不同线宽
- 统一动画管理，避免多个动画冲突
- 高亮后自动重新对齐开始和结束节点，保持布局完整性
- 在维度切换和筛选器变化时自动清除高亮状态
- 组件卸载时清理资源
- 添加错误处理，确保功能稳定性

**兼容性保证：**
- 与现有全局筛选器组件完全兼容
- 与频率/耗时维度切换功能兼容
- 保持现有的缩放和平移功能
- 使用纯CSS/Sass样式，符合项目规范

## 用户交互流程

1. **浏览模式**：用户可以正常查看DFG图表
2. **高亮模式**：单击节点进入高亮模式，显示相关连线和流动动画
3. **详情查看**：双击节点或连线查看详细统计信息
4. **取消高亮**：点击空白区域或切换功能时自动恢复原始状态

## 技术架构

### 状态管理
```typescript
// DFG交互状态
const highlightedNodeKey = ref<string>('')
const highlightedLinks = ref<Set<string>>(new Set())
const isHighlightMode = ref(false)
```

### 核心函数
- `highlightNode(nodeKey: string)`: 高亮指定节点
- `clearHighlight()`: 清除所有高亮效果
- `startFlowAnimationForHighlightedLinks()`: 为所有高亮连线启动统一动画
- `stopAllAnimations()`: 停止所有流动动画

### 动画算法
```typescript
// 智能虚线间隔计算
const dashLength = Math.max(originalWidth * 3, 12) // 虚线长度
const gapLength = Math.max(originalWidth * 2, 8)   // 间隔长度

// GoJS动画实现
flowAnimation = new go.Animation()
flowAnimation.easing = go.Animation.EaseLinear
flowAnimation.duration = 2000 // 2秒周期
flowAnimation.runCount = Infinity // 无限循环

// 为每个高亮连线添加动画
flowAnimation.add(linkShape, "strokeDashOffset", 0, -dashCycle)
flowAnimation.start()
```

### 事件处理
- `ObjectDoubleClicked`: 双击详情展示
- `ObjectSingleClicked`: 单击高亮功能
- `BackgroundSingleClicked`: 空白区域取消高亮

## 样式实现

使用纯CSS/Sass实现，符合项目设计规范：

```scss
// 流动动画效果
@keyframes dash-flow {
  0% { stroke-dashoffset: 0; }
  100% { stroke-dashoffset: -24px; }
}

// 全局样式支持
:deep(svg) {
  .highlighted-flow {
    animation: dash-flow 2s linear infinite;
  }
}
```

## 用户指南更新

图表说明已更新，包含新的交互功能说明：
- **单击节点**：高亮节点及其相关连线，显示流动动画效果
- **双击节点或连线**：打开详细信息模态框，查看统计数据和图表
- **点击空白区域**：取消所有高亮效果，恢复原始状态
- **高亮模式下连线显示流动虚线动画，清晰表示流程方向**

## 动画实现细节

### 问题解决过程
1. **初始问题**：虚线间隔太小，缺少动画效果
2. **第一次尝试**：使用requestAnimationFrame，但性能不佳
3. **第二次尝试**：直接操作DOM元素，但GoJS结构复杂
4. **最终解决**：使用GoJS Animation类，参考官方论坛建议

### 关键技术点
- 使用`go.Animation`类而非自定义动画循环
- 正确设置动画起始值和结束值：`(dashCycle, 0)`而非`(0, -dashCycle)`
- 初始化`strokeDashOffset = 0`确保动画起点正确
- 统一管理所有高亮连线的动画，避免冲突

### 调试信息
代码中包含详细的console.log输出，便于调试：
- 虚线数组设置确认
- 动画参数计算验证
- 动画启动状态检查

## 总结

本次增强实现了完整的DFG图表交互功能，包括：
- ✅ 双击详情展示
- ✅ 单击高亮和动画
- ✅ 空白区域取消高亮
- ✅ 性能优化和兼容性保证

所有功能都基于GoJS框架实现，使用Element Plus组件和纯CSS/Sass样式，与现有系统完美集成，显著提升了用户的数据分析体验。动画效果使用GoJS官方推荐的Animation类实现，确保性能和稳定性。
