# 全局筛选器组件实现文档

## 概述

在流程发现页面（`client/pages/analysis/[processId]/discover.vue`）成功添加了全局筛选器组件，实现了四种核心筛选功能，提供了强大的数据过滤和分析能力。

## 🎯 实现的功能

### 1. 资源筛选
- **功能描述**: 允许用户选择特定的资源（执行者/角色）来过滤流程数据
- **特性**:
  - 多选支持
  - 搜索功能
  - 显示每个资源的记录数量
  - 实时筛选

### 2. 活动耗时筛选
- **功能描述**: 提供时间范围选择器，根据活动执行时间长度进行筛选
- **特性**:
  - 最小值和最大值设置
  - 支持多种时间单位（分钟、小时、天）
  - 数值输入验证
  - 范围筛选

### 3. 业务字段内容筛选
- **功能描述**: 基于流程数据中的业务属性字段进行筛选
- **特性**:
  - 动态字段选择
  - 多种筛选操作符（包含、等于、大于、小于、范围）
  - 文本匹配和数值范围筛选
  - 灵活的筛选条件组合

### 4. 前后续活动占比筛选
- **功能描述**: 根据特定活动的前置活动和后续活动的出现频率/占比进行筛选
- **特性**:
  - 活动选择器
  - 前置/后续活动类型切换
  - 百分比范围滑块
  - 可视化占比显示

## 🏗️ 技术架构

### 组件结构
```
client/components/analysis/GlobalFilter.vue
├── 筛选器头部（标题、操作按钮）
├── 可折叠的筛选内容区域
│   ├── 资源筛选组
│   ├── 活动耗时筛选组
│   ├── 业务字段筛选组
│   └── 前后续活动占比筛选组
└── 筛选结果统计和活跃筛选标签
```

### 核心特性

#### 1. 响应式设计
- 使用CSS Grid布局，自适应不同屏幕尺寸
- 移动端友好的单列布局
- 现代化的玻璃拟态设计风格

#### 2. 状态管理
- 完整的筛选状态追踪
- 本地存储支持（保存/加载筛选条件）
- 实时筛选结果统计

#### 3. 用户体验
- 可折叠的筛选面板
- 活跃筛选条件的可视化标签
- 一键重置功能
- 筛选条件保存功能

## 🎨 设计系统集成

### 样式规范
- **颜色系统**: 使用项目统一的主题色彩
- **间距系统**: 遵循设计系统的间距规范
- **字体系统**: 统一的字体大小和权重
- **组件样式**: 与Element Plus组件保持一致

### 动画效果
- **进入动画**: fadeInDown 动画
- **交互动画**: 悬停和点击效果
- **过渡动画**: 平滑的状态切换

## 🔧 集成方式

### 在流程发现页面中的集成

1. **组件导入**:
```vue
import GlobalFilter from '~/components/analysis/GlobalFilter.vue'
```

2. **模板使用**:
```vue
<GlobalFilter
  v-if="discoveryResult"
  :process-id="processId"
  :discovery-data="discoveryResult"
  @filter-change="onFilterChange"
  @reset="onFilterReset"
/>
```

3. **事件处理**:
- `onFilterChange`: 处理筛选条件变化
- `onFilterReset`: 处理筛选重置

### 🆕 动态数据提取功能

#### 智能资源提取
组件会自动从流程数据中提取资源信息：
- 支持多种资源字段：`resource`, `performer`, `executor`, `assignee`, `user`, `actor`
- 自动统计每个资源的使用频率
- 按频率排序显示

#### 智能业务字段识别
- 自动分析节点属性，识别业务字段
- 排除系统字段，只显示业务相关字段
- 智能推断字段类型（字符串、数字、日期）
- 提供中文字段标签映射

#### 活动列表自动生成
- 从节点数据中自动提取活动列表
- 支持 `id`, `label`, `name` 等字段
- 自动去重和排序

### 数据流处理

#### 筛选逻辑
- 保存原始未筛选的数据 (`originalDiscoveryResult`)
- 应用筛选条件生成新的数据集
- 实时更新DFG图表和统计信息

#### 筛选算法
- **资源筛选**: 基于节点的资源字段
- **耗时筛选**: 基于活动和连接的平均耗时
- **业务字段筛选**: 支持多种操作符的字段匹配
- **路径占比筛选**: 基于前置/后续活动的频率占比计算

## 🤖 智能数据提取

### 资源字段识别
```javascript
// 支持的资源字段（按优先级）
const resourceFields = ['resource', 'performer', 'executor', 'assignee', 'user', 'actor']

// 自动统计资源使用频率
resourceFields.forEach(field => {
  if (node[field] && typeof node[field] === 'string') {
    resourceSet.add(node[field])
    resourceCounts.set(node[field], count + frequency)
  }
})
```

### 业务字段智能分析
```javascript
// 排除的系统字段
const systemFields = [
  'id', 'label', 'frequency', 'avgDuration', 'minDuration', 'maxDuration',
  'nodeStyle', 'resource', 'performer', 'executor', 'assignee', 'user', 'actor',
  'key', 'color', 'borderColor', 'currentValue', 'dimension'
]

// 字段类型推断
if (typeof firstValue === 'number') {
  fieldTypes.set(field, 'number')
} else if (firstValue instanceof Date || isDateString(firstValue)) {
  fieldTypes.set(field, 'date')
} else {
  fieldTypes.set(field, 'string')
}
```

### 字段标签本地化
```javascript
const fieldMappings = {
  'department': '部门',
  'priority': '优先级',
  'amount': '金额',
  'customer_type': '客户类型',
  'region': '地区',
  // ... 更多映射
}
```

## 📊 功能特点

### 1. 实时筛选
- 筛选条件变化时立即更新结果
- 无需手动触发筛选操作
- 平滑的视觉反馈

### 2. 筛选状态可视化
- 活跃筛选条件的标签显示
- 筛选结果数量统计
- 占比百分比显示

### 3. 数据持久化
- 筛选条件的本地存储
- 页面刷新后自动恢复筛选状态
- 按流程ID分别保存筛选配置

### 4. 用户友好性
- 直观的筛选界面
- 清晰的操作反馈
- 便捷的重置和保存功能

## 🧪 测试页面

创建了专门的测试页面 (`client/pages/test-filter.vue`) 用于验证筛选器组件的功能：

- 独立的组件测试环境
- 实时显示筛选条件变化
- 便于调试和功能验证

## 🚀 使用方法

### 基本使用
1. 在流程发现页面完成流程分析后，筛选器会自动显示
2. 点击筛选器头部可以展开/折叠筛选面板
3. 在各个筛选组中设置筛选条件
4. 筛选结果会实时更新到DFG图表和数据模块

### 高级功能
- **保存筛选**: 点击"保存"按钮将当前筛选条件保存到本地
- **重置筛选**: 点击"重置"按钮清除所有筛选条件
- **移除单个筛选**: 点击筛选标签的关闭按钮移除特定筛选条件

## 🔮 扩展性

### 新增筛选类型
组件设计具有良好的扩展性，可以轻松添加新的筛选类型：

1. 在 `FilterState` 接口中添加新的筛选字段
2. 在模板中添加新的筛选组
3. 在 `applyFilters` 方法中实现筛选逻辑
4. 更新 `checkHasActiveFilters` 和 `activeFilterTags` 计算属性

### 自定义筛选逻辑
可以根据具体的业务需求自定义筛选算法，特别是：
- 复杂的业务字段筛选规则
- 高级的路径分析算法
- 多维度的数据关联筛选

## 📝 注意事项

1. **数据结构依赖**: 筛选功能依赖于特定的数据结构，需要确保流程数据包含必要的字段
2. **性能考虑**: 对于大数据集，筛选操作可能需要优化以提高性能
3. **浏览器兼容性**: 使用了现代CSS特性，需要考虑旧版浏览器的兼容性

## 🎉 总结

全局筛选器组件成功实现了四种核心筛选功能，提供了强大而灵活的数据过滤能力。组件采用现代化的设计风格，具有良好的用户体验和扩展性，完美集成到了流程发现页面中，为用户提供了更加精细化的流程分析工具。
