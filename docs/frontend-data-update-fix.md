# 前端数据更新问题修复

## 问题描述

在应用流程发现配置并勾选刷新缓存后，前端的挖掘结果数据未正确更新，需要强制刷新页面才能看到正确结果。同时，GlobalFilter组件出现了JavaScript错误：

```
TypeError: Cannot read properties of undefined (reading '0')
at GlobalFilter.vue:371:29
```

## 问题分析

### 1. 数据更新问题
在`applyDiscoveryConfig`方法中，只更新了`discoveryResult.value`，但没有更新`originalDiscoveryResult.value`。这导致：
- 如果用户之后使用筛选功能，数据会被重置回原来的结果
- 筛选器状态与新数据不同步

### 2. GlobalFilter组件错误
当流程发现数据更新时，如果GlobalFilter组件中已经有pathway类型的条件，但新数据还没有传递到组件，或者在数据更新过程中出现时序问题，`condition.data.frequency`可能会变成undefined，导致访问`frequency[0]`时出错。

## 解决方案

### 1. 修复数据更新逻辑

在`discover.vue`的`applyDiscoveryConfig`方法中：

```typescript
// 处理结果数据，确保包含时间戳
const processedResult = {
  ...result,
  timestamp: result.timestamp || new Date().toISOString()
}

// 更新原始数据和当前显示数据
originalDiscoveryResult.value = processedResult
discoveryResult.value = processedResult

// 如果有活跃的筛选器，重新应用筛选器
if (hasActiveFilters.value && currentFilters.value) {
  console.log('Re-applying active filters after config change')
  await onFilterChange(currentFilters.value)
} else {
  // 重新渲染DFG
  await nextTick()
  if (dfgContainer.value) {
    renderDFG()
  }
}
```

### 2. 修复GlobalFilter组件错误

#### 2.1 添加模板安全检查

在模板中添加条件渲染，确保`frequency`数组存在：

```vue
<div class="input-row" v-if="condition.data.frequency && Array.isArray(condition.data.frequency)">
  <el-input-number
    v-model="condition.data.frequency[0]"
    :min="1"
    :max="condition.data.frequency[1]"
    placeholder="最小频次"
    size="small"
    class="frequency-input"
    @change="emitFilterChange"
  />
  <span class="frequency-separator">至</span>
  <el-input-number
    v-model="condition.data.frequency[1]"
    :min="condition.data.frequency[0]"
    :max="getMaxFrequencyForCondition(condition)"
    placeholder="最大频次"
    size="small"
    class="frequency-input"
    @change="emitFilterChange"
  />
</div>
<div v-else class="frequency-loading">
  <span>加载频次数据中...</span>
</div>
```

#### 2.2 数据变化时重新初始化

在数据监听器中添加pathway条件的重新初始化：

```typescript
// 监听流程数据变化
watch(() => props.discoveryData, (newData) => {
  if (newData) {
    extractFilterOptions(newData)
    
    // 重新初始化pathway条件的frequency数组
    const maxFreq = getMaxFrequencyFromData()
    filters.value.groups.forEach(group => {
      if (group.type === 'pathway') {
        group.conditions.forEach(condition => {
          if (!condition.data.frequency || !Array.isArray(condition.data.frequency)) {
            condition.data.frequency = [1, maxFreq]
          } else {
            // 更新最大值，但保持用户设置的当前值
            condition.data.frequency[1] = Math.min(condition.data.frequency[1], maxFreq)
          }
        })
      }
    })
  }
}, { immediate: true, deep: true })
```

#### 2.3 添加加载状态样式

```scss
.frequency-loading {
  padding: spacing(2);
  text-align: center;
  color: theme-color(gray, 500);
  font-size: font-size(sm);
  background-color: theme-color(gray, 50);
  border-radius: border-radius(sm);
}
```

## 修复效果

### 1. 数据更新正常
- 应用配置后，`originalDiscoveryResult`和`discoveryResult`都正确更新
- 如果有活跃筛选器，会自动重新应用到新数据上
- 不再需要手动刷新页面

### 2. 组件错误消除
- 添加了安全检查，避免访问undefined数组
- 数据更新时自动重新初始化pathway条件
- 提供友好的加载状态提示

### 3. 用户体验改善
- 配置应用后立即看到结果
- 筛选器状态与新数据保持同步
- 没有JavaScript错误干扰

## 测试验证

1. **基本功能测试**：
   - 应用流程发现配置
   - 勾选强制刷新缓存
   - 验证结果立即更新

2. **筛选器兼容性测试**：
   - 先应用筛选器
   - 再应用流程发现配置
   - 验证筛选器自动重新应用

3. **错误处理测试**：
   - 在有pathway筛选条件时更新数据
   - 验证不出现JavaScript错误
   - 验证加载状态正常显示

## 注意事项

1. **数据一致性**：确保`originalDiscoveryResult`和`discoveryResult`始终保持同步
2. **筛选器状态**：配置变更后需要重新评估筛选器的有效性
3. **性能考虑**：避免不必要的重新渲染和数据处理
4. **错误处理**：对所有可能的undefined情况进行防护
