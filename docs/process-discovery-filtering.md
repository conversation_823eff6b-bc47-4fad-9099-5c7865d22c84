# 流程发现活动节点过滤功能

## 功能概述

在流程发现中增加了活动节点过滤配置功能，允许用户指定必须包含的活动节点，只有包含这些活动节点的案例链路才会参与流程发现计算。

## 功能特性

- **活动节点过滤**: 支持选择多个必须包含的活动节点
- **包含任一逻辑**: 多个活动节点是"或"的关系，即案例中包含任一活动节点即可
- **动态活动列表**: 从现有流程发现结果中动态加载可选的活动节点
- **配置界面**: 提供友好的配置对话框界面
- **向后兼容**: 保持与现有API的向后兼容性

## 实现架构

### 后端实现

#### 1. DTO定义
- `ProcessDiscoveryOptionsDto`: 流程发现配置参数
  - `requiredActivities?: string[]`: 必须包含的活动节点列表
  - `forceRefresh?: boolean`: 是否强制刷新缓存

#### 2. 服务层修改
- `ProcessMiningService.discoverProcess()`: 修改方法签名支持配置参数
- `filterEventLogsByRequiredActivities()`: 新增过滤方法，实现案例级别的活动节点过滤

#### 3. API控制器
- `POST /api/v1/analysis/discover/:processId`: 支持请求体传递配置参数
- 保持查询参数的向后兼容性

### 前端实现

#### 1. 类型定义
- `ProcessDiscoveryOptions`: 流程发现配置接口

#### 2. 配置组件
- `ProcessDiscoveryConfig.vue`: 流程发现配置对话框
  - 活动节点多选下拉框
  - 强制刷新选项
  - 配置应用和重置功能

#### 3. 页面集成
- `discover.vue`: 在导出结果按钮后添加配置按钮
- 配置应用后自动重新运行流程发现

## 使用方法

### 1. 打开配置界面
在流程发现页面，完成初始流程发现后，点击"流程发现配置"按钮。

### 2. 选择活动节点
在配置对话框中，从下拉列表中选择必须包含的活动节点。可以选择多个节点。

### 3. 应用配置
点击"应用配置"按钮，系统将使用新的配置重新运行流程发现。

### 4. 查看结果
配置应用后，流程图将只显示包含指定活动节点的案例链路的流程发现结果。

## API使用示例

### 基础流程发现（无过滤）
```http
POST /api/v1/analysis/discover/1
Content-Type: application/json

{
  "forceRefresh": false
}
```

### 带活动节点过滤的流程发现
```http
POST /api/v1/analysis/discover/1
Content-Type: application/json

{
  "requiredActivities": ["活动A", "活动B"],
  "forceRefresh": false
}
```

## 过滤逻辑说明

1. **案例级过滤**: 过滤在案例（case_id）级别进行，而不是事件级别
2. **包含任一**: 如果指定了多个活动节点，案例中包含任一活动节点即符合条件
3. **完整链路**: 保留符合条件案例的完整事件链路，不会截断或修改事件序列
4. **统计更新**: 过滤后的统计信息（案例数、活动数等）会相应更新

## 技术细节

### 过滤算法
```typescript
private filterEventLogsByRequiredActivities(
  eventLogs: EventLog[],
  requiredActivities: string[],
): EventLog[] {
  // 按案例分组
  const caseGroups = this.groupByCaseId(eventLogs);
  
  // 找出包含必须活动的案例ID
  const validCaseIds = new Set<string>();
  
  Object.entries(caseGroups).forEach(([caseId, events]) => {
    const caseActivities = new Set(events.map(event => event.activity));
    const hasRequiredActivity = requiredActivities.some(activity => 
      caseActivities.has(activity)
    );
    
    if (hasRequiredActivity) {
      validCaseIds.add(caseId);
    }
  });

  // 只保留有效案例的事件日志
  return eventLogs.filter(event => validCaseIds.has(event.caseId));
}
```

### 前端配置组件特性
- 使用Element Plus的多选下拉框
- 支持搜索和过滤
- 动态加载活动节点列表
- 响应式设计，适配不同屏幕尺寸
- z-index优化，确保下拉框正确显示

## 测试覆盖

- 单元测试覆盖过滤逻辑
- API接口测试
- 前端组件测试
- 集成测试验证端到端功能

## 注意事项

1. **性能考虑**: 大数据量时过滤可能影响性能，建议合理选择活动节点
2. **缓存策略**: 不同的过滤配置会生成不同的缓存键
3. **数据一致性**: 过滤后的结果与原始数据保持逻辑一致性
4. **用户体验**: 配置变更后会自动重新计算，用户需要等待计算完成
