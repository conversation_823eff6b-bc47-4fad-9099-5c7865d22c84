# ProMax 用户使用指南

欢迎使用 ProMax 流程挖掘套件！本指南将帮助您快速上手并充分利用 ProMax 的强大功能。

## 📖 目录

1. [什么是 ProMax](#什么是-promined)
2. [快速开始](#快速开始)
3. [核心功能](#核心功能)
4. [数据准备](#数据准备)
5. [流程分析](#流程分析)
6. [可视化报表](#可视化报表)
7. [常见问题](#常见问题)
8. [技术支持](#技术支持)

## 什么是 ProMax

ProMax 是一个专业的流程挖掘平台，帮助企业：

- 🔍 **发现隐藏流程**：从业务数据中自动发现实际执行的业务流程
- 📊 **分析流程性能**：识别流程瓶颈、异常和改进机会
- 📈 **监控流程指标**：实时跟踪关键性能指标（KPI）
- 🎯 **优化业务流程**：基于数据驱动的洞察优化流程效率

## 快速开始

### 第一步：登录系统

1. 打开浏览器，访问 ProMax 平台
2. 使用您的账号和密码登录
3. 首次登录会看到欢迎界面和快速导览

### 第二步：创建项目

1. 点击 **"新建项目"** 按钮
2. 填写项目基本信息：
   - 项目名称：为您的分析项目起一个有意义的名称
   - 项目描述：简要描述项目目标和范围
   - 业务领域：选择相关的业务领域（如：采购、销售、客服等）
3. 点击 **"创建"** 完成项目创建

### 第三步：上传数据

1. 在项目页面点击 **"数据管理"**
2. 选择 **"上传数据文件"**
3. 支持的文件格式：
   - CSV 文件
   - Excel 文件 (.xlsx, .xls)
   - JSON 文件
4. 确保数据包含必要的字段（详见[数据准备](#数据准备)章节）

## 核心功能

### 🔍 流程发现

**功能说明**：自动从事件日志中发现业务流程模型

**使用步骤**：
1. 进入 **"流程发现"** 模块
2. 选择要分析的数据集
3. 配置发现参数：
   - 案例ID字段：标识流程实例的字段
   - 活动字段：流程步骤/活动的字段
   - 时间戳字段：事件发生时间的字段
4. 点击 **"开始发现"**
5. 系统将生成流程模型图

**输出结果**：
- 流程模型图（DFG - 直接跟随图格式，类似 Celonis 风格）
- 流程统计信息
- 活动频率分析
- 可选导出为 BPMN 2.0 标准格式

### 📊 符合性检查

**功能说明**：检查实际执行的流程与标准流程的符合程度

**使用步骤**：
1. 进入 **"符合性检查"** 模块
2. 上传标准流程模型（可选）
3. 选择要检查的事件日志
4. 设置检查参数
5. 查看符合性分析结果

**输出结果**：
- 符合性得分
- 偏差分析报告
- 异常案例列表

### ⚡ 性能分析

**功能说明**：分析流程执行的性能指标和瓶颈

**使用步骤**：
1. 进入 **"性能分析"** 模块
2. 选择分析维度：
   - 案例持续时间
   - 活动等待时间
   - 资源利用率
3. 设置时间范围和过滤条件
4. 生成性能分析报告

**输出结果**：
- 性能指标仪表板
- 瓶颈识别报告
- 改进建议

### 📈 变体分析

**功能说明**：分析流程的不同执行路径和变体

**使用步骤**：
1. 进入 **"变体分析"** 模块
2. 系统自动识别流程变体
3. 查看变体统计信息：
   - 变体数量
   - 变体频率
   - 变体性能对比
4. 深入分析特定变体

## 数据准备

### 数据格式要求

您的数据文件必须包含以下核心字段：

| 字段名称 | 字段类型 | 必需 | 说明 | 示例 |
|---------|---------|------|------|------|
| 案例ID | 文本 | ✅ | 唯一标识一个流程实例 | ORDER_001, TICKET_12345 |
| 活动名称 | 文本 | ✅ | 流程中的具体步骤或活动 | 创建订单, 审批申请, 发货 |
| 时间戳 | 日期时间 | ✅ | 活动发生的时间 | 2024-01-15 14:30:00 |
| 资源 | 文本 | ⭕ | 执行活动的人员或系统 | 张三, 系统自动, 客服部 |
| 成本 | 数值 | ⭕ | 活动相关的成本 | 100.50, 0 |

### 数据示例

```csv
案例ID,活动名称,时间戳,资源,成本
ORDER_001,创建订单,2024-01-15 09:00:00,客户,0
ORDER_001,订单审核,2024-01-15 10:30:00,审核员A,50
ORDER_001,库存检查,2024-01-15 11:00:00,系统,0
ORDER_001,订单确认,2024-01-15 14:00:00,销售员B,25
ORDER_001,发货准备,2024-01-16 08:00:00,仓库员C,30
ORDER_001,订单发货,2024-01-16 15:00:00,快递员,100
ORDER_001,订单完成,2024-01-18 10:00:00,系统,0
```

### 数据质量检查

上传数据后，系统会自动进行质量检查：

- ✅ **完整性检查**：确保必需字段不为空
- ✅ **格式检查**：验证时间戳格式是否正确
- ✅ **一致性检查**：检查数据的逻辑一致性
- ✅ **重复性检查**：识别可能的重复记录

## 流程分析

### 分析工作流

1. **数据预处理**
   - 数据清洗和标准化
   - 异常值检测和处理
   - 数据质量评估

2. **流程发现**
   - 自动生成流程模型
   - 识别主要流程路径
   - 分析流程复杂度

3. **深度分析**
   - 性能瓶颈分析
   - 资源利用率分析
   - 异常模式识别

4. **结果解读**
   - 生成分析报告
   - 提供改进建议
   - 制定优化方案

### 分析技巧

**💡 提示 1：选择合适的时间范围**
- 包含足够的数据样本（建议至少100个案例）
- 避免包含异常时期的数据（如系统维护期间）

**💡 提示 2：合理设置过滤条件**
- 根据业务需求过滤特定类型的案例
- 排除测试数据和异常数据

**💡 提示 3：关注关键指标**
- 平均处理时间
- 案例完成率
- 资源利用率
- 成本效益比

## 可视化报表

### 仪表板功能

ProMax 提供丰富的可视化组件：

#### 📊 流程图表
- **DFG 流程图**：直接跟随图，显示活动间的跟随关系和频率（类似 Celonis 风格）
- **BPMN 流程图**：标准业务流程建模图，适合流程文档化
- **社会网络图**：展示资源间的协作关系
- **决策点图**：突出显示流程中的关键决策点

#### 📈 性能图表
- **时间线图**：显示案例的时间分布
- **箱线图**：展示处理时间的分布情况
- **热力图**：识别性能瓶颈位置

#### 📋 统计报表
- **汇总统计**：关键指标的统计摘要
- **趋势分析**：时间序列趋势图
- **对比分析**：不同时期或条件的对比

### 报表导出

支持多种格式的报表导出：

- **PDF报告**：完整的分析报告，适合打印和分享
- **Excel表格**：详细数据，便于进一步分析
- **图片文件**：高质量的图表，用于演示文稿
- **PowerBI/Tableau**：与其他BI工具集成

## 常见问题

### Q1：支持哪些数据源？
**A**：ProMax 支持多种数据源：
- 文件上传：CSV, Excel, JSON
- 数据库连接：MySQL, PostgreSQL, SQL Server
- API接口：RESTful API, GraphQL
- 企业系统：SAP, Oracle, Salesforce

### Q2：数据安全如何保障？
**A**：我们采用多层安全措施：
- 数据传输加密（HTTPS/TLS）
- 数据存储加密
- 访问权限控制
- 审计日志记录
- 定期安全备份

### Q3：分析结果的准确性如何？
**A**：分析准确性取决于：
- 数据质量：完整、准确、及时的数据
- 数据量：足够的样本数量
- 配置参数：合适的分析参数设置
- 业务理解：正确的业务逻辑映射

### Q4：如何处理大数据量？
**A**：对于大数据量场景：
- 支持分批处理
- 提供采样分析选项
- 优化算法性能
- 云端计算资源扩展

### Q5：可以与现有系统集成吗？
**A**：是的，ProMax 提供：
- RESTful API接口
- Webhook通知机制
- 标准数据格式支持
- 企业级SSO集成

## 技术支持

### 📞 联系方式
- **技术支持热线**：400-XXX-XXXX
- **邮箱支持**：<EMAIL>
- **在线客服**：平台内置聊天功能
- **工作时间**：周一至周五 9:00-18:00

### 📚 学习资源
- **视频教程**：[在线视频库](https://learn.promined.com)
- **最佳实践**：[案例研究库](https://cases.promined.com)
- **用户社区**：[论坛讨论](https://community.promined.com)
- **API文档**：[开发者文档](https://docs.promined.com)

### 🎓 培训服务
- **在线培训**：定期举办在线培训课程
- **现场培训**：企业定制化培训服务
- **认证考试**：ProMax 专业认证
- **咨询服务**：流程优化咨询服务

---

**感谢您选择 ProMax！**

如果您在使用过程中遇到任何问题，请随时联系我们的技术支持团队。我们致力于帮助您充分发挥流程挖掘的价值，实现业务流程的持续优化。

*最后更新：2024年6月*
