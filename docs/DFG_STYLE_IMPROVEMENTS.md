# DFG 图表样式改进说明

## 改进概述

本次更新对 ProMax 项目中的 DFG（Directly-Follows Graph）图表进行了全面的样式改进，使其更符合流程挖掘的专业标准，并提供更好的数据可视化效果。

## 主要改进内容

### 1. 数据驱动的动态样式映射

#### 科学的颜色映射算法
- **百分位数算法**：基于实际数据分布计算 P25、P50、P75、P90 百分位数
- **动态调整**：根据数据范围自动调整颜色深浅和大小
- **避免固定阈值**：不再使用硬编码的频率阈值，适应不同数据集

#### 节点样式映射
- **颜色方案**：采用绿色系表示活动节点
  - P90+ (高频): 深绿色 `#2E7D32`
  - P75-P90: 中深绿色 `#388E3C`
  - P50-P75: 标准绿色 `#4CAF50`
  - P25-P50: 浅绿色 `#81C784`
  - <P25 (低频): 很浅绿色 `#C8E6C9`

- **大小映射**：节点大小根据频率在数据集中的相对位置动态调整
- **边框样式**：边框粗细和颜色与填充色协调

#### 连接线样式映射
- **颜色方案**：采用热力图颜色表示频率
  - P90+ (高频): 深红色 `#D32F2F`
  - P75-P90: 红色 `#F44336`
  - P50-P75: 橙色 `#FF9800`
  - P25-P50: 蓝色 `#2196F3`
  - <P25 (低频): 浅蓝色 `#90CAF9`

- **粗细映射**：线条粗细根据频率动态调整
- **箭头缩放**：箭头大小与线条粗细协调

### 2. 视觉效果增强

#### 阴影和深度效果
- **节点阴影**：添加柔和阴影增强立体感
- **连接线阴影**：轻微阴影提升视觉层次
- **圆角设计**：使用圆角矩形提升现代感

#### 字体和排版优化
- **专业字体**：使用 "Segoe UI" 等现代字体
- **文本颜色**：根据背景色自动调整文本颜色确保可读性
- **标签布局**：改进的文本布局和间距

#### 布局优化
- **智能路由**：连接线避开节点，减少重叠
- **跳跃曲线**：连接线交叉时使用跳跃效果
- **间距调整**：增加节点和层级间距，提升可读性

### 3. 交互体验改进

#### 缩放和导航
- **平滑缩放**：改进的鼠标滚轮缩放体验
- **智能适应**：自动适应屏幕大小
- **工具栏控制**：提供放大、缩小、重置、适应屏幕等功能

#### 信息展示
- **更新的说明文本**：反映新的样式特性
- **详细的图例**：解释颜色和大小的含义
- **数据分布信息**：在控制台显示百分位数信息

## 技术实现

### 核心算法

```typescript
// 数据分布计算
const calculateDataDistribution = (nodes: any[], edges: any[]) => {
  // 计算节点和边的频率分布
  // 返回百分位数信息
}

// 节点样式映射
const getNodeStyle = (frequency: number, percentiles: any) => {
  // 基于百分位数返回样式对象
}

// 连接线样式映射
const getLinkStyle = (frequency: number, percentiles: any) => {
  // 基于百分位数返回样式对象
}
```

### 文件修改

#### 主要修改文件
- `client/pages/analysis/[processId]/discover.vue`：主要的 DFG 实现
- `client/pages/test-dfg-styles.vue`：样式测试页面（新增）

#### 修改内容
1. **新增函数**：
   - `calculateDataDistribution()`: 数据分布计算
   - `getNodeStyle()`: 节点样式映射
   - `getLinkStyle()`: 连接线样式映射

2. **更新模板**：
   - 节点模板：增强的视觉效果和动态绑定
   - 连接线模板：改进的样式和路由
   - 布局配置：优化的间距和对齐

3. **样式改进**：
   - 阴影效果
   - 圆角设计
   - 字体优化
   - 颜色协调

## 使用方法

### 测试新样式
1. 启动开发服务器：`npm run dev`
2. 访问测试页面：`http://localhost:3000/test-dfg-styles`
3. 点击"生成测试数据"查看效果

### 在实际项目中使用
新的样式已集成到现有的流程发现页面中，无需额外配置即可使用。

## 兼容性

- **向后兼容**：保持现有 API 接口不变
- **数据格式**：支持现有的节点和边数据格式
- **浏览器支持**：支持所有现代浏览器

## 性能优化

- **高效算法**：百分位数计算复杂度为 O(n log n)
- **缓存机制**：样式计算结果可缓存
- **渲染优化**：使用 GoJS 的高效渲染引擎

## 未来扩展

1. **自定义主题**：支持用户自定义颜色方案
2. **动画效果**：添加节点和连接线的动画过渡
3. **交互增强**：更丰富的鼠标悬停和点击效果
4. **导出功能**：支持高质量图片导出

## 总结

本次改进显著提升了 DFG 图表的专业性和可读性，通过科学的数据映射算法和现代化的视觉设计，为用户提供了更好的流程挖掘体验。新的样式系统具有良好的扩展性和维护性，为未来的功能扩展奠定了基础。
