"""
配置设置
"""

import os
from typing import List, Dict, Any
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """应用配置"""
    
    # 服务配置
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False

    def __init__(self, **kwargs):
        # 优先使用 PYTHON_SERVICE_PORT 环境变量
        if 'PYTHON_SERVICE_PORT' in os.environ:
            kwargs['port'] = int(os.environ['PYTHON_SERVICE_PORT'])
        super().__init__(**kwargs)
    
    # CORS配置
    allowed_origins: str = "http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000,http://127.0.0.1:3001"
    
    # 数据库配置
    mysql_host: str = "localhost"
    mysql_port: int = 3306
    mysql_user: str = "root"
    mysql_password: str = ""
    mysql_database: str = "promined"
    
    # Redis配置
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_password: str = ""
    redis_db: int = 0
    redis_key_prefix: str = "promined:mining:"
    
    # 性能配置
    max_workers: int = 4
    max_memory_usage_percent: float = 80.0
    batch_size: int = 1000

    # GPU配置
    enable_gpu_acceleration: bool = True
    gpu_memory_limit_percent: float = 85.0
    gpu_memory_fraction: float = 0.8
    prefer_gpu_for_large_data: bool = True
    large_data_threshold: int = 5000
    gpu_fallback_enabled: bool = True
    force_gpu_type: str = "auto"  # auto, cuda, mps, cpu
    gpu_batch_size_multiplier: float = 2.0
    
    # 算法配置
    min_frequency: int = 2
    min_confidence: float = 0.1
    max_pattern_length: int = 5

    # GPU算法优化配置
    gpu_tensor_batch_size: int = 1000
    gpu_max_sequence_length: int = 100
    gpu_memory_cleanup_interval: int = 300  # 秒
    enable_gpu_memory_monitoring: bool = True
    gpu_performance_logging: bool = True
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/mining_service.log"
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )
        
    @property
    def mysql_url(self) -> str:
        """MySQL连接URL"""
        return f"mysql+pymysql://{self.mysql_user}:{self.mysql_password}@{self.mysql_host}:{self.mysql_port}/{self.mysql_database}"
    
    @property
    def redis_url(self) -> str:
        """Redis连接URL"""
        if self.redis_password:
            return f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}/{self.redis_db}"
        return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"

    def get_gpu_config(self) -> Dict[str, Any]:
        """获取GPU配置摘要"""
        return {
            'enabled': self.enable_gpu_acceleration,
            'memory_limit_percent': self.gpu_memory_limit_percent,
            'memory_fraction': self.gpu_memory_fraction,
            'prefer_for_large_data': self.prefer_gpu_for_large_data,
            'large_data_threshold': self.large_data_threshold,
            'fallback_enabled': self.gpu_fallback_enabled,
            'force_type': self.force_gpu_type,
            'batch_size_multiplier': self.gpu_batch_size_multiplier,
            'tensor_batch_size': self.gpu_tensor_batch_size,
            'max_sequence_length': self.gpu_max_sequence_length,
            'memory_cleanup_interval': self.gpu_memory_cleanup_interval,
            'memory_monitoring': self.enable_gpu_memory_monitoring,
            'performance_logging': self.gpu_performance_logging
        }

    def should_use_gpu(self, data_size: int = 0) -> bool:
        """判断是否应该使用GPU"""
        if not self.enable_gpu_acceleration:
            return False

        if self.prefer_gpu_for_large_data and data_size >= self.large_data_threshold:
            return True

        # 如果强制指定GPU类型
        if self.force_gpu_type in ['cuda', 'mps']:
            return True
        elif self.force_gpu_type == 'cpu':
            return False

        # 自动模式：根据数据大小决定
        return data_size >= self.large_data_threshold

    def get_optimal_batch_size(self, base_batch_size: int, use_gpu: bool = False) -> int:
        """获取最优批处理大小"""
        if use_gpu:
            return int(base_batch_size * self.gpu_batch_size_multiplier)
        return base_batch_size

    def validate_gpu_config(self) -> List[str]:
        """验证GPU配置"""
        issues = []

        if self.gpu_memory_limit_percent < 50 or self.gpu_memory_limit_percent > 95:
            issues.append(f"GPU内存限制应在50-95%之间，当前: {self.gpu_memory_limit_percent}%")

        if self.gpu_memory_fraction < 0.1 or self.gpu_memory_fraction > 1.0:
            issues.append(f"GPU内存分配比例应在0.1-1.0之间，当前: {self.gpu_memory_fraction}")

        if self.large_data_threshold < 100:
            issues.append(f"大数据阈值过小，建议至少100，当前: {self.large_data_threshold}")

        if self.force_gpu_type not in ['auto', 'cuda', 'mps', 'cpu']:
            issues.append(f"无效的GPU类型: {self.force_gpu_type}")

        if self.gpu_batch_size_multiplier < 1.0 or self.gpu_batch_size_multiplier > 10.0:
            issues.append(f"GPU批处理倍数应在1.0-10.0之间，当前: {self.gpu_batch_size_multiplier}")

        return issues
