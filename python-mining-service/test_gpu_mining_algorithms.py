#!/usr/bin/env python3
"""
GPU挖掘算法综合测试
测试GPU加速的流程挖掘算法性能和准确性
"""

import asyncio
import sys
import os
import time
import torch
from datetime import datetime, timedelta
from typing import List, Dict
import random

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.mining_models import (
    EventLog, SubprocessDiscoveryRequest, SubprocessDiscoveryOptions
)
from services.gpu_accelerated_mining import GPUAcceleratedMining
from utils.gpu_detector import GPUDetector

def generate_manufacturing_test_data(num_cases: int = 100) -> List[EventLog]:
    """生成制造业流程测试数据"""
    
    print(f"🏭 生成制造业流程测试数据 ({num_cases} 个案例)...")
    
    # 制造业流程活动
    activities = {
        'procurement': ['原材料采购申请', '供应商资质审核', '采购合同签署', '原材料入库检验', '入库确认'],
        'production': ['生产准备', '首件检验', '批量生产', '过程检验', '成品检验'],
        'quality': ['不合格品处理', '供应商整改通知', '重新检验', '质量问题分析', '改进措施制定'],
        'packaging': ['包装', '出库检验', '发货准备', '物流配送'],
        'maintenance': ['设备维护', '设备检验', '设备验收']
    }
    
    # 流程模式
    patterns = [
        activities['procurement'],
        activities['production'],
        activities['quality'][:3],  # 质量控制子流程
        activities['packaging'],
        activities['maintenance']
    ]
    
    events = []
    event_id = 1
    
    for case_num in range(1, num_cases + 1):
        case_id = f"case_{case_num:04d}"
        
        # 随机选择流程模式
        if case_num % 10 == 0:
            # 10%的案例包含质量问题
            selected_activities = (
                activities['procurement'] + 
                activities['production'][:3] + 
                activities['quality'] + 
                activities['production'][3:] + 
                activities['packaging']
            )
        elif case_num % 15 == 0:
            # 设备维护流程
            selected_activities = activities['maintenance'] + activities['production']
        else:
            # 正常流程
            selected_activities = (
                activities['procurement'] + 
                activities['production'] + 
                activities['packaging']
            )
        
        # 生成时间戳
        base_time = datetime(2024, 1, 1) + timedelta(days=case_num)
        current_time = base_time
        
        # 添加一些随机性
        if random.random() < 0.3:
            # 30%的案例跳过某些步骤
            selected_activities = selected_activities[::2]
        
        for activity in selected_activities:
            events.append(EventLog(
                id=event_id,
                case_id=case_id,
                activity=activity,
                timestamp=current_time,
                resource=f"worker_{random.randint(1, 10)}",
                cost=random.uniform(100, 1000)
            ))
            
            event_id += 1
            current_time += timedelta(minutes=random.randint(5, 60))
    
    print(f"✅ 生成完成: {len(events)} 个事件")
    return events

async def test_gpu_mining_performance():
    """测试GPU挖掘性能"""
    
    print("🚀 开始GPU挖掘算法性能测试")
    print("=" * 60)
    
    # 1. 检测GPU环境
    print("🔍 检测GPU环境...")
    gpu_detector = GPUDetector()
    gpu_info = gpu_detector.get_device_info()

    print(f"   GPU类型: {gpu_info['gpu_type']}")
    print(f"   设备名称: {gpu_info['device_name']}")
    print(f"   可用: {gpu_info['gpu_available']}")

    if gpu_info['gpu_type'] == 'cuda':
        print(f"   GPU内存: {gpu_info['memory_total_gb']:.1f}GB")
        print(f"   CUDA版本: {gpu_info['cuda_version']}")

    # 2. 初始化GPU挖掘服务
    device = gpu_detector.get_optimal_device()
    gpu_mining = GPUAcceleratedMining(device)
    
    # 3. 测试不同数据规模
    test_sizes = [50, 100, 200, 500]
    
    for size in test_sizes:
        print(f"\n📊 测试数据规模: {size} 个案例")
        print("-" * 40)
        
        # 生成测试数据
        test_events = generate_manufacturing_test_data(size)
        
        # 创建请求
        request = SubprocessDiscoveryRequest(
            process_id=size,  # 使用整数作为process_id
            event_logs=test_events,
            options=SubprocessDiscoveryOptions(
                min_frequency=2,
                min_confidence=0.1,
                max_pattern_length=5
            )
        )
        
        # 执行GPU挖掘
        start_time = time.time()
        
        try:
            response = await gpu_mining.discover_subprocesses_gpu(request)
            
            processing_time = time.time() - start_time
            
            print(f"   ✅ 处理完成")
            print(f"   ⏱️  处理时间: {processing_time:.2f}秒")
            print(f"   📈 处理速度: {len(test_events)/processing_time:.0f}条/秒")
            print(f"   🔍 发现子流程: {len(response.subprocesses)}个")
            print(f"   📊 DFG节点: {len(response.hierarchical_dfg.nodes)}个")
            print(f"   🔗 DFG边: {len(response.hierarchical_dfg.edges)}个")
            
            # 显示发现的子流程
            if response.subprocesses:
                print(f"   🎯 主要子流程:")
                for i, subprocess in enumerate(response.subprocesses[:3]):
                    print(f"      {i+1}. {subprocess.name}")
                    print(f"         频率: {subprocess.frequency}, 置信度: {subprocess.confidence:.2f}")
                    print(f"         活动: {' -> '.join(subprocess.activities[:3])}{'...' if len(subprocess.activities) > 3 else ''}")
            
        except Exception as e:
            print(f"   ❌ 处理失败: {e}")
            import traceback
            traceback.print_exc()

async def test_gpu_memory_management():
    """测试GPU内存管理"""
    
    print("\n💾 测试GPU内存管理")
    print("=" * 60)
    
    gpu_detector = GPUDetector()
    gpu_info = gpu_detector.get_device_info()
    device = gpu_detector.get_optimal_device()

    gpu_mining = GPUAcceleratedMining(device)

    # 生成大量数据测试内存管理
    large_dataset = generate_manufacturing_test_data(1000)

    print(f"📊 大数据集测试: {len(large_dataset)} 个事件")

    # 监控内存使用
    if gpu_info['gpu_type'] == 'cuda':
        initial_memory = torch.cuda.memory_allocated() / 1024**2
        print(f"   初始GPU内存: {initial_memory:.1f}MB")
    
    request = SubprocessDiscoveryRequest(
        process_id=9999,  # 使用整数作为process_id
        event_logs=large_dataset,
        options=SubprocessDiscoveryOptions(
            min_frequency=5,
            min_confidence=0.2,
            max_pattern_length=4
        )
    )
    
    try:
        start_time = time.time()
        response = await gpu_mining.discover_subprocesses_gpu(request)
        processing_time = time.time() - start_time
        
        if gpu_info['gpu_type'] == 'cuda':
            final_memory = torch.cuda.memory_allocated() / 1024**2
            print(f"   最终GPU内存: {final_memory:.1f}MB")
            print(f"   内存增长: {final_memory - initial_memory:.1f}MB")
        
        print(f"   ✅ 大数据集处理成功")
        print(f"   ⏱️  处理时间: {processing_time:.2f}秒")
        print(f"   📈 处理速度: {len(large_dataset)/processing_time:.0f}条/秒")
        
    except Exception as e:
        print(f"   ❌ 大数据集处理失败: {e}")

async def test_algorithm_accuracy():
    """测试算法准确性"""
    
    print("\n🎯 测试算法准确性")
    print("=" * 60)
    
    # 创建已知模式的测试数据
    known_patterns = [
        ['原材料采购申请', '供应商资质审核', '采购合同签署'],
        ['生产准备', '首件检验', '批量生产'],
        ['不合格品处理', '供应商整改通知', '重新检验']
    ]
    
    # 生成包含已知模式的数据
    events = []
    event_id = 1
    
    for case_num in range(1, 21):  # 20个案例
        case_id = f"accuracy_case_{case_num}"
        base_time = datetime(2024, 1, 1) + timedelta(hours=case_num)
        current_time = base_time
        
        # 每个案例包含一个已知模式
        pattern = known_patterns[case_num % len(known_patterns)]
        
        for activity in pattern:
            events.append(EventLog(
                id=event_id,
                case_id=case_id,
                activity=activity,
                timestamp=current_time,
                resource=f"worker_{case_num % 5 + 1}",
                cost=100.0
            ))
            event_id += 1
            current_time += timedelta(minutes=10)
    
    print(f"📊 准确性测试数据: {len(events)} 个事件, 包含 {len(known_patterns)} 个已知模式")

    gpu_detector = GPUDetector()
    device = gpu_detector.get_optimal_device()

    gpu_mining = GPUAcceleratedMining(device)
    
    request = SubprocessDiscoveryRequest(
        process_id=8888,  # 使用整数作为process_id
        event_logs=events,
        options=SubprocessDiscoveryOptions(
            min_frequency=2,
            min_confidence=0.1,
            max_pattern_length=5
        )
    )
    
    try:
        response = await gpu_mining.discover_subprocesses_gpu(request)
        
        print(f"   🔍 发现的子流程数量: {len(response.subprocesses)}")
        
        # 检查是否发现了已知模式
        found_patterns = 0
        for subprocess in response.subprocesses:
            subprocess_activities = subprocess.activities
            for known_pattern in known_patterns:
                if all(activity in subprocess_activities for activity in known_pattern):
                    found_patterns += 1
                    print(f"   ✅ 发现已知模式: {' -> '.join(known_pattern)}")
                    break
        
        accuracy = found_patterns / len(known_patterns)
        print(f"   📊 模式识别准确率: {accuracy:.1%}")
        
        if accuracy >= 0.8:
            print("   ✅ 算法准确性测试通过")
        else:
            print("   ⚠️  算法准确性需要改进")
            
    except Exception as e:
        print(f"   ❌ 准确性测试失败: {e}")

async def main():
    """主测试函数"""
    
    print("🧪 GPU挖掘算法综合测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 1. 性能测试
        await test_gpu_mining_performance()
        
        # 2. 内存管理测试
        await test_gpu_memory_management()
        
        # 3. 准确性测试
        await test_algorithm_accuracy()
        
        print("\n🎉 所有测试完成!")
        print("✅ GPU挖掘算法测试成功")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
