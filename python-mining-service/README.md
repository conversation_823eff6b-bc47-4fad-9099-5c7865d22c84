# ProMined Python Mining Service

🚀 高性能子流程挖掘微服务，专为苹果M芯片GPU加速优化

## ✨ 特性

- **🔥 GPU加速**: 支持苹果M芯片MPS (Metal Performance Shaders)
- **⚡ 高性能**: 针对大规模数据优化的算法实现
- **🧠 智能算法**: 预定义业务模式 + 动态模式发现
- **📊 实时监控**: 内存、CPU、GPU使用情况监控
- **🔄 异步处理**: 支持并发请求和后台任务
- **🛡️ 内存保护**: 智能内存管理和垃圾回收

## 🏗️ 技术栈

- **Python 3.12+**: 最新Python版本
- **FastAPI**: 现代化Web框架
- **PyTorch**: 深度学习框架，支持MPS
- **Pandas/NumPy**: 数据处理
- **PM4Py**: 专业流程挖掘库
- **Conda**: 环境管理

## 📦 快速开始

### 方式1: 自动安装 (推荐)

```bash
cd python-mining-service

# 自动设置conda环境
chmod +x setup_env.sh
./setup_env.sh
```

### 方式2: 手动conda安装

```bash
# 如果自动安装失败，使用手动安装
chmod +x install_manual.sh
./install_manual.sh
```

### 方式3: 纯pip安装

```bash
# 适用于已有Python 3.12环境
chmod +x install_pip.sh
./install_pip.sh
```

### 方式4: 使用简化环境

```bash
# 使用简化的conda环境配置
conda env create -f environment-simple.yml
conda activate promined-mining
```

### 3. 配置服务

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置
vim .env
```

### 4. 启动服务

```bash
# 方式1: 使用启动脚本 (推荐)
python start_service.py

# 方式2: 手动启动
conda activate promined-mining
python app/main.py

# 方式3: 开发模式
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 5. 验证服务

```bash
# 健康检查
curl http://localhost:8000/health

# 系统信息
curl http://localhost:8000/system-info

# API文档
open http://localhost:8000/docs
```

## 🎯 API接口

### 主要端点

- `GET /` - 服务状态
- `GET /health` - 健康检查
- `GET /system-info` - 系统信息
- `POST /subprocess-discovery` - 子流程发现
- `POST /force-gc` - 强制垃圾回收

### 子流程发现示例

```python
import requests

# 请求数据
data = {
    "process_id": 1,
    "event_logs": [
        {
            "id": 1,
            "case_id": "case_1",
            "activity": "原材料采购申请",
            "timestamp": "2024-01-01T10:00:00",
            "resource": "采购员A"
        },
        # ... 更多事件日志
    ],
    "options": {
        "min_frequency": 2,
        "min_confidence": 0.1,
        "use_gpu_acceleration": True
    }
}

# 发送请求
response = requests.post(
    "http://localhost:8000/subprocess-discovery",
    json=data
)

result = response.json()
print(f"发现 {len(result['subprocesses'])} 个子流程")
```

## 🔧 配置说明

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `HOST` | 0.0.0.0 | 服务监听地址 |
| `PORT` | 8000 | 服务端口 |
| `DEBUG` | false | 调试模式 |
| `MAX_WORKERS` | 4 | 最大工作线程数 |
| `ENABLE_GPU_ACCELERATION` | true | 启用GPU加速 |
| `MIN_FREQUENCY` | 2 | 最小模式频率 |
| `MIN_CONFIDENCE` | 0.1 | 最小置信度 |

### 性能调优

```bash
# 内存限制
MAX_MEMORY_USAGE_PERCENT=80.0

# 批处理大小
BATCH_SIZE=1000

# GPU内存分配
GPU_MEMORY_FRACTION=0.8
```

## 🚀 性能优化

### GPU加速

在苹果M芯片上自动启用MPS加速：

```python
# 检查GPU可用性
import torch
if torch.backends.mps.is_available():
    device = torch.device("mps")
    print("✅ 使用GPU加速")
else:
    device = torch.device("cpu")
    print("💻 使用CPU模式")
```

### 内存优化

- 自动内存监控和清理
- 智能批处理大小调整
- 垃圾回收优化

### 算法优化

- 预定义业务模式优先匹配
- 滑动窗口模式发现
- 并行处理支持

## 📊 监控和日志

### 性能监控

服务自动监控：
- 内存使用率
- CPU使用率
- GPU状态
- 处理性能

### 日志配置

```python
# 日志级别
LOG_LEVEL=INFO

# 日志文件
LOG_FILE=logs/mining_service.log
```

## 🔗 与NestJS集成

在NestJS服务中调用Python服务：

```typescript
// subprocess-discovery.service.ts
async discoverSubprocesses(processId: number) {
  const response = await fetch('http://localhost:8000/subprocess-discovery', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      process_id: processId,
      event_logs: eventLogs,
      options: { use_gpu_acceleration: true }
    })
  });
  
  return response.json();
}
```

## 🛠️ 开发指南

### 项目结构

```
python-mining-service/
├── app/
│   └── main.py                 # FastAPI应用
├── services/
│   ├── gpu_accelerated_mining.py    # GPU加速服务
│   └── subprocess_mining_service.py # CPU优化服务
├── models/
│   └── mining_models.py        # 数据模型
├── config/
│   └── settings.py            # 配置管理
├── utils/
│   └── performance_monitor.py  # 性能监控
├── tests/                     # 测试文件
├── logs/                      # 日志目录
├── environment.yml            # Conda环境
└── README.md
```

### 添加新算法

1. 在 `services/` 目录创建新服务
2. 在 `models/` 中定义数据模型
3. 在 `app/main.py` 中添加API端点

### 测试

```bash
# 运行测试
pytest tests/

# 性能测试
python tests/performance_test.py
```

## 🐛 故障排除

### 常见问题

1. **GPU不可用**
   ```bash
   # 检查MPS支持
   python -c "import torch; print(torch.backends.mps.is_available())"
   ```

2. **内存不足**
   ```bash
   # 调整批处理大小
   export BATCH_SIZE=500
   ```

3. **端口占用**
   ```bash
   # 更改端口
   export PORT=8001
   ```

## 📈 性能基准

在苹果M2 Pro上的测试结果：

| 数据量 | CPU模式 | GPU模式 | 加速比 |
|--------|---------|---------|--------|
| 1K事件 | 2.3s | 1.1s | 2.1x |
| 10K事件 | 23.5s | 8.7s | 2.7x |
| 50K事件 | 125s | 35s | 3.6x |

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！
