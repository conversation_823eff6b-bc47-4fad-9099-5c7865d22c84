#!/bin/bash

# ProMined Python Mining Service 环境设置脚本
# 支持苹果M芯片GPU加速的高性能子流程挖掘服务

echo "🚀 开始设置 ProMined Python Mining Service 环境..."

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    echo "❌ 错误: conda 未安装。请先安装 Miniconda 或 Anaconda。"
    echo "📥 下载地址: https://docs.conda.io/en/latest/miniconda.html"
    exit 1
fi

# 检查是否为苹果M芯片
if [[ $(uname -m) == "arm64" ]]; then
    echo "✅ 检测到苹果M芯片，将启用GPU加速优化"
    export PYTORCH_ENABLE_MPS_FALLBACK=1
else
    echo "ℹ️  检测到非苹果M芯片，将使用CPU优化"
fi

# 创建conda环境
echo "📦 创建conda虚拟环境 'promined-mining'..."

# 尝试使用简化版环境配置
if conda env create -f environment-simple.yml; then
    echo "✅ 使用简化环境配置创建成功"
else
    echo "⚠️  简化环境配置失败，尝试原版配置..."
    if conda env create -f environment.yml; then
        echo "✅ 使用原版环境配置创建成功"
    else
        echo "❌ 环境创建失败，尝试手动创建基础环境..."
        conda create -n promined-mining python=3.12 -y
        conda activate promined-mining

        # 手动安装关键包
        echo "📦 手动安装关键包..."
        conda install -c conda-forge numpy pandas scipy scikit-learn networkx psutil joblib -y
        conda install -c pytorch pytorch torchvision torchaudio -y

        # 配置pip使用国内镜像源
        pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
        pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

        # 使用pip安装其他包
        pip install fastapi uvicorn pydantic pydantic-settings
        pip install pymysql sqlalchemy redis
        pip install pm4py loguru httpx aiohttp
        pip install pytest black isort flake8
        pip install memory-profiler orjson msgpack
        pip install python-igraph
    fi
fi

# 激活环境
echo "🔄 激活虚拟环境..."
conda activate promined-mining

# 验证Python版本
echo "🐍 验证Python版本..."
python --version

# 验证PyTorch GPU支持
echo "🔍 检查PyTorch GPU支持..."
python -c "
import torch
print(f'PyTorch版本: {torch.__version__}')
if torch.backends.mps.is_available():
    print('✅ 苹果M芯片GPU (MPS) 可用')
    print(f'MPS设备数量: {torch.mps.device_count() if hasattr(torch.mps, \"device_count\") else \"N/A\"}')
else:
    print('⚠️  MPS不可用，将使用CPU')
print(f'可用设备: {torch.device(\"mps\" if torch.backends.mps.is_available() else \"cpu\")}')
"

# 创建项目目录结构
echo "📁 创建项目目录结构..."
mkdir -p {app,tests,config,logs,data,models,utils}

echo "✅ 环境设置完成！"
echo ""
echo "🎯 下一步操作:"
echo "1. 激活环境: conda activate promined-mining"
echo "2. 启动服务: python app/main.py"
echo "3. 访问API文档: http://localhost:8000/docs"
echo ""
echo "💡 提示:"
echo "- 使用 'conda deactivate' 退出环境"
echo "- 使用 'conda env remove -n promined-mining' 删除环境"
