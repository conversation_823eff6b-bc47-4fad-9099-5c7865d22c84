#!/usr/bin/env python3
"""
测试层次化DFG构建功能
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.mining_models import EventLog, SubprocessPattern
from services.subprocess_mining_service import SubprocessMiningService
from config.settings import Settings

async def test_hierarchical_dfg():
    """测试层次化DFG构建"""
    
    # 创建测试数据
    test_events = [
        # Case 1: A -> B -> C -> D -> E
        EventLog(id=1, case_id="case1", activity="A", timestamp="2024-01-01T10:00:00", resource="user1"),
        EventLog(id=2, case_id="case1", activity="B", timestamp="2024-01-01T10:05:00", resource="user1"),
        EventLog(id=3, case_id="case1", activity="C", timestamp="2024-01-01T10:10:00", resource="user1"),
        EventLog(id=4, case_id="case1", activity="D", timestamp="2024-01-01T10:15:00", resource="user1"),
        EventLog(id=5, case_id="case1", activity="E", timestamp="2024-01-01T10:20:00", resource="user1"),

        # Case 2: A -> B -> C -> D -> E (重复模式)
        EventLog(id=6, case_id="case2", activity="A", timestamp="2024-01-01T11:00:00", resource="user2"),
        EventLog(id=7, case_id="case2", activity="B", timestamp="2024-01-01T11:05:00", resource="user2"),
        EventLog(id=8, case_id="case2", activity="C", timestamp="2024-01-01T11:10:00", resource="user2"),
        EventLog(id=9, case_id="case2", activity="D", timestamp="2024-01-01T11:15:00", resource="user2"),
        EventLog(id=10, case_id="case2", activity="E", timestamp="2024-01-01T11:20:00", resource="user2"),

        # Case 3: A -> X -> Y -> E (不同路径)
        EventLog(id=11, case_id="case3", activity="A", timestamp="2024-01-01T12:00:00", resource="user3"),
        EventLog(id=12, case_id="case3", activity="X", timestamp="2024-01-01T12:05:00", resource="user3"),
        EventLog(id=13, case_id="case3", activity="Y", timestamp="2024-01-01T12:10:00", resource="user3"),
        EventLog(id=14, case_id="case3", activity="E", timestamp="2024-01-01T12:15:00", resource="user3"),
    ]
    
    # 创建子流程模式
    test_patterns = [
        SubprocessPattern(
            id="subprocess_1",
            name="核心处理流程",
            activities=["B", "C", "D"],
            frequency=2,
            avg_duration=900000,  # 15分钟
            cases=["case1", "case2"],
            pattern_type="sequential",
            confidence=1.0
        )
    ]
    
    print("🧪 开始测试层次化DFG构建...")
    print(f"📊 测试数据: {len(test_events)} 个事件, {len(test_patterns)} 个子流程模式")
    
    # 创建服务实例
    settings = Settings()
    service = SubprocessMiningService(settings)
    
    # 按案例分组
    case_groups = await service._group_by_case_async(test_events)
    print(f"📦 案例分组: {len(case_groups)} 个案例")
    for case_id, events in case_groups.items():
        activities = [e.activity for e in events]
        print(f"  - {case_id}: {' -> '.join(activities)}")
    
    # 构建层次化DFG
    hierarchical_dfg = await service._build_hierarchical_dfg_async(case_groups, test_patterns)
    
    print("\n🏗️ 层次化DFG构建结果:")
    print(f"📍 节点数: {len(hierarchical_dfg.nodes)}")
    for node in hierarchical_dfg.nodes:
        print(f"  - {node.id} ({node.node_type}): {node.label} [频率: {node.frequency}]")
    
    print(f"\n🔗 边数: {len(hierarchical_dfg.edges)}")
    for edge in hierarchical_dfg.edges:
        print(f"  - {edge.source} -> {edge.target} [频率: {edge.frequency}]")
    
    # 测试序列压缩功能
    print("\n🔄 测试序列压缩:")
    test_sequence = ["A", "B", "C", "D", "E"]
    compressed = service._compress_sequence_with_subprocesses(test_sequence, test_patterns)
    print(f"  原始序列: {' -> '.join(test_sequence)}")
    print(f"  压缩序列: {' -> '.join(compressed)}")
    
    # 验证结果
    print("\n✅ 验证结果:")
    
    # 检查是否有子流程节点
    subprocess_nodes = [n for n in hierarchical_dfg.nodes if n.node_type == "subprocess"]
    activity_nodes = [n for n in hierarchical_dfg.nodes if n.node_type == "activity"]
    
    print(f"  - 子流程节点: {len(subprocess_nodes)} 个")
    print(f"  - 活动节点: {len(activity_nodes)} 个")
    
    # 检查子流程节点是否正确
    if len(subprocess_nodes) == 1:
        subprocess_node = subprocess_nodes[0]
        if subprocess_node.id == "subprocess_1" and subprocess_node.frequency == 2:
            print("  ✅ 子流程节点创建正确")
        else:
            print("  ❌ 子流程节点信息不正确")
    else:
        print("  ❌ 子流程节点数量不正确")
    
    # 检查活动节点（应该只包含不在子流程中的活动）
    expected_activities = {"A", "E", "X", "Y"}  # B, C, D 应该被子流程替换
    actual_activities = {n.id for n in activity_nodes}
    
    if actual_activities == expected_activities:
        print("  ✅ 活动节点过滤正确")
    else:
        print(f"  ❌ 活动节点不正确，期望: {expected_activities}, 实际: {actual_activities}")
    
    # 检查边的连接
    edge_map = {(e.source, e.target): e.frequency for e in hierarchical_dfg.edges}
    expected_edges = {
        ("A", "subprocess_1"): 2,  # A -> 子流程
        ("subprocess_1", "E"): 2,  # 子流程 -> E
        ("A", "X"): 1,             # A -> X
        ("X", "Y"): 1,             # X -> Y
        ("Y", "E"): 1              # Y -> E
    }
    
    print("  边连接验证:")
    all_correct = True
    for (source, target), expected_freq in expected_edges.items():
        if (source, target) in edge_map:
            actual_freq = edge_map[(source, target)]
            if actual_freq == expected_freq:
                print(f"    ✅ {source} -> {target}: {actual_freq}")
            else:
                print(f"    ❌ {source} -> {target}: 期望 {expected_freq}, 实际 {actual_freq}")
                all_correct = False
        else:
            print(f"    ❌ 缺失边: {source} -> {target}")
            all_correct = False
    
    if all_correct:
        print("  ✅ 所有边连接正确")
    else:
        print("  ❌ 边连接存在问题")
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    asyncio.run(test_hierarchical_dfg())
