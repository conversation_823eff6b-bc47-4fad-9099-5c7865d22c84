"""
ProMined Python Mining Service
高性能子流程挖掘微服务 - 支持苹果M芯片GPU加速
"""

import os
import sys
from contextlib import asynccontextmanager
from typing import Dict, Any
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
import psutil

from config.settings import Settings
from services.subprocess_mining_service import SubprocessMiningService
from services.gpu_accelerated_mining import GPUAcceleratedMining
from models.mining_models import (
    SubprocessDiscoveryRequest,
    SubprocessDiscoveryResponse,
    HealthResponse,
    SystemInfoResponse
)
from utils.performance_monitor import PerformanceMonitor
from utils.gpu_detector import gpu_detector

# 配置日志
logger.remove()
logger.add(
    sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level="INFO"
)
logger.add(
    "logs/mining_service.log",
    rotation="100 MB",
    retention="7 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    level="DEBUG"
)

# 全局变量
settings = Settings()
performance_monitor = PerformanceMonitor()
mining_service = None
gpu_mining = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global mining_service, gpu_mining
    
    logger.info("🚀 启动 ProMined Python Mining Service")

    # 使用GPU检测器获取最优设备
    device = gpu_detector.get_optimal_device(prefer_gpu=settings.enable_gpu_acceleration)
    gpu_info = gpu_detector.get_device_info()

    logger.info("🔍 GPU环境检测结果:")
    logger.info(f"   GPU可用: {gpu_info['gpu_available']}")
    logger.info(f"   GPU类型: {gpu_info['gpu_type']}")
    logger.info(f"   设备名称: {gpu_info['device_name']}")
    logger.info(f"   内存总量: {gpu_info['memory_total_gb']}GB")
    logger.info(f"   选择设备: {device}")

    # 检查GPU要求
    gpu_ok, gpu_issues = gpu_detector.check_gpu_requirements(min_memory_gb=2.0)
    if not gpu_ok:
        for issue in gpu_issues:
            logger.warning(f"⚠️  {issue}")

    # 初始化服务
    mining_service = SubprocessMiningService(settings)
    gpu_config = settings.get_gpu_config()
    gpu_mining = GPUAcceleratedMining(device=device, config=gpu_config)
    
    # 启动性能监控（包含GPU内存监控）
    performance_monitor = PerformanceMonitor(gpu_memory_limit_percent=settings.gpu_memory_limit_percent)
    performance_monitor.start_monitoring()

    # 验证配置
    config_issues = settings.validate_gpu_config()
    if config_issues:
        for issue in config_issues:
            logger.warning(f"⚠️  配置问题: {issue}")

    logger.info(f"🎯 服务启动完成，监听端口: {settings.port}")
    logger.info(f"💾 内存使用: {psutil.virtual_memory().percent:.1f}%")
    logger.info(f"🔧 设备: {device}")
    logger.info(f"⚙️  GPU配置: {gpu_config}")
    
    yield
    
    # 清理资源
    logger.info("🛑 正在关闭服务...")
    performance_monitor.stop_monitoring()
    logger.info("✅ 服务已关闭")


# 创建FastAPI应用
app = FastAPI(
    title="ProMined Python Mining Service",
    description="高性能子流程挖掘微服务 - 支持苹果M芯片GPU加速",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins.split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/", response_model=Dict[str, str])
async def root():
    """根路径"""
    gpu_info = gpu_detector.get_device_info()
    return {
        "service": "ProMined Python Mining Service",
        "version": "1.0.0",
        "status": "running",
        "gpu_available": str(gpu_info['gpu_available']),
        "gpu_type": gpu_info['gpu_type'],
        "device": str(gpu_detector.get_optimal_device()),
        "gpu_acceleration": str(settings.enable_gpu_acceleration)
    }


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查"""
    memory = psutil.virtual_memory()
    cpu_percent = psutil.cpu_percent(interval=1)
    
    return HealthResponse(
        status="healthy",
        timestamp=performance_monitor.get_current_time(),
        memory_usage_percent=memory.percent,
        cpu_usage_percent=cpu_percent,
        gpu_available=torch.backends.mps.is_available(),
        device=str(torch.device("mps" if torch.backends.mps.is_available() else "cpu"))
    )


@app.get("/system-info", response_model=SystemInfoResponse)
async def get_system_info():
    """获取系统信息"""
    memory = psutil.virtual_memory()
    gpu_info = gpu_detector.get_device_info()

    return SystemInfoResponse(
        python_version=sys.version,
        torch_version=torch.__version__,
        device=str(gpu_detector.get_optimal_device()),
        gpu_available=gpu_info['gpu_available'],
        cpu_count=psutil.cpu_count(),
        memory_total_gb=round(memory.total / (1024**3), 2),
        memory_available_gb=round(memory.available / (1024**3), 2),
        platform=sys.platform
    )


@app.get("/gpu-status")
async def get_gpu_status():
    """获取GPU状态详情"""
    try:
        gpu_info = gpu_detector.get_device_info()
        memory_info = gpu_detector.get_memory_info()
        gpu_memory_status = performance_monitor.get_gpu_memory_status()
        gpu_recommendations = performance_monitor.get_gpu_recommendations()

        return {
            "gpu_info": gpu_info,
            "memory_info": memory_info,
            "memory_status": gpu_memory_status,
            "recommendations": gpu_recommendations,
            "config": settings.get_gpu_config()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取GPU状态失败: {str(e)}")


@app.post("/gpu-memory-cleanup")
async def cleanup_gpu_memory():
    """清理GPU内存"""
    try:
        result = performance_monitor.force_gpu_memory_cleanup()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"GPU内存清理失败: {str(e)}")


@app.post("/gpu-config")
async def update_gpu_config(
    memory_limit_percent: float = None,
    enable_acceleration: bool = None
):
    """更新GPU配置"""
    try:
        updates = {}

        if memory_limit_percent is not None:
            if 50.0 <= memory_limit_percent <= 95.0:
                performance_monitor.set_gpu_memory_limit(memory_limit_percent)
                updates['memory_limit_percent'] = memory_limit_percent
            else:
                raise HTTPException(status_code=400, detail="内存限制应在50-95%之间")

        if enable_acceleration is not None:
            settings.enable_gpu_acceleration = enable_acceleration
            updates['enable_acceleration'] = enable_acceleration

        return {
            "success": True,
            "updates": updates,
            "current_config": settings.get_gpu_config()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新GPU配置失败: {str(e)}")


@app.post("/subprocess-discovery", response_model=SubprocessDiscoveryResponse)
async def discover_subprocesses(
    request: SubprocessDiscoveryRequest,
    background_tasks: BackgroundTasks
):
    """
    高性能子流程发现
    支持GPU加速的大规模数据处理
    """
    try:
        logger.info(f"🔍 开始子流程发现，进程ID: {request.process_id}")
        logger.info(f"📊 数据量: {len(request.event_logs)} 条事件日志")
        
        # 记录开始时间和内存使用
        start_time = performance_monitor.get_current_time()
        start_memory = psutil.virtual_memory().percent
        
        # 根据配置和数据量决定使用GPU还是CPU
        data_size = len(request.event_logs)
        should_use_gpu = settings.should_use_gpu(data_size) and gpu_detector.gpu_info.is_available

        if should_use_gpu:
            logger.info(f"🚀 使用GPU加速处理数据 (数据量: {data_size})")
            logger.info(f"   GPU类型: {gpu_detector.gpu_info.gpu_type.value}")
            logger.info(f"   设备: {gpu_detector.gpu_info.device_name}")
            result = await gpu_mining.discover_subprocesses_gpu(request)
        else:
            logger.info(f"💻 使用CPU优化算法处理 (数据量: {data_size})")
            if not settings.enable_gpu_acceleration:
                logger.info("   原因: GPU加速已禁用")
            elif not gpu_detector.gpu_info.is_available:
                logger.info("   原因: GPU不可用")
            else:
                logger.info(f"   原因: 数据量({data_size})小于阈值({settings.large_data_threshold})")
            result = await mining_service.discover_subprocesses(request)
        
        # 记录性能指标
        end_time = performance_monitor.get_current_time()
        end_memory = psutil.virtual_memory().percent
        processing_time = (end_time - start_time).total_seconds()
        
        logger.info(f"✅ 子流程发现完成")
        logger.info(f"⏱️  处理时间: {processing_time:.2f}秒")
        logger.info(f"💾 内存变化: {start_memory:.1f}% -> {end_memory:.1f}%")
        logger.info(f"🎯 发现子流程数量: {len(result.subprocesses)}")
        
        # 添加性能指标到响应
        gpu_memory_info = gpu_detector.get_memory_info() if gpu_detector.gpu_info.is_available else {}

        result.performance_metrics = {
            "processing_time_seconds": processing_time,
            "memory_usage_start": start_memory,
            "memory_usage_end": end_memory,
            "device_used": str(gpu_detector.get_optimal_device() if should_use_gpu else torch.device("cpu")),
            "gpu_used": should_use_gpu,
            "gpu_type": gpu_detector.gpu_info.gpu_type.value if gpu_detector.gpu_info.is_available else "none",
            "gpu_memory_usage": gpu_memory_info.get('usage_percent', 0.0),
            "event_count": data_size,
            "subprocess_count": len(result.subprocesses),
            "data_processing_rate": data_size / processing_time if processing_time > 0 else 0
        }
        
        # 后台任务：清理内存
        background_tasks.add_task(performance_monitor.cleanup_memory)
        
        return result
        
    except Exception as e:
        logger.error(f"❌ 子流程发现失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"子流程发现失败: {str(e)}")


@app.post("/force-gc")
async def force_garbage_collection():
    """强制垃圾回收"""
    import gc
    collected = gc.collect()
    
    if torch.backends.mps.is_available():
        torch.mps.empty_cache()
    
    memory = psutil.virtual_memory()
    
    logger.info(f"🧹 强制垃圾回收完成，回收对象: {collected}")
    
    return {
        "collected_objects": collected,
        "memory_usage_percent": memory.percent,
        "memory_available_gb": round(memory.available / (1024**3), 2)
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug",
        access_log=True
    )
