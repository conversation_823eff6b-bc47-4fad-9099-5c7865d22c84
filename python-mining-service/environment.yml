name: promined-mining
channels:
  - conda-forge
  - pytorch
  - defaults
dependencies:
  - python=3.12
  - pip

  # 科学计算基础库
  - numpy>=1.24.0
  - pandas>=2.0.0
  - scipy>=1.10.0

  # 机器学习和深度学习 (支持苹果M芯片GPU和CUDA)
  - pytorch>=2.0.0
  - torchvision>=0.15.0
  - torchaudio>=2.0.0
  - scikit-learn>=1.3.0

  # GPU加速计算
  - numba>=0.58.0
  
  # 图算法和网络分析
  - networkx=3.2.1
  - python-igraph=0.11.3
  
  # 数据可视化
  - matplotlib=3.8.2
  - seaborn=0.13.0
  
  # 并行处理
  - joblib=1.3.2
  
  # 系统监控
  - psutil=5.9.6
  
  # 通过pip安装的包
  - pip:
    # Web框架
    - fastapi==0.104.1
    - uvicorn[standard]==0.24.0
    - pydantic==2.5.0
    
    # 数据库连接
    - pymysql==1.1.0
    - sqlalchemy==2.0.23
    - redis==5.0.1
    
    # 进程挖掘专用库
    - pm4py==********
    
    # 日志和监控
    - loguru==0.7.2
    
    # HTTP客户端
    - httpx==0.25.2
    - aiohttp==3.9.1
    
    # 开发和测试工具
    - pytest==7.4.3
    - pytest-asyncio==0.21.1
    - black==23.11.0
    - isort==5.12.0
    - flake8==6.1.0
    
    # 性能分析
    - memory-profiler==0.61.0
    - line-profiler==4.1.1
    
    # 数据序列化
    - orjson==3.9.10
    - msgpack==1.0.7

    # GPU加速库 (条件安装，需要根据系统手动安装)
    # 对于CUDA系统，可以安装:
    # pip install cupy-cuda11x  # 或 cupy-cuda12x
    # pip install cudf-cu11    # 或 cudf-cu12
    # 对于Apple Silicon，PyTorch MPS已包含在pytorch中
