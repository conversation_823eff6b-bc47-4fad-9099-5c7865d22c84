"""
CPU优化的子流程挖掘服务
高效的算法实现，适用于中小规模数据
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Set
from collections import defaultdict, Counter
from datetime import datetime
from loguru import logger
import asyncio
from concurrent.futures import ThreadPoolExecutor

from config.settings import Settings
from models.mining_models import (
    SubprocessDiscoveryRequest,
    SubprocessDiscoveryResponse,
    SubprocessPattern,
    HierarchicalDFG,
    DFGNode,
    DFGEdge,
    SubprocessStatistics,
    SubprocessDiscoveryOptions,
    EventLog
)


class SubprocessMiningService:
    """CPU优化的子流程挖掘服务"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.executor = ThreadPoolExecutor(max_workers=settings.max_workers)
        
        # 预定义的业务模式
        self.business_patterns = [
            ['原材料采购申请', '供应商资质审核', '采购合同签署'],
            ['原材料入库检验', '入库确认'],
            ['生产准备', '首件检验', '批量生产'],
            ['不合格品处理', '供应商整改通知', '重新检验'],
            ['过程检验', '返工处理', '重新检验'],
            ['成品检验', '包装', '出库检验'],
            ['质量问题分析', '改进措施制定', '措施实施'],
            ['设备维护', '设备检验', '设备验收'],
        ]
        
        logger.info("💻 初始化CPU优化挖掘服务")
    
    async def discover_subprocesses(
        self, 
        request: SubprocessDiscoveryRequest
    ) -> SubprocessDiscoveryResponse:
        """CPU优化的子流程发现"""
        
        logger.info(f"🔍 开始CPU优化子流程发现，数据量: {len(request.event_logs)}")
        
        # 设置默认选项
        options = request.options or SubprocessDiscoveryOptions()
        
        # 数据预处理
        case_groups = await self._group_by_case_async(request.event_logs)
        logger.info(f"📊 分组完成，案例数量: {len(case_groups)}")
        
        # 内存优化：限制案例数量
        if len(case_groups) > 1000:
            logger.warning(f"案例数量过多 ({len(case_groups)})，限制为1000个")
            case_groups = dict(list(case_groups.items())[:1000])
        
        # 并行模式发现
        sequential_patterns = await self._discover_sequential_patterns_async(
            case_groups, options
        )
        
        # 构建层次化DFG
        hierarchical_dfg = await self._build_hierarchical_dfg_async(
            case_groups, sequential_patterns
        )
        
        # 计算统计信息
        statistics = self._calculate_statistics(sequential_patterns)
        
        logger.info(f"✅ CPU优化发现完成，找到 {len(sequential_patterns)} 个子流程")
        
        return SubprocessDiscoveryResponse(
            process_id=request.process_id,
            subprocesses=sequential_patterns,
            hierarchical_dfg=hierarchical_dfg,
            statistics=statistics
        )
    
    async def _group_by_case_async(self, event_logs: List[EventLog]) -> Dict[str, List[EventLog]]:
        """异步案例分组"""
        
        def group_by_case():
            # 使用pandas进行高效分组
            df_data = []
            for log in event_logs:
                df_data.append({
                    'case_id': log.case_id,
                    'activity': log.activity,
                    'timestamp': log.timestamp,
                    'resource': log.resource,
                    'cost': log.cost or 0.0,
                    'original_log': log
                })
            
            df = pd.DataFrame(df_data)
            
            # 按案例分组并排序
            case_groups = {}
            for case_id, group in df.groupby('case_id'):
                # 按时间戳排序
                sorted_group = group.sort_values('timestamp')
                events = [row['original_log'] for _, row in sorted_group.iterrows()]
                case_groups[case_id] = events
            
            return case_groups
        
        # 在线程池中执行
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, group_by_case)
    
    async def _discover_sequential_patterns_async(
        self,
        case_groups: Dict[str, List[EventLog]],
        options: SubprocessDiscoveryOptions
    ) -> List[SubprocessPattern]:
        """异步顺序模式发现"""
        
        logger.info("🔍 开始CPU优化顺序模式发现")
        
        def discover_patterns():
            patterns = {}
            
            # 首先使用滑动窗口发现所有可能的模式
            logger.info("🔍 开始滑动窗口模式发现...")
            patterns = self._discover_sliding_window_patterns(case_groups, options)

            # 发现序列链模式（垂直子流程）
            logger.info("🔍 开始序列链模式发现...")
            chain_patterns = self._discover_sequential_chains(case_groups, options)
            patterns.update(chain_patterns)

            # 然后检查预定义的业务模式作为补充
            logger.info("🔍 检查预定义业务模式...")
            for pattern_activities in self.business_patterns:
                matches = []

                for case_id, events in case_groups.items():
                    activities = [event.activity for event in events]

                    # 检查模式是否存在于活动序列中
                    if self._contains_subsequence(activities, pattern_activities):
                        matches.append(case_id)

                if len(matches) >= options.min_frequency:
                    pattern_key = ' -> '.join(pattern_activities)
                    # 只添加不重复的模式
                    if pattern_key not in patterns:
                        patterns[pattern_key] = {
                            'activities': pattern_activities,
                            'matches': matches,
                            'frequency': len(matches)
                        }
                        logger.info(f"✅ 找到业务模式: {pattern_key} (频率: {len(matches)})")
            
            return patterns
        
        # 在线程池中执行
        loop = asyncio.get_event_loop()
        patterns = await loop.run_in_executor(self.executor, discover_patterns)
        
        # 转换为SubprocessPattern对象
        subprocess_patterns = []
        logger.info(f"🔍 开始过滤模式，总模式数: {len(patterns)}, 最小频率: {options.min_frequency}, 最小置信度: {options.min_confidence}")

        for idx, (pattern_key, pattern_data) in enumerate(patterns.items()):
            frequency = pattern_data['frequency']
            confidence = frequency / len(case_groups)

            logger.info(f"📊 模式 '{pattern_key}': 频率={frequency}, 置信度={confidence:.3f}")

            if frequency >= options.min_frequency:
                logger.info(f"✅ 频率检查通过: {frequency} >= {options.min_frequency}")

                if confidence >= options.min_confidence:
                    logger.info(f"✅ 置信度检查通过: {confidence:.3f} >= {options.min_confidence}")
                    subprocess_patterns.append(SubprocessPattern(
                        id=f"cpu_seq_{idx + 1}",
                        name=self._generate_pattern_name(pattern_data['activities']),
                        activities=pattern_data['activities'],
                        frequency=frequency,
                        confidence=confidence,
                        avg_duration=self._calculate_avg_duration(
                            pattern_data['matches'], case_groups
                        ),
                        cases=pattern_data['matches'],
                        pattern_type="sequential"
                    ))
                else:
                    logger.warning(f"❌ 置信度检查失败: {confidence:.3f} < {options.min_confidence}")
            else:
                logger.warning(f"❌ 频率检查失败: {frequency} < {options.min_frequency}")
        
        # 按频率排序
        subprocess_patterns.sort(key=lambda x: x.frequency, reverse=True)
        
        logger.info(f"🎉 CPU优化发现完成，找到 {len(subprocess_patterns)} 个有效模式")
        
        return subprocess_patterns[:15]  # 限制返回数量
    
    def _contains_subsequence(self, sequence: List[str], subsequence: List[str]) -> bool:
        """检查序列是否包含子序列"""
        if len(subsequence) > len(sequence):
            return False
        
        for i in range(len(sequence) - len(subsequence) + 1):
            if sequence[i:i + len(subsequence)] == subsequence:
                return True
        
        return False
    
    def _discover_sliding_window_patterns(
        self,
        case_groups: Dict[str, List[EventLog]],
        options: SubprocessDiscoveryOptions
    ) -> Dict[str, Dict]:
        """优化的滑动窗口模式发现"""

        logger.info("🔄 开始优化滑动窗口模式发现")

        pattern_counts = defaultdict(list)

        # 增加处理的案例数量
        max_cases = min(500, len(case_groups))
        case_items = list(case_groups.items())[:max_cases]

        # 首先收集所有活动的频率
        activity_freq = Counter()
        for case_id, events in case_items:
            for event in events:
                activity_freq[event.activity] += 1

        # 过滤掉过于稀少的活动
        min_activity_freq = max(1, len(case_items) * 0.01)  # 至少出现在1%的案例中
        frequent_activities = {act for act, freq in activity_freq.items() if freq >= min_activity_freq}

        logger.info(f"📊 总活动数: {len(activity_freq)}, 频繁活动数: {len(frequent_activities)}")

        for case_id, events in case_items:
            activities = [event.activity for event in events if event.activity in frequent_activities]

            if len(activities) < 2:
                continue

            # 生成不同长度的子序列，优先发现较长的模式
            max_length = min(options.max_pattern_length, len(activities), 8)
            for length in range(2, max_length + 1):
                for start in range(len(activities) - length + 1):
                    subsequence = activities[start:start + length]

                    # 跳过重复活动过多的序列
                    if len(set(subsequence)) < len(subsequence) * 0.6:
                        continue

                    pattern_key = ' -> '.join(subsequence)
                    pattern_counts[pattern_key].append(case_id)

        # 过滤和排序模式
        filtered_patterns = {}
        for pattern_key, matches in pattern_counts.items():
            unique_matches = list(set(matches))  # 去重
            if len(unique_matches) >= options.min_frequency:
                activities = pattern_key.split(' -> ')

                # 计算模式质量分数
                pattern_length = len(activities)
                frequency = len(unique_matches)
                uniqueness = len(set(activities)) / len(activities)  # 活动多样性

                # 综合评分：频率 * 长度 * 唯一性
                score = frequency * pattern_length * uniqueness

                filtered_patterns[pattern_key] = {
                    'activities': activities,
                    'matches': unique_matches,
                    'frequency': frequency,
                    'score': score
                }

        # 按评分排序，选择最佳模式
        sorted_patterns = dict(sorted(
            filtered_patterns.items(),
            key=lambda x: x[1]['score'],
            reverse=True
        ))

        logger.info(f"🔍 滑动窗口发现 {len(sorted_patterns)} 个模式")

        return sorted_patterns

    def _discover_sequential_chains(
        self,
        case_groups: Dict[str, List[EventLog]],
        options: SubprocessDiscoveryOptions
    ) -> Dict[str, Dict]:
        """发现序列链模式（垂直子流程）"""

        logger.info("🔗 开始序列链模式发现")

        chain_patterns = {}

        # 分析每个案例的完整序列
        for case_id, events in case_groups.items():
            activities = [event.activity for event in events]

            if len(activities) < 3:  # 至少需要3个活动形成链
                continue

            # 寻找长序列链（3-8个活动）
            for chain_length in range(3, min(9, len(activities) + 1)):
                for start in range(len(activities) - chain_length + 1):
                    chain = activities[start:start + chain_length]

                    # 检查是否是有效的序列链
                    if self._is_valid_sequential_chain(chain):
                        pattern_key = ' -> '.join(chain)

                        if pattern_key not in chain_patterns:
                            chain_patterns[pattern_key] = {
                                'activities': chain,
                                'matches': [],
                                'frequency': 0
                            }

                        if case_id not in chain_patterns[pattern_key]['matches']:
                            chain_patterns[pattern_key]['matches'].append(case_id)
                            chain_patterns[pattern_key]['frequency'] += 1

        # 过滤低频链模式
        filtered_chains = {}
        for pattern_key, pattern_data in chain_patterns.items():
            if pattern_data['frequency'] >= options.min_frequency:
                filtered_chains[pattern_key] = pattern_data

        logger.info(f"🔗 发现 {len(filtered_chains)} 个序列链模式")

        return filtered_chains

    def _is_valid_sequential_chain(self, chain: List[str]) -> bool:
        """检查是否是有效的序列链"""

        # 1. 活动不能完全重复
        if len(set(chain)) < len(chain) * 0.7:
            return False

        # 2. 不能有太多相邻重复
        adjacent_repeats = sum(1 for i in range(len(chain) - 1) if chain[i] == chain[i + 1])
        if adjacent_repeats > len(chain) * 0.3:
            return False

        # 3. 链长度合理
        if len(chain) < 3 or len(chain) > 8:
            return False

        return True
    
    async def _build_hierarchical_dfg_async(
        self,
        case_groups: Dict[str, List[EventLog]],
        patterns: List[SubprocessPattern]
    ) -> HierarchicalDFG:
        """异步构建层次化DFG"""

        def build_dfg():
            # 收集所有子流程中的活动
            subprocess_activities = set()
            for pattern in patterns:
                subprocess_activities.update(pattern.activities)

            # 统计活动频率和转换关系
            activity_freq = Counter()
            transitions = Counter()

            # 为每个案例构建压缩后的活动序列
            compressed_sequences = {}

            for case_id, events in case_groups.items():
                # 原始活动序列
                original_sequence = [event.activity for event in events]

                # 压缩序列：将子流程替换为单个节点
                compressed_sequence = self._compress_sequence_with_subprocesses(
                    original_sequence, patterns
                )
                compressed_sequences[case_id] = compressed_sequence

                # 统计压缩后序列的频率
                for activity in compressed_sequence:
                    activity_freq[activity] += 1

                # 统计压缩后序列的转换关系
                for i in range(len(compressed_sequence) - 1):
                    source = compressed_sequence[i]
                    target = compressed_sequence[i + 1]
                    transitions[(source, target)] += 1

            # 创建节点
            nodes = []

            # 1. 添加子流程节点
            for pattern in patterns:
                nodes.append(DFGNode(
                    id=pattern.id,
                    label=pattern.name,
                    frequency=pattern.frequency,
                    node_type="subprocess"
                ))

            # 2. 添加独立活动节点（不在任何子流程中的活动）
            for activity, freq in activity_freq.items():
                # 检查是否已经作为子流程添加
                if not any(pattern.id == activity for pattern in patterns):
                    # 检查是否是原子活动（不在任何子流程中）
                    if activity not in subprocess_activities:
                        nodes.append(DFGNode(
                            id=activity,
                            label=activity,
                            frequency=freq,
                            node_type="activity"
                        ))

            # 创建边
            edges = []
            for (source, target), freq in transitions.items():
                edges.append(DFGEdge(
                    source=source,
                    target=target,
                    frequency=freq,
                    avg_duration=0.0  # 简化处理
                ))

            return HierarchicalDFG(nodes=nodes, edges=edges)

        # 在线程池中执行
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, build_dfg)

    def _compress_sequence_with_subprocesses(
        self,
        sequence: List[str],
        patterns: List[SubprocessPattern]
    ) -> List[str]:
        """将活动序列中的子流程替换为单个节点"""
        compressed = []
        i = 0

        while i < len(sequence):
            # 检查是否匹配任何子流程模式
            matched_pattern = None
            max_length = 0

            for pattern in patterns:
                pattern_length = len(pattern.activities)
                if (i + pattern_length <= len(sequence) and
                    sequence[i:i + pattern_length] == pattern.activities and
                    pattern_length > max_length):
                    matched_pattern = pattern
                    max_length = pattern_length

            if matched_pattern:
                # 用子流程ID替换整个模式
                compressed.append(matched_pattern.id)
                i += len(matched_pattern.activities)
            else:
                # 保留原始活动
                compressed.append(sequence[i])
                i += 1

        return compressed
    
    def _calculate_statistics(self, patterns: List[SubprocessPattern]) -> SubprocessStatistics:
        """计算统计信息"""
        
        if not patterns:
            return SubprocessStatistics(
                total_subprocesses=0,
                avg_frequency=0.0,
                avg_confidence=0.0,
                pattern_type_distribution={}
            )
        
        total_freq = sum(p.frequency for p in patterns)
        total_conf = sum(p.confidence for p in patterns)
        
        type_dist = Counter(p.pattern_type for p in patterns)
        
        return SubprocessStatistics(
            total_subprocesses=len(patterns),
            avg_frequency=total_freq / len(patterns),
            avg_confidence=total_conf / len(patterns),
            pattern_type_distribution=dict(type_dist)
        )
    
    def _generate_pattern_name(self, activities: List[str]) -> str:
        """生成模式名称"""
        if len(activities) <= 2:
            return f"简单流程: {' -> '.join(activities)}"
        elif any(keyword in ' '.join(activities) for keyword in ['检验', '质量', '审核']):
            return f"质量控制流程: {activities[0]} -> ... -> {activities[-1]}"
        elif any(keyword in ' '.join(activities) for keyword in ['采购', '供应商']):
            return f"采购管理流程: {activities[0]} -> ... -> {activities[-1]}"
        elif any(keyword in ' '.join(activities) for keyword in ['生产', '制造']):
            return f"生产执行流程: {activities[0]} -> ... -> {activities[-1]}"
        else:
            return f"业务流程: {activities[0]} -> ... -> {activities[-1]}"
    
    def _calculate_avg_duration(
        self, 
        matches: List[str], 
        case_groups: Dict[str, List[EventLog]]
    ) -> float:
        """计算平均持续时间"""
        durations = []
        
        for case_id in matches[:10]:  # 限制计算数量
            if case_id in case_groups:
                events = case_groups[case_id]
                if len(events) >= 2:
                    start_time = events[0].timestamp
                    end_time = events[-1].timestamp
                    duration = (end_time - start_time).total_seconds() * 1000
                    durations.append(duration)
        
        return sum(durations) / len(durations) if durations else 0.0
