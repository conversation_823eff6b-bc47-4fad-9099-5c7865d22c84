"""
GPU加速的子流程挖掘服务
支持CUDA、Apple MPS等多种GPU加速环境
优化的高性能算法，支持大规模数据处理
"""

import torch
import torch.nn.functional as F
import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Set, Optional
from collections import defaultdict, Counter
from datetime import datetime
from loguru import logger
import gc
import time

from models.mining_models import (
    SubprocessDiscoveryRequest,
    SubprocessDiscoveryResponse,
    SubprocessPattern,
    HierarchicalDFG,
    DFGNode,
    DFGEdge,
    SubprocessStatistics,
    SubprocessDiscoveryOptions,
    EventLog
)


class GPUMemoryManager:
    """GPU内存管理器"""

    def __init__(self, device: torch.device, gpu_type: str):
        self.device = device
        self.gpu_type = gpu_type
        self.memory_threshold = 0.85  # 85%内存使用率阈值

    def get_memory_info(self) -> Dict[str, float]:
        """获取GPU内存信息"""
        if self.gpu_type == 'cuda' and torch.cuda.is_available():
            total = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            allocated = torch.cuda.memory_allocated(0) / (1024**3)
            cached = torch.cuda.memory_reserved(0) / (1024**3)
            return {
                'total': total,
                'allocated': allocated,
                'cached': cached,
                'free': total - allocated,
                'usage_percent': (allocated / total) * 100
            }
        else:
            # MPS或其他GPU类型的简化处理
            return {
                'total': 8.0,  # 估算值
                'allocated': 0.0,
                'cached': 0.0,
                'free': 8.0,
                'usage_percent': 0.0
            }

    def cleanup_memory(self):
        """清理GPU内存"""
        try:
            if self.gpu_type == 'cuda' and torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
            elif self.gpu_type == 'mps' and torch.backends.mps.is_available():
                torch.mps.empty_cache()

            # Python垃圾回收
            gc.collect()

        except Exception as e:
            logger.warning(f"GPU内存清理失败: {e}")

    def check_memory_usage(self) -> bool:
        """检查内存使用是否超过阈值"""
        memory_info = self.get_memory_info()
        return memory_info['usage_percent'] > (self.memory_threshold * 100)

    def optimize_batch_size(self, base_batch_size: int, data_size: int) -> int:
        """根据GPU内存优化批处理大小"""
        memory_info = self.get_memory_info()

        # 如果内存使用率高，减少批处理大小
        if memory_info['usage_percent'] > 70:
            return max(base_batch_size // 2, 100)
        elif memory_info['usage_percent'] < 30:
            # 内存充足，可以增加批处理大小
            return min(base_batch_size * 2, data_size // 10)

        return base_batch_size


class GPUAcceleratedMining:
    """GPU加速的子流程挖掘"""

    def __init__(self, device: torch.device, config: Optional[Dict] = None):
        self.device = device
        self.config = config or {}
        self.gpu_type = self._detect_gpu_type()
        self.memory_manager = GPUMemoryManager(device, self.gpu_type)

        logger.info(f"🚀 初始化GPU加速挖掘服务")
        logger.info(f"   设备: {device}")
        logger.info(f"   GPU类型: {self.gpu_type}")
        logger.info(f"   内存管理: 启用")

        # 预定义的业务模式（制造业流程）
        self.business_patterns = [
            ['原材料采购申请', '供应商资质审核', '采购合同签署'],
            ['原材料入库检验', '入库确认'],
            ['生产准备', '首件检验', '批量生产'],
            ['不合格品处理', '供应商整改通知', '重新检验'],
            ['过程检验', '返工处理', '重新检验'],
            ['成品检验', '包装', '出库检验'],
            ['质量问题分析', '改进措施制定', '措施实施'],
            ['设备维护', '设备检验', '设备验收'],
        ]

        # 性能配置
        self.batch_size = self.config.get('gpu_tensor_batch_size', 1000)
        self.max_sequence_length = self.config.get('gpu_max_sequence_length', 100)
        self.enable_memory_monitoring = self.config.get('enable_gpu_memory_monitoring', True)

    def _detect_gpu_type(self) -> str:
        """检测GPU类型"""
        if self.device.type == 'cuda':
            return 'cuda'
        elif self.device.type == 'mps':
            return 'mps'
        else:
            return 'cpu'
    
    async def discover_subprocesses_gpu(
        self,
        request: SubprocessDiscoveryRequest
    ) -> SubprocessDiscoveryResponse:
        """GPU加速的子流程发现"""

        start_time = time.time()
        data_size = len(request.event_logs)

        logger.info(f"🔥 开始GPU加速子流程发现")
        logger.info(f"   数据量: {data_size}")
        logger.info(f"   设备: {self.device}")
        logger.info(f"   GPU类型: {self.gpu_type}")

        # 内存监控
        if self.enable_memory_monitoring:
            initial_memory = self.memory_manager.get_memory_info()
            logger.info(f"   初始GPU内存使用: {initial_memory['usage_percent']:.1f}%")

        try:
            # 设置默认选项
            options = request.options or SubprocessDiscoveryOptions()

            # 优化批处理大小
            optimized_batch_size = self.memory_manager.optimize_batch_size(
                self.batch_size, data_size
            )
            logger.info(f"   优化批处理大小: {optimized_batch_size}")

            # 数据预处理
            case_groups = self._group_by_case_gpu(request.event_logs)
            logger.info(f"📊 分组完成，案例数量: {len(case_groups)}")

            # GPU加速的模式发现
            sequential_patterns = await self._discover_sequential_patterns_gpu(
                case_groups, options, optimized_batch_size
            )

            # 构建层次化DFG
            hierarchical_dfg = self._build_hierarchical_dfg_gpu(
                case_groups, sequential_patterns
            )

            # 计算统计信息
            statistics = self._calculate_statistics(sequential_patterns)

            # 性能统计
            processing_time = time.time() - start_time

            if self.enable_memory_monitoring:
                final_memory = self.memory_manager.get_memory_info()
                logger.info(f"   最终GPU内存使用: {final_memory['usage_percent']:.1f}%")

                # 如果内存使用率过高，执行清理
                if self.memory_manager.check_memory_usage():
                    logger.info("🧹 执行GPU内存清理...")
                    self.memory_manager.cleanup_memory()

            logger.info(f"✅ GPU加速发现完成")
            logger.info(f"   处理时间: {processing_time:.2f}秒")
            logger.info(f"   找到子流程: {len(sequential_patterns)}个")
            logger.info(f"   平均处理速度: {data_size/processing_time:.0f}条/秒")

            return SubprocessDiscoveryResponse(
                process_id=request.process_id,
                subprocesses=sequential_patterns,
                hierarchical_dfg=hierarchical_dfg,
                statistics=statistics
            )

        except Exception as e:
            logger.error(f"❌ GPU加速处理失败: {e}")
            # 清理GPU内存
            self.memory_manager.cleanup_memory()
            raise
    
    def _group_by_case_gpu(self, event_logs: List[EventLog]) -> Dict[str, List[EventLog]]:
        """GPU优化的案例分组"""
        
        # 转换为DataFrame进行高效分组
        df_data = []
        for log in event_logs:
            df_data.append({
                'case_id': log.case_id,
                'activity': log.activity,
                'timestamp': log.timestamp,
                'resource': log.resource,
                'cost': log.cost or 0.0
            })
        
        df = pd.DataFrame(df_data)
        
        # 按案例分组并排序
        case_groups = {}
        for case_id, group in df.groupby('case_id'):
            # 按时间戳排序
            sorted_group = group.sort_values('timestamp')
            
            # 转换回EventLog对象
            events = []
            for _, row in sorted_group.iterrows():
                events.append(EventLog(
                    id=0,  # 临时ID
                    case_id=row['case_id'],
                    activity=row['activity'],
                    timestamp=row['timestamp'],
                    resource=row['resource'],
                    cost=row['cost']
                ))
            
            case_groups[case_id] = events
        
        return case_groups
    
    async def _discover_sequential_patterns_gpu(
        self,
        case_groups: Dict[str, List[EventLog]],
        options,
        batch_size: int = 1000
    ) -> List[SubprocessPattern]:
        """GPU加速的顺序模式发现"""
        
        logger.info("🔍 开始GPU加速顺序模式发现")
        
        # 创建活动到索引的映射
        all_activities = set()
        for events in case_groups.values():
            for event in events:
                all_activities.add(event.activity)
        
        activity_to_idx = {act: idx for idx, act in enumerate(sorted(all_activities))}
        idx_to_activity = {idx: act for act, idx in activity_to_idx.items()}
        
        logger.info(f"📝 活动词汇表大小: {len(activity_to_idx)}")
        
        # 转换案例为张量序列（批处理优化）
        case_tensors = []
        case_ids = []

        for case_id, events in case_groups.items():
            if len(events) < 2:  # 跳过太短的案例
                continue

            # 限制序列长度以节省内存
            max_length = min(len(events), self.max_sequence_length)
            sequence = [activity_to_idx[event.activity] for event in events[:max_length]]

            case_tensors.append(torch.tensor(sequence, dtype=torch.long))
            case_ids.append(case_id)

        logger.info(f"🎯 有效案例数量: {len(case_tensors)}")
        logger.info(f"🎯 最大序列长度限制: {self.max_sequence_length}")
        
        # 首先检查预定义的业务模式
        patterns = {}
        
        for pattern_activities in self.business_patterns:
            # 检查模式是否在词汇表中
            if all(act in activity_to_idx for act in pattern_activities):
                pattern_indices = [activity_to_idx[act] for act in pattern_activities]
                pattern_tensor = torch.tensor(pattern_indices, dtype=torch.long)
                
                # GPU加速的模式匹配
                matches = self._find_pattern_matches_gpu(
                    case_tensors, pattern_tensor, case_ids
                )
                
                if len(matches) >= options.min_frequency:
                    pattern_key = ' -> '.join(pattern_activities)
                    patterns[pattern_key] = {
                        'activities': pattern_activities,
                        'matches': matches,
                        'frequency': len(matches)
                    }
                    logger.info(f"✅ 找到业务模式: {pattern_key} (频率: {len(matches)})")
        
        # 如果业务模式不足，使用滑动窗口发现更多模式
        if len(patterns) < 5:
            additional_patterns = await self._discover_sliding_window_patterns_gpu(
                case_tensors, case_ids, idx_to_activity, options
            )
            patterns.update(additional_patterns)
        
        # 转换为SubprocessPattern对象
        subprocess_patterns = []
        for idx, (pattern_key, pattern_data) in enumerate(patterns.items()):
            if pattern_data['frequency'] >= options.min_frequency:
                confidence = pattern_data['frequency'] / len(case_groups)
                
                if confidence >= options.min_confidence:
                    subprocess_patterns.append(SubprocessPattern(
                        id=f"gpu_seq_{idx + 1}",
                        name=self._generate_pattern_name(pattern_data['activities']),
                        activities=pattern_data['activities'],
                        frequency=pattern_data['frequency'],
                        confidence=confidence,
                        avg_duration=self._calculate_avg_duration(
                            pattern_data['matches'], case_groups
                        ),
                        cases=pattern_data['matches'],
                        pattern_type="sequential"
                    ))
        
        # 按频率排序
        subprocess_patterns.sort(key=lambda x: x.frequency, reverse=True)
        
        logger.info(f"🎉 GPU加速发现完成，找到 {len(subprocess_patterns)} 个有效模式")
        
        return subprocess_patterns[:20]  # 限制返回数量
    
    def _find_pattern_matches_gpu(
        self,
        case_tensors: List[torch.Tensor],
        pattern_tensor: torch.Tensor,
        case_ids: List[str]
    ) -> List[str]:
        """GPU加速的模式匹配（批处理优化）"""

        pattern_length = len(pattern_tensor)
        matches = []

        # 移动模式到GPU设备
        pattern_tensor = pattern_tensor.to(self.device)

        # 批处理匹配以提高效率
        batch_size = min(self.batch_size, len(case_tensors))

        for batch_start in range(0, len(case_tensors), batch_size):
            batch_end = min(batch_start + batch_size, len(case_tensors))
            batch_tensors = case_tensors[batch_start:batch_end]
            batch_ids = case_ids[batch_start:batch_end]

            # 处理当前批次
            batch_matches = self._process_pattern_batch(
                batch_tensors, batch_ids, pattern_tensor, pattern_length
            )
            matches.extend(batch_matches)

            # 内存管理：定期检查和清理
            if self.enable_memory_monitoring and (batch_start + batch_size) % (batch_size * 5) == 0:
                if self.memory_manager.check_memory_usage():
                    logger.debug("🧹 批处理中执行GPU内存清理")
                    self.memory_manager.cleanup_memory()

        return matches

    def _process_pattern_batch(
        self,
        batch_tensors: List[torch.Tensor],
        batch_ids: List[str],
        pattern_tensor: torch.Tensor,
        pattern_length: int
    ) -> List[str]:
        """处理单个批次的模式匹配"""
        batch_matches = []

        for case_tensor, case_id in zip(batch_tensors, batch_ids):
            if len(case_tensor) < pattern_length:
                continue

            # 移动到GPU设备
            case_tensor = case_tensor.to(self.device)

            try:
                # 使用向量化操作进行高效匹配
                if self._vectorized_pattern_match(case_tensor, pattern_tensor, pattern_length):
                    batch_matches.append(case_id)
            except Exception as e:
                # 回退到逐个匹配
                logger.debug(f"向量化匹配失败，回退到逐个匹配: {e}")
                if self._sequential_pattern_match(case_tensor, pattern_tensor, pattern_length):
                    batch_matches.append(case_id)

        return batch_matches

    def _vectorized_pattern_match(
        self,
        case_tensor: torch.Tensor,
        pattern_tensor: torch.Tensor,
        pattern_length: int
    ) -> bool:
        """向量化模式匹配（CUDA优化）"""
        if self.gpu_type != 'cuda':
            # 非CUDA设备回退到顺序匹配
            return self._sequential_pattern_match(case_tensor, pattern_tensor, pattern_length)

        case_length = len(case_tensor)
        if case_length < pattern_length:
            return False

        # 创建滑动窗口视图
        windows = case_tensor.unfold(0, pattern_length, 1)

        # 向量化比较
        matches = torch.all(windows == pattern_tensor.unsqueeze(0), dim=1)

        return torch.any(matches).item()

    def _sequential_pattern_match(
        self,
        case_tensor: torch.Tensor,
        pattern_tensor: torch.Tensor,
        pattern_length: int
    ) -> bool:
        """顺序模式匹配（兼容性回退）"""
        for j in range(len(case_tensor) - pattern_length + 1):
            window = case_tensor[j:j + pattern_length]
            if torch.equal(window, pattern_tensor):
                return True
        return False
    
    async def _discover_sliding_window_patterns_gpu(
        self,
        case_tensors: List[torch.Tensor],
        case_ids: List[str],
        idx_to_activity: Dict[int, str],
        options
    ) -> Dict[str, Dict]:
        """GPU加速的滑动窗口模式发现"""
        
        logger.info("🔄 开始滑动窗口模式发现")
        
        pattern_counts = defaultdict(list)
        
        # 根据GPU内存动态调整处理的案例数量
        memory_info = self.memory_manager.get_memory_info()
        if memory_info['usage_percent'] > 70:
            max_cases = min(200, len(case_tensors))
        elif memory_info['usage_percent'] > 50:
            max_cases = min(500, len(case_tensors))
        else:
            max_cases = min(1000, len(case_tensors))

        logger.info(f"🔄 滑动窗口处理案例数量: {max_cases}/{len(case_tensors)}")
        
        for case_idx in range(max_cases):
            case_tensor = case_tensors[case_idx]
            case_id = case_ids[case_idx]
            
            # 生成不同长度的子序列
            for length in range(2, min(options.max_pattern_length + 1, len(case_tensor) + 1)):
                for start in range(len(case_tensor) - length + 1):
                    subsequence = case_tensor[start:start + length]
                    
                    # 转换为活动名称
                    activities = [idx_to_activity[idx.item()] for idx in subsequence]
                    pattern_key = ' -> '.join(activities)
                    
                    pattern_counts[pattern_key].append(case_id)
        
        # 过滤低频模式
        filtered_patterns = {}
        for pattern_key, matches in pattern_counts.items():
            unique_matches = list(set(matches))  # 去重
            if len(unique_matches) >= options.min_frequency:
                activities = pattern_key.split(' -> ')
                filtered_patterns[pattern_key] = {
                    'activities': activities,
                    'matches': unique_matches,
                    'frequency': len(unique_matches)
                }
        
        logger.info(f"🔍 滑动窗口发现 {len(filtered_patterns)} 个额外模式")
        
        return filtered_patterns
    
    def _build_hierarchical_dfg_gpu(
        self,
        case_groups: Dict[str, List[EventLog]],
        patterns: List[SubprocessPattern]
    ) -> HierarchicalDFG:
        """构建层次化DFG"""
        
        # 统计活动频率
        activity_freq = Counter()
        transitions = Counter()
        
        for events in case_groups.values():
            for event in events:
                activity_freq[event.activity] += 1
            
            # 统计转换关系
            for i in range(len(events) - 1):
                source = events[i].activity
                target = events[i + 1].activity
                transitions[(source, target)] += 1
        
        # 创建节点
        nodes = []
        for activity, freq in activity_freq.items():
            nodes.append(DFGNode(
                id=activity,
                label=activity,
                frequency=freq,
                node_type="activity"
            ))
        
        # 创建边
        edges = []
        for (source, target), freq in transitions.items():
            edges.append(DFGEdge(
                source=source,
                target=target,
                frequency=freq,
                avg_duration=0.0  # 简化处理
            ))
        
        return HierarchicalDFG(nodes=nodes, edges=edges)
    
    def _calculate_statistics(self, patterns: List[SubprocessPattern]) -> SubprocessStatistics:
        """计算统计信息"""
        
        if not patterns:
            return SubprocessStatistics(
                total_subprocesses=0,
                avg_frequency=0.0,
                avg_confidence=0.0,
                pattern_type_distribution={}
            )
        
        total_freq = sum(p.frequency for p in patterns)
        total_conf = sum(p.confidence for p in patterns)
        
        type_dist = Counter(p.pattern_type for p in patterns)
        
        return SubprocessStatistics(
            total_subprocesses=len(patterns),
            avg_frequency=total_freq / len(patterns),
            avg_confidence=total_conf / len(patterns),
            pattern_type_distribution=dict(type_dist)
        )
    
    def _generate_pattern_name(self, activities: List[str]) -> str:
        """生成模式名称"""
        if len(activities) <= 2:
            return f"简单流程: {' -> '.join(activities)}"
        elif any(keyword in ' '.join(activities) for keyword in ['检验', '质量', '审核']):
            return f"质量控制流程: {activities[0]} -> ... -> {activities[-1]}"
        elif any(keyword in ' '.join(activities) for keyword in ['采购', '供应商']):
            return f"采购管理流程: {activities[0]} -> ... -> {activities[-1]}"
        elif any(keyword in ' '.join(activities) for keyword in ['生产', '制造']):
            return f"生产执行流程: {activities[0]} -> ... -> {activities[-1]}"
        else:
            return f"业务流程: {activities[0]} -> ... -> {activities[-1]}"
    
    def _calculate_avg_duration(
        self, 
        matches: List[str], 
        case_groups: Dict[str, List[EventLog]]
    ) -> float:
        """计算平均持续时间"""
        durations = []
        
        for case_id in matches[:10]:  # 限制计算数量
            if case_id in case_groups:
                events = case_groups[case_id]
                if len(events) >= 2:
                    start_time = events[0].timestamp
                    end_time = events[-1].timestamp
                    duration = (end_time - start_time).total_seconds() * 1000
                    durations.append(duration)
        
        return sum(durations) / len(durations) if durations else 0.0
