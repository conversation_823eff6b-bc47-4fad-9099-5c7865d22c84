version: '3.8'

services:
  python-mining-service:
    build: .
    container_name: promined-python-mining
    ports:
      - "8000:8000"
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=false
      - MYSQL_HOST=host.docker.internal
      - MYSQL_PORT=3306
      - MYSQL_USER=root
      - MYSQL_PASSWORD=your_password
      - MYSQL_DATABASE=promined
      - REDIS_HOST=host.docker.internal
      - REDIS_PORT=6379
      - MAX_WORKERS=4
      - ENABLE_GPU_ACCELERATION=true
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - promined-network

networks:
  promined-network:
    driver: bridge
