#!/bin/bash

# ProMined Python Mining Service 手动安装脚本
# 适用于解决conda环境依赖冲突的情况

echo "🔧 ProMined Python Mining Service 手动安装"
echo "=" * 50

# 检查conda
if ! command -v conda &> /dev/null; then
    echo "❌ 错误: conda 未安装。请先安装 Miniconda 或 Anaconda。"
    exit 1
fi

# 删除已存在的环境（如果有）
echo "🧹 清理已存在的环境..."
conda env remove -n promined-mining -y 2>/dev/null || true

# 创建基础Python环境
echo "🐍 创建Python 3.12基础环境..."
conda create -n promined-mining python=3.12 -y

# 激活环境
echo "🔄 激活环境..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate promined-mining

# 验证Python版本
echo "✅ Python版本: $(python --version)"

# 安装基础科学计算库
echo "📊 安装基础科学计算库..."
conda install -c conda-forge numpy pandas scipy -y

# 安装PyTorch (苹果M芯片优化)
echo "🔥 安装PyTorch (苹果M芯片优化)..."
conda install -c pytorch pytorch torchvision torchaudio -y

# 安装机器学习库
echo "🤖 安装机器学习库..."
conda install -c conda-forge scikit-learn -y

# 安装图算法库
echo "🕸️  安装图算法库..."
conda install -c conda-forge networkx -y

# 安装系统监控库
echo "📈 安装系统监控库..."
conda install -c conda-forge psutil joblib -y

# 配置pip使用国内镜像源
echo "🌏 配置pip使用国内镜像源..."
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# 使用pip安装Web框架
echo "🌐 安装Web框架..."
pip install fastapi==0.104.1
pip install "uvicorn[standard]==0.24.0"
pip install pydantic==2.5.0
pip install pydantic-settings==2.1.0

# 安装数据库连接库
echo "🗄️  安装数据库连接库..."
pip install pymysql==1.1.0
pip install sqlalchemy==2.0.23
pip install redis==5.0.1

# 安装进程挖掘专用库
echo "⛏️  安装进程挖掘库..."
pip install pm4py==********

# 安装日志和HTTP库
echo "📝 安装日志和HTTP库..."
pip install loguru==0.7.2
pip install httpx==0.25.2
pip install aiohttp==3.9.1

# 安装开发工具
echo "🛠️  安装开发工具..."
pip install pytest==7.4.3
pip install pytest-asyncio==0.21.1
pip install black==23.11.0
pip install isort==5.12.0
pip install flake8==6.1.0

# 安装性能分析工具
echo "⚡ 安装性能分析工具..."
pip install memory-profiler==0.61.0

# 安装数据序列化库
echo "📦 安装数据序列化库..."
pip install orjson==3.9.10
pip install msgpack==1.0.7

# 安装图算法扩展
echo "🔗 安装图算法扩展..."
pip install python-igraph==0.11.3

# 验证PyTorch GPU支持
echo "🔍 验证PyTorch GPU支持..."
python -c "
import torch
print(f'PyTorch版本: {torch.__version__}')
if torch.backends.mps.is_available():
    print('✅ 苹果M芯片GPU (MPS) 可用')
    device = torch.device('mps')
    print(f'设备: {device}')
    # 测试GPU
    x = torch.randn(10, 10).to(device)
    y = torch.randn(10, 10).to(device)
    z = torch.mm(x, y)
    print('✅ GPU计算测试通过')
else:
    print('⚠️  MPS不可用，将使用CPU')
    device = torch.device('cpu')
    print(f'设备: {device}')
"

# 创建项目目录结构
echo "📁 创建项目目录结构..."
mkdir -p {logs,data,tests}

# 创建.env文件
if [ ! -f .env ]; then
    echo "📝 创建配置文件..."
    cp .env.example .env
fi

echo ""
echo "✅ 手动安装完成！"
echo ""
echo "🎯 下一步操作:"
echo "1. 激活环境: conda activate promined-mining"
echo "2. 编辑配置: vim .env"
echo "3. 启动服务: python app/main.py"
echo "4. 测试服务: python test_service.py"
echo ""
echo "💡 提示:"
echo "- 使用 'conda deactivate' 退出环境"
echo "- 使用 'conda env remove -n promined-mining' 删除环境"
