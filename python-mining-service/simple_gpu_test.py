#!/usr/bin/env python3
"""
简化的GPU测试脚本
测试基本的GPU功能和PyTorch CUDA支持
"""

import torch
import time
import numpy as np
from datetime import datetime

def test_gpu_basic():
    """测试基本GPU功能"""
    print("🔍 测试基本GPU功能...")
    
    # 检查CUDA可用性
    print(f"   PyTorch版本: {torch.__version__}")
    print(f"   CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"   CUDA版本: {torch.version.cuda}")
        print(f"   GPU数量: {torch.cuda.device_count()}")
        print(f"   GPU名称: {torch.cuda.get_device_name(0)}")
        
        # 获取GPU内存信息
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"   GPU总内存: {gpu_memory:.1f}GB")
        
        return True
    else:
        print("   ❌ CUDA不可用")
        return False

def test_gpu_computation():
    """测试GPU计算性能"""
    print("\n🚀 测试GPU计算性能...")
    
    if not torch.cuda.is_available():
        print("   跳过测试 - GPU不可用")
        return
    
    # 测试不同大小的矩阵乘法
    sizes = [1000, 2000, 4000]
    
    for size in sizes:
        print(f"\n   测试 {size}x{size} 矩阵乘法:")
        
        # CPU测试
        print("     CPU计算...")
        x_cpu = torch.randn(size, size)
        y_cpu = torch.randn(size, size)
        
        start_time = time.time()
        z_cpu = torch.mm(x_cpu, y_cpu)
        cpu_time = time.time() - start_time
        print(f"     CPU时间: {cpu_time:.3f}秒")
        
        # GPU测试
        print("     GPU计算...")
        x_gpu = torch.randn(size, size).cuda()
        y_gpu = torch.randn(size, size).cuda()
        
        # 预热GPU
        torch.mm(x_gpu, y_gpu)
        torch.cuda.synchronize()
        
        start_time = time.time()
        z_gpu = torch.mm(x_gpu, y_gpu)
        torch.cuda.synchronize()
        gpu_time = time.time() - start_time
        print(f"     GPU时间: {gpu_time:.3f}秒")
        
        # 计算加速比
        speedup = cpu_time / gpu_time
        print(f"     加速比: {speedup:.1f}x")
        
        # 验证结果一致性
        z_gpu_cpu = z_gpu.cpu()
        max_diff = torch.max(torch.abs(z_cpu - z_gpu_cpu)).item()
        print(f"     最大误差: {max_diff:.2e}")
        
        # 清理GPU内存
        del x_gpu, y_gpu, z_gpu
        torch.cuda.empty_cache()

def test_gpu_memory():
    """测试GPU内存管理"""
    print("\n💾 测试GPU内存管理...")
    
    if not torch.cuda.is_available():
        print("   跳过测试 - GPU不可用")
        return
    
    # 获取初始内存状态
    torch.cuda.empty_cache()
    initial_memory = torch.cuda.memory_allocated() / 1024**2
    print(f"   初始GPU内存使用: {initial_memory:.1f}MB")
    
    # 分配大量内存
    print("   分配GPU内存...")
    tensors = []
    for i in range(10):
        tensor = torch.randn(1000, 1000).cuda()
        tensors.append(tensor)
        current_memory = torch.cuda.memory_allocated() / 1024**2
        print(f"     第{i+1}次分配后: {current_memory:.1f}MB")
    
    # 释放内存
    print("   释放GPU内存...")
    del tensors
    torch.cuda.empty_cache()
    final_memory = torch.cuda.memory_allocated() / 1024**2
    print(f"   释放后GPU内存使用: {final_memory:.1f}MB")

def test_mixed_precision():
    """测试混合精度计算"""
    print("\n🎯 测试混合精度计算...")
    
    if not torch.cuda.is_available():
        print("   跳过测试 - GPU不可用")
        return
    
    size = 2000
    
    # FP32计算
    print("   FP32计算...")
    x_fp32 = torch.randn(size, size, dtype=torch.float32).cuda()
    y_fp32 = torch.randn(size, size, dtype=torch.float32).cuda()
    
    start_time = time.time()
    z_fp32 = torch.mm(x_fp32, y_fp32)
    torch.cuda.synchronize()
    fp32_time = time.time() - start_time
    print(f"     FP32时间: {fp32_time:.3f}秒")
    
    # FP16计算
    print("   FP16计算...")
    x_fp16 = x_fp32.half()
    y_fp16 = y_fp32.half()
    
    start_time = time.time()
    z_fp16 = torch.mm(x_fp16, y_fp16)
    torch.cuda.synchronize()
    fp16_time = time.time() - start_time
    print(f"     FP16时间: {fp16_time:.3f}秒")
    
    # 计算加速比
    speedup = fp32_time / fp16_time
    print(f"     FP16加速比: {speedup:.1f}x")
    
    # 清理内存
    del x_fp32, y_fp32, z_fp32, x_fp16, y_fp16, z_fp16
    torch.cuda.empty_cache()

def main():
    """主测试函数"""
    print("🧪 开始简化GPU功能测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # 1. 基本GPU功能测试
    gpu_available = test_gpu_basic()
    
    if gpu_available:
        # 2. GPU计算性能测试
        test_gpu_computation()
        
        # 3. GPU内存管理测试
        test_gpu_memory()
        
        # 4. 混合精度测试
        test_mixed_precision()
        
        print("\n🎉 所有GPU测试完成！")
        print("✅ GPU环境配置成功，可以进行高性能计算")
    else:
        print("\n❌ GPU不可用，请检查CUDA安装")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
