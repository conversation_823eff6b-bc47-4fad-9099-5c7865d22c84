# 🔧 故障排除指南

## 🚨 常见安装问题

### 1. Conda环境依赖冲突

**问题**: `LibMambaUnsatisfiableError` 或包版本冲突

**解决方案**:
```bash
# 方案1: 使用简化环境配置
conda env create -f environment-simple.yml

# 方案2: 手动安装
./install_manual.sh

# 方案3: 清理并重新安装
conda env remove -n promined-mining -y
conda clean --all -y
./setup_env.sh
```

### 2. PyTorch版本不兼容

**问题**: `torchvision` 与 Python 3.12 不兼容

**解决方案**:
```bash
# 使用最新兼容版本
conda install -c pytorch pytorch torchvision torchaudio -y

# 或使用pip安装
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
```

### 3. pytorch-metal 包不存在

**问题**: `pytorch-metal 0.1.0** does not exist`

**说明**: 这个包已经不需要了，PyTorch 2.0+ 原生支持苹果M芯片MPS

**解决方案**: 使用简化环境配置，不包含 `pytorch-metal`

### 4. Python版本问题

**问题**: 某些包不支持Python 3.12

**解决方案**:
```bash
# 降级到Python 3.11
conda create -n promined-mining python=3.11 -y
conda activate promined-mining

# 然后安装依赖
pip install -r requirements.txt
```

## 🔍 运行时问题

### 1. GPU不可用

**检查**:
```bash
python -c "import torch; print(torch.backends.mps.is_available())"
```

**解决方案**:
- 确保使用苹果M芯片设备
- 更新到最新的macOS版本
- 服务会自动降级到CPU模式

### 2. 内存不足

**症状**: 处理大数据集时内存溢出

**解决方案**:
```bash
# 调整环境变量
export BATCH_SIZE=500
export MAX_MEMORY_USAGE_PERCENT=70.0
export MAX_WORKERS=2
```

### 3. 端口占用

**问题**: `Error: listen EADDRINUSE: address already in use :::8000`

**解决方案**:
```bash
# 查找占用端口的进程
lsof -i :8000

# 杀死进程
kill -9 <PID>

# 或更改端口
export PORT=8001
```

### 4. 数据库连接失败

**检查MySQL连接**:
```bash
mysql -h localhost -u root -p promined
```

**解决方案**:
```bash
# 启动MySQL服务
brew services start mysql

# 检查配置
vim .env
# 确保数据库配置正确
```

## 🛠️ 性能优化

### 1. 苹果M芯片优化

```bash
# 设置环境变量
export PYTORCH_ENABLE_MPS_FALLBACK=1
export OMP_NUM_THREADS=8
export MKL_NUM_THREADS=8
```

### 2. 内存优化

```bash
# 启用内存监控
export ENABLE_MEMORY_MONITORING=true

# 自动垃圾回收
export AUTO_CLEANUP_MEMORY=true

# 调整批处理大小
export BATCH_SIZE=1000
```

### 3. 算法参数调优

```bash
# 降低复杂度
export MAX_PATTERN_LENGTH=4
export MIN_FREQUENCY=3

# 禁用复杂算法
export ENABLE_PARALLEL_DETECTION=false
export ENABLE_LOOP_DETECTION=false
```

## 📊 调试工具

### 1. 健康检查

```bash
curl http://localhost:8000/health
```

### 2. 系统信息

```bash
curl http://localhost:8000/system-info
```

### 3. 强制垃圾回收

```bash
curl -X POST http://localhost:8000/force-gc
```

### 4. 查看日志

```bash
# 实时日志
tail -f logs/mining_service.log

# 错误日志
grep "ERROR" logs/mining_service.log

# 性能日志
grep "performance" logs/mining_service.log
```

## 🔄 重新安装

### 完全清理重装

```bash
# 删除conda环境
conda env remove -n promined-mining -y

# 清理conda缓存
conda clean --all -y

# 删除项目文件
rm -rf logs/ data/ venv/

# 重新安装
./install_manual.sh
```

### 仅重装Python包

```bash
# 激活环境
conda activate promined-mining

# 重装关键包
pip uninstall torch torchvision torchaudio -y
pip install torch torchvision torchaudio

# 重装其他包
pip install -r requirements.txt --force-reinstall
```

## 📞 获取帮助

### 1. 检查系统状态

```bash
# 运行诊断脚本
python -c "
import sys
import torch
import platform

print('=== 系统诊断 ===')
print(f'操作系统: {platform.system()} {platform.release()}')
print(f'架构: {platform.machine()}')
print(f'Python版本: {sys.version}')
print(f'PyTorch版本: {torch.__version__}')
print(f'MPS可用: {torch.backends.mps.is_available()}')

if torch.backends.mps.is_available():
    print('✅ GPU加速可用')
else:
    print('⚠️  仅CPU模式')
"
```

### 2. 测试基本功能

```bash
python test_service.py
```

### 3. 性能基准测试

```bash
python -c "
import time
import torch

device = torch.device('mps' if torch.backends.mps.is_available() else 'cpu')
print(f'使用设备: {device}')

# 简单性能测试
start_time = time.time()
x = torch.randn(1000, 1000).to(device)
y = torch.randn(1000, 1000).to(device)
z = torch.mm(x, y)
end_time = time.time()

print(f'矩阵乘法耗时: {end_time - start_time:.4f}秒')
"
```

## 📝 报告问题

如果问题仍未解决，请提供以下信息：

1. 操作系统和版本
2. Python版本
3. 错误日志
4. 系统诊断输出
5. 安装方式和步骤

联系方式：
- 项目Issue: [GitHub Issues]
- 邮件: [<EMAIL>]
