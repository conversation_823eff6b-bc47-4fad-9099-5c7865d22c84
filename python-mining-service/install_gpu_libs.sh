#!/bin/bash

# GPU加速库安装脚本
# 根据系统环境自动检测并安装合适的GPU加速库

set -e

echo "🚀 开始检测GPU环境并安装相应的加速库..."

# 配置pip使用国内镜像源
echo "🌏 配置pip使用国内镜像源..."
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# 检测操作系统
OS=$(uname -s)
ARCH=$(uname -m)

echo "📋 系统信息:"
echo "   操作系统: $OS"
echo "   架构: $ARCH"

# 检测Python环境
PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1,2)
echo "   Python版本: $PYTHON_VERSION"

# 检测CUDA
check_cuda() {
    if command -v nvidia-smi &> /dev/null; then
        CUDA_VERSION=$(nvidia-smi | grep "CUDA Version" | sed 's/.*CUDA Version: \([0-9.]*\).*/\1/')
        echo "✅ 检测到CUDA: $CUDA_VERSION"
        return 0
    else
        echo "❌ 未检测到CUDA"
        return 1
    fi
}

# 检测Apple Silicon
check_apple_silicon() {
    if [[ "$OS" == "Darwin" && "$ARCH" == "arm64" ]]; then
        echo "✅ 检测到Apple Silicon (M系列芯片)"
        return 0
    else
        return 1
    fi
}

# 安装CUDA相关库
install_cuda_libs() {
    echo "🔧 安装CUDA加速库..."
    
    # 根据CUDA版本选择CuPy
    if [[ "$CUDA_VERSION" == 12.* ]]; then
        echo "   安装CuPy for CUDA 12.x..."
        pip install cupy-cuda12x
    elif [[ "$CUDA_VERSION" == 11.* ]]; then
        echo "   安装CuPy for CUDA 11.x..."
        pip install cupy-cuda11x
    else
        echo "   ⚠️  CUDA版本不支持，跳过CuPy安装"
    fi
    
    # 尝试安装RAPIDS cuDF (可能需要特定环境)
    echo "   尝试安装RAPIDS cuDF..."
    if [[ "$CUDA_VERSION" == 12.* ]]; then
        pip install --extra-index-url=https://pypi.nvidia.com cudf-cu12 || echo "   ⚠️  cuDF安装失败，可能需要手动安装"
    elif [[ "$CUDA_VERSION" == 11.* ]]; then
        pip install --extra-index-url=https://pypi.nvidia.com cudf-cu11 || echo "   ⚠️  cuDF安装失败，可能需要手动安装"
    fi
    
    echo "✅ CUDA库安装完成"
}

# 安装Apple Silicon优化库
install_apple_libs() {
    echo "🔧 安装Apple Silicon优化库..."
    
    # 确保PyTorch支持MPS
    echo "   检查PyTorch MPS支持..."
    python3 -c "import torch; print('MPS可用:', torch.backends.mps.is_available())" || echo "   ⚠️  PyTorch MPS检查失败"
    
    # 安装Apple优化的科学计算库
    echo "   安装Apple优化的NumPy/SciPy..."
    pip install --upgrade numpy scipy
    
    echo "✅ Apple Silicon库检查完成"
}

# 安装通用GPU加速库
install_common_libs() {
    echo "🔧 安装通用GPU加速库..."
    
    # Numba (支持CUDA JIT编译)
    echo "   安装Numba..."
    pip install numba
    
    # 其他性能优化库
    echo "   安装其他性能库..."
    pip install psutil memory-profiler
    
    echo "✅ 通用库安装完成"
}

# 验证安装
verify_installation() {
    echo "🔍 验证GPU库安装..."
    
    python3 -c "
import sys
print('Python版本:', sys.version)

# 检查PyTorch
try:
    import torch
    print('✅ PyTorch版本:', torch.__version__)
    print('   CUDA可用:', torch.cuda.is_available())
    if torch.cuda.is_available():
        print('   CUDA设备数量:', torch.cuda.device_count())
        print('   CUDA版本:', torch.version.cuda)
    print('   MPS可用:', torch.backends.mps.is_available())
except ImportError as e:
    print('❌ PyTorch导入失败:', e)

# 检查CuPy
try:
    import cupy
    print('✅ CuPy版本:', cupy.__version__)
    print('   CuPy设备数量:', cupy.cuda.runtime.getDeviceCount())
except ImportError:
    print('⚠️  CuPy未安装 (仅CUDA系统需要)')
except Exception as e:
    print('⚠️  CuPy检查失败:', e)

# 检查cuDF
try:
    import cudf
    print('✅ cuDF版本:', cudf.__version__)
except ImportError:
    print('⚠️  cuDF未安装 (仅CUDA系统需要)')
except Exception as e:
    print('⚠️  cuDF检查失败:', e)

# 检查Numba
try:
    import numba
    print('✅ Numba版本:', numba.__version__)
    print('   CUDA支持:', numba.cuda.is_available())
except ImportError as e:
    print('❌ Numba导入失败:', e)
"
}

# 主安装流程
main() {
    echo "🎯 开始GPU加速库安装流程..."
    
    # 安装通用库
    install_common_libs
    
    # 根据系统类型安装特定库
    if check_apple_silicon; then
        install_apple_libs
    elif check_cuda; then
        install_cuda_libs
    else
        echo "⚠️  未检测到GPU环境，仅安装CPU优化库"
    fi
    
    # 验证安装
    verify_installation
    
    echo ""
    echo "🎉 GPU加速库安装完成！"
    echo ""
    echo "📝 使用说明:"
    echo "   1. 重启Python服务以使用新安装的库"
    echo "   2. 检查日志确认GPU加速已启用"
    echo "   3. 如有问题，请查看安装日志"
    echo ""
}

# 执行主函数
main "$@"
