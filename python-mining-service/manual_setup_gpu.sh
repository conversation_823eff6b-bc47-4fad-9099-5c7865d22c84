#!/bin/bash

# 手动GPU服务器设置脚本
# 在GPU服务器上运行此脚本来设置Python环境

set -e

echo "🚀 开始手动设置GPU环境..."

# 检查当前目录
if [ ! -f "requirements.txt" ]; then
    echo "❌ 错误: 请在包含requirements.txt的目录下运行此脚本"
    exit 1
fi

# 检查NVIDIA GPU
echo "🔍 检查NVIDIA GPU..."
if command -v nvidia-smi &> /dev/null; then
    nvidia-smi --query-gpu=name,memory.total,driver_version --format=csv,noheader,nounits
    echo "✅ NVIDIA GPU检测成功"
else
    echo "⚠️  nvidia-smi未找到，可能需要安装NVIDIA驱动"
fi

# 检查CUDA
echo "🔍 检查CUDA..."
if command -v nvcc &> /dev/null; then
    nvcc --version | grep "release"
    echo "✅ CUDA检测成功"
else
    echo "⚠️  CUDA未找到，可能需要安装CUDA toolkit"
fi

# 设置conda路径
export PATH="$HOME/miniconda3/bin:$PATH"

# 初始化conda
if [ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]; then
    source "$HOME/miniconda3/etc/profile.d/conda.sh"
else
    echo "❌ Conda未正确安装"
    exit 1
fi

# 创建简单的conda环境
ENV_NAME="promined-gpu"
echo "🔧 创建conda环境: $ENV_NAME"

if conda env list | grep -q "$ENV_NAME"; then
    echo "⚠️  环境已存在，删除旧环境..."
    conda env remove -n "$ENV_NAME" -y
fi

# 创建Python 3.11环境（兼容PyTorch）
echo "📋 创建Python 3.11环境..."
conda create -n "$ENV_NAME" python=3.11 -y

# 激活环境
echo "🔄 激活环境..."
conda activate "$ENV_NAME"

# 配置pip使用国内镜像源
echo "🌏 配置pip使用国内镜像源..."
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# 安装PyTorch with CUDA support (使用官方源)
echo "🔥 安装PyTorch with CUDA support..."
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# 安装基础依赖
echo "📦 安装基础依赖..."
pip install numpy pandas scikit-learn

# 安装其他依赖
echo "📦 安装项目依赖..."
pip install -r requirements.txt

# 安装GPU加速库
echo "🚀 安装GPU加速库..."
pip install numba[cuda]
pip install cupy-cuda12x

echo "✅ 环境设置完成！"
echo ""
echo "📝 测试GPU功能："
echo "   conda activate $ENV_NAME"
echo "   python test_gpu_features.py"
echo ""
echo "📝 启动服务："
echo "   python app/main.py"
