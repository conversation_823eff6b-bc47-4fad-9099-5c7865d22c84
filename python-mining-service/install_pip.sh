#!/bin/bash

# ProMined Python Mining Service - 纯pip安装脚本
# 适用于已有Python 3.12环境的情况

echo "🐍 ProMined Python Mining Service - Pip安装"
echo "=" * 50

# 检查Python版本
python_version=$(python3 --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+')
required_version="3.12"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ 需要Python 3.12+，当前版本: $python_version"
    echo "请先安装Python 3.12或使用conda创建环境"
    exit 1
fi

echo "✅ Python版本检查通过: $(python3 --version)"

# 创建虚拟环境
echo "📦 创建Python虚拟环境..."
python3 -m venv venv

# 激活虚拟环境
echo "🔄 激活虚拟环境..."
source venv/bin/activate

# 配置pip使用国内镜像源
echo "🌏 配置pip使用国内镜像源..."
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# 升级pip
echo "⬆️  升级pip..."
pip install --upgrade pip

# 安装PyTorch (苹果M芯片优化)
echo "🔥 安装PyTorch (苹果M芯片优化)..."
pip install torch torchvision torchaudio

# 安装其他依赖
echo "📦 安装项目依赖..."
pip install -r requirements.txt

# 验证安装
echo "🔍 验证安装..."
python3 -c "
import sys
print(f'Python版本: {sys.version}')

try:
    import torch
    print(f'PyTorch版本: {torch.__version__}')
    
    if torch.backends.mps.is_available():
        print('✅ 苹果M芯片GPU (MPS) 可用')
        device = torch.device('mps')
        print(f'设备: {device}')
        
        # 简单GPU测试
        x = torch.randn(5, 5).to(device)
        y = torch.randn(5, 5).to(device)
        z = torch.mm(x, y)
        print('✅ GPU计算测试通过')
    else:
        print('⚠️  MPS不可用，将使用CPU')
        device = torch.device('cpu')
        print(f'设备: {device}')
        
    import fastapi
    print(f'FastAPI版本: {fastapi.__version__}')
    
    import pandas as pd
    print(f'Pandas版本: {pd.__version__}')
    
    import numpy as np
    print(f'NumPy版本: {np.__version__}')
    
    print('✅ 所有关键库安装成功')
    
except ImportError as e:
    print(f'❌ 导入错误: {e}')
    sys.exit(1)
"

# 创建目录结构
echo "📁 创建项目目录..."
mkdir -p {logs,data,tests}

# 创建配置文件
if [ ! -f .env ]; then
    echo "📝 创建配置文件..."
    cp .env.example .env
fi

# 创建激活脚本
echo "📝 创建激活脚本..."
cat > activate.sh << 'EOF'
#!/bin/bash
# 激活ProMined Python Mining Service环境

echo "🔄 激活ProMined Python Mining Service环境..."
source venv/bin/activate
echo "✅ 环境已激活"
echo "💡 使用 'deactivate' 退出环境"
EOF

chmod +x activate.sh

echo ""
echo "✅ Pip安装完成！"
echo ""
echo "🎯 下一步操作:"
echo "1. 激活环境: source activate.sh"
echo "2. 编辑配置: vim .env"
echo "3. 启动服务: python app/main.py"
echo "4. 测试服务: python test_service.py"
echo ""
echo "💡 提示:"
echo "- 使用 'source activate.sh' 激活环境"
echo "- 使用 'deactivate' 退出环境"
echo "- 删除环境: rm -rf venv"
