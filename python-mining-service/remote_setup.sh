#!/bin/bash

# 远程GPU服务器设置脚本
# 在GPU服务器上运行此脚本来设置环境

echo "🚀 GPU服务器环境设置开始..."

# 检查当前目录
echo "📂 当前目录: $(pwd)"

# 检查NVIDIA GPU
echo "🔍 检查NVIDIA GPU..."
if command -v nvidia-smi &> /dev/null; then
    echo "✅ NVIDIA驱动已安装"
    nvidia-smi --query-gpu=name,memory.total,driver_version --format=csv,noheader,nounits
    echo ""
    nvidia-smi
else
    echo "❌ nvidia-smi未找到"
    echo "请安装NVIDIA驱动："
    echo "  sudo apt update"
    echo "  sudo apt install nvidia-driver-535"
    echo "  sudo reboot"
    exit 1
fi

# 检查CUDA
echo ""
echo "🔍 检查CUDA..."
if command -v nvcc &> /dev/null; then
    echo "✅ CUDA已安装"
    nvcc --version
else
    echo "⚠️  CUDA未找到，将安装CUDA 12.1..."
    
    # 下载并安装CUDA
    wget https://developer.download.nvidia.com/compute/cuda/12.1.1/local_installers/cuda_12.1.1_530.30.02_linux.run
    sudo sh cuda_12.1.1_530.30.02_linux.run --silent --toolkit
    
    # 添加CUDA到PATH
    echo 'export PATH=/usr/local/cuda-12.1/bin:$PATH' >> ~/.bashrc
    echo 'export LD_LIBRARY_PATH=/usr/local/cuda-12.1/lib64:$LD_LIBRARY_PATH' >> ~/.bashrc
    source ~/.bashrc
    
    echo "✅ CUDA安装完成"
fi

# 检查Python和conda
echo ""
echo "🐍 检查Python环境..."
if command -v conda &> /dev/null; then
    echo "✅ Conda已安装"
    conda --version
else
    echo "📥 安装Miniconda..."
    
    # 下载并安装Miniconda
    wget -q https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O miniconda.sh
    bash miniconda.sh -b -p $HOME/miniconda3
    rm miniconda.sh
    
    # 初始化conda
    $HOME/miniconda3/bin/conda init bash
    source ~/.bashrc
    
    echo "✅ Miniconda安装完成"
fi

# 确保conda可用
if [ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]; then
    source "$HOME/miniconda3/etc/profile.d/conda.sh"
fi

# 创建conda环境
ENV_NAME="promined-gpu"
echo ""
echo "🔧 创建conda环境: $ENV_NAME"

# 删除旧环境（如果存在）
if conda env list | grep -q "$ENV_NAME"; then
    echo "⚠️  环境已存在，删除旧环境..."
    conda env remove -n "$ENV_NAME" -y
fi

# 创建新环境
echo "📋 创建Python 3.12环境..."
conda create -n "$ENV_NAME" python=3.12 -y

# 激活环境
echo "🔄 激活环境..."
conda activate "$ENV_NAME"

# 安装PyTorch with CUDA support
echo ""
echo "🔥 安装PyTorch with CUDA support..."
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia -y

# 验证PyTorch CUDA
echo "🔍 验证PyTorch CUDA..."
python -c "
import torch
print('PyTorch版本:', torch.__version__)
print('CUDA可用:', torch.cuda.is_available())
if torch.cuda.is_available():
    print('CUDA版本:', torch.version.cuda)
    print('GPU数量:', torch.cuda.device_count())
    for i in range(torch.cuda.device_count()):
        print(f'GPU {i}:', torch.cuda.get_device_name(i))
"

# 配置pip使用国内镜像源
echo ""
echo "🌏 配置pip使用国内镜像源..."
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# 安装基础依赖
echo ""
echo "📦 安装基础依赖..."
pip install fastapi uvicorn pydantic pydantic-settings
pip install numpy pandas scipy scikit-learn
pip install networkx python-igraph
pip install pymysql sqlalchemy redis
pip install pm4py
pip install loguru psutil memory-profiler
pip install httpx aiohttp
pip install pytest pytest-asyncio
pip install numba

# 安装GPU加速库
echo ""
echo "🚀 安装GPU加速库..."

# 安装CuPy
echo "   安装CuPy for CUDA 12.x..."
pip install cupy-cuda12x

# 尝试安装RAPIDS cuDF
echo "   尝试安装RAPIDS cuDF..."
pip install --extra-index-url=https://pypi.nvidia.com cudf-cu12 || echo "   ⚠️  cuDF安装失败，继续..."

# 验证安装
echo ""
echo "🔍 验证安装..."
python -c "
print('=== 验证Python库 ===')

# PyTorch
try:
    import torch
    print('✅ PyTorch:', torch.__version__)
    print('   CUDA可用:', torch.cuda.is_available())
    if torch.cuda.is_available():
        print('   GPU数量:', torch.cuda.device_count())
        print('   当前GPU:', torch.cuda.get_device_name(0))
except Exception as e:
    print('❌ PyTorch错误:', e)

# CuPy
try:
    import cupy
    print('✅ CuPy:', cupy.__version__)
    print('   GPU数量:', cupy.cuda.runtime.getDeviceCount())
except Exception as e:
    print('❌ CuPy错误:', e)

# cuDF
try:
    import cudf
    print('✅ cuDF:', cudf.__version__)
except Exception as e:
    print('⚠️  cuDF不可用:', e)

# 其他库
try:
    import fastapi, pandas, numpy, loguru
    print('✅ 其他依赖库正常')
except Exception as e:
    print('❌ 依赖库错误:', e)

print('=== 验证完成 ===')
"

# 创建测试脚本
echo ""
echo "📝 创建快速测试脚本..."
cat > quick_test.py << 'EOF'
#!/usr/bin/env python3
import torch
import time

print("🧪 快速GPU测试")
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name(0)}")
    
    # 简单性能测试
    size = 5000
    device = torch.device('cuda')
    
    # 创建测试数据
    a = torch.randn(size, size, device=device)
    b = torch.randn(size, size, device=device)
    
    # 预热
    torch.mm(a, b)
    torch.cuda.synchronize()
    
    # 测试
    start = time.time()
    c = torch.mm(a, b)
    torch.cuda.synchronize()
    gpu_time = time.time() - start
    
    print(f"GPU矩阵乘法 ({size}x{size}): {gpu_time:.3f}秒")
    print("✅ GPU测试成功！")
else:
    print("❌ CUDA不可用")
EOF

chmod +x quick_test.py

echo ""
echo "🎉 GPU服务器环境设置完成！"
echo ""
echo "📝 下一步操作："
echo "   1. 激活环境: conda activate $ENV_NAME"
echo "   2. 快速测试: python quick_test.py"
echo "   3. 完整测试: python test_gpu_server.py"
echo "   4. 启动服务: python app/main.py"
echo ""
echo "💡 提示："
echo "   - 如果遇到CUDA问题，请重启系统"
echo "   - 确保NVIDIA驱动版本 >= 530"
echo "   - 检查CUDA环境变量是否正确设置"
echo ""
