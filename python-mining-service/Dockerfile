# ProMined Python Mining Service Dockerfile
# 支持苹果M芯片GPU加速的高性能子流程挖掘服务

FROM continuumio/miniconda3:latest

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV CONDA_ENV_NAME=promined-mining

# 复制环境配置文件
COPY environment.yml .

# 创建conda环境
RUN conda env create -f environment.yml && \
    conda clean -afy

# 激活环境
SHELL ["conda", "run", "-n", "promined-mining", "/bin/bash", "-c"]

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p logs data

# 设置权限
RUN chmod +x start_service.py

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD conda run -n promined-mining python -c "import requests; requests.get('http://localhost:8000/health')"

# 启动命令
CMD ["conda", "run", "--no-capture-output", "-n", "promined-mining", "python", "app/main.py"]
