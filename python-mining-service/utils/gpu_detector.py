"""
GPU环境检测和配置工具
支持CUDA、Apple MPS等多种GPU加速环境
"""

import os
import platform
import subprocess
import torch
from typing import Dict, Any, Optional, List, Tuple
from loguru import logger
from dataclasses import dataclass
from enum import Enum


class GPUType(Enum):
    """GPU类型枚举"""
    NONE = "none"
    CUDA = "cuda"
    MPS = "mps"
    OPENCL = "opencl"


@dataclass
class GPUInfo:
    """GPU信息数据类"""
    gpu_type: GPUType
    device_name: str
    memory_total: float  # GB
    memory_available: float  # GB
    compute_capability: Optional[str] = None
    driver_version: Optional[str] = None
    cuda_version: Optional[str] = None
    device_count: int = 1
    is_available: bool = False


class GPUDetector:
    """GPU环境检测器"""
    
    def __init__(self):
        self.system_info = self._get_system_info()
        self.gpu_info = self._detect_gpu_environment()
        
    def _get_system_info(self) -> Dict[str, str]:
        """获取系统信息"""
        return {
            'platform': platform.platform(),
            'system': platform.system(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'python_version': platform.python_version(),
            'architecture': platform.architecture()[0]
        }
    
    def _detect_gpu_environment(self) -> GPUInfo:
        """检测GPU环境"""
        logger.info("🔍 开始检测GPU环境...")
        
        # 检测CUDA
        cuda_info = self._detect_cuda()
        if cuda_info.is_available:
            logger.info(f"✅ 检测到CUDA GPU: {cuda_info.device_name}")
            return cuda_info
        
        # 检测Apple MPS
        mps_info = self._detect_mps()
        if mps_info.is_available:
            logger.info(f"✅ 检测到Apple MPS GPU: {mps_info.device_name}")
            return mps_info
        
        # 无GPU可用
        logger.warning("⚠️  未检测到可用的GPU，将使用CPU模式")
        return GPUInfo(
            gpu_type=GPUType.NONE,
            device_name="CPU",
            memory_total=0.0,
            memory_available=0.0,
            is_available=False
        )
    
    def _detect_cuda(self) -> GPUInfo:
        """检测CUDA环境"""
        try:
            if not torch.cuda.is_available():
                return GPUInfo(
                    gpu_type=GPUType.CUDA,
                    device_name="CUDA Not Available",
                    memory_total=0.0,
                    memory_available=0.0,
                    is_available=False
                )
            
            device_count = torch.cuda.device_count()
            device = torch.cuda.get_device_properties(0)
            
            # 获取内存信息
            memory_total = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            memory_allocated = torch.cuda.memory_allocated(0) / (1024**3)
            memory_available = memory_total - memory_allocated
            
            # 获取CUDA版本
            cuda_version = torch.version.cuda if torch.version.cuda else "Unknown"
            
            # 获取驱动版本
            driver_version = self._get_nvidia_driver_version()
            
            return GPUInfo(
                gpu_type=GPUType.CUDA,
                device_name=device.name,
                memory_total=memory_total,
                memory_available=memory_available,
                compute_capability=f"{device.major}.{device.minor}",
                driver_version=driver_version,
                cuda_version=cuda_version,
                device_count=device_count,
                is_available=True
            )
            
        except Exception as e:
            logger.warning(f"CUDA检测失败: {e}")
            return GPUInfo(
                gpu_type=GPUType.CUDA,
                device_name="CUDA Error",
                memory_total=0.0,
                memory_available=0.0,
                is_available=False
            )
    
    def _detect_mps(self) -> GPUInfo:
        """检测Apple MPS环境"""
        try:
            if not torch.backends.mps.is_available():
                return GPUInfo(
                    gpu_type=GPUType.MPS,
                    device_name="MPS Not Available",
                    memory_total=0.0,
                    memory_available=0.0,
                    is_available=False
                )
            
            # Apple MPS内存信息较难获取，使用系统内存作为估算
            import psutil
            memory = psutil.virtual_memory()
            # 假设GPU可用内存为系统内存的一部分
            estimated_gpu_memory = memory.total / (1024**3) * 0.3  # 30%作为估算
            
            device_name = self._get_apple_gpu_name()
            
            return GPUInfo(
                gpu_type=GPUType.MPS,
                device_name=device_name,
                memory_total=estimated_gpu_memory,
                memory_available=estimated_gpu_memory,
                compute_capability="MPS",
                driver_version=self._get_macos_version(),
                device_count=1,
                is_available=True
            )
            
        except Exception as e:
            logger.warning(f"MPS检测失败: {e}")
            return GPUInfo(
                gpu_type=GPUType.MPS,
                device_name="MPS Error",
                memory_total=0.0,
                memory_available=0.0,
                is_available=False
            )
    
    def _get_nvidia_driver_version(self) -> Optional[str]:
        """获取NVIDIA驱动版本"""
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=driver_version', '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                return result.stdout.strip()
        except Exception:
            pass
        return None
    
    def _get_apple_gpu_name(self) -> str:
        """获取Apple GPU名称"""
        try:
            # 尝试通过system_profiler获取GPU信息
            result = subprocess.run(['system_profiler', 'SPDisplaysDataType'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'Chipset Model:' in line:
                        return line.split(':')[1].strip()
        except Exception:
            pass
        
        # 根据机器类型推断
        machine = platform.machine()
        if 'arm64' in machine:
            return "Apple Silicon GPU"
        return "Apple GPU"
    
    def _get_macos_version(self) -> Optional[str]:
        """获取macOS版本"""
        try:
            return platform.mac_ver()[0]
        except Exception:
            return None
    
    def get_optimal_device(self, prefer_gpu: bool = True) -> torch.device:
        """获取最优设备"""
        if not prefer_gpu or not self.gpu_info.is_available:
            return torch.device("cpu")
        
        if self.gpu_info.gpu_type == GPUType.CUDA:
            return torch.device("cuda")
        elif self.gpu_info.gpu_type == GPUType.MPS:
            return torch.device("mps")
        else:
            return torch.device("cpu")
    
    def get_device_info(self) -> Dict[str, Any]:
        """获取设备信息摘要"""
        return {
            'gpu_available': self.gpu_info.is_available,
            'gpu_type': self.gpu_info.gpu_type.value,
            'device_name': self.gpu_info.device_name,
            'memory_total_gb': round(self.gpu_info.memory_total, 2),
            'memory_available_gb': round(self.gpu_info.memory_available, 2),
            'compute_capability': self.gpu_info.compute_capability,
            'driver_version': self.gpu_info.driver_version,
            'cuda_version': self.gpu_info.cuda_version,
            'device_count': self.gpu_info.device_count,
            'system_info': self.system_info
        }
    
    def check_gpu_requirements(self, min_memory_gb: float = 2.0) -> Tuple[bool, List[str]]:
        """检查GPU要求"""
        issues = []
        
        if not self.gpu_info.is_available:
            issues.append("GPU不可用")
            return False, issues
        
        if self.gpu_info.memory_available < min_memory_gb:
            issues.append(f"GPU内存不足，需要至少{min_memory_gb}GB，当前可用{self.gpu_info.memory_available:.1f}GB")
        
        if self.gpu_info.gpu_type == GPUType.CUDA:
            # 检查CUDA版本兼容性
            if self.gpu_info.cuda_version and self.gpu_info.cuda_version < "11.0":
                issues.append(f"CUDA版本过低，建议11.0+，当前{self.gpu_info.cuda_version}")
        
        return len(issues) == 0, issues
    
    def get_memory_info(self) -> Dict[str, float]:
        """获取GPU内存信息"""
        if not self.gpu_info.is_available:
            return {'total': 0.0, 'available': 0.0, 'used': 0.0, 'usage_percent': 0.0}
        
        if self.gpu_info.gpu_type == GPUType.CUDA:
            try:
                total = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                allocated = torch.cuda.memory_allocated(0) / (1024**3)
                cached = torch.cuda.memory_reserved(0) / (1024**3)
                available = total - allocated
                usage_percent = (allocated / total) * 100
                
                return {
                    'total': total,
                    'available': available,
                    'used': allocated,
                    'cached': cached,
                    'usage_percent': usage_percent
                }
            except Exception:
                pass
        
        # MPS或其他GPU类型的简化处理
        return {
            'total': self.gpu_info.memory_total,
            'available': self.gpu_info.memory_available,
            'used': self.gpu_info.memory_total - self.gpu_info.memory_available,
            'usage_percent': ((self.gpu_info.memory_total - self.gpu_info.memory_available) / self.gpu_info.memory_total) * 100 if self.gpu_info.memory_total > 0 else 0.0
        }
    
    def cleanup_gpu_memory(self):
        """清理GPU内存"""
        try:
            if self.gpu_info.gpu_type == GPUType.CUDA and torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                logger.info("🧹 CUDA内存缓存已清理")
            elif self.gpu_info.gpu_type == GPUType.MPS and torch.backends.mps.is_available():
                torch.mps.empty_cache()
                logger.info("🧹 MPS内存缓存已清理")
        except Exception as e:
            logger.warning(f"GPU内存清理失败: {e}")


# 全局GPU检测器实例
gpu_detector = GPUDetector()
