"""
性能监控工具
监控内存、CPU、GPU使用情况
支持CUDA和Apple MPS GPU加速
"""

import gc
import time
import psutil
import torch
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
from loguru import logger
import threading
import asyncio
import platform
import subprocess


class PerformanceMonitor:
    """性能监控器"""

    def __init__(self, gpu_memory_limit_percent: float = 85.0):
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.stats_history = []
        self.max_history_size = 100
        self.gpu_memory_limit_percent = gpu_memory_limit_percent
        self.gpu_memory_warnings = 0
        self.last_gpu_cleanup = None
        
    def start_monitoring(self, interval: float = 30.0):
        """开始性能监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        logger.info(f"📊 性能监控已启动，监控间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        logger.info("📊 性能监控已停止")
    
    def _monitor_loop(self, interval: float):
        """监控循环"""
        while self.monitoring:
            try:
                stats = self.get_current_stats()
                self.stats_history.append(stats)
                
                # 限制历史记录大小
                if len(self.stats_history) > self.max_history_size:
                    self.stats_history.pop(0)
                
                # 检查内存使用情况
                if stats['memory_percent'] > 85:
                    logger.warning(f"⚠️  内存使用率过高: {stats['memory_percent']:.1f}%")
                    self.cleanup_memory()

                # 检查CPU使用情况
                if stats['cpu_percent'] > 90:
                    logger.warning(f"⚠️  CPU使用率过高: {stats['cpu_percent']:.1f}%")

                # 检查GPU内存使用情况
                if stats.get('gpu_available', False):
                    gpu_memory_percent = stats.get('gpu_memory_usage_percent', 0.0)
                    if gpu_memory_percent > self.gpu_memory_limit_percent:
                        self.gpu_memory_warnings += 1
                        logger.warning(f"⚠️  GPU内存使用率过高: {gpu_memory_percent:.1f}%")

                        # 如果连续警告，执行GPU内存清理
                        if self.gpu_memory_warnings >= 2:
                            self._cleanup_gpu_memory_if_needed()
                            self.gpu_memory_warnings = 0
                    else:
                        self.gpu_memory_warnings = 0
                
                time.sleep(interval)
                
            except Exception as e:
                logger.error(f"❌ 性能监控错误: {e}")
                time.sleep(interval)
    
    def get_current_stats(self) -> Dict[str, Any]:
        """获取当前性能统计"""
        memory = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=1)
        
        stats = {
            'timestamp': datetime.now(),
            'memory_percent': memory.percent,
            'memory_used_gb': round(memory.used / (1024**3), 2),
            'memory_available_gb': round(memory.available / (1024**3), 2),
            'memory_total_gb': round(memory.total / (1024**3), 2),
            'cpu_percent': cpu_percent,
            'cpu_count': psutil.cpu_count(),
        }
        
        # GPU信息（使用GPU检测器）
        try:
            from utils.gpu_detector import gpu_detector
            gpu_info = gpu_detector.get_device_info()
            memory_info = gpu_detector.get_memory_info()

            stats.update({
                'gpu_available': gpu_info['gpu_available'],
                'gpu_type': gpu_info['gpu_type'],
                'gpu_device': gpu_info['device_name'],
                'gpu_memory_total_gb': gpu_info['memory_total_gb'],
                'gpu_memory_available_gb': memory_info['available'],
                'gpu_memory_usage_percent': memory_info['usage_percent'],
                'torch_version': torch.__version__,
                'compute_capability': gpu_info.get('compute_capability'),
                'driver_version': gpu_info.get('driver_version')
            })
        except Exception as e:
            logger.warning(f"GPU信息获取失败: {e}")
            stats.update({
                'gpu_available': False,
                'gpu_device': 'Error',
                'torch_version': torch.__version__,
            })
        
        return stats
    
    def get_current_time(self) -> datetime:
        """获取当前时间"""
        return datetime.now()
    
    def cleanup_memory(self):
        """清理内存"""
        try:
            # Python垃圾回收
            collected = gc.collect()

            # GPU内存清理（使用GPU检测器）
            try:
                from utils.gpu_detector import gpu_detector
                gpu_detector.cleanup_gpu_memory()
            except Exception as gpu_e:
                logger.warning(f"GPU内存清理失败: {gpu_e}")
                # 回退到原有方式
                if torch.backends.mps.is_available():
                    torch.mps.empty_cache()
                elif torch.cuda.is_available():
                    torch.cuda.empty_cache()

            logger.info(f"🧹 内存清理完成，回收对象: {collected}")

        except Exception as e:
            logger.error(f"❌ 内存清理失败: {e}")
    
    def get_memory_usage_mb(self) -> float:
        """获取当前内存使用量(MB)"""
        memory = psutil.virtual_memory()
        return memory.used / (1024 * 1024)
    
    def get_memory_usage_percent(self) -> float:
        """获取内存使用百分比"""
        return psutil.virtual_memory().percent
    
    def log_performance_summary(self):
        """记录性能摘要"""
        if not self.stats_history:
            return
        
        recent_stats = self.stats_history[-10:]  # 最近10次记录
        
        avg_memory = sum(s['memory_percent'] for s in recent_stats) / len(recent_stats)
        avg_cpu = sum(s['cpu_percent'] for s in recent_stats) / len(recent_stats)
        
        max_memory = max(s['memory_percent'] for s in recent_stats)
        max_cpu = max(s['cpu_percent'] for s in recent_stats)
        
        logger.info("📈 性能摘要:")
        logger.info(f"   平均内存使用: {avg_memory:.1f}%")
        logger.info(f"   平均CPU使用: {avg_cpu:.1f}%")
        logger.info(f"   峰值内存使用: {max_memory:.1f}%")
        logger.info(f"   峰值CPU使用: {max_cpu:.1f}%")
    
    def check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源状态"""
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        # 获取GPU状态
        gpu_status = 'unavailable'
        gpu_memory_percent = 0.0
        try:
            from utils.gpu_detector import gpu_detector
            if gpu_detector.gpu_info.is_available:
                memory_info = gpu_detector.get_memory_info()
                gpu_memory_percent = memory_info['usage_percent']
                gpu_status = 'healthy' if gpu_memory_percent < 80 else 'warning' if gpu_memory_percent < 90 else 'critical'
            else:
                gpu_status = 'unavailable'
        except Exception:
            gpu_status = 'error'

        return {
            'memory_status': 'healthy' if memory.percent < 80 else 'warning' if memory.percent < 90 else 'critical',
            'disk_status': 'healthy' if disk.percent < 80 else 'warning' if disk.percent < 90 else 'critical',
            'cpu_status': 'healthy',  # 简化处理
            'gpu_status': gpu_status,
            'gpu_memory_percent': gpu_memory_percent,
            'recommendations': self._get_recommendations(memory.percent, disk.percent, gpu_memory_percent)
        }
    
    def _get_recommendations(self, memory_percent: float, disk_percent: float, gpu_memory_percent: float = 0.0) -> List[str]:
        """获取性能优化建议"""
        recommendations = []

        if memory_percent > 85:
            recommendations.append("建议清理内存或减少数据处理批次大小")

        if disk_percent > 85:
            recommendations.append("建议清理磁盘空间")

        if gpu_memory_percent > 85:
            recommendations.append("建议清理GPU内存或减少GPU批处理大小")

        try:
            from utils.gpu_detector import gpu_detector
            if not gpu_detector.gpu_info.is_available:
                recommendations.append("建议在支持GPU加速的设备上运行以获得更好性能")
            elif gpu_detector.gpu_info.gpu_type.value == 'cuda':
                recommendations.append("CUDA GPU已启用，性能最佳")
            elif gpu_detector.gpu_info.gpu_type.value == 'mps':
                recommendations.append("Apple MPS GPU已启用，性能良好")
        except Exception:
            recommendations.append("建议检查GPU环境配置")

        if not recommendations:
            recommendations.append("系统资源状态良好")

        return recommendations
    
    def _cleanup_gpu_memory_if_needed(self):
        """根据需要清理GPU内存"""
        from datetime import datetime, timedelta

        # 避免频繁清理，至少间隔30秒
        now = datetime.now()
        if self.last_gpu_cleanup and (now - self.last_gpu_cleanup) < timedelta(seconds=30):
            return

        try:
            from utils.gpu_detector import gpu_detector
            memory_info_before = gpu_detector.get_memory_info()

            logger.info(f"🧹 开始GPU内存清理，当前使用: {memory_info_before['usage_percent']:.1f}%")

            # 执行GPU内存清理
            gpu_detector.cleanup_gpu_memory()

            # 强制Python垃圾回收
            import gc
            gc.collect()

            # 检查清理效果
            memory_info_after = gpu_detector.get_memory_info()
            freed_memory = memory_info_before['used'] - memory_info_after['used']

            logger.info(f"✅ GPU内存清理完成，释放: {freed_memory:.2f}GB，当前使用: {memory_info_after['usage_percent']:.1f}%")

            self.last_gpu_cleanup = now

        except Exception as e:
            logger.error(f"❌ GPU内存清理失败: {e}")

    def get_gpu_memory_status(self) -> Dict[str, Any]:
        """获取GPU内存状态"""
        try:
            from utils.gpu_detector import gpu_detector
            if not gpu_detector.gpu_info.is_available:
                return {'available': False, 'message': 'GPU不可用'}

            memory_info = gpu_detector.get_memory_info()
            device_info = gpu_detector.get_device_info()

            status = 'healthy'
            if memory_info['usage_percent'] > 90:
                status = 'critical'
            elif memory_info['usage_percent'] > self.gpu_memory_limit_percent:
                status = 'warning'

            return {
                'available': True,
                'status': status,
                'usage_percent': memory_info['usage_percent'],
                'total_gb': memory_info['total'],
                'used_gb': memory_info['used'],
                'available_gb': memory_info['available'],
                'device_name': device_info['device_name'],
                'gpu_type': device_info['gpu_type'],
                'warning_threshold': self.gpu_memory_limit_percent,
                'warnings_count': self.gpu_memory_warnings
            }

        except Exception as e:
            return {'available': False, 'error': str(e)}

    def set_gpu_memory_limit(self, limit_percent: float):
        """设置GPU内存使用限制"""
        if 50.0 <= limit_percent <= 95.0:
            self.gpu_memory_limit_percent = limit_percent
            logger.info(f"🎯 GPU内存限制已设置为: {limit_percent}%")
        else:
            logger.warning(f"⚠️  GPU内存限制值无效: {limit_percent}%，应在50-95%之间")

    async def async_cleanup_memory(self):
        """异步内存清理"""
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, self.cleanup_memory)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        current_stats = self.get_current_stats()
        resource_status = self.check_system_resources()
        gpu_status = self.get_gpu_memory_status()

        return {
            'current_stats': current_stats,
            'resource_status': resource_status,
            'gpu_memory_status': gpu_status,
            'history_count': len(self.stats_history),
            'monitoring_active': self.monitoring,
            'gpu_memory_limit_percent': self.gpu_memory_limit_percent
        }

    def force_gpu_memory_cleanup(self) -> Dict[str, Any]:
        """强制GPU内存清理"""
        try:
            from utils.gpu_detector import gpu_detector
            if not gpu_detector.gpu_info.is_available:
                return {'success': False, 'message': 'GPU不可用'}

            memory_before = gpu_detector.get_memory_info()

            # 执行清理
            gpu_detector.cleanup_gpu_memory()
            import gc
            gc.collect()

            memory_after = gpu_detector.get_memory_info()
            freed_memory = memory_before['used'] - memory_after['used']

            return {
                'success': True,
                'memory_before': memory_before,
                'memory_after': memory_after,
                'freed_gb': round(freed_memory, 2),
                'improvement_percent': round((freed_memory / memory_before['used']) * 100, 1) if memory_before['used'] > 0 else 0
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def get_gpu_recommendations(self) -> List[str]:
        """获取GPU优化建议"""
        recommendations = []

        try:
            gpu_status = self.get_gpu_memory_status()

            if not gpu_status['available']:
                recommendations.append("建议在支持GPU的环境中运行以获得更好性能")
                return recommendations

            usage_percent = gpu_status['usage_percent']

            if usage_percent > 90:
                recommendations.append("GPU内存使用率过高，建议减少批处理大小或清理内存")
            elif usage_percent > self.gpu_memory_limit_percent:
                recommendations.append(f"GPU内存使用率超过阈值({self.gpu_memory_limit_percent}%)，建议优化内存使用")
            elif usage_percent < 30:
                recommendations.append("GPU内存使用率较低，可以考虑增加批处理大小以提高性能")
            else:
                recommendations.append("GPU内存使用率正常")

            if gpu_status['gpu_type'] == 'cuda':
                recommendations.append("CUDA GPU性能最佳，建议启用所有GPU加速功能")
            elif gpu_status['gpu_type'] == 'mps':
                recommendations.append("Apple MPS GPU性能良好，适合中等规模数据处理")

        except Exception as e:
            recommendations.append(f"GPU状态检查失败: {e}")

        return recommendations
