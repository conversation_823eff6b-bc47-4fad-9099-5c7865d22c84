# ProMined Python Mining Service Requirements
# 最小化依赖列表，适用于pip安装

# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 数据处理和科学计算
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0

# 机器学习 (支持苹果M芯片和CUDA)
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0
scikit-learn>=1.3.0

# GPU加速库 (条件安装)
# CuPy for CUDA acceleration (install with: pip install cupy-cuda11x or cupy-cuda12x)
# cupy-cuda11x>=12.0.0; sys_platform != "darwin"
# cupy-cuda12x>=12.0.0; sys_platform != "darwin"

# RAPIDS cuDF for GPU-accelerated dataframes (CUDA only)
# cudf-cu11>=23.0.0; sys_platform != "darwin"
# cudf-cu12>=23.0.0; sys_platform != "darwin"

# Numba for JIT compilation with GPU support
numba>=0.58.0

# 图算法和网络分析
networkx>=3.0
python-igraph>=0.11.0

# 数据库连接
pymysql==1.1.0
sqlalchemy==2.0.23
redis==5.0.1

# 进程挖掘专用库
pm4py>=2.7.0

# 并行处理和系统监控
joblib>=1.3.0
psutil>=5.9.0

# 日志和监控
loguru==0.7.2

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1

# 开发和测试
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0

# 性能分析
memory-profiler>=0.61.0

# 数据序列化
orjson>=3.9.0
msgpack>=1.0.0
