#!/usr/bin/env python3
"""
GPU功能测试脚本
测试GPU检测、内存管理、加速算法等功能
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import torch
from loguru import logger
from datetime import datetime, timedelta
import random

from utils.gpu_detector import gpu_detector
from utils.performance_monitor import PerformanceMonitor
from config.settings import Settings
from services.gpu_accelerated_mining import GPUAcceleratedMining
from models.mining_models import (
    SubprocessDiscoveryRequest,
    SubprocessDiscoveryOptions,
    EventLog
)


def test_gpu_detection():
    """测试GPU检测功能"""
    print("🔍 测试GPU检测功能...")
    
    # 获取设备信息
    device_info = gpu_detector.get_device_info()
    print(f"   GPU可用: {device_info['gpu_available']}")
    print(f"   GPU类型: {device_info['gpu_type']}")
    print(f"   设备名称: {device_info['device_name']}")
    print(f"   内存总量: {device_info['memory_total_gb']}GB")
    
    # 检查GPU要求
    gpu_ok, issues = gpu_detector.check_gpu_requirements()
    print(f"   GPU要求检查: {'✅ 通过' if gpu_ok else '❌ 失败'}")
    if issues:
        for issue in issues:
            print(f"     - {issue}")
    
    # 获取最优设备
    optimal_device = gpu_detector.get_optimal_device()
    print(f"   最优设备: {optimal_device}")
    
    return device_info['gpu_available']


def test_gpu_memory_management():
    """测试GPU内存管理"""
    print("\n💾 测试GPU内存管理...")
    
    if not gpu_detector.gpu_info.is_available:
        print("   跳过测试 - GPU不可用")
        return
    
    # 获取内存信息
    memory_info = gpu_detector.get_memory_info()
    print(f"   内存使用率: {memory_info['usage_percent']:.1f}%")
    print(f"   已用内存: {memory_info['used']:.2f}GB")
    print(f"   可用内存: {memory_info['available']:.2f}GB")
    
    # 测试内存清理
    print("   执行内存清理...")
    gpu_detector.cleanup_gpu_memory()
    
    # 再次检查内存
    memory_info_after = gpu_detector.get_memory_info()
    print(f"   清理后使用率: {memory_info_after['usage_percent']:.1f}%")


def test_performance_monitor():
    """测试性能监控器"""
    print("\n📊 测试性能监控器...")
    
    monitor = PerformanceMonitor(gpu_memory_limit_percent=80.0)
    
    # 获取当前统计
    stats = monitor.get_current_stats()
    print(f"   CPU使用率: {stats['cpu_percent']:.1f}%")
    print(f"   内存使用率: {stats['memory_percent']:.1f}%")
    print(f"   GPU可用: {stats.get('gpu_available', False)}")
    
    if stats.get('gpu_available'):
        print(f"   GPU类型: {stats.get('gpu_type', 'unknown')}")
        print(f"   GPU内存使用率: {stats.get('gpu_memory_usage_percent', 0):.1f}%")
    
    # 测试GPU内存状态
    gpu_status = monitor.get_gpu_memory_status()
    print(f"   GPU内存状态: {gpu_status}")
    
    # 获取GPU建议
    recommendations = monitor.get_gpu_recommendations()
    print("   GPU优化建议:")
    for rec in recommendations:
        print(f"     - {rec}")


def generate_test_data(size: int = 1000) -> list:
    """生成测试数据"""
    activities = [
        "原材料采购申请", "供应商资质审核", "采购合同签署",
        "原材料入库检验", "入库确认", "生产准备",
        "首件检验", "批量生产", "过程检验",
        "成品检验", "包装", "出库检验"
    ]
    
    event_logs = []
    base_time = datetime.now() - timedelta(days=30)
    
    for i in range(size):
        case_id = f"case_{i // 10}"  # 每10个事件一个案例
        activity = random.choice(activities)
        timestamp = base_time + timedelta(
            days=random.randint(0, 30),
            hours=random.randint(0, 23),
            minutes=random.randint(0, 59)
        )
        
        event_logs.append(EventLog(
            id=i,
            case_id=case_id,
            activity=activity,
            timestamp=timestamp,
            resource=f"resource_{random.randint(1, 5)}",
            cost=random.uniform(100, 1000)
        ))
    
    return event_logs


async def test_gpu_mining():
    """测试GPU加速挖掘"""
    print("\n🚀 测试GPU加速挖掘...")
    
    # 创建配置
    settings = Settings()
    gpu_config = settings.get_gpu_config()
    
    # 获取设备
    device = gpu_detector.get_optimal_device(prefer_gpu=True)
    print(f"   使用设备: {device}")
    
    # 创建GPU挖掘服务
    gpu_mining = GPUAcceleratedMining(device=device, config=gpu_config)
    
    # 生成测试数据
    test_sizes = [100, 500, 1000]
    
    for size in test_sizes:
        print(f"\n   测试数据量: {size}")
        
        event_logs = generate_test_data(size)
        
        request = SubprocessDiscoveryRequest(
            process_id=f"test_process_{size}",
            event_logs=event_logs,
            options=SubprocessDiscoveryOptions(
                min_frequency=2,
                min_confidence=0.1,
                max_pattern_length=5
            )
        )
        
        # 执行挖掘
        start_time = datetime.now()
        try:
            result = await gpu_mining.discover_subprocesses_gpu(request)
            end_time = datetime.now()
            
            processing_time = (end_time - start_time).total_seconds()
            
            print(f"     处理时间: {processing_time:.2f}秒")
            print(f"     发现子流程: {len(result.subprocesses)}个")
            print(f"     处理速度: {size/processing_time:.0f}条/秒")
            
            if result.subprocesses:
                top_pattern = result.subprocesses[0]
                print(f"     最频繁模式: {top_pattern.name} (频率: {top_pattern.frequency})")
            
        except Exception as e:
            print(f"     ❌ 测试失败: {e}")


def test_config_validation():
    """测试配置验证"""
    print("\n⚙️  测试配置验证...")
    
    settings = Settings()
    
    # 验证GPU配置
    issues = settings.validate_gpu_config()
    if issues:
        print("   配置问题:")
        for issue in issues:
            print(f"     - {issue}")
    else:
        print("   ✅ 配置验证通过")
    
    # 显示GPU配置
    gpu_config = settings.get_gpu_config()
    print("   GPU配置:")
    for key, value in gpu_config.items():
        print(f"     {key}: {value}")


async def main():
    """主测试函数"""
    print("🧪 开始GPU功能测试\n")
    
    # 1. 测试GPU检测
    gpu_available = test_gpu_detection()
    
    # 2. 测试GPU内存管理
    test_gpu_memory_management()
    
    # 3. 测试性能监控
    test_performance_monitor()
    
    # 4. 测试配置验证
    test_config_validation()
    
    # 5. 测试GPU挖掘（如果GPU可用）
    if gpu_available:
        await test_gpu_mining()
    else:
        print("\n⚠️  跳过GPU挖掘测试 - GPU不可用")
    
    print("\n🎉 GPU功能测试完成！")


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
        level="INFO"
    )
    
    # 运行测试
    asyncio.run(main())
