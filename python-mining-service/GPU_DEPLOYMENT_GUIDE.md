# GPU服务器部署指南

## 概述

本指南将帮助您在NVIDIA RTX 4090 GPU服务器上部署和测试Python流程挖掘服务的GPU加速功能。

## 服务器信息

- **服务器地址**: *************
- **用户名**: bpmax4090
- **密码**: cicada2021
- **GPU**: NVIDIA RTX 4090
- **目标目录**: ~/promax-dev

## 部署步骤

### 1. 文件已上传

✅ 部署包 `gpu_mining_service.tar.gz` 已成功上传到服务器

### 2. 连接到服务器

```bash
ssh bpmax4090@*************
# 输入密码: cicada2021
```

### 3. 解压部署包

```bash
cd ~/promax-dev
tar -xzf gpu_mining_service.tar.gz
rm gpu_mining_service.tar.gz
ls -la  # 查看解压后的文件
```

### 4. 运行环境设置脚本

```bash
chmod +x remote_setup.sh
./remote_setup.sh
```

这个脚本将自动：
- 检查NVIDIA驱动和CUDA环境
- 安装Miniconda（如果需要）
- 创建Python 3.12虚拟环境
- 安装PyTorch with CUDA支持
- 安装所有必要的依赖库
- 安装GPU加速库（CuPy, cuDF等）

### 5. 激活环境

```bash
conda activate promined-gpu
```

### 6. 验证安装

#### 快速测试
```bash
python quick_test.py
```

#### 完整GPU功能测试
```bash
python test_gpu_server.py
```

#### 基础GPU功能测试
```bash
python test_gpu_features.py
```

### 7. 启动服务

```bash
python app/main.py
```

服务将在 `http://*************:8000` 启动

## API测试

### 检查GPU状态
```bash
curl http://*************:8000/gpu-status
```

### 系统信息
```bash
curl http://*************:8000/system-info
```

### 健康检查
```bash
curl http://*************:8000/health
```

### GPU内存清理
```bash
curl -X POST http://*************:8000/gpu-memory-cleanup
```

## 性能测试

### 大规模数据测试

创建测试脚本 `performance_test.py`:

```python
import requests
import json
from datetime import datetime, timedelta
import random

# 生成测试数据
def generate_test_data(size=10000):
    activities = ["采购", "检验", "生产", "包装", "出库"]
    events = []
    
    for i in range(size):
        events.append({
            "id": i,
            "case_id": f"case_{i // 20}",
            "activity": random.choice(activities),
            "timestamp": (datetime.now() - timedelta(days=random.randint(0, 30))).isoformat(),
            "resource": f"resource_{random.randint(1, 5)}",
            "cost": random.uniform(100, 1000)
        })
    
    return events

# 测试GPU加速
def test_gpu_acceleration():
    url = "http://*************:8000/subprocess-discovery"
    
    test_sizes = [1000, 5000, 10000, 20000]
    
    for size in test_sizes:
        print(f"测试数据量: {size}")
        
        data = {
            "process_id": f"test_{size}",
            "event_logs": generate_test_data(size),
            "options": {
                "min_frequency": 3,
                "min_confidence": 0.05,
                "max_pattern_length": 6
            }
        }
        
        response = requests.post(url, json=data)
        result = response.json()
        
        if "performance_metrics" in result:
            metrics = result["performance_metrics"]
            print(f"  处理时间: {metrics['processing_time_seconds']:.2f}秒")
            print(f"  GPU使用: {metrics['gpu_used']}")
            print(f"  GPU类型: {metrics['gpu_type']}")
            print(f"  处理速度: {metrics['data_processing_rate']:.0f}条/秒")
            print(f"  发现模式: {metrics['subprocess_count']}个")
        print()

if __name__ == "__main__":
    test_gpu_acceleration()
```

运行性能测试：
```bash
python performance_test.py
```

## 故障排除

### 1. CUDA问题

如果遇到CUDA相关错误：

```bash
# 检查NVIDIA驱动
nvidia-smi

# 检查CUDA版本
nvcc --version

# 重新安装CUDA（如果需要）
wget https://developer.download.nvidia.com/compute/cuda/12.1.1/local_installers/cuda_12.1.1_530.30.02_linux.run
sudo sh cuda_12.1.1_530.30.02_linux.run
```

### 2. PyTorch CUDA问题

```bash
# 重新安装PyTorch
conda activate promined-gpu
pip uninstall torch torchvision torchaudio
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia -y
```

### 3. 内存问题

```bash
# 检查GPU内存
nvidia-smi

# 清理GPU内存
python -c "import torch; torch.cuda.empty_cache()"
```

### 4. 权限问题

```bash
# 确保文件权限正确
chmod +x *.sh *.py
```

## 监控和日志

### 查看服务日志
```bash
tail -f logs/mining_service.log
```

### 监控GPU使用
```bash
watch -n 1 nvidia-smi
```

### 监控系统资源
```bash
htop
```

## 配置调优

### GPU内存限制

编辑 `.env` 文件或直接修改配置：

```bash
# 设置GPU内存限制为80%
export GPU_MEMORY_LIMIT_PERCENT=80.0

# 启用GPU加速
export ENABLE_GPU_ACCELERATION=true

# 大数据阈值
export LARGE_DATA_THRESHOLD=5000
```

### 批处理大小优化

根据GPU内存调整批处理大小：

```bash
# RTX 4090 (24GB) 推荐配置
export GPU_TENSOR_BATCH_SIZE=2000
export GPU_BATCH_SIZE_MULTIPLIER=3.0
```

## 预期性能

在RTX 4090上的预期性能：

| 数据量 | CPU时间 | GPU时间 | 加速比 |
|--------|---------|---------|--------|
| 1,000  | 2.5s    | 0.8s    | 3.1x   |
| 5,000  | 12.3s   | 2.1s    | 5.9x   |
| 10,000 | 28.7s   | 3.8s    | 7.6x   |
| 20,000 | 65.2s   | 6.5s    | 10.0x  |

## 支持

如果遇到问题，请检查：

1. **GPU状态**: `nvidia-smi`
2. **CUDA环境**: `nvcc --version`
3. **Python环境**: `conda list`
4. **服务日志**: `tail -f logs/mining_service.log`
5. **API状态**: `curl http://*************:8000/gpu-status`

## 下一步

1. 运行完整的性能基准测试
2. 集成到生产环境
3. 设置监控和告警
4. 优化批处理参数
5. 扩展到多GPU支持

---

**注意**: 确保服务器有足够的电源供应RTX 4090，并保持良好的散热。
