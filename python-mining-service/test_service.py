#!/usr/bin/env python3
"""
Python Mining Service 测试脚本
"""

import asyncio
import json
from datetime import datetime, timedelta
import requests
import time

def generate_test_data():
    """生成测试数据"""
    base_time = datetime.now()
    
    # 制造业流程测试数据
    test_cases = [
        # 案例1: 正常采购流程
        [
            ("原材料采购申请", 0),
            ("供应商资质审核", 30),
            ("采购合同签署", 60),
            ("原材料入库检验", 120),
            ("入库确认", 150),
        ],
        # 案例2: 生产流程
        [
            ("生产准备", 0),
            ("首件检验", 45),
            ("批量生产", 90),
            ("过程检验", 180),
            ("成品检验", 240),
            ("包装", 270),
            ("出库检验", 300),
        ],
        # 案例3: 质量问题处理流程
        [
            ("原材料入库检验", 0),
            ("不合格品处理", 30),
            ("供应商整改通知", 60),
            ("重新检验", 120),
            ("入库确认", 150),
        ],
        # 案例4: 设备维护流程
        [
            ("设备维护", 0),
            ("设备检验", 60),
            ("设备验收", 90),
        ],
        # 案例5: 复杂质量流程
        [
            ("生产准备", 0),
            ("首件检验", 30),
            ("批量生产", 60),
            ("过程检验", 120),
            ("返工处理", 150),
            ("重新检验", 180),
            ("成品检验", 210),
            ("包装", 240),
        ],
    ]
    
    event_logs = []
    event_id = 1
    
    # 生成多个案例实例
    for case_idx in range(50):  # 生成50个案例
        case_template = test_cases[case_idx % len(test_cases)]
        case_id = f"case_{case_idx + 1}"
        
        for activity, offset_minutes in case_template:
            timestamp = base_time + timedelta(minutes=offset_minutes + case_idx * 360)  # 每个案例间隔6小时
            
            event_logs.append({
                "id": event_id,
                "case_id": case_id,
                "activity": activity,
                "timestamp": timestamp.isoformat(),
                "resource": f"资源_{(event_id % 5) + 1}",
                "cost": round(100 + (event_id % 500), 2),
                "attributes": {"department": "制造部"}
            })
            event_id += 1
    
    return event_logs

def test_health_check():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get("http://localhost:8001/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查通过")
            print(f"   状态: {data['status']}")
            print(f"   内存使用: {data['memory_usage_percent']:.1f}%")
            print(f"   CPU使用: {data['cpu_usage_percent']:.1f}%")
            print(f"   GPU可用: {data['gpu_available']}")
            print(f"   设备: {data['device']}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_system_info():
    """测试系统信息"""
    print("\n🖥️  测试系统信息...")
    try:
        response = requests.get("http://localhost:8001/system-info", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 系统信息获取成功")
            print(f"   Python版本: {data['python_version']}")
            print(f"   PyTorch版本: {data['torch_version']}")
            print(f"   计算设备: {data['device']}")
            print(f"   CPU核心数: {data['cpu_count']}")
            print(f"   总内存: {data['memory_total_gb']}GB")
            print(f"   可用内存: {data['memory_available_gb']}GB")
            return True
        else:
            print(f"❌ 系统信息获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 系统信息获取异常: {e}")
        return False

def test_subprocess_discovery():
    """测试子流程发现"""
    print("\n🔍 测试子流程发现...")
    
    # 生成测试数据
    event_logs = generate_test_data()
    print(f"📊 生成测试数据: {len(event_logs)} 条事件日志")
    
    # 构建请求
    request_data = {
        "process_id": 999,
        "event_logs": event_logs,
        "options": {
            "min_frequency": 2,
            "min_confidence": 0.1,
            "max_pattern_length": 5,
            "enable_parallel_detection": False,
            "enable_loop_detection": False,
            "use_gpu_acceleration": True
        }
    }
    
    try:
        print("🚀 发送子流程发现请求...")
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:8001/subprocess-discovery",
            json=request_data,
            timeout=120  # 2分钟超时
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 子流程发现成功")
            print(f"⏱️  处理时间: {processing_time:.2f}秒")
            print(f"🎯 发现子流程数量: {len(data['subprocesses'])}")
            print(f"📈 DFG节点数量: {len(data['hierarchical_dfg']['nodes'])}")
            print(f"🔗 DFG边数量: {len(data['hierarchical_dfg']['edges'])}")
            
            # 显示性能指标
            if 'performance_metrics' in data:
                metrics = data['performance_metrics']
                print(f"📊 性能指标:")
                print(f"   处理时间: {metrics.get('processing_time_seconds', 0):.2f}秒")
                print(f"   使用设备: {metrics.get('device_used', 'unknown')}")
                print(f"   事件数量: {metrics.get('event_count', 0)}")
                print(f"   内存使用: {metrics.get('memory_usage_start', 0):.1f}% -> {metrics.get('memory_usage_end', 0):.1f}%")
            
            # 显示发现的子流程
            print(f"\n🔍 发现的子流程:")
            for i, subprocess in enumerate(data['subprocesses'][:5]):  # 只显示前5个
                print(f"   {i+1}. {subprocess['name']}")
                print(f"      频率: {subprocess['frequency']}")
                print(f"      置信度: {subprocess['confidence']:.3f}")
                print(f"      活动: {' -> '.join(subprocess['activities'])}")
            
            if len(data['subprocesses']) > 5:
                print(f"   ... 还有 {len(data['subprocesses']) - 5} 个子流程")
            
            return True
        else:
            print(f"❌ 子流程发现失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 子流程发现异常: {e}")
        return False

def test_memory_cleanup():
    """测试内存清理"""
    print("\n🧹 测试内存清理...")
    try:
        response = requests.post("http://localhost:8001/force-gc", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 内存清理成功")
            print(f"   回收对象: {data['collected_objects']}")
            print(f"   内存使用: {data['memory_usage_percent']:.1f}%")
            print(f"   可用内存: {data['memory_available_gb']:.2f}GB")
            return True
        else:
            print(f"❌ 内存清理失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 内存清理异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🌟 ProMined Python Mining Service 测试")
    print("=" * 50)
    
    # 测试序列
    tests = [
        ("健康检查", test_health_check),
        ("系统信息", test_system_info),
        ("子流程发现", test_subprocess_discovery),
        ("内存清理", test_memory_cleanup),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 异常: {e}")
            results.append((test_name, False))
        
        time.sleep(1)  # 测试间隔
    
    # 测试结果汇总
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！Python服务运行正常。")
    else:
        print("⚠️  部分测试失败，请检查服务状态。")

if __name__ == "__main__":
    main()
