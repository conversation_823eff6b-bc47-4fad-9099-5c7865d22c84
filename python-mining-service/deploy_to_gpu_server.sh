#!/bin/bash

# GPU服务器部署脚本
# 将Python流程挖掘服务部署到NVIDIA RTX 4090服务器

set -e

# 服务器配置
SERVER_HOST="*************"
SERVER_USER="bpmax4090"
SERVER_PASSWORD="cicada2021"
REMOTE_DIR="~/promax-dev"
LOCAL_DIR="."

echo "🚀 开始部署到GPU服务器..."
echo "   服务器: $SERVER_HOST"
echo "   用户: $SERVER_USER"
echo "   目标目录: $REMOTE_DIR"

# 检查本地文件
if [ ! -f "requirements.txt" ]; then
    echo "❌ 错误: 请在python-mining-service目录下运行此脚本"
    exit 1
fi

# 创建临时部署包
DEPLOY_PACKAGE="gpu_mining_service.tar.gz"
echo "📦 创建部署包..."

# 排除不需要的文件
tar -czf "$DEPLOY_PACKAGE" \
    --exclude="__pycache__" \
    --exclude="*.pyc" \
    --exclude="*.log" \
    --exclude=".git" \
    --exclude="logs/*" \
    --exclude="$DEPLOY_PACKAGE" \
    .

echo "✅ 部署包创建完成: $DEPLOY_PACKAGE"

# 自动化部署（无密码SSH）
echo "🔐 使用SSH密钥进行自动化部署..."

# 创建远程目录
ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" "mkdir -p $REMOTE_DIR"

# 上传部署包
echo "📤 上传部署包到服务器..."
scp -o StrictHostKeyChecking=no "$DEPLOY_PACKAGE" "$SERVER_USER@$SERVER_HOST:$REMOTE_DIR/"

# 在服务器上解压和设置
echo "🔧 在服务器上解压和设置..."
ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" << 'EOF'
cd ~/promax-dev
echo "📂 当前目录: $(pwd)"

# 解压部署包
if [ -f gpu_mining_service.tar.gz ]; then
    echo "📦 解压部署包..."
    tar -xzf gpu_mining_service.tar.gz
    rm gpu_mining_service.tar.gz
    echo "✅ 解压完成"
else
    echo "❌ 部署包未找到"
    exit 1
fi

# 检查NVIDIA GPU
echo "🔍 检查NVIDIA GPU..."
if command -v nvidia-smi &> /dev/null; then
    nvidia-smi --query-gpu=name,memory.total,driver_version --format=csv,noheader,nounits
    echo "✅ NVIDIA GPU检测成功"
else
    echo "⚠️  nvidia-smi未找到，可能需要安装NVIDIA驱动"
fi

# 检查CUDA
echo "🔍 检查CUDA..."
if command -v nvcc &> /dev/null; then
    nvcc --version | grep "release"
    echo "✅ CUDA检测成功"
else
    echo "⚠️  CUDA未找到，可能需要安装CUDA toolkit"
fi

# 检查Python和conda
echo "🐍 检查Python环境..."
if command -v conda &> /dev/null; then
    echo "✅ Conda已安装: $(conda --version)"
else
    echo "📥 安装Miniconda..."
    wget -q https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O miniconda.sh
    bash miniconda.sh -b -p $HOME/miniconda3
    rm miniconda.sh
    echo 'export PATH="$HOME/miniconda3/bin:$PATH"' >> ~/.bashrc
    source ~/.bashrc
    echo "✅ Miniconda安装完成"
fi

# 初始化conda（如果需要）
if [ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]; then
    source "$HOME/miniconda3/etc/profile.d/conda.sh"
fi

# 创建conda环境
ENV_NAME="promined-gpu"
echo "🔧 创建conda环境: $ENV_NAME"

if conda env list | grep -q "$ENV_NAME"; then
    echo "⚠️  环境已存在，删除旧环境..."
    conda env remove -n "$ENV_NAME" -y
fi

# 使用environment.yml创建环境
if [ -f "environment.yml" ]; then
    echo "📋 使用environment.yml创建环境..."
    conda env create -f environment.yml -n "$ENV_NAME"
else
    echo "📋 手动创建环境..."
    conda create -n "$ENV_NAME" python=3.12 -y
fi

echo "✅ Conda环境创建完成"

# 激活环境并安装依赖
echo "📦 激活环境并安装依赖..."
conda activate "$ENV_NAME"

# 安装PyTorch with CUDA support
echo "🔥 安装PyTorch with CUDA support..."
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia -y

# 安装其他依赖
echo "📦 安装其他依赖..."
pip install -r requirements.txt

# 安装GPU加速库
echo "🚀 安装GPU加速库..."
if ./install_gpu_libs.sh; then
    echo "✅ GPU加速库安装成功"
else
    echo "⚠️  GPU加速库安装可能有问题，继续..."
fi

echo "🎉 服务器环境设置完成！"
echo ""
echo "📝 下一步操作："
echo "   1. SSH登录服务器: ssh bpmax4090@*************"
echo "   2. 进入目录: cd ~/promax-dev"
echo "   3. 激活环境: conda activate $ENV_NAME"
echo "   4. 运行测试: python test_gpu_features.py"
echo "   5. 启动服务: python app/main.py"
echo ""
EOF

# 清理本地临时文件
rm -f "$DEPLOY_PACKAGE"
echo "🧹 清理临时文件完成"

echo ""
echo "🎯 部署完成！"
echo "   部署包已上传到: $SERVER_HOST:$REMOTE_DIR"
echo "   请按照提示完成后续设置"
