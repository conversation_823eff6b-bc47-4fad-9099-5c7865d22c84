#!/usr/bin/env python3
"""
GPU服务器专用测试脚本
专门测试NVIDIA RTX 4090的CUDA加速功能
"""

import sys
import asyncio
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import torch
import numpy as np
from loguru import logger
from datetime import datetime, timedelta
import random

# 尝试导入CUDA相关库
try:
    import cupy as cp
    CUPY_AVAILABLE = True
    print("✅ CuPy可用")
except ImportError:
    CUPY_AVAILABLE = False
    print("⚠️  CuPy不可用")

try:
    import cudf
    CUDF_AVAILABLE = True
    print("✅ cuDF可用")
except ImportError:
    CUDF_AVAILABLE = False
    print("⚠️  cuDF不可用")

from utils.gpu_detector import gpu_detector
from utils.performance_monitor import PerformanceMonitor
from config.settings import Settings
from services.gpu_accelerated_mining import GPUAcceleratedMining
from models.mining_models import (
    SubprocessDiscoveryRequest,
    SubprocessDiscoveryOptions,
    EventLog
)


def test_cuda_environment():
    """测试CUDA环境"""
    print("🔥 测试CUDA环境...")
    
    # 基本CUDA信息
    print(f"   PyTorch版本: {torch.__version__}")
    print(f"   CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"   CUDA版本: {torch.version.cuda}")
        print(f"   cuDNN版本: {torch.backends.cudnn.version()}")
        print(f"   GPU数量: {torch.cuda.device_count()}")
        
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            print(f"   GPU {i}: {props.name}")
            print(f"     内存: {props.total_memory / 1024**3:.1f}GB")
            print(f"     计算能力: {props.major}.{props.minor}")
            print(f"     多处理器: {props.multi_processor_count}")
        
        # 测试GPU内存
        device = torch.device('cuda:0')
        print(f"   当前设备: {device}")
        
        # 内存信息
        total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        allocated_memory = torch.cuda.memory_allocated(0) / 1024**3
        cached_memory = torch.cuda.memory_reserved(0) / 1024**3
        
        print(f"   内存总量: {total_memory:.1f}GB")
        print(f"   已分配: {allocated_memory:.1f}GB")
        print(f"   缓存: {cached_memory:.1f}GB")
        print(f"   可用: {total_memory - allocated_memory:.1f}GB")
        
        return True
    else:
        print("❌ CUDA不可用")
        return False


def test_cuda_performance():
    """测试CUDA性能"""
    print("\n⚡ 测试CUDA性能...")
    
    if not torch.cuda.is_available():
        print("   跳过 - CUDA不可用")
        return
    
    # 测试矩阵运算性能
    sizes = [1000, 5000, 10000]
    
    for size in sizes:
        print(f"\n   测试矩阵大小: {size}x{size}")
        
        # CPU测试
        cpu_tensor_a = torch.randn(size, size)
        cpu_tensor_b = torch.randn(size, size)
        
        start_time = time.time()
        cpu_result = torch.mm(cpu_tensor_a, cpu_tensor_b)
        cpu_time = time.time() - start_time
        
        print(f"     CPU时间: {cpu_time:.3f}秒")
        
        # GPU测试
        gpu_tensor_a = cpu_tensor_a.cuda()
        gpu_tensor_b = cpu_tensor_b.cuda()
        
        # 预热GPU
        torch.mm(gpu_tensor_a, gpu_tensor_b)
        torch.cuda.synchronize()
        
        start_time = time.time()
        gpu_result = torch.mm(gpu_tensor_a, gpu_tensor_b)
        torch.cuda.synchronize()
        gpu_time = time.time() - start_time
        
        print(f"     GPU时间: {gpu_time:.3f}秒")
        print(f"     加速比: {cpu_time/gpu_time:.1f}x")
        
        # 清理GPU内存
        del gpu_tensor_a, gpu_tensor_b, gpu_result
        torch.cuda.empty_cache()


def test_cupy_performance():
    """测试CuPy性能"""
    print("\n🐍 测试CuPy性能...")
    
    if not CUPY_AVAILABLE:
        print("   跳过 - CuPy不可用")
        return
    
    # 测试数组运算
    size = 10000000  # 1000万元素
    
    print(f"   测试数组大小: {size:,}元素")
    
    # NumPy测试
    np_array_a = np.random.randn(size).astype(np.float32)
    np_array_b = np.random.randn(size).astype(np.float32)
    
    start_time = time.time()
    np_result = np_array_a * np_array_b + np.sin(np_array_a)
    np_time = time.time() - start_time
    
    print(f"     NumPy时间: {np_time:.3f}秒")
    
    # CuPy测试
    cp_array_a = cp.asarray(np_array_a)
    cp_array_b = cp.asarray(np_array_b)
    
    start_time = time.time()
    cp_result = cp_array_a * cp_array_b + cp.sin(cp_array_a)
    cp.cuda.Stream.null.synchronize()
    cp_time = time.time() - start_time
    
    print(f"     CuPy时间: {cp_time:.3f}秒")
    print(f"     加速比: {np_time/cp_time:.1f}x")
    
    # 验证结果一致性
    cp_result_cpu = cp.asnumpy(cp_result)
    max_diff = np.max(np.abs(np_result - cp_result_cpu))
    print(f"     最大差异: {max_diff:.2e}")


def generate_large_test_data(size: int = 10000) -> list:
    """生成大规模测试数据"""
    activities = [
        "数据采集", "数据预处理", "特征提取", "模型训练",
        "模型验证", "模型部署", "结果分析", "报告生成",
        "质量检查", "性能优化", "系统监控", "异常处理",
        "数据备份", "安全审计", "用户反馈", "系统更新"
    ]
    
    event_logs = []
    base_time = datetime.now() - timedelta(days=90)
    
    # 生成更复杂的案例结构
    num_cases = size // 20  # 每个案例平均20个事件
    
    for case_idx in range(num_cases):
        case_id = f"case_{case_idx:06d}"
        case_start_time = base_time + timedelta(
            days=random.randint(0, 90),
            hours=random.randint(0, 23)
        )
        
        # 每个案例的事件数量
        events_per_case = random.randint(10, 30)
        
        for event_idx in range(events_per_case):
            activity = random.choice(activities)
            timestamp = case_start_time + timedelta(
                hours=event_idx * random.uniform(0.5, 2.0),
                minutes=random.randint(0, 59)
            )
            
            event_logs.append(EventLog(
                id=len(event_logs),
                case_id=case_id,
                activity=activity,
                timestamp=timestamp,
                resource=f"resource_{random.randint(1, 10)}",
                cost=random.uniform(50, 500)
            ))
    
    return event_logs


async def test_large_scale_gpu_mining():
    """测试大规模GPU挖掘"""
    print("\n🚀 测试大规模GPU挖掘...")
    
    # 创建配置
    settings = Settings()
    settings.enable_gpu_acceleration = True
    settings.gpu_memory_limit_percent = 80.0
    settings.large_data_threshold = 1000
    
    gpu_config = settings.get_gpu_config()
    
    # 获取CUDA设备
    if torch.cuda.is_available():
        device = torch.device("cuda:0")
    else:
        print("   跳过 - CUDA不可用")
        return
    
    print(f"   使用设备: {device}")
    
    # 创建GPU挖掘服务
    gpu_mining = GPUAcceleratedMining(device=device, config=gpu_config)
    
    # 测试不同规模的数据
    test_sizes = [1000, 5000, 10000, 20000]
    
    results = []
    
    for size in test_sizes:
        print(f"\n   测试数据量: {size:,}")
        
        # 生成测试数据
        print("     生成测试数据...")
        event_logs = generate_large_test_data(size)
        actual_size = len(event_logs)
        print(f"     实际数据量: {actual_size:,}")
        
        # 创建请求
        request = SubprocessDiscoveryRequest(
            process_id=f"large_test_{size}",
            event_logs=event_logs,
            options=SubprocessDiscoveryOptions(
                min_frequency=3,
                min_confidence=0.05,
                max_pattern_length=6
            )
        )
        
        # 监控GPU内存
        if torch.cuda.is_available():
            torch.cuda.reset_peak_memory_stats()
            initial_memory = torch.cuda.memory_allocated() / 1024**3
            print(f"     初始GPU内存: {initial_memory:.2f}GB")
        
        # 执行挖掘
        start_time = time.time()
        try:
            result = await gpu_mining.discover_subprocesses_gpu(request)
            end_time = time.time()
            
            processing_time = end_time - start_time
            throughput = actual_size / processing_time
            
            # GPU内存统计
            if torch.cuda.is_available():
                peak_memory = torch.cuda.max_memory_allocated() / 1024**3
                final_memory = torch.cuda.memory_allocated() / 1024**3
                
                print(f"     处理时间: {processing_time:.2f}秒")
                print(f"     吞吐量: {throughput:.0f}条/秒")
                print(f"     发现子流程: {len(result.subprocesses)}个")
                print(f"     峰值GPU内存: {peak_memory:.2f}GB")
                print(f"     最终GPU内存: {final_memory:.2f}GB")
                
                if result.subprocesses:
                    top_patterns = result.subprocesses[:3]
                    print("     前3个模式:")
                    for i, pattern in enumerate(top_patterns, 1):
                        print(f"       {i}. {pattern.name} (频率: {pattern.frequency}, 置信度: {pattern.confidence:.3f})")
                
                results.append({
                    'size': actual_size,
                    'time': processing_time,
                    'throughput': throughput,
                    'patterns': len(result.subprocesses),
                    'peak_memory': peak_memory
                })
            
            # 清理GPU内存
            torch.cuda.empty_cache()
            
        except Exception as e:
            print(f"     ❌ 测试失败: {e}")
            torch.cuda.empty_cache()
    
    # 性能总结
    if results:
        print("\n📊 性能总结:")
        print("   数据量     处理时间    吞吐量      模式数    峰值内存")
        print("   -------   --------   --------   ------   --------")
        for r in results:
            print(f"   {r['size']:7,}   {r['time']:6.1f}s   {r['throughput']:6.0f}/s   {r['patterns']:6d}   {r['peak_memory']:6.2f}GB")


async def main():
    """主测试函数"""
    print("🧪 GPU服务器测试开始\n")
    print("=" * 60)
    
    # 1. 测试CUDA环境
    cuda_available = test_cuda_environment()
    
    if cuda_available:
        # 2. 测试CUDA性能
        test_cuda_performance()
        
        # 3. 测试CuPy性能
        test_cupy_performance()
        
        # 4. 测试GPU检测器
        print("\n🔍 测试GPU检测器...")
        device_info = gpu_detector.get_device_info()
        print(f"   检测结果: {device_info}")
        
        # 5. 测试大规模GPU挖掘
        await test_large_scale_gpu_mining()
        
        print("\n🎉 GPU服务器测试完成！")
        print("✅ 所有CUDA功能正常工作")
    else:
        print("\n❌ CUDA不可用，无法进行GPU测试")
    
    print("=" * 60)


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
        level="INFO"
    )
    
    # 运行测试
    asyncio.run(main())
