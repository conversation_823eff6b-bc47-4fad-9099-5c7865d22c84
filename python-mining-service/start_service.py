#!/usr/bin/env python3
"""
ProMined Python Mining Service 启动脚本
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def check_conda():
    """检查conda是否可用"""
    try:
        result = subprocess.run(['conda', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Conda 可用: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Conda 未找到，请先安装 Miniconda 或 Anaconda")
    print("📥 下载地址: https://docs.conda.io/en/latest/miniconda.html")
    return False

def check_environment():
    """检查conda环境是否存在"""
    try:
        result = subprocess.run(['conda', 'env', 'list'], capture_output=True, text=True)
        if 'promined-mining' in result.stdout:
            print("✅ conda环境 'promined-mining' 已存在")
            return True
        else:
            print("❌ conda环境 'promined-mining' 不存在")
            return False
    except Exception as e:
        print(f"❌ 检查环境失败: {e}")
        return False

def create_environment():
    """创建conda环境"""
    print("📦 正在创建conda环境...")
    try:
        # 检查environment.yml是否存在
        env_file = Path("environment.yml")
        if not env_file.exists():
            print("❌ environment.yml 文件不存在")
            return False
        
        # 创建环境
        result = subprocess.run([
            'conda', 'env', 'create', '-f', 'environment.yml'
        ], check=True)
        
        print("✅ conda环境创建成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 创建环境失败: {e}")
        return False

def activate_and_run():
    """激活环境并运行服务"""
    print("🚀 启动 ProMined Python Mining Service...")
    
    # 检测操作系统
    system = platform.system()
    
    if system == "Windows":
        # Windows
        activate_cmd = "conda activate promined-mining && python app/main.py"
        subprocess.run(activate_cmd, shell=True)
    else:
        # macOS/Linux
        # 获取conda路径
        conda_base = os.environ.get('CONDA_PREFIX', os.path.expanduser('~/miniconda3'))
        if not os.path.exists(conda_base):
            conda_base = os.path.expanduser('~/anaconda3')
        
        # 构建激活命令
        activate_script = os.path.join(conda_base, 'etc', 'profile.d', 'conda.sh')
        
        if os.path.exists(activate_script):
            cmd = f"""
            source {activate_script}
            conda activate promined-mining
            python app/main.py
            """
        else:
            # 备用方法
            env_python = os.path.join(conda_base, 'envs', 'promined-mining', 'bin', 'python')
            if os.path.exists(env_python):
                cmd = f"{env_python} app/main.py"
            else:
                print("❌ 无法找到conda环境的Python解释器")
                return False
        
        subprocess.run(cmd, shell=True, executable='/bin/bash')

def main():
    """主函数"""
    print("🌟 ProMined Python Mining Service 启动器")
    print("=" * 50)
    
    # 检查当前目录
    if not Path("app/main.py").exists():
        print("❌ 请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 检查conda
    if not check_conda():
        sys.exit(1)
    
    # 检查环境
    if not check_environment():
        print("🔧 需要创建conda环境")
        if input("是否现在创建? (y/N): ").lower() == 'y':
            if not create_environment():
                sys.exit(1)
        else:
            print("请先运行: conda env create -f environment.yml")
            sys.exit(1)
    
    # 检查系统信息
    print(f"🖥️  操作系统: {platform.system()} {platform.release()}")
    print(f"🐍 Python版本: {sys.version}")
    
    # 检查是否为苹果M芯片
    if platform.system() == "Darwin" and platform.machine() == "arm64":
        print("🚀 检测到苹果M芯片，将启用GPU加速")
    
    # 启动服务
    try:
        activate_and_run()
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
