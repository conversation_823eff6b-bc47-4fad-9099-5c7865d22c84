"""
数据模型定义
"""

from datetime import datetime
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field


class EventLog(BaseModel):
    """事件日志模型"""
    id: int
    case_id: str = Field(..., description="案例ID")
    activity: str = Field(..., description="活动名称")
    timestamp: datetime = Field(..., description="时间戳")
    resource: Optional[str] = Field(None, description="资源")
    cost: Optional[float] = Field(None, description="成本")
    attributes: Optional[Dict[str, Any]] = Field(None, description="其他属性")


class SubprocessPattern(BaseModel):
    """子流程模式"""
    id: str = Field(..., description="模式ID")
    name: str = Field(..., description="模式名称")
    activities: List[str] = Field(..., description="活动序列")
    frequency: int = Field(..., description="频率")
    confidence: float = Field(..., description="置信度")
    avg_duration: float = Field(..., description="平均持续时间(毫秒)")
    cases: List[str] = Field(..., description="包含此模式的案例列表")
    pattern_type: str = Field(..., description="模式类型: sequential, parallel, loop")


class DFGNode(BaseModel):
    """DFG节点"""
    id: str = Field(..., description="节点ID")
    label: str = Field(..., description="节点标签")
    frequency: int = Field(..., description="频率")
    node_type: str = Field(default="activity", description="节点类型")


class DFGEdge(BaseModel):
    """DFG边"""
    source: str = Field(..., description="源节点ID")
    target: str = Field(..., description="目标节点ID")
    frequency: int = Field(..., description="频率")
    avg_duration: float = Field(..., description="平均持续时间(毫秒)")


class HierarchicalDFG(BaseModel):
    """层次化DFG"""
    nodes: List[DFGNode] = Field(..., description="节点列表")
    edges: List[DFGEdge] = Field(..., description="边列表")


class SubprocessStatistics(BaseModel):
    """子流程统计信息"""
    total_subprocesses: int = Field(..., description="总子流程数量")
    avg_frequency: float = Field(..., description="平均频率")
    avg_confidence: float = Field(..., description="平均置信度")
    pattern_type_distribution: Dict[str, int] = Field(..., description="模式类型分布")


class SubprocessDiscoveryOptions(BaseModel):
    """子流程发现选项"""
    min_frequency: int = Field(default=2, description="最小频率")
    min_confidence: float = Field(default=0.1, description="最小置信度")
    max_pattern_length: int = Field(default=5, description="最大模式长度")
    enable_parallel_detection: bool = Field(default=False, description="启用并行检测")
    enable_loop_detection: bool = Field(default=False, description="启用循环检测")
    use_gpu_acceleration: bool = Field(default=True, description="使用GPU加速")


class SubprocessDiscoveryRequest(BaseModel):
    """子流程发现请求"""
    process_id: int = Field(..., description="流程ID")
    event_logs: List[EventLog] = Field(..., description="事件日志列表")
    options: Optional[SubprocessDiscoveryOptions] = Field(default=None, description="发现选项")


class SubprocessDiscoveryResponse(BaseModel):
    """子流程发现响应"""
    process_id: int = Field(..., description="流程ID")
    subprocesses: List[SubprocessPattern] = Field(..., description="发现的子流程列表")
    hierarchical_dfg: HierarchicalDFG = Field(..., description="层次化DFG")
    statistics: SubprocessStatistics = Field(..., description="统计信息")
    performance_metrics: Optional[Dict[str, Any]] = Field(None, description="性能指标")
    timestamp: datetime = Field(default_factory=datetime.now, description="生成时间")


class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str = Field(..., description="状态")
    timestamp: datetime = Field(..., description="时间戳")
    memory_usage_percent: float = Field(..., description="内存使用百分比")
    cpu_usage_percent: float = Field(..., description="CPU使用百分比")
    gpu_available: bool = Field(..., description="GPU是否可用")
    device: str = Field(..., description="当前使用的设备")


class SystemInfoResponse(BaseModel):
    """系统信息响应"""
    python_version: str = Field(..., description="Python版本")
    torch_version: str = Field(..., description="PyTorch版本")
    device: str = Field(..., description="计算设备")
    gpu_available: bool = Field(..., description="GPU是否可用")
    cpu_count: int = Field(..., description="CPU核心数")
    memory_total_gb: float = Field(..., description="总内存(GB)")
    memory_available_gb: float = Field(..., description="可用内存(GB)")
    platform: str = Field(..., description="平台信息")
