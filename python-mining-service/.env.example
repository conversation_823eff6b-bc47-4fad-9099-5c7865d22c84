# ProMined Python Mining Service 配置文件

# 服务配置
HOST=0.0.0.0
PORT=8001
DEBUG=false

# 数据库配置 (MySQL)
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=promined

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_KEY_PREFIX=promined:mining:

# 性能配置
MAX_WORKERS=4
MAX_MEMORY_USAGE_PERCENT=80.0
BATCH_SIZE=1000
GPU_MEMORY_FRACTION=0.8

# 算法配置
MIN_FREQUENCY=2
MIN_CONFIDENCE=0.1
MAX_PATTERN_LENGTH=5
ENABLE_GPU_ACCELERATION=true

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/mining_service.log

# CORS配置 (多个域名用逗号分隔)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000,http://127.0.0.1:3001
