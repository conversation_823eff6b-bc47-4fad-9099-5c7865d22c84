# ProMined Python Mining Service 部署指南

## 🚀 快速部署

### 方式1: 本地开发部署 (推荐)

```bash
# 1. 进入Python服务目录
cd python-mining-service

# 2. 创建conda环境
conda env create -f environment.yml

# 3. 激活环境
conda activate promined-mining

# 4. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等

# 5. 启动服务
python app/main.py
```

### 方式2: 使用启动脚本

```bash
# 自动化部署脚本
python start_service.py
```

### 方式3: Docker部署

```bash
# 构建镜像
docker build -t promined-python-mining .

# 运行容器
docker run -p 8000:8000 promined-python-mining

# 或使用docker-compose
docker-compose up -d
```

## 🔧 配置NestJS集成

### 1. 更新NestJS环境配置

在 `server/.env` 中添加：
```bash
PYTHON_MINING_SERVICE_URL=http://localhost:8000
```

### 2. 重启NestJS服务

```bash
cd server
npm run start:dev
```

### 3. 验证集成

```bash
# 测试子流程发现API
curl -X POST http://localhost:3003/api/v1/analysis/subprocess-discovery/1 \
  -H "Content-Type: application/json" \
  -d '{"options": {"use_gpu_acceleration": true}}'
```

## 📊 性能优化配置

### 苹果M芯片优化

```bash
# 环境变量
export PYTORCH_ENABLE_MPS_FALLBACK=1
export OMP_NUM_THREADS=8

# Python服务配置
ENABLE_GPU_ACCELERATION=true
MAX_WORKERS=4
BATCH_SIZE=1000
GPU_MEMORY_FRACTION=0.8
```

### 内存优化

```bash
# 内存限制配置
MAX_MEMORY_USAGE_PERCENT=80.0

# 算法参数优化
MIN_FREQUENCY=2
MIN_CONFIDENCE=0.1
MAX_PATTERN_LENGTH=5
```

## 🔍 监控和调试

### 健康检查

```bash
# 服务状态
curl http://localhost:8000/health

# 系统信息
curl http://localhost:8000/system-info

# 强制垃圾回收
curl -X POST http://localhost:8000/force-gc
```

### 日志监控

```bash
# 查看实时日志
tail -f logs/mining_service.log

# 查看性能日志
grep "performance" logs/mining_service.log
```

### API文档

访问 http://localhost:8000/docs 查看完整API文档

## 🛠️ 故障排除

### 常见问题

1. **GPU不可用**
   ```bash
   # 检查MPS支持
   python -c "import torch; print(torch.backends.mps.is_available())"
   
   # 解决方案：确保使用苹果M芯片设备，或设置CPU模式
   export ENABLE_GPU_ACCELERATION=false
   ```

2. **内存不足**
   ```bash
   # 减少批处理大小
   export BATCH_SIZE=500
   
   # 降低最大内存使用
   export MAX_MEMORY_USAGE_PERCENT=70.0
   ```

3. **端口冲突**
   ```bash
   # 更改端口
   export PORT=8001
   ```

4. **数据库连接失败**
   ```bash
   # 检查MySQL连接
   mysql -h localhost -u root -p promined
   
   # 更新连接配置
   export MYSQL_HOST=your_host
   export MYSQL_PASSWORD=your_password
   ```

### 性能调优

1. **大数据集处理**
   ```bash
   # 启用采样
   export ENABLE_SAMPLING=true
   export SAMPLE_RATIO=0.1
   
   # 增加工作线程
   export MAX_WORKERS=8
   ```

2. **GPU内存优化**
   ```bash
   # 调整GPU内存分配
   export GPU_MEMORY_FRACTION=0.6
   
   # 启用内存清理
   export AUTO_CLEANUP_MEMORY=true
   ```

## 📈 性能基准测试

### 运行基准测试

```bash
# 基本功能测试
python test_service.py

# 性能压力测试
python tests/performance_test.py

# 内存泄漏测试
python tests/memory_test.py
```

### 预期性能指标

| 数据规模 | 处理时间 (GPU) | 处理时间 (CPU) | 内存使用 |
|----------|----------------|----------------|----------|
| 1K事件   | 1-2秒          | 2-4秒          | <500MB   |
| 10K事件  | 5-10秒         | 20-40秒        | <2GB     |
| 50K事件  | 30-60秒        | 2-5分钟        | <4GB     |

## 🔄 更新和维护

### 更新Python服务

```bash
# 拉取最新代码
git pull origin main

# 更新conda环境
conda env update -f environment.yml

# 重启服务
python app/main.py
```

### 数据库迁移

```bash
# 备份数据
mysqldump -u root -p promined > backup.sql

# 应用迁移
# (根据具体迁移脚本)
```

### 日志轮转

```bash
# 配置loguru自动轮转
# 在 app/main.py 中已配置:
# rotation="100 MB", retention="7 days"
```

## 🔐 安全配置

### 生产环境配置

```bash
# 禁用调试模式
DEBUG=false

# 配置CORS
ALLOWED_ORIGINS=https://your-domain.com

# 设置强密码
MYSQL_PASSWORD=strong_password_here
REDIS_PASSWORD=redis_password_here
```

### 防火墙配置

```bash
# 只允许内网访问
ufw allow from ***********/24 to any port 8000

# 或使用nginx反向代理
# 配置nginx.conf
```

## 📞 技术支持

如遇到问题，请：

1. 查看日志文件 `logs/mining_service.log`
2. 运行健康检查 `curl http://localhost:8000/health`
3. 检查系统资源使用情况
4. 提交Issue到项目仓库

## 📚 相关文档

- [API文档](http://localhost:8000/docs)
- [性能优化指南](./PERFORMANCE.md)
- [开发指南](./README.md)
- [故障排除手册](./TROUBLESHOOTING.md)
